﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>转园记录</title>
    <link rel="stylesheet" type="text/css" href="../../css/reset.css" />
    <link rel="stylesheet" type="text/css" href="../../layui-btkj/css/layui.css" />
    <link rel="stylesheet" type="text/css" href="../../css/icon.css" />
    <link rel="stylesheet" type="text/css" href="../../css/commonstyle-layui-btkj.css" />
    <link rel="stylesheet" type="text/css" href="../../css/style.css" />
    <link rel="stylesheet" href="../../css/medical.css" />
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <style>
        html, body { background: #EAEFF3; overflow: hidden; }
        .layui-form-item { display: inline-block; vertical-align: top; margin: 5px 5px 0px 0; }
        .layui-form-label { line-height: 28px; }
        .em { display: none; }
        xm-select > .xm-body { z-index: 19999999 !important; }
    </style>
</head>
<body style="display: contents;">
    <div class="marmain">
        <div class="content-medical">
            <div id="form" class="layui-form layui-comselect" style="padding:10px;" lay-filter="formOk">
                <div class="layui-form-item">
                    <label class="layui-form-label">选择地区：</label>
                    <div class="layui-input-inline">
                        <select id="selarea" lay-filter="selarea"></select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em class="em">*</em>转出托幼园所名称：</label>
                    <div class="layui-input-inline" style="float: left;width:200px;">
                        <div id="selyeyid" class="xm-select-demo" style="display: inline-block;width:100%;"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em class="em">*</em>姓名：</label>
                    <div class="layui-input-inline" style="float: left;width: auto;">
                        <input type="text" style="width: 100px; " id="txtstuname" lay-vertype="tips" autocomplete="off" placeholder="请输入姓名" class="layui-input" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">性别：</label>
                    <div class="layui-input-inline" style="float: left;width: 120px;">
                        <select id="selsex">
                            <option value="">请选择性别</option>
                            <option value="男">男</option>
                            <option value="女">女</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">转园状态：</label>
                    <div class="layui-input-inline" style="float: left;width:120px;">
                        <select id="selzhuanyuantatus">
                            <option value="">请选择转园状态</option>
                            <option value="1" selected="selected">已转出</option>
                            <option value="2">已转入</option>
                            <option value="3">过期</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">入园日期：</label>
                    <div class="layui-input-block" style="min-height: 30px;margin-left: 0px; position: relative;float: left; width: 150px;">
                        <input autocomplete="off" class="layui-input alendar-padding" type="text" id="txtstartdate" placeholder="开始时间" style="width:150px; display: inline-block;" />
                        <i class="layui-alendar"><img id="iconstartdate" src="../../images/cal_icon.png" style="width: 14px;" /></i>
                    </div>
                    <span style="display: inline-block;vertical-align: top;color: #e6e6e6;float: left;margin-top: 8px;"> — </span>
                    <div class="layui-input-inline" style="min-height: 30px;margin-left: 0px; position: relative;width: 150px; float: left;">
                        <input autocomplete="off" class="layui-input alendar-padding" type="text" id="txtenddate" placeholder="结束时间" style="width:150px; display: inline-block;" />
                        <i class="layui-alendar"><img id="iconenddate" src="../../images/cal_icon.png" style="width: 14px;" /></i>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">转出日期：</label>
                    <div class="layui-input-block" style="min-height: 30px;margin-left: 0px; position: relative;float: left; width: 150px;">
                        <input autocomplete="off" class="layui-input alendar-padding" type="text" id="txtstartdate2" placeholder="开始时间" style="width:150px; display: inline-block;" />
                        <i class="layui-alendar"><img id="iconstartdate2" src="../../images/cal_icon.png" style="width: 14px;" /></i>
                    </div>
                    <span style="display: inline-block;vertical-align: top;color: #e6e6e6;float: left;margin-top: 8px;"> — </span>
                    <div class="layui-input-inline" style="min-height: 30px;margin-left: 0px; position: relative;width: 150px; float: left;">
                        <input autocomplete="off" class="layui-input alendar-padding" type="text" id="txtenddate2" placeholder="结束时间" style="width:150px; display: inline-block;" />
                        <i class="layui-alendar"><img id="iconenddate2" src="../../images/cal_icon.png" style="width: 14px;" /></i>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">转入日期：</label>
                    <div class="layui-input-block" style="min-height: 30px;margin-left: 0px; position: relative;float: left; width: 150px;">
                        <input autocomplete="off" class="layui-input alendar-padding" type="text" id="txtstartdate3" placeholder="开始时间" style="width:150px; display: inline-block;" />
                        <i class="layui-alendar"><img id="iconstartdate3" src="../../images/cal_icon.png" style="width: 14px;" /></i>
                    </div>
                    <span style="display: inline-block;vertical-align: top;color: #e6e6e6;float: left;margin-top: 8px;"> — </span>
                    <div class="layui-input-inline" style="min-height: 30px;margin-left: 0px; position: relative;width: 150px; float: left;">
                        <input autocomplete="off" class="layui-input alendar-padding" type="text" id="txtenddate3" placeholder="结束时间" style="width:150px; display: inline-block;" />
                        <i class="layui-alendar"><img id="iconenddate3" src="../../images/cal_icon.png" style="width: 14px;" /></i>
                    </div>
                </div>
                <div class="layui-form-item">
                    <button class="layui-btn form-search" style="margin-left: 5px;vertical-align: top;" id="btnSearch" lay-submit=lay-submit>查询</button>
                    <button class="layui-btn form-search" style="margin-left: 5px;vertical-align: top; background: #00AC9F !important;" id="btnreset">重置</button>
                </div>
            </div>
        </div>
        <div id="content" style="width: 100%;">
            <div class="marmain-cen">
                <div class="content-medical" id="divtable">
                    <table id="laytable" lay-filter="laytable"></table>
                </div>
            </div>
        </div>
    </div>
    <script data-main="../../js/yey/zhuanyuanlist" src="../../sys/require.min.js"></script>
</body>
</html>
