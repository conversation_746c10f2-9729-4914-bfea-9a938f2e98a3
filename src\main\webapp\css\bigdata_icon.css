.iconfont{
    font-family:"iconfont" !important;
    font-size:36px;font-style:normal;
    -webkit-font-smoothing: antialiased;
    -webkit-text-stroke-width: 0.2px;
    -moz-osx-font-smoothing: grayscale;
}
@font-face {
  font-family: 'iconfont';  /* project id 421803 */
  src: url('//at.alicdn.com/t/font_421803_pwedpanl0ys.eot');
  src: url('//at.alicdn.com/t/font_421803_pwedpanl0ys.eot?#iefix') format('embedded-opentype'),
  url('//at.alicdn.com/t/font_421803_pwedpanl0ys.woff2') format('woff2'),
  url('//at.alicdn.com/t/font_421803_pwedpanl0ys.woff') format('woff'),
  url('//at.alicdn.com/t/font_421803_pwedpanl0ys.ttf') format('truetype'),
  url('//at.alicdn.com/t/font_421803_pwedpanl0ys.svg#iconfont') format('svg');
}
/*上三角*/
.icon_trian-top:after{
	content: "\e983";
	font-size:16px;
	color: #BBBBBB;
	vertical-align: top;
}
/*下三角*/
.icon_trian-down:after{
	content: "\e779";
	font-size:16px;
	color: #BBBBBB;
	vertical-align: top;
}

/*左三角*/
.icon_tria-left:after{
	content: "\e650";
	font-size:14px;
	color: #aaaaaa;
}
/*下三角*/
.icon_tria-down:after{
	content: "\e64f";
	font-size:14px;
	color: #aaaaaa;
}

/*时间3*/
.icon_time3:after{
	content: "\e604";
	font-size: 17px;
	color: #333333;
	margin-right: 5px;
}
/*右箭头*/
.icon_arrow_right:after{
	content: "\e602";
	font-size: 20px;
	color: #666666;
}
/*下箭头*/
.icon_arrow_down:after{
	content: "\e981";
	font-size: 20px;
	color: #666666;
}
/*上升箭头*/
.icon_arrow_goup:after{
	content: "\e632";
	font-size: 13px;
	color: #ff0000;
}
/*下降箭头*/
.icon_arrow_decline:after{
	content: "\e885";
	font-size: 13px;
	color: #00d45f;
}
/*上升箭头1*/
.icon_arrow_goup1:after{
	content: "\e776";
	font-size: 24px;
	color: #ff0000; vertical-align: top;
}
/*下降箭头1*/
.icon_arrow_decline1:after{
	content: "\e777";
	font-size: 24px;
	color: #00d45f;vertical-align: top;
}

/*定位*/
.icon_location:after{
	content: "\e60d";
	font-size: 18px;
	color: #e70c0c;
}
/*上升箭头2*/
.icon_arrow_goup2:after{
	content: "\e984";
	font-size: 12px;
	color: #ffffff; 
	vertical-align: top;
}
/*下降箭头2*/
.icon_arrow_decline2:after{
	content: "\e76c";
	font-size: 12px;
	color: #ffffff;
	vertical-align: top;
}
/*原点*/
.icon_circle:after{
	content: "\e601";
	font-size: 18px;
	color: #fe1818;
}
/*幼儿园*/
.icon_kindergar:after{
	content: "\e653";
	font-size: 20px;
	color: #8eb5fd;
}
/*原点*/
.icon_circle1:after{
	content: "\e601";
	font-size: 18px;
	color: #dddddd;
}
/*时间*/
.icon_time:after{
	content: "\e647";
	font-size: 24px;
	color: #52ffff;
	vertical-align: top;
}
/*全屏*/
.icon_fullscreen:after{
	content: "\e63b";
	font-size: 24px;
	color: #52ffff;
	vertical-align: top;
}
/*缩屏*/
.icon_reducescreen:after{
	content: "\e793";
	font-size: 24px;
	color: #52ffff;
	vertical-align: top;
}
/*能量*/
.icon_energy:after{
	content: "\e660";
	font-size: 20px;
	vertical-align: top;
}
/*蛋白质*/
.icon_protein:after{
	content: "\e689";
	font-size: 20px;
	vertical-align: top;
}
/*脂肪*/
.icon_fat:after{
	content: "\e641";
	font-size: 20px;
	vertical-align: top;
}
/*VA*/
.icon_VA:after{
	content: "\e644";
	font-size: 20px;
	vertical-align: top;
}
/*VB*/
.icon_VB:after{
	content: "\e645";
	font-size: 20px;
	vertical-align: top;
}
/*VC*/
.icon_VC:after{
	content: "\e646";
	font-size: 20px;
	vertical-align: top;
}
/*钙*/
.icon_gai:after{
	content: "\e649";
	font-size: 20px;
	vertical-align: top;
}
/*铁*/
.icon_tie:after{
	content: "\e64a";
	font-size: 20px;
	vertical-align: top;
}
/*锌*/
.icon_xin:after{
	content: "\e64e";
	font-size: 20px;
	vertical-align: top;
}
/*用户*/
.icon_user:after{
	content: "\e734";
	font-size: 17px;
	color: #52ffff;
	vertical-align: top;
}
/*播放*/
.icon_paly:after{
	content: "\ebe0";
	font-size: 50px;
	color: #ffffff;
	vertical-align: top;
}
/*暂停*/
.icon_stop:after{
	content: "\e643";
	font-size: 50px;
	color: #ffffff;
	vertical-align: top;
}
/*提醒*/
.icon_remind:after{
	content: "\e63c";
	font-size: 14px;
	color: #ff9c00;
	vertical-align: top;
}
/*左耳*/
.icon_earleft:after{
	content: "\e657";
	font-size: 19px;
	color: #E9AE39;
	vertical-align: top;
}
/*右耳*/
.icon_earright:after{
	content: "\e656";
	font-size: 19px;
	color: #E9AE39;
	vertical-align: top;
}
/*牙齿*/
.icon_tooth:after{
	content: "\e6bc";
	font-size: 19px;
	color: #5D87F0;
	vertical-align: top;
}
/*基因*/
.icon_gene:after{
	content: "\e655";
	font-size: 19px;
	color: #FA5467;
	vertical-align: top;
}
/*提示*/
.icon_prompt:after{
	content: "\e6c9";
	font-size: 18px;
	color: #ee2e31;
	vertical-align: middle;
}
/*眼睛*/
.icon_eye:after{
	content: "\e651";
	font-size: 19px;
	color: #06C4EA;
	vertical-align: top;
}
/*消息*/
.icon_message:after{content: "\e73c";font-size: 22px;}
/*联系人*/
.icon_contact:after{content: "\e7df";font-size: 22px;}
/*功能*/
.icon_function:after{content: "\e6e0";font-size: 20px;}
/*删除*/
.icon_delete:after{content: "\e6e2";font-size: 18px;color: #ff0d0d;}