/*
日期：2023
作者：
功能：儿童基础档案
*/
var objdata = {
    remarkArr: [
        {
            name: '长白街道', value: 10, children: [
                {name: '天河社区', value: 1001},
                {name: '夹河社区', value: 1002},
                {name: '翡翠城社区', value: 1003},
                {name: '金沙湾社区', value: 1004},
                {name: '沙岗子社区', value: 1005},
                {name: '胜利社区', value: 1006},
                {name: '格林社区', value: 1007},
                {name: '幸福里社区', value: 1008,/*selected:true*/},
            ]
        },
        {
            name: '沈水湾街道', value: 11, children: [
                {name: '中海社区', value: 1101},
                {name: '合众社区', value: 1102},
                {name: '远洋社区', value: 1103},
            ]
        },
        {
            name: '其他', value: 12, children: [
                {name: '其他', value: 1201},
            ]
        },
    ]
};

require.config({
    paths: {
        jquery: '../../sys/jquery',
        system: '../../sys/system',
        func: '../../sys/function',
        layui: "../../layui-btkj/layui",
        sendMsg: "../../js/common/sendMsg",
        "xm-select": "../../plugin/xm-select/xm-select"
    },
    shim: {
        "system": {
            deps: ["jquery"]
        },
        "layui": {
            deps: ["jquery", "system"]
        }
    },
    waitSeconds: 0
});

require(["jquery", "system", 'layui', 'func', "xm-select", "sendMsg"], function () {
    layui.use(['table', "laydate", 'form'], function () {
        initEvent();
        initlist();
    });
});

function initEvent() {
    //搜索框回车事件
    $('.search').on('keydown', function (event) {
        if (event.keyCode == 13) {
            search();
            return false;
        }
    });
    $("#btnSearch").click(function () {
        search();
    });

    $("#btnadd").click(function () {
        jQuery.getparent().layer.open({
            type: 2,
            title: "添加",
            shadeClose: false,
            area: ['100%', '100%'],
            btnAlign: 'c',
            // btn: ["保存","保存并下一步", "取消"],
            content: 'html/child/childhealthadd.html?v=' + Arg("v") + '&mid=' + Arg("mid") + "&laiyuan=1",
            success: function (layero, index) {
                jQuery.getparent().objdata.cur_Index = index;
                jQuery.getparent().objdata.cur_layero = layero;
                jQuery.getparent().objdata.childhealthWindow = window;
                jQuery.getparent().objdata.childhealthaddFrame = layero.find('iframe')[0];
            }
        });

    });

    $('#iconstartdate').click(function (e) {
        var show = true;
        if (document.activeElement.id == "txtstartdate") {
            show = false;
        }
        var objdate = {
            elem: '#txtstartdate',
            type: "month",
            show: show, //直接显示
            needrender: true,
            isInitValue: true,
            showBottom: true,
            range: true,
            done: function () {
                $('#txtstartdate').blur();
            }
        }
        layui.laydate.render(objdate);
        return false;
    });
    $('#txtstartdate').focus(function (e) {
        setTimeout(function () {
            $('#iconstartdate').trigger("click");
        }, 100);
    });

    //出生日期
    $('#iconbirthday').click(function (e) {
        var show = true;
        if (document.activeElement.id == "txtbirthday") {
            show = false;
        }
        var objdate = {
            elem: '#txtbirthday',
            type: "day",
            show: show, //直接显示
            needrender: true,
            isInitValue: true,
            showBottom: true,
            range: true,
            done: function () {
                $('#txtbirthday').blur();
            }
        }
        layui.laydate.render(objdate);
        return false;
    });
    $('#txtbirthday').focus(function (e) {
        setTimeout(function () {
            $('#iconbirthday').trigger("click");
        }, 100);
    });

    //体检日期
    $('#icontjckdate').click(function (e) {
        var show = true;
        if (document.activeElement.id == "txtjckdate") {
            show = false;
        }
        var objdate = {
            elem: '#txtjckdate',
            type: "day",
            show: show, //直接显示
            needrender: true,
            isInitValue: true,
            showBottom: true,
            range: true,
            done: function () {
                $('#txtjckdate').blur();
            }
        }
        layui.laydate.render(objdate);
        return false;
    });
    $('#txtjckdate').focus(function (e) {
        setTimeout(function () {
            $('#icontjckdate').trigger("click");
        }, 100);
    });

    $("#btnsetup").click(function () {
        var ckday = getckDay();
        var strhtml =
            '<div style="padding: 40px;">\
                <div class="layui-form-item layui-form" style="margin:10px 0">\
                    <label class="layui-form-label" style="width:137px;">录入体检数据</label>\
                    <div class="layui-input-block" style="margin-left:144px;">\
                        <input name="title" placeholder="请输入" class="layui-input" type="number" value="' + ckday + '" style="width:80px; display: inline-block; margin-right:10px;">天不可更改\
                    </div>\
                </div>\
            </div>';
        jQuery.getparent().layer.open({
            type: 1,
            title: ["设置", 'text-align:center'],
            area: ['580px', '300px'], //宽高
            btnAlign: 'c',
            btn: ["确定", "取消"],
            content: strhtml,
            yes: function (index, layero) {
                var val = $.trim(layero.find('input').val());
                if (!val) {
                    return objparent.layer.msg("请输入天数");
                }

                $.sm(function (re, err) {
                    if (err) {
                        jQuery.getparent().layer.msg(err, {icon: 5});
                    } else {
                        jQuery.getparent().layer.msg('设置成功');
                        jQuery.getparent().layer.close(index);
                    }
                }, ["yhszedit.sysinsert", val, "child_ckday"]);
            }
        });
    });

    //导入儿童档案
    $("#btnimport").click(function () {
        jQuery.getparent().objdata.yeyimpyey = jQuery.getparent().layer.open({
            type: 2,
            title: '导入数据',
            shadeClose: false,
            area: ['500px', '250px'],
            content: "html/importutil.html?v=" + (Arg("v") || 1) + "&mid=" + Arg("mid"),
            btn: ["确定", "关闭"],
            success: function (layero, index) {
                //			    jQuery.getparent().objdata.infoframe = layero.find('iframe')[0]; //得到iframe页的窗口对象，执行iframe页的方法：iframeWin.method();
            }, yes: function (index, layero) {
                var childpage = layero.find('iframe')[0].contentWindow;
                var src = childpage.$('#src').val();
                if (!src) {
                    jQuery.getparent().layer.msg("请先上传excel文件！", {icon: 5});
                    return;
                }
                var name = childpage.$("#txtfname").val();
                if (!name) {
                    return jQuery.getparent().layer.msg("请先选择要导入的数据！");
                }
                importExcel(src, name);
            }, btn2: function (index, layero) {//关闭时删除文件
                deletefile(layero);
            },
            cancel: function (index, layero) {
                // 右上角关闭事件的逻辑
                deletefile(layero);
            }
        });
    });
    $("#executepj").click(function () {
        $.sm(function (re, err) {
            if (err) {
                jQuery.getparent().layer.msg(err);
            }
        }, ["childhealth.changecheckresult", jQuery.getparent().objdata.my.id, "2024-12-04 13:03", "2024-12-04 13:06"]);
    });

    // 批量打印
    $("#btnBatchPrint").click(function () {
        // 获取表格选中的数据
        var checkStatus = layui.table.checkStatus('laytable');
        var data = checkStatus.data;
        
        if(data.length === 0) {
            return jQuery.getparent().layer.msg('请选择要打印的档案');
        }
    
        // 收集选中的学生编号
        var stuArr = data.map(function(item) {
            return item.stuno;
        });
    
        // 打开打印页面
        jQuery.getparent().layer.open({
            type: 2,
            title: '批量打印',   
            shadeClose: false,
            area: ['100%', '100%'],
            btnAlign: 'c',
            content: "html/child/childhealthview.html?v=" + Arg("v") + '&mid=' + Arg("mid") + "&type=print&stuno=" + stuArr.join(',') + "&laiyuan=1",
            btn: ["打印预览", "导出Excel", "关闭"],
            success: function (layero, index) {
                jQuery.getparent().objdata.childhealthWindow = window;
            }, 
            yes: function (index, layero) {
                layero.find('iframe')[0].contentWindow.$('#btprint').trigger('click');
            }, 
            btn2: function (index, layero) {
                layero.find('iframe')[0].contentWindow.$('#btexcel').trigger('click');
                return false;
            }
        });
    }); 

    //测试发送小程序消息
    $("#btnsendmsg").click(function () {

        var msgtime = jQuery.getparent().objdata.moment().format("YYYY-MM-DD HH:mm:ss");

        // #########发送消息###############
        sendMsg("xcxcpmch", 1, ["ojPb86yI9kyX9NLBzKYdt5_aUoSw"], {
            "time3": {
                "value": msgtime
            },
            "thing2": {
                "value": "这是个消息内容"
            }
        });

        // ##########保存消息#############
        var uids = [2]
        for (var i = 0; i < uids.length; i++) {
            var xcxuid = uids[i];
            var dataObj = {
                msgtype: 3,//消息类型
                titile: "测试消息",//消息头
                msgtime: msgtime,//消息时间
                detailid: 0,//详情id
                yeyid: 0,//园所id
                guid: "",//区县guid
                receiveuid: xcxuid,//接收人id
                receivetype: 1,//接收人类型
                detailtype: 1,//详情消息类型
                otherdata: "{\"d\":123}"//其他数据
            }
            $.smaction(function (re, err) {
                if (err) {
                    jQuery.getparent().layer.msg("消息提醒保存失败" + err);
                }
                if (i == uids.length) {
                    cb && cb();
                }
            }, dataObj, { datastring: true, action: "message/savemsg/save" });
        }

    });

    //选择社区
    objdata.xs1 = xmSelect.render({
        el: '#abc1',
        toolbar: {
            show: false,
        },
        radio: true,
        tree: {
            show: true,
            showFolderIcon: true,
            showLine: false,
            indent: 20,
            expandedKeys: true,
        },
        iconfont: {
            parent: 'hidden' //隐藏父节点图标
        },
        autoRow: true,
        // paging: true,
        // pageSize: 10,
        data: objdata.remarkArr

    })
}


/**
 * 初始化列表
 */
function initlist() {
    var table = layui.table;
    var arrcols = [[ //标题栏
        {type: 'checkbox'}, // 添加复选框列
        {type: 'numbers', minWidth: 70, title: '序号'},
        {
            field: 'djmonth', title: '登记年月', align: 'left', templet: function (d) {
                return d.djmonth ? parent.objdata.moment(d.djmonth).format("YYYY-MM") : "";
            }
        },
        {field: 'fnumber', title: '电子档案号', align: 'left'},
        {field: 'stuname', title: '姓名', align: 'left'},
        {field: 'sex', title: '性别', align: 'left'},
        {field: 'birthday', title: '出生日期', align: 'center'},
        {
            field: 'age', title: '年龄', align: 'center', templet: function (d) {
                return GetAge(d.birthday, parent.objdata.curmoment.format("YYYY-MM-DD"), 'zh');
            }
        },
        {field: 'midcard', title: '母亲身份证号', align: 'left'},
        {field: 'tjnum', title: '体检次数', align: 'left'},
        {
            field: 'fromtype', title: '来源', align: 'center', templet: function (d) {
                var lyObj = {1: "新建", 2: "照片录入"};
                return lyObj[d.fromtype] || "";
            }
        },
        {
            field: 'huk_state', title: '户口状态', align: 'center', templet: function (d) {
                var lyObj = {1: "迁入", 2: "迁出"};
                return lyObj[d.huk_state] || "";
            }
        },
        {field: 'aruptime', title: '最近更新时间', align: 'center'},
        {
            title: '操作', minWidth: 220, align: 'center', fixed: 'right',
            templet: function (d) {
                var arrbtnhtml = [];
                arrbtnhtml.push('<a class="blue-txt layui-btn-xs" lay-event="jichu">基础信息</a>');
                arrbtnhtml.push('<a class="blue-txt layui-btn-xs" lay-event="tijian">体检记录</a>');
                arrbtnhtml.push('<a class="blue-txt layui-btn-xs" lay-event="print">打印</a>');
                arrbtnhtml.push('<a class="blue-txt layui-btn-xs" lay-event="del">删除</a>');
                return arrbtnhtml.join('');
            }
        }
    ]];
    table.render({
        elem: '#laytable',
        // toolbar: '#toolbarlaytable', //开启头部工具栏，并为其绑定左侧模板
        data: [],
        url: encodeURI($.getLayUrl() + "?" + $.getSmStr(["childhealth.table"])),
        where: {
            swhere: $.msgwhere(getswhere()),
            fields: 'stuno',
            types: 'desc'
        },
        height: 'full-' + ($("#laytable").offset().top + 10),
        autoSort: false,
        cols: arrcols,
        skin: 'row', //表格风格
        jump: function (obj1, obj2) {
        },
        done: function (o1, o2, count) {
            if (count === 0) {
                var backurl = "../../images/searchwhere.png";
                if (o1.hasOwnProperty("code")) {//后端返回值0条
                    backurl = "../../plugin/flexigrid/images/kb.png";
                }
                $(".layui-none").html('<div style="background: url(' + backurl + ') center center no-repeat;width: 100%;height: 100%"></div>').css({"height": "100%"});
            }
        },
        countNumberBool: true,
        even: true,
        page: true, //是否显示分页
        limits: [10, 20, 30, 50, 100],
        limit: 20 //每页默认显示的数量
    });
    table.on('sort(laytable)', function (obj) {
        table.reload('laytable', {
            where: {
                swhere: $.msgwhere(getswhere()),
                fields: obj.field, //排序字段
                types: obj.type, //排序方式                
                isPage: 0
            }
        });
    });
    table.on('tool(laytable)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
        var data = obj.data; //获得当前行数据
        var layEvent = obj.event; //获得 lay-event 对应的值（也可以是表头的 event 参数对应的值）
        if (layEvent === "jichu" || layEvent === "tijian") {
            openwin(layEvent, data.stuno, data.stuname, data.yeyid, data);
        } else if (layEvent === "print") {
            openwin(layEvent, data.stuno, data.stuname, data.yeyid, data);
        } else if (layEvent == 'del') {
            jQuery.getparent().layer.confirm("<span style='display:block;text-align: center;color: red'>确定删除该儿童档案数据吗？<br>删除后将无法恢复，请谨慎操作</span>", function (index) {
                // 删除1基本数据 2体检数据
                $.sm(function (re, err) {
                    if (err) {
                        return jQuery.getparent().layer.msg('删除失败' + err);
                    }
                    jQuery.getparent().layer.msg('操作成功');
                    search();
                    jQuery.getparent().layer.close(index);
                }, [["childhealth.del", data.stuno],
                    ["childhealth.tijian.del", data.stuno]])
            })
        }
    });
}

function openwin(type, stuno, name, yeyid, data) {
    if (type === "jichu" || type === "tijian") {
        jQuery.getparent().layer.open({
            type: 2,
            title: "添加",
            shadeClose: false,
            area: ['100%', '100%'],
            btnAlign: 'c',
            // btn: ["保存", "取消"],
            content: 'html/child/childhealthadd.html?v=' + Arg("v") + '&mid=' + Arg("mid") + "&type=" + type + "&stuno=" + stuno + "&laiyuan=1",
            success: function (layero, index) {
                jQuery.getparent().objdata.cur_Index = index;
                jQuery.getparent().objdata.childhealthFrame = window;
                jQuery.getparent().objdata.childhealthaddFrame = layero.find('iframe')[0];
            }
        });
    } else if (type === "print") {
        jQuery.getparent().layer.open({
            type: 2,
            title: '打印',   
            shadeClose: false,
            area: ['100%', '100%'],
            btnAlign: 'c', //按钮居中
            content: "html/child/childhealthview.html?v=" + Arg("v") + '&mid=' + Arg("mid") + "&type=" + type + "&stuno=" + stuno + "&laiyuan=1",
            btn: ["打印预览", "导出Excel", "关闭"],
            success: function (layero, index) {
            }, yes: function (index, layero) {
                layero.find('iframe')[0].contentWindow.$('#btprint').trigger('click');
            }, btn2: function (index, layero) {
                layero.find('iframe')[0].contentWindow.$('#btexcel').trigger('click');
                return false;
            }
        });
    }
    
}

function search() {
    layui.table.reload('laytable', {
        where: {
            swhere: $.msgwhere(getswhere()),
            fields: 'stuno',
            types: 'desc'
        },
        page: {
            curr: 1
        }
    })
}

function getswhere() {
    var objwhere = {};
    var keyword = $("#txtkeyword").val();
    if (keyword) {
        objwhere.keyword = [keyword];
    }

    var sex = $("#sex").val();
    if (sex) {
        objwhere.sex = [sex];
    }
    var age1 = $("#selage1").val();
    if (age1) {
        if (age1 == 0.01) {
            objwhere.age1 = [0, 0.03];
        } else if (age1 == 0.03) {
            objwhere.age1 = [0.03, 0.06];
        } else if (age1 == 0.06) {
            objwhere.age1 = [0.06, 0.08];
        } else if (age1 == 0.08) {
            objwhere.age1 = [0.08, 1];
        } else if (age1 == 1) {
            objwhere.age1 = [1, 1.06];
        } else if (age1 == 1.06) {
            objwhere.age1 = [1.06, 2];
        } else if (age1 == 2) {
            objwhere.age1 = [2, 2.06];
        } else if (age1 == 2.06) {
            objwhere.age1 = [2.06, 3];
        } else if (age1 == 3) {
            objwhere.age1 = [3, 4];
        } else if (age1 == 4) {
            objwhere.age1 = [4, 5];
        } else if (age1 == 5) {
            objwhere.age1 = [5, 6];
        } else if (age1 == 6) {
            objwhere.age1 = [6, 7];
        }

    }
    var fromtype = $("#fromtype").val();
    if (fromtype) {
        objwhere.fromtype = [fromtype];
    }
    var huk_state = $("#huk_state").val();
    if (huk_state) {
        objwhere.huk_state = [huk_state];
    }
    var djmonth = $("#txtstartdate").val();
    if (djmonth) {
        var momth = djmonth.split(" - ");
        objwhere.djmonth = [momth[0], momth[1]];
    }
    var birthday = $("#txtbirthday").val();
    if (birthday) {
        birthday = birthday.split(" - ");
        objwhere.birthday = [birthday[0], birthday[1]];
    }
    var tjckdate = $("#txtjckdate").val();
    if (tjckdate) {
        tjckdate = tjckdate.split(" - ");
        objwhere.tjckdate = [tjckdate[0], tjckdate[1]];
    }
    var remarkArr = objdata.xs1.getValue();
    if (remarkArr && remarkArr.length) {
        objwhere.address = [remarkArr[0].name];
    }
    return objwhere;
}

function getckDay() {
    var ckday = "";
    $.sm(function (re, err, obj) {
        if (err) {
            jQuery.getparent().layer.msg(err, {icon: 5});
        } else {
            ckday = obj.sysvalue || "";
        }
    }, ["childhealth.ckday"], null, null, {async: false});
    return ckday;
}

/**
 * 导入信息
 * @param src
 * @param name
 */
function importExcel(src, name) {
    jQuery.getparent().layer.load();
    objdata.arruntreated = [];
    var sheetArr = [];//[{sheetname,sheetindex,sheetfield,sheetvalue,sheetype}]
    sheetArr.push(
        {sheetname: "3岁", sheetfield: "ckage", sheetvalue: "3", sheetindex: 0, sheetype: "string"},
        {sheetname: "4岁", sheetfield: "ckage", sheetvalue: "4", sheetindex: 1, sheetype: "string"},
        {sheetname: "5岁", sheetfield: "ckage", sheetvalue: "5", sheetindex: 2, sheetype: "string"},
        {sheetname: "6岁", sheetfield: "ckage", sheetvalue: "6", sheetindex: 3, sheetype: "string"},
        {sheetname: "三岁", sheetfield: "ckage", sheetvalue: "3", sheetindex: 0, sheetype: "string"},
        {sheetname: "四岁", sheetfield: "ckage", sheetvalue: "4", sheetindex: 1, sheetype: "string"},
        {sheetname: "五岁", sheetfield: "ckage", sheetvalue: "5", sheetindex: 2, sheetype: "string"},
        {sheetname: "六岁", sheetfield: "ckage", sheetvalue: "6", sheetindex: 3, sheetype: "string"});
    var tableArr = [];//[{tablename:'child_checkresult',fieldarr:[{name,field,type}],where:[s1,s2],isneedsheet:true}}]
    tableArr.push({
        tablename: 'child',
        fieldarr: [
            {name: '姓名', field: 'stuname', type: 'string', fieldlen: 50},
            {name: '性别', field: 'sex', type: 'string', fieldlen: 1},
            {name: '出生日期', field: 'birthday', type: 'date', fieldlen: 10, "isconvert": {"k": "3"}},
            {name: '省网编号', field: 'fnumber', type: 'string', fieldlen: 20},
            {name: '保健编号', field: 'fnumber', type: 'string', fieldlen: 20},
            {name: '母亲身份证号', field: 'midcard', type: 'string', fieldlen: 18},
            {name: '母亲身份证', field: 'midcard', type: 'string', fieldlen: 18},
            {name: '联系电话', field: 'mophone', type: 'string', fieldlen: 20},
            {name: '电话', field: 'mophone', type: 'string', fieldlen: 20}
        ],
        where: ["fnumber"],
        linktable: '',
        linkfield_me: '',
        linkfield_other: '',
        isneedsheet: false
    });
    tableArr.push({
        tablename: 'child_checkresult',
        fieldarr: [
            {name: '体检日期', field: 'rday', type: 'date', fieldlen: 10, "isconvert": {"k": "3"}},
            {name: '时间', field: 'rday', type: 'date', fieldlen: 10, "isconvert": {"k": "3"}},
            {name: '日期', field: 'rday', type: 'date', fieldlen: 10, "isconvert": {"k": "3"}},
            {name: '出生日期', field: 'birthday', type: 'date', fieldlen: 10, "isconvert": {"k": "3"}},
            {name: '性别', field: 'sex', type: 'string', fieldlen: 1},
            {name: '身高', field: 'hight', type: 'int', fieldlen: 6},
            {name: '体重', field: 'weight', type: 'int', fieldlen: 6},
            {name: '血红蛋白', field: 'hematin_res', type: 'string', fieldlen: 20},
            {name: '血红蛋白', field: 'hematin', type: 'int', fieldlen: 5, "isconvert": {"k": "2", "v": 'hematin_beizhu'}},
            {name: '营养性疾病', field: 'yyxjb_res', type: 'string', fieldlen: 20},
            {name: '备注', field: 'remark', type: 'string', fieldlen: 300},
        ],
        where: ["ckage"],
        linktable: 'child',
        linkfield_me: 'stuno',
        linkfield_other: 'stuno',
        isneedsheet: true
    });

    $.sm(function (re, err) {
        if (err) {
            jQuery.getparent().layer.msg(err);
        } else {
            if (re && err) {
                jQuery.getparent().layer.msg("导入成功，其中以下未成功：<br/>" + err);
                location.reload();
            } else if (re) {
                jQuery.getparent().layer.msg("导入成功！");
                location.reload();
                //以下去掉 改为后台线程触发
                // 处理评价，血红蛋白，营养性疾病
                // $.sm(function (re, err) {
                //     if (err) {
                //         jQuery.getparent().layer.msg(err);
                //     }
                // }, ["childhealth.changecheckresult",jQuery.getparent().objdata.my.id,"",""]);
            } else if (err) {
                jQuery.getparent().layer.msg(err);
            } else {
                jQuery.getparent().layer.msg("没有符合导入的数据！");
            }
        }
        jQuery.getparent().layer.close(jQuery.getparent().objdata.yeyimpyey);
        jQuery.getparent().layer.closeAll("loading");
    }, ["excel.importdata", src, JSON.stringify(sheetArr), JSON.stringify(tableArr)]);
}

/**
 * 删除上传文件
 * @param layero
 * @returns {boolean}
 */
function deletefile(layero) {
    var childpage = layero.find('iframe')[0].contentWindow;
    var src = childpage.$('#src').val();
    if (!src) {
        return true;
    }

    $.sm(function (re, err) {
    }, ["deletefile", src]);
}