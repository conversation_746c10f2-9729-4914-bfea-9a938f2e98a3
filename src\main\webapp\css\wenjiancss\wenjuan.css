/*切换*/
.tab-common3 li{height: 45px;font-size: 16px;}
.tab-common3 li.current{color: #00cd91;}
.tab-common3 li.current:before{background: #00cd91;width: 30%;margin-left: -15%;}
.tab-common3 li img{width: 25px;height: 25px;margin-right: 5px;}
.mould-ul{background: #ffffff;margin-top: 7px;font-size: 14px;color: #666666;white-space: nowrap;overflow-x: auto;padding: 8px 5px;}
.mould-ul li{-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;text-align: center;display: inline-block;margin: 0 5px;}
.mould-ul li a{padding: 2px 15px;display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;}
.mould-ul li.current a{background: #e1f8f1;color: #00cd91;padding: 2px 15px 1px 15px;border-radius: 20px;}
.search-sign{align-items: center;width: 24px;height: 37px;padding: 0 10px;background: #00cd91;margin-top: 4px;border-radius: 20px 0 0 20px;}
/*问卷模板*/
.mould-cell{background: #ffffff;margin: 6px 5px;border-radius: 4px;padding: 10px 12px;}
.mould-cell .star-img{vertical-align: top;margin: 3px 0 0 -6px;text-align: center;float: left;width: 30px;}
.mould-cell .star-img img{width: 18px;height: 18px;}
.mould-cell .weui-btn{height: 24px;line-height: 24px;border-radius: 20px;font-size: 12px;margin: 0!important;
  background: -webkit-linear-gradient(left,#69c5ff, #53a2e9); /* Safari 5.1 - 6.0 */
  background: -o-linear-gradient(left,#69c5ff, #53a2e9); /* Opera 11.1 - 12.0 */
  background: -moz-linear-gradient(left,#69c5ff, #53a2e9); /* Firefox 3.6 - 15 */
  background: linear-gradient(left,#69c5ff, #53a2e9); /* 标准的语法 */
}
.mould-cell .ellipsis-clamp{font-size: 16px;color: #333333;margin-top: 5px;-webkit-line-clamp: 1;line-height: 22px;}
.mould-cell .mould-opr{font-size: 12px;color: #999999;position: relative;justify-content: space-between;margin: 10px 0 5px 0;}
.mould-cell.mould-add{height: 40px;display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;}
/*搜索*/			
.btn-remove:before{color: #d4d4d4;margin-right: 0;}
.data-con{/*background-color: #ffffff*/}
/*无内容*/
.nodata-con{width:100%;padding: 13px 0;background: #ffffff;margin-top: 7px;position: absolute;top: 45px;bottom: 0;}
.nodata-txt{width: 85%;height: 93px;background-size: 100% 100%;margin: auto;flex-direction:column;background: url(images/nodata_bg.png)no-repeat;}
.nodata-txt p{font-size: 12px;color: #b4bac6;margin-top: 5px;}
/*批量添加*/
.option-label{background: #ffffff;padding: 5px 11px;}		
.option-label span{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;min-width: 80px;border-radius: 20px;font-size: 13px;color: #666666;background: #f5f5f5;height: 28px;margin: 5px 4px;}
.option-label span.current{background: #e1f8f1;color: #00cd91;}			
.mould-heading{font-size: 14px;color: #333333;padding: 12px 15px 6px 15px;}	
.option-title{background: #ffffff;padding: 7px 15px;}
.option-title textarea{border: none;width: 100%;display: block;height: 46px;}			
ul.option-ul{background: #ffffff;padding: 0 15px;font-size: 14px;color: #333333;}
ul.option-ul li{padding: 9px 0;border-bottom: 1px dashed #dddddd;}
ul.option-ul li input{border: none;width: 100%;}	
ul.option-ul li:last-child{border-bottom: none;}	
/*我参与的*/
.questionnaier-con{position: relative;background: #ffffff;margin: 10px 15px 0 15px;border-radius: 3px;padding: 0 10px 0 10px;}
.stat-sign{position: absolute;left: -3px;top: 11px;display: inline-block;line-height: 22px;text-align: center;width: 52px;height: 28px;font-size: 12px;color: #ffffff;}
.stat-sign.redbg{background: url(images/mark_redbg.png)no-repeat;background-size: 100% 100%;}
.stat-sign.greenbg{background: url(images/mark_greenbg.png)no-repeat;background-size: 100% 100%;}
.stat-sign.graybg{background: url(images/mark_graybg.png)no-repeat;background-size: 100% 100%;}
.questionnaier-txt1{display: -webkit-box;display: -webkit-flex;display: flex;justify-content: center;flex-direction: column;position: relative;border-bottom: 1px dashed #dddddd;padding: 7px 15px 6px 45px;min-height: 34px;}
.questionnaier-txt1 h5{font-size: 18px;color: #333333;font-weight: bold;-webkit-line-clamp: 1;}
.questionnaier-txt1 p{margin-top: -2px;font-size: 11px;color: #999999;}
.questionnaier-txt1 p label{font-size: 12px;color: #333333;font-weight: bold;margin-left: 10px;}
.questionnaier-txt2{flex-direction: column;justify-content: center;line-height: 26px;font-size: 12px;color: #666666;border-bottom: 1px dashed #dddddd;padding: 4px 15px 4px 0;min-height: 32px;}
.questionnaier-txt2 p{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;}	
.questionnaier-txt3{align-items: center;justify-content: space-between;padding: 6px 0;font-size: 12px;}
/*新建空白问卷*/
.remind-title{align-items: center;justify-content: space-between;background: #4fa2f8;font-size: 12px;color: #ffffff;height: 30px;padding: 0 8px 0 15px;}
.question-outer{background: #ffffff;margin-top: 7px;padding: 10px 0 3px 0;}
.question-outer.current{border: 2px dashed #2a93ff;}
.question-outer.current .question-inner{border: 1px dashed #2a93ff;}
.question-outer.current .question-addson{border: 1px dashed #2a93ff;}
.question-outer h5{font-size: 16px;color: #666666;align-items: center;justify-content: space-between;padding: 0 15px;}
.question-outer h5 label{font-size: 15px;color: #333;margin-right: 10px;}
.question-outer h5 input{font-size: 15px;}
.question-outer ul li,.question-outer .divadditem{font-size: 14px;color: #333333;display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: space-between;padding: 3px 13px 3px 22px;min-height: 32px;}
.question-outer ul li{
    flex-direction: column;align-items: flex-start;
}
.question-outer ul li.current{background: #e9f1f9;}
.question-outer .btn-remove,.question-outer .icon_add_icon{height: 30px;margin-right: 5px;width: 30px;margin-left: -5px;text-align: center;}
.question-outer .btn-remove:before{color: #ec6862;}
.question-outer input[type="text"]{border: none;background: none;}
.question-opr{align-items:center;background: #e9f1f9;height: 45px;padding: 0 10px;margin-bottom: -3px;}
.question-opr span{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;margin: 0 3px;height: 25px;line-height: 26px;color: #ffffff;background: #2993ff;font-size: 12px;border-radius: 20px;}
.question-opr span i{margin-right: 5px;}
.questicon-pic{position: relative;display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;width: 25px;height: 25px;}
.questicon-pic .btn-remove{position: absolute;top: -11px;left: -18px;margin: 0;}			
.questicon-textarea{padding: 10px 15px;background: #f1f1f1;margin: 10px 15px;border-radius: 2px;}
.questicon-textarea input{width: 100%;height: 100%;border: none;background: none;}
.questicon-textarea textarea{width: 100%;height: 100%;border: none;background: none;display: block;line-height: 20px;height: 60px;}
.star-rank{padding: 0 15px;}
.star-rank i{margin: 0 5px;}
.question-inner{background: #fbfbfb;margin: 7px 8px 10px 8px;border: 1px dashed #d3d3d3;}
.question-inner h5{padding: 12px 15px 3px 15px;}
.question-inner h5 span label{font-size: 16px;}
.question-inner input[type="text"]{background: #fbfbfb;}
.question-inner .question-opr{background: #f1f1f1;margin-bottom: 0;padding: 0 1px;}
.question-addson{font-size: 14px;color: #333333;height: 43px;background: #ffffff;margin: 18px 8px;border: 1px dashed #cccccc;}
.add-type .iconfont{margin-right: 10px;}
.add-type .iconfont:before{color: #ffffff;height: 36px;font-size: 18px;}
.add-type .iconfont.btn-add:before{font-size: 22px;}
.add-type a{background: #00cd91;font-size: 15px;margin: 0 6px!important;}
.oprbtm-btn{box-shadow: 10px 10px 10px 10px #e9e9e9;}
.oprbtm-btn a{margin: 0!important;border-radius: 0;font-size: 15px;height: 50px;line-height: 48px;}
.oprbtn-left{width: 60%;}	
.oprbtn-left span{max-width: 30.5%;}	
.oprbtn-right{width: 40%;}	
.oprbtn-right span{max-width: 46%;background: #00cd91;}		
.question-option{align-items: center;justify-content: space-between;width: 100%;}
.question-inp{width: 100%;}
.question-inp input{background: #f1f1f1!important;width: 100%;height: 40px;padding: 0 12px;margin: 3px 0 3px 30px;}
/*统计模版*/
.questionnaier-census{ overflow: hidden; }
.questionnaier-census .weui-cell::before{ border-top: none;}
.questionnaier-census .weui-cell{background: #fff;margin: 10px 26px 0 26px; font-size: 15px; color: #333333;box-shadow: 0px 3px 3px 1px rgba(239,238,239,0.9);}
.questionnaier-census .edit-bg{ display:inline-block;  background:#DDE9FF; width: 26px; height:26px; text-align: center; border-radius:50%; }
.questionnaier-census .set-radio-style3 input[type="radio"]{ margin-top: -2px;}
/*问卷设置*/
.questionnaire-list {background: #fff; padding:10px 0 0 0;}
.questionnaire-list textarea{margin: 0 15px;width: 90%;height:60px;padding: 10px 0; border: none;}
.questionnaire-list h3{ margin: 0 0 0 15px;border-bottom: 1px dashed #dddddd;overflow: hidden; font-size: 15px; font-weight: normal; line-height: 40px;padding: 5px 15px 5px 0;}
.questionnaire-list p{ font-size: 14px; color: #999999;}
.questionnaire-list input{text-align:left;border: none;outline: none; font-size: 20px; color:#333333;}
.questionnaire-setting .weui-cell:before{left: 0;}
.questionnaire-bot {background: #fff; margin-top: 7px;}
.questionnaire-bot .weui-cell:before{border-top: 1px dashed #dddddd;}
.questionnaire-bot .weui-cell:before{ left:15px;}
.questionnaire-setting .weui-cell__bd {font-size: 15px;}
/*统计结果*/
.count-cell{background: #ffffff;margin-top: 7px;padding: 0 15px;}
.count-cell h5{font-size: 16px;color: #333333;padding: 16px 0;}
.count-cell h6{border-top: 1px dashed #dddddd;padding: 3px 0 10px 0;font-size: 16px;}
.table-cell{padding-bottom: 10px;}			
.table-cell table{text-align: center;border: 0;border-left: 1px solid #e3e3e3;}	
.table-cell table th{height: 43px;background: #8ec7ff;font-size: 15px;color: #ffffff;border-right: 1px solid #ffffff;}	
.table-cell table th:last-child{border-right: 1px solid #8ec7ff;}
.table-cell table td{padding: 0 10px;line-height: 16px;height: 41px;font-size: 14px;color: #666666;border-right: 1px solid #dedede;border-bottom: 1px solid #dedede;}		
.table-cell table td:first-child{color: #333333;font-weight: bold;}	
.table-cell table tr.yellowtable{background: #f7f1da;}		
.table-cell table a{font-weight: normal;color: #4ea7fe;text-decoration: underline;} 
.select-div{background: #ffffff;height: 43px;border-bottom: 1px solid #dddddd;margin: 7px 0 -7px 0;}		
.select-div select{width: 100%;border: none;outline: none;}					
.name-label{font-size: 13px;color: #666666;background: #EEEEEE;height: 22px;padding: 0 10px;border-radius: 3px;margin-bottom: 10px;}	
/*查看阅读提交情况*/		
.title-markline{padding-left: 25px!important;height: 36px;}
.title-markline:after{content: "";width: 4px;height: 18px;background: #00cd91;position: absolute;left: 15px;border-radius: 10px;}
.read-cen .weui-cells{border: none;}
/*导出结果*/
.explain-txt{text-align: center;position: relative;background: #ffffff;}
.explain-txt span{font-size: 13px;color: #999999;background: #ffffff;padding: 15px 7px;text-align: center;position: relative;}
.explain-txt:before{content: "";height: 1px;width: 100%;position: absolute;left: 0;top: 15px;
  background: -webkit-linear-gradient(left, #f6f6f6, #999999, #f6f6f6); /* Safari 5.1 - 6.0 */
  background: -o-linear-gradient(left, #f6f6f6, #999999, #f6f6f6); /* Opera 11.1 - 12.0 */
  background: -moz-linear-gradient(left, #f6f6f6, #999999, #f6f6f6); /* Firefox 3.6 - 15 */
  background: linear-gradient(left, #f6f6f6, #999999, #f6f6f6); /* 标准的语法 */
}
.mask-con{background: url(images/result_txtbg.png);background-size: 230px;}
.mask-con .count-cell{padding: 0 15px;position: relative;z-index: -1;}
.mask-con .count-cell:after{content: "";width: 100%;height: 7px;background: #f6f6f6;position: absolute;left: 0px;}
.mask-txtbg{background: url(images/result_txtbg.png);width: 100%;height: 100%;position: absolute;top: 0;bottom: 0;background-size: 230px;opacity: 0.7;}
/*数据加密安全防护*/
.data-panel{padding: 18px 0;box-shadow: 0px 4px 9px -1px #ebf1fd;margin: -49px 15px 0 15px;font-size: 14px;color: #191818;flex-direction: column;background: #ffffff;border-radius: 5px;}
.data-panel h5{text-align: center;font-size: 16px;color: #3f7ee9;margin-bottom: 16px;}
.data-panel img{width: 36px;height: 36px;}
/*插入题目弹框*/
.insert-con{background: #ffffff;border-radius: 20px 20px 0 0;}
.insert-con h5{font-size: 17px;color: #333333;height: 50px;border-bottom: 1px dashed #dddddd;}
.insert-con .weui-cell{padding: 14px 0;border-bottom: 1px solid #dddddd;margin: 0 15px;}
.insert-con .weui-cell:before{content: none;}
.insert-con .weui-cell__ftp{padding: 0;}
.insert-con .weui-cell__ftp a{font-size: 14px;height: 28px;line-height: 28px;margin: 0;}
.insert-type{font-size: 13px;color: #666666;height: 103px;align-items: center;justify-content: space-around;}
.insert-type>div{display: -webkit-box;display: -webkit-flex;display: flex;flex-direction: column;align-items: center;}
.insert-type>div img{margin-bottom: 8px;}
.no-record{text-align: center;padding: 60px 0;}
.no-record p{text-align: center;color: #333333; font-size: 15px;}
