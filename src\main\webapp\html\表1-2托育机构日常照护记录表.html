<!DOCTYPE html>
<html>
<head>
<title>幼儿健康评价结果报告单</title>
<meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
<link rel="stylesheet" href="../css/reset.css"/>
<link rel="stylesheet" href="../css/style.css"/>
<link rel="stylesheet" href="../css/icon.css">
<script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
<style>
body {
	background: #fff;
}
</style>
</head>
<body>
<div style="width: 90%;margin: 0 auto;">
<div class="rep-table tab_parent_sec" style="width: 90%;margin: 0 auto 20px auto;padding: 30px 0 0px 0;">
	<div class="divhead" title="表4-2托育机构膳食营养分析表" style="text-align: center; font-size: 16pt; font-weight: bold; font-family: 宋体, 微软雅黑, 黑体; margin: 0px auto; width: 100%;">表1-2托育机构日常照护记录表</div>
    	
	</div>

	<div style="height:25px;">
		<div class="divprintreport" style="font-size:10pt;font-weight:bold;text-align: left;display:inline;"> 
			<span class="lbselectdate0"> 班级：</span> <span style="float:right;"> 姓名： 
			<label style="display:inline-block; margin-left:80px"> </label>
			</span> 
		</div>
	</div>
       <table class="reporttable" style="clear: both; margin: 0px auto; font-size: 11pt; font-family: 宋体; border: 2px solid black; width: 100%;" cellspacing="0" cellpadding="1" border="0">
		<thead>
			<tr>
					 <th width="9%" style=" border-left:1px solid Black;border-top:1px solid Black;">日期</th>
			
		
				<th colspan="3" style=" border-left:1px solid Black;border-top:1px solid Black;">进 食</th>
				<th colspan="2" style=" border-left:1px solid Black;border-top:1px solid Black;"><p ><strong>睡眠</strong></p></th>
                <th colspan="2" style=" border-left:1px solid Black;border-top:1px solid Black;"><p ><strong>户外活动</strong></p></th>
                      <th colspan="2" style=" border-left:1px solid Black;border-top:1px solid Black;"><p ><strong>大便</strong></p></th>
                         <th colspan="2" style=" border-left:1px solid Black;border-top:1px solid Black;"><p ><strong>小便</strong></p></th>
                           <th colspan="2" style=" border-left:1px solid Black;border-top:1px solid Black;"><p ><strong>饮水</strong></p></th>
                               <th colspan="2" style=" border-left:1px solid Black;border-top:1px solid Black;"><p ><strong>体温</strong></p></th>
                                  <th rowspan="2" style=" border-left:1px solid Black;border-top:1px solid Black;"><p ><strong>其他</strong></p>                                    <p >&nbsp;</p></th>
				
			</tr>
			<tr>
              <th  style=" border-left:1px solid Black;border-top:1px solid Black;"><p >&nbsp;</p></th>
			  <th  style=" border-left:1px solid Black;border-top:1px solid Black;"><p >时间</p></th>
			  <th  style=" border-left:1px solid Black;border-top:1px solid Black;"><p >餐次</p></th>
                <th  style=" border-left:1px solid Black;border-top:1px solid Black;"><p >情况</p></th>
              
			  <th  style=" border-left:1px solid Black;border-top:1px solid Black;"><p >时间</p></th>
			  <th  style=" border-left:1px solid Black;border-top:1px solid Black;"><p >时长</p></th>
                <th  style=" border-left:1px solid Black;border-top:1px solid Black;"><p >时间</p></th>
			  <th  style=" border-left:1px solid Black;border-top:1px solid Black;"><p >时长</p></th>
                <th  style=" border-left:1px solid Black;border-top:1px solid Black;"><p >时间</p></th>
			  <th  style=" border-left:1px solid Black;border-top:1px solid Black;"><p >性状</p></th>
                <th  style=" border-left:1px solid Black;border-top:1px solid Black;"><p >时间</p></th>
			  <th  style=" border-left:1px solid Black;border-top:1px solid Black;"><p >颜色</p></th>
                <th  style=" border-left:1px solid Black;border-top:1px solid Black;"><p >时间</p></th>
			  <th  style=" border-left:1px solid Black;border-top:1px solid Black;"><p >量</p></th>
                <th  style=" border-left:1px solid Black;border-top:1px solid Black;"><p >时间</p></th>
			  <th  style=" border-left:1px solid Black;border-top:1px solid Black;"><p >数值</p></th>
          </tr>
		
		</thead>
		<tbody>
			
            
            	<tr style="line-height:20px;height:20px;">
				<td align="center" style=" border-left:1px solid Black;border-top:1px solid Black;"></td>
       
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"></td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"></td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"></td>
                <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"></td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"></td>
                <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"></td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"></td>
                <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"></td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"></td>
                <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"></td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"></td>
			
			</tr>
            	
			
       	  <tr style="line-height:20px;height:20px;">
				<td align="center" style=" border-left:1px solid Black;border-top:1px solid Black;"><p >日合计</p></td>
       
				<td colspan="3" align="center" style=" border-left:1px solid Black;border-top:1px solid Black;">			    </td>
				<td colspan="2" align="center" style=" border-left:1px solid Black;border-top:1px solid Black;"></td>
                <td colspan="2" align="center" style=" border-left:1px solid Black;border-top:1px solid Black;">
                
                <td colspan="2" align="center" style=" border-left:1px solid Black;border-top:1px solid Black;">                </td>
				<td colspan="2" align="center" style=" border-left:1px solid Black;border-top:1px solid Black;"></td>
                <td colspan="2" align="center" style=" border-left:1px solid Black;border-top:1px solid Black;">
                
                <td colspan="2" align="center" style=" border-left:1px solid Black;border-top:1px solid Black;">                </td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"></td>
			
		  </tr>
         </tbody>
         </table>
        <div class="divprintreport" style="font-size:10pt;font-weight:bold;text-align: left;display:inline;">备注：1.“进食餐次”一栏填写：“早餐、早点、午餐、午点、晚餐”。2.“进食情况”、“大便性状”及“小便颜色”一栏中填写“正常”、“不正常”，若“不正常”请具体描述。3.其他是指精神、身体、行为、意外伤害等异常。4.本表由保育人员填写。5.若本机构有类似记录表，可不做重复填写。
         </div>
      
        
</div>
</div>
<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../system/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../system/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../plugin/nicescroll/jquery.nicescroll.min.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../plugin/js/moment.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../plugin/viewer/viewer.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../js/order/orderdetail.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>
