﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>传染病患病监控</title>
    <link rel="stylesheet" href="../css/reset.css" />
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../css/icon.css">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/bigdata.css">
    <link rel="stylesheet" href="../plugin/jqcloud/jqcloud.css">
    <script type="text/javascript">
        document.write("<link rel=\"stylesheet\" id=\"linktheme\" href=\"../css/" + parent.objdata.curtheme + ".css\" type=\"text/css\" media=\"screen\"/>");
    </script>
    <!--[if lt IE 9]>
    <script src="../sys/html5shiv.min.js"></script>
    <![endif]-->
    <style type="text/css">
        body { background: #000f1d; }
        .wrapouter, .circle, .percent { position: absolute; width: 72px; height: 72px; border-radius: 50%; }
        .wrapouter { background-color: #eee; }
        .circle { box-sizing: border-box; border: 4px solid #eee; clip: rect(0,72px,72px,36px); }
        .clip-auto { clip: rect(auto, auto, auto, auto); }
        .percent { box-sizing: border-box; top: -4px; left: -4px; }
        .perleft { transition: transform ease; border: 4px solid #fc784f; clip: rect(0,36px,72px,0); }
        .perright { border: 4px solid #fc784f; clip: rect(0,72px,72px,36px); }
        .perleft.green { border: 4px solid #00a84d; }
        .perleft.red { border: 4px solid red; }
        .wth0 { width: 0; }
        .centercontent { position: absolute; box-sizing: border-box; width: 64px; height: 64px; text-align: center; left: 4px; top: 4px; border-radius: 50%; background-color: #fff; z-index: 1; padding: 5px 10px 0 10px; display: flex; display: -webkit-flex; align-items: center; justify-content: center; flex-direction: column; color: #52ffff; background: #03162d; }
        .wrapouter .perleft, .wrapouter .perright { border-color: #52bfef; }
        .chart-map { width: 85%; height: 90%; margin: -20px 0 0 -15px; }

        /*@media screen and (max-height: 800px) {*/
        /*    .chart-map { height: 80%; }*/
        /*}*/

        .anchorBL { display: none; }
    </style>
</head>
<body>
    <section>
<!--        <div style="height: 95px;position: absolute;width: 100%;z-index: 99;">-->
<!--            <div style="width: 100%;height: 12px;background: #041425;"></div>-->
<!--                <div style="margin-left: 10px;"><div class="bigdata-top">-->
<!--                <div class="bigdata-time">-->
<!--                    &lt;!&ndash;				<span style="display: inline-block;vertical-align: top;margin: 15px 25px 0 5px;color: #fff;font-size: 16px;" id="spcurdatetime"></span>&ndash;&gt;-->
<!--                    <span style="display: inline-block;vertical-align: top;" id="spcurdatetime"><label id="headtime" style="font-size: 30px;font-weight: bold;"></label><label id="headdate"></label></span>-->
<!--                </div>-->
<!--                <div style="display: inline-block;">-->
<!--                    <div class="bigdata-title"><span id="labcityname"></span>传染病患病监控</div>-->
<!--                </div>-->
<!--                <div class="bigdata-topright">-->
<!--&lt;!&ndash;                    <i class="iconfont icon_time"></i>&ndash;&gt;-->
<!--&lt;!&ndash;                    <span style="display: inline-block;vertical-align: top;margin: 6px 25px 0 5px;" id="spcurdatetime"></span>&ndash;&gt;-->
<!--&lt;!&ndash;                    <i id="fullScreen" class="iconfont icon_fullscreen" style="cursor: pointer;"></i>&ndash;&gt;-->
<!--&lt;!&ndash;                    <i id="exitFullScreen" class="iconfont icon_reducescreen" style="cursor: pointer;display:none;"></i>&ndash;&gt;-->
<!--                    <img src="../images/bigdata/fullscreen.png" style="width: 21px;cursor: pointer;"  id="fullScreen">-->
<!--                    <img src="../images/bigdata/exitfullscreen.png" style="width: 21px;cursor: pointer;display:none;" id="exitFullScreen">-->
<!--                </div>-->
<!--					  </div>-->
<!--			</div>-->
<!--        </div>-->
        <div class="bigdata-con" style="overflow: hidden;">
            <div style="font-size: 0;height: 68%; margin:15px 0px 0 10px;">
                <div class="bigdata-cell" style="width: 40%;height: 100%;">
                    <div class="bigdata-list diseasedata-top" style="height: 100%;margin:0;">
						<h3 class="diseasedata-tit"><p><i class="report-rtit"></i><span>患传染病幼儿的热力分布图</span></p></h3>
                        <div class="bigdata-detail">
                            <div style="height: 100%;">
                                <div style="display: inline-block;margin: 22px 18px;line-height: 22px;color: #00cccd;width: 100%;height: 100%;">
                                    <div class="div_maptitle" style="position:absolute;z-index:999;color: #BBB;">
                                        <p>园所数：<span style="color: #E9AE39;" id="p_yeynum" class="font20"></span>所</p>
                                        <p>总幼儿数：<span style="color: #28fe08;" id="p_yenum" class="font20"></span>人</p>
                                        <p>患传染病人数：<span style="color: #CE4E5B;" id="p_crbnum" class="font20"></span>人</p>
                                    </div>
                                    <div class="chart-map" id="divindiseasesmap" style="width:100%;height:99%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bigdata-cell" style="width: 60%;height: 100%;">
                    <div class="bigdata-list diseasedata-top" style="height: 100%; margin:0;margin-left: 10px;">
                        <h3 class="diseasedata-tit"><p><i class="report-rtit"></i><span>各年龄段传染病患病</span></p></h3>
                        <div class="bigdata-detail">
                            <div style="margin:0 auto; width: 100%;height: 100%;" id="divagecrbcount">
                                <!--								<img src="images/indiseases2.png" style="width: 100%;height: 535px;">-->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div style="font-size: 0;height: 32%;margin:15px 0px 0 10px;">
                <div class="bigdata-cell" style="width: 46%;height: 100%; margin-right: 10px;">
                    <div class="bigdata-list diseasedata-top" style="height: 100%;margin:0;">
						 <h3 class="diseasedata-tit"><p><i class="report-rtit"></i><span>传染病幼儿分析</span></p></h3>
                        <div class="bigdata-detail delheight">
                            <div style="font-size: 0;height: 100%;">
                                <div style="display: inline-block;vertical-align: top;width: 36%;height:100%;font-size: 14px;">
                                    <div class="progress-cell data-zhzs1" style="text-align: center;height:100%">
<!--                                        <div class="procell-con" style="float: initial;">-->
<!--                                            <div class="wrapouter">-->
<!--                                                <div class="circle">-->
<!--                                                    <div class="percent perleft red"></div>-->
<!--                                                    <div class="percent perright wth0"></div>-->
<!--                                                </div>-->
<!--                                                <div class="centercontent">-->
<!--                                                    <p id="p_crbrate"></p>-->
<!--                                                </div>-->
<!--                                            </div>-->
<!--                                        </div>-->
										<div>
											<div style="height: 50%;">
												<div id="indiseasesrate" style="height: 100%;">
												</div>
											</div>
											<div style="height: 50%;">
												<p style="color: #BBB;text-align: center;line-height: 18px;margin-top: 5px;">相当于每<span style="font-size: 24px;font-weight: bold;color: #41CF83;">1000</span>孩子</p>
												<p style="color: #BBB;text-align: center;line-height: 18px;">有<span style="font-size: 24px;font-weight: bold;color: #f18e4f;" id="sp_getcrbnum"></span>人得传染病</p>
											</div>
										</div>
                                    </div>
                                </div>
                                <div id="divdatastate" style="display: inline-block;vertical-align: top;width: 28%;height: 100%;padding-top:10px;font-size: 14px;">
                                    <div class="data-state">
                                        <img src="../images/xbimages/people-ico.png" style="width: 23px; margin:0 10px; line-height: 20px;">
                                        <span id="sp_allcrb"></span>人
                                    </div>
                                    <div class="data-state" style="color:#5684ec;">
                                         <img src="../images/xbimages/boy-ico.png" style="width: 23px; margin:0 10px; line-height: 20px;">
                                        <span id="sp_allbody"></span>人
                                    </div>
                                    <div class="data-state current" style="color: #f55356;">
                                        <img src="../images/xbimages/girl-ico.png" style="width: 23px; margin:0 10px; line-height: 20px;">
                                        <span id="sp_allgirl"></span>人
                                    </div>
                                </div>
                                <div style="display: inline-block;width: 36%;font-size: 14px;height: 100%;">
                                    <div style="width: 100%;height: 100%;" id="divagecrbpie"></div>
                                    <!--									<img src="images/indiseases3.png" style="width: 100%;height: 185px;">-->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bigdata-cell" style="width: 26%;height: 100%; margin-right:10px;">
                    <div class="bigdata-list diseasedata-top" style="height: 100%;margin:0;">
						<h3 class="diseasedata-tit"><p><i class="report-rtit"></i><span>传染病患病 TOP5</span></p></h3>
<!--                        <div class="bigdata-detail">-->
                            <ul class="bigdata-detail delheight progress-list" id="ul_crbtop5">
                                <!--<li>
                                    <span class="pro-name">水痘</span>
                                    <div class="pro-content">
                                        <div class="pro-bar" style="background: #6799ff;width: 40%;"></div><span class="pro-txt">174人</span>
                                    </div>
                                </li>-->
                            </ul>
<!--                        </div>-->
                    </div>
                </div>
                <div class="bigdata-cell" style="width: 26%;height: 100%;">
                    <div class="bigdata-list  diseasedata-top" style="height: 100%;margin:0;">
						 <h3 class="diseasedata-tit"><p><i class="report-rtit"></i><span>各传染病占总患病的比例</span></p></h3>
                        <div class="bigdata-detail delheight">
                            <div style="width: 100%;height: 100%;" id="divcrbrate">
                                <!--								<img src="images/indiseases4.png" style="width: 100%;height: 185px;">-->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <script>
        var v = top.version;
        document.write("<script type='text/javascript' src='../sys/arg.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../sys/jquery.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../sys/function.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../js/areacoordinate.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='https://api.map.baidu.com/api?v=3.0&ak=bkpk4sh4cFcEGqgqjUG79DfRGhqNS7W8'><" + "/script>");
        // document.write("<script type='text/javascript' src='../plugin/echarts-latest/echarts.js?v=" + v + "'><" + "/script>");
        // document.write("<script type='text/javascript' src='../plugin/echarts-latest/extension/bmap.min.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../plugin/echart-5.1.2/dist/echarts.min.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../plugin/echart-5.1.2/dist/extension/bmap.min.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../plugin/mock/dist/mock.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../js/bigdata_common.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../js/bigdatamap.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../js/bigdata_indiseases.js?v=" + v + "'><" + "/script>");
    </script>
</body>
</html>
