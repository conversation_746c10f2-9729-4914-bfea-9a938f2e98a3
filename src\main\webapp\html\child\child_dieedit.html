﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>死亡儿童登记</title>
    <link rel="stylesheet" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../plugin/jQueryValidationEngine/css/validationEngine.jquery.css" type="text/css"/>
    <link rel="stylesheet" href="../../css/style.css"/>
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <!--[if lt IE 9]>
    <script src="../../sys/html5shiv.min.js"></script>
    <![endif]-->
    <style>
        .u-edit {
            display: none;
        }
        .divrole {
            display: none;
        }
        .layui-form-label {
            width: 160px;
            /*text-align: left;*/
        }
        .location{
            cursor: pointer;
            padding: 4px 4px 4px 20px;
            margin-left: 4px;
            background: url(../../images/locate.png) 0 2px no-repeat;
            background-size: 20px;
        }
        #bmapicon{
            display: inline-block;
            background: url(../../images/bmapicon.png) center no-repeat;
            background-size: 20px;
            width: 30px;
            height: 20px;
            float: right;
        }
        .theme-fgc{
            color: #3aa6ff !important;
        }
        .layui-input-inline{
            width: 260px;
        }
        .layui-form-label{padding: 9px 5px;}
    </style>
</head>
<body>
<section class="personalInfo font14 cl1 ">
    <form id="form" class="layui-form" style="height: 100%;width: 100%;position: absolute">
        <section id="contant" style="overflow-y: auto;overflow-x: hidden" class="clearfix pd20">
            <section class=" fl">
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>母亲姓名：</label>
                    <div class="layui-input-inline" style="width: 350px;">
                        <input id="txtmoname" type="text" name="moname" required lay-verify="required" placeholder="请输入母亲姓名" maxlength="20" autocomplete="off" class="layui-input">
                    </div>
                    <a id="btnselchildinfo" style="margin-left:5px;vertical-align: top;display: none;" class="layui-btn">选择</a>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>母亲电话：</label>
                    <div class="layui-input-inline" style="width: 350px;">
                        <input id="txtmophone" type="text" name="mophone" required lay-verify="required" placeholder="请输入母亲电话" maxlength="20" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>父亲姓名：</label>
                    <div class="layui-input-inline" style="width: 350px;">
                        <input id="txtfaname" type="text" name="faname" required lay-verify="required" placeholder="请输入父亲姓名" maxlength="20" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>父亲电话：</label>
                    <div class="layui-input-inline" style="width: 350px;">
                        <input id="txtfaphone" type="text" name="faphone" required lay-verify="required" placeholder="请输入父亲电话" maxlength="20" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>常住地址：</label>
                    <div class="layui-input-inline" style="width: 350px;">
                        <input id="txtpermanentaddress" type="text" name="permanentaddress" autocomplete="off" required lay-verify="required" placeholder="请输入常住地址" class="layui-input" maxlength="100">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>现住地址：</label>
                    <div class="layui-input-inline" style="width: 350px;">
                        <input id="txtnowaddress" type="text" name="nowaddress" autocomplete="off" required lay-verify="required" placeholder="请输入现住地址" class="layui-input" maxlength="100">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>儿童姓名：</label>
                    <div class="layui-input-inline" style="width: 350px;">
                        <input id='txtstuname' type="text" name="stuname" required lay-verify="required" placeholder="请输入儿童姓名" autocomplete="off" class="layui-input" maxlength="50">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>儿童性别：</label>
                    <div class="layui-input-inline" style="width: 150px;">
                        <select id="selsex" lay-filter="selsex">
                            <option value="男">男</option>
                            <option value="女">女</option>
                            <option value="">性别不明</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>出生日期：</label>
                    <div class="layui-input-inline" style="width: 350px;">
                        <input id="txtbirthday" type="text" style="width: 150px;display:inline-block;" lay-verify="required|date" placeholder="" class="layui-input"/>
                        <img id="iconbirthday" src="../../images/newicon/ico_calendar.png" style="cursor: pointer;position: relative;top: -1px;width: 20px;left: -30px;">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>死亡日期：</label>
                    <div class="layui-input-inline" style="width: 350px;">
                        <input id="txtdiedate" type="text" style="width: 150px;display:inline-block;" lay-verify="required|date" placeholder="" class="layui-input"/>
                        <img id="icondiedate" src="../../images/newicon/ico_calendar.png" style="cursor: pointer;position: relative;top: -1px;width: 20px;left: -30px;">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>死亡年龄：</label>
                    <div class="layui-input-inline" style="width: 350px;">
                        <label id="lbdieage" style="height: 40px;line-height: 40px;display: inline-block;"></label>
<!--                        <input id="txtdieage" type="text" style="width: 150px;" lay-verify="required|date" placeholder="" class="layui-input"/>-->
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>死亡诊断：</label>
                    <div class="layui-input-inline" style="width: 350px;">
                        <input id="txtdiereason" type="text" name="diereason" maxlength="200" required lay-verify="required" placeholder="请输入死亡诊断" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>死因分类：</label>
                    <div class="layui-input-inline">
                        <select id="seldieclass" lay-filter="seldieclass" lay-verify="required" style="width: 356px;">
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>出生医院：</label>
                    <div class="layui-input-inline" style="width: 350px;">
                        <input id="txtbirthhospital" type="text" name="birthhospital" maxlength="50" required lay-verify="required" placeholder="请输入出生医院" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>死亡地点：</label>
                    <div class="layui-input-inline" style="width: 350px;">
                        <input id="txtdieaddress" type="text" name="dieaddress" maxlength="100" required lay-verify="required" placeholder="请输入死亡地点" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>治疗医院：</label>
                    <div class="layui-input-inline" style="width: 350px;">
                        <input id="txttreatmenthospital" type="text" name="treatmenthospital" maxlength="50" required lay-verify="required" placeholder="请输入治疗医院" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>未就医原因：</label>
                    <div class="layui-input-inline" style="width: 350px;">
                        <input id="txtnocheckreason" type="text" name="nocheckreason" maxlength="300" required lay-verify="required" placeholder="请输入未就医原因" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <input type="hidden" value="" id="txtchild_stuno">
            </section>
        </section>
        <div class="btns-foot" style="display:none;">
            <a id="btnsave" class="layui-btn" lay-submit="" lay-filter="btnsave">保存</a>
        </div>
    </form>
</section>

<script type="text/javascript">
    var v = top.version;
    document.write('<script type="text/javascript" src="../../sys/jquery.js?v="' + v + '><' + '/script>');
    document.write('<script type="text/javascript" src="../../sys/arg.js?v="' + v + '><' + '/script>');
    document.write("<script type='text/javascript' src='../../sys/system.js?v='" + v + "><" + "/script>");
    document.write('<script type="text/javascript" src="../../layui-btkj/layui.js?v="' + v + '><' + '/script>');
    document.write("<script type='text/javascript' src='../../sys/function.js?v='" + v + "><" + "/script>");
    document.write('<script type="text/javascript" src="../../js/child/child_dieedit.js?v="' + v + '><' + '/script>');
</script>
</body>
</html>
