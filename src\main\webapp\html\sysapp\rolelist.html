<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>角色管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link type="text/css" rel="stylesheet" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css" media="all">
    <link rel="stylesheet" href="../../css/layui_ext.css" media="all">
    <style>
        .toolbar .layui-form-item {
            margin-bottom: 0;
        }

        .toolbar .layui-inline {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <!-- 内容区 -->
        <div class="layui-card-body">
            <!-- 表格工具栏 -->
            <div class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <input type="text" name="rname" id="keywords" placeholder="请输入角色名称" autocomplete="off" class="layui-input" lay-affix="clear">
                    </div>
                    <div class="layui-inline">
                        <div class="layui-input-inline" style="width: auto;">
                            <button class="layui-btn" id="search" lay-perm="sys:role:list"><i class="layui-icon"></i>查询</button>
<!--                            <button class="layui-btn btnOption  layui-btn-small btnAdd" id="add" lay-perm="sys:role:add"><i class="layui-icon layui-icon-add-1"></i>添加角色</button>-->
                            <button id="expand" class="layui-btn layui-btn-small layui-btn-normal"><i class="layui-icon"></i>全部展开</button>
                            <button id="collapse" class="layui-btn layui-btn-small layui-btn-warm"><i class="layui-icon"></i>全部折叠</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据表格 -->
            <table class="layui-hide" id="tableList" lay-filter="tableList"></table>
        </div>
    </div>
</div>
<script data-main="../../js/sysapp/rolelist" src='../../sys/require.min.js'></script>
</body>
</html>