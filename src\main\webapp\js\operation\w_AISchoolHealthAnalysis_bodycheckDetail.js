/*
***文件创建: 2025-07-01 17:58:08
***创建作者: wang<PERSON><PERSON><PERSON>
***最后修改: 2025-07-01 17:58:08
***修改人员: wangzenghui
***内容摘要: AI入园体检分析-体检详细列表页面
*/
require.config({
    paths: {
        jquery: '../../sys/jquery',
        system: '../../sys/system',
        echarts: '../../plugin/echart-5.1.2/dist/echarts.min',
        echartsgl: '../../plugin/echarts-gl/dist/echarts-gl.min',
        commom3dbt: 'common',
        mock: '../../plugin/mock/dist/mock-min',
        layui: '../../layui-btkj/layui'
    },
    shim: {
        "system": {
            deps: ["jquery"]
        },
        "echarts": {
            deps: ["jquery"]
        },
        "echartsgl": {
            deps: ["echarts"]
        },
        "commom3dbt": {
            deps: ["jquery", "echarts"],
            exports: "commom3dbt"
        },
        "mock": {
            deps: ["jquery"],
            exports: "Mock"
        },
        "layui": {
            deps: ["jquery"]
        }
    },
    waitSeconds: 0
});
require(["jquery", "system", "mock", "commom3dbt", "layui"], function ($, system, Mock, commom3dbt, a) {
    // Mock响应数据
    var totalCountFromUrl = Arg("totalcount") || 50; // 默认50条

    Mock.mock(/\/fuyou\/LayEnter/, 'get', function (options) {
        var params = {};
        // 解析URL参数
        if (options.url && options.url.indexOf('?') !== -1) {
            var queryString = options.url.split('?')[1];
            var pairs = queryString.split('&');
            for (var i = 0; i < pairs.length; i++) {
                var pair = pairs[i].split('=');
                if (pair.length >= 2) {
                    params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1] || '');
                }
            }
        }

        // 只处理目标请求
        if (params.arr && params.arr.indexOf('w_AISchoolHealthAnalysis_bodycheckDetail.getBodycheckDetail') !== -1) {

            // 解析参数 - 优先使用URL中的totalcount参数
            var totalCount = parseInt(totalCountFromUrl) || 50;
            if (params.swhere) {
                try {
                    var swhere = JSON.parse(decodeURIComponent(params.swhere));
                    if (swhere.msg_where && swhere.msg_where.totalCount) {
                        totalCount = parseInt(swhere.msg_where.totalCount[0]) || 50;
                    }
                } catch (e) {
                    console.log('解析swhere参数失败:', e);
                }
            }

            // 基础数据池
            var childNames = ['李玲', '李二', '王三', '赵四', '钱五', '孙六', '周七', '吴八', '郑九', '王十'];
            var kindergartens = ['阳光幼儿园', '蓝天幼儿园', '彩虹幼儿园', '星星幼儿园', '月亮幼儿园'];
            var grades = ['小班', '中班', '大班'];
            var classes = ['一班', '二班', '三班', '四班'];
            var sexes = ['男', '女'];
            var physicalArrangements = ['入园体检', '年度体检', '专项体检'];
            var pastMedicalHistory = ['无', '先天性心脏病', '哮喘', '癫痫', '过敏性疾病'];
            var allergyHistory = ['无', '青霉素过敏', '海鲜过敏', '花粉过敏', '尘螨过敏'];
            var doctors = ['张医生', '李医生', '王医生', '赵医生', '刘医生'];
            var medicalUnits = ['第一人民医院', '儿童医院', '妇幼保健院', '社区医疗中心'];
            var examinationResults = ['正常', '需复查', '建议专科就诊'];
            var doctorOpinions = ['保持健康生活习惯', '建议增加营养', '建议视力复查', '建议口腔检查'];
            var remarks = ['', '家长需关注孩子视力问题', '建议三个月后复查', '注意饮食均衡'];

            var mockData = [];
            var currentDate = new Date();

            // 生成身份证号前缀
            function generateIdPrefix() {
                var province = Math.floor(Math.random() * 30 + 10).toString().padStart(2, '0');
                var city = Math.floor(Math.random() * 20 + 1).toString().padStart(2, '0');
                var county = Math.floor(Math.random() * 20 + 1).toString().padStart(2, '0');
                return province + city + county;
            }

            for (var i = 0; i < totalCount; i++) {
                // 基本信息和时间
                var birthYear = 2018 + Math.floor(Math.random() * 4); // 2018-2021
                var birthMonth = String(Math.floor(Math.random() * 12) + 1).padStart(2, '0');
                var birthDay = String(Math.floor(Math.random() * 28) + 1).padStart(2, '0');
                var birthDate = birthYear + '-' + birthMonth + '-' + birthDay;

                // 生成身份证号
                var idPrefix = generateIdPrefix();
                var idNumber = idPrefix + birthYear + birthMonth + birthDay +
                    Math.floor(Math.random() * 9000 + 1000).toString();

                var checkYear = currentDate.getFullYear();
                var checkMonth = String(Math.floor(Math.random() * 12) + 1).padStart(2, '0');
                var checkDay = String(Math.floor(Math.random() * 28) + 1).padStart(2, '0');
                var checkDate = checkYear + '-' + checkMonth + '-' + checkDay;

                var age = checkYear - birthYear;
                var ageMonths = Math.floor(Math.random() * 12);
                var ageString = age + '岁' + (ageMonths > 0 ? ageMonths + '月' : '');

                // 体检数据 - 体格测量
                var height = (80 + Math.floor(Math.random() * 40)).toFixed(1);
                var weight = (10 + Math.floor(Math.random() * 20)).toFixed(1);
                var bmi = (weight / ((height / 100) * (height / 100))).toFixed(1);

                // 视力数据
                var leftEyeVision = (0.8 + Math.random() * 0.5).toFixed(1);
                var rightEyeVision = (0.8 + Math.random() * 0.5).toFixed(1);
                var isLowVision = leftEyeVision < 1.0 || rightEyeVision < 1.0 ? '是' : '否';

                // 龋齿数据
                var decayedTeeth = Math.floor(Math.random() * 5);
                var totalTeeth = 20;

                // 血红蛋白数据
                var hemoglobin = (110 + Math.floor(Math.random() * 30)).toFixed(0);
                var hemoglobinEvaluation = hemoglobin < 120 ? '偏低' : '正常';

                // 体格检查结果
                var physicalExamResults = ['正常', '异常', '未检查'];
                var getRandomExamResult = function () {
                    return physicalExamResults[Math.floor(Math.random() * 3)];
                };

                // 创建时间
                var createDate = new Date(checkYear, checkMonth - 1, checkDay);
                createDate.setHours(Math.floor(Math.random() * 24));
                createDate.setMinutes(Math.floor(Math.random() * 60));
                createDate.setSeconds(Math.floor(Math.random() * 60));
                var createTime = createDate.getFullYear() + '-' +
                    String(createDate.getMonth() + 1).padStart(2, '0') + '-' +
                    String(createDate.getDate()).padStart(2, '0') + ' ' +
                    String(createDate.getHours()).padStart(2, '0') + ':' +
                    String(createDate.getMinutes()).padStart(2, '0') + ':' +
                    String(createDate.getSeconds()).padStart(2, '0');

                var record = {
                    id: i + 1,
                    childname: childNames[i % childNames.length],
                    childsex: sexes[Math.floor(Math.random() * sexes.length)],
                    checkage: ageString,
                    childbirthday: birthDate,
                    idNumber: idNumber,
                    checkdate: checkDate,
                    createtime: createTime,
                    kindergarten: kindergartens[Math.floor(Math.random() * kindergartens.length)],
                    classname: grades[Math.floor(Math.random() * grades.length)] +
                        classes[Math.floor(Math.random() * classes.length)],
                    physicalarrangement: physicalArrangements[Math.floor(Math.random() * physicalArrangements.length)],
                    pastmedicalhistory: pastMedicalHistory[Math.floor(Math.random() * pastMedicalHistory.length)],
                    allergyhistory: allergyHistory[Math.floor(Math.random() * allergyHistory.length)],

                    // 体格测量数据
                    height: height,
                    weight: weight,
                    weightage: Math.random() > 0.8 ? '异常' : '正常',
                    heightage: Math.random() > 0.8 ? '异常' : '正常',
                    weightheight: Math.random() > 0.8 ? '异常' : '正常',

                    // 龋齿数据
                    decayedTeeth: decayedTeeth,
                    totalTeeth: totalTeeth,

                    // 视力数据
                    leftEyeVision: leftEyeVision,
                    rightEyeVision: rightEyeVision,
                    leftEyeVisionEvaluation: leftEyeVision < 1.0 ? '偏低' : '正常',
                    rightEyeVisionEvaluation: rightEyeVision < 1.0 ? '偏低' : '正常',
                    isLowVision: isLowVision,

                    // 眼外观
                    leftEyeAppearance: getRandomExamResult(),
                    rightEyeAppearance: getRandomExamResult(),

                    // 耳
                    leftEar: getRandomExamResult().replace('未检查', '无异常'), // 耳检查只有无异常/异常
                    rightEar: getRandomExamResult().replace('未检查', '无异常'),

                    // 听力
                    leftHearing: getRandomExamResult(),
                    rightHearing: getRandomExamResult(),

                    // 血红蛋白数据
                    hemoglobin: hemoglobin,
                    hemoglobinEvaluation: hemoglobinEvaluation,

                    // 体格检查
                    skin: getRandomExamResult(),
                    head: getRandomExamResult(),
                    thorax: getRandomExamResult(),
                    limbs: getRandomExamResult(),
                    genitalia: getRandomExamResult(),
                    liverspleen: getRandomExamResult(),
                    heartlung: getRandomExamResult(),
                    pharynx: getRandomExamResult(),

                    // 医生信息
                    doctor: doctors[Math.floor(Math.random() * doctors.length)],
                    medicalunit: medicalUnits[Math.floor(Math.random() * medicalUnits.length)],

                    // 检查结果和意见
                    examinationresult: examinationResults[Math.floor(Math.random() * examinationResults.length)],
                    doctoropinion: doctorOpinions[Math.floor(Math.random() * doctorOpinions.length)],
                    remark: remarks[Math.floor(Math.random() * remarks.length)]
                };

                mockData.push(record);
            }

            // 返回标准格式
            return {
                msg: "",
                code: 0,
                data: mockData,
                count: mockData.length
            };
        }

        // 非目标请求返回空数据
        return {
            msg: "",
            code: 0,
            data: [],
            count: 0
        };
    });

    // 下拉框的mock数据
    Mock.mock(new RegExp(location.origin.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '/api/AISchoolHealthAnalysis/kindergartens'), 'post', function () {
        var data = [
            {value: "0", text: "阳光幼儿园"},
            {value: "1", text: "蓝天幼儿园"},
            {value: "2", text: "彩虹幼儿园"},
            {value: "3", text: "星星幼儿园"},
            {value: "4", text: "月亮幼儿园"}
        ];
        return {
            "code": 200,
            "msg": "success",
            "data": data
        };
    });

    Mock.mock(new RegExp(location.origin.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '/api/AISchoolHealthAnalysis/physicalArrangements'), 'post', function () {
        var data = [
            {value: "0", text: "入园体检"},
            {value: "1", text: "年度体检"},
            {value: "2", text: "专项体检"}
        ];
        return {
            "code": 200,
            "msg": "success",
            "data": data
        };
    });

    // 园所获取 - 修改后的函数
    function getKindergartenSelectData(callback) {
        $.smaction(function (response) {
            if (response) {
                commom3dbt.updateSelectOptions('kindergarten', response);
                layui.use(['form'], function () {
                    var form = layui.form;
                    form.render('select');

                    // 修改点1：使用Arg()获取参数
                    var kindergarten = Arg("kindergarten");
                    if (kindergarten !== undefined) {  // 允许值为0
                        console.log("获取到的kindergarten参数:", kindergarten);
                        $("#kindergarten").val(kindergarten);
                        form.render('select');
                    }

                    // 绑定select变更事件
                    form.on('select(kindergarten)', function (data) {
                        btnsearch();
                    });

                    if (callback) callback();
                });
            }
        }, {}, {route: "api", action: "AISchoolHealthAnalysis/kindergartens", datastring: true});
    }

    // 体检安排获取 - 修改后的函数
    function getPhysicalArrangementSelectData(callback) {
        $.smaction(function (response) {
            if (response) {
                commom3dbt.updateSelectOptions('physicalarrangement', response);
                layui.use(['form'], function () {
                    var form = layui.form;
                    form.render('select');

                    // 修改点2：使用Arg()获取参数
                    var physicalarrangement = Arg("physicalarrangement");
                    if (physicalarrangement !== undefined) {
                        $("#physicalarrangement").val(physicalarrangement);
                        form.render('select');
                    }

                    // 绑定select变更事件
                    form.on('select(physicalarrangement)', function (data) {
                        btnsearch();
                    });

                    if (callback) callback();
                });
            }
        }, {}, {route: "api", action: "AISchoolHealthAnalysis/physicalArrangements", datastring: true});
    }

    // 使用 layui 模块
    layui.use(['form', 'table', 'laydate'], function () {
        window.layuiForm = layui.form;
        window.laydate = layui.laydate;
        window.commom3dbt = commom3dbt;
        layer = layui.layer;
        form = layui.form;
        table = layui.table;
        laydate = layui.laydate;

        form.render();
        console.log('form.render()已执行');

        // 初始化下拉框数据
        getKindergartenSelectData(function () {
            getPhysicalArrangementSelectData(function () {
                initTable();
            });
        });

        // 初始化事件
        initEvent();
    });

    // 初始化所有下拉框并绑定事件 - 修改后的函数
    function initEvent() {
        // 修改点3：统一使用Arg()获取参数
        var kindergarten = Arg("kindergarten");
        if (kindergarten !== undefined) {
            $("#kindergarten").val(kindergarten);
            form.render('select');
        }

        var physicalarrangement = Arg("physicalarrangement");
        if (physicalarrangement !== undefined) {
            $("#physicalarrangement").val(physicalarrangement);
            form.render('select');
        }

        // 绑定搜索按钮事件
        $("#btnsearch").click(function () {
            btnsearch();
        });

        // 绑定所有下拉框的变更事件，变更时自动触发搜索
        form.on('select(kindergarten)', btnsearch);
        form.on('select(weightAge)', btnsearch);
        form.on('select(heightAge)', btnsearch);
        form.on('select(weightHeight)', btnsearch);
        form.on('select(malnutrition)', btnsearch);
        form.on('select(overweightObesity)', btnsearch);
        form.on('select(anemia)', btnsearch);
        form.on('select(eyeAppearance)', btnsearch);
        form.on('select(vision)', btnsearch);
        form.on('select(ear)', btnsearch);
        form.on('select(hearing)', btnsearch);
        form.on('select(dentalCaries)', btnsearch);
        form.on('select(skull)', btnsearch);
        form.on('select(thoracicCavity)', btnsearch);
        form.on('select(spineLimbs)', btnsearch);
        form.on('select(pharynx)', btnsearch);
        form.on('select(cardiopulmonary)', btnsearch);
        form.on('select(liverSpleen)', btnsearch);
        form.on('select(externalGenitals)', btnsearch);
        form.on('select(skin)', btnsearch);
    }

    /*
     * 功能：查询
     * 参数说明：无
     * 返回值说明：无
     */
    function btnsearch() {
        var objwhere = getswhere();
        layui.table.reload('laytable', {
            url: encodeURI($.getLayUrl() + "?" + $.getSmStr(["w_AISchoolHealthAnalysis_bodycheckDetail.getBodycheckDetail"])),
            page: {curr: 1},
            where: {
                swhere: $.msgwhere(objwhere),
                fields: 'id',
                types: 'asc'
            }
        });
    }

    /*
     * 功能：获取查询条件
     * 参数说明：无
     * 返回值说明：查询条件对象
     */
    function getswhere() {
        const fields = [
            'kindergarten', 'weightAge', 'heightAge', 'weightHeight',
            'malnutrition', 'overweightObesity', 'anemia', 'eyeAppearance',
            'vision', 'ear', 'hearing', 'dentalCaries', 'skull', 'thoracicCavity',
            'spineLimbs', 'pharynx', 'cardiopulmonary', 'liverSpleen',
            'externalGenitals', 'skin'
        ];

        const objwhere = {};

        fields.forEach(field => {
            const value = $(`#${field}`).val();
            if (value) {
                objwhere[field] = [value];
            }
        });

        console.log("当前查询条件:", objwhere);
        return objwhere;
    }

    /*
     * 功能：初始化体检详情列表
     * 参数说明：无
     * 返回值说明：无
     */
    function initTable() {
        var objwhere = getswhere();

        // 第一行表头（分组标题）
        var arrcol1 = [
            {field: 'id', title: '序号', width: '5%', rowspan: 2, type: 'numbers', align: 'center'},
            {field: 'childname', title: '姓名', width: '8%', rowspan: 2, align: 'center'},
            {field: 'childsex', title: '性别', width: '8%', rowspan: 2, align: 'center'},
            {field: 'childbirthday', title: '出生日期', width: '8%', rowspan: 2, align: 'center'},
            {field: 'checkage', title: '年龄', width: '8%', rowspan: 2, align: 'center'},
            {field: 'idNumber', title: '身份证号', width: '8%', rowspan: 2, align: 'center'},
            {field: 'pastmedicalhistory', title: '既往病史', width: '8%', rowspan: 2, align: 'center'},
            {field: 'allergyhistory', title: '过敏史', width: '8%', rowspan: 2, align: 'center'},
            {field: 'checkdate', title: '体检日期', width: '8%', rowspan: 2, align: 'center'},
            {title: '体格发育测评情况', align: 'center', colspan: 5},
            {title: '龋齿', align: 'center', colspan: 2},
            {title: '视力', align: 'center', colspan: 5},
            {title: '眼外观', align: 'center', colspan: 2},
            {title: '耳', align: 'center', colspan: 2},
            {title: '听力', align: 'center', colspan: 2},
            {title: '血红蛋白', align: 'center', colspan: 2},
            {field: 'skin', title: '皮肤', width: '8%', rowspan: 2, align: 'center'},
            {field: 'head', title: '头颅', width: '8%', rowspan: 2, align: 'center'},
            {field: 'thorax', title: '胸廓', width: '8%', rowspan: 2, align: 'center'},
            {field: 'limbs', title: '四肢', width: '8%', rowspan: 2, align: 'center'},
            {field: 'genitalia', title: '外生殖器', width: '8%', rowspan: 2, align: 'center'},
            {field: 'liverspleen', title: '肝脾', width: '8%', rowspan: 2, align: 'center'},
            {field: 'heartlung', title: '心肺', width: '8%', rowspan: 2, align: 'center'},
            {field: 'pharynx', title: '咽部', width: '8%', rowspan: 2, align: 'center'},
            {field: 'examinationresult', title: '检查结果', width: '8%', rowspan: 2, align: 'center'},
            {field: 'doctoropinion', title: '医生意见', width: '8%', rowspan: 2, align: 'center'},
            {field: 'doctor', title: '体检医生', width: '8%', rowspan: 2, align: 'center'},
            {field: 'medicalunit', title: '体检单位', width: '8%', rowspan: 2, align: 'center'},
            {field: 'remark', title: '备注', width: '8%', rowspan: 2, align: 'center'},
        ];

        // 第二行表头（具体列名）
        var arrcol2 = [
            //体格发育测评情况
            {field: 'height', title: '身高数值（厘米）', width: '12%', align: 'center'},
            {field: 'heightage', title: '年龄/身高', width: '12%', align: 'center'},
            {field: 'weight', title: '体重数值（公斤）', width: '12%', align: 'center'},
            {field: 'weightage', title: '年龄/体重', width: '12%', align: 'center'},
            {field: 'weightheight', title: '身高/体重', width: '12%', align: 'center'},
            //龋齿
            {field: 'decayedTeeth', title: '龋齿数', width: '6%', align: 'center'},
            {field: 'totalTeeth', title: '牙齿数', width: '6%', align: 'center'},
            //视力
            {field: 'leftEyeVision', title: '左眼视力', width: '12%', align: 'center'},
            {field: 'leftEyeVisionEvaluation', title: '左眼视力评价', width: '6%', align: 'center'},
            {field: 'rightEyeVision', title: '右眼视力', width: '12%', align: 'center'},
            {field: 'rightEyeVisionEvaluation', title: '右眼视力评价', width: '12%', align: 'center'},
            {field: 'isLowVision', title: '是否低常', width: '8', align: 'center'},
            //眼外观
            {field: 'leftEyeAppearance', title: '左眼', width: '6%', align: 'center'},
            {field: 'rightEyeAppearance', title: '右眼', width: '6%', align: 'center'},
            //耳
            {field: 'leftEar', title: '左耳', width: '6%', align: 'center'},
            {field: 'rightEar', title: '右耳', width: '6%', align: 'center'},
            //听力
            {field: 'leftHearing', title: '左耳', width: '6%', align: 'center'},
            {field: 'rightHearing', title: '右耳', width: '6%', align: 'center'},
            //血红蛋白
            {field: 'hemoglobin', title: '血红蛋白值', width: '12%', align: 'center'},
            {field: 'hemoglobinEvaluation', title: '评价', width: '6%', align: 'center'},
        ];
        layui.table.render({
            skin: 'line',
            elem: '#laytable',
            url: encodeURI($.getLayUrl() + "?" + $.getSmStr(["w_AISchoolHealthAnalysis_bodycheckDetail.getBodycheckDetail"])),
            height: 'full-' + ($('#laytable').offset().top + 10),
            where: {
                swhere: $.msgwhere(objwhere),
                fields: 'id',
                types: 'asc'
            },
            cols: [arrcol1, arrcol2], // 使用两个表头数组
            done: function (res, curr, count) {
                $('#laytable').next('.layui-table-view').css({
                    'border': '1px solid #e6e6e6',
                    'border-width': '1px 1px 0 1px'
                });
                $('.layui-table-box').css('border', '1px solid #e6e6e6');
                $('.layui-table-cell').css('border-right', '1px solid #e6e6e6');
                // 找到表头的序号列（第1列）
                //  $('.layui-table-header .laytable-cell-1-0-0').css({
                //     'height': '80px',
                //     'line-height': '80px',
                 //    'align-content': 'center'
                //  });
                $('.layui-table-header .laytable-cell-1-0-0').css({
                    'height': '80px',
                    'line-height': '80px',
                    'align-content': 'center',
                });

                console.log('体检详细数据加载完成，共' + count + '条记录');
            },
            countNumberBool: true,
            even: true,
            page: {
                layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                align: 'right'
            },
            limits: [30, 50, 100, 200],
            limit: 30
        });
    }
});