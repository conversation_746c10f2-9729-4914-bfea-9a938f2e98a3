<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>后台推送数据详细</title>
    <link rel="stylesheet" type="text/css" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" type="text/css" href="../../css/icon.css"/>
    <link rel="stylesheet" type="text/css" href="../../styles/tbstyles.css"/>
    <link href="../../plugin/flexigrid/css/tbflexigrid.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css"/>
    <script type="text/javascript">
        var curtheme = (top && top.objdata && top.objdata.curtheme) ? top.objdata.curtheme : 'backstage';
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + curtheme + '.css" type="text/css" media="screen"/>');
    </script>

    <style type="text/css">
        html, body {
            background: #EAEFF3;
            overflow: hidden;
        }
        .layui-form-item {
            display: inline-block;
            vertical-align: top;
            margin: 5px 5px 0px 0;
        }
        .layui-form-label {
            line-height: 28px;
            padding: 0 10px;
            width: auto !important;
        }
        .layui-input-inline {
            margin-right: 0 !important;
        }
        .search-line-gap {
            display: inline-block;
            width: 15px;
        }
    </style>
</head>
<body>
<div class="marmain">
    <div class="content-medical">
        <div class="layui-form layui-comselect" style="padding:10px;">
            <!-- 第一行 -->
            <div class="layui-form-item" style="margin-bottom: 10px;">
                <label class="layui-form-label">推送通知：</label>
                <div class="layui-input-inline" style="width:120px;">
                    <select id="notificationType" lay-filter="notificationType">
                        <option value="">请选择</option>
                    </select>
                </div>

                <div class="layui-input-inline" style="width:120px; margin-left: 40px;">
                    <input id="datestart" type="text" autocomplete="off" placeholder="开始日期" class="layui-input">
                </div>
                <div class="layui-form-mid" style="padding: 0 8px;">至</div>
                <div class="layui-input-inline" style="width:120px;">
                    <input id="dateend" type="text" autocomplete="off" placeholder="结束日期" class="layui-input">
                </div>
                <div class="search-line-gap"></div>

                <label class="layui-form-label">是否发送成功：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="sendStatus" lay-filter="sendStatus">
                        <option value="">请选择</option>
                        <option value="1">是</option>
                        <option value="0">否</option>
                    </select>
                </div>
                <div class="search-line-gap"></div>

                <label class="layui-form-label">是否查看：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="viewStatus" lay-filter="viewStatus">
                        <option value="">请选择</option>
                        <option value="1">是</option>
                        <option value="0">否</option>
                    </select>
                </div>
                <div class="search-line-gap"></div>

                <label class="layui-form-label">预约状态：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="appointStatus" lay-filter="appointStatus">
                        <option value="">请选择</option>
                        <option value="1">已预约</option>
                        <option value="0">未预约</option>
                        <option value="2">已取消</option>
                    </select>
                </div>
            </div>

            <!-- 第二行 -->
            <div class="layui-form-item" style="margin-bottom: 10px;">
                <label class="layui-form-label">幼儿园：</label>
                <div class="layui-input-inline" style="width:120px;">
                    <select id="kindergarten" lay-filter="kindergarten">
                        <option value="">请选择</option>
                    </select>
                </div>
                <div class="search-line-gap"></div>

                <label class="layui-form-label">年级：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="grade" lay-filter="grade">
                        <option value="">请选择</option>
                        <option value="小班">小班</option>
                        <option value="中班">中班</option>
                        <option value="大班">大班</option>
                    </select>
                </div>
                <div class="search-line-gap"></div>

                <label class="layui-form-label">班级：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="class" lay-filter="class">
                        <option value="">请选择</option>
                    </select>
                </div>
                <div class="search-line-gap"></div>

                <label class="layui-form-label">幼儿性别：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="childSex" lay-filter="childSex">
                        <option value="">请选择</option>
                        <option value="男">男</option>
                        <option value="女">女</option>
                    </select>
                </div>
                <div class="search-line-gap"></div>

                <label class="layui-form-label">体检年龄：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="checkAge" lay-filter="checkAge">
                        <option value="">请选择</option>
                        <option value="3">3岁</option>
                        <option value="4">4岁</option>
                        <option value="5">5岁</option>
                        <option value="6">6岁</option>
                    </select>
                </div>
                <div class="search-line-gap"></div>

                <label class="layui-form-label">关键字：</label>
                <div class="layui-input-inline" style="width:200px;">
                    <input id="keyword" type="text" autocomplete="off" placeholder="请输入幼儿姓名或家长姓名" class="layui-input">
                </div>
            </div>

            <!-- 第三行 -->
            <div class="layui-form-item" style="margin-bottom: 15px;">
                <label class="layui-form-label">家长性别：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="parentSex" lay-filter="parentSex">
                        <option value="">请选择</option>
                        <option value="男">男</option>
                        <option value="女">女</option>
                    </select>
                </div>
                <div class="search-line-gap"></div>

                <label class="layui-form-label">家长年龄：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="parentAge" lay-filter="parentAge">
                        <option value="">请选择</option>
                        <option value="20-30">20-30岁</option>
                        <option value="30-40">30-40岁</option>
                        <option value="40-50">40-50岁</option>
                    </select>
                </div>
                <div class="search-line-gap"></div>

                <label class="layui-form-label">访问设备：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="device" lay-filter="device">
                        <option value="">请选择</option>
                        <option value="安卓">安卓</option>
                        <option value="苹果">苹果</option>
                        <option value="PC">PC</option>
                    </select>
                </div>
                <div class="search-line-gap"></div>

                <label class="layui-form-label">区域分布：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="areaType" lay-filter="areaType">
                        <option value="">请选择</option>
                        <option value="城区">城区</option>
                        <option value="郊区">郊区</option>
                    </select>
                </div>
                <div class="search-line-gap"></div>

                <label class="layui-form-label">孩子与家长关系：</label>
                <div class="layui-input-inline" style="width:120px;">
                    <select id="relationship" lay-filter="relationship">
                        <option value="">请选择</option>
                        <option value="父子">父子</option>
                        <option value="母子">母子</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
                <div class="search-line-gap"></div>

                <button class="layui-btn form-search" id="btnsearch">查询</button>
            </div>
        </div>
    </div>
    <div id="content" style="width: 100%;">
        <div class="marmain-cen">
            <div class="content-medical" id="divtable">
                <table id="laytable" lay-filter="laytable"></table>
            </div>
        </div>
    </div>
</div>

<script data-main="../../js/operation/w_pushNotificationDetail" src='../../sys/require.min.js'></script>
</body>
</html> 