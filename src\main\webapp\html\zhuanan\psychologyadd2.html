﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>心里行为发育征象-添加</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <link href="../../styles/admin.css" rel="stylesheet" type="text/css" />
    <link href="../../layui-btkj/css/layui.css" rel="stylesheet" type="text/css"/>
    <link href="../../styles/tbstyles.css" type="text/css" rel="stylesheet" />
    <link href="../../plugin/ehaiform/css/tbbaseform.css" rel="stylesheet" type="text/css" />
    <link href="../../plugin/editselect/css/editselect.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="../../css/commonstyle-layui-btkj.css"/>
    <link rel="stylesheet" href="../../css/medical.css"/>
    <style type="text/css">
        .tablestyle_field tr td span {
            text-align: right;
            font-size: 14px;
            padding-right: 5px;
            display: inline-block;
            vertical-align: middle;
        }
    </style>
</head>
<body>
<div class="bodywidth">
    <div class="content">
        <fieldset>
            <div class="legend_bjcyadd">
                <u class="skinIconright"></u><label id="lbtitle">心里行为发育管理</label></div>
            <table cellpadding="0" cellspacing="0" border="0" class="tablestyle_field" style="width: 100%;">
                <tr>
                    <td class="tdspan">
                        <span><b style="color:red">*</b>班级：</span>
                    </td>
                    <td>
                        <label id="selclas">
                        </label>
                    </td>
                    <td class="tdspan">
                        <span><b style="color:red">*</b>姓名：</span>
                    </td>
                    <td>
                        <label id="selstud">
                        </label>
                    </td>
                </tr>
                <tr>
                    <td class="tdspan">
                        <span><b style="color:red">*</b>出生日期：</span>
                    </td>
                    <td>
                        <input id="txtbirthday" type="text" readonly="readonly" style="width: 100px;" />
                    </td>
                    <td class="tdspan">
                        <span><b style="color:red">*</b>所属年龄节点：</span>
                    </td>
                    <td>
                        <input id="txtage" type="text" style="width: 100px;" readonly/>
                    </td>
                </tr>
                <tr>
                    <td class="tdspan">
                        <span><b style="color:red">*</b>检查日期：</span>
                    </td>
                    <td>
                        <div class="layui-form-item" style="height:39px;inline-height: 39px;margin-bottom: -5px;">
                            <div class="layui-input-inline" style="float: none;width: auto;margin-right: 0px;">
                                <input id="txtcheckdate" type="text" style="width: 130px;" readonly placeholder="检查日期" class="layui-input" required="required" lay-verify="required"/>
                                <img id="iconcheckdate" src="../../images/newicon/ico_calendar.png" style="cursor: pointer;position: relative;top: -25px;width: 20px;left: 115px;">
                            </div>
                        </div>
<!--                        <input id="txtcheckdate" type="text" style="width: 100px;" />-->
                    </td>
                    <td class="tdspan">
                        <span><b style="color:red">*</b>检查年龄：</span>
                    </td>
                    <td>
                        <input id="txtcheckage" type="text" style="width: 100px;" readonly/>
                    </td>
                </tr>
                <tr>
                    <td class="tdspan">
                        <span><b style="color:red">*</b>预警征象：</span>
                    </td>
                    <td colspan="3">
                        <div id="fypgdiv"></div>
                    </td>
                </tr>
            </table>
        </fieldset>
    </div>
</div>
<script data-main="../../js/zhuanan/psychologyadd2" src='../../sys/require.min.js'></script>
</body>
</html>
