-- 为 xj_label 表添加 category 字段
-- 执行前请备份数据库

-- 添加 category 字段
ALTER TABLE xj_label ADD COLUMN category VARCHAR(50) DEFAULT 'general' COMMENT '标签分类：hw-身高体重,eye-眼科,listen-听力,matin-血红蛋白,car-口腔,xf-胸部腹部,neike-内科检查,general-通用';

-- 为现有数据设置默认分类
UPDATE xj_label SET category = 'general' WHERE category IS NULL OR category = '';

-- 添加索引以提高查询性能
CREATE INDEX idx_xj_label_category ON xj_label(category);

-- 验证修改结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'xj_label' 
AND COLUMN_NAME = 'category';

-- 查看表结构
DESCRIBE xj_label;
