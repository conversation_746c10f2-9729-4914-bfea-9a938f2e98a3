<!DOCTYPE html>
<html>
<head>
    <title>专家建议模板</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <link rel="stylesheet" href="../css/reset.css"/>
    <link rel="stylesheet" href="../css/style.css"/>
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../css/icon.css">
</head>
<body>
<section>
    <form class="layui-form layui-form-pane">
        <div class="layui-form-item">
            <label class="layui-form-label">医院名称</label>
            <div class="layui-input-inline">
                <input type="text" id="hosname" lay-verify="required" placeholder="请输入" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">医院地址</label>
            <div class="layui-input-inline">
                <input type="text" id="hosdress" lay-verify="required" placeholder="请输入" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">联系方式</label>
            <div class="layui-input-inline">
                <input type="text" id="hostel" lay-verify="required" placeholder="请输入" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="" >
            <a id="btnsave" class="layui-btn" lay-submit="" lay-filter="btnsave" data-id="">保存</a>
        </div>
    </form>

    <div style="min-width:460px;height:450px;overflow-y: auto;border-bottom: 1px solid #DDDDDD;" id="temp_list">
        <!--<div>
            <div class="heightnum"><span>肥胖管理：中度</span></div>
            <div class="panel-list">
                <div class="panel-txt"><textarea placeholder="输入内容..."></textarea></div>
                <div class="panel-opr"><img src="../images/save_icon.png"><img src="../images/del_icon2.png"></div>
            </div>
        </div>
        <div>
            <div class="heightnum"><span>肥胖管理：中度</span><span style="margin-left: 20px;">王丽</span></div>
            <div class="panel-list">
                <div class="panel-txt">对于肥胖儿童的饮食，应在保证足够的蛋白质、维生素和无机盐的前提下，适当增加含纤维素的食品。</div>
                <div class="panel-opr"><img src="../images/edi_icon.png"><img src="../images/del_icon2.png"></div>
            </div>
        </div>
        <div>
            <div class="heightnum"><span>营养不良儿童：消瘦</span></div>
            <div class="panel-list">
                <div class="panel-txt current">对于肥胖儿童的饮食，应在保证足够的蛋白质、维生素和无机盐的前提下，适当增加含纤维素的食品。</div>
                <div class="panel-opr"><img src="../images/edi_icon.png"><img src="../images/del_icon2.png"></div>
            </div>
        </div>-->
    </div>
    <div class="pay-btn" style="padding: 0 0 20px 0;">
        <a class="opr-btn">确定</a>
    </div>
</section>

<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../sys/function.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../js/opinionTemp.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>