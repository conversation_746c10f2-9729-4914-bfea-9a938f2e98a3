<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="../css/reset.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        .popup-container {margin: 0 auto;}
        .table-row {padding: 4px 0;}
        .process {border: 1px solid #ccc;height: 20px;padding: 2px;position: relative;}
        .process-bar {height: 100%; width: 0;  -webkit-transition: 0.5s;-moz-transition: 0.5s;-ms-transition: 0.5s;-o-transition: 0.5s;transition: 0.5s;}
            /*animation: barberpole 12s linear infinite;*/
        .process>span {float: left;margin-left: 38%;}
        .move-down {margin-top: 24px; display: none;}
        /*@keyframes barberpole {*/
            /*from {background-position: 0% 0%;}*/
            /*to {background-position: 600px 0%;}*/
        /*}*/
        
    </style>
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../css/'+parent.objdata.curtheme+'.css" type="text/css" media="screen"/>');
    </script>
    <title>synchildren</title>
</head>
<body>
<div class="popup-container width-setting1">
    <section class="table-row">
        <label for="childrennum" class="inline-label">幼儿总数量：</label>
        <input id="childrennum" type="text" class="ul-input" value="" readonly="readonly"/>
    </section>
    <section class="table-row">
        <label for="boynum" class="inline-label">男孩数量：</label>
        <input id="boynum" type="text" class="ul-input" value="" readonly="readonly"/>
    </section>
    <section class="table-row">
        <label for="girlnum" class="inline-label">女孩数量：</label>
        <input id="girlnum" type="text" class="ul-input" value="" readonly="readonly"/>
    </section>
    <section class="table-row">
        <label for="parentnum" class="inline-label">家长总数量：</label>
        <input id="parentnum" type="text" class="ul-input" value="" readonly="readonly"/>
    </section>
    <section class="table-row">
        <label for="fathernum" class="inline-label">爸爸数量：</label>
        <input id="fathernum" type="text" class="ul-input" value="" readonly="readonly"/>
    </section>
    <section class="table-row">
        <label for="mothernum" class="inline-label">妈妈数量：</label>
        <input id="mothernum" type="text" class="ul-input" value="" readonly="readonly"/>
    </section>
    <section class="table-row">
        <label for="otherparnum" class="inline-label">其他家长数量：</label>
        <input id="otherparnum" type="text" class="ul-input" value="" readonly="readonly"/>
    </section>
    <section class="table-row center move-down">
        <span>上次更新时间：</span>
        <span class="theme-fgc-static" id="lastupdate"></span>
    </section>
    <section class="table-row center move-down">
        <div class="process">
            <span id="processData">updating ......</span>
            <div class="process-bar theme-bgc-static" id="processBar"></div>
        </div>
    </section>
</div>
<script type="text/javascript" src="../sys/jquery.js"></script>
<script type="text/javascript" src="../sys/arg.js"></script>
<script type="text/javascript" src="../layui-btkj/layui.js"></script>
<script type="text/javascript" src="../js/synchildren.js"></script>
</body>
</html>