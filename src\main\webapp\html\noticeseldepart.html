<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>科室设置</title>
    <link rel="stylesheet" href="../css/reset.css"/>
    <link rel="stylesheet" href="../plugin/zTree/css/zTreeStyle/zTreeStyle.css" type="text/css">
    <link rel="stylesheet" href="../css/style.css"/>
    <link rel="stylesheet" href="../css/icon.css">
    <link rel="stylesheet" href="../css/medical.css">
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../styles/tbstyles.css"/>
    <link rel="stylesheet" type="text/css" href="../css/commonstyle-layui-btkj.css"/>
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../css/' + top.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>

    <style>
        ul {
            line-height: 40px;
        }

        html, body {
            background: #EAEFF3;
        }

        .treeWrap{
            width: 100%;
        }

    </style>
</head>

<body>
<div class="marmain" style="display: flex; flex-direction: row;">
    <section class="treeWrap" style="position:fixed;background: #fff;color: #34495E;">
        <div class="content_wrap">
            <div class="zTreeDemoBackground left">
                <ul id="treeDemo" class="ztree"></ul>
            </div>
        </div>
    </section>
</div>

<script>
    var v = top.version;
    document.write('<script type="text/javascript" src="../sys/jquery.js?v=' + v + '"><' + '/script>');
    document.write('<script type="text/javascript" src="../sys/arg.js?v=' + v + '"><' + '/script>');
    document.write('<script type="text/javascript" src="../layui-btkj/layui.js?v=' + v + '"><' + '/script>');
    document.write('<script type="text/javascript" src="../js/noticeseldepart.js?v=' + v + '"><' + '/script>');
    document.write('<script type="text/javascript" src="../plugin/zTree/js/jquery.ztree.core-3.5.js?v=' + v + '"><' + '/script>');
    document.write('<script type="text/javascript" src="../plugin/zTree/js/jquery.ztree.excheck-3.5.js?v=' + v + '"><' + '/script>');
    document.write('<script type="text/javascript" src="../plugin/zTree/js/jquery.ztree.exedit-3.5.js?v=' + v + '"><' + '/script>');
</script>
</body>
</html>