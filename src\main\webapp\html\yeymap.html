﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../css/reset.css" />
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <!--[if lt IE 9]>
    <script src="../sys/html5shiv.min.js"></script>
    <![endif]-->
    <style>
        body { background-color: #f8f8f8; }
        .min-width { min-width: 1260px; }
        .topframe { height: 85px; padding: 1px 0 0 1px; font-size: 0; }
            .topframe > .data-show { height: 96%; margin-right: .5%; display: inline-block; font-size: 14px; vertical-align: middle; }
                .topframe > .data-show:first-child { min-width: 210px; width: 15.62%; background-color: #fff; border: 1px solid #ccc; }
                    .topframe > .data-show:first-child > div { padding-top: 20px; padding-left: 8%; }
                        .topframe > .data-show:first-child > div:last-child { padding-top: 5px; }
                        .topframe > .data-show:first-child > div > button { background-color: #DDDDDD; border: none; padding: 0 3px; font-size: 14px; color: #A1A1A1; cursor: pointer; }
                            .topframe > .data-show:first-child > div > button.curr-fgc { background: #BF312D; color: #fff; }
                        .topframe > .data-show:first-child > div > span { }
                        .topframe > .data-show:first-child > div > a { }
                            .topframe > .data-show:first-child > div > a:nth-child(2) { }
                            .topframe > .data-show:first-child > div > a:last-child { }
                .topframe > .data-show:nth-child(2),
                .topframe > .data-show:nth-child(3),
                .topframe > .data-show:nth-child(4),
                .topframe > .data-show:nth-child(5),
                .topframe > .data-show:nth-child(6),
                .topframe > .data-show:nth-child(7) { min-width: 165px; width: 13%; border-radius: 4px; color: #fff; font-size: 16px; }
                .topframe > .data-show:nth-child(2) { background-color: #E74F5B; border: 1px solid #E74F5B; }
                .topframe > .data-show:nth-child(3) { background-color: #3598DC; border: 1px solid #64b0cc; }
                .topframe > .data-show:nth-child(4) { background-color: #32C6D2; }
                .topframe > .data-show:nth-child(5) { background-color: #9AC664; }
                .topframe > .data-show:nth-child(6) { background-color: #FF8762; }
                .topframe > .data-show:nth-child(7) { background-color: #8D44AD; }
                    .topframe > .data-show:nth-child(2) > div:first-child,
                    .topframe > .data-show:nth-child(3) > div:first-child,
                    .topframe > .data-show:nth-child(4) > div:first-child,
                    .topframe > .data-show:nth-child(5) > div:first-child,
                    .topframe > .data-show:nth-child(6) > div:first-child,
                    .topframe > .data-show:nth-child(7) > div:first-child { height: 33px; line-height: 33px; }
                .topframe > .data-show:nth-child(2) > div:first-child { border-bottom: 1px solid #E3898F; text-align: center; }
                .topframe > .data-show:nth-child(3) > div:first-child { border-bottom: 1px solid #79B4DD; text-align: center; }
                .topframe > .data-show:nth-child(4) > div:first-child { border-bottom: 1px solid #77CFD7; text-align: center; }
                .topframe > .data-show:nth-child(5) > div:first-child { border-bottom: 1px solid #B5D095; text-align: center; }
                .topframe > .data-show:nth-child(6) > div:first-child { border-bottom: 1px solid #F2AA94; text-align: center; }
                .topframe > .data-show:nth-child(7) > div:first-child { border-bottom: 1px solid #AE82C1; text-align: center; }
                .topframe > .data-show:nth-child(4) > div:last-child { }
                .topframe > .data-show:nth-child(4) > div:first-child > div { display: inline-block; width: 33.3%; font-size: 16px; text-align: center; }
                    .topframe > .data-show:nth-child(4) > div:first-child > div:last-child { text-align: left; }
                        .topframe > .data-show:nth-child(2) > div:first-child > span:first-child,
                        .topframe > .data-show:nth-child(3) > div:first-child > span:first-child,
                        .topframe > .data-show:nth-child(4) > div:first-child > div:last-child > span:first-child { padding-left: 10px; }
                        .topframe > .data-show:nth-child(2) > div:first-child > span:last-child,
                        .topframe > .data-show:nth-child(3) > div:first-child > span:last-child,
                        .topframe > .data-show:nth-child(4) > div:first-child > div:last-child > span:last-child { float: right; padding-left: 19px; margin-right: 10px; background: url("../images/growth.png") left no-repeat; background-size: 22px; }
                .topframe > .data-show:nth-child(4) > div:last-child > div { display: inline-block; width: 33%; font-size: 16px; text-align: center; }
                .topframe > .data-show:nth-child(2) > div:last-child > span:first-child,
                .topframe > .data-show:nth-child(3) > div:last-child > span:first-child,
                .topframe > .data-show:nth-child(4) > div:last-child > span:first-child,
                .topframe > .data-show:nth-child(5) > div:last-child > span:first-child,
                .topframe > .data-show:nth-child(6) > div:last-child > span:first-child,
                .topframe > .data-show:nth-child(7) > div:last-child > span:first-child { float: left; width: 40px; height: 50px; margin-left: 19px; }
                .topframe > .data-show:nth-child(2) > div:last-child > span:first-child { background: url("../images/textyuan.png") center no-repeat; background-size: 40px; }
                .topframe > .data-show:nth-child(3) > div:last-child > span:first-child { background: url("../images/twouser.png") center no-repeat; background-size: 40px; }
                .topframe > .data-show:nth-child(4) > div:last-child > span:first-child { background: url("../images/distri_pic.png") center no-repeat; background-size: 31px; }
                .topframe > .data-show:nth-child(5) > div:last-child > span:first-child { background: url("../images/distri_pic2.png") center no-repeat; background-size: 23px; }
                .topframe > .data-show:nth-child(6) > div:last-child > span:first-child { background: url("../images/distri_pic3.png") center no-repeat; background-size: 25px; }
                .topframe > .data-show:nth-child(7) > div:last-child > span:first-child { background: url("../images/distri_pic4.png") center no-repeat; background-size: 22px; }
                .topframe > .data-show:nth-child(2) > div:last-child > span:nth-child(2),
                .topframe > .data-show:nth-child(2) > div:last-child > span:last-child,
                .topframe > .data-show:nth-child(3) > div:last-child > span:nth-child(2),
                .topframe > .data-show:nth-child(3) > div:last-child > span:last-child,
                .topframe > .data-show:nth-child(4) > div:last-child > span:nth-child(2),
                .topframe > .data-show:nth-child(4) > div:last-child > span:last-child,
                .topframe > .data-show:nth-child(5) > div:last-child > span:nth-child(2),
                .topframe > .data-show:nth-child(5) > div:last-child > span:last-child,
                .topframe > .data-show:nth-child(6) > div:last-child > span:nth-child(2),
                .topframe > .data-show:nth-child(6) > div:last-child > span:last-child,
                .topframe > .data-show:nth-child(7) > div:last-child > span:nth-child(2),
                .topframe > .data-show:nth-child(7) > div:last-child > span:last-child,
                .topframe > .data-show:nth-child(4) > div:last-child > span:first-child > span:nth-child(2),
                .topframe > .data-show:nth-child(4) > div:last-child > span:first-child > span:last-child { float: right; }
                .topframe > .data-show:nth-child(2) > div:last-child > span:nth-child(2),
                .topframe > .data-show:nth-child(3) > div:last-child > span:nth-child(2),
                .topframe > .data-show:nth-child(4) > div:last-child > span:nth-child(2),
                .topframe > .data-show:nth-child(5) > div:last-child > span:nth-child(2),
                .topframe > .data-show:nth-child(6) > div:last-child > span:nth-child(2),
                .topframe > .data-show:nth-child(7) > div:last-child > span:nth-child(2) { margin-right: 10px; padding-top: 16px; }
                .topframe > .data-show:nth-child(4) > div:last-child > div:first-child > span:nth-child(2) { padding: 13px 10px 9px 0; border-right: 1px solid #fff; margin-top: 3px; }
                .topframe > .data-show:nth-child(2) > div:last-child > span:last-child,
                .topframe > .data-show:nth-child(3) > div:last-child > span:last-child,
                .topframe > .data-show:nth-child(4) > div:last-child > span:last-child,
                .topframe > .data-show:nth-child(5) > div:last-child > span:last-child,
                .topframe > .data-show:nth-child(6) > div:last-child > span:last-child,
                .topframe > .data-show:nth-child(7) > div:last-child > span:last-child,
                .topframe > .data-show:nth-child(4) > div:last-child > div:first-child > span:last-child { font-size: 25px; padding-top: 8px; }
                .topframe > .data-show:nth-child(4) > div:last-child > div:nth-child(2),
                .topframe > .data-show:nth-child(4) > div:last-child > div:last-child { height: 43px; margin-top: 3px; border-right: 1px solid #fff; vertical-align: top; }
                .topframe > .data-show:nth-child(4) > div:last-child > div:last-child { border-right: 0; }
                    .topframe > .data-show:nth-child(4) > div:last-child > div:nth-child(2) > span,
                    .topframe > .data-show:nth-child(4) > div:last-child > div:last-child > span { display: inline-block; }
                        .topframe > .data-show:nth-child(4) > div:last-child > div:nth-child(2) > span:first-child,
                        .topframe > .data-show:nth-child(4) > div:last-child > div:last-child > span:first-child { font-size: 25px; padding: 4px 0 0 10px; }
                        .topframe > .data-show:nth-child(4) > div:last-child > div:nth-child(2) > span:last-child,
                        .topframe > .data-show:nth-child(4) > div:last-child > div:last-child > span:last-child { margin-top: 13px; }
        .bottom-frame { padding: 0 1px; }
            .bottom-frame .map-con { float: left; width: 100%; min-height: 500px; }
            .bottom-frame .map-show { margin-left: 80%; height: 619px; padding-left: 2px; min-height: 500px; display: none; }
                .bottom-frame .map-show .container { border: 1px solid #e2e1e1; height: 99.7%; border-radius: 6px; background-color: #fff; padding: 10px 10px 0 10px; font-size: 13px; overflow: auto; }
                    /*search area*/
                    .bottom-frame .map-show .container .search-area { border-bottom: 1px dotted #cccccc; padding-bottom: 15px; }
                        .bottom-frame .map-show .container .search-area > div:first-child > input { border-radius: 4px; border: 1px solid #cccccc; padding-left: 8px; height: 30px; width: 77%; }
                        .bottom-frame .map-show .container .search-area > div:first-child > i { display: inline-block; width: 14%; height: 30px; background: url("../images/search.png") center no-repeat; background-size: 21px; cursor: pointer; vertical-align: middle; }
                        .bottom-frame .map-show .container .search-area > div:last-child { padding: 15px 0 0 9px; }
                    /*table result*/
                    .bottom-frame .map-show .container .result-area .table-result { border: 1px solid #ccc; width: 99%; margin-top: 15px; border-radius: 4px; }
                        .bottom-frame .map-show .container .result-area .table-result .row { border-bottom: 1px solid #cccccc; height: 40px; line-height: 40px; font-size: 0; }
                            .bottom-frame .map-show .container .result-area .table-result .row:last-child { border-bottom: 0; }
                            .bottom-frame .map-show .container .result-area .table-result .row > span { display: inline-block; font-size: 13px; width: 33%; text-align: center; }
                                .bottom-frame .map-show .container .result-area .table-result .row > span:first-child { border-right: 1px solid #cccccc; }
                                .bottom-frame .map-show .container .result-area .table-result .row > span:nth-child(2):hover,
                                .bottom-frame .map-show .container .result-area .list-result .list-detail > ul > li:hover,
                                .bottom-frame .map-show .container .result-area .search-result > ul > li:hover { text-decoration: underline; cursor: pointer; }
                    /*list result*/
                    .bottom-frame .map-show .container .result-area .list-result { width: 99%; margin-top: 15px; }
                        .bottom-frame .map-show .container .result-area .list-result .overview { padding-bottom: 15px; border-bottom: 1px solid #cccccc; }
                            .bottom-frame .map-show .container .result-area .list-result .overview > a { padding-left: 15px; background: url("../images/backward.png") left no-repeat; -webkit-background-size: 20px; background-size: 17px; color: #1296db; }
                            .bottom-frame .map-show .container .result-area .list-result .overview > span { float: right; }
                        .bottom-frame .map-show .container .result-area .list-result .list-detail > ul { padding-left: 9px; padding-top: 10px; }
                            .bottom-frame .map-show .container .result-area .list-result .list-detail > ul > li,
                            .bottom-frame .map-show .container .result-area .search-result > ul > li { height: 30px; line-height: 30px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
                    /*search result*/
                    .bottom-frame .map-show .container .result-area .search-result { width: 99%; margin-top: 15px; }
                        .bottom-frame .map-show .container .result-area .search-result > ul { padding-left: 9px; }
                        .bottom-frame .map-show .container .result-area .search-result .no-result { text-align: center; text-decoration: underline; cursor: pointer; }
        #yeyStat:hover, #yeyCount:hover, #userCount:hover, #childCount:hover, #boyCount:hover, #girlCount:hover, #parentCount:hover { cursor: pointer; text-decoration: underline; }
        #sync { width: 14px; height: 14px; float: right; display: block; margin-top: 10px; cursor: pointer; }
    </style>
</head>
<body>
    <div class="min-width">
        <div class="topframe">
            <div class="data-show">
                <div id="modeSwitch">
                    <span class="theme-fgc-static">地图模式：</span>
                    <button class="theme-fgc curr-fgc">统计</button>
                    <button class="theme-fgc">星点</button>
                    <button class="theme-fgc">名称</button>
                </div>
                <div id="borderSwitch">
                    <form class="layui-form">
                        <span class="theme-fgc-static" style="float: left;margin-top: 5px;">显示边界：</span>
                        <div class="layui-input-block" style="float: left;margin-left: 4px;margin-top: -3px;">
                            <input type="checkbox" lay-filter="switchBoundary" name="switchBoundary" lay-skin="switch" lay-text="开启|关闭">
                        </div>
                    </form>
                </div>
            </div>
            <div class="data-show">
                <div>
                    <span id="yeyStat" title="查看关联园所统计图">关联幼儿园数</span>
                    <span id="yeyGrow"></span>
                </div>
                <div>
                    <span></span>
                    <span>所</span><span id="yeyCount" title="切换园所数据"></span>
                </div>
            </div>
            <div class="data-show">
                <div>
                    <span id="userStat" title="查看幼儿园负责人统计图">幼儿园负责人数</span>
                    <span id="userGrow"></span>
                </div>
                <div>
                    <span></span>
                    <span>人</span><span id="userCount" title="切换用户数据"></span>
                </div>
            </div>
            <div class="data-show">
                <div>
                    <span id="childStat" title="查看幼儿图">幼儿数</span>
                    <img src="../images/sync.svg" alt="同步" id="sync" title="同步" style="margin-right: 18px;display:none;">
                    <span id="childGrow"></span>
                </div>
                <div>
                    <span></span>
                    <span>人</span><span id="childCount" title="切换用户数据"></span>
                </div>
            </div>
            <div class="data-show">
                <div>
                    <span id="boyStat" title="查看男童数统计图">男童数</span>
                    <span id="boyGrow"></span>
                </div>
                <div>
                    <span></span>
                    <span>人</span><span id="boyCount" title="切换园所数据"></span>
                </div>
            </div>
            <div class="data-show">
                <div>
                    <span id="girlStat" title="查看女童数统计图">女童数</span>
                    <span id="girlGrow"></span>
                </div>
                <div>
                    <span></span>
                    <span>人</span><span id="girlCount" title="切换用户数据"></span>
                </div>
            </div>
            <div class="data-show">
                <div>
                    <span id="parentStat" title="查看家长数统计图">家长数</span>
                    <span id="parentGrow"></span>
                </div>
                <div>
                    <span></span>
                    <span>人</span><span id="parentCount" title="切换用户数据"></span>
                </div>
            </div>
        </div>
        <div class="bottom-frame">
            <div id="allmap" class="map-con"></div>
            <div id="mapshow" class="map-show">
                <div class="container">
                    <div class="search-area">
                        <div>
                            <label for="search"></label><input type="text" id="search" placeholder="请输入幼儿园名称" />
                            <i class="search-icon"></i>
                        </div>
                        <div>
                            所属区域：<span>北京市</span>
                        </div>
                    </div>
                    <div class="result-area">
                        <div class="table-result">
                            <div class="row">
                                <span>公立园</span>
                                <span class="theme-fgc-static">333</span>
                                <span>22.22%</span>
                            </div>
                            <div class="row">
                                <span>公立园</span>
                                <span class="theme-fgc-static">333</span>
                                <span>22.22%</span>
                            </div>
                            <div class="row">
                                <span>公立园</span>
                                <span class="theme-fgc-static">333</span>
                                <span>22.22%</span>
                            </div>
                        </div>
                        <div style="display: none;" class="list-result">
                            <div class="overview">
                                <a href="javascript:;">返回</a>
                                <span>公立园(123)</span>
                            </div>
                            <div class="list-detail">
                                <ul>
                                    <li>蓝天幼儿园蓝天幼儿园蓝天幼儿园蓝天幼儿园</li>
                                    <li>蓝天幼儿园</li>
                                    <li>蓝天幼儿园</li>
                                    <li>蓝天幼儿园</li>
                                    <li>蓝天幼儿园</li>
                                    <li>蓝天幼儿园</li>
                                    <li>蓝天幼儿园</li>
                                </ul>
                            </div>
                        </div>
                        <div style="display: none;" class="search-result">
                            <ul>
                                <li>蓝天幼儿园</li>
                                <li>蓝天幼儿园</li>
                                <li>蓝天幼儿园</li>
                                <li>蓝天幼儿园</li>
                                <li>蓝天幼儿园</li>
                            </ul>
                            <div class="no-result theme-fgc">全国范围搜索</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript" src="../sys/jquery.js"></script>
    <script type="text/javascript" src="../sys/arg.js"></script>
    <script type="text/javascript" src="../layui-btkj/layui.js"></script>
    <script src="../plugin/js/selarea.js"></script>
    <script src="../js/citycode.js"></script>
    <script src="../js/areacoordinate.js"></script>
    <script type="text/javascript" src="//api.map.baidu.com/api?v=2.0&ak=MfX8ZrC7i89wF1UcPFnorhsWFebQLtBv&services=true"></script>
    <script type="text/javascript" src="../plugin/mock/dist/mock.js"></script>
    <script type="text/javascript" src="../js/bigdata_common.js"></script>
    <script type="text/javascript" src="../js/yeymap.js"></script>
</body>
</html>
