﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>传染病统计编辑</title>
    <link type="text/css" rel="stylesheet" href="../../css/reset.css" />
    <link type="text/css" rel="stylesheet" href="../../layui-btkj/css/layui.css" />
    <link rel="stylesheet" type="text/css" href="../../css/icon.css" />
    <link rel="stylesheet" type="text/css" href="../../css/commonstyle-layui-btkj.css" />
    <link rel="stylesheet" type="text/css" href="../../css/style.css" />
    <style type="text/css">
        .isview { margin-top: 0; }

        .layui-table-view { margin: 0px; }

        .webuploader-container { height: 120px; }

        .default-form .layui-form-label { width: 140px; color: #778CA2; }
        .layui-input-block { margin-left: 150px; }

        .layui-table td { border-width: 1px !important; }
        .form-display .layui-form-radio { margin: 0 0px 0 0; width: 33%; }
    </style>
</head>
<body>
    <div class="layui-page" style="padding: 0 15px;">
        <form id="form" action="" class="layui-form form-display" lay-filter="formOk">
            <div class="default-form" style="margin:20px 10px;">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>年份：</label>
                        <div class="layui-input-block">
                            <select id="selyear" lay-filter="selyear" disabled="disabled"></select>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>月份：</label>
                        <div class="layui-input-block">
                            <select id="selmonth" lay-filter="selmonth" disabled="disabled"></select>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>在册儿童数：</label>
                        <div class="layui-input-block">
                            <input id="txtstucount" class="layui-input" value="0" lay-verify="required|chkcount" data-type="number" maxlength="4" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>手足口：</label>
                        <div class="layui-input-block">
                            <input id="txtshouzukou" class="layui-input" value="0" lay-verify="required|chkcount" data-type="number" maxlength="4" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>流感：</label>
                        <div class="layui-input-block">
                            <input id="txtliugan" class="layui-input" value="0" lay-verify="required|chkcount" data-type="number" maxlength="4" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>水痘：</label>
                        <div class="layui-input-block">
                            <input id="txtshuidou" class="layui-input" value="0" lay-verify="required|chkcount" data-type="number" maxlength="4" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>流腮：</label>
                        <div class="layui-input-block">
                            <input id="txtliusai" class="layui-input" value="0" lay-verify="required|chkcount" data-type="number" maxlength="4" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>猩红热：</label>
                        <div class="layui-input-block">
                            <input id="txtxinghongre" class="layui-input" value="0" lay-verify="required|chkcount" data-type="number" maxlength="4" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>百日咳：</label>
                        <div class="layui-input-block">
                            <input id="txtbairike" class="layui-input" value="0" lay-verify="required|chkcount" data-type="number" maxlength="4" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>风疹：</label>
                        <div class="layui-input-block">
                            <input id="txtfengzhen" class="layui-input" value="0" lay-verify="required|chkcount" data-type="number" maxlength="4" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>麻疹：</label>
                        <div class="layui-input-block">
                            <input id="txtmazhen" class="layui-input" value="0" lay-verify="required|chkcount" data-type="number" maxlength="4" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>急性出血性结膜炎：</label>
                        <div class="layui-input-block">
                            <input id="txtjiemoyan" class="layui-input" value="0" lay-verify="required|chkcount" data-type="number" maxlength="4" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>痢疾：</label>
                        <div class="layui-input-block">
                            <input id="txtliji" class="layui-input" value="0" lay-verify="required|chkcount" data-type="number" maxlength="4" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>病毒性肝炎：</label>
                        <div class="layui-input-block">
                            <input id="txtganyan" class="layui-input" value="0" lay-verify="required|chkcount" data-type="number" maxlength="4" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>其它感染性腹泻：</label>
                        <div class="layui-input-block">
                            <input id="txtfuxie" class="layui-input" value="0" lay-verify="required|chkcount" data-type="number" maxlength="4" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>疱疹性咽峡炎：</label>
                        <div class="layui-input-block">
                            <input id="txtyanjiayan" class="layui-input" value="0" lay-verify="required|chkcount" data-type="number" maxlength="4" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>其他：</label>
                        <div class="layui-input-block">
                            <input id="txtqita" class="layui-input" value="0" lay-verify="required|chkcount" data-type="number" maxlength="4" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <div class="layui-input-block">
                            <a id="btnget" class="layui-btn">获取数据</a>
                        </div>
                    </div>
                </div>
                <div style="display:none;">
                    <div class="layui-input-block">
                        <a id="btnok" class="layui-btn" lay-submit=lay-submit>立即提交</a>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <script data-main="../../js/richang/illtongjiadd" src="../../sys/require.min.js"></script>
</body>
</html>