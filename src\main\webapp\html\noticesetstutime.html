﻿<!DOCTYPE html>
<html>
<head>
    <title>通知-批量设置学习时长</title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link type="text/css" rel="stylesheet" href="../css/reset.css" />
    <link rel="stylesheet" type="text/css" href="../css/icon.css" />
    <link type="text/css" rel="stylesheet" href="../layui-btkj/css/layui.css" />
    <link rel="stylesheet" type="text/css" href="../css/commonstyle-layui-btkj.css" />
    <link rel="stylesheet" href="../css/medical.css">
    <style>
        .default-form .layui-form-label {
            width: 170px;
            line-height: 28px;
        }
        .layui-input-block {
            margin-left: 180px;
        }
    </style>
</head>
<body>
    <div class="layui-page" style="padding: 0 15px;">
        <form id="form" action="" class="layui-form form-display" lay-filter="formOk">
            <div class="default-form" style="margin:10px;">
                <div class="layui-form-item">
                    <div class="layui-inline" id="divimportway">
                        <label class="layui-form-label">是否覆盖： </label>
                        <div class="layui-input-block">
                            <div class="layui-input-inline isedit" style="">
                                <input type="radio" name="radcover" value="0" lay-filter="radcover" checked="checked" title="不覆盖" />
                                <input type="radio" name="radcover" value="1" lay-filter="radcover" title="覆盖" />
                                <label id="lbcover0" class="layui-form-label lbcover" style="width: 135px;display: none;">已有数值<b style="color:red;">不会被覆盖</b></label>
                                <label id="lbcover1" class="layui-form-label lbcover" style="width: 135px;display: none;">已有数值<b style="color:red;">会被覆盖</b></label>
                            </div>
                        </div>
                    </div>
                    <div id="divcertlist">

                    </div>
<!--                    <div class="layui-inline">-->
<!--                        <label class="layui-form-label">学习时长：</label>-->
<!--                        <div class="layui-input-block">-->
<!--                            <div class="layui-input-inline isview" style="display:none;">-->
<!--                                <label id="labclassname" style="display: contents;"></label>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
                </div>
                <div style="display:none;">
                    <div class="layui-input-block">
                        <a id="btnok" class="layui-btn" lay-submit=lay-submit>确定</a>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <script type="text/javascript">
        var v = top.version;
        document.write("<script type='text/javascript' src='../sys/jquery.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../sys/arg.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../sys/function.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../js/noticesetstutime.js?v=" + v + "'><" + "/script>");
    </script>
</body>
</html>
