<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>登录历史</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<link href="../styles/admin.css" rel="stylesheet" type="text/css" />
	<link href="../css/style.css" rel="stylesheet" type="text/css"/>
	<link href="../styles/tbstyles.css" rel="stylesheet" />
	<link href="../layui-btkj/css/layui.css" rel="stylesheet" type="text/css"/>
	<style type="text/css">
		.show-list .staff-sear label.lbfhide {
			display: none;
		}
		.show-list{font-size: 16px;text-align: center;padding: 15px 30px;}
	</style>
	<link rel="stylesheet" href="../layui-btkj/css/modules/layer/default/layer.css">
</head>
<body>
<div class="staff-con">
	<div class="bjright_nav" style="padding-bottom: 0;margin-left: 5px;width: auto">
		<input type="button" class="searchbutton" id="btnsearch" style=" vertical-align:top;margin-right: 5px;">登录统计
	</div>
	<div class="show-list">
		<span >登录总次数：<span id="totallogincount">0</span>次</span>
		<a style="margin: 0 20px;color: #2C9AEA;cursor: pointer;">总登录时长：<span id="hasouttimetotal">0</span></a>
		<span>平均登录时长：<span id="hasouttimepercent">0</span></span>
	</div>
	<div class="legend_bjcyadd" style="border-bottom: none;margin-left: 5px;">
		<u class="skinIconright"></u><label id="">登录历史</label>
	</div>
	<div class="alle-tab" style="margin: 0;margin-left: 10px;">
		<table id="tabelList" lay-filter="tabelList"></table>
	</div>
	<script type="text/html" id="toolbaropt">
		<a style="color: blue" lay-event="alterpwd" id="btn{{d.id}}">？！</a>
	</script>
</div>
<!--<script type="text/javascript" src="../sys/jquery.js"></script>-->
<!--<script type="text/javascript" src="../plugin/jqueryprint/jQuery.print.js"></script>-->
<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../sys/system.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../plugin/wdatepicker/wdatepicker.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../sys/function.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../js/loginhistory.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>
