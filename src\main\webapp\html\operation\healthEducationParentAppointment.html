<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>健康宣教家长预约情况</title>
    <link rel="stylesheet" type="text/css" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" type="text/css" href="../../css/icon.css"/>
    <link rel="stylesheet" type="text/css" href="../../styles/tbstyles.css"/>
    <link href="../../plugin/flexigrid/css/tbflexigrid.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css"/>
    <script type="text/javascript">
        // 检查 top.objdata 是否存在，如果不存在则设置默认主题
        var curtheme = (top && top.objdata && top.objdata.curtheme) ? top.objdata.curtheme : 'backstage';
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + curtheme + '.css" type="text/css" media="screen"/>');
    </script>

    <style type="text/css">
       html, body {
            background: #EAEFF3;
           
        }
        .layui-form-item {
            display: inline-block;
            vertical-align: top;
            margin-right: 15px;
            margin-bottom: 10px;
        }

        .layui-form-label {
            line-height: 28px;
            width: 110px;
        }

        .layui-input-inline {
            width: 120px;
        }

        .layui-form-mid {
            line-height: 28px;
            width: 30px;
            text-align: center;
        }

    </style>
</head>
<body>
<div class="marmain">
    <div class="content-medical">
        <div class="layui-form layui-comselect" style="padding:10px;">
            <!-- 开始日期 -->
            <div class="layui-form-item">
                <div class="layui-input-inline" style="width:120px; margin-right: 5px;">
                    <input id="datestart" type="text" autocomplete="off" placeholder="开始日期" class="layui-input">
                </div>
            </div>
            
            <!-- 至 -->
            <div class="layui-form-item">
                <div class="layui-form-mid">至</div>
            </div>
            
            <!-- 结束日期 -->
            <div class="layui-form-item">
                <div class="layui-input-inline" style="width:120px; margin-right: 20px;">
                    <input id="dateend" type="text" autocomplete="off" placeholder="结束日期" class="layui-input">
                </div>
            </div>
            
            <!-- 宣教文章 -->
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 80px;">宣教文章：</label>
                <div class="layui-input-inline" style="width:120px; margin-right: 15px;">
                    <select id="articletype" lay-filter="articletype">
                        <option value="">请选择</option>
                        <option value="营养膳食">营养膳食</option>
                        <option value="生长发育">生长发育</option>
                        <option value="疾病预防">疾病预防</option>
                        <option value="心理健康">心理健康</option>
                        <option value="安全教育">安全教育</option>
                    </select>
                </div>
            </div>
            
            <!-- 预约号码 -->
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 80px;">预约号码：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="appointno" lay-filter="appointno">
                        <option value="">请选择</option>
                        <option value="1">有预约</option>
                        <option value="0">无预约</option>
                    </select>
                </div>
            </div>
            
            <!-- 是否取消预约 -->
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 110px;">是否取消预约：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="iscanceled" lay-filter="iscanceled">
                        <option value="">请选择</option>
                        <option value="1">是</option>
                        <option value="0">否</option>
                    </select>
                </div>
            </div>
            
            <!-- 预约时间段 -->
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 100px;">预约时间段：</label>
                <div class="layui-input-inline" style="width:120px; margin-right: 15px;">
                    <select id="timeperiod" lay-filter="timeperiod">
                        <option value="">请选择</option>
                        <option value="08:00-10:00">08:00-10:00</option>
                        <option value="10:00-12:00">10:00-12:00</option>
                        <option value="14:00-16:00">14:00-16:00</option>
                        <option value="16:00-18:00">16:00-18:00</option>
                        <option value="19:00-21:00">19:00-21:00</option>
                    </select>
                </div>
            </div>
            
            <!-- 幼儿园 -->
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 70px;">幼儿园：</label>
                <div class="layui-input-inline" style="width:120px; margin-right: 15px;">
                    <select id="kindergarden" lay-filter="kindergarden">
                        <option value="">请选择</option>
                        <option value="蓝天幼儿园">蓝天幼儿园</option>
                        <option value="阳光幼儿园">阳光幼儿园</option>
                        <option value="金桥幼儿园">金桥幼儿园</option>
                        <option value="启明幼儿园">启明幼儿园</option>
                    </select>
                </div>
            </div>
            
            <!-- 年级 -->
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 50px;">年级：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="grade" lay-filter="grade">
                        <option value="">请选择</option>
                        <option value="小班">小班</option>
                        <option value="中班">中班</option>
                        <option value="大班">大班</option>
                    </select>
                </div>
            </div>
            
            <!-- 班级 -->
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 50px;">班级：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="classname" lay-filter="classname">
                        <option value="">请选择</option>
                        <option value="一班">一班</option>
                        <option value="二班">二班</option>
                        <option value="三班">三班</option>
                    </select>
                </div>
            </div>
            
            <!-- 幼儿性别 -->
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 80px;">幼儿性别：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="childsex" lay-filter="childsex">
                        <option value="">请选择</option>
                        <option value="男">男</option>
                        <option value="女">女</option>
                    </select>
                </div>
            </div>
            
            <!-- 体检年龄 -->
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 80px;">体检年龄：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="checkage" lay-filter="checkage">
                        <option value="">请选择</option>
                        <option value="3岁">3岁</option>
                        <option value="4岁">4岁</option>
                        <option value="5岁">5岁</option>
                        <option value="6岁">6岁</option>
                    </select>
                </div>
            </div>
            
            <!-- 家长性别 -->
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 80px;">家长性别：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="parentsex" lay-filter="parentsex">
                        <option value="">请选择</option>
                        <option value="男">男</option>
                        <option value="女">女</option>
                    </select>
                </div>
            </div>
            
            <!-- 家长年龄 -->
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 80px;">家长年龄：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="parentage" lay-filter="parentage">
                        <option value="">请选择</option>
                        <option value="20-30">20-30岁</option>
                        <option value="30-40">30-40岁</option>
                        <option value="40-50">40-50岁</option>
                    </select>
                </div>
            </div>
            
            <!-- 访问设备 -->
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 80px;">访问设备：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="device" lay-filter="device">
                        <option value="">请选择</option>
                        <option value="安卓">安卓</option>
                        <option value="苹果">苹果</option>
                        <option value="PC">PC</option>
                    </select>
                </div>
            </div>
            
            <!-- 区域分类 -->
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 80px;">区域分类：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="areatype" lay-filter="areatype">
                        <option value="">请选择</option>
                        <option value="城区">城区</option>
                        <option value="郊区">郊区</option>
                    </select>
                </div>
            </div>
            
            <!-- 孩子与家长关系 -->
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 120px;">孩子与家长关系：</label>
                <div class="layui-input-inline" style="width:120px; margin-right: 20px;">
                    <select id="relationship" lay-filter="relationship">
                        <option value="">请选择</option>
                        <option value="父子">父子</option>
                        <option value="母子">母子</option>
                        <option value="父女">父女</option>
                        <option value="母女">母女</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
            </div>
            
            <!-- 关键字 -->
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 60px;">关键字：</label>
                <div class="layui-input-inline" style="width:200px; margin-right: 20px;">
                    <input id="txtkeyword" type="text" autocomplete="off" placeholder="请输入幼儿姓名或家长姓名" class="layui-input">
                </div>
            </div>
            
            <!-- 查询按钮 -->
            <div class="layui-form-item">
                <button class="layui-btn form-search" style="margin-left: 10px;" id="btnsearch">查询</button>
            </div>
        </div>
    </div>
    <div id="content" style="width: 100%;">
        <div class="marmain-cen">
            <div class="content-medical" id="divtable">
                <table id="laytable" lay-filter="laytable"></table>
            </div>
        </div>
    </div>
</div>

<script data-main="../../js/operation/healthEducationParentAppointment" src='../../sys/require.min.js'></script>
</body>
</html> 