package com.btkj.fuyou.wrules.check;

import com.whoami.db.DBUtil;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 标签算法管理器
 * 负责标签算法的加载、缓存和管理
 */
public class TagAlgorithmManager {
    private static Logger log = org.apache.logging.log4j.LogManager.getLogger(TagAlgorithmManager.class);
    
    // 算法缓存，key为标签编码
    private static Map<String, JSONObject> algorithmCache = new ConcurrentHashMap<>();
    
    // 缓存最后更新时间
    private static long lastUpdateTime = 0;
    
    // 缓存刷新间隔（毫秒）
    private static final long CACHE_REFRESH_INTERVAL = 5 * 60 * 1000; // 5分钟
    
    /**
     * 获取所有活跃的标签算法
     * @param cp 数据库连接参数
     * @return 算法列表
     */
    public static List<JSONObject> getActiveAlgorithms(String[] cp) {
        refreshCacheIfNeeded(cp);
        List<JSONObject> algorithms = new ArrayList<>();
        for (JSONObject algorithm : algorithmCache.values()) {
            if (algorithm.optInt("is_active", 0) == 1) {
                algorithms.add(algorithm);
            }
        }
        return algorithms;
    }
    
    /**
     * 根据标签编码获取算法
     * @param tagCode 标签编码
     * @param cp 数据库连接参数
     * @return 算法对象
     */
    public static JSONObject getAlgorithm(String tagCode, String[] cp) {
        refreshCacheIfNeeded(cp);
        return algorithmCache.get(tagCode);
    }
    
    /**
     * 根据分类获取算法列表
     * @param category 分类
     * @param cp 数据库连接参数
     * @return 算法列表
     */
    public static List<JSONObject> getAlgorithmsByCategory(String category, String[] cp) {
        refreshCacheIfNeeded(cp);
        List<JSONObject> algorithms = new ArrayList<>();
        for (JSONObject algorithm : algorithmCache.values()) {
            if (algorithm.optInt("is_active", 0) == 1 && 
                category.equals(algorithm.optString("category"))) {
                algorithms.add(algorithm);
            }
        }
        return algorithms;
    }
    
    /**
     * 如果需要则刷新缓存
     * @param cp 数据库连接参数
     */
    private static void refreshCacheIfNeeded(String[] cp) {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastUpdateTime > CACHE_REFRESH_INTERVAL || algorithmCache.isEmpty()) {
            loadAlgorithms(cp);
        }
    }
    
    /**
     * 从数据库加载算法到缓存
     * @param cp 数据库连接参数
     */
    public static void loadAlgorithms(String[] cp) {
        try {
            String sql = "SELECT id, labelname, algorithm_type, algorithm_content,  category,  is_active " +
                       "FROM xj_label WHERE isdel=0 and is_active = 1";
            
            JSONObject result = DBUtil.select(cp[0], cp[1], sql);
            
            if (result.has("re")) {
                Map<String, JSONObject> newCache = new ConcurrentHashMap<>();
                org.json.JSONArray dataArray = result.optJSONArray("re");
                
                for (int i = 0; i < dataArray.length(); i++) {
                    JSONObject row = dataArray.optJSONObject(i);
                    JSONObject algorithm = new JSONObject();
                    algorithm.put("id", row.optString("id"));
                    algorithm.put("labelname", row.optString("labelname"));
                    algorithm.put("algorithm_type", row.optString("algorithm_type"));
                    algorithm.put("algorithm_content", row.optString("algorithm_content"));
                    algorithm.put("category", row.optString("category"));
                    algorithm.put("priority", row.optInt("priority"));
                    algorithm.put("is_active", row.optInt("is_active"));
                    algorithm.put("update_time", row.optString("update_time"));
                    
                    newCache.put(row.optString("id"), algorithm);
                }
                
                algorithmCache = newCache;
                lastUpdateTime = System.currentTimeMillis();
                log.info("Loaded " + algorithmCache.size() + " tag algorithms");
            } else {
                log.warn("Failed to load tag algorithms: " + result.optString("msg"));
            }
            
        } catch (Exception e) {
            log.error("Exception", e);
        }
    }
    
    /**
     * 强制刷新缓存
     * @param cp 数据库连接参数
     */
    public static void forceRefresh(String[] cp) {
        lastUpdateTime = 0;
        loadAlgorithms(cp);
    }
    
    /**
     * 清空缓存
     */
    public static void clearCache() {
        algorithmCache.clear();
        lastUpdateTime = 0;
    }
}