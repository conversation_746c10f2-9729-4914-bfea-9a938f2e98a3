<!DOCTYPE html>
<html>
<head>
<title>接诊登记</title>
<meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
<link rel="stylesheet" href="../css/reset.css">
<link rel="stylesheet" href="../css/icon.css">
<link rel="stylesheet" href="../layui-btkj/css/layui.css">
<link rel="stylesheet" href="../css/commonstyle-layui-btkj.css">
<link rel="stylesheet" href="../css/medical.css">
<script type="text/javascript" src="../layui-btkj/layui.js"></script>
<style>
.view p{color: #34495E; font-size: 14px;}
.layui-form-label {
  text-align: left;
  width: 120px;
}
</style>
</head>
<body>
<section>
	<div style="margin: 10px 15px;">
		<div class="form-list">
			<h3 class="title-info"> <span>基本信息</span> </h3>
			<div class="layui-form-item" style="margin: 0;">
				<label class="layui-form-label" style="float: left;">姓 名：</label>
				<div class="layui-input-block" style="margin-left: 5px;width:300px;float: left;">
					<p>李林颖</p>
				</div>
				<label class="layui-form-label" style="float: left;">性 别：</label>
				<div class="layui-input-block" style="margin-left: 5px;float: left;width: 30%;">
					<p>女</p>
				</div>
			</div>
			<div class="layui-form-item" style="margin: 0;">
				<label class="layui-form-label" style="float: left;">年 龄：</label>
				<div class="layui-input-block" style="margin-left: 5px;width:300px;float: left;">
					<p>6月6天</p>
				</div>
				<label class="layui-form-label" style="float: left;">出生时间：</label>
				<div class="layui-input-block" style="margin-left: 5px;float: left;width: 30%;">
					<p>2023-07-02</p>
				</div>
			</div>
			<div class="layui-form-item" style="margin: 0;">
				<label class="layui-form-label" style="float: left;">联系电话：</label>
				<div class="layui-input-block" style="margin-left: 5px;float: left;">
					<p>2020.09.09-2020.09.10</p>
				</div>
			</div>
		
		</div>
		<div class="form-list">
			<h3 class="title-info"> <span>转诊信息</span> </h3>
			<div class="layui-form-item" style="margin: 0;">
				<label class="layui-form-label" style="float: left;">转诊日期：</label>
				<div class="layui-input-block" style="margin-left: 5px;width:300px;float: left;">
					<p>2023-06-29</p>
				</div>
				<label class="layui-form-label" style="float: left;">转诊医生：</label>
				<div class="layui-input-block" style="margin-left: 5px;float: left;width: 30%;">
					<p>赵晓珲</p>
				</div>
			</div>
			<div class="layui-form-item" style="margin: 0;">
				<label class="layui-form-label" style="float: left;">转诊机构：</label>
				<div class="layui-input-block" style="margin-left: 5px;float: left;">
					<p>船房街道卫生服务中心</p>
				</div>
			</div>
			<div class="layui-form-item" style="margin: 0;">
				<label class="layui-form-label" style="float: left;">转诊原因：</label>
				<div class="layui-input-block" style="margin-left: 5px;float: left;">
					<p>微笑型抑郁症</p>
				</div>
			</div>
		</div>
		<div class="form-list">
			<h3 class="title-info"> <span>接诊信息</span> </h3>
				<div class="layui-form-item" style="margin: 0;">
				<label class="layui-form-label" style="float: left;">转诊医院：</label>
				<div class="layui-input-block" style="margin-left: 5px;float: left;">
					<p>广安门中医院</p>
				</div>
			</div>
				<div class="layui-form-item layui-form " style="margin: 0;">
				<label class="layui-form-label" style="float: left;">接诊医生：</label>
				<div class="layui-input-block" style="margin-left: 5px;width:300px;float: left;">
					<p><input type="text" placeholder="请输入" class="layui-input" style="width: 250px;"></p>
				</div>
				
				<label class=" layui-form-label" style="float: left;">转诊医生：</label>
				<div class="layui-input-block" style="min-height: 30px;margin-left:0px;width:250px;float: left;">
				<select name="city" lay-verify="required">
					<option value="请选择">请选择</option>
				</select>
			</div>
			</div>
	  </div>
	</div>
		<div style="padding: 10px 0;text-align: center;background: #F8F8F8;position: fixed;bottom: 0;width: 100%;">
		<div style="margin-right: 15px;"> <a class="layui-btn gray-bg">取消</a> <a class="layui-btn">保存</a> </div>
	</div>
</section>
	<script>
		//Demo
		layui.use('form', function(){
		  var form = layui.form;
		  
		  //监听提交
		  form.on('submit(formDemo)', function(data){
		    layer.msg(JSON.stringify(data.field));
		    return false;
		  });
		});
		</script>
<script>
		layui.use(['form', 'laydate'], function(){			
		});
	</script>
</body>
</html>