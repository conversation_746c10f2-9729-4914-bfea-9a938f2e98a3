﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>儿童基础档案</title>
    <link rel="stylesheet" type="text/css" href="../../css/reset.css" />
    <link rel="stylesheet" type="text/css" href="../../layui-btkj/css/layui.css" />
    <link rel="stylesheet" type="text/css" href="../../css/icon.css" />
    <link rel="stylesheet" type="text/css" href="../../css/commonstyle-layui-btkj.css" />
    <link rel="stylesheet" type="text/css" href="../../css/style.css" />
    <link rel="stylesheet" href="../../css/medical.css" />
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <style>
        html, body { background: #EAEFF3; overflow: hidden; }

        .layui-form-item { display: inline-block; vertical-align: top; margin: 5px 5px 0px 0; }

        .layui-form-label { line-height: 28px; }

        /*.layui-table-cell{height:auto;}*/
    </style>
</head>
<body>
    <div class="marmain">
        <div class="content-medical">
            <div class="layui-form layui-comselect" style="padding: 10px;">
                <div class="layui-form-item" style="display: none;">
                    <label class="layui-form-label">区县名称：</label>
                    <div class="layui-input-inline">
                        <select id="selarea" lay-filter="selarea"></select>
                    </div>
                </div>
                <div class="layui-form-item" style="">
                    <label class="layui-form-label">托幼机构类型：</label>
                    <div class="layui-input-inline">
                        <select id="selyeytype" lay-filter="selyeytype"></select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">托幼机构名称：</label>
                    <div class="layui-input-inline" style="float: left; width: 200px;">
                        <div id="selyeyid" class="xm-select-demo" style="display: inline-block; width: 100%;"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">年龄段：</label>
                        <div class="layui-input-inline" style="width: 100px;">
                            <select id="selage1" lay-filter="selage1">
                                <option value="">请选择</option>
                                <option value="0">0岁</option>
                                <option value="1">1岁</option>
                                <option value="2">2岁</option>
                                <option value="3">3岁</option>
                                <option value="4">4岁</option>
                                <option value="5">5岁</option>
                                <option value="6">6岁</option>
                                <option value="7">7岁</option>
                            </select>
                        </div>
                        <div class="layui-form-mid">-</div>
                        <div class="layui-input-inline" style="width: 100px;">
                            <select id="selage2" lay-filter="selage2">
                                <option value="">请选择</option>
                                <option value="0">0岁</option>
                                <option value="1">1岁</option>
                                <option value="2">2岁</option>
                                <option value="3">3岁</option>
                                <option value="4">4岁</option>
                                <option value="5">5岁</option>
                                <option value="6">6岁</option>
                                <option value="7">7岁</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">关键字：</label>
                    <div class="layui-input-inline">
                        <input type="text" id="txtkeyword" lay-verify="required" placeholder="请输入幼儿姓名/身份证号" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">一卡通对接状态：</label>
                        <div class="layui-input-inline" style="width: 100px;">
                            <select id="ecardstate" lay-filter="ecardstate">
                                <option value="">请选择</option>
                                <option value="1">已对接</option>
                                <option value="2">未对接</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">状态：</label>
                    <div class="layui-input-inline" style="min-height: 35px; line-height: 36px;">
                        <input type="radio" name="radstatus" value="0" title="在园">
                        <input type="radio" name="radstatus" value="1" title="离校">
                        <input type="radio" name="radstatus" value="2" title="毕业">
                    </div>
                </div>
                <div class="layui-form-item">
                    <button class="layui-btn form-search" style="margin-left: 5px; vertical-align: top;" id="btnSearch">查询</button>
                    <button class="layui-btn form-search" style="margin-left: 5px; vertical-align: top;" id="btnexcel">导出Excel</button>
                </div>
            </div>
        </div>
        <div id="content" style="width: 100%;">
            <div class="marmain-cen">
                <div class="content-medical" id="divtable">
                    <table id="laytable" lay-filter="laytable"></table>
                </div>
            </div>
        </div>
    </div>
    <script type="text/html" id="toolbarlaytable">
        <div style="">
            <span style="font-size: 14px; font-weight: bold;">儿童列表</span>
        </div>
    </script>
    <script data-main="../../js/child/childlist" src="../../sys/require.min.js"></script>
</body>
</html>
