<!DOCTYPE html>
<html>
<head>
    <title>新增企业管理</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <link rel="stylesheet" href="../css/style.css"/>
    <link href="../plugin/chosen/css/chosen.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <style>
        body {
            background: #fff;
        }
        /*.incr-chat {*/
            /*width: 734px;*/
            /*margin: 0px auto;*/
            /*padding: 0 40px*/
        /*}*/
        .layui-div {
            width: 612px;
            margin: 30px auto;
        }
        .layui-input-block .layui-input {
            width: 200px;
        }
    </style>
</head>
<body>
<section class="incr-chat" style="">
    <div class="layui-div layui-form" action="">
        <div class="layui-form-item">
            <label class="layui-form-label">姓名:</label>
            <div class="layui-input-block">
                <input id="truename" type="text" name="truename" required="" lay-verify="required" maxlength="30" autocomplete="off" class="layui-input">
                <input type="hidden" id="txtsp" name="txtsp"/>
                <input type="hidden" id="txtspe" name="txtspe"/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">手机号:</label>
            <div class="layui-input-block">
                <input id="mobile" type="text" name="mobile" required="" lay-verify="required" maxlength="15" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">身份证号:</label>
            <div class="layui-input-block">
                <input id="idcard" type="text" name="idcard" required="" lay-verify="required" maxlength="18" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">性别:</label>
            <div class="layui-input-block">
                <input id="txtsex" type="text" name="txtsex" required="" lay-verify="required" maxlength="2" autocomplete="off" class="layui-input" readonly="readonly" style="border:0;">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">出生日期:</label>
            <div class="layui-input-block">
                <input id="birthday" type="text" name="birthday" required="" lay-verify="required" maxlength="10" autocomplete="off" class="layui-input" readonly="readonly" style="border:0;">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">婚否</label>
            <div class="layui-input-inline">
                <select id="ismarried" style="width:150px;" name="ismarried" lay-filter="sysitem">
                    <option value="1" selected>已婚</option>
                    <option value="0">未婚</option>
                </select>
            </div>

        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">现住址:</label>
            <div class="layui-input-inline" style="width: 360px;">
                <input id="address" type="text" name="address" autocomplete="off" class="layui-input">
            </div>
        </div>
    </div>
</section>

<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../sys/system.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../plugin/wdatepicker/wdatepicker.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../js/custom/personneladd.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>
