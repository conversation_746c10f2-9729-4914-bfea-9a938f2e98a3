<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>科室设置</title>
    <link rel="stylesheet" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../plugin/zTree/css/zTreeStyle/zTreeStyle.css" type="text/css">
    <link rel="stylesheet" href="../../css/style.css"/>
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../styles/tbstyles.css"/>
    <link rel="stylesheet" href="../../css/medical.css">
    <link rel="stylesheet" type="text/css" href="../../css/commonstyle-layui-btkj.css"/>
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + top.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <style>
        ul {
            line-height: 40px;
        }

        html, body {
            background: #EAEFF3;
        }

        /*按钮被禁用时的样式*/
        .layui-btn.disabled-btn {
            background-color: #ccc !important;
            color: #888;
            opacity: 0.6;
            cursor: not-allowed;
        }

        .popRight {
            margin-left: 260px;
            background: #fff;
            width: 100%;
            min-height: calc(100vh - 28px);
        }

        .popRight {
            margin-left: 260px;
        }

    </style>
</head>

<body>
<div class="marmain" style="display: flex; flex-direction: row;">
    <section class="treeWrap" style="position:fixed;background: #fff;color: #34495E;left:8px;">
        <div class="content_wrap">
            <div class="zTreeDemoBackground left">
                <ul id="treeDemo" class="ztree"></ul>
            </div>
        </div>
        <div style="display: flex; justify-content: center; align-items: center; height: 150px; display: none">
            <button type="button" class="layui-btn layui-bg-blue" style="width: 50%;">导入科室</button>
        </div>
    </section>

    <section class="popRight" style="padding:10px 0 ;display:none;">
        <div class="form-list">
            <!-- 第1行 -->
            <div class="layui-form-item layui-form " style="margin: 0;">
                <div class="keshi-left">
                    <label class="layui-form-label" style="float: left;">门诊名称：</label>
                    <div class="layui-input-block" style="margin-left: 10px;float: left;width: 70%;">
                        <span id="nname" style="line-height: 37px;"></span>
                    </div>
                </div>
                <div class="keshi-left">
                    <label class=" layui-form-label" style="float: left;">编号：</label>
                    <div class="layui-input-block" style="margin-left: 5px;float: left; width: 70%;">
                        <p>
                            <input type="text" placeholder="请输入" class="layui-input" id="deptId" maxlength="32">
                        </p>
                    </div>
                    <div style="margin-left: 20px;float: left;width: 10%;">
                        <button id="btnSave" class="layui-btn bluebtn btnoperate" lay-submit="" lay-filter="formDemo"
                                style="background:#4A90E2 !important; color: #fff !important;width: 70px; height: 37px; line-height: 37px;">
                            保存
                        </button>
                    </div>
                </div>
            </div>
            <!-- 第二行 -->
            <div class="layui-form-item layui-form " style="margin-top:10px;">
                <div class="keshi-left">
                    <label class="layui-form-label" style="float: left;">门诊介绍：</label>
                    <div class="layui-input-block" style="margin-left: 10px;float: left;" id="keshiIntro">
                        <p class="addtxt">
                            <label>添加 </label> / <label>编辑</label>
                        </p>
                    </div>
                </div>
                <div class="keshi-left">
                    <label class=" layui-form-label" style="float: left;">就诊须知：</label>
                    <div class="layui-input-block" style="margin-left: 10px;float: left;" id="medicalNotice">
                        <p class="addtxt">
                            <label>添加 </label> / <label>编辑</label>
                        </p>
                    </div>
                </div>
                <!-- 第3行 -->
                <div class="keshi-left" style="margin-top: 10px;">
                    <label class=" layui-form-label" style="float: left; width: 70px;">号&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;源：</label>
                    <div class="layui-input-block" style="margin-left: 10px;float: left;" id="addFun">
                        <p class="addtxt">
                            <label>添加 </label> / <label>编辑</label>
                        </p>
                    </div>
                </div>
            </div>
            <div id="show-panel"></div>
        </div>
    </section>
</div>

<script type="text/javascript" data-main="../../js/depart/departset" src="../../sys/require.min.js"></script>
</body>
</html>