<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="renderer" content="webkit"/>
    <title>妇幼医院介绍-机构介绍</title>
    <link rel="stylesheet" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../css/style.css"/>
    <link href="../../styles/tbstyles.css" type="text/css" rel="stylesheet"/>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../plugin/calendar/fullcalendar.css"/>
    <link rel="stylesheet" href="../../plugin/calendar/fullcalendar.print.css" media="print"/>
    <link type="text/css" rel="stylesheet" href="../../plugin/jquery-ui/jquery-ui.min.css">
    <!--[if lt IE 9]>
    <script src="../../sys/html5shiv.min.js"></script>
    <![endif]-->
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>

    <style>
        html, body {
            background: #EAEFF3;
        }

        .phone-detail {
            display: inline-block;
            background: url(../../images/medical/phone_imgbg.png) no-repeat;
            width: 358px;
            height: 651px;
            position: relative;
            margin-top: 27px;
            background-size: 100% 100%;
        }

        .phone-txt {
            background: #ffffff;
            border-radius: 0 0 22px 22px;
            width: 275px;
            height: 454px;
            margin: 15px auto 0 30px;
            text-align: left;
            padding: 0 10px 10px 10px;
            overflow-y: auto;
        }

        ul {
            line-height: 40px;
        }

        .ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default{ color:#34495E;background:none !important;border: none;}
        .ui-widget-header {background: none;border:1px solid #E7ECF0;color: #34495E;font-weight:normal;}
        .ui-widget-content{background: #fff;}
        .fc-day-num{color: #34495E;font-size: 18px;font-weight: normal;float: left;padding-left: 20px;}
        .fc th, .fc td{border:1px solid #E7ECF0;}
        .fc th{text-align: left;padding-left: 20px;}
        .ui-state-default .ui-icon {
            background-image:none;
        }
        .ui-state-default .ui-icon.ui-icon-circle-triangle-w  {
            background-image:url(../../images/medical/left.png);width: 6px; height: 12px; background-size:6px 12px;
        }
        .ui-state-default .ui-icon.ui-icon-circle-triangle-e {
            background-image:url(../../images/medical/right.png);width: 6px; height: 12px; background-size:6px 12px;
        }
        .ui-icon-circle-triangle-e {
            background-position:left;
        }
        .fc-prev-button span, .fc-next-button span {
            visibility:visible;
        }
        .ui-icon-circle-triangle-w {
            background-position:left;
        }
        .gardenhouse-left{padding: 30px 20px; }
        .fc-day-header { line-height: 40px;}
        .fc-toolbar .fc-left {
            float:none;
            position: absolute;
            top: 3px;
        }
        .ui-widget{position: relative;}
        .checkbox-blue select{border:1px solid #CBD6E1; margin-right: 10px;}

        .layui-laydate-content>.layui-laydate-list {
            padding-bottom: 0px;
            overflow: hidden;
        }
        .layui-laydate-content>.layui-laydate-list>li{
            width:50%
        }
    </style>
</head>
<body style="min-width: 1300px;">
<div class="marmain">
    <div class="content-medical" style="overflow: hidden;">
        <div style="float: left;">
            <section class="formList" style="padding-left: 10px">
                <div style="clear: both">
                    <label style="padding: 8px 0px 5px 0px; text-align: right;width: 135px;"><em>*</em>机构名称：</label>
                    <span>
                        <input class="text-input" maxlength="50" id="hospitalname" value="" placeholder="机构名称" name=""
                               type="text" style="width: 300px;"/>
                    </span>
                </div>
                <div style="clear: both">
                    <label style="padding: 8px 0px 5px 0px; text-align: right;width: 135px;"><em>*</em>机构等级：</label>
                    <span>
                        <input class="text-input" maxlength="50" id="hospitallevel" value="" placeholder="机构等级" name=""
                               type="text" style="width: 300px;"/>
                    </span>
                </div>
                <div class="pr">
                    <label style="padding: 8px 0px 5px 0px; text-align: right;width: 135px;"><em>*</em>机构地址：</label>
                    <input class="text-input" maxlength="100" id="mapaddress" placeholder="机构地址" name="" type="text"
                           style="width: 300px;"/>
                    <span class="location" id="mapmsg"><a class="lanflag" style=" visibility: visible;color: #34495E;">点击图标定位</a></span>
                </div>
                <div style="clear: both">
                    <label style="padding: 8px 0px 5px 0px; text-align: right;width: 135px;">乘车路线：</label>
                    <span>
                        <input class="text-input" maxlength="100" id="carline" value="" placeholder="乘车路线" name=""
                               type="text"
                               style="width: 300px;"/>
                    </span>
                </div>
                <div style="clear: both">
                    <label style="padding: 8px 0px 5px 0px; text-align: right;width: 135px;">联系电话：</label>
                    <span>
                        <input class="text-input" maxlength="20" id="hospitalphone" value="" placeholder="联系电话" name=""
                               type="text"
                               style="width: 300px;"/>
                    </span>
                </div>
            </section>

            <section>
                <div class="">
                    <div class="content-medical">
                        <div class="examination-number">
                            <div class="layui-form-item" style="display: inline-block;vertical-align: top;width: 100%;line-height: 33px">
                                <label class="layui-form-label" style="padding: 8px 0px 5px 0px; text-align: right;width: 135px;"><em>*</em>工作时间：</label>
                                <div class="def-search" style="margin-top: -10px;">
                                    <div>
                                        <div style="margin-left: 96px;display: flex;margin-top:10px;height: 30px;line-height: 19px;">
                                            <label style="line-height: 36px;display: inline-block;vertical-align: top;margin-right: 5px;width:60px;">上午</label>
                                            <span>
                                       <input id="txtamstarttime" type="text" autocomplete="off" placeholder="开始时间" class="layui-input" onblur="changeWorkTime" style="width: 130px;" lay-ignore>
                                   </span>
                                            <span class="linebg">-</span>
                                            <span>
                                       <input id="txtamendtime" type="text" autocomplete="off" placeholder="结束时间" class="layui-input" onchange="changeWorkTime" style="width: 130px;" lay-ignore>
                                   </span>

                                            <label style="line-height: 36px;display: inline-block;vertical-align: top; margin-left: 10px;margin-right: 5px;width:60px;">下午</label>
                                            <span>
                                       <input id="txtpmstarttime" type="text" autocomplete="off" placeholder="开始时间" class="layui-input" onchange="changeWorkTime" style="width: 130px;" lay-ignore>
                                   </span>
                                            <span class="linebg">-</span>
                                            <span>
                                       <input id="txtpmendtime" type="text" autocomplete="off" placeholder="结束时间" class="layui-input" onchange="changeWorkTime" style="width: 130px;" lay-ignore>
                                   </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div style="display:block; margin-top: 0px;">
                                <label style="float: left;line-height: 38px;text-align: right;width: 135px;display: inline-block;"><em>*</em>医院开放时间：</label>
                                <div class="setup-con">
                                    <!--  <label style="width: 146px;"></label>-->
                                    <div style="width: 100%" id="setweekdate">
                                        <div id="checktime" class="setup-timeone">
                                        </div>
                                        <div id="customsetup"></div>

                                    </div>
                                </div>
                                <button id="addbtn" class="layui-btn addbtn" style="margin:10px; margin-left: 135px;display: none; "> +添加自定义设置</button>
                            </div>
                        </div>
                    </div>
                    <div style="display: none;" class="datemain">
                        <ul>
                            <li>
                                <div id="main" style="width:100%">
                                    <div id='calendar'></div>
                                    <div id="window"></div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </section>

            <div style="display: flex;flex-direction: column">
                <div style="padding-top: 20px;">
                    <label class="fl" style="width: 103px;text-align: right;">机构简介：</label>
                    <div class="scloolright" style="width: 650px;font-size: 14px;color: #34495E;">
                        <script id="container" name="content" type="text/plain"></script>
                    </div>
                </div>
                <div>
                    <button id="btnSave" class="layui-btn bluebtn btnoperate" lay-submit="" lay-filter="formDemo"
                            style="background:#4A90E2 !important; color: #fff !important;width: 140px; height: 48px; line-height: 48px; margin: 20px 10px 30px 103px;">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/system.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/uploadutil.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../plugin/nicescroll/jquery.nicescroll.min.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../plugin/js/moment.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../plugin/ueditor/ueditor.config.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../plugin/ueditor/ueditor.all.min.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../plugin/ueditor/lang/zh-cn/zh-cn.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../plugin/ueditor/third-party/zeroclipboard/ZeroClipboard.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../plugin/ueditor/third-party/webuploader/webuploader.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../plugin/wdatepicker/wdatepicker.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../plugin/calendar/fullcalendar.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../js/richang/introduction.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>
