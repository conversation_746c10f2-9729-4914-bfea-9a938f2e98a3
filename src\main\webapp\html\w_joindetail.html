<!DOCTYPE html>
<html>
<head>
    <title>查看园所申请的详情信息</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <link rel="stylesheet" href="../css/reset.css"/>
    <link rel="stylesheet" href="../css/style.css"/>
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <style>
        th, td {
            border: 1px solid black;
        }
		.heightnum span{display: inline-block; text-align: left;width: 100px;}
    </style>
    <!--[if lt IE 9]>
    <script src='sys/html5shiv.min.js'></script>
    <![endif]-->
</head>
<body>
<div id="addyeyinfo" class="layui-row heightnum" style="display:none;">
    <script id="arryeyinfo" type="text/html">
        {{#  layui.each(d.list, function(index, item){ }}
        <div class="layui-col-xs6 layui-col-sm6 layui-col-md6">
            <p><span>园所名称：</span><label class="layui-color-gary1">{{ item.yeyname }}</label></p>
            <p><span>账号：</span><label class="layui-color-gary1">{{ item.phone }}</label></p>
            <p><span>所属医院名称：</span><label class="layui-color-gary1">{{ item.fuyouorganname }}</label></p>
        </div>
        <div class="layui-col-xs6 layui-col-sm6 layui-col-md6">
            <p><span>园所ID：</span><label class="layui-color-gary1">{{ item.yeyid }}</label></p>
            <p><span>添加时间：</span><label class="layui-color-gary1">{{ timeFormat(item.relationtime) }}</label></p>
            <p><span>体检医院名称：</span><label class="layui-color-gary1">{{ timeFormat(item.fuyoumdicalorganname) }}</label></p>
        </div>
        {{#  }); }}
    </script>
</div>
<div id="info" class="layui-row heightnum" style="border-bottom: 1px solid #ccc;display:none;">
    <script id="yeyinfo" type="text/html">
        {{#  layui.each(d.list, function(index, item){ }}
        <div class="layui-col-xs6 layui-col-sm6 layui-col-md6">
            <p><span>园所名称：</span><label class="layui-color-gary1">{{ item.yeyname }}</label></p>
            <p><span>申请账号：</span><label class="layui-color-gary1">{{ item.joinname }}</label></p>
            <p><span>所属医院名称：</span><label class="layui-color-gary1">{{ item.fuyouorganname }}</label></p>
        </div>
        <div class="layui-col-xs6 layui-col-sm6 layui-col-md6">
            <p><span>园所ID：</span><label class="layui-color-gary1">{{ item.yeyid }}</label></p>
            <p><span>申请加入时间：</span><label class="layui-color-gary1">{{ timeFormat(item.sendtime) }}</label></p>
            <p><span>体检医院名称：</span><label class="layui-color-gary1">{{ timeFormat(item.fuyoumdicalorganname) }}</label></p>
        </div>
        {{#  }); }}
    </script>
</div>
<div class="heightnum js_yeyuserinfo" style="min-height: 200px;overflow: auto;display: none;">
    <div class="person-tab" style="margin: 10px 0;">
        <p class="gove-tit">园长负责人列表</p>
        <table border="1" cellpadding="0" cellspacing="0" style="width: 100%;border-collapse: collapse;">
            <thead>
            <tr style="background: #F1F1F1;">
                <td style="width: 22%;">园长姓名</td>
                <td style="width: 22%;">手机号</td>
                <td style="width: 31%;">邮箱</td>
<!--                <td style="width: 25%;">微信绑定状态</td>-->
            </tr>
            </thead>
            <tbody id="yz">
            <script id="yuanzhang" type="text/html">
                {{#  layui.each(d.yzlist, function(index, item){ }}
                <tr>
                    <td style="text-align: center">{{item.truename }}</td>
                    <td style="text-align: center">{{item.mobile }}</td>
                    <td style="text-align: center">{{item.email }}</td>
<!--                    {{# if(!item.wxstatus){ }}-->
<!--                    {{# } else if(item.wxstatus==1){ }}-->
<!--                    <td style="text-align: center">已绑定</td>-->
<!--                    {{# } else if(item.wxstatus==0){ }}-->
<!--                    <td style="text-align: center">未绑定</td>-->
<!--                    {{# } }}-->
                </tr>
                {{#  }); }}
            </script>
            </tbody>
        </table>
    </div>
    <div class="person-tab" style="margin: 10px 0;">
        <p class="gove-tit">保健医负责人列表</p>
        <table border="1" cellpadding="0" cellspacing="0" style="width: 100%;border-collapse: collapse;">
            <thead>
            <tr style="background: #F1F1F1;">
                <td style="width: 22%;">保健医姓名</td>
                <td style="width: 22%;">手机号</td>
                <td style="width: 31%;">邮箱</td>
<!--                <td style="width: 25%;">微信绑定状态</td>-->
            </tr>
            </thead>
            <tbody id="bjy">
            <script id="baojianyi" type="text/html">
                {{#  layui.each(d.bjylist, function(index, item){ }}
                <tr>
                    <td style="text-align: center">{{item.truename }}</td>
                    <td style="text-align: center">{{item.mobile }}</td>
                    <td style="text-align: center">{{item.email }}</td>
<!--                    {{# if(!item.wxstatus){ }}-->
<!--                    {{# } else if(item.wxstatus==1){ }}-->
<!--                    <td style="text-align: center">已绑定</td>-->
<!--                    {{# } else if(item.wxstatus==0){ }}-->
<!--                    <td style="text-align: center">未绑定</td>-->
<!--                    {{# } }}-->
                </tr>
                {{#  }); }}
            </script>
            </tbody>
        </table>
    </div>
</div>
<script type="text/javascript" src="../sys/jquery.js"></script>
<script type="text/javascript" src="../sys/arg.js"></script>
<script type="text/javascript" src="../layui-btkj/layui.js"></script>
<script type="text/javascript" src="../js/w_joindetail.js"></script>
</body>
</html>
