/**
 * 上报管理
 */
layui.config({
    base: './js/'
}).extend({ //设定模块别名
    promise: '../../plugin/promise/promise'
});
var templete = {
    //按次上报模板
    secondTem: '<tr data-id="{{id}}" data-rate="{{rate}}" data-id="{{id}}" data-rate="{{rate}}">\
                        <td style="color: #5CB2E8;text-decoration: underline;"><a class="a_detail">{{reportname}}</a></td>\
                        <td>{{reporttypename}}</td>\
                        <td><i class="red">{{reportTablecount}}</i>个</td>\
                        <td>{{reportStatus}}</td>\
                        <td style="{{endTimeStyle}}">{{endtime}}</td>\
                        <td>{{isSaveed}}</td>\
                        <td>{{sbStatus}}</td>\
                        <td style="{{shStatusstyle}}" class="{{btnviewcheck}}">{{shStatus}}</td>\
                        <td class="report-num" data-isadd="{{isAdd}}" data-shstate="{{shStatus}}" data-endtime="{{endtime}}">\
                            <a style="{{AddDisplay}}" class="curr-tab btn-add">填表</a>\
                            <a style="{{viewDisplay}}" class="curr-tab btn-edit">{{tianbaoname}}</a>\
                            <a style="{{SbDisplay}}" class="curr-tab btn-sb">上报</a>\
                            <div style="{{SbViewDisplay}}"><a class="curr-tab btn-cksb">{{viewsbname}}</a><label style="{{redDisplay}}position:relative;right:0px;top:0;" class="redsbg">1</label></div>\
                        </td>\
                    </tr>',
    //按月模板
    monthTem: '<tr data-id="{{id}}" data-rate="{{rate}}" data-id="{{id}}" data-rate="{{rate}}">\
                        <td rowspan="{{rowspan}}" style="color: #5CB2E8;text-decoration: underline;"><a class="a_detail">{{reportname}}</a></td>\
                        <td rowspan="{{rowspan}}">{{reporttypename}}</td>\
                        <td rowspan="{{rowspan}}"><i class="red">{{reportTablecount}}</i>个</td>\
                        <td>{{month}}</td>\
                        <td>{{reportStatus}}</td>\
                        <td style="{{endTimeStyle}}">{{monthdate}}</td>\
                        <td>{{isSaveed}}</td>\
                        <td>{{sbStatus}}</td>\
                        <td style="{{shStatusstyle}}" class="{{btnviewcheck}}">{{shStatus}}</td>\
                        <td class="report-num" data-month="{{month}}" data-year="{{year}}" data-quarter="{{quarter}}" data-isadd="{{isAdd}}" data-shstate="{{shStatus}}" data-endtime="{{endtime}}">\
                            <a style="{{AddDisplay}}" class="curr-tab btn-add">填表</a>\
                            <a style="{{viewDisplay}}" class="curr-tab btn-edit">{{tianbaoname}}</a>\
                            <a style="{{SbDisplay}}" class="curr-tab btn-sb">上报</a>\
                            <div style="{{SbViewDisplay}}"><a class="curr-tab btn-cksb">{{viewsbname}}</a><label style="{{redDisplay}}position:relative;right:0px;top:0;" class="redsbg">1</label></div>\
                        </td>\
                    </tr>',
    //单个月份模板
    monthItemTem: '<tr data-id="{{id}}" data-rate="{{rate}}" data-id="{{id}}" data-rate="{{rate}}">\
                        <td>{{month}}</td>\
                        <td>{{reportStatus}}</td>\
                        <td style="{{endTimeStyle}}">{{monthdate}}</td>\
                        <td>{{isSaveed}}</td>\
                        <td>{{sbStatus}}</td>\
                        <td style="{{shStatusstyle}}" class="{{btnviewcheck}}">{{shStatus}}</td>\
                        <td class="report-num" data-month="{{month}}" data-year="{{year}}" data-isadd="{{isAdd}}" data-shstate="{{shStatus}}" data-endtime="{{endtime}}">\
                            <a style="{{AddDisplay}}" class="curr-tab btn-add">填表</a>\
                            <a style="{{viewDisplay}}" class="curr-tab btn-edit">{{tianbaoname}}</a>\
                            <a style="{{SbDisplay}}" class="curr-tab btn-sb">上报</a>\
                            <div style="{{SbViewDisplay}}"><a class="curr-tab btn-cksb">{{viewsbname}}</a><label style="{{redDisplay}}position:relative;right:0px;top:0;" class="redsbg">1</label></div>\
                        </td>\
                    </tr>'
},
    /**
     * 替换模板数据 模板与数据中的变量名完全一致
     * @param template {String} 模板字符串
     * @param model {Object} 模板数据
     * @returns {*} {strHtml} 数据替换后的模板字符串
     */
    renderTemp = function (template, model) {
        var reg = /\{\{\w+\}\}/g;
        template = template.replace(reg, function (regStr) {
            var reg2 = /\{\{(\w+)\}\}/g,
                key = reg2.exec(regStr)[1];
            return model[key];
        });
        return template;
    },
    objdata = {
        curtime: $.getparent().objdata.curmoment.format("YYYY-MM-DD"),
        reportdata: {} //所有数据
        , monthTypeName: {
            "1": "月前",
            "2": "月末"
        }
        , quarterTypeName: {
            "1": "季前",
            "2": "季末"
        },
        index: 0 //当前选择列表
    },
    isload = [0, 0, 0, 0],
    curReportList,//当前列表对象
    objReportSave,//幼儿园上报子表
    init = { //初始化单条数据
        initCount: function () {
            var objson = {
                yeyid: $.getparent().objdata.yeyinfo.id,
                areacode: $.getparent().objdata.objmy.dataareacode
            };
            new Promise(function (resolve, reject) {
                $.sm(function (re, err) {
                    if (!err) {
                        resolve(re);
                    } else {
                        $.getparent().layer.msg("请求失败！");
                    }
                }, ["fuyou.post", "report.count", JSON.stringify(objson)]);
            }).then(function (data) {
                $(".sp-count,.sp-badge").hide();
                if (data && data[0]) {
                    var countlist = $(".sp-count"),
                        badgeList = $(".sp-badge");
                    if (data[0].count1) { //当前上报数
                        countlist.eq(0).html("(" + data[0].count1 + ")").show();
                    }
                    if (data[0].count2) { //历史上报数
                        countlist.eq(1).html("(" + data[0].count2 + ")").show();
                    }
                    if (data[0].count5) { //当前未通过
                        badgeList.eq(0).html((data[0].count5 > 99 ? "99+" : data[0].count5)).show();
                    }
                    if (data[0].count6) { //历史未通过
                        badgeList.eq(1).html((data[0].count6 > 99 ? "99+" : data[0].count6)).show();
                    }
                }
            });
        },
        //reporttype,reporttypename,reportcodes,reportnames,rate,endtype,enddays,endyear,endmonths,reportname,isend,endtime,reportcount,classification,yeytype,startstate,classificationname,yeytypename,yeycount
        /**
         *按次初始化
         */
        secondInit: function (item) {
            var objyey = {};
            if (objReportSave[item.id] && objReportSave[item.id].second) {
                objyey = objReportSave[item.id].second;
            }
            item.endlastdays = DateDiff(item.endtime, objdata.curtime);// 剩余天数
            item.ReportCount = parseInt(objyey.total || 0); //上报数
            item.unReviewCount = (objyey.total ? (objyey.total - objyey.unpass - objyey.pass) : 0);
            item.unPassCount = (objyey.unpass || 0);
            item.sbStatus = (item.ReportCount > 0 ? "已上报" : "未上报");
            item.sbClass = (item.ReportCount > 0 ? "blue-bg" : "pred-bg");
            //判断按钮显隐
            item.AddDisplay = "display:none;";//填表
            item.viewDisplay = "display:none;";//查看已填表格
            item.isSaveed = "否";//是否完善
            item.SbDisplay = "display:none;";//上报
            item.SbViewDisplay = "display:none;";//查看上报
            item.isAdd = item.endlastdays >= 0 ? 1 : 0;//是否添加状态
            item.endDaysStatus = item.endlastdays >= 0 ? "" : "display:none;";
            if (objdata.index == 0) {
                if (objyey && objyey.localsave == 1) {
                    item.viewDisplay = "";
                    item.isSaveed = "是";
                } else {
                    item.AddDisplay = "";
                }
                if (item.ReportCount > 0) {
                    item.SbViewDisplay = "display:inline;";
                } else {
                    item.SbDisplay = "";
                }
            } else if (objdata.index == 1) {
                if (objyey && objyey.localsave == 1) {
                    item.viewDisplay = "";
                }
                if (item.ReportCount > 0) {
                    item.SbViewDisplay = "display:inline;";
                }
            }
            item.shStatus = item.ReportCount > 0 ? (item.unReviewCount > 0 ? "未审核" : item.unPassCount > 0 ? "不通过" : "已通过") : "";
            item.tianbaoname = "查看已填表格";
            item.viewsbname = "查看上报";
            item.btnviewcheck = "";
            if (objdata.index == 0 && item.shStatus == "不通过") {
                item.tianbaoname = "重新编辑";
                item.viewsbname = "上报";
                item.btnviewcheck = "btnviewcheck";
            }
            item.shStatusstyle = item.ReportCount > 0 ? (item.unReviewCount > 0 ? "color:#00AC9F" : item.unPassCount > 0 ? "color:#FE3939;cursor: pointer;" : "color:#3aa6ff") : "";
            item.shClass = item.ReportCount > 0 ? (item.unReviewCount > 0 ? "pink-bg" : item.unPassCount > 0 ? "lightpink-bg" : "green-bg") : "";
            item.redDisplay = item.unPassCount > 0 ? "display:inline-block;" : "display:none;";
            //上报活动状态
            var status = $.getparent().objdata.moment(objdata.curtime).isBefore(item.starttime) ? "未开始" : $.getparent().objdata.moment(item.endtime).isBefore(objdata.curtime) ? "已结束" : "进行中";
            item.reportStatus = init.statusName(status);
            objdata.reportdata[item.id] = item;
            return renderTemp(templete.secondTem, item);
        },
        statusName: function (status) {
            if (objdata.index == 0) {
            } else if (objdata.index == 1) {
                status = "已结束";
            }
            return status;
        },
        /**
         * 按月初始化
         * @param item
         * @param num
         * @returns {*}
         */
        monthInit: function (item, num) {
            item.endtypename = objdata.monthTypeName[item.endtype];
            item.startyear = item.starttime.substring(0, 4);
            objdata.reportdata[item.id] = item;
            item.ordernumber = num < 10 ? "0" + num : num;
            return init.monthReportByYear(item.id);
        },
        /**
         * 按季初始化
         * @param item
         * @param num
         * @returns {*}
         */
        quarterInit: function (item, num) {
            item.endtypename = objdata.quarterTypeName[item.endtype];
            item.startyear = item.starttime.substring(0, 4);
            objdata.reportdata[item.id] = item;
            item.ordernumber = num < 10 ? "0" + num : num;
            return init.quarterReportByYear(item.id);
        },
        /**
         * 处理按月份上报信息
         * @param id
         * @param year
         * @returns {string}
         */
        monthReportByYear: function (id, year) {
            var item = objdata.reportdata[id],
                startyear = item.startyear,
                startmonth = item.startmonths,
                endyear = parseInt(item.endtime.substring(0, 4)),
                endmonth = parseInt(item.endtime.substring(5, 7)),
                intstart = parseInt(startyear + "" + (startmonth.toString().length == 1 ? "0" + startmonth : startmonth)),
                intend = parseInt(endyear + "" + (endmonth.toString().length == 1 ? "0" + endmonth : endmonth)),
                intcur = parseInt(objdata.curtime.substring(0, 4) + objdata.curtime.substring(5, 7)),
                arrMonthHtml = [],
                mlen = ((endyear * 12 + parseInt(endmonth))) - (startyear * 12 + parseInt(startmonth));
            var year = startyear,
                month = startmonth;
            for (var i = 0; i <= mlen; i++) {
                if (i == 0) {
                } else {
                    month++;
                    if (month > 12) {//月份增加
                        year++;
                        month = 1;
                    }
                }
                var nowmonth = parseInt(year + "" + (month < 10 ? "0" + month : month)),
                    tem = "", //模板
                    obj = {
                        year: year,
                        month: month,
                        monthStatus: "",
                        bgcolor: "gray-bg",
                        reportStatus: "进行中",
                        endDaysStatus: "display:none;"
                    },
                    dd = parseInt(objdata.curtime.substring(8, 11)),
                    objyey = {},
                    isover = true,
                    enddays = item.enddays;
                if (objReportSave[item.id] && objReportSave[item.id][year + "_" + month]) {
                    objyey = objReportSave[item.id][year + "_" + month];
                }
                if (i == 0) {
                    obj.rowspan = mlen + 1;
                    tem = templete.monthTem;
                } else {
                    tem = templete.monthItemTem;
                }
                obj.ReportCount = parseInt(objyey.total || 0); //上报数
                obj.unReviewCount = (objyey.total ? (objyey.total - objyey.unpass - objyey.pass) : 0);
                obj.unPassCount = (objyey.unpass || 0);
                obj.sbStatus = (obj.ReportCount > 0 ? "已上报" : "未上报");
                obj.sbClass = (obj.ReportCount > 0 ? "blue-bg" : "pred-bg");
                obj.shStatus = obj.ReportCount > 0 ? (obj.unReviewCount > 0 ? "未审核" : obj.unPassCount > 0 ? "不通过" : "已通过") : "";
                obj.tianbaoname = "查看已填表格";
                obj.viewsbname = "查看上报";
                obj.btnviewcheck = "";
                if (objdata.index == 0 && obj.shStatus == "不通过") {
                    obj.tianbaoname = "重新编辑";
                    obj.viewsbname = "上报";
                    obj.btnviewcheck = "btnviewcheck";
                }
                obj.shStatusstyle = obj.ReportCount > 0 ? (obj.unReviewCount > 0 ? "color:#00AC9F" : obj.unPassCount > 0 ? "color:#FE3939;cursor: pointer;" : "color:#3aa6ff") : "";
                obj.shClass = obj.ReportCount > 0 ? (obj.unReviewCount > 0 ? "pink-bg" : obj.unPassCount > 0 ? "lightpink-bg" : "green-bg") : "";
                obj.endtypename = objdata.monthTypeName[item.endtype];
                obj.redDisplay = obj.unPassCount > 0 ? "" : "display:none;";
                obj.enddays = item.enddays;
                obj.monthdate = "";
                //处理截止日期
                var mdays = getDaysInOneMonth(year, month);//选择月天数
                if (enddays) {
                    enddays = item.endtype == "2" ? mdays - enddays : enddays;
                    obj.monthdate = year + "-" + (month < 10 ? "0" + month : month) + "-" + enddays + "";
                } else {
                    obj.monthdate = year + "-" + (month < 10 ? "0" + month : month) + "-" + mdays + "";
                }
                if (nowmonth < intstart || (item.isend == "1" && nowmonth > intend)) {
                    //tem = templete.monthNoneTem;
                } else {
                    if (nowmonth <= intcur) {//小于当前时间处理
                        if (objdata.curtime.substring(0, 4) == year) {
                            if (nowmonth == intcur) {//当前月处理
                                obj.bgcolor = "orange-bg";
                                //判断当前是否已结束
                                if (enddays && enddays < dd) {
                                    obj.reportStatus = "已结束";
                                } else {
                                    obj.endDaysStatus = "";
                                    obj.endlastdays = (enddays || mdays) - dd;//剩余天数
                                    isover = false;
                                }
                            } else {
                                obj.reportStatus = "已结束";
                            }
                        } else {
                            obj.reportStatus = "已结束";
                        }
                    } else {
                        if (nowmonth > intend) {
                            // tem = templete.monthNoneTem;
                        } else {
                            obj.reportStatus = "未开始";
                        }
                    }
                }
                //判断按钮显隐
                obj.AddDisplay = "display:none;";
                obj.viewDisplay = "display:none;";
                obj.SbDisplay = "display:none;";
                obj.SbViewDisplay = "display:none;";
                item.isSaveed = "否";//是否完善
                obj.isAdd = !isover ? 1 : 0;
                if (objdata.index == 0) {
                    if (objyey && objyey.localsave == 1) {
                        obj.viewDisplay = "";
                        item.isSaveed = "是";//是否完善
                    } else {
                        if (!isover) obj.AddDisplay = "";
                    }
                    if (obj.ReportCount > 0) {
                        obj.SbViewDisplay = "";
                    } else {
                        if (!isover) obj.SbDisplay = "";
                    }
                } else if (objdata.index == 1) {
                    if (objyey && objyey.localsave == 1) {
                        obj.viewDisplay = "";
                    }
                    if (obj.ReportCount > 0) {
                        obj.SbViewDisplay = "";
                    }
                }
                obj.endTimeStyle = obj.endlastdays <= 3 ? "color: #FF4F4F;" : "";
                obj.reportStatus = init.statusName(obj.reportStatus);
                obj = $.extend(obj, item);
                arrMonthHtml.push(renderTemp(tem, obj));
            }
            return arrMonthHtml.join('');
        },
        /**
         * 某年某季度显示
         * @param id
         * @param year
         * @returns {string}
         */
        quarterReportByYear: function (id, year) {
            var item = objdata.reportdata[id],
                startyear = item.startyear,
                startmonth = item.startmonths,
                endyear = parseInt(item.endyear),
                endmonth = parseInt(item.endmonths),
                intstart = parseInt(startyear + "" + (startmonth)),
                intend = parseInt(endyear + "" + endmonth),
                month = parseInt(objdata.curtime.substring(5, 7)),
                intcur = parseInt(objdata.curtime.substring(0, 4) + ((month > 0 && month < 4 ? 1 : month > 3 && month < 7 ? 2 : month > 6 && month < 10 ? 3 : 4))),
                arrMonthHtml = [],
                mlen = ((endyear * 4 + parseInt(endmonth))) - (startyear * 4 + parseInt(startmonth));
            var year = startyear,
                quarter = startmonth;
            //for (var i = 0; i <= mlen; i++) {
            //    if (i == 0) {
            //    } else {
            //        quarter++;
            //        if (quarter > 4) {//季度增加
            //            year++;
            //            quarter = 1;
            //        }
            //    }
            var nowmonth = parseInt(year + "" + quarter),
                tem = "", //模板
                obj = {
                    year: year,
                    quarter: quarter,
                    month: quarter,
                    quarterName: quarter == 1 ? "第一季度" : quarter == 2 ? "第二季度" : quarter == 3 ? "第三季度" : "第四季度",
                    reportStatus: "进行中",
                    bgcolor: "gray-bg",
                    endDaysStatus: "display:none;"
                },
                dd = parseInt(objdata.curtime.substring(8, 11)),
                objyey = {},
                enddays = item.enddays,
                isover = false;
            if (objReportSave[item.id] && objReportSave[item.id][year + "_" + quarter]) {
                objyey = objReportSave[item.id][year + "_" + quarter];
            }
            //if (i == 0) {
            //    obj.rowspan = mlen + 1;
            tem = templete.monthTem;
            //} else {
            //    tem = templete.monthItemTem;
            //}
            obj.ReportCount = parseInt(objyey.total || 0); //上报数
            obj.unReviewCount = (objyey.total ? (objyey.total - objyey.unpass - objyey.pass) : 0);
            obj.unPassCount = (objyey.unpass || 0);
            obj.sbStatus = (obj.ReportCount > 0 ? "已上报" : "未上报");
            obj.sbClass = (obj.ReportCount > 0 ? "blue-bg" : "pred-bg");
            obj.shStatus = obj.ReportCount > 0 ? (obj.unReviewCount > 0 ? "未审核" : obj.unPassCount > 0 ? '不通过' : '已通过') : "";
            obj.tianbaoname = "查看已填表格";
            obj.viewsbname = "查看上报";
            obj.btnviewcheck = "";
            if (objdata.index == 0 && obj.shStatus == "不通过") {
                obj.tianbaoname = "重新编辑";
                obj.viewsbname = "上报";
                obj.btnviewcheck = "btnviewcheck";
            }
            obj.shStatusstyle = obj.ReportCount > 0 ? (obj.unReviewCount > 0 ? "未审核" : obj.unPassCount > 0 ? "color:#FE3939;cursor: pointer;" : "color:#3aa6ff") : "";
            obj.shClass = obj.ReportCount > 0 ? (obj.unReviewCount > 0 ? "pink-bg" : obj.unPassCount > 0 ? "lightpink-bg" : "green-bg") : "";
            obj.endtypename = objdata.quarterTypeName[item.endtype];
            obj.redDisplay = obj.unPassCount > 0 ? "" : "display:none;";
            obj.enddays = item.enddays;
            obj.monthdate = item.endtime;
            ////处理截止日期
            //var quatertdays = getDaysInOneQuarter(year, quarter), //当前季度天数
            //    endjdstartdate = year + "-" + (quarter == 1 ? "01" : quarter == 2 ? "04" : quarter == 3 ? "07" : "10") + "-01";
            //if (enddays) {
            //    enddays = item.endtype == "2" ? quatertdays - enddays : enddays;
            //    obj.monthdate = getnextday(endjdstartdate, enddays);
            //} else {
            //    obj.monthdate = getnextday(endjdstartdate, quatertdays);
            //}
            if (nowmonth < intstart || (item.isend == "1" && jQuery.getparent().DateDiff("d", jQuery.getparent().getDateByStr(objdata.curtime), jQuery.getparent().getDateByStr(item.endtime)) >= 0)) {
                // tem = templete.quarterNoneTem;
            } else {
                if (nowmonth <= intcur) {//当前年判断
                    if (objdata.curtime.substring(0, 4) == year) {
                        if (nowmonth == intcur) {//当前月处理
                            obj.bgcolor = "orange-bg";
                            //判断当前是否已结束
                            var lsdays = jQuery.getparent().DateDiff("d", jQuery.getparent().getDateByStr(objdata.curtime), jQuery.getparent().getDateByStr(item.endtime));
                            if (enddays && enddays < lsdays) {
                                obj.reportStatus = "已结束";
                                isover = true;
                            } else {
                                obj.endDaysStatus = "";
                                obj.endlastdays = (enddays || quatertdays) - lsdays;//剩余天数

                            }
                        } else {
                            obj.reportStatus = "已结束";
                        }
                    } else {
                        obj.reportStatus = "已结束";
                    }
                    // tem = templete.quarterItemTem;
                } else {
                    if (nowmonth > intend) {
                        // tem = templete.quarterNoneTem;
                    } else {
                        obj.reportStatus = "未开始";
                        // tem = templete.quarterUnStartTem;
                    }
                }
            }
            //判断按钮显隐
            obj.AddDisplay = "display:none;";
            obj.viewDisplay = "display:none;";
            item.isSaveed = "否";//是否完善
            obj.SbDisplay = "display:none;";
            obj.SbViewDisplay = "display:none;";
            obj.isAdd = !isover ? 1 : 0;
            if (objdata.index == 0) {
                if (objyey && objyey.localsave == 1) {
                    obj.viewDisplay = "";
                    item.isSaveed = "是";//是否完善
                } else {
                    if (!isover) obj.AddDisplay = "";
                }
                if (obj.ReportCount > 0) {
                    obj.SbViewDisplay = "";
                } else {
                    if (!isover) obj.SbDisplay = "";
                }
            } else if (objdata.index == 1) {
                if (objyey && objyey.localsave == 1) {
                    obj.viewDisplay = "";
                }
                if (obj.ReportCount > 0) {
                    obj.SbViewDisplay = "";
                }
            }
            obj.endTimeStyle = obj.endlastdays <= 3 ? "color: #FF4F4F;" : "";
            obj.reportStatus = init.statusName(obj.reportStatus);
            obj = $.extend(obj, item);
            arrMonthHtml.push(renderTemp(tem, obj));
            //}
            return arrMonthHtml.join('');
        }
    };
//入口
layui.use(['promise', 'form', "laydate"], function () {
    $(window).on('resize', function () {
        var height = $(window).height() - $(".layui-tab").height();
        $(".report-list").height(height);
        $(".report-list").eq(0).height(height - 55);
        $(".report-list").eq(1).height(height - 55);
    }).trigger("resize");
    var arrhtml = ['<option value="">请选择</option>'];
    if (jQuery.getparent().objdata.yeyinfo.yeytype == 6) {
        $("#labjdmonthname").html("年份/季度");
        arrhtml.push('<option value="1_1">上半年</option>');
        arrhtml.push('<option value="1_2">下半年</option>');
        arrhtml.push('<option value="3_1">第一季度</option>');
        arrhtml.push('<option value="3_2">第二季度</option>');
        arrhtml.push('<option value="3_3">第三季度</option>');
        arrhtml.push('<option value="3_4">第四季度</option>');
    } else {
        if (jQuery.getparent().objdata.yeyinfo.yeytype == 3) {
            $("#labjdmonthname").html("上报季度");
            arrhtml.push('<option value="1">第一季度</option>');
            arrhtml.push('<option value="2">第二季度</option>');
            arrhtml.push('<option value="3">第三季度</option>');
            arrhtml.push('<option value="4">第四季度</option>');
        } else {
            $("#labjdmonthname").html("上报年份");
            arrhtml.push('<option value="1">上半年</option>');
            arrhtml.push('<option value="2">下半年</option>');
        }
    }
    $("#seljdmonth").html(arrhtml.join(''));
    layui.form.render();
    //注册事件
    if ($.getparent().objdata.objmy.dataareacode) {
        $(".sec-noarea").hide();
        initEvent();
        initData(0);
    } else {
        $(".sec-noarea").show();
        $(".sec-noarea").find(".cl2").off('click').on('click', function () {
            jQuery.getparent().areajoinSet(function () {
                location.reload();
            });
        });
    }
    var acode = parent.objdata.my.areacode;
    var objwhere = {};
    if (jQuery.getparent().objdata.yeyinfo.yeytype == 6) {
        objwhere.sbtype_in = [1, 3];
    } else {
        objwhere.sbtype = [jQuery.getparent().objdata.yeyinfo.yeytype];
    }
    $.sm(function (re, err) {
        if (!err) {
            var arrhtml = ['<option value="">请选择</option>'];
            for (var i = 0; i < re.length; i++) {
                arrhtml.push('<option value="' + re[i].reportset_code + '">' + re[i].reportset_name + '</option>');
            }
            $("#selreporttype").html(arrhtml.join(''));
            layui.form.render();
        } else {
            parent.layer.msg("请求失败！");
        }
    }, ["reportadd.getreportset", $.msgwhere({ areacode: [acode, acode.substring(0, 4) + "00", acode.substring(0, 2) + "0000"] }), $.msgwhere(objwhere)], "fyyypt", "", { route: 'fyyypt' });
    layui.form.render();
});
/**
 * 初始化列表数据
 * @param index
 */
function initData(index) {
    if (index != 2 && index != 3) {
        init.initCount();
    }
    $(".report-none").hide();
    $(".report-list").hide();
    if (isload[index] == 1) {
        $(".report-list").eq(index).show();
        return;
    }
    curReportList = $(".report-list").eq(index);
    var objwhere = {};
    var rate = $("select[name='type']").val();
    var reportname = $("input[name='name']").val();
    var endtime1 = $("#txtyear").val();
    var endtime2 = $("#txtyear2").val();
    var reporttype = $("#selreporttype").val();
    var startmonths = $("#seljdmonth").val();
    var startyear = $("#txtyear3").val();

    switch (index) {
        case 0: //正在上报
            $("#btnrefresh").show();
            $("#btnaddfile").hide();
            objwhere.nowsb = [];
            break;
        case 1: //历史上报
            $("#btnrefresh").show();
            $("#btnaddfile").hide();
            objwhere.hissb = [];
            break;
        // case 2: //基础信息上报
        //     $("#btnrefresh").hide();
        //     $("#btnaddfile").hide();
        //     $(".report-list").eq(index).show();
        //     df.initEvent();
        //     return;
        //     break;
        // case 3: //文件上报
        //     $("#btnrefresh").hide();
        //     $("#btnaddfile").show();
        //     $(".report-list").eq(index).show();
        //     sf.initEvent();
        //     return;
        //     break;
    }
    $.getparent().layer.load();
    var objson = {
        yeyid: $.getparent().objdata.yeyinfo.id,
        areacode: $.getparent().objdata.objmy.dataareacode,
        rate: rate,
        reportname: reportname,
        reporttype: reporttype,
        endtime1: endtime1,
        endtime2: endtime2,
        startmonths: startmonths,
        startyear: startyear,
    };
    if (startmonths && startmonths.indexOf('_') > 0) {
        var arrstartmonths = startmonths.split('_');
        objson.startmonths = arrstartmonths[1];
        objson.yeytype = arrstartmonths[0];
    } else if (jQuery.getparent().objdata.yeyinfo.yeytype == 6) {
        objson.yeytype = $("#selyeytype").val();
        $("#divyeytype").css("display", " inline-block");
    }
    function getAreaWhere(areacode, fix, arrareacode) {
        if (areacode.length > 6) {
            areacode = areacode.substring(0, areacode.length - 2);
            arrareacode.push(areacode);
            getAreaWhere(areacode, fix, arrareacode);
        } else {
            if (areacode.substring(4, 6) != "00") {
                areacode = areacode.substring(0, 4) + "00";
                arrareacode.push(areacode);
            }
        }
        return arrareacode;
    }
    new Promise(function (resolve, reject) {
        var arrareacode = getAreaWhere($.getparent().objdata.objmy.dataareacode, "", [$.getparent().objdata.objmy.dataareacode]);
        $.sm(function (re, err) {
            if (!err) {
                resolve(re);
            } else {
                $.getparent().layer.closeAll("loading");
                $.getparent().layer.msg("请求失败, 请稍后再试！");
            }
        }, ['fuyou.post', 'reportlist.data', JSON.stringify(objson), $.msgwhere(objwhere)]);
    }).then(function (data) {
        var data1 = [{'re': data}];
        return new Promise(function (resolve, reject) {
            $.sm(function (re, err) {
                if (!err) {
                    data1.push(re);
                    resolve(data1);
                } else {
                    $.getparent().layer.closeAll("loading");
                    $.getparent().layer.msg("请求失败, 请稍后再试！");
                }
            }, ["fuyoureportlist.getLocal", $.msgwhere({ yeyid: [jQuery.getparent().objdata.my.yeyid] })]);
        });

    }).then(function (data) {
        if (data.length == 0 || (data[0] && data[0].re && data[0].re.length == 0)) {//无数据显示
            curReportList.hide();
            $(".report-none").show();
            $.getparent().layer.closeAll("loading");
            return;
        }
        $(".report-list").eq(index).show();
        initHtml(data);
        $.getparent().layer.closeAll("loading");

    });
}
/**
 * 处理页面展示
 */
function initHtml(re) {
    objReportSave = {}; //存储幼儿园上报情况
    //处理本地是否存储
    if (re && re[1] && re[1][0]) {//判断本地是否保存过
        //tb_report_id,yeyid,yyyymm,jd,rate,shstate
        for (var i = 0; i < re[1].length; i++) {
            var item = re[1][i],
                key = "second";
            if (!objReportSave[item.tb_report_id]) {
                objReportSave[item.tb_report_id] = {};
            }
            if (item.rate != "second") {
                key = item.yyyymm + "_" + parseInt(item.jd);
            }
            if (!objReportSave[item.tb_report_id][key]) {
                objReportSave[item.tb_report_id][key] = {
                    localsave: 1,
                    total: 0,
                    pass: 0,
                    unpass: 0,
                    shstate: 0
                };
            }
        }
    }
    if (re && re[0] && re[0].re && re[0].re[1]) {
        //tb_report_id,yeyid,yyyymm,jd,rate,shstate
        for (var i = 0; i < re[0].re[1].length; i++) {
            var item = re[0].re[1][i],
                key = "second";
            if (!objReportSave[item.tb_report_id]) {
                objReportSave[item.tb_report_id] = {};
            }
            if (item.rate != "second") {
                key = item.yyyymm + "_" + parseInt(item.jd);
            }
            if (!objReportSave[item.tb_report_id][key]) {
                objReportSave[item.tb_report_id][key] = {
                    localsave: 0,
                    total: 0,
                    pass: 0,
                    unpass: 0,
                    shstate: item.shstate
                };
            }
            objReportSave[item.tb_report_id][key].shstate = item.shstate;
            objReportSave[item.tb_report_id][key].total++;
            if (item.shstate == "1") {//未通过
                objReportSave[item.tb_report_id][key].unpass++;
            } else if (item.shstate == "2") {//通过
                objReportSave[item.tb_report_id][key].pass++;
            }
        }
    }
    //上报主表
    if (re && re[0] && re[0].re && re[0].re[0]) {
        var arrSecond = [], //按次html
            arrMonth = [],  //按月html
            arrQuarter = [];//按季html
        for (var i = 0; i < re[0].re[0].length; i++) {
            var item = re[0].re[0][i];
            item.reportTablecount = item.reportcodes.split(',').length;
            if (item.rate === "second") {
                arrSecond.push(init.secondInit(item, arrSecond.length + 1));
            } else if (item.rate === "month") {
                arrMonth.push(init.monthInit(item, arrMonth.length + 1));
            } else if (item.rate === "quarter") {
                arrQuarter.push(init.quarterInit(item, arrQuarter.length + 1));
            }
        }
        //页面拼接显示
        if (arrSecond.length) {
            curReportList.find(".sec_second").show();
            curReportList.find(".sec_second").find(".sp_num").html(arrSecond.length);
            curReportList.find(".sec_second").find(".tb_body").html(arrSecond.join(''));
        } else {
            curReportList.find(".sec_second").hide();
        }
        if (arrMonth.length) {
            curReportList.find(".sec_month").show();
            curReportList.find(".sec_month").find(".sp_num").html(arrMonth.length);
            curReportList.find(".sec_month").find(".tb_body").html(arrMonth.join(''));
        } else {
            curReportList.find(".sec_month").hide();
        }
        if (arrQuarter.length) {
            curReportList.find(".sec_quarter").show();
            curReportList.find(".sec_quarter").find(".sp_num").html(arrQuarter.length);
            curReportList.find(".sec_quarter").find(".tb_body").html(arrQuarter.join(''));
        } else {
            curReportList.find(".sec_quarter").hide();
        }
    }
}
/**
 * 注册事件
 */
function initEvent() {
    //刷新事件
    $("#btnrefresh,.btn-search").off("click").on('click', function () {
        isload[objdata.index] = 0;
        initData(objdata.index);
    });
    //添加事件
    $("#btnaddfile").off("click").on('click', function () {//上传文件
        if (objdata.index == 3) {//文件上传
            sf.initaddfile();
        }
    });
    //切换tab事件
    $(".layui-tab-title").children('li').off('click').on('click', function () {
        var $this = $(this);
        objdata.index = $this.index();
        if (!$this.hasClass("layui-this")) {
            $this.addClass("layui-this").siblings().removeClass("layui-this");
            $("select[name='type']").val("");
            $("input[name='name']").val("");
            if (objdata.index != 2 && objdata.index != 3) {
                $(".div-report").show();
            } else {
                $(".div-report").hide();
            }
            layui.form.render();
            initData($this.index());
        }
    });
    //处理前一年、后一年
    $("body").off('click', '.left-arr').on('click', '.left-arr', function () {
        var _this = $(this),
            _section = _this.parents(".report-month-quarter"),
            data_id = _section.attr("data-id"),
            data_rate = _section.attr("data-rate"),
            data_year = _section.find(".iyear").html(),
            item = objdata.reportdata[data_id];
        if (data_year <= item.startyear) {
            return $.getparent().layer.msg("已切换到开始时间", { icon: 5 });
        }
        if (data_rate === "month") {
            _section.find(".div_months").html(init.monthReportByYear(data_id, parseInt(data_year) - 1));
        } else if (data_rate === "quarter") {
            _section.find(".div_quarters").html(init.quarterReportByYear(data_id, parseInt(data_year) - 1));
        }
        _section.find(".iyear").html(parseInt(data_year) - 1);
    }).off('click', '.right-arr').on('click', '.right-arr', function () {
        var _this = $(this),
            _section = _this.parents(".report-month-quarter"),
            data_id = _section.attr("data-id"),
            data_rate = _section.attr("data-rate"),
            data_year = _section.find(".iyear").html(),
            item = objdata.reportdata[data_id];
        if (data_year >= item.endyear) {
            return $.getparent().layer.msg("已切换到结束时间", { icon: 5 });
        }
        if (data_rate === "month") {
            _section.find(".div_months").html(init.monthReportByYear(data_id, parseInt(data_year) + 1));
        } else if (data_rate === "quarter") {
            _section.find(".div_quarters").html(init.quarterReportByYear(data_id, parseInt(data_year) + 1));
        }
        _section.find(".iyear").html(parseInt(data_year) + 1);
    }).off('click', '.a_detail').on('click', '.a_detail', function () {//查看详情页
        var _this = $(this),
            data_id = _this.parents('[data-id]').attr("data-id"),
            reportname = _this.html();
        $.getparent().objdata.reportdetail = $.getparent().layer.open({
            type: 2,
            title: "查看【" + reportname + "】详情",
            area: ['850px', '600px'],
            content: 'tongsb/fuyou_reportdetail.html?v=' + Arg("v") + "&mid=" + Arg("mid") + (data_id ? "&id=" + data_id : "") + "&index=" + objdata.index
        });
    }).off('click', '.btn-zhedie').on('click', '.btn-zhedie', function () {//折叠
        var _this = $(this);
        if (_this.html() == "折叠月份") {
            _this.parents("[data-id]").find(".div_months").hide();
            _this.html('展开月份');
        } else if (_this.html() == "展开月份") {
            _this.parents("[data-id]").find(".div_months").show();
            _this.html('折叠月份');
        } else if (_this.html() == "折叠季度") {
            _this.parents("[data-id]").find(".div_quarters").hide();
            _this.html('展开季度');
        } else if (_this.html() == "展开季度") {
            _this.parents("[data-id]").find(".div_quarters").show();
            _this.html('折叠季度');
        }
    }).off('click', '.btn-add,.btn-edit').on('click', '.btn-add,.btn-edit', function () { //填表
        var _this = $(this),
            _div = _this.parents('[data-id]'),
            data_id = _div.attr("data-id"),
            data_rate = _div.attr("data-rate"),
            item = objdata.reportdata[data_id],
            objson = {},
            yyyymm = "",
            jd = "",
            isview = "1", //0添加， 1查看 2 编辑
            shstate = _this.parent().attr("data-shstate"),
            isadd = _this.parent().attr("data-isadd");
        if (data_rate == "month") {
            yyyymm = _this.parent().attr("data-year");
            jd = _this.parent().attr("data-month").replace("月", "");
        } else if (data_rate == "quarter") {
            yyyymm = _this.parent().attr("data-year");
            jd = _this.parent().attr("data-quarter");
        } else {
            yyyymm = item.startyear;
            jd = item.startmonths;
        }
        objson = {
            tb_report_id: data_id,
            tb_report_name: item.reportname,
            areacode: item.areacode,
            areaname: item.areaname
        };
        if (isadd == "1" && (!shstate || (objdata.index == 0 && shstate == "不通过"))) {
            if (_this.hasClass("btn-add")) {//添加
                isview = 0;
            } else {
                isview = 2;
            }
        }
        if (isview == "1") {
            $.getparent().objdata.chdcreport = $.getparent().layer.open({
                type: 2,
                title: "填写报表",
                area: ['100%', '100%'],
                btn: ["打印预览", "导出Excel", "关闭"],
                content: 'tongsb/fuyou_chdcReport.html?v=' + Arg("v") + "&mid=" + Arg("mid") + "&tb_report_id=" + data_id + "&endtime=" + item.endtime + "&rate=" + data_rate + "&yyyymm=" + yyyymm + "&jd=" + jd + "&reportcodes=" + item.reportcodes + "&reportnames=" + item.reportnames + "&tb_report_name=" + item.reportname + "&isview=" + isview,
                yes: function (index, layero) {
                    var zpage = layero.find("iframe")[0].contentWindow;
                    zpage.$(".btn-zhediereport").hide();
                    zpage.$(".btn_retrieve").hide();
                    zpage.$(".tab_parent_sec").css('border', '0');
                    zpage.$.print(zpage.$(".tab_parent_sec"));
                    zpage.$(".btn-zhediereport").show();
                    zpage.$(".btn_retrieve").show();
                    zpage.$(".tab_parent_sec").css('border', '1px solid #DFDFDF;');
                },
                btn2: function (index, layero) {
                    var w = layero.find('iframe')[0].contentWindow;
                    w.$('#btnexcel').trigger('click');
                    return false;
                }
            });
        } else {
            $.getparent().objdata.chdcreport = $.getparent().layer.open({
                type: 2,
                title: "填写报表",
                area: ['100%', '100%'],
                btn: ["保存", "打印预览", "关闭"],
                content: 'tongsb/fuyou_chdcReport.html?v=' + Arg("v") + "&mid=" + Arg("mid") + "&tb_report_id=" + data_id + "&endtime=" + item.endtime + "&rate=" + data_rate + "&yyyymm=" + yyyymm + "&jd=" + jd + "&reportcodes=" + item.reportcodes + "&reportnames=" + item.reportnames + "&tb_report_name=" + item.reportname + "&isview=" + isview,
                yes: function (index, layero) {
                    layero.find("iframe")[0].contentWindow.saveEvent(objson, function () {
                        $("#btnsearch").trigger("click");
                        $.getparent().layer.close(index);
                    });
                },
                btn2: function (index, layero) {
                    var zpage = layero.find("iframe")[0].contentWindow;
                    zpage.$(".btn-zhediereport").hide();
                    zpage.$(".btn_retrieve").hide();
                    zpage.$(".tab_parent_sec").css('border', '0');
                    zpage.$.print(zpage.$(".tab_parent_sec"));
                    zpage.$(".btn-zhediereport").show();
                    zpage.$(".btn_retrieve").show();
                    zpage.$(".tab_parent_sec").css('border', '1px solid #DFDFDF;');
                    return false;
                }
            });
        }
    }).off('click', '.btn-sb').on('click', '.btn-sb', function () { //上报
        sbEvent($(this), "report");
    }).off('click', '.btn-cksb').on('click', '.btn-cksb', function () { //查看上报
        var _this = $(this),
            _div = _this.parents('[data-id]'),
            data_id = _div.attr("data-id"),
            data_rate = _div.attr("data-rate"),
            item = objdata.reportdata[data_id],
            yyyymm = "",
            jd = "",
            isview = 1,
            _parent = _this.parent().parent(),
            shstate = _parent.attr("data-shstate"),
            enddate = _parent.attr("data-endtime"),
            isadd = _parent.attr("data-isadd");
        if (data_rate == "month") {
            yyyymm = _parent.attr("data-year");
            jd = _parent.attr("data-month");
        } else if (data_rate == "quarter") {
            yyyymm = _parent.attr("data-year");
            jd = _parent.attr("data-quarter");
        }
        if (isadd == "1" && (!shstate || (objdata.index == 0 && shstate == "不通过"))) {
            isview = 0;
        }
        $.getparent().objdata.reportinfo = $.getparent().layer.open({
            type: 2,
            title: "查看上报",
            area: ['75%', '600px'],
            btn: isview ? ["关闭"] : ["重新上报", "关闭"],
            content: 'tongsb/fuyou_reportInfo.html?v=' + Arg("v") + "&mid=" + Arg("mid") + "&tb_report_id=" + data_id + "&rate=" + data_rate + "&yyyymm=" + yyyymm + "&jd=" + jd + "&reportcodes=" + item.reportcodes + "&reportnames=" + item.reportnames + "&tb_report_name=" + item.reportname + "&isadd=" + isadd + "&shstate=" + shstate + "&reportname=" + item.reportname + "&reporttypename=" + item.reporttypename + "&endtime=" + enddate,
            yes: function (index, layero) {
                if (isview) {
                    $.getparent().layer.close(index);
                } else {
                    //上报事件
                    sbEvent(_this.parent(), "rereport", function () {
                        $.getparent().layer.close(index);
                    });
                }
            }, cancel: function (index, layero) {
                $.getparent().layer.close(index);
            }
        });
    }).off('click', '.btnviewcheck').on('click', '.btnviewcheck', function () { //查看不通过原因
        var _this = $(this),
            _div = _this.parents('[data-id]'),
            data_id = _div.attr("data-id"),
            data_rate = _div.attr("data-rate"),
            yyyymm = "",
            jd = "";
        if (data_rate == "month") {
            yyyymm = _parent.attr("data-year");
            jd = _parent.attr("data-month");
        } else if (data_rate == "quarter") {
            yyyymm = _parent.attr("data-year");
            jd = _parent.attr("data-quarter");
        }
        parent.layer.load();
        var objson = {
            yeyid: $.getparent().objdata.yeyinfo.id,
            areacode: $.getparent().objdata.objmy.dataareacode
        };
        var objwhere = {};
        objwhere.tb_report_id = [data_id];
        if (yyyymm) {
            objwhere.yyyymm = [yyyymm];
        }
        if (jd) {
            objwhere.jd = [jd];
        }
        $.sm(function (re, err) {
            if (!err) {
                var reason = "";
                for (var i = 0; i < re.length; i++) {
                    if (re[i].operationtype == "unpass") {
                        reason = re[i].recontent;
                    }
                }
                $.getparent().layer.alert(reason);
            } else {
                parent.layer.msg("请求失败, 请稍后再试！");
            }
            parent.layer.closeAll("loading");
        }, ["fuyou.post", "report.getlog", JSON.stringify(objson), $.msgwhere(objwhere)]);
    });
    layui.laydate.render({
        elem: '#txtyear'
        , type: 'year'
        // ,value: curyear
    });
    layui.laydate.render({
        elem: '#txtyear2'
        , type: 'year'
        // ,value: curyear
    });
    layui.laydate.render({
        elem: '#txtyear3'
        , type: 'year'
        // ,value: curyear
    });
}
/***
 * 上报事件
 * @param _this
 */
function sbEvent(_this, operationtype, cb) {
    var _div = _this.parents('[data-id]'),
        data_id = _div.attr("data-id"),
        data_rate = _div.attr("data-rate"),
        item = objdata.reportdata[data_id],
        isadd = _this.parent().attr("data-isadd"),
        yyyymm = "",
        jd = "",
        _parent = _this.parent(),
        key = "second",
        objyey = {};
    var objwhere = {
        yeyid: [$.getparent().objdata.yeyinfo.id]
    };
    if (data_rate == "month") {
        yyyymm = _parent.attr("data-year");
        jd = _parent.attr("data-month");
    } else if (data_rate == "quarter") {
        yyyymm = _parent.attr("data-year");
        jd = _parent.attr("data-quarter");
    }
    if (data_rate != "second") {
        key = yyyymm + "_" + jd;
        objwhere.yyyymm = [yyyymm];
        objwhere.jd = [jd];
    }
    if (objReportSave[item.id] && objReportSave[item.id][key]) {
        objyey = objReportSave[item.id][key];
    }
    if (objyey && objyey.localsave == "1") {
        $.getparent().layer.confirm("请确定上报【" + item.reportname + "】吗？上报后将不可修改。", {}, function (index) {
            $.getparent().layer.load();
            new Promise(function (resolve, reject) {
                $.sm(function (re, err) {
                    if (!err) {
                        resolve(re);
                    } else {
                        $.getparent().layer.msg("请求失败！");
                        $.getparent().layer.closeAll("loading");
                        cb && cb();
                    }
                }, ["fuoyoureportlist.getOneReport", data_id, $.msgwhere(objwhere)]);
            }).then(function (data) {
                for (var i = 0; i < data.length; i++) {
                    data[i].reportcontent = JSON.parse(data[i].reportcontent);
                }
                var my = $.getparent().objdata.my;
                var objson = {
                    tb_report_id: data_id,
                    areacode: item.areacode,
                    areaname: item.areaname,
                    sbtruename: my.username,
                    sbuid: my.id,
                    rate: data_rate,
                    yyyymm: yyyymm,
                    jd: jd,
                    operationtype: operationtype,
                    data: data,
                    organ_id: parent.objdata.my.fuyouorganid//所属机构id
                };
                return new Promise(function (resolve, reject) {
                    $.sm(function (re, err) {
                        if (!err) {
                            resolve(re);
                        } else {
                            $.getparent().layer.closeAll("loading");
                            cb && cb();
                            $.getparent().layer.msg("上报失败！");
                        }
                    }, ["fuyou.post", "report.sb", (JSON.stringify(objson))]);
                });
            }).then(function (data) {
                $.getparent().layer.closeAll("loading");
                cb && cb();
                //上报成功
                //更新上报状态
                $("#btnsearch").trigger("click");
            });
            $.getparent().layer.close(index);
        });
    } else {
        $.getparent().layer.alert("您的报表还未填写，现在还不能进行上报操作！");
    }
}
/**
 * 获取某月天数
 * @param year
 * @param month
 * @returns {number}
 */
function getDaysInOneMonth(year, month) {
    month = parseInt(month, 10);
    var d = new Date(year, month, 0);
    return d.getDate();
}
//计算天数差的函数，通用
function DateDiff(sDate1, sDate2) {  //sDate1和sDate2是yyyy-MM-dd格式
    var aDate, oDate1, oDate2, iDays;
    aDate = sDate1.split("-");
    oDate1 = Date.parse(new Date(aDate[0], aDate[1], aDate[2]));  //转换为yyyy-MM-dd格式
    aDate = sDate2.split("-");
    oDate2 = Date.parse(new Date(aDate[0], aDate[1], aDate[2]));
    iDays = parseInt(Math.abs(oDate1 - oDate2) / 1000 / 60 / 60 / 24); //把相差的毫秒数转换为天数
    return iDays;  //返回相差天数
}
///**
// * 获取某季度天数
// * @param year
// * @param jd
// * @returns {number}
// */
//function getDaysInOneQuarter(year, quarter) {
//    var firstdate, lastdate, day;
//    if (quarter == "1") {
//        firstdate = year + '-' + 01 + '-01';
//        day = new Date(year, 3, 0);
//        lastdate = year + '-' + 03 + '-' + day.getDate();//获取第一季度最后一天日期
//    } else if (quarter == "2") {
//        firstdate = year + '-' + 04 + '-01';
//        day = new Date(year, 6, 0);
//        lastdate = year + '-' + 06 + '-' + day.getDate();//获取第二季度最后一天日期
//    } else if (quarter == "3") {
//        firstdate = year + '-' + 07 + '-01';
//        day = new Date(year, 9, 0);
//        lastdate = year + '-09-' + day.getDate();//获取第三季度最后一天日期
//    } else if (quarter == "4") {
//        firstdate = year + '-' + 10 + '-01';
//        day = new Date(year, 12, 0);
//        lastdate = year + '-' + 12 + '-' + day.getDate();//获取第四季度最后一天日期
//    }
//    return DateDiff(lastdate, firstdate);
//}