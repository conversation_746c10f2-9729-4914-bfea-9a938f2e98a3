<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/html">
<head>
    <meta charset="UTF-8">
    <title>二维码内容设置</title>
    <link rel="stylesheet" href="../../styles/xbcomstyle.css">
    <link rel="stylesheet" href="../../styles/tbstyles.css"/>
    <link rel="stylesheet" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../styles/tbstyles.css"/>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">

    <link rel="stylesheet" type="text/css" href="../../css/commonstyle-layui-btkj.css"/>
    <style type="text/css">
        body {
            background: #EAEFF3;
        }

        er
        .Preview-box {
            width: 45%;
            display: flex;
            flex-direction: row; /* 默认为row，可以不写 */
            flex-wrap: wrap; /* 允许换行 */
            position: relative;
        }

        .Preview-box-item {
            padding-right: 10px; /* 为了更好的视觉效果，添加右边距 */
            width: 100%;
            box-sizing: border-box; /* 确保宽度和高度包含了内边距和边框 */
            display: flex;
            flex-direction: row;
        }

        .Preview-box-item-span-title {
            display: flex;
            justify-content: flex-end;
            padding: 10px;
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            color: #666666;
        }

        .Preview-box-item-span-text {
            color: #34495E;
            display: flex;
            justify-content: left;
            padding: 10px;
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

        }

        .layui-row {
            width: 100%;
        }

        #btnadd {
            width: 150px;
            margin-top: 10px;
            margin-left: 250px;
            border-radius: 5px;
        }

        /*按钮被禁用时的样式*/
        .layui-btn.disabled-btn {
            /* 降低亮度的样式，例如改变背景色、文字颜色、透明度等 */
            background-color: #ccc; /* 假设的背景色 */
            color: #888; /* 假设的文字颜色 */
            opacity: 0.6; /* 透明度 */
            cursor: not-allowed; /* 禁用时的鼠标样式 */
        }

    </style>
</head>
<body>


<div class="marmain topmar">
    <div class="content-medical" style="overflow: auto;">
        <div class="legend_bjcyadd">
            <i class="left-tit"></i><label id="lbtitle1">显示信息设置</label>
        </div>
        <div class="layui-form layui-comselect" style="padding:10px;">
            <div class="layui-form-item">
                <label class="layui-form-label">二维码类型：</label>
                <div class="layui-input-inline">
                    <select id="seltype" lay-filter="seltype">
                        <option value="1">预约挂号</option>
                        <option value="2">入园体检</option>
                        <option value="3">在园体检</option>
                    </select>
                </div>
            </div>

        </div>
        <div style="display: flex">
            <div class="layui-col-md6">
                <table id="laytable" lay-filter="laytable"></table>
            </div>
            <div class="layui-col-md6" style="background: #F7F8FA; margin: 10px;">
                <!--    预览信息，在js中动态添加-->
                <div class="Preview-box">
                </div>
                <button type="button" class="layui-btn" id="btnadd">保存</button>
            </div>
        </div>
    </div>

</div>
<script type="text/javascript" data-main="../../js/depart/erwmset" src="../../sys/require.min.js"></script>
</body>
</html>
