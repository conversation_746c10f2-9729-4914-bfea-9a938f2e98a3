<!DOCTYPE html>
<html>
<head>
    <title>证书管理</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="../../css/reset.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <link rel="stylesheet" type="text/css" href="../../css/style.css"/>
    <style type="text/css">
        html, body {
            background: #EAEFF3;
            overflow-y: hidden;
        }
        .layui-form-label {
            line-height: 28px;
        }
		.layui-form .layui-form-item {
  margin-bottom: 5px;
}
    </style>
</head>
<body>
<div class="marmain">
    <div class="content-medical">
        <div class="layui-form layui-comselect" style="padding: 10px 10px 5px 10px; ">
            <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                <label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 10px;">托幼机构类型：</label>
                <div class="layui-input-inline" style="min-height: 30px;width:190px;">
                    <select lay-filter="selyeytype" id="selyeytype"></select>
                </div>
            </div>
            <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                <label class="layui-form-label" style="padding: 5px 0px 5px 10px;">托幼机构：</label>
                <div class="layui-input-inline" style="min-height: 30px;width:200px;">
                    <select lay-filter="selyey" id="selyey">
                    </select>
                </div>
            </div>
            <div class="layui-form-item" style="display: inline-block;vertical-align: top;height:39px;">
                <label class="layui-form-label" style="padding: 5px 0px 5px 10px;">起止日期：</label>
                <div class="layui-input-inline" style="float: none;width: auto;margin-right: 0px;vertical-align: top;">
                    <input id="txtstartdate" type="text" style="width: 130px;" readonly placeholder="开始时间" class="layui-input"/>
                    <img id="iconstartdate" src="../../images/newicon/ico_calendar.png" style="cursor: pointer;position: relative;top: -29px;width: 20px;left: 103px;">
                </div>
                <span class="linebg">-</span>
                <div class="layui-input-inline" style="float: none;width: auto; vertical-align: top;">
                    <input id="txtenddate" type="text" style="width: 130px; " readonly placeholder="结束时间" class="layui-input"/>
                    <img id="iconenddate" src="../../images/newicon/ico_calendar.png" style="cursor: pointer;position: relative;top: -29px;width: 20px;left: 103px;">
                </div>
            </div>
            <div class="layui-form-item" style="display: inline-block;vertical-align: top; ">
                <label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 10px;">证书名称：</label>
                <div class="layui-input-inline" style="min-height: 30px;width:100px;">
                    <select name="selcertname" lay-verify="required" lay-filter="selcertname" id="selcertname">
                    </select>
                </div>
            </div>
            <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                <label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 10px;">关键字：</label>
                <div class="layui-input-inline" style="min-height: 30px;width:200px;">
                    <input type="text" placeholder="姓名/身份证号/电话/园所名称" class="layui-input" id="txtkeyword">
                </div>
            </div>
            <button id="btnSearch" class="layui-btn form-search" style="vertical-align: top;">查询</button>
            <button id="btnimport" class="layui-btn form-search" style="vertical-align: top;">导出Excel</button>
        </div>
    </div>
    <div class="marmain-cen">
        <div class="content-medical">
            <table id="table_list" lay-filter="table_list"></table>
        </div>
    </div>
</div>
<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../plugin/xm-select/xm-select.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/function.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../js/common/listcommon.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../js/peixun/provemanage.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>