/*新增日期: 2025.06.25
作 者: 朱辽原
修改时间：
修改人：
內容摘要: 便民服务牌单园_全部园家长预约详细-预约
*/
require.config({
    paths: {
        system: '../../sys/system',
        jquery: '../../sys/jquery',
        layui: '../../layui-btkj/layui',
        dataSource: './enterData'
    },
    shim: {
        system: {
            deps: ['jquery']
        },
        layui: {
            deps: ['jquery', 'system', 'dataSource']
        }
    },
    waitSeconds: 0
})
require(['jquery', 'layui', 'system', 'dataSource'], ($, layui2, system, dataSource) => {
    layui.config().extend({ //设定模块别名
        system: '../../sys/system',
    });
    layui.use(['system', 'form', 'table', 'laydate'], function () {
        layer = layui.layer;
        form = layui.form;
        laydate = layui.laydate;
        const params = Arg.all();
        initDatePicker();
        initSelectData();
        initEvent();
        initTable();

        function initDatePicker() {
            const isoDate = new Date().toISOString();
            const nowDate = isoDate.split('T')[0];
            laydate.render({
                elem: '#startDate',
                type: 'date',
                value: params.startDate || nowDate,
                done: function (value) {
                    console.log(value)
                }
            });
            laydate.render({
                elem: '#endDate',
                type: 'date',
                value: params.endDate || nowDate
            });
            // 初始化开始时间选择器
            laydate.render({
                elem: '#startTime',
                type: 'time',
                format: 'HH:mm',
                value: params.startTime || '00:00'
            });
            // 初始化结束时间选择器
            laydate.render({
                elem: '#endTime',
                type: 'time',
                format: 'HH:mm',
                value: params.endTime || '23:59'
            });

        }

        function initSelectData() {
            // console.log(params)
            // 获取园所名称数据
            $.sm(function (re1, err1) {
                // console.log(re1, err1)
                if (re1) {
                    // console.log(re1)
                    var gardenData = [{id: '', name: '请选择'}];
                    for (let i = 0; i < re1.length; i++) {
                        gardenData.push({id: re1[i].id, name: re1[i].name});
                    }
                    var gardenSelect = $('#gardenName');
                    gardenSelect.empty();
                    gardenData.forEach(item => {
                        gardenSelect.append(`<option value="${item.id}">${item.name}</option>`);
                    });
                    gardenSelect.val(params.kindergartenId || '');
                } else {
                    jQuery.getparent().layer.msg(err1, {icon: 5});
                }
            }, ["garden.list", JSON.stringify({regional: params.regional})], null, null, {async: false});

            // 获取年级数据（模拟数据，实际应从后端获取）
            const gradeData = [{id: '', name: '请选择'}, {id: '小班', name: '小班'}, {
                id: '中班',
                name: '中班'
            }, {id: '大班', name: '大班'}];
            const gradeSelect = $('#grade');
            gradeSelect.empty();
            gradeData.forEach(item => {
                gradeSelect.append(`<option value="${item.id}">${item.name}</option>`);
            });
            gradeSelect.val(params.grade || '');

            // 获取科室数据（模拟数据，实际应从后端获取）
            const deptData = [{id: '', name: '请选择'}, {id: '儿科', name: '儿科'}, {
                id: '耳鼻喉科',
                name: '耳鼻喉科'
            }, {id: '眼科', name: '眼科'}];
            const deptSelect = $('#dept');
            deptSelect.empty();
            deptData.forEach(item => {
                deptSelect.append(`<option value="${item.id}">${item.name}</option>`);
            });
            deptSelect.val(params.dept || '');

            const isCancelSelect = $('#isCancel');
            isCancelSelect.val(params.isCancel === '' ? '' : params.isCancel)

            form.render('select');
        }

        function initEvent() {
            $("#btnsearch").click(btnsearch);
            $("#printPreview").click(function () {
                layer.msg('打印预览功能', {icon: 1});
            });
            $("#closeBtn").click(function () {
                parent.layer.closeAll();
            });
        }

        function initTable() {
            var objwhere = getswhere();
            var arrcol = [
                {field: 'id', type: "numbers", title: '序号', width: 100, align: 'center'},
                { field: 'fromtype', title: '来源', width: 200, align: 'center', templet: () => "便民服务牌" },
                { field: 'childclassify', title: '子分类', width: 100, align: 'center', templet: () => "/" },
                {field: 'gardenName', title: '幼儿园', width: 300, align: 'center'},
                {field: 'childName', title: '幼儿姓名', width: 130, align: 'center'},
                {
                    field: 'childGender',
                    title: '性别',
                    width: 60,
                    align: 'center',
                    templet: d => d.childGender === '男' ? '男' : '女'
                },
                {field: 'birthDate', title: '出生日期', width: 130, align: 'center'},
                {field: 'className', title: '班级', width: 130, align: 'center'},
                {field: 'grade', title: '年级', width: 130, align: 'center'},
                {field: 'parentIdentity', title: '家长身份', width: 130, align: 'center'},
                {field: 'parentName', title: '家长姓名', width: 130, align: 'center'},
                {
                    field: 'parentGender',
                    title: '性别',
                    width: 60,
                    align: 'center',
                    templet: d => d.parentGender === '女' ? '女' : '男'
                },
                {field: 'parentAge', title: '年龄', width: 130, align: 'center'},
                {field: 'address', title: '家庭住址', width: 300, align: 'center'},
                {field: 'phone', title: '家长手机号', width: 130, align: 'center'},
                {field: 'deviceType', title: '设备类型', width: 130, align: 'center'},
                // {field: 'appOpenTime', title: '打开APP时间', width: 200, align: 'center'},
                {field: 'deptName', title: '预约科室', width: 130, align: 'center'},
                {
                    field: 'recheckTime', title: '预约挂号时间', width: 200, align: 'center', templet: d => {
                        return d.recheckTime
                    }
                },
                {
                    field: 'cancelTime',
                    title: '取消预约挂号时间',
                    width: 200,
                    align: 'center',
                    templet: d => d.cancelTime ? d.cancelTime : '-'
                }
            ];

            layui.table.render({
                elem: '#laytable',
                url: encodeURI($.getLayUrl() + "?" + $.getSmStr(["parentAppointment.list"])),
                height: 'full-100',
                where: {
                    swhere: $.msgwhere(objwhere),
                    fields: 'id',
                    types: 'asc'
                },
                cols: [arrcol],
                done: function (res, curr, count) {
                    // 表格加载完成后的回调
                },
                countNumberBool: true,
                even: true,
                page: true,
                limits: [30, 50, 100, 200],
                limit: 30,
                skin: 'row'
            });

            layui.table.on('tool(laytable)', function (obj) {
                // 表格工具条事件处理
            });

            layui.table.on('row(laytable)', function (obj) {
                // 单元格点击事件处理
            });
        }

        function btnsearch() {
            var objwhere = getswhere();
            layui.table.reload('laytable', {
                page: {curr: 1},
                where: {
                    swhere: $.msgwhere(objwhere),
                    fields: 'id',
                    types: 'asc'
                }
            });
        }

        function getswhere() {
            var objwhere = {};
            var startDate = $("#startDate").val() || params.startDate;
            var endDate = $("#endDate").val() || params.endDate;
            var startTime = $("#startTime").val() || params.startTime;
            var endTime = $("#endTime").val() || params.endTime;
            var gardenId = $("#gardenName").val();
            var grade = $("#grade").val();
            var dept = $("#dept").val();
            var isCancel = $("#isCancel").val();
            console.log(startDate, endDate, startTime, endTime)

            // 处理日期时间组合
            if (startDate) {
                if (startTime) {
                    objwhere.startDateTime = [`${startDate} ${startTime}:00`];
                } else {
                    objwhere.startDate = [startDate];
                }
            }
            if (endDate) {
                if (endTime) {
                    objwhere.endDateTime = [`${endDate} ${endTime}:59`];
                } else {
                    objwhere.endDate = [endDate];
                }
            }
            if (gardenId) objwhere.gardenId = [gardenId];
            if (grade) objwhere.grade = [grade];
            if (dept) objwhere.dept = [dept];
            if (isCancel !== '') objwhere.isCancel = [isCancel];

            // console.log(objwhere);
            // console.log(params)
            return {
                ...params,
                ...objwhere
            };
        }
    });
})
