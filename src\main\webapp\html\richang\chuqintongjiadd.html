﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>出勤统计编辑</title>
    <link type="text/css" rel="stylesheet" href="../../css/reset.css" />
    <link type="text/css" rel="stylesheet" href="../../layui-btkj/css/layui.css" />
    <link rel="stylesheet" type="text/css" href="../../css/icon.css" />
    <link rel="stylesheet" type="text/css" href="../../css/commonstyle-layui-btkj.css" />
    <link rel="stylesheet" type="text/css" href="../../css/style.css" />
    <style type="text/css">
        .isview { margin-top: 0; }

        .layui-table-view { margin: 0px; }

        .webuploader-container { height: 120px; }

        .default-form .layui-form-label { width: 120px; color: #778CA2; }
        .layui-input-block { margin-left: 130px; }

        .layui-table td { border-width: 1px !important; }
        .form-display .layui-form-radio { margin: 0 0px 0 0; width: 33%; }
    </style>
</head>
<body>
    <div class="layui-page" style="padding: 0 15px;">
        <form id="form" action="" class="layui-form form-display" lay-filter="formOk">
            <div class="default-form" style="margin:20px 10px;">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>年份：</label>
                        <div class="layui-input-block">
                            <select id="selyear" lay-filter="selyear" disabled="disabled"></select>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>月份：</label>
                        <div class="layui-input-block">
                            <select id="selmonth" lay-filter="selmonth" disabled="disabled"></select>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>在册儿童数：</label>
                        <div class="layui-input-block">
                            <input id="txtstucount" class="layui-input" value="0" lay-verify="required" data-type="number" maxlength="4" />
<!--                            <label id="txtstucount" class="layui-form-label" style="text-align: left;color: black;"></label>-->
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>应出勤日数：</label>
                        <div class="layui-input-block">
                            <input id="txtdaycount" class="layui-input" value="0" lay-verify="" data-type="number" maxlength="4" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>应出勤人次数：</label>
                        <div class="layui-input-block">
                            <input id="txtydcount" class="layui-input" value="0" lay-verify="required|chkcount" data-type="number" maxlength="5" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>实际出勤人次数：</label>
                        <div class="layui-input-block">
                            <input id="txtsdcount" class="layui-input" value="0" lay-verify="required|chkcount" data-type="number" maxlength="5" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">缺勤人次数：</label>
                        <div class="layui-input-block">
                            <label id="labqq" class="layui-form-label" style="text-align: left;color: black;">0</label>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>因病：</label>
                        <div class="layui-input-block">
                            <input id="txtbjcount" class="layui-input" value="0" lay-verify="required|chkqq" data-type="number" maxlength="4" notip="1"/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>因事：</label>
                        <div class="layui-input-block">
                            <input id="txtsjcount" class="layui-input" value="0" lay-verify="required|chkqq" data-type="number" maxlength="4" notip="1"/>
                        </div>
                    </div>
                </div>
<!--                <div class="layui-form-item">-->
<!--                    <div class="layui-inline">-->
<!--                        <label class="layui-form-label"><em>*</em>寒暑假：</label>-->
<!--                        <div class="layui-input-block">-->
<!--                            <input id="txthscount" class="layui-input" value="0" lay-verify="required|chkqq" data-type="number" maxlength="4" notip="1"/>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>其他：</label>
                        <div class="layui-input-block">
                            <input id="txtqtcount" class="layui-input" value="0" lay-verify="required|chkqq" data-type="number" maxlength="4" notip="1"/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <div class="layui-input-block">
                            <a id="btnget" class="layui-btn">获取数据</a>
                        </div>
                    </div>
                </div>
                <div style="display:none;">
                    <div class="layui-input-block">
                        <a id="btnok" class="layui-btn" lay-submit=lay-submit>立即提交</a>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <script data-main="../../js/richang/chuqintongjiadd" src="../../sys/require.min.js"></script>
</body>
</html>