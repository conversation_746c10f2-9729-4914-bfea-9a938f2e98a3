﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>卫生保健——专案管理——添加心理行为专案专案</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <link href="../../styles/admin.css" rel="stylesheet" type="text/css"/>
    <link href="../../styles/tbstyles.css" type="text/css" rel="stylesheet"/>
    <link href="../../plugin/ehaiform/css/tbbaseform.css" rel="stylesheet" type="text/css"/>
    <link href="../../plugin/editselect/css/editselect.css" rel="stylesheet" type="text/css"/>
    <link href="../../plugin/contextmenu/css/contextmenu.css" rel="stylesheet" type="text/css"/>
    <style type="text/css">
        /*表格*/
        table.tabstyle {
            border: 1.5pt solid black;
            margin: 0px auto;
            width: 100%;
            font-size: 13px;
        }

        table.tabstyle th, table.tabstyle td {
            line-height: 22px;
            border: #9F9F9F solid 1px;
            text-align: center;
            padding: 5px 10px;
        }
    </style>
</head>
<body>
<div class="bodywidth">
    <div style="padding: 20px;">
        <div>
            <table cellpadding="0" cellspacing="0" border="0" class="tablestyle_field" style="width: 100%;">
                <tr>
                    <td class="tdspan">
                        <span>班级：</span>
                    </td>
                    <td>
                        <label id="selclas">
                        </label>
                    </td>
                    <td class="tdspan">
                        <span>姓名：</span>
                    </td>
                    <td>
                        <label id="selstud">
                        </label>
                    </td>
                </tr>
                <tr>
                    <td class="tdspan">
                        <span>出生日期：</span>
                    </td>
                    <td>
                        <input id="txtbirthday" type="text" readonly="readonly" style="width: 100px;"/>
                    </td>
                    <td class="tdspan">
                        <span>年龄：</span>
                    </td>
                    <td>
                        <input id="txtage" type="text" readonly="readonly" style="width: 100px;"/>
                    </td>
                </tr>
                <!--<tr>-->
                <!--<td class="tdspan">-->
                <!--<span>父亲姓名：</span>-->
                <!--</td>-->
                <!--<td>-->
                <!--<input id="txtfname" type="text" style="width: 100px;" />-->
                <!--</td>-->
                <!--<td class="tdspan">-->
                <!--<span>父亲心理状况：</span>-->
                <!--</td>-->
                <!--<td>-->
                <!--<input id="txtfpsychicstatus" type="text" style="width: 100px;"/>-->
                <!--</td>-->
                <!--</tr>-->
                <!--<tr>-->
                <!--<td class="tdspan">-->
                <!--<span>母亲姓名：</span>-->
                <!--</td>-->
                <!--<td>-->
                <!--<input id="txtmname" type="text" style="width: 100px;" />-->
                <!--</td>-->
                <!--<td class="tdspan">-->
                <!--<span>母亲心理状况：</span>-->
                <!--</td>-->
                <!--<td>-->
                <!--<input id="txtmpsychicstatus" type="text" style="width: 100px;"/>-->
                <!--</td>-->
                <!--</tr>-->
            </table>
        </div>
        <div id="divcontent"></div>
    </div>
    <button style="display: none" id="btnsave">保存</button>
</div>
<script data-main="../../js/zhuanan/psychologyadd" src='../../sys/require.min.js'></script>
</body>
</html>
