/**
 * 通知详情
 * @type {{certids: string, title: string}}
 */
var objdata = {
    title: '<h3 style="line-height: 35px;text-align: center;font-size: 16px;padding:5px 15px 5px 15px;">{{title}}</h3>'
    , certids: ""//证书id
};
layui.use(["layer"], function () {
    var isPC = /Android|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent) ? false : true;
    if (!isPC) {
        objdata.title = '<h3 style="text-align: left;font-size: 16px;margin:0.1rem 0.3rem 0;">{{title}}</h3>';
        $("#container").parent().css({
            position: "absolute",
            width: "100%",
            overflowY: "scroll",
            top: 0,
            bottom: 0
        });
    }
    var outCon = $("#container"),
        ossBaseUrl = ossPrefix,
        noticeId = Arg("noticeid"),
        mobile = Arg("mobile"),
        yeyid = Arg("yeyid"),
        fromsys = Arg("fromsys"),
        attacheIcon = {
            baseUrl: "/images/",
            png: "txt_img1.png",
            jpg: "txt_img1.png",
            doc: "txt_img2.png",
            docx: "txt_img2.png",
            zip: "txt_img3.png",
            rar: "txt_img3.png",
            txt: "txt_img5.png",
            xlsx: "txt_img6.png",
            xls: "txt_img6.png",
            pdf: "txt_img7.png",
            ppt: "txt_img10.png"
        };
    //定义模板
    //详情头部



    var headTem = '<div class="view-notifications">' +
        '<h3>{{title}}</h3>' +
        '<div class="view-notifictxt">' +
        '<div style="margin-right: 225px;line-height: 22px;"><span>发布人：{{fromname}}</span> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span>通知类型：{{typename}}</span>' +
        '{{noticeneedinfos}}</div> ' +
        '<div style="position: absolute;right: 20px;top: 35px;"><span style="float: right; margin-right: 0;">发布日期：{{publishtime}}</span></div></div>' +
        '</div>' +
        '<div class="view-notifications-list">{{content}}<div class="unit-txt">发文单位：{{fromdepart}}</div></div>';
    var attacheTem = '<div class="js-attache-list" style="padding: 0 20px;">\
                    <dl class="layui-txt-add">\
                        <dt><img src="{{icon}}" style="width: 32px; margin-right: 5px;"></dt>\
                        <dd><p class="layui-txt-tit" style="line-height: 20px;">{{name}}<label>（{{size}}）</label></p>\
                        <a class="js-down-file" href="{{path}}" data-path="{{path}}" style="color: #4A90E2; !important;">下载</a></dd>\
                </dl></div>';
    // var headTem = objdata.title +
    //     '<hr style="height:1px;border:none;border-top:1px dashed lightgray;margin: 0 10px;opacity: 0.5;">' +
    //     '<div class="noti-tit-list">' +
    //     '<div>' +
    //     '<p style="font-size: 14px;margin: 10px 0 0 0;">发布人：<span>{{fromname}}</span></p>' +
    //     '<p class="qr-type-name" style="margin-top: 10px;font-size: 14px;margin-left: 2.4rem">通知类型：<span>{{typename}}</span></p>' +
    //     '</div>' +
    //     '<div>' +
    //     '<p style="font-size: 14px;margin: 0 0 10px 0;">发布日期：<span>{{publishtime}}</span></p>' +
    //     '</div>' +
    //     '</div>' +
    //     '<hr style="height:1px;border:none;border-top:1px dashed lightgray;margin: 0 10px;opacity: 0.5;">';
    // var attacheTem = '<dl style="padding: 0 0.15rem 0.08rem">' +
    //     '<dt><img src="{{icon}}"></dt>' +
    //     '<dd style="font-size: 14px;margin-left: 33px;">' +
    //     '<p class="layui-txt-tit">{{name}}<label>（{{size}}）</label></p>' +
    //     '<a class="js-down-file-pc" data-path="{{path}}" href="{{hrefPath}}" style="display: {{downPcShow}};color: #1ca89f !important;">下载</a>' +
    //     '<a class="js-down-file-wx" target="_blank" href="{{hrefPath}}" style="display: {{downWxShow}};color: #1ca89f !important;">下载</a>' +
    //     '</dd>' +
    //     '</dl>';
    var templete = {
            headTem: headTem, //    详情头部
            attacheTem: attacheTem//附件
        },
        /**
         * 替换模板数据 模板与数据中的变量名完全一致
         * @param template {String} 模板字符串
         * @param model {Object} 模板数据
         * @returns {*} {strHtml} 数据替换后的模板字符串
         */
        renderTemp = function (template, model) {
            var reg = /\{\{\w+\}\}/g;
            template = template.replace(reg, function (regStr) {
                var reg2 = /\{\{(\w+)\}\}/g,
                    key = reg2.exec(regStr)[1];
                return model[key];
            });
            return template;
        },
        params = {
            yeyId: Arg("yeyid"),
            noticeId: noticeId,
            fromsys: Arg("fromsys"),
            areaCode: Arg("areacode")
        };

    $(window).on('resize', function () {
        var btnh = $("#qr-enroll-btn").height();
        var bodyh = $("body").height();
        $('#noticecontent').height(bodyh - 60);
    }).resize();

    function init() {
        initEvent();
        new Promise(function (resolve, reject) {
            // aboutEnroll();
            var objjson = {
                    areacode: Arg("areacode"),
                    noticeId: params.noticeId
                },
                arrsm = ["yey.noticegetOneNotice", (JSON.stringify(objjson))];
            $.sm(function (re, err) {
                if (!err && re) {
                    resolve(re[0]);
                } else {
                    layer.msg("发送消息失败")
                }
            }, arrsm);
        }).then(function (data) {
            if (!data) {
                return new Promise.reject("参数不合法");
            }
            params.isnneedinfo = data.isnneedinfo;
            objdata.certids = data.certids;//证书id
            var getnotice = function () {
                return new Promise(function (resolve, reject) {
                    resolve(data);
                });
            };
            var getAttaches = function (noticeId) {
                    return new Promise(function (resolve, reject) {
                        var objjson = {
                            areacode: Arg("areacode"),
                            noticeId: noticeId
                        };
                        var arrpm = ["yey.notice.selAttache", JSON.stringify(objjson)];
                        $.sm(function (re, err) {
                            if (!err && re) {
                                resolve(re);
                            } else {
                                layer.msg("查找附件失败")
                            }
                        }, arrpm);
                    });
                };
            return Promise.all([getnotice(),getAttaches(params.noticeId)]);
        }).then(function (data) {
            var arrpm = ["getreportrul", Arg("areacode")];
            $.sm(function (re, err, obj) {
                if (!err && obj) {
                    objdata.reporturl = obj.reporturl;
                    showContent(data);
                } else {
                    layer.msg("获取附件地址失败！");
                }
            }, arrpm);
        }).catch(function (msg) {
            $("body").html("<h2>" + msg + "</h2>");
        });
        //更新阅读状态
        if (!Arg("readstatus") || Arg("readstatus") == "noread") {
            new Promise(function (resolve, reject) {
                var objson = {
                    yeyid: Arg("yeyid"),
                    fuyouid: $.getparent().objdata.my.fuyouid,
                    noticeid: noticeId,
                    areacode: Arg("areacode"),
                    mobile: mobile,
                    fromsys: Arg("fromsys"),
                    truename: $.getparent().objdata.my.name,
                    uid: $.getparent().objdata.my.id
                };
                if (Arg("iswx")) {
                    objson.iswx = Arg("iswx");
                }
                var arrsm2 = ["notice_read", JSON.stringify(objson)];
                $.sm(function (re, err) {
                    if (!err) {
                        resolve(re);
                    } else {
                        console.log("请求失败！");
                    }
                }, arrsm2);
            }).then(function (data) {
                // if(params.fromsys && params.fromsys == "f" && data[0]){
                //     $.sm(function (re, err) {
                //         if (!err) {
                //             resolve(re);
                //         } else {
                //             console.log("请求失败！");
                //         }
                //     }, ['areNotice.changenNum', noticeId, mobile]);
                // }
            });
        }
    }

    function initEvent() {//    下载附件
        outCon.off("click", ".js-down-file-pc").on("click", ".js-down-file-pc", function (ev) {
            var fileObj = {};
            fileObj.fileName = $(this).prev().get(0).childNodes[0].nodeValue;
            fileObj.ossKey = $(this).attr("data-path");
            downAttache(fileObj);
        });
        outCon.off("click", ".js-down-file-wx").on("click", ".js-down-file-wx", function (ev) {
            // layer.msg("如果微信端不能打开或下载，请前往pc端下载相关附件");
        });
        // 验证电话
        $('#txtmobile').on('blur', function () {
            var $this = $(this);
            var $val = $.trim($this.val());
            if ($val) {
                if (!mobileCheck($.trim($val))) {
                    layer.msg("请输入有效的手机号码！", {shift: 6});
                    return false;
                }
            }
        });
    }

    function showContent(data) {//展现通告内容
        var content = data[0],
            attaches = data[1];
        content.content = $.DecodeSpChar(content.content);
        if(content.isnneedinfo == 1 && content.needinfos){
            var arrneed = [];
            var needinfos = JSON.parse(content.needinfos);//需要提交的资料
            for (var j in needinfos) {
                arrneed.push(needinfos[j]);
            }
            if(arrneed.length > 0){
                content.noticeneedinfos = '<br/><span>报名需要提交的资料：' + arrneed.join('，') + '</span>';
            }else{
                content.noticeneedinfos = '';
            }
        }else{
            content.noticeneedinfos = '';
        }
        $("#noticecontent").append(renderTemp(templete.headTem, content));
        if (!isPC) {
            outCon.find(".qr-type-name").css({
                display: "block",
                marginLeft: "0px"
            });
        }
        // if (!isPC) {
        //     outCon.children().eq(0).find(".noti-txt-con").css({marginTop: "0"}).html('<div style="font-size: 14px;line-height: 22px;">' + decodeURI(content.content) + '</div>');
        //     outCon.find(".end-txt").css({margin: "0.2rem 0.3rem"}).html('<div style="font-size: 14px;margin-right: 30px;">' + "发文单位：" + content.fromdepart + '</div>');
        // }
        if (attaches.length === 0) {
            return;
        }
        var arrHtml = [];
        for (var i = 0; i < attaches.length; i++) {
            var curAttache = attaches[i];
            var ext = "";
            if (curAttache.name) {
                ext = /\.\w+/.exec(curAttache.name)[0];
            }
            if (curAttache.path) {
                var reporturl = objdata.reporturl.replace("/fuyou/post", "");
                curAttache.icon = reporturl + attacheIcon.baseUrl + (attacheIcon[ext] || attacheIcon.txt);
                curAttache.hrefPath = curAttache.path;
                curAttache.path = ossPrefix + curAttache.path;//.replace("fuyou/notice/", "");
                curAttache.downPcShow = isPC ? "inline" : "none";
                curAttache.downWxShow = isPC ? "none" : "inline";
                arrHtml.push(renderTemp(templete.attacheTem, attaches[i]));
            }
        }
        if(arrHtml.length > 0){
            $("#noticecontent").append('<div class="view-notifications"><p class="annex-txt"><img src="../images/newicon/fujian.png" style="width: 16px; margin-right: 5px;">附件' + arrHtml.length + '个</p></div>');
            $("#noticecontent").append(arrHtml.join(""));
        }
    }

    /**
     *
     * @param fileOption
     */
    function downAttache(fileOption) {//以附件形式下载文件
        var form = document.createElement("form");
        form.style.display = "none";
        form.method = "post";
        form.action = objdata.reporturl.replace("/post", "") + "/download?filename=" + fileOption.ossKey + "&toname=" + fileOption.fileName + "&downtype=notice";
        document.body.appendChild(form);
        form.submit();
    }

    /**
     * 通知是否报名，及我的报名状态
     */
    function aboutEnroll() {
        var btnBox = $("#qr-enroll-btn");
        new Promise(function (resolve, reject) {
            var objjson = {
                noticeId: noticeId
                , fromid: jQuery.getparent().objdata.my.id
                , receivefuyouid: jQuery.getparent().objdata.my.fuyouid
                , areacode: Arg("areacode")
            };
            var arrsm = ['yeycheck.notice.notiEnroll', (JSON.stringify(objjson))];
            $.sm(function (re, err, obj) {
                if (re && !err) {
                    resolve(re[0]);
                }
            }, arrsm);
        }).then(function (data) {
            if (!data.isenroll) {//不报名
                btnBox.show().find(".qr-type").show();
                $(".qr-type .qr-close-enroll").click(function (){
                    parent.layer.close(parent.objdata.messagecenterpage);
                });
                return false;
            }
            var arrBtnClass = "qr-type" + data.enrollstatus;
            if (+moment(data.enrolltime)._d < +new Date()) {//报名时间截止
                data.enrollstatus == 1 ? arrBtnClass = "qr-type3" : arrBtnClass = "qr-type4";
                //如果用户点击不报名，并且报名时间已截止，
                if ("qr-type4" == arrBtnClass && 2 == data.enrollstatus) {
                    btnBox.find(".qr-type4 .qr-end-enroll").text("已截止报名(我已不报名)");
                }
            }
            btnBox.show().find("." + arrBtnClass).show();
            btnBox.off("click", "a").on("click", "a", function (ev) {
                var conBox = $("#qr-enroll-info"),
                    _this = $(this),
                    btnClass = _this.attr("class");
                switch (btnClass) {
                    case "qr-enroll" : {
                        //我要报名
                        checkEnrollEnd(noticeId, mobile, yeyid).then(function (data) {
                            if (data[0] === "end") {
                                layer.msg("报名已截止");
                            } else if (!(data[1] == 0 || data[1] == 2)) {
                                layer.confirm("报名信息已经更改，请刷新后继续？", {
                                    icon: 3,
                                    title: "提示",
                                    btn: ["刷新", "取消"],
                                    btnAlign: "c"
                                }, function (index2) {
                                    changeEnrollInfo(index2);
                                }, function (index2) {
                                    layer.close(index2);
                                });
                            } else {
                                var objjson = {
                                    areacode: Arg("areacode")
                                    // , mobile: Arg('mobile')
                                    , yeyid: Arg('yeyid')
                                };
                                $.sm(function (re, err) {//处理报名信息默认值
                                    if (re && re[0]) {
                                        // var inputss = conBox.find("input");
                                        // inputss.eq(0).val(re[0][1]);
                                        // inputss.eq(1).val(re[0][2]);
                                        $("#txtname").val(re[0][1]);
                                        $("#txtmobile").val(re[0][2]);
                                    }
                                }, ['yeychdc.user.information', JSON.stringify(objjson)]);
                                if (params.isnneedinfo == "1") {//报名是否需要提交材料
                                    openwinlist(noticeId, 1);
                                } else {
                                    layer.open({
                                        type: 1,
                                        title: "填写报名信息",
                                        area: [isPC ? "50%" : "100%", "380px"],
                                        content: $("#qr-enroll-info"),
                                        btn: ["提交", "取消"],
                                        btnAlign: "c",
                                        shift: 'top', //从左动画弹出
                                        success: function (layer) {
                                            // //有了这个就非常好办了，我用js调试了一下马上就定位到了弹窗框的位置然后改了他的class属性，成功。
                                            // //这是我定位的第一个按钮的代码
                                            // layer[0].childNodes[3].childNodes[0].attributes[0].value = 'layui-layer-btn1';
                                            // layer[0].childNodes[3].childNodes[1].attributes[0].value = 'layui-layer-btns';
                                        },
                                        yes: function (index, layero) {
                                            var enrollman = $.trim($("#txtname").val()),
                                                enrollmobile = $.trim($("#txtmobile").val()),
                                                enrollnum = $.trim($("#txtnum").val()),
                                                enrollmark = $.trim(conBox.find("textarea").val());
                                            if (!enrollman || !enrollmobile || !enrollnum) {
                                                layer.msg("必填项不可为空");
                                                return false;
                                            }
                                            if (!mobileCheck(enrollmobile)) {
                                                layer.msg("请输入有效的手机号码！", {shift: 6});
                                                return false;
                                            }
                                            checkEnrollEnd(noticeId, mobile, yeyid).then(function (data) {
                                                if (data[0] === "end") {
                                                    layer.msg("报名已截止");
                                                } else if (!(data[1] == 0 || data[1] == 2)) {
                                                    layer.confirm("报名信息已经更改，请刷新后继续？", {
                                                        icon: 3,
                                                        title: "提示",
                                                        btn: ["刷新", "取消"],
                                                        btnAlign: "c"
                                                    }, function (index2) {
                                                        changeEnrollInfo(index2, index);
                                                    }, function (index2) {
                                                        layer.close(index2);
                                                    });
                                                } else {
                                                    var objjson = {
                                                        enrollman: enrollman
                                                        , enrollmobile: enrollmobile
                                                        , enrollnum: enrollnum
                                                        , enrollmark: enrollmark
                                                        , noticeId: noticeId
                                                        , fuyouid: jQuery.getparent().objdata.my.fuyouid
                                                        , mobile: mobile
                                                        , areacode: Arg("areacode")
                                                    };

                                                    $.sm(function (re, err) {//幼儿园通过通知报名
                                                        if (!err && re) {
                                                            layer.msg("报名成功");
                                                            btnBox.children().hide().eq(1).show();
                                                            setTimeout(function () {
                                                                layer.close(index);
                                                            }, 800);
                                                        } else {
                                                            layer.msg("报名失败,请稍后再试");
                                                            setTimeout(function () {
                                                                layer.close(index);
                                                            }, 800);
                                                        }
                                                    }, ["yey.notice.enroll", JSON.stringify(objjson)]);
                                                }
                                            });
                                            return false;
                                        },
                                        btn2: function (index, layero) {
                                            layer.close(index);
                                        }
                                    });
                                }
                            }
                        });
                        break;
                    }
                    case "qr-no-enroll" : {//端确认不报名
                        checkEnrollEnd(noticeId, mobile, yeyid).then(function (data) {
                            //不报名
                            if (data[0] === "end") {
                                layer.msg("报名已截止");
                            } else if (!(data[1] == 0 || data[1] == 2)) {
                                layer.confirm("报名信息已经更改，请刷新后继续？", {
                                    icon: 3,
                                    title: "提示",
                                    btn: ["刷新", "取消"],
                                    btnAlign: "c"
                                }, function (index2) {
                                    changeEnrollInfo(index2, index);
                                }, function (index2) {
                                    layer.close(index2);
                                });
                            } else {
                                layer.open({
                                    type: 1,
                                    title: "提示",
                                    icon: 3,
                                    btnAlign: "c",
                                    area: [isPC ? "30%" : "80%", "30%"],
                                    content: "<div style='text-align: center; margin-top: 22px;'>确认不报名吗？</div>",
                                    btn: ["确定", "取消"],
                                    yes: function (index) {
                                        var objjson = {
                                            areacode: Arg("areacode")
                                            , noticeId: noticeId
                                            , mobile: mobile
                                            , fuyouid: jQuery.getparent().objdata.my.fuyouid
                                        };
                                        $.sm(function (re, err) {
                                            if (!err && re) {
                                                layer.msg("您已确认不报名");
                                                btnBox.children().hide().eq(2).show();
                                                setTimeout(function () {
                                                    layer.close(index);
                                                }, 800);
                                            } else {
                                                layer.msg("不报名失败,请稍后再试");
                                                layer.close(index);
                                            }
                                        }, ["yey.notice.notenroll", JSON.stringify(objjson)]);
                                    }
                                });
                            }
                        });
                        break;
                    }
                    case "qr-my-enroll" : {//我的报名信息
                        checkEnrollEnd(noticeId, mobile, yeyid).then(function (data) {
                            if (data[1] != 1) {
                                layer.confirm("报名信息已经更改，请刷新后继续？", {
                                    icon: 3,
                                    title: "提示",
                                    btn: ["刷新", "取消"],
                                    btnAlign: "c"
                                }, function (index2) {
                                    changeEnrollInfo(index2);
                                }, function (index2) {
                                    layer.close(index2);
                                });
                            } else {
                                if (params.isnneedinfo == "1") {//报名资料需要审核
                                    openwinlist(noticeId, 0);
                                } else {
                                    var cancelBtn = ["关闭"];
                                    if (_this.parent().prop("class") === "qr-type1") {
                                        cancelBtn = ["修改", "取消"];
                                    }
                                    layer.open({
                                        type: 1,
                                        title: "我的报名信息",
                                        area: [isPC ? "50%" : "100%", "380px"],
                                        content: $("#qr-enroll-info"),
                                        btn: cancelBtn,
                                        btnAlign: "c",
                                        success: function (layero, index) {
                                            var inputs = conBox.find("input");
                                            inputs.val("");
                                            if (cancelBtn[0] == "关闭") {
                                                layero.find("input").prop("disabled", "disabled");
                                                layero.find(".layui-form .layui-form-item .layui-textarea").prop({
                                                    disabled: "disabled"
                                                });
                                            }
                                            var objjson = {
                                                areacode: Arg("areacode")
                                                , noticeId: noticeId
                                                , fuyouid: parent.objdata.my.fuyouid
                                                , mobile: mobile
                                            };
                                            $.sm(function (re, err) {//看chdc端的获取报名信息
                                                if (!err && re) {
                                                    var objInfo = re[0];
                                                    $("#txtname").val(objInfo.enrollman);
                                                    $("#txtmobile").val(objInfo.enrollmobile);
                                                    $("#txtnum").val(objInfo.enrollnum);
                                                    $("#txtbeizhu").val(objInfo.enrollmark);
                                                } else {
                                                    layer.msg("查看报名信息失败,请稍后再试");
                                                    layer.close(index);
                                                }
                                            }, ["yey.notice.getEnrollInfo", JSON.stringify(objjson)]);
                                        },
                                        yes: function (index, layero) {
                                            if(_this.parent().prop("class") === "qr-type1"){
                                                var inputs = conBox.find("input"),
                                                    enrollman = $.trim(inputs.eq(0).val()),
                                                    enrollmobile = $.trim(inputs.eq(1).val()),
                                                    enrollnum = $.trim(inputs.eq(2).val()),
                                                    enrollmark = $.trim(conBox.find("textarea").val());
                                                if (!enrollman || !enrollmobile || !enrollnum) {
                                                    layer.msg("必填项不可为空");
                                                    return false;
                                                }
                                                if (!mobileCheck($.trim(enrollmobile))) {
                                                    layer.msg("请输入有效的手机号码！", {shift: 6});
                                                    return false;
                                                }
                                                checkEnrollEnd(noticeId, mobile, yeyid).then(function (data) {
                                                    if (data[0] === "end") {
                                                        layer.msg("报名已截止");
                                                    } else if (data[1] != 1) {
                                                        layer.confirm("报名信息已经更改，请刷新后继续？", {
                                                            icon: 3,
                                                            title: "提示",
                                                            btn: ["刷新", "取消"],
                                                            btnAlign: "c"
                                                        }, function (index2) {
                                                            changeEnrollInfo(index2, index);
                                                        }, function (index2) {
                                                            layer.close(index2);
                                                        });
                                                    } else {
                                                        var objjson = {
                                                            enrollman: enrollman
                                                            , enrollmobile: enrollmobile
                                                            , enrollnum: enrollnum
                                                            , enrollmark: enrollmark
                                                            , noticeId: noticeId
                                                            , fuyouid: jQuery.getparent().objdata.my.fuyouid
                                                            , mobile: mobile
                                                            , areacode: Arg("areacode")
                                                        };
                                                        $.sm(function (re, err) {
                                                            if (!err && re) {
                                                                layer.msg("修改报名成功");
                                                                btnBox.children().hide().eq(1).show();
                                                                setTimeout(function () {
                                                                    layer.close(index);
                                                                }, 800);
                                                            } else {
                                                                layer.msg("修改报名失败,请稍后再试");
                                                                setTimeout(function () {
                                                                    layer.close(index);
                                                                }, 800);
                                                            }
                                                        }, ["yey.notice.enroll", JSON.stringify(objjson)]);
                                                    }
                                                });
                                                return false;
                                            }else{
                                                layer.close(index);
                                            }
                                        }
                                    });
                                }
                            }
                        });
                        break;
                    }
                    case "qr-cancel-enroll" : {//取消报名
                        checkEnrollEnd(noticeId, mobile, yeyid).then(function (data) {
                            if (data[0] === "end") {
                                layer.msg("报名已截止");
                            } else if (data[1] != 1) {
                                layer.confirm("报名信息已经更改，请刷新后继续？", {
                                    icon: 3,
                                    title: "提示",
                                    btn: ["刷新", "取消"],
                                    btnAlign: "c"
                                }, function (index2) {
                                    changeEnrollInfo(index2);
                                }, function (index2) {
                                    layer.close(index2);
                                });
                            }else if (data[2] > 0) {
                                layer.msg("报名已审核通过,无法取消报名！");
                            } else {
                                layer.open({
                                    type: 1,
                                    title: "提示",
                                    icon: 3,
                                    btnAlign: "c",
                                    area: [isPC ? "30%" : "80%", "30%"],
                                    content: "<div style='text-align: center;margin-top: 22px;'>确认取消报名？</div>",
                                    btn: ["确定", "取消"],
                                    yes: function (index) {
                                        var objjson = {
                                            areacode: Arg("areacode")
                                            , noticeId: noticeId
                                            , fuyouid: $.getparent().objdata.my.fuyouid
                                            , mobile: mobile
                                            , yeyid: $.getparent().objdata.my.yeyid
                                        };
                                        $.sm(function (re, err) {
                                            if (!err && re) {
                                                layer.msg("取消报名成功");
                                                $("#txtname").val("");
                                                $("#txtmobile").val("");
                                                $("#txtnum").val("1");
                                                $("#txtbeizhu").val("");
                                                btnBox.children().hide().eq(0).show();
                                                setTimeout(function () {
                                                    layer.close(index);
                                                }, 800);
                                            } else {
                                                layer.msg("取消报名失败,请稍后再试");
                                                layer.close(index);
                                            }
                                        }, ["yey.notice.cancelEnroll", JSON.stringify(objjson)]);
                                    }
                                });
                            }
                        });
                        break;
                    }
                    case "qr-not-enroll" : {
                        checkEnrollEnd(noticeId, mobile, yeyid).then(function (data) {
                            if (data[0] === "end") {
                                layer.msg("报名已截止");
                            } else if (!(data[1] == 0 || data[1] == 2)) {
                                layer.confirm("报名信息已经更改，请刷新后继续？", {
                                    icon: 3,
                                    title: "提示",
                                    btn: ["刷新", "取消"],
                                    btnAlign: "c"
                                }, function (index2) {
                                    changeEnrollInfo(index2);
                                }, function (index2) {
                                    layer.close(index2);
                                });
                            } else {
                                layer.open({
                                    type: 1,
                                    title: "提示",
                                    icon: 3,
                                    btnAlign: "c",
                                    area: [isPC ? "30%" : "80%", "30%"],
                                    content: "<div style='text-align: center;margin-top: 22px;'>确认取消不报名？</div>",
                                    btn: ["确定", "取消"],
                                    yes: function (index) {
                                        var objjson = {
                                            areacode: Arg("areacode")
                                            , noticeId: noticeId
                                            , fuyouid: $.getparent().objdata.my.fuyouid
                                            , mobile: mobile
                                            , yeyid: $.getparent().objdata.my.yeyid
                                        };
                                        $.sm(function (re, err) {
                                            if (!err && re) {
                                                layer.msg("取消不报名成功", {time: 800});
                                                btnBox.children().hide().eq(0).show();
                                                setTimeout(function () {
                                                    layer.close(index);
                                                }, 800);
                                            } else {
                                                layer.msg("取消不报名失败,请稍后再试");
                                                layer.close(index);
                                            }
                                        }, ["yey.notice.cancelEnroll", JSON.stringify(objjson)]);
                                    }
                                });
                            }
                        });
                        break;
                    }
                    case "qr-close-enroll":
                        parent.layer.close(parent.objdata.messagecenterpage);
                        break;
                }
            });
        });
    }

    init();

    //报名信息更改刷新
    function changeEnrollInfo(index2, index) {
        layer.close(index2);
        index && layer.close(index);
        $("#qr-enroll-btn").children().hide();
        aboutEnroll();
    }
});

/**
 * 验证手机号
 * @param mobile
 * @returns {boolean}
 */
function mobileCheck(mobile) {
    var first = mobile.charAt(0);
    if (first == 1 && mobile.length == 11) {
        return true;
    } else {
        return false;
    }
}

/**
 * 检查报名是否已经截止
 * @param noticeId
 * @param mobile
 * @param yeyid
 * @returns {Promise<unknown>}
 */
function checkEnrollEnd(noticeId, mobile, yeyid) {
    return new Promise(function (resolve, reject) {
        var objjson = {
            noticeId: noticeId,
            mobile: mobile,
            yeyid: yeyid,
            fuyouid: $.getparent().objdata.my.fuyouid,
            areacode: Arg("areacode")
        };
        $.sm(function (re, err) {
            if (re && !err) {
                resolve(re[0]);
            }
        }, ['yey.notice.enrollIsEnd', (JSON.stringify(objjson))]);
    });
}

function openwinlist(noticeid, isfirst = 1) {
    //打开窗口
    var arrbtn = ["提交", "取消"], isnover = 0;
    if($(".qr-type3").css("display") != 'none'){
        arrbtn = ["关闭"];
        isnover = 1;
    }
    jQuery.getparent().layer.open({
        type: 2,
        title: '填写报名信息',
        shadeClose: false,
        area: ['100%', '100%'],
        content: 'html/fuyouenrolllist.html?v=' + (Arg("v") || 1) + '&mid=' + Arg("mid") + "&noticeid=" + noticeid + "&isfirst=" + isfirst + (isnover ? "&isover=" + isnover : ""),// + "&certids=" + objdata.certids,
        btn: arrbtn,
        yes: function (index, layero) { //或者使用btn1
            if(isnover){
                jQuery.getparent().layer.close(index);
            }else{
                layero.find('iframe')[0].contentWindow.subEnroll(function () {
                    jQuery.getparent().layer.close(index);
                    var win = parent.objdata.messagecenteriframe;
                    if(win){
                        win.location.reload();
                    }
                    location.reload();
                });
            }
        }
    });
}
