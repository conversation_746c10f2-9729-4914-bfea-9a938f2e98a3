/*蓝色皮肤*/
/*color*/
.bg1{background: #6eb92b;}
.bg2{background: #d4d5d9;}
.bg3{background: #6eb92b;}
.bg4{background: #f3f3f3;}/*浅灰*/
.bg5{background: #e9e9e9;}/*深灰*/
.cl1{color: #47494a;}
.cl2{color: #6eb92b;}/*蓝色*/

/*scroll*/
.scrollArea,.foldList{/*background:#eee;*/ background:#fff;}

/*icon*/
u[class^=skinIcon],.layim_chateme .layim_chatsay .layim_zero,skinIcondenglu u,skinIconbq u{background-image:url(../images/icon-lightGreen.png);}

/*btn*/
.btn-big,.personalImg .webuploader-pick,.btn-black,.monthHead,.echarts-dataview button{background: #3499da;}
.letterIndex{background: #ccc;}
.popTitle,.fc-day-header{background: #6eb92b;}
.btn-border{border:1px solid #3398dc;}
.btn-border:hover{background: #3a99d9;color: #fff;}
.btn-black:hover,.btn-big:hover{background: #0069b0;}
.layui-layer-btn a.layui-layer-btn0,.btnTop a:hover,.btn-control:hover,.operateBtn a:hover{background:#6eb92b;border-color:#0069b0;}
.layui-layer-btn a{font-size: 14px;}

/*tab*/
.tab li:hover, .tab li.current{color: #fff; background:#6eb92b!important;border: 1px solid #6eb92b;}
.tab li.pulldown.current{color: #fff; background:#6eb92b url(../images/icon_pulldown_white.png) no-repeat 75px center!important;background-size: 12px !important;border: 1px solid #6eb92b;}
.tab li.pulldown:hover{color: #fff; background:#6eb92b url(../images/icon_pulldown_white.png) no-repeat 75px center!important; background-size: 12px !important;border: 1px solid #6eb92b;}

/*letter*/
.letter{background:#eee;}

/*page*/
.dataTables_wrapper .dataTables_paginate .paginate_button.current, .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover,.dataTables_wrapper .dataTables_paginate .paginate_button:hover{ background: -webkit-linear-gradient(#3a99d9 0%, #007ed2 100%);background: linear-gradient(#3a99d9 0%, #007ed2 100%);color: #fff !important;border-color:#007ed2;filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#3a99d9', endColorstr='#007ed2', GradientType=0);}

/*聊天窗口*/
.layim_chatmore{border-right:1px solid #0089bb; background-color:#6eb92b;}
.layim_chatlist li:hover,.layim_chatlist .layim_chatnow,.layim_chatlist .layim_chatnow:hover{background-color:#9bd8ee;}
.layim_sendbtn{ background-color:#6eb92b;}
.layim_enter{border-left-color:#8dd3eb;}
.layim_sendbtn:hover,.layim_enter:hover{background-color:#0089bb;}
.layim_sendtype span:hover,.layim_sendtype span:hover i{background-color:#6eb92b;color:#fff;}
.layim_chateme .layim_chatsay{background: #87bbde;}

/*左侧面板*/
.noticeTitle a:hover{color:#0069b0;}
.funList li{color: #6eb92b;}
.usualFun a:hover,.inner a:hover,.letterList li:hover,.letterList li.current,.btn-head:hover,.articleGray a:hover,.loadMore:hover,.innerHistory li:hover,.today_date,.alm_content p,.list .current,.ztree li a:hover,.foldList li p,.foldList li:hover,.dataTables_wrapper a,.otx-table a,.foldList li,.fc-sat .fc-day-num, .fc-sun .fc-day-num{color: #59b3f0;}/*蓝色*/
footer a.current,footer a:hover{color: #6eb92b;}
.curList a,.workTotal{color: #fff;}
.curList .current,.curList a:hover{color: #0c2e46;}
/*.usualFun a:hover img,.inner a:hover img,.foldList li:hover img{background: url(../images/hover-blue.png) no-repeat center center;}
*/.list li{border-bottom: 1px solid #3497db;}
.leftTitle{border-bottom: 1px solid #2d323e; background: #3398dc;}
.btn-lang:hover,.current.btn-lang{border-color:#3499da;color:#3499da;}
.list .face{border-color:#b8b8b8;}

/*智能中控*/
.btn-far:hover,.btn-near:hover{background:url(../images/distance-blue.png);}
.fast:hover,.normal:hover,.slow:hover,.arrow-up:hover,.arrow-down:hover{background: url(../images/hover-blue.png);}
.numberList span:hover{background: url(../images/blue-big.png);}

/*日历*/
.fc-state-down,.fc-state-active,.fc-button:hover{border-color:#6eb92b;}
.fc-planmeeting-button{border:0;border-radius: 0;}
.fc-year-button:hover,.fc-month-button:hover,.fc-agendaWeek-button:hover,.fc-agendaDay-button:hover,.fc-state-active{background: #6eb92b;color:#fff;}

/*会控*/
.meetingIndex,.fc-planmeeting-button{color:#fff; background: #3499da;}
.rankList span.current{background: #3497db;}

/*栏目管理*/
.meauList span{border-top: 2px solid #6eb92b; border-bottom: 2px solid #6eb92b;}
.meauList .inner a{color: #6eb92b;}
.inner a.selected{background: #e3f2fd;}

/*单位管理*/
.jOrgChart .node,.formList .webuploader-pick{background:#6eb92b;}

/*tree*/
.ztree li a.curSelectedNode{background-color: #e3f2fd;border: 1px solid #6eb92b;}

/*会议控制*/
.meetMain{border-top: 4px solid #6eb92b;}
.submeetList{border-top: 4px solid #cfe8ff;}

/*下拉多选*/
.ui-widget-header{background: #3398dc; border-color:#3398dc;}
.ui-state-hover, .ui-widget-content .ui-state-hover, .ui-widget-header .ui-state-hover, .ui-state-focus, .ui-widget-content .ui-state-focus, .ui-widget-header .ui-state-focus{color: #3398dc;}
.ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active{background: none; border-color:#3398dc;color: #3398dc;}
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default{ border-color:#3398dc;color: #3398dc;}



/*新增加样式*/

.usualFun{ background:#FFFFFF; padding-left:3px; padding-bottom:3px}
.usualFun a{float: left; width: 32%; +width: 32%; margin-top:3px;position: relative;text-align: center;font-size: 12px;line-height: 28px;color:#ffffff; background:#6eb92b; margin-right:3px; font-size:14px;}
.usualFun img{width: 35px;height: 35px; display: block; margin:0 auto;/*border-radius: 50%;*/ /*background: url(../images/gray.png) no-repeat center center;*/ padding-top:10px}
.usualFun u{position: absolute;display: none;}

/*.inner a:hover{width: 32%; +width: 32%; margin-top:3px;position: relative;text-align: center;font-size: 12px;line-height: 28px;color:#ffffff; background:#53C2BC; margin-right:3px; font-size:14px; display:block}
*/


.inner a:hover{ color:#FFF; background:#6eb92b; display:inline-block}

/*幼儿园名称下拉背景色*/
.selectColor{background-color:#6eb92b }

/*智慧数字幼儿园*/
/*桌面头部菜单*/
.ultopnums li.current,.ultopnums li:hover,.topultool li:hover,.topultool li.current {
    color: #ffffff;
    background: -webkit-linear-gradient(#6eb92b, #57961f); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(#6eb92b, #57961f); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(#6eb92b, #57961f); /* Firefox 3.6 - 15 */
    background: linear-gradient(#6eb92b, #57961f); /* 标准的语法 */
}
/*菜单切换*/
.menutab ul{color: #34495E!important;}
.menutab ul li{color: #F3F4F4;border-bottom:3px solid #D5D6D6; 
	background: #F3F4F4;		
	border-image: -webkit-linear-gradient(#F2F3F4,#D5D6D6) 40 20;
    border-image: -moz-linear-gradient(#F2F3F4,#D5D6D6) 40 20;		
	border-image: -o-linear-gradient(#F2F3F4,#D5D6D6) 40 20;
	border-image: linear-gradient(#F2F3F4,#D5D6D6) 40 20;}
.menutab ul li.current{color: #34495E!important; background: #fff;}
#divdesktop .scrollArea{background: none;}
#divmenulist .funList li:hover{background: #F2F2F2;color: #666!important;}
#divdeskleft:after{content: "";width: 20px;position: absolute;top: 0px;bottom: 0;right: -20px;box-shadow: 5px 0px 5px -4px inset #DCE0E4;
  
}
.funtop .desktop-logo .tabout-div{box-shadow: 0px 0px 5px 2px #63a826;}
.funtop .desktop-logo .tab-cell{box-shadow: 0px -8px 4px -5px inset #a7c190;}

/*保教*/
.term-tab.current{background: #a6d879;color: #ffffff;}
.term-tab.current .calendar-div{border: 1px solid #7fb450;}
.term-list.redbg{background: #a6d879;}
.calen-top h5{background: #a6d879;}
.calen-txt p{color: #a6d879;}
.div-right .layui-table tbody.current{border: 2px solid #a6d879;}
.div-right .layui-table tbody.current tr:nth-child(1) td:nth-child(1){background-color: #a6d879;}
.div-right .layui-table td.pinkcurrent{background-color: #a6d879;}
.workrest-left h5{background: #a0d075!important;}
.workrest-right h5{background: #a6d879!important;}
.time-work li.time-worksel{ background:#a6d879;}
.week-tab.current{background: #a6d879;}
.icon-list .layui-btn{background: #71b236!important;}
.protect-educate .plan-txt.current{background: #a6d879;color: #ffffff;border: 1px solid #a6d879;}
.play-sub{border: 2px solid #a6d879!important;}
.def-icon .icon_calen:before{color: #a6d879!important;}
.opreate-icon .icon-txt .iconfont{color: #a6d879!important;}
.protect-educate .plan-left-icon>div .iconfont:before{color: #a6d879;}
.protect-educate .plan-left-icon>div .icon_ratio:before{border: 2px solid #a6d879;}
.def-icon .icon_tableft:before,.def-icon .icon_tabright:before{color: #a6d879;}
#a_unselect{color: #a6d879!important;}
.upload-btn .opr_select{background: #a6d879!important;}
.opr-btn.pay-save{background: #a6d879!important;}
#pgriddivshipuarea.def-layui-table tr.current td:not(.tr-firsttd){background: #eef8e5;}
#pgridplantable.def-layui-table tr.current td:not(.tr-firsttd){background: #eef8e5;}
/*作息弹框按钮颜色*/
.opr-btn{background: #a6d879;}
.cho-main .layui-btn{background: #a6d879;}
.plan-con{border: 4px solid #a6d879;}
.plan-leftspan.currentpink, .plan-rightspan.currentpink{background: #a6d879;}
.plan-leftspan.currentred, .plan-rightspan.currentred{background: #6bb924;}
.left-contain .common-tab3 li.current a{color: #a6d879;font-weight: bold;border-bottom: 2px solid #a6d879;}
.left-contain .icon_search2:before{color: #a6d879;}
.type-plan .iconfont.redicon2:before{color: #a6d879;}
#divdt .layui-table tbody.current{border: 2px solid #a6d879;}
#divdt .layui-table tbody.current tr:nth-child(1) td:nth-child(1){background-color: #a6d879;}
#divdt .layui-table td.pinkcurrent{background-color: #a6d879;}
#divdt .tdexist{background-color: #84c14e;}
.timetable-list .circle-label{background: #a6d879;}
.teaching-list .teachadd-btn .teachlayui-btn{background: #a6d879;}
.upload-list .layui-btn-normal, .teaching-list-bom .upload-right .layui-btn, .cover-list .layui-btn{background: #a6d879;}
#add_pic .webuploader-pick{background: #a6d879;}
.teaching-list .layui-btn{color: #a6d879;}
.upload-btn .opr_save{background: #a6d879!important;}
.upload-btn .opr_saveadd{background: #a6d879!important;}
.course-opr .layui-tab-brief>.layui-tab-title .layui-this{color: #a6d879;}
.course-opr .layui-tab-brief>.layui-tab-more li.layui-this:after,.course-opr .layui-tab-brief>.layui-tab-title .layui-this:after{border-bottom: 4px solid #a6d879;}
#add_pic .webuploader-pick{background:#a6d879;}
#add_pic .webuploader-pick, #add_video .webuploader-pick{background:#a6d879;}
#add_audio .webuploader-pick{background:#a6d879;}
#add_files .webuploader-pick{background:#a6d879;}

