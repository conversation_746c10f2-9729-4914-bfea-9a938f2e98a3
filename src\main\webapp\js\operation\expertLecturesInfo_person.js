/*新增日期: 2025.06.23
作 者: 朱辽原
修改时间：
修改人：
內容摘要: 专家讲座数据详情
*/
require.config({
    paths: {
        system: '../../sys/system',
        jquery: '../../sys/jquery',
        layui: '../../layui-btkj/layui',
        dataSource: './enterData',
        commonUtils: './commonUtils'
    },
    shim: {
        system: {
            deps: ['jquery']
        },
        layui: {
            deps: ['jquery', 'system']
        }
    },
    waitSeconds: 0
})

const openMock = false

const baseModule = ['jquery', 'commonUtils', 'layui', 'system']

if (openMock) {
    baseModule.push('dataSource')
}

require(baseModule, ($, utils) => {
    const openLog = true
    layui.config().extend({
        system: '../../sys/system',
    });
    layui.use(['system', 'form', 'table', 'laydate'], function () {
        layer = layui.layer;
        form = layui.form;
        laydate = layui.laydate;
        const params = Arg.all();
        initDatePicker();
        initSelectData();
        initEvent();
        initTable();

        function initDatePicker() {
            const isoDate = new Date().toISOString();
            const nowDate = isoDate.split('T')[0];
            laydate.render({
                elem: '#startDate',
                type: 'date',
                value: params.startDate || nowDate
            });
            laydate.render({
                elem: '#endDate',
                type: 'date',
                value: params.endDate || nowDate
            });
            // 开始日期
            laydate.render({
                elem: '#startDate',
                type: 'date',
                value: params.startDate || nowDate
            });
            // 结束日期
            laydate.render({
                elem: '#endDate',
                type: 'date',
                value: params.endDate || nowDate
            });
            // 初始化开始时间选择器
            laydate.render({
                elem: '#startTime',
                type: 'time',
                format: 'HH:mm',
                value: params.startTime || '00:00'
            });
            // 初始化结束时间选择器
            laydate.render({
                elem: '#endTime',
                type: 'time',
                format: 'HH:mm',
                value: params.endTime || '23:59'
            });
        }

        function initSelectData() {
            utils.logWithCondition(openLog, new Error().stack, params)

            // 专家讲座数据
            $('#lectureTitle').val(params.title || '')

            // 是否报名数据
            $('#isRegister').val(params.isRegister || '');

            // 是否签到
            $('#isSign').val(params.isSign || '');

            // 是否发送成功
            $('#isSuccess').val(params.isSuccess || '')

            // 家长性别
            $('#parentGender').val(params.parentGender || '')

            // 设备
            $('#deviceType').val(params.deviceType || '')

            // 幼儿性别
            $('#childGender').val(params.childGender || '')

            // 家长年龄
            $('#parentAge').val(params.parentAge || '')

            // 区域分布
            $('#areaDistribution').val(params.areaDistribution || '')
            // 专家讲座
            $('#lectureName').val(params.lectureId || '')

            if (openMock) {
                // 家长年龄
                const parentAgeDate = [

                ]
                // 年级数据
                const gradeData = [
                    { id: '', name: '请选择' },
                    { id: '小班', name: '小班' },
                    { id: '中班', name: '中班' },
                    { id: '大班', name: '大班' }];
                const gradeSelect = $('#grade');
                gradeSelect.empty();
                gradeData.forEach(item => {
                    gradeSelect.append(`<option value="${item.id}">${item.name}</option>`);
                });
                gradeSelect.val(params.grade || '');
                // 班级数据
                const classData = [{ id: '', name: '请选择' },
                { id: '一班', name: '一班' },
                { id: '二班', name: '二班' },
                { id: '三班', name: '三班' }
                ];
                const classSelect = $('#className');
                classSelect.empty();
                classData.forEach(item => {
                    classSelect.append(`<option value="${item.id}">${item.name}</option>`);
                });
                classSelect.val(params.className || '');
            } else {
                // 获取家长孩子关系数据
                $.sm(function (re1, err1) {
                    if (re1) {
                        const relationList = re1.map(item => item.ptype)
                        var parentRelationSelect = $('#parentRelation');
                        parentRelationSelect.empty();
                        parentRelationSelect.append(`<option value="">请选择</option>`);
                        relationList.forEach(item => {
                            parentRelationSelect.append(`<option value="${item}">${item}</option>`);
                        });
                        parentRelationSelect.val(params.parentRelation || '');
                    } else {
                        jQuery.getparent().layer.msg('获取关系列表失败', { icon: 5 });
                    }
                }, ["parentReservation.relation"], null, null, { async: false });

                // 获取幼儿园数据
                $.sm(function (re1, err1) {
                    if (re1) {
                        utils.logWithCondition(true, new Error().stack, re1)
                        const gardenList = re1.map(item => ({ name: item.name, id: item.id }))
                        var gardenSelect = $('#gardenName');
                        gardenSelect.empty();
                        gardenSelect.append(`<option value="">请选择</option>`);
                        gardenList.forEach(item => {
                            gardenSelect.append(`<option value="${item.id}">${item.name}</option>`);
                        });
                        gardenSelect.val(params.gardenId || '');
                    } else {
                        jQuery.getparent().layer.msg('获取幼儿园列表失败', { icon: 5 });
                    }
                }, ["garden.list", $.msgwhere()], null, null, { async: false });

                // 获取年级数据
                $.sm(function (re1, err1) {
                    if (re1) {
                        utils.logWithCondition(true, new Error().stack, re1)
                        const gardenList = re1.map(item => item.grades)
                        const gradeSelect = $('#grade');
                        gradeSelect.empty();
                        gradeSelect.append(`<option value="">请选择</option>`);
                        gardenList.forEach(item => {
                            gradeSelect.append(`<option value="${item}">${item}年级</option>`);
                        });
                        gradeSelect.val(params.grade || '');
                    } else {
                        jQuery.getparent().layer.msg('获取年级列表失败', { icon: 5 });
                    }
                }, ["garden.gradeList", $.msgwhere({ regional: [params.regional] })], null, null, { async: false });
            }

            // 初始化班级数据
            const classSelect = $('#className');
            classSelect.empty();
            classSelect.append(`<option value="">请选择</option>`);

            $('#physicalAge').val(params.physicalAge || '');

            form.render('select');
        }

        function initEvent() {
            $("#btnsearch").click(function () {
                btnsearch();
            });
            form.on('select(gardenName)', function (data) {
                utils.logWithCondition(true, new Error().stack, data);
                getClassList()
            });
            form.on('select(grade)', function (data) {
                utils.logWithCondition(true, new Error().stack, data);
                getClassList()
            });
        }

        /**
         * 渲染班级select
         */
        function getClassList() {
            if (openMock) {
                return
            }
            let gardenId = $('#gardenName').val()
            let grade = $('#grade').val()
            if (!gardenId || !grade) {
                return
            }
            // 获取班级数据
            $.sm(function (re1, err1) {
                if (re1) {
                    utils.logWithCondition(true, new Error().stack, re1)
                    let classList = re1.map(item => item.claname)
                    const classSelect = $('#className');
                    classSelect.empty();
                    classSelect.append(`<option value="">请选择</option>`);
                    classList.forEach(item => {
                        classSelect.append(`<option value="${item}">${item}</option>`);
                    })
                } else {
                    jQuery.getparent().layer.msg(err1, { icon: 5 });
                }
                form.render('select');
            }, ["garden.classList", gardenId, params.endDate, $.msgwhere({ gardenId: [gardenId], grade: [grade] })], null, null, { async: false });
        }

        function initTable() {
            var arrcol = [
                { field: 'id', type: "numbers", title: '序号', width: 100, align: 'center' },
                { field: 'lecturename', title: '专家讲座', width: 300, align: 'center' },
                { field: 'childname', title: '幼儿姓名', width: 130, align: 'center' },
                {
                    field: 'childgender', title: '性别', width: 130, align: 'center',
                    templet: d => {
                        if (openMock) {
                            return d.childgender == '2' ? '女' : '男'
                        }
                        return d.childgender
                    }
                },
                { field: 'birthday', title: '出生日期', width: 200, align: 'center' },
                { field: 'gardenname', title: '幼儿园', width: 300, align: 'center' },
                { field: 'grade', title: '年级', width: 130, align: 'center' },
                { field: 'classname', title: '班级', width: 130, align: 'center' },
                {
                    field: 'parentrelation', title: '家长身份', width: 130, align: 'center'
                },
                {
                    field: 'parentname', title: '家长姓名', width: 130, align: 'center'
                },
                {
                    field: 'parentgender', title: '性别', width: 130, align: 'center',
                    templet: d => {
                        if (openMock) {
                            d.parentgender == '2' ? '女' : '男'
                        }
                        return d.parentgender
                    }
                },
                {
                    field: 'parentage', title: '年龄', width: 130, align: 'center'
                },
                { field: 'address', title: '家庭住址', width: 300, align: 'center' },
                { field: 'phone', title: '家长手机号', width: 200, align: 'center' },
                { field: 'devicetype', title: '设备类型', width: 130, align: 'center' },
                // {field: 'appOpenTime', title: '打开APP时间', width: 250, align: 'center'},
                {
                    field: 'viewtime', title: '查看通知时间', width: 200, align: 'center', templet: function (d) {
                        if (openMock) {
                            return d.viewtime
                        } else {
                            return d.wxtime ? d.wxtime : d.pctime
                        }
                    }
                },
                { field: 'registertime', title: '报名时间', width: 200, align: 'center' },
                { field: 'signtime', title: '签到时间', width: 200, align: 'center' }
            ];
            layui.table.render({
                elem: '#laytable',
                url: encodeURI($.getLayUrl() + "?" + $.getSmStr(["expertLecture.list_person"])),
                height: 'full-180',
                where: {
                    swhere: $.msgwhere(getswhere()),
                    fields: 'n.id',
                    types: 'asc'
                },
                cols: [arrcol],
                done: function (res, curr, count) {
                    // 表格加载完成回调
                },
                countNumberBool: true,
                even: true,
                page: true,
                limits: [30, 50, 100, 200],
                limit: 30,
                skin: 'row'
            });
            layui.table.on('tool(laytable)', function (obj) {
                // 工具条事件处理
            });
        }

        function btnsearch() {
            layui.table.reload('laytable', {
                page: { curr: 1 },
                where: {
                    swhere: $.msgwhere(openMock ? { ...getswhere(), isSearch: '1' } : getswhere())
                }
            });
        }

        function getswhere() {
            var objwhere = {};
            var startDate = $("#startDate").val() || params.startDate;
            var endDate = $("#endDate").val() || params.endDate;
            var lectureName = $("#lectureName").val();
            var gardenName = $("#gardenName").val();
            var parentGender = $("#parentGender").val();
            var isSuccess = $("#isSuccess").val() || '';
            var className = $("#className").val();
            var childGender = $("#childGender").val();
            var physicalAge = $("#physicalAge").val();
            var keyword = $("#keyword").val();
            var reservationStatus = $("#reservationStatus").val();
            var parentAge = $("#parentAge").val();
            var deviceType = $("#deviceType").val();
            var areaDistribution = $("#areaDistribution").val();
            var parentRelation = $("#parentRelation").val();
            var isView = $("#isView").val() || '';
            var isSign = $("#isSign").val() || '';
            var isRegister = $("#isRegister").val() || '';
            var grade = $("#grade").val();
            var title = $('#lectureTitle').val() || params.title

            if (startDate) {
                objwhere.startDate = [startDate];
            }
            if (endDate) {
                objwhere.endDate = [endDate];
            }
            if (grade) objwhere.grade = [grade];
            if (lectureName) {
                objwhere.lectureId = [lectureName];
            }
            if (parentGender) {
                objwhere.parentGender = [parentGender];
            }
            if (title) {
                objwhere.title = [title];
            }
            if (className) {
                objwhere.className = [className];
            }
            if (childGender) {
                objwhere.childGender = [childGender];
            }
            if (physicalAge) {
                objwhere.physicalAge = [physicalAge];
            }
            if (keyword) {
                objwhere.keyword = [keyword];
            }
            if (reservationStatus) {
                objwhere.reservationStatus = [reservationStatus];
            }
            if (deviceType) {
                objwhere.deviceType = [deviceType];
            }
            if (openMock) {
                if (areaDistribution) {
                    objwhere.areaDistribution = [areaDistribution];
                }
            } else {
                if (areaDistribution && areaDistribution != '5') {
                    objwhere.area = [areaDistribution[0], areaDistribution[2]]
                } else if (areaDistribution == '5') {
                    objwhere.area2 = [areaDistribution]
                }
            }

            if (parentRelation) {
                objwhere.parentRelation = [parentRelation.trim()];
            }

            if (openMock) {
                if (isSuccess) {
                    objwhere.isSuccess = [isSuccess];
                }
                if (isView) {
                    objwhere.isView = [isView];
                }
                if (isSign) {
                    objwhere.isSign = [isSign];
                }
                if (isRegister) {
                    objwhere.isRegister = [isRegister];
                }
                if (gardenName) {
                    objwhere.gardenName = [gardenName];
                }
                if (parentAge) {
                    objwhere.parentAge = [parentAge];
                }
            } else {
                utils.logWithCondition(openLog, new Error().stack, params)
                if (isSuccess == '1') {
                    objwhere.isnread = [1];
                } else if (isSuccess == '0') {
                    objwhere.isnread2 = [1];
                }

                if (isView == '1') {
                    objwhere.isView = [1];
                } else if (isView == '0') {
                    objwhere.isView2 = [1];
                }

                if (isSign == '1') {
                    objwhere.isSign = [1];
                } else if (isSign == '0') {
                    objwhere.isSign2 = [1];
                }

                if (isRegister == '1') {
                    objwhere.isRegister = [1];
                } else if (isRegister == '0') {
                    objwhere.isRegister2 = [1];
                }
                if (gardenName) {
                    objwhere.gardenId = [gardenName];
                }
                if (parentAge) {
                    if (+parentAge < 8) {
                        objwhere.parentAge = [parentAge * 10, parentAge * 10 + 9];
                    } else if (+parentAge == 8) {
                        objwhere.parentAge2 = [parentAge * 10];
                    }

                }
            }
            if (openMock) {
                // utils.logWithCondition(openLog, new Error().stack,objwhere)
                utils.logWithCondition(openLog, new Error().stack, { ...params, ...objwhere })
                return { ...params, ...objwhere };
            } else {
                utils.logWithCondition(openLog, new Error().stack, objwhere)
                return objwhere
            }
        }
    });
})
