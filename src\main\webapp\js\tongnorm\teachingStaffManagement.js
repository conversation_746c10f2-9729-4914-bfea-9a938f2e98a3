/**
 * Created by 李文鹤 on 2017/9/1.
 */
var table = null;
var objdata = {
    type: '',
    classno: ''
    , arrstate: [['请选择状态', ''], ['在职', 'in'], ['实习', 'practice'], ['离职', 'out'], ['停职', 'stop'], ['退休', 'retire']]
    , tableIns: null
    , swhere: ''//条件
    , swhere2: ''//条件
    , objpost: {}
    , arruntreated: []//存放没有处理的行序号
    , objfieldsort: {//顺序(按照模版的顺序记录序号，导入教职工用)
        'truename': 0,
        'sex': 1,
        'birthday': 2,//出生日期
        'ismarry': 3,//婚姻状况
        'nation': 4,//民族
        'mobile': 5,//手机号
        'gangwei': 6,//职务
        'emnumber': 7,//工号
        'intime': 8,//入职日期
        'workdate': 9,//参加工作日期
        'cardnumber': 10,//证件号码
        'depart': 11,//工作单位
        'email': 12,//邮箱
        'education': 13,//学历
        'fromgraduate': 14,//毕业院校
        'mandarinlevel': 15,//普通话等级一级二级三级甲等乙等
        // 'mandarinclass': 16,//普通话等级甲等乙等
        'teachernumber': 16,//教师资格证号码
        'nursenumber': 17,//保育员资格证号码
        'healthnumber': 18,//健康证号码
        'address': 19//住址
    }
    , objemp: {}
    // , objt: {}
    // , res: {}
    , curpage: 0
},
    objcols = {
        'emnumber': {field: 'emnumber', title: '工号', sort: false, minWidth: 100, align: 'center'}
        , 'truename': {field: 'truename', title: '姓名', sort: false, minWidth: 80, align: 'left'}
        , 'sex': {field: 'sex', title: '性别', sort: false, minWidth: 50, align: 'center'}
        , 'birthday': {field: 'birthday', title: '出生日期', sort: false, minWidth: 105, align: 'center'}
        , 'mobile': {field: 'mobile', title: '手机号', sort: false, minWidth: 120, align: 'center'}
        , 'gangwei': {field: 'gangwei', title: '职务', sort: false, minWidth: 90, align: 'left', templet: "#curPost"}
        // , 'cardnum':  {field: 'cardnum', title: '考勤卡', sort: false, minWidth: 90, align: 'left', templet: function (d) {
        //         var aImg = '<a href="#"><span lay-event="cardnumDetail">' + d.cardnum + '</span></a>';
        //         return aImg;
        //     }}
        // , 'class': {field: 'class', title: '所在班级', sort: false, minWidth: 105, align: 'left', templet: "#classname"}
        , 'intime': {field: 'intime', title: '入职日期', sort: false, minWidth: 105, align: 'center'}
        , 'curstate': {field: 'curstate', title: '当前状态', sort: false, minWidth: 90, align: 'left', templet: "#statusTpl"}
        , 'statechangedate': {field: 'statechangedate', title: '状态变更日期', sort: false, minWidth: 115, align: 'center'}
        , 'maxhealthdate': {field: 'maxhealthdate', title: '最近体检时间', sort: false, minWidth: 180, align: 'center'}
        , 'nexthealthdate': {field: 'nexthealthdate', title: '下次体检时间', sort: false, minWidth: 180, align: 'center', templet:function (d){
            return d.maxhealthdate ? getNextYear(d.maxhealthdate) : "";
            }}
        // , 'ktstate': {field: 'ktstate', title: 'PC系统开通状态', sort: false, minWidth: 140, align: 'left', templet: "#ktstate"}
        , 'cardnumber': {field: 'cardnumber', title: '证件号码', sort: false, minWidth: 180, align: 'center'}
        , 'nation': {field: 'nation', title: '民族', sort: false, minWidth: 100, align: 'center'}
        , 'email': {field: 'email', title: '邮箱', sort: false, minWidth: 160, align: 'center'}
        , 'education': {field: 'education', title: '学历', sort: false, minWidth: 80, align: 'center'}
        , 'address': {field: 'address', title: '住址', sort: false, minWidth: 155, align: 'center'}
        , 'depart': {field: 'depart', title: '工作单位', sort: false, minWidth: 155, align: 'center'}
        , 'healthnumber': {field: 'healthnumber', title: '健康证号', minWidth: 110, sort: false, align: 'center'}
        , 'nursenumber': {field: 'nursenumber', title: '保育员证号', minWidth: 110, sort: false, align: 'center'}
        , 'mandarinlevel': {field: 'mandarinlevel', title: '普通话等级', sort: false, minWidth: 105, align: 'center', templet: "#mandLevel"}//mandarinclass
        , 'workdate': {field: 'workdate', title: '参加工作日期', sort: false, minWidth: 115, align: 'center'}
        , 'teachernumber': {field: 'teachernumber', title: '教师资格证号', sort: false, minWidth: 115, align: 'center'}
        , 'fromgraduate': {field: 'fromgraduate', title: '毕业院校', sort: false, minWidth: 105, align: 'center'}
        , 'ismarry': {field: 'ismarry', title: '婚姻状况', width: 105, sort: false, minWidth: 105, align: 'center', templet: "#isMarry"}
        // , 'isnwx': {field: 'isnwx', title: '微信关注状态', width: 115, sort: false, minWidth: 105, align: 'center', templet: "#isnwx"}
    }, form;
layui.config({
    base: './js/',
    waitSeconds: 0
}).extend({
    // system: '../../sys/system'
});
layui.use(['table', 'form'], function () {
    objdata.swhere = $.msgwhere({});
    objdata.swhere2 = $.msgwhere({});
    form = layui.form;
    table = layui.table;
    initEvent();
    initData();
    // initMachine();
    $(window).on("resize", function () {
        // 重置列表高度
        if (objdata.tableIns) {
            objdata.tableIns.reload({
                height: 'full-' + ($("#alle-tab").offset().top + 20)
            });
        }
    }).trigger("resize");
});

/**
 * 初始化事件
 */
function initEvent() {
    $(document).off('click').click(function (event) {
        if (!(event.target.id == "btnimportemployee" || $(event.target).parents("#btnimportemployee").length > 0)) {
            $("#divslide").slideUp();
        }
        if (!(event.target.id == "bring_class" || $(event.target).parents("#bring_class").length > 0) && event.target.id != "selcols" && event.target.id != "imgcols" && event.target.id != "icols") {
            $("#bring_class").slideUp();
        }
    });
    //打印
    $("#btnprint").click(function () {
        jQuery.getparent().layer.open({
            type: 2,
            title: "打印数据验证",
            area: ['800px', '60%'],
            btn: ["申请", "验证", "关闭"],
            content: 'html/common/checksms.html?v=' + (Arg("v") || 1) + '&mid=' + (Arg("mid") || "") + "&smstype=teachingStaffManagement",
            success: function (e, q, r) {
                $(e).find(".layui-layer-btn-c .layui-layer-btn1").addClass("layui-layer-btn0");
            },
            yes: function (index, layero) {
                var w = layero.find('iframe')[0].contentWindow;
                w.$('#btnapply').trigger('click');
            },
            btn2: function (index, layero) {
                var w = layero.find('iframe')[0].contentWindow;
                w.$('#btnok').trigger('click', function (objsms) {
                    var objcheck = {
                        user1mobile: objsms.user1mobile,
                        user1smscode: objsms.user1smscode,
                        user2mobile: objsms.user2mobile,
                        user2smscode: objsms.user2smscode,
                        smstype: "teachingStaffManagement"
                    };
                    $.sm(function (re, err, obj) {
                        if (err) {
                            if (obj.user == 1) {
                                w.objdata.sms1code = "1";
                                w.$("#txtsms1code").blur();
                                w.objdata.sms1code = "";
                            } else if (obj.user == 2) {
                                w.objdata.sms2code = "1";
                                w.$("#txtsms2code").blur();
                                w.objdata.sms2code = "";
                            }
                            return jQuery.getparent().layer.msg(err, {icon: 5});
                        } else if (obj) {
                            $.getparent().layer.open({
                                type: 2, //此处以iframe举例
                                title: "教职工列表",
                                area: ['100%', '100%'],
                                shade: 0,
                                maxmin: true,
                                btnAlign: 'c',//按钮居中
                                content: 'tongnorm/teachingPrint.html?v=' + (GetQueryString("v") || 1) + '&mid=' + GetQueryString("mid") + '&emnumber=' + $("#emnumber").val() + '&truename=' + $("#truename").val() + '&mobile=' + $("#mobile").val() + '&gangwei=' + $("#gangwei").val() + '&class=' + objdata.classno + '&curstate=' + $("#selcurstate").val() + '&sort=' + $("#selsort").val(),
                                btn: ["打印预览", "关闭"],
                                success: function (index, layero) {
                                },
                                yes: function (index, layero) {
                                    layero.find('iframe')[0].contentWindow.$('#btprint').trigger('click');
                                }
                                //,btn2: function (index, layero) {
                                //    layero.find('iframe')[0].contentWindow.$('#btexcel').trigger('click');
                                //    return false;
                                //}
                            });
                            var objpostdata = {
                                user1id: objsms.user1id,
                                user1truename: objsms.user1truename,
                                user1mobile: objsms.user1mobile,
                                user1smscode: objsms.user1smscode,
                                user2id: objsms.user2id,
                                user2truename: objsms.user2truename,
                                user2mobile: objsms.user2mobile,
                                user2smscode: objsms.user2smscode,
                                type: "teachingStaffManagement",
                                notes: "教职工档案"
                            };
                            $.sm(function (re, err) {
                            }, ["wexportlog.add", JSON.stringify(objpostdata)]);
                            jQuery.getparent().layer.close(index);
                        } else {
                            jQuery.getparent().layer.msg('导出失败！', {icon: 5});
                        }
                    }, ["sms.checksms", JSON.stringify(objcheck)]);
                });
                return false;
            }
        });
    });
    //花名册打印
    $("#huaprint").click(function () {
        $.getparent().layer.open({
            type: 2, //此处以iframe举例
            title: "教职工花名册",
            area: ['100%', '100%'],
            shade: 0,
            maxmin: true,
            btnAlign: 'c',//按钮居中
            content: 'tongnorm/teachingRoster.html?v=' + (GetQueryString("v") || 1) + '&mid=' + GetQueryString("mid") + '&sort=' + $("#selsort").val(),
            btn: ["打印预览", "导出Excel", "关闭"],
            success: function (index, layero) {
                // layer.close(index);
            },
            yes: function (index, layero) {
                layero.find('iframe')[0].contentWindow.$('#btprint').trigger('click');
            },
            btn2: function (index, layero) {
                layero.find('iframe')[0].contentWindow.$('#btexcel').trigger('click');
                return false;
            }
        });
    });
    //添加
    $("#add").click(function () {
        objdata.type = 'add';
        var title = "添加信息";
        layerOpen(objdata.type, title);
    });
    $(".staff-con").keyup(function (event) {
        e = event || window.event;
        if (e.keyCode == 13) {
            $("#btnsearch").trigger('click');
        }
    });
    //搜索
    $("#btnsearch").click(function () {
        var objwhere = {};
        if ($("#emnumber").val()) {
            objwhere.emnumber = [$("#emnumber").val()];
        }
        if ($("#truename").val()) {
            objwhere.truename = [$("#truename").val()];
        }
        if ($("#mobile").val()) {
            objwhere.mobile = [$("#mobile").val()];
        }
        if ($("#gangwei").val() && $("#gangwei").val() != "全部") {
            objwhere.gangwei = [$("#gangwei").val()];
        }
        var curstate = $("#selcurstate").val();
        if (curstate) {
            objwhere.curstate = [curstate];
        }
        var str1 = '';
        var objw = {};
        // if (!objdata.classno || objdata.classno == "全部") {
        //     str1 = '';
        // } else {
        //     objw.classno = [objdata.classno];
        // }
        objdata.swhere = $.msgwhere(objwhere);
        objdata.swhere2 = $.msgwhere(objw);
        var sortype = $("#selsort").val(), strfield = "";
        if (sortype) {
            strfield = "length(emnumber) " + sortype + ",emnumber";
        }
        if (objdata.tableIns) {
            objdata.tableIns.reload({
                url: encodeURI($.getLayUrl() + "?" + $.getSmStr(["teachingStaffManagement.select", objdata.swhere2]))
                , where: { //设定异步数据接口的参数
                    swhere: objdata.swhere,
                    fields: strfield,
                    types: sortype
                }
                , cols: [reloadcol()]
                , page: {
                    curr: 1 // 重载后从第一页开始
                }
            });
        }
    });
    form.on('select(gangwei)', function (data) {//职务
        if (data.value == "2") {//教师
            // $("#lb_class").css('display', 'inline-block').show();
            // form.render('select');
        } else {//非教师
            // $("#lb_class").hide();
            $("#selclass option:first").prop("selected", 'selected');
            form.render('select');
            objdata.classno = "全部";
        }
    });
    form.on('select(selsort)', function (data) {//排序
        initData();
    });
    form.on('select(selclass)', function (data) {//选择班级
        objdata.classno = data.value;
    });
    //从教师表
    $("#btnimportemployee").click(function () {
        $("#divslide").slideToggle();
    });
    //从班级教师导入
    $("#fromteacher").click(function () {
        $("#divslide").slideToggle();
        $.getparent().layer.open({
            type: 2, //此处以iframe举例
            title: '导入教师到教职工',
            area: ['600px', '95%'],
            btnAlign: 'c', //按钮居中
            shadeClose: false,
            content: 'tongnorm/teachingImportPeople.html?v=' + GetQueryString("v") + '&type=1&mid=' + GetQueryString("mid"),
            success: function (layero) {
                //                 layer.setTop(layero); //重点2
            },
            btn: ['确定', '取消'], //只是为了演示
            yes: function (index, layero) {
                layero.find('iframe')[0].contentWindow.$("#btsave").trigger('click');
            }
        });
    });
    //从开通用户导入
    $("#fromuser").click(function () {
        $("#divslide").slideToggle();
        $.getparent().layer.open({
            type: 2, //此处以iframe举例
            title: '导入用户到教职工',
            area: ['600px', '95%'],
            btnAlign: 'c', //按钮居中
            shadeClose: false,
            content: 'tongnorm/teachingImportPeople.html?v=' + GetQueryString("v") + '&type=2&mid=' + GetQueryString("mid"),
            success: function (layero) {
                //                 layer.setTop(layero); //重点2
            },
            btn: ['确定', '取消'], //只是为了演示
            yes: function (index, layero) {
                layero.find('iframe')[0].contentWindow.$("#btsave").trigger('click');
            }
        });
    });
    //导入模版下载
    $("#btndownmodel").click(function () {
        var a = document.createElement("a");
        a.href = "../templates/usertemplete/employeemodel.xls";
        a.download = "教职工导入模版";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        return;
    });
    //批量导入
    $("#btnimport").click(function () {//
        jQuery.getparent().objdata.imreportteacher = jQuery.getparent().layer.open({
            type: 2,
            title: '导入教职工信息',
            shadeClose: false,
            area: ['500px', '350px'],
            content: "tongnorm/indata.html?v=" + (Arg("v") || 1) + "&mid=" + Arg("mid") + "&type=employee",
            btn: ["确定", "关闭"],
            success: function (layero, index) {
                //			    			jQuery.getparent().objdata.infoframe = layero.find('iframe')[0]; //得到iframe页的窗口对象，执行iframe页的方法：iframeWin.method();
            }, yes: function (index, layero) {
                var childpage = layero.find('iframe')[0].contentWindow;
                var src = childpage.$('#src').val();
                if (!src) {
                    jQuery.getparent().layer.msg("请先上传excel文件！", {icon: 5});
                    return;
                }
                var name = childpage.$("#txtfname").val();
                if (!name) {
                    return jQuery.getparent().layer.msg("请先选择要导入的数据！");
                }
                importExcel(src, name);
            }
        });
    });
    $("#selcols").click(function () {
        $(".bring_class").slideToggle();
    });
    $(".txt_box").click(function () {//勾选列表项
        initData();
        // objdata.tableIns.reload({
        //     // elem: '#tabelList',
        //     cols: [reloadcol()]
        // });
    });
    // $("#btnsetedu").click(function () {//教科研老师设置
    //     jQuery.getparent().layer.open({
    //         type: 2,
    //         title: "设置教科研老师",
    //         shadeClose: false,
    //         area: ['800px', '580px'],
    //         content: "tongpe/eduteacherSet.html?v=" + (Arg("v") || 1) + "&mid=" + Arg("mid"),
    //         btn: ['保存', '取消'],
    //         success: function (layero, index) {
    //             jQuery.getparent().objdata.eduteacherframe = layero.find('iframe')[0]; //得到iframe页的窗口对象，执行iframe页的方法：iframeWin.method();
    //         }, yes: function (index, layero) {
    //             layero.find('iframe')[0].contentWindow.$("#btnsave").trigger("click");
    //         }
    //     });
    // });
    // $("#btnvoice").click(function () {//考勤机语音播报
    //     jQuery.getparent().layer.open({
    //         type: 2,
    //         title: "设置考勤机语音播报名称",
    //         shadeClose: false,
    //         area: ['800px', '580px'],
    //         content: "tongnorm/teacherVoice.html?v=" + (Arg("v") || 1) + "&mid=" + Arg("mid"),
    //         btn: ['确定', '取消'],
    //         success: function (layero, index) {
    //             jQuery.getparent().objdata.teacherVoice = layero.find('iframe')[0]; //得到iframe页的窗口对象，执行iframe页的方法：iframeWin.method();
    //         }, yes: function (index, layero) {
    //             layero.find('iframe')[0].contentWindow.savedata();
    //         }
    //     });
    // });
    // $("#btnautocard").click(function () {//自动分配卡号
    //     jQuery.getparent().layer.confirm("是否批量分配教职工人员的虚拟卡？", function (r) {
    //         jQuery.getparent().layer.confirm("此操作会影响考勤识别功能,请谨慎操作!", function (r2) {
    //             jQuery.getparent().layer.load();
    //             var mobileArr=[];
    //             var allemp = getAllemployee();
    //             for (var i = 0; i < allemp.length; i++) {
    //                 var onemp = allemp[i];
    //                 if (onemp.mobile) {
    //                     mobileArr.push(onemp.mobile);
    //                 }
    //             }
    // var allface = getAllFaceUrl(mobileArr);
    // for (var i = 0; i < allemp.length; i++) {
    //     var onemp = allemp[i];
    //     for (var j = 0; j < allface.length; j++) {
    //         var oneface = allface[j];
    //         if(onemp.mobile==oneface.mobile){
    //             onemp['facePicUrl'] = oneface.photopath ? ossPrefix + oneface.photopath.split(",")[0] : "";
    //             onemp['openid'] = oneface.openid;
    //             break;
    //         }
    //     }
    // }

    // var empArr=[];
    // for (var i = 0; i < allemp.length; i++) {
    //     var onemp = allemp[i];
    //     if(!onemp.openid){
    //         continue;
    //     }
    //     empArr.push({
    //         "empid": onemp.id,
    //         "empname": onemp.truename,
    //         "openid": onemp.openid,
    //         "facePicUrl": onemp.facePicUrl
    //     });
    // }
    // toBindCard(empArr);
    //             jQuery.getparent().layer.close(r2);
    //         });
    //         jQuery.getparent().layer.close(r);
    //     });
    // });
}


function getAllemployee() {
    var allemp = [];
    $.sm(function (re, err) {
        if (re) {
            allemp = re || [];
        }
    }, ["teachingStaffMessage.getallemployee"], null, null, {async: false});
    return allemp;
}
// function getAllFaceUrl(mobileArr) {
//     var allface=[];
//     $.sm(function (re, err) {
//         if (re) {
//             allface = re || [];
//         }
//     }, ["teachingStaffMessage.getallemployeeFace", $.msgwhere({mobiles: $.msgpJoin(mobileArr)})], null, null, {async: false});
//     return allface;
// }

function reloadcol() {
    var arrcols = [];
    $(".txt_box").each(function () {
        var colv = $(this).attr('colv');
        if ($(this).is(':checked')) {
            arrcols.push(objcols[colv]);
        }
    });
    arrcols.push(
        {
            fixed: 'right', minWidth: 260, title: '操作', align: 'center',
            templet: function (d) {
                var arrbtnhtml = [];
                arrbtnhtml.push('<a class="default-btn" style="background: #00b793;" lay-event="detail">查看</a>');
                arrbtnhtml.push('<a class="default-btn" style="background: #49a1ff;" lay-event="edit"><i class="iconfont icon_edit3"></i>编辑</a>');
                arrbtnhtml.push('<a class="default-btn" style="background: #49a1ff;" lay-event="changerstatus"><i class="iconfont icon_alternations"></i>变更</a>');
                arrbtnhtml.push('<a lay-event="del"><img src="../images/newicon/btn_delete.png" style="margin: 7px 2px;vertical-align: top;width: 17px;"></a>');//<img src="../images/newicon/btn_delete.png" style="margin: 3px 2px;vertical-align: top;width: 17px;">
                return arrbtnhtml.join('');
            }
        }//, toolbar: '#btncz'
    );
    return arrcols;
}

// //导入教师到教职工表
// function importteacher() {
//     $.sm(function (re, err) {
//         if (err) {
//
//         } else {
// //	    	$('#btcancel').trigger('click');
// //        	initdata();
// //            $.getparent().layer.closeAll('loading');
// //            if(emid){
//             $.getparent().layer.msg("导入成功！", {icon: 1});
// //            }
// //            $.getparent().layer.close($.getparent().objdata.choteacher);
//         }
//     }, ["teachingStaffMessage.selteacher"]);
// }

/**
 * 初始化数据
 */
function initData() {
    //处理当前状态的选择查询条件
    var arrstate = objdata.arrstate,
        strhtml = [];
    for (var i = 0; i < arrstate.length; i++) {
        strhtml.push('<option value="' + arrstate[i][1] + '" ' + (arrstate[i][1] == 'in' ? 'selected="selected"' : "") + '>' + arrstate[i][0] + '</option>');
    }
    $("#selcurstate").html(strhtml.join(''));
    //职务start
    var arrpost = jQuery.getparent().objdata.arrpost,//职务
        arrpt = [];
    arrpt.push('<option value="全部">全部</option>');
    for (var i = 0; i < arrpost.length; i++) {
        arrpt.push('<option value="' + arrpost[i][1] + '">' + arrpost[i][0] + '</option>');
        objdata.objpost[arrpost[i][1]] = arrpost[i][0];
    }
    $("#gangwei").html(arrpt.join(''));
    form.render('select');
    // //职务end
    // $.sm(function (re, err) {
    //     if (re) {
    //         var html = [];
    //         for (var i = 0; i < re.length; i++) {
    //             html.push('<option value="' + re[i][1] + '">' + re[i][0] + '</option>');
    //         }
    //         html.unshift('<option>全部</option>')
    //         $("#selclass").html(html.join(''));
    //         form.render('select');
    //     } else {
    //         $.getparent().layer.msg('查询班级出错！');
    //     }
    // }, ["medicine_management.classname"]);
    var sortype = $("#selsort").val(), strfield = "";
    if (sortype) {
        strfield = "length(emnumber) " + sortype + ",emnumber";
    }
    var arrCols = reloadcol();
    objdata.tableIns = table.render({
        elem: '#tabelList'
        , url: encodeURI($.getLayUrl() + "?" + $.getSmStr(["teachingStaffManagement.select", $.msgwhere({curstate: ["in"]})]))
        , where: {swhere: objdata.swhere, fields: strfield, types: sortype}
        , height: 'full-' + ($("#alle-tab").offset().top + 20)
        , cols: [arrCols]
        , done: function (res, curr, count) {
            //如果是异步请求数据方式，res即为你接口返回的信息。
            //如果是直接赋值的方式，res即为：{data: [], count: 99} data为当前页数据、count为数据总长度
            objdata.curpage = curr;
            var arrmobile = [];
            arrempinfo = res.data;
            for (var i = 0; i < arrempinfo.length; i++) {
                objdata.objemp[arrempinfo[i].id] = arrempinfo[i];
                arrmobile.push(arrempinfo[i].mobile);
            }
            if (count == 0) {
                $(".layui-none").html('<div style="background: url(../plugin/flexigrid/images/kb.png) center center no-repeat;width: 100%;height: 100%"></div>').css({"height": "100%"});
            }
            // if(arrmobile.length > 0){
            //     $.sm(function (re, err) {
            //         if(re){
            //             for (var i = 0; i < re.length; i++) {
            //                 $("#emp" + re[i].mobile).prop('src','../image/wx.png');
            //             }
            //         }
            //     },["select.wxbangding", $.msgwhere({arrmobile: $.msgpJoin(arrmobile)})]);
            // }
        }
        // , page: true //是否显示分页
        , page: {
            layout: ['count', 'limit', 'prev', 'page', 'next', 'skip'] //自定义分页布局
            //,curr: 5 //设定初始在第 5 页
            // ,groups: 5 //只显示 5 个连续页码
            // ,first: true //显示首页
            // ,last: true //显示尾页

        }
        , limits: [5, 10, 15]
        , limit: 10 //每页默认显示的数量
    });
    table.on('tool(test)', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;
        var tr = obj.tr;
        switch (layEvent) {
            case "detail":
                objdata.type = 'detail';
                var title = "查看信息";
                var peopleId = data.id;
                layerOpen(objdata.type, title, peopleId, data.curstate);
                break;
            case "del":
                var peopleId = data.id;
                var name = data.truename;
                var claname = data.class;
                var wxsstate = data.wxstate;
                var str = '';
                // if (claname && (!wxsstate || wxsstate == 0)) {
                //     str = "【"+ name + "】已关联过班级，确认继续删除吗？"
                // } else if (!claname && wxsstate > 0) {
                //     str = "【"+ name + "】创建过系统账号，确认继续删除吗？"
                // } else if (claname && wxsstate > 0) {
                //     str = "【"+ name + "】创建过系统账号并已关联过班级，确认继续删除吗？"
                // } else if (!claname && (!wxsstate || wxsstate == 0)) {
                str = "您确定要删除【" + name + "】职工吗？";
                // }
                // str = (str ? "，" + str : "确认是否真的要删除" + name + "？");
                $.getparent().layer.confirm('<b style="color: red;font-size: 14px;">' + str + "</b>", function (index) {
                    var arrdelsm = [["teachingStaffManagement.delete", peopleId]];
                    $.sm(function (re, err) {
                        if (err) {
                            $.getparent().layer.msg('删除失败!' + err);
                        } else {
                            $.getparent().layer.msg('删除成功');
                            location.reload();
                        }
                    }, arrdelsm);
                    $.getparent().layer.close(index);
                });
                break;
            case "edit":
                objdata.type = 'edit';
                var title = "编辑信息";
                var peopleId = data.id;
                layerOpen(objdata.type, title, peopleId);
                break;
            case "changerstatus":
                objdata.type = 'changerstatus';
                var title = "变更状态";
                var peopleId = data.id;
                layerOpen(objdata.type, title, peopleId);
                break;
            case "openuser":
                jQuery.getparent().layer.open({
                    type: 2,
                    title: '添加用户信息',
                    skin: 'layui-layer-rim', //加上边框
                    area: ['880px', '450px'],
                    content: 'html/w_usersedit.html?v=' + (Arg("v") || 1) + '&mid=' + (Arg("mid") || "") + "&employeeid=" + data.id + "&truename=" + data.truename + "&mobile=" + data.mobile + "&gangwei=" + data.gangwei,
                    success: function (layero, index) {
                        jQuery.getparent().objdata.usersedit = layero.find('iframe')[0]; //得到iframe页的窗口对象，执行iframe页的方法：iframeWin.method();
                    }
                });
                break;
            // case "cardnumDetail": {
            //     if (!data.cardnum) {
            //         return jQuery.getparent().layer.msg('暂无考勤卡');
            //     }
            //     jQuery.getparent().layer.open({
            //         type: 2,
            //         title: "正在查看" + (data.truename) + "的考勤卡信息",
            //         shadeClose: false,
            //         btnAlign: 'c', //按钮居中
            //         area: ['900px', '95%'],
            //         content: "yejcxx/attecardlistemp.html?v=" + (Arg("v") || 1)  + "&mid=" + Arg("mid")+"&mobile="+data.mobile+"&empid="+ data.id,
            //         btn: ['关闭'],
            //         success: function (layero, index) {
            //             //    			jQuery.getparent().objdata.jgjsframe = layero.find('iframe')[0]; //得到iframe页的窗口对象，执行iframe页的方法：iframeWin.method();
            //         }/*, yes: function (index, layero) {
            //             layero.find('iframe')[0].contentWindow.$("#btnok").trigger('click',function () {
            //                 initdata();
            //                 $.getparent().layer.close(index);
            //             })
            //         }*/
            //     });
            //     break;
            // }
        }
    });
}

/**
 * 开通完用户，回调事件
 */
function resetUser() {
    $(".layui-laypage-btn").trigger("click");
}

//功能：获得导入excel教职工信息并处理导入
function importExcel(src, name) {
    jQuery.getparent().layer.load();
    $.sm(function (re, err) {
        if (err) {
            jQuery.getparent().layer.msg(err);
            jQuery.getparent().layer.closeAll("loading");
        } else {
            // var objstud = tjsetting.objstud;
            re = JSON.parse(re);
            if (re && re.length > 0) {
                var arrpm = [];
                var fsort = objdata.objfieldsort;
                var arrmobile = [], arrexlmobiled = [], arrfield = ['truename', 'sex', 'birthday', 'ismarry', 'nation', 'mobile', 'gangwei', 'emnumber', 'intime', 'workdate', 'cardnumber', 'depart', 'email', 'education', 'fromgraduate', 'mandarinlevel', 'teachernumber', 'nursenumber', 'healthnumber', 'address']
                    // , arrnum = ['ismarry','mandarinlevel']
                    , arrlevel = ['一级甲等', '一级乙等', '二级甲等', '二级乙等', '三级甲等', '三级乙等'];
                for (var i = 0; i < re.length; i++) {
                    var arrcol = [], arrvalue = [], flag = true, mobile = '';
                    //没有不处理数据，姓名，出生日期，手机号，职务，入职时间，证件号必须有
                    if (re[i][fsort['truename']] && re[i][fsort['sex']] && re[i][fsort['birthday']] && re[i][fsort['mobile']] && re[i][fsort['gangwei']] && re[i][fsort['intime']]) {// && re[i][fsort['cardnumber']]
                        var arrerrtip = [];
                        for (var j = 0; j < arrfield.length; j++) {
                            if (fsort[arrfield[j]] === 0 || fsort[arrfield[j]]) {
                                var valu = re[i][fsort[arrfield[j]]];
                                if (arrfield[j] == 'birthday') {//判断是不是日期格式
                                    valu = valu.replace(/\./g, '-').replace(/\//g, '-');
                                    if (!parent.isDate(valu)) {
                                        arrerrtip.push("出生日期有误");
                                        flag = false;
                                        continue;
                                    }
                                    if (!parent.DateCompare(parent.tongsetting.cursysdate, valu)) {
                                        arrerrtip.push("出生日期大于当前日期");
                                        flag = false;
                                        continue;
                                    }
                                }
                                if (arrfield[j] == 'intime') {//判断是不是日期格式
                                    re[i][fsort['birthday']] = re[i][fsort['birthday']].replace(/\./g, '-').replace(/\//g, '-');
                                    valu = valu.replace(/\./g, '-').replace(/\//g, '-');
                                    if (!parent.isDate(valu)) {
                                        arrerrtip.push("入职日期有误");
                                        flag = false;
                                        continue;
                                    }
                                    if (!parent.isDate(re[i][fsort['birthday']])) {
                                        flag = false;
                                        continue;
                                    }
                                    if (re[i][fsort['birthday']] && parent.DateCompare(re[i][fsort['birthday']], valu)) {
                                        arrerrtip.push("出生日期不能大于入职日期");
                                        flag = false;
                                        continue;
                                    }
                                }
                                if (arrfield[j] == 'workdate' && valu && !parent.isDate(valu)) {//判断是不是日期格式
                                    arrerrtip.push("参加工作日期有误");
                                    flag = false;
                                    continue;
                                }
                                if (arrfield[j] == 'mobile') {//手机号
                                    if (!mobileCheck(valu)) {
                                        arrerrtip.push("手机号有误");
                                        flag = false;
                                        continue;
                                    }
                                    if ($.inArray(valu, arrmobile) >= 0) {//excel中有重复的
                                        arrerrtip.push("手机号" + valu + "与其他行手机号有重复");
                                        flag = false;
                                        continue;
                                    }
                                    mobile = valu;
                                    arrmobile.push(mobile);
                                }
                                if (arrfield[j] == 'ismarry') {//婚姻状况
                                    arrcol.push({column: [arrfield[j]]});
                                    if (valu == '已婚') {
                                        arrvalue.push({columnnum: [1]});
                                    } else {
                                        arrvalue.push({columnnum: [0]});
                                    }
                                    continue;
                                }
                                if (arrfield[j] == 'mandarinlevel') {//普通话等级
                                    if ($.inArray(valu, arrlevel) >= 0) {
                                        arrcol.push({column: [arrfield[j]]});
                                        arrcol.push({column: ['mandarinclass']});
                                        if (valu == '一级甲等') {
                                            arrvalue.push({columnnum: [1]});
                                            arrvalue.push({columnnum: [1]});
                                        } else if (valu == '一级乙等') {
                                            arrvalue.push({columnnum: [1]});
                                            arrvalue.push({columnnum: [2]});
                                        } else if (valu == '二级甲等') {
                                            arrvalue.push({columnnum: [2]});
                                            arrvalue.push({columnnum: [1]});
                                        } else if (valu == '二级乙等') {
                                            arrvalue.push({columnnum: [2]});
                                            arrvalue.push({columnnum: [2]});
                                        } else if (valu == '三级甲等') {
                                            arrvalue.push({columnnum: [3]});
                                            arrvalue.push({columnnum: [1]});
                                        } else if (valu == '三级乙等') {
                                            arrvalue.push({columnnum: [3]});
                                            arrvalue.push({columnnum: [2]});
                                        }
                                    }
                                    continue;
                                }
                                if (arrfield[j] == 'gangwei') {
                                    var objpost = getobjfromarr(jQuery.getparent().objdata.arrpost);
                                    valu = objpost[valu];
                                    if (!valu) {
                                        arrerrtip.push("请选择正确的职务！");
                                        flag = false;
                                        continue;
                                    }
                                }
                                if (valu) {
                                    arrcol.push({column: [arrfield[j]]});
                                    arrvalue.push({columnstr: [valu]});
                                }
                            }
                        }
                        if (arrerrtip.length > 0) {
                            objdata.arruntreated.push("第" + (i + 2) + "行" + arrerrtip.join('、'));
                        }
                        if (flag) {
                            arrcol.push({column: ['curstate']});
                            arrvalue.push({columnstr: ["in"]});
                            // arrcol.push({column: ['yeyid']});
                            // arrvalue.push({columnnum: [jQuery.getparent().objdata.yeyinfo.id]});
                            arrpm.push(["teachingstaffmanagement.addemployee", $.msgArrwhere(arrcol), $.msgArrwhere(arrvalue), mobile, jQuery.getparent().objdata.yeyinfo.id]);
                        }
                    } else {
                        objdata.arruntreated.push("第" + (i + 2) + "行必填项的数据不能为空！");
                    }
                }
                if (arrpm.length > 0) {
                    // 系统中如果存在该手机号的员工将不替换导入该员工，如果手机号填错
                    // validitymoblie(arrmobile,function (arrm) {//手机号验证
                    //     var tip = arrm.length > 0 ? "手机号为" + arrm.join(",") + "的教职工在系统中已存在，是否需要覆盖导入？<br/>" : "";
                    //     tip += objdata.arruntreated.length > 0 ? "导入数据中存在如下问题：" + objdata.arruntreated.join(",") + "，这些职工信息将不会被导入" : "";
                    //     var objbtn = {btn: ['确定导入','取消']};
                    //     if(arrm.length > 0){
                    //         objbtn = {btn: ['覆盖已有职工导入','不覆盖导入','取消']};
                    //     }
                    // });
                    validitymoblie(arrmobile, function (arrm) {
                        var tip = arrm.length > 0 ? " &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;手机号为" + arrm.join(",") + "的教职工在系统中已存在，导入后这些职工信息将被覆盖？<br/>" : "";
                        tip += objdata.arruntreated.length > 0 ? " &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;导入数据存在如下问题：" + objdata.arruntreated.join("，") + "，这些职工信息将不会被导入" : "";
                        if (tip) {
                            jQuery.getparent().layer.closeAll("loading");
                            jQuery.getparent().layer.confirm("<b style='color: red;'>信息提示：<br/>" + tip + "</b>", {btn: ['确定导入', '取消']}, function (index) {
                                jQuery.getparent().layer.load();
                                employeeimport(arrpm, function () {
                                    jQuery.getparent().layer.close(index);
                                });
                            }, function (index) {
                                objdata.arruntreated = [];
                                jQuery.getparent().layer.close(index);
                            });
                        } else {
                            employeeimport(arrpm);
                        }
                    });
                } else {
                    if (objdata.arruntreated.length > 0) {
                        jQuery.getparent().layer.alert(objdata.arruntreated.join('，'));
                        objdata.arruntreated = [];
                    }
                    jQuery.getparent().layer.closeAll("loading");
                }
            } else {
                jQuery.getparent().layer.msg("没有需要导入的数据,请重新上传文件！", {icon: 5});
                jQuery.getparent().layer.closeAll("loading");
            }
        }
    }, ["tjdjedit.rtnToExcel", src, name]);
}

/**
 * 导入教职工
 * @param arrpm
 */
function employeeimport(arrpm, ck) {
    if (arrpm.length > 0) {
        $.sm(function (re, err) {
            if (err) {
                $.getparent().layer.msg(err, {icon: 5});
                jQuery.getparent().layer.closeAll("loading");
            } else if (re && re[0]) {
                objdata.arruntreated = [];
                $.getparent().layer.msg("导入成功！");
                ck && ck();
                location.reload();
                jQuery.getparent().layer.close(jQuery.getparent().objdata.imreportteacher);
                jQuery.getparent().layer.closeAll("loading");
            } else {
                jQuery.getparent().layer.closeAll("loading");
            }
        }, arrpm);
    } else {
        $.getparent().layer.msg("没有需要导入的数据！");
    }
}

/**
 * 通过手机号验证该员工是否存在
 * @param arrmobile
 * @param ck
 */
function validitymoblie(arrmobile, ck) {
    $.sm(function (re1, err1) {//根据手机号验证重复
        if (err1) {
            $.getparent().layer.msg(err1, {icon: 5});
        } else {
            var arrhadmobile = [];
            for (var k = 0; k < re1.length; k++) {
                arrhadmobile.push(re1[k][1]);
            }
            ck && ck(arrhadmobile);
        }
    }, ["teachingstaffmessage.validatemobile", $.msgwhere({arrmobile: $.msgpJoin(arrmobile)}), $.msgwhere({})]);
}

/**
 * 手机号验证
 * @param mobile 文本输入号码
 * @returns {Boolean}
 */
function mobileCheck(mobile) {
    var first = mobile.charAt(0);
    if (first == 1 && mobile.length == 11) {
        return true;
    } else {
        return false;
    }
}

/*
 * 功能：从数组得到对象
 */
function getobjfromarr(arr) {
    var obj = {};
    for (var i = 0; i < arr.length; i++) {
        obj[arr[i][0]] = arr[i][1];
    }
    return obj;
}

function GetQueryString(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return unescape(r[2]);
    return null;
}

function layerOpen(type, title, peopleId, curstate) {
    var arr = [], strpage = "teachingStaffMessage", area = ['900px', '95%'];
    if (type == "add" || type == "edit" || type == "changerstatus") {
        arr = ['保存', '关闭'];
        if (type == "changerstatus") {
            strpage = "teachingStaffChangerStatus";
            area = ['420px', '440px'];
        }
    } else if (type == "detail") {
        arr = ['关闭'];
    }
    var sortype = $("#selsort").val(), strfield = "";
    if (sortype) {
        strfield = "emnumber";
    }
    var objopen = {
        type: 2, //此处以iframe举例
        title: title,
        area: area,
        btnAlign: 'c', //按钮居中
        shadeClose: false,
        content: 'tongnorm/' + strpage + '.html?v=' + GetQueryString("v") + '&type=' + type + (peopleId ? '&peopleId=' + peopleId : "") + '&mid=' + GetQueryString("mid") + (curstate ? '&curstate=' + curstate : "") + (sortype ? "&sortype=" + sortype + "&sortfield=" + strfield : ""),
        btn: arr,
        yes: function (index, layero) {
            // layer.msg('保存');
            if (type == "add" || type == "edit" || type == "changerstatus") {
                layero.find('iframe')[0].contentWindow.savrForm(function () {
                    $.getparent().layer.msg("保存成功！");
                    $.getparent().layer.close(index);//暂时关掉
                });
            } else if (type == "detail") {
                $.getparent().layer.close(index);//暂时关掉
            }
        },
        btn2: function () {
            // layer.msg('关闭');
            // layer.closeAll();
        },
        success: function (layero) {
            //             layer.setTop(layero); //重点2
        }
    };
    $.getparent().layer.open(objopen);
}

//获取链接中的参数
function GetQueryString(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return unescape(r[2]);
    return null;
}