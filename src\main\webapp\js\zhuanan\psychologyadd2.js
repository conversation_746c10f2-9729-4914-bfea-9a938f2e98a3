﻿/*
日期：
作者：
功能：
*/
var fpsetting = {
    objarrstud: {}, //当前班级学生信息
    curstudent: []//学生下拉框
    , firstrecord: {}//开始坚持记录
    , objquestion: {
        "0.03": {
            "0.03-1": "对很大声音有反应",
            "0.03-2": "逗引时发音或微笑",
            "0.03-3": "注视人脸,不追视移动人或物品",
            "0.03-4": "俯卧时抬头"
        },
        "0.06": {
            "0.06-1": "发音少,笑出声",
            "0.06-2": "伸手抓物",
            "0.06-3": "紧握拳松",
            "0.06-4": "扶坐"
        },
        "0.08": {
            "0.08-1": "听到声音应答",
            "0.08-2": "区分生人和熟人",
            "0.08-3": "双手间传递玩具",
            "0.08-4": "独坐"
        },
        "1": {
            "1-1": "呼唤名字反应",
            "1-2": "模仿“再见”或“欢迎”动作",
            "1-3": "用拇食指对捏小物品",
            "1-4": "扶物站立"
        },
        "1.06": {
            "1.06-1": "有意识叫“爸爸”或“妈妈”",
            "1.06-2": "按要求指人或物",
            "1.06-3": "与人目光交流",
            "1.06-4": "独走"
        },
        "2": {
            "2-1": "说3个物品的名称",
            "2-2": "按吩咐做简单的事情",
            "2-3": "用勺吃饭",
            "2-4": "扶栏杆上楼梯/台阶"
        },
        // "2.06": {
        //     "2.06-1": "不会说2-3个字的短语",
        //     "2.06-2": "兴趣单一、刻板",
        //     "2.06-3": "不会示意大小便",
        //     "2.06-4": "不会跑"
        // },
        "2.5": {
            "2.5-1": "说2-3个字的短语",
            "2.5-2": "兴趣单一、刻板",
            "2.5-3": "示意大小便",
            "2.5-4": "跑"
        },
        "3": {
            "3-1": "说自己的名字",
            "3-2": "玩“拿棍当马骑”等假想游戏",
            "3-3": "模仿画圆",
            "3-4": "双脚跳"
        },
        "4": {
            "4-1": "说带形容词的句子",
            "4-2": "按要求等待和轮流",
            "4-3": "独立穿衣",
            "4-4": "单脚站立"
        },
        "5": {
            "5-1": "简单叙说事情经过",
            "5-2": "知道自己的性别",
            "5-3": "用筷子吃饭",
            "5-4": "单脚跳"
        },
        "6": {
            "6-1": "表达自己的感受或想法",
            "6-2": "玩角色扮演的集体游戏",
            "6-3": "画方形",
            "6-4": "奔跑"
        }
    }
};

require.config({
    paths: {
        sys: "../../sys/system",
        jquery: "../../sys/jquery",
        layui: "../../layui-btkj/layui",
        editselect: "../../plugin/editselect/js/editselect"
    }, shim: {
        "editselect": {
            deps: ["jquery"]
        }
//    		contextmenu:["jquery"]
    },
    waitSeconds: 0
});
var parentobj = null;
require(["jquery"], function () {
    require(["editselect", "sys", 'layui'], function () {
        parentobj = jQuery.getparent().$("#win_" + Arg('mid')).children('iframe')[0].contentWindow;
        layui.use(["laydate", 'form'], function () {
            $("#txtcheckdate").val(Arg("checkdate") || parentobj.getDateByStr(jQuery.getparent().objdata.curmoment.format('YYYY-MM-DD')));
            initclass();
            initstud();
            initEvent();
        });
    });
});

/**
 * 初始化事件
 */
function initEvent() {
    $('#iconcheckdate').click(function (e) {
        var show = true;
        if (document.activeElement.id == "txtcheckdate") {
            show = false;
        }
        var objdate = {
            elem: '#txtcheckdate',
            show: show, //直接显示
            needrender: true,
            isInitValue: true,
            showBottom: true,
            done: function () {
                $('#txtcheckdate').blur();
                initdata(1);
            }
        }
        layui.laydate.render(objdate);
        return false;
    });
    $('#txtcheckdate').focus(function (e) {
        setTimeout(function () {
            $('#iconcheckdate').trigger("click");
        }, 100);
    });
}

/*
功能：初始化下拉框
*/
function initclass() {
    $('#selclas').editableSelect({
        bg_iframe: true,
        isreadonly: Arg("classno") ? 1 : 0,
        isdisabled: Arg("classno") ? 1 : 0,
        arrdata: parentobj.classbyexclude(2, parentobj.tongsetting.objsbqClass),
        defvalue: Arg("classno") || parentobj.classbyexclude(2, parentobj.tongsetting.objsbqClass)[0][1],
        width: '100px',
        onSelect: initstud, //选择事件
        case_sensitive: false, // If set to true, the user has to type in an exact
        items_then_scroll: 10 // If there are more than 10 items, display a scrollbar
    });
};

/*
功能:读取学生信息;
*/
function initstud() {
    var clas = $("#selclas").val();
    var gouptime = parentobj.tongsetting.cursysdate;//parentobj.tongsetting.objsbqClass[clas][4];
    $.sm(function (res, err) {
        if (err) {
            jQuery.getparent().layer.msg(err, {icon: 5});
        } else {
            var re = res[0];
            fpsetting.curstudent = [];
            fpsetting.objarrstud = {};
            for (var i = 0; i < re.length; i++) {
                fpsetting.curstudent.push([re[i][1], re[i][0]]);
                fpsetting.objarrstud[re[i][0]] = [re[i][1], re[i][2], re[i][3], re[i][4]];
            }
            // for (var i = 0; i < firstrecord.length; i++) {
            //     fpsetting.firstrecord[firstrecord[i][0]] = firstrecord[i];
            // }
            selstud();
        }
    }, [["yybladd.getstu", clas, gouptime]]);
};

/*
功能：初始化学员下拉框
*/
function selstud() {
    $("#selstud").closest('td').html('<label id="selstud"></label>');
    $('#selstud').editableSelect({
        bg_iframe: true,
        isreadonly: Arg("stuno") ? 1 : 0,
        isdisabled: Arg("stuno") ? 1 : 0,
        arrdata: fpsetting.curstudent,
        defvalue: Arg("stuno") || fpsetting.curstudent[0][1],
        width: '100px',
        onSelect: initdata, //选择事件
        case_sensitive: false, // If set to true, the user has to type in an exact
        items_then_scroll: 10 // If there are more than 10 items, display a scrollbar
    });
    initdata()
    //initclear();
};
/**
 *
 * issel是否切换日期
 * */
function initdata(issel) {
    var stud = $("#selstud").val();
    // var age = parentobj.GetAge(fpsetting.objarrstud[stud][2]);
    var checkdate = $("#txtcheckdate").val();
    $("#txtbirthday").val(fpsetting.objarrstud[stud][2]);
    var strage = parseFloat(parentobj.GetAge(fpsetting.objarrstud[stud][2], checkdate || ""));
    var stragezh = parentobj.GetAge(fpsetting.objarrstud[stud][2], checkdate || "", "zh");
    if (strage >= 7 || strage <= 0.03) {//与专案保持一致，小孩年龄未在筛查范围内
        return;
    }
    var objkey = {};
    if(Arg("stuno") && Arg("age") && Arg("checkdate")){
        $.sm(function (re, err){
            if(err){
                jQuery.getparent().layer.msg(err);
            }else{
                for (var i = 0; i < re.length; i++) {
                    var item = re[i];
                    if(!objkey[item.key]){
                        objkey[item.key] = item.result;
                    }
                }

            }
        },['psychologyadd2.getreult',Arg("stuno"), checkdate, Arg("age")], null, null, {async: false});
    }
    var fypg_age = "", strfypgage = "";
    //此段代码处理与历史数据保持一致
    if (strage >= 2.06 && strage < 3) {//处理两岁半年龄问题。专案历史数据存储的是2.5
        fypg_age = "2.5";
    } else if (strage == 1.06 || strage < 1) {
        if (strage >= 0.03 && strage < 0.06) {
            fypg_age = 0.03 + "";
        } else if (strage >= 0.06 && strage < 0.08) {
            fypg_age = 0.06 + "";
        } else if (strage >= 0.08 && strage < 1) {
            fypg_age = 0.08 + "";
        } else if (strage == 1.06) {
            fypg_age = strage + "";
        }
    } else {
        fypg_age = ("" + strage).split(".")[0];
    }
    fypg_age = issel ? fypg_age : (Arg("age") || fypg_age);
    strfypgage = fypg_age + (parseFloat(fypg_age) < 1 ? "月" : "岁");
    $("#txtage").val(strfypgage).attr("saveage", fypg_age);
    $("#txtcheckage").val(stragezh).attr("savecheckage", strage);
    var fypgHtml = '';
    var fypgObj = fpsetting.objquestion[fypg_age] ? fpsetting.objquestion[fypg_age] : fpsetting.objquestion[parseInt(fypg_age)];
    for (var kk in fypgObj) {
        var idx = kk.split("-")[1];
        fypgHtml += '<p><input id="fypgcontent' + kk + '" type="checkbox" name="CheckboxGroup1" ' + (objkey[kk] && objkey[kk] == "是" ? "checked" : "") + ' value="' + idx + '"><label for="fypgcontent' + kk + '">' + idx + '.' + fypgObj[kk] + "</label></p>";
    }
    $("#fypgdiv").html(fypgHtml);
};
//
// /*
// 功能：
// */
// function strhewe(type) {
//     var stud = $("#selstud").val();
//     var objstud = fpsetting.objarrstud[stud];
//     switch (type) {
//         case 1://sphematinvalue
//             var hematin = $("#txthematin").val();
//             hematin = parentobj.getOneFloat(hematin);
//             if (isNaN(hematin) || hematin == "") {
//                 jQuery.getparent().layer.msg('请输入数字！', function (r) {
//                     $("#txthematin").val("");
//                     $("#txthematin").focus();
//                     jQuery.getparent().layer.close(r);
//                 });
//             } else if (Math.floor(hematin) > 500) {
//                 jQuery.getparent().layer.msg('您输入的血红蛋白不符合常理,请重新输入!', function (r) {
//                     $("#txthematin").val("");
//                     $("#txthematin").focus();
//                     jQuery.getparent().layer.close(r);
//                 });
//             } else {
//                 if(!objstud[2]){
//                     jQuery.getparent().layer.msg('出生日期不能为空!', function (r) {
//                         $("#txthematin").val("");
//                         $("#txthematin").focus();
//                         jQuery.getparent().layer.close(r);
//                     });
//                 }else{
//                     var curdate = parent.getStrByDate(parent.objdata.curmoment._d);
//                     var age = (curdate ? parentobj.GetAge(objstud[2], curdate) : "");
//
//                     $("#txthematin").val(hematin);
//                     var hematinvalue = parentobj.GetHematin(hematin, age, "");
//                     var hematinvalueshow = ($.inArray(hematinvalue, fpsetting.arrnopxtxt) >= 0 ? hematinvalue + "贫血" : hematinvalue)
//                     $("#sphematinvalue").text(hematinvalueshow).attr("hematinvalue", hematinvalue);
//                 }
//             }
//             break;
//     }
// };

/*
功能：保存
*/
function btsave(cb) {
    var stuno = $("#selstud").val();
    var ptype = 0;//0 园所
    var checkdate = $.trim($("#txtcheckdate").val());//检查时间
    var age = $("#txtage").attr("saveage");//所属年龄节点
    var checkage = $("#txtcheckage").attr("savecheckage");//检查年龄
    if (!checkdate) {
        jQuery.getparent().layer.msg("请先选择检查时间!");
        return;
    }
    if (!checkage) {
        jQuery.getparent().layer.msg("检查年龄不能为空!");
        return;
    }
    var strage = parseFloat(checkage);
    if (strage<0.03 || strage >= 7) {
        return jQuery.getparent().layer.msg("幼儿年龄未在筛查范围内！");
    }
    jQuery.getparent().layer.load();
    if ($('input[name="CheckboxGroup1"]:checked').length > 0) {
        var arrchecked = [];
        var result = "";
        var arrsm = [['tb_psychtest.add2', stuno, age, 1, ptype]];
        $('input[name="CheckboxGroup1"]').each(function (){
            result = "否";
            var oldkey = (Arg("age") || age) + "-" + $(this).val();
            var key = age + "-" + $(this).val();
            if($(this).is(":checked")){
                arrchecked.push($(this).val());
                result = "是";
            }
            arrsm.push(['tb_psychtestreslut.add2', stuno, age, key, result, checkdate, checkage, Arg("age") || age, Arg("checkdate") || checkdate, oldkey]);
        });
        $.sm(function (re, err) {
            jQuery.getparent().layer.closeAll("loading");
            if (err) {
                jQuery.getparent().layer.msg(err, {icon: 5});
            } else {
                jQuery.getparent().layer.msg('保存成功！');
                cb && cb();
            }
        }, arrsm, null, null, null, null, 1);
    } else {
        jQuery.getparent().layer.msg("请选择预警征象!");
        jQuery.getparent().layer.closeAll("loading");
    }
}