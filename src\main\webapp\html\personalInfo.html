<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <link rel="stylesheet" href="../css/reset.css"/>
    <link rel="stylesheet" href="../css/gray.css"/>
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../css/style.css"/>
    <link rel="stylesheet" href="../styles/xbcomstyle.css">
    <!--<script type="text/javascript">-->
    <!--document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');-->
    <!--</script>-->
    <!--[if lt IE 9]>
    <script src="../sys/html5shiv.min.js"></script>
    <![endif]-->
    <style>
        body{
            background: #F4F5F7;
        }
        .u-edit {
            display: none;
        }

        .divrole {
            display: none;
        }

        .layui-form-label {
            width: 160px
        }
        .location{
            cursor: pointer;
            padding: 4px 4px 4px 20px;
            margin-left: 4px;
            background: url(../images/locate.png) 0 2px no-repeat;
            background-size: 20px;
        }
        #bmapicon{
            display: inline-block;
            background: url(../images/bmapicon.png) center no-repeat;
            background-size: 20px;
            width: 30px;
            height: 20px;
            float: right;
        }
        .theme-fgc{
            color: #1da89e !important;
        }
        .layui-comthis.tjpad::after{width: 85px;height: 38px;}
        .legend_bjcyadd{position: relative;height: 34px; line-height: 34px;border-bottom: 1px solid #EAEAEA;}
        .incr-chat .div_field .layui-form-label {line-height: 80px;padding-bottom: 0;}
        .div_field .layui-input, .div_field .layui-select, .div_field .layui-textarea {margin-top: 20px;}
        .incr-chat .layui-form-label {width:100px;}
        .div_field .layui-input-block {margin-left: 254px; line-height: 70px;}
        .div_field{border-bottom: none;}
        .layui-form-item .layui-input-inline{width: 300px;}
        .layui-form-radio{margin: 10px 0px 0 15px;}
        .sexdiv .layui-form-radio{margin: 27px 0px 0 15px;}
        .personal-center-left .webuploader-pick{border: 1px solid #EAEAEA;line-height: 15px;color: #686B70;border-radius: 3px; background: #F4F5F7; padding:5px 10px;}
        .personal-center-left .webuploader-pick a {
            color: #686B70;
        }
    </style>
</head>
<body>
<section class="personalInfo font14 cl1 pd10-20">
    <div>
        <!--<h3 class="personal-tit">个人基本信息</h3>-->
        <form id="form" class="layui-form incr-chat" style="margin:20px 0;">
            <section class="clearfix div_field">
                <section class=" fl" style="width:60%">
                    <div class="layui-form-item">
                        <label class="layui-form-label"><em>*</em>真实姓名：</label>
                        <div class="layui-input-inline">
                            <input id="txttruename" type="text" name="truename" required lay-verify="required" maxlength="30" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item sexdiv">
                        <label class="layui-form-label"><em>*</em>性别：</label>
                        <input type="radio" name="sex" value="男" title="男">
                        <input type="radio" name="sex" value="女" title="女">
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><em>*</em>手机：</label>
                        <div class="layui-input-inline">
                            <input id="txtmobile" type="text" name="mobile" maxlength="13" required lay-verify="required" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><em>*</em>用户名：</label>
                        <div class="layui-input-inline">
                            <input id='txtuname' type="text" name="uname" required lay-verify="required" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><em>*</em>角色：</label>
                        <div class="layui-input-inline">
                            <input style="border: none;" id='txtrole' readonly type="text" name="rname" required lay-verify="required" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">邮箱：</label>
                        <div class="layui-input-inline">
                            <input id="txtemail" type="text" name="email" maxlength="30" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">单位名称：</label>
                        <div class="layui-input-inline">
                            <input id="txtdepartname" type="text" name="departname" placeholder="15字以内" maxlength="30" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">单位地址：</label>
                        <div class="layui-input-inline">
                            <input id="txtdepartaddress" type="text" name="departaddress" autocomplete="off" class="layui-input">
                        </div>
                        <div style="display: inline-block;line-height: 80px;padding-right: 30px;"><span id="mapmsg"><img  src="../images/xbimages/location-ico.png" style="width: 20px; margin:0 5px; " /><a style="color: #12ABEA;">点击图标定位</a></span><!--<a id="bmapicon" title="点击查看百度地址"></a>-->
                        </div>
                    </div>
                </section>
                <section id="divicon" class="fl  personal-center-left" style="text-align: center;width:40%">
                    <div>
                        <span style="background: #EDEFF2; height: 640px; text-align: right; display: inline-block;width:100px;line-height:80px;  padding-right: 10px;float: left;" ><i class="layui-mark-red">*</i>头像：
                        </span>
                        <div style="margin: 0 auto; width: 200px; padding-top: 100px;padding-left: 100px;">
                            <img style="display: inline-block;vertical-align: top;" src="../images/default.png" onerror="this.src='../images/default.png'" alt="" class="otx-head"/>
                            <p>尺寸：96*96</p>
                            <p>支持jpg、png、gif、bmp</p>
                            <a class="cl2 lanflag layui-btn-primary" id="btndelimg" style="color: #E82A2E;display: inline-block;padding:3px 15px; background: #F4F5F7;margin-right:10px;">删除</a>
                            <span id="btnup">
                                <a class="lanflag" id="lanlocalup">本地上传</a>
                            </span>
                        </div>
                    </div>
                </section>
            </section>
            <div style="clear: both;margin:15px 239px;">
                <input type="hidden" id="txtroletype" style="padding-top: 10px; text-align: center;"/>
                <a id="btnsave" class="layui-btn bluebtn" lay-submit="" lay-filter="btnsave">保存</a>
            </div>
        </form>
        <!--     <div class="legend_bjcyadd">-->
        <!--          <label class="layui-comthis tjpad">启动打开页面</label>-->
        <!--	</div>-->
        <!--&lt;!&ndash;    <h3 class="noticeTitle bg1" style="margin-top: 10px;"><span class="lanflag">启动打开页面</span></h3>&ndash;&gt;-->
        <!--    <div class="incr-chat" style="margin: 20px 0;">-->
        <!--        <div class="bg6 div_field">-->
        <!--            <form class="layui-form">-->
        <!--                <div class="layui-form-item">-->
        <!--                    <label class="layui-form-label">打开页面：</label>-->
        <!--                    <div class="layui-input-block">-->
        <!--                        <input type="radio" name="openpage" value="1" title="系统" lay-filter="rdsystem" style="margin-top: 0;">-->
        <!--                        <div id="defaultpages" class="layui-input-block" style="margin-left: 10px;display: none;width: 351px;display: inline-block;}">-->
        <!--                        &lt;!&ndash;<input type="checkbox" name="page" value="72" title="工作台">&ndash;&gt;-->
        <!--                        &lt;!&ndash;<input type="checkbox" name="page" value="10" title="日历页">&ndash;&gt;-->
        <!--                        &lt;!&ndash;<input type="checkbox" name="page" value="62" title="数据展示页">&ndash;&gt;-->
        <!--                        </div>-->
        <!--&lt;!&ndash;                        <input type="radio" name="openpage" value="2" title="常用功能页面" checked lay-filter="rdlastopen" style="margin-top: 0;">&ndash;&gt;-->
        <!--                        <a lay-submit lay-filter="applayopen" class="btn-black" style="margin-left: 7px;top: 5px;">-->
        <!--                            <span class="lanflag">应用</span>-->
        <!--                        </a>-->

        <!--                    </div>-->

        <!--                    <div id="Commonpages" class="layui-input-block" style="margin-left: 185px;display: none">-->

        <!--                    </div>-->
        <!--                </div>-->
        <!--            </form>-->
        <!--        </div>-->
        <!--    </div>-->
        <div class="legend_bjcyadd" style="display:none;">
            <label class="layui-comthis tjpad">锁屏密码设置</label>
        </div>
        <!--    <h3 class="noticeTitle bg1" style="margin-top: 10px;"><span class="lanflag">锁屏密码设置</span></h3>-->
        <div class="incr-chat" style="margin: 20px 0;display:none;">
            <div class=" bg6 div_field">
                <!--<h4><b><span class="lanflag" id="lansettime">推荐红包配置</span></b></h4>-->
                <form class="layui-form">
                    <div class="layui-form-item">
                        <label class="layui-form-label">锁屏密码：</label>
                        <div class="layui-input-block">
                            <input id="password" type="password" name="lockpwd" placeholder="请输入密码" autocomplete="off" class="layui-input" style="width: 240px;display: inline">
                            <a lay-submit lay-filter="lockScreenPas" class="btn-black" style="margin-left: 7px"><span class="lanflag">应用</span></a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!--    <div class="legend_bjcyadd">-->
        <!--        <label class="layui-comthis tjpad">切换角色</label>-->
        <!--    </div>-->
        <!--    <div class="incr-chat" style="margin: 20px 0;">-->
        <!--        <div class="bg6 div_field">-->
        <!--            <form class="layui-form">-->
        <!--                <div class="layui-form-item">-->
        <!--                    <label class="layui-form-label">切换角色：</label>-->
        <!--                    <div class="layui-input-block" style="width: 260px;">-->
        <!--                        <select id="selaccount" lay-filter="selaccount" lay-verify="required"></select>-->
        <!--                    </div>-->
        <!--                </div>-->
        <!--            </form>-->
        <!--        </div>-->
        <!--    </div>-->
    </div>
</section>
<script>
    var v = top.version;
    document.write("<script type='text/javascript' src='../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../sys/uploadutil.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../plugin/ueditor/third-party/webuploader/webuploader.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../js/personalInfo.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>