﻿.introduce-list li/*制作商管理*/
.manufacturer-txt{display: inline-block; float: left;  color:#34495E; font-weight: bold; font-size: 14px;line-height: 30px;}
.weui-flex{display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex;}
/*表格从写*/
.layui-table th, .layui-table td{height: 40px;line-height: 40px;}
.layui-table td, .layui-table th{font-size: 13px;}
.layui-input, .layui-select, .layui-textarea{border-radius: 2px;}
.layui-table thead tr, .layui-table-click, .layui-table-header,  .layui-table-mend, .layui-table-patch, .layui-table-tool, .layui-table-total, .layui-table-total tr{background:#F6F6F6;color: #4C5E71; }
.layui-table,input,selec{color: #34495E;}
.layui-table th, .layui-table td, .layui-table[lay-skin="line"], .layui-table[lay-skin="row"], .layui-table-view, .layui-table-tool, .layui-table-header, .layui-table-col-set, .layui-table-total, .layui-table-page, .layui-table-fixed-r, .layui-table-tips-main, .layui-table-grid-down{border-color: #DCDFE6;}
.gray-main .layui-table td, .gray-main .layui-table th,.gray-main .layui-table-col-set,.gray-main .layui-table-fixed-r,.gray-main .layui-table-grid-down,.gray-main .layui-table-header,.gray-main .layui-table-page,.gray-main .layui-table-tips-main,.gray-main .layui-table-tool,.gray-main .layui-table-total,.gray-main .layui-table-view,.gray-main .layui-table[lay-skin="line"],.gray-main .layui-table[lay-skin="row"] {
	border-bottom: 1px solid #DCDFE6;border-width:0px; }
.gray-main .cuptop{padding: 10px;}
.layui-table{margin: 0;}
/*新版日历*/
.layui-comselect .layui-alendar{width: auto;height: auto;left: 10px;position: absolute; top:9px;cursor: pointer;}
.cuptop{background: #ffffff; margin: 10px 0; border-radius: 5px; padding: 10px 20px;color: #4C5E71;}
.default-btn{min-width: 44px;height: 25px;line-height: 19px;margin: 3px;text-align: center;padding: 3px 8px;box-sizing: border-box;font-size: 13px;display: inline-block;color: #ffffff;border-radius: 2px;vertical-align: top;cursor: pointer;}
.layui-form-radio > i:hover, .layui-form-radioed > i {
    color: #3C99FC;
}
.select-open{border: 1px solid #DDDDDD; }
/*复选框*/
.checkbox-blue input[type="checkbox"]{-webkit-appearance: none;width: 14px;height: 14px;border: 1px solid #c8c8c8;background-size:14px 14px;outline: none;margin: -2px 5px 0 2px;}
.checkbox-blue input[type="checkbox"]:checked{width: 14px;height: 14px;border:none;background: url(../images/checkbox_bluebg.png) 50%;background-size:14px 14px;outline: none;}
/*栏目重写*/
.noticeTitle{font-weight: bold;border-top-left-radius:5px;border-top-right-radius:5px;background: #D1DEEB; height: 45px; line-height: 45px; padding: 0 10px; font-size: 15px;}
.noticeTitle,.noticeTitle a{color: #34495E;}
.formList label{width: 101px;text-align: right;display: inline-block;}
.formList input[type="text"] {
    border: 1px solid #ccc;
    height: 35px;
    line-height: 35px;
    padding: 0 8px;
    border-radius:4px;
}
.formList input[type="text"] {
    width: 520px;
}
.formList select {
    border: 1px solid #ccc;
    height: 35px;
    line-height: 35px;
    padding: 0 4px;
    border-radius: 0;
    color: #6C6C6C;
    margin-left: -4px;
    border-radius:4px;
}

.layui-input.form-ordernum{padding-left: 35px;}
.knowledgebox,.recipeinfobox{cursor: pointer;}
/*餐别样式*/
.meals-bg{width:500px;border-right: 1px solid #E6EBF1;display: inline-block;margin-right: 35px;}
.meals-bg label{margin-right:15px;}
/*公示牌申领*/
.apply-tit{background:#FCFCFC; line-height:45px; height: 45px;  color: #34495E; font-size: 14px;border-bottom: 1px solid #DCDFE6;padding-left: 10px;font-weight: bold;}
.introduce-list{margin: 10px 20px;border-bottom: 1px solid #DCDFE6; padding-bottom: 10px; overflow: auto; }
.introduce-list h5{padding: 5px 0px}
.introduce-list p{line-height: 25px; color: #666666;}
.introduce-list li{width: 305px;float: left; margin: 10px 10px 10px 0px;}
.introduce-list .layui-form-label{padding: 5px 0px 5px 0px; width: 112px;}
.success-img{width:250px; border: 2px solid #A5DA8B; padding: 15px; border-radius: 3px; margin: 15px 0;}
/*去支付*/
.payment-tit{background:#FFF7F2; border-top-right-radius:20px; border-bottom-right-radius:20px; line-height: 30px;  padding:0 15px; display: inline-block; margin: 20px 0; }
.payment-cen{ width: 168px; margin: 0 auto; text-align: center; color: #34495E;font-size: 16px; }
/*我的证照*/
.licence-top{border-bottom: 1px solid #EEEEEE;color:#34495E; padding-left: 15px; font-size: 14px;font-weight: bold; border-top-left-radius: 5px;border-top-right-radius: 5px; background:#FCFCFC; height: 48px; line-height: 48px;}
/*用户管理*/
.catalog-tit{
    background: #F2F8FF;
    border: 1px solid #A1C4EE;
    height: 22px;
    line-height: 22px;
    border-radius: 30px;
	display: inline-block;
    color: #333333;
    margin:0px 0px 0 10px;
	font-size: 12px;
	cursor: pointer;
	padding: 0 10px;
}
.catalog-num{text-decoration: underline; color:#4A90E2;}
/*统计分析*/
.safestat-cen{height: 168px;text-align: center;display: -moz-box;  /* Firefox */
display: -ms-flexbox;    /* IE10 */
display: -webkit-box;    /* Safari */ 
display: -webkit-flex;display: flex;/* Chrome, WebKit */ margin-bottom: 8px;}
.visiting-statis{display: -moz-box;  /* Firefox */
display: -ms-flexbox;    /* IE10 */
display: -webkit-box;    /* Safari */ 
display: -webkit-flex;display: flex;/* Chrome, WebKit */}
.safestat-tit{width: 100%; text-align: left; font-size:14px; color: #34495E;border-top-left-radius:5px;border-top-right-radius:5px;overflow: hidden;  border-bottom: 1px solid #E2ECF3; height: 50px; padding-right:15px;line-height: 50px;}
.safestat-tit span{ display: inline-block; line-height:50px; padding-left: 15px; }
.safenum-cell{display: inline-block;height: 168px;width: 34.6%;margin-right: 0.5%; color: #94A6B8; text-align: left;}
.safenum-cell:last-child{margin-right: 0;}
.safenum-cell p{font-size: 38px; padding-left: 35px;}
.safenum-celltxt{ padding:35px 0 20px 35px; font-size: 18px;}
.safestat-cen.statistics-list{position: relative;background: #fff;border: 1px solid #eeeeee;margin:10px 3px; border-radius: 5px;}
/*查看详情*/ 
.form-list{margin: 10px auto; position: relative;border-bottom: 1px solid #F0F3F7; padding: 5px 0px}
.form-list .layui-form-label{padding: 5px 0; }
.form-list .layui-input-block{ color:#798EA4;line-height: 30px; }
.form-list h3{color:#34495E; font-size: 14px;font-weight: bold;margin:0 0px 5px 30px;}
.pur-progress{text-align: center;width: 100%;margin: 25px auto 20px auto;position: relative;z-index: 999;}
/*.pur-img-pro:after{content: "";width: 151px;border-top:1px dashed #E5E5E5;position: absolute;top: 48px;right: -75px;}*/
.pur-img-pro:last-child:after{content: none;}
.pur-progress .pur-img-pro{width: 205px;position: relative;font-size: 12px;display: inline-block;vertical-align: top;text-align: center;}
.pur-progress .pur-img-pro p{color: #999999;font-size: 14px;}
.pur-progress .pur-img-pro span{display: inline-block;color: #999;}
.pur-img-pro.current label .pay-txt{color: #34495E;}
.stepbg img{ background: #fff;  border-radius: 50%; text-align: center; vertical-align: middle; padding:0px 1px;}
.pur-img-pro .order-bill {width: 28px;height: 28px;line-height: 28px;border-radius: 50%;margin: 5px auto 8px auto;background: #ffffff;border: 1px solid #DCDDDE;}
.pur-img-pro.current .order-bill {background: #ffffff;border: 1px solid #025293;}
.pur-img-pro.current .order-bill span{margin-top: 5px;background: #025293; width: 18px; height: 18px;border-radius: 50%; text-align: center; }
.pur-img-pro .order-bill:before{content:"";height: 3px;background: #DCDDDE;width: 100%;display: inline-block;position: absolute;left: -50%;margin-top: 12px;z-index: -1;}
.pur-img-pro.current .order-bill:before{background: #DCDDDE;}
.pur-img-pro .order-bill span{margin-top: 5px;background: #DCDDDE; width: 18px; height: 18px;border-radius: 50%; text-align: center; }
.pur-img-pro:first-child .order-bill:before{content: none;}
.pay-txt{font-size: 15px;color: #34495E;line-height: 25px;}
.pur-img-pro label{display: block;}
/*营养素分析*/
.nutrition-list{border: 1px solid #EEEEEE; padding:0 20px 20px 20px; overflow: auto;margin-top: 10px;}
.nutrition-list li{display:inline-block;width: 14%;font-size: 14px;vertical-align: top;position: relative;  }
.nutrition-list  .nutrition-list-txt{padding-left: 10px; margin: 0 0px 5px 0px;border-bottom: 1px solid #DCDFE6; background:#FCFCFC; line-height:45px; height: 45px;  color: #333333; font-size: 14px;}
.nutrition-list  .nutrition-list-txt label{ font-size: 12px; color: #34495E;margin-left: 15px;}
.nutrition-tb{ display: inline-block; background: #00cd91;width:4px;height: 16px; position: absolute; left:0px;top:2px;  font-size: 16px; color: #333333; border-top-left-radius:10px;border-top-right-radius:10px;border-bottom-left-radius: 10px;border-bottom-right-radius:10px;}
.nutrition-tit{position: relative; padding-left: 10px; margin: 15px 0; color: #333333;display: block}
.nutrition-cell{display: inline-block; border: 1px dashed #EEEEEE; padding:0 10px;line-height: 28px;display: -moz-box;  /* Firefox */
display: -ms-flexbox;    /* IE10 */
display: -webkit-box;    /* Safari */ 
display: -webkit-flex;display: flex;/* Chrome, WebKit */ height:28px;justify-content: space-between;}
.nutrition-cell input{border: none;float: right; text-align: right;width: 50px;line-height: 28px; height:28px;color: #333333;padding-left: 0;}
.nutrition-cell label{float: left;}
.nutrition-cell label i{color: #333333;}
.taboo-list .layui-table[lay-skin="row"] tr { border-left:3px solid #D0021B;}
/*收货地址*/
.table-details{border: 1px solid #DCDFE6;margin-bottom: 10px;}
.table-details h5{background:#FCFCFC; line-height:45px; height: 45px;  color: #34495E; font-size: 14px;font-weight: bolder;padding-left: 10px;border-bottom: 1px solid #DCDFE6;}
.order-details{margin-bottom: 10px;}
.order-details h5{background:#FCFCFC; line-height:45px; height: 45px;  color: #34495E; font-size: 14px;font-weight: bolder;padding-left: 10px;border-left: 1px solid #DCDFE6;border-top: 1px solid #DCDFE6;border-right:1px solid #DCDFE6;}
.layui-form-main  .layui-form-label{line-height: 28px;}

/*填写申报表*/
.topbg{background:#003158; height: 63px; color: #ffffff;font-size: 18px;line-height: 63px;}
.topbg img{width: 32px; float: left;margin: 22px 9px;}
 .lbsex{vertical-align: text-top;margin: 0 18px;}
.step-num{width: 50px;height: 50px;line-height: 50px;margin: 0 auto 8px auto;text-align: center;font-size: 23px;color: #9b9b9b;font-weight: bold;border: 2px solid #e2e2e2;border-radius: 50%;}
.step-cell{width: 168px;display: inline-block;text-align: center;position: relative;margin: 0 100px;cursor: pointer;}
.step-cen .step-cell{color: #5A6D80;}
.step-cen .step-cell.current{color: #ffffff;}
.step-con{padding: 36px 0;text-align: center;background: #ffffff;font-size: 16px;box-shadow: 0 0 18px 3px #ebebeb;display: inline-block;margin: 20px 0 0 0;width: 100%;}
.step-cen{background:url(../images/recipes/onestep.png)no-repeat; width: 1114px; height: 56px; line-height: 56px; margin: 10px auto;}
/*.step-cen.step-two{background:url(../images/recipes/twostep.png)no-repeat; width: 1114px; height: 56px; line-height: 56px; margin: 10px auto;}*/
/*.step-cen.step-three{background:url(../images/recipes/threestep.png)no-repeat; width: 1114px; height: 56px; line-height: 56px; margin: 10px auto;}*/
.step-list{margin: 10px auto; border-bottom: 1px solid #F0F3F7; padding: 5px 0px; color: #34495E; }
.step-list .layui-form-label{padding: 5px 0px 5px 0px; width: 136px;}
.step-list input{width: 429px;}
.step-list h3{color:#34495E; font-size: 14px;font-weight: bold;margin:0 0px 5px 30px;}
/*表格*/
.def-layui-table{margin: 0;background: #faf8f8;width: 100%;}
.def-layui-table td, .def-layui-table th{padding: 9px 15px;min-height: 20px;line-height: 20px;font-size: 14px;}
.def-layui-table td, .def-layui-table th{border: 1px solid #e2e2e2;}

/*login*/
.nav_login{  margin:0px   auto 0  auto ; height:71px; width:980px; padding-top:15px;}
.nav_login img{ float:left; cursor:pointer}
.nav_login .zleft{ float:left; }
.nav_login .zright{ float:right; margin-top:8px;}
.nav_login .zright a{ color:#009c77; font-size:16px; line-height:30px; height:30px; padding-right:5px;}
.loginContent{width:100%;/*background:url(../images/alpha3.png);*/ position: relative;background:#02ad5e; padding:120px 0 86px 0px; overflow:hidden}
.loginTitle{color: #444444;font-size:22px; line-height:30px; text-align:center; height:39px; margin-bottom:30px; width:496px;}
.loginArea{ float:right; width:536px;height:427px;}
.con-area{ width:296px; padding:27px 0px 0 18px;float: left; }
.code-area{width: 180px;float: left;padding: 110px 20px 20px 20px;text-align: center;position: relative;}
.code-area h5{font-size: 14px;margin-bottom: 5px;}
.for-wordtxt{ color:#4d8cff; float:right; text-decoration:underline; cursor:pointer}
.footer{text-align:center;line-height:40px;height:40px;color:#666666;font-size:12px;clear:both;}
.loginContent{width:100%; background:#02ad5e; padding:80px 0 8px 0; overflow:hidden}
.loginContent .loginContentdiv{height: 410px;margin-top: -5px;}
/*.loginContent .loginArea{height: 410px;}*/
.login_imgcen li{float: left;padding-top: 10px;}

/*登录*/
.nav_login{height: 94px;padding-top: 26px;}
.loginContent{background: url(../images/content_bg.png)no-repeat;background-size: cover;padding: 150px 0 55px 0px;}
.loginContentdiv{width: 1038px;background: none!important;height: 461px;margin: 0 auto;}
.nav_login{width: 1038px;}
.loginArea{width: 460px;height: 440px;background: #ffffff;position: relative;box-shadow: 0px 0px 10px 0px #e7e7e7;}
.loginTitle{text-align: left;margin-bottom: 50px;height: auto;}
.con-area{padding: 40px 0px 0 33px;width: 392px;}
.input-group{background: #fdfdfd;border: 1px solid #dfdee4;height: 46px;line-height: 46px;margin: 0px 0 18px 0;}
.input-group input{width: 92%;font-size: 13px;margin: 0 12px;height: 46px;line-height: 46px;border: none;vertical-align: top;}
.zbtn-login{display: block;background: #d1d1d1;font-size: 15px;color: #ffffff;height: 50px;line-height: 50px;text-align: center;border-radius: 3px;width: auto;margin: 15px 0 18px 0;}
.loginArea{float: left;}
.code-area{position: relative;width: 420px;padding: 50px 20px 20px 20px;}
.code-area h5{margin-bottom: 45px;}
.code-div{width: 220px;margin: 0 auto;padding: 0;}
.code-div .bor-span{top: -14px;left: 0;border-top: 2px solid #e6e6e6;border-left: 2px solid #e6e6e6;}
.code-div .bor-span2{bottom: 12px;right: 0;border-bottom: 2px solid #e6e6e6;border-right: 2px solid #e6e6e6;}
.code-div .bor-span3{position: absolute;width: 22px;height: 22px;display: inline-block;top: -14px;right: 0;border-top: 2px solid #e6e6e6;border-right: 2px solid #e6e6e6;}
.code-div .bor-span4{position: absolute;width: 22px;height: 22px;display: inline-block;bottom: 12px;left: 0;border-bottom: 2px solid #e6e6e6;border-left: 2px solid #e6e6e6;}
.mark-txt{height: 88px;line-height: 88px;background: rgba(33,110,159,0.88);position: absolute;top: 0;width: 100%;font-size: 30px;color: #ffffff;}
.login-sub{width: 518px;height: 406px;margin-top: 13px;float: right;background: rgba(194,194,194,0.4);padding: 0 30px;}
.login-sub h1{margin: 27px 0 23px 0;text-align: center;font-size: 22px;color: #3474be;font-weight: bold;}
.applybtn{text-align: center;background: #c42b0b;font-size: 15px;color: #ffffff;width: 120px;height: 40px;line-height: 40px;text-align: center;display: inline-block;border-radius: 3px;}
.applycon .layui-table{display: inline-block;width: 83px;text-align: right;margin: 15px 0;width: 100px;}
.applycon .layui-input{width:340px; }
.chtel{position: absolute; left: 10px;border-right: 1px solid #CBD6E1; height: 37px; padding: 0 10px 0 0px;color: #34495E;line-height: 37px;}
.step-btn{text-align: center;padding: 50px 0 50px 0;}
.step-btn a{width: 136px;height: 32px;line-height: 32px;text-align: center;font-size: 13px;border-radius: 2px;display: inline-block;}
/*完成*/
.default-bg{width: 1197px;margin: 20px auto 50px auto;background: #ffffff;box-shadow: 0 0 18px 3px #ebebeb;padding-top: 5px;font-size: 14px;min-height: 465px;}
.state-sub{text-align: center;padding: 60px 0;font-size: 14px;color: #9da1ac;}
.state-sub h3{font-size: 22px;color: #34495E;font-weight: bold;margin: 25px 0;}
.state-sub label{color: #4A90E2;font-weight: bold;font-size: 18px;}
.page-detail{background: #dedede;font-size: 12px;color: #5a5a5a;position: fixed;bottom: 0;width: 100%;height: 32px;line-height: 32px;}
.default-bg select{width: 260px;height: 40px;border: 1px solid #dde3e8;outline: none;}
.default-bg select:focus{border: 1px solid #71b6ef;}
/*个人中心*/
.usertopbg{background:#025293; height: 43px; color: #ffffff;font-size: 14px;line-height: 43px; cursor: pointer;}
.topbg-cen{width: 1194px; margin: 0 auto;}
.topbg-cen span{cursor: pointer;}
.titlebg{/*border-right: 1px solid #80A8C9;*/ padding: 0 10px; margin-right: 10px;}
.userimg{width: 20px; height: 20px; border: 1px solid #ffffff; /*border-radius: 50%; */}
.titleright{float: right;}
.search-div{background:#FFFFFF; height: 107px;box-shadow: 0 0 18px 3px #ebebeb; }
/*健康知识*/
.place-tit{color: #333333; font-size: 14px; line-height: 30px; height: 30px;width: 1197px;margin: 10px auto 0px auto;}
.place-tit a{color: #268FEA;}
.comment-bg{width: 1197px;margin: 15px auto 10px auto;background: #ffffff;border-radius: 1px; padding-top: 5px;font-size: 14px;}
.search-txt{width: 333px; display: inline-block;height: 37px;border: 1px solid #025293;box-sizing: border-box;background: #ffffff;}
.search-txt .icon_search{float: left;margin: 0 5px 0 8px;height: 37px;line-height: 33px;}
.search-txt label input{height: 35px;box-sizing: border-box;border: none;}
.banner{margin: 20px auto; text-align: center;}
.collect-txt{position: absolute; bottom: 10px;width: 934px;}
/*健康知识列表*/
.content-cen{min-height: 450px;margin-top: 10px;}
.content-main{min-height: 450px;}
.issue-sub-index2{ width: 1194px; margin: 0px auto; overflow: hidden;}
.sidebar-left{ background: #ffffff;padding: 10px 0px 0 0px; width: 222px;float: left;}
.sidebar-left .h1 {
    width: 217px;
    height: 40px;
    line-height: 40px;
    text-indent: 12px;
    font-size: 16px;
    color: #34495E;
    font-weight: bold;
}
.sidebar-left h3{line-height: 46px; height: 46px;font-size: 14px;color: #34495E; position: relative;}
.sidebar-left h3 a{color: #34495E;padding-left: 10px;}
.sidebar-left h3 a.active{border-left:3px solid #025293;background:#EFF8FF;color: #025293;display: block; padding-left: 10px;font-weight: bold;}
.sidebar-left ul li{  font-size: 14px;}
.sidebar-left ul li:last-child{border-bottom: none;}
.sidebar-left ul li a:hover,.sidebar-leftr ul li .active {
    text-decoration: none;
    font-size: 14px;
    color: #ffffff;
	background:#025293;
}
.sidebar-left ul li a {
	color: #34495E;
    display: block;
    height: 30px;
    text-align: left;
    line-height: 30px;
	padding-left: 15px;
}
.sidebar-right{float: right;width: 940px; overflow: hidden; padding: 10px; background: #ffffff;}
.sidebar-sel{height: 170px;}
.sidebar-sel p{font-size: 16px; color:#333333;line-height: 30px;}
.sidebar-sel p.collect-tit{font-size: 12px; line-height: 15px;color: #9e9e9e;}
.layui-col-md3{width: 25%;}
.health-con{text-align: center; margin: 20px auto; max-width: 1100px;}
.health-con img{width: 100%;}
/*营养计算器*/
.search-main{width: 520px; margin: 20px auto;}
/*食谱公示率*/
.verify-term{background:#FAFBFC;position:relative;width: 100%; margin: 10px 0px;padding: 15px 0;font-size: 14px;color: #4C5E71;}
.verify-term:before{content:"";display:block;border-width:8px;position:absolute; top:-15px;left:50%; border-color: transparent transparent #ffffff transparent;
        border-style: dashed dashed solid dashed;font-size:0; line-height:0;}
.verify-term:after{top:-14px; border-color: transparent transparent #ffffff transparent;} 
.verify-con{font-size: 12px;color: #666666;border-top: 1px solid #dddddd;padding: 10px 0;line-height: 24px;}
.state-txt{color: #ed4f4c;}	
.success-state{ display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex; position: relative;}			
.success-state .state-txt{color: #0fd69c;}
.progressbar_1{ 
	background-color:#eeeeee; 
    height: 5px; 
    width:80%;    
    color:#222222; 
	border-radius:10px;
     margin: 8px 20px 8px 10px;
} 
.progressbar_1 .bar { 
    background-color:#4A90E2; 
    height:5px; 
	border-radius:10px;
	position: relative;
}
.kindergarten-tit{overflow: hidden;text-overflow: ellipsis;white-space: nowrap;margin: 0 15px 0 10px; width:70px; display:inline-block;}
.success-state.yellow{color:#FD9B5B;}
.success-state.yellow .progressbar_1 .bar{ background-color:#FD9B5B;}
/*食谱公示展开*/
.on-trial{ margin: 10px 20px; color: #333333;font-size: 14px; }
.on-trial span{display: inline-block;color: #9e9e9e; margin-right: 15px;width: 70px;}
.on-trial label{display: inline-block; text-align: center; min-width: 60px; cursor: pointer;margin-right: 7px;}
.on-trial label.hover{background:#268FEA; color: #FFFFFF; border-radius: 2px; text-align: center; padding: 2px 4px;}
.cookbook-txt{color: #7C7C7C;font-size: 13px; margin-top: 8px;}
.cookbook-txt label{border: 1px solid #9e9e9e; padding: 0px 5px; display: inline-block; margin-right: 15px;cursor: pointer;}
/*食谱率排行*/
.on-trial.success-state label{margin-bottom: 10px;}
.retract-tit{color: #4C5E71;position: absolute; right: -10px; top:50px; cursor: pointer;}
.exceed-txt{background:#FCFCFC; border-radius: 20px; padding: 3px 10px; font-size: 12px; color:#333333; margin-left: 10px;}
/*食谱公示与追溯平台*/
.publicity-tit01{background:url(../images/recipes/img01.jpg)no-repeat; width: 800px; height: 640px;background-size: 100% 100%;  margin: 0 auto;}
.publicity-tit01 p{text-align: center; padding-top: 153px;}
.publicity-tit01 span,.publicity-tit03 span{display: block; text-align: center;line-height:50px;font-size: 17px;font-weight: bold;color: #555555; }
.publicity-tit02{background:url(../images/recipes/img02.jpg)no-repeat; width: 800px; height: 640px;background-size: 100% 100%;  margin: 0 auto;position: relative;}
.publicity-tit02 p{text-align: center; padding-top: 200px; float: right; margin-right: 100px;}
.publicity-tit02 p img,.publicity-tit01 img,.publicity-tit03 p img{width:300px;}
.publicity-tit03{background:url(../images/recipes/img03.jpg)no-repeat; width: 800px; height: 640px;background-size: 100% 100%;  margin: 0 auto;position: relative;}
.publicity-tit03 p{text-align: center; padding-top: 125px;}
.school-txt{color:#ffffff; display: block; text-align: center;position: absolute; width: 800px; text-align: center; bottom: 50px;font-size: 17px;font-weight: bold;}
/*个人中心*/
.layui-tab-title1 {border-bottom: 1px solid #DDDDDD; font-size:0;position: relative; height: 35px; background: #FAFAFA;}
.layui-tab-title1  li{height:35px;display:inline-block;vertical-align:middle;
 font-size:14px;
 line-height:35px;
 min-width:65px;
 padding:0 15px;
 text-align:center; cursor:pointer; color: #A1A1A1;
}
.layui-tab-title1 li:first-child {
    /*border-right: none;*/
}
.layui-tab-title1  li.layui-this{background: #ffffff;color: #333333; border-bottom: none; line-height: 36px; height: 36px;}
.layui-tab-title1 .layui-this::after {
    position: absolute;
    left: 0;
    top: 0;
    content: "";
    width:96px;
    height: 35px;
    border-right:1px solid #DDDDDD;
    border-bottom-color: #FAFAFA;
    box-sizing: border-box;
    pointer-events: none;
}

@media screen and (max-width: 1400px) and (min-width: 1280px){
	.loginContent .loginArea {
		height: 440px;
	}
}
/*食谱管理*/
.info-list{margin: 8px 0 7px -7px;}
.info-list li{display: inline;font-size: 14px;border-right: 1px solid #e1e1e1;padding: 0 10px 0 7px;cursor: pointer;}
.info-list li:last-child{border: none;}
.recipeinfo-cell{ margin: 10px 0;padding: 0 0 10px 0;border-bottom: 1px solid #eeeeee;min-height: 120px;position: relative;}
.recipeinfo-cell:last-child{padding-bottom: 0;border: none;}
.opr-label a{color: #4f94e3;font-size: 14px;text-decoration: underline;margin: 0 5px;}
.recipeinfo-cell .white-tit{width: 210px;white-space: nowrap;text-overflow: ellipsis;-o-text-overflow: ellipsis;overflow: hidden;display: block;}
/*公示模板设置*/
.module-con{font-size: 0;white-space: nowrap;overflow-x: auto;}
.module-cell{text-align: center;display: inline-block;font-size: 14px;margin: 0 20px 0 0;vertical-align: top;}
.module-sub{margin: 0 20px;padding: 15px 0;border-bottom: 1px solid #e7ebf4;position: relative;}
.tab-btn{width: 19px;height: 50px;cursor: pointer;position: absolute;}
.sel-recipe{padding: 13px 15px;}
.sel-recipe.current{background: #eff5f7;}
/*公示模板设置*/
.layui-layer-btn .layui-layer-btn0{border-color: #025293;
background-color: #025293;}