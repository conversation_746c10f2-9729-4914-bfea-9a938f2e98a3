﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>报表条件</title>
    <link type="text/css" rel="stylesheet" href="../../css/reset.css" />
    <link type="text/css" rel="stylesheet" href="../../layui-btkj/css/layui.css" />
    <link rel="stylesheet" type="text/css" href="../../css/icon.css" />
    <link rel="stylesheet" type="text/css" href="../../css/commonstyle-layui-btkj.css" />
    <link rel="stylesheet" type="text/css" href="../../css/style.css" />
    <style type="text/css">
        .default-form .layui-form-label { width: 100px; color: #778CA2; }

        .webuploader-container { height: 85px; }

        .course-cen .webuploader-pick { position: relative; display: inline-block; cursor: pointer; background: #1da89e; padding: 2px 10px; color: #fff; text-align: center; border-radius: 0px; overflow: hidden; }
        .coverart p { text-align: center; color: #666666; font-size: 12px; margin: 5px 0; }
        .coverart { height: auto; border: 1px solid #eee; background: #FFFFFF; box-sizing: border-box; border-radius: 3px; text-align: center; overflow: hidden; }


        .upload-cell { height: auto; }
        #divcustominfo .layui-field-box { padding: 0 15px; }
        .webuploader-pick { background: none !important; }
        .tab_sysptom td {
            border: 1px solid #CBD6E1;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="layui-page" style="padding: 0 15px;">
        <form id="form" action="" class="layui-form form-display" lay-filter="formOk">
            <div class="default-form" style="margin: 20px 10px;">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>发病日期：</label>
                        <div class="layui-input-block" style="min-height: 30px; margin-left: 0px; position: relative; float: left; width: 150px;">
                            <input autocomplete="off" class="layui-input alendar-padding" type="text" id="txtstartdate" placeholder="发病日期" style="width: 150px; display: inline-block;" lay-verify="required" notip="1" />
                            <i class="layui-alendar">
                                <img id="iconstartdate" src="../../images/cal_icon.png" style="width: 14px;" /></i>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>班级：</label>
                        <div class="layui-input-block">
                            <select id="selclass" lay-filter="selclass" lay-verify="required"></select>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>幼儿：</label>
                        <div class="layui-input-block" style="width: 150px;">
                            <select id="selstu" lay-filter="selstu" lay-verify="required|sheckstu"></select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">性别：</label>
                        <div class="layui-input-block">
                            <label id="labsex"></label>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">年龄：</label>
                        <div class="layui-input-block">
                            <label id="labage"></label>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">确认日期：</label>
                        <div class="layui-input-block" style="min-height: 30px; margin-left: 0px; position: relative; float: left; width: 150px;">
                            <input autocomplete="off" class="layui-input alendar-padding" type="text" id="txtconfirmdate" placeholder="确认日期" style="width: 150px; display: inline-block;" />
                            <i class="layui-alendar">
                                <img id="iconconfirmdate" src="../../images/cal_icon.png" style="width: 14px;" /></i>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">诊断单位：</label>
                        <div class="layui-input-block">
                            <input class="layui-input" type="text" id="txthospitalname" placeholder="请输入诊断单位" style="width: 486px;" maxlength="100" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">症状体征：</label>
                        <div class="layui-input-block">
                            <input class="layui-input" type="text" id="txtsymptom" placeholder="请输入症状体征" style="width: 486px;" maxlength="200" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">诊断：</label>
                        <div class="layui-input-block">
                            <div id="txtzhenduan" class="xm-select-demo" style="display: inline-block;width:220px;"></div>
<!--                            <div id="othercrb" style="display: inline-block;vertical-align: top;"></div>-->
                            <input class="layui-input" type="text" id="othercrb" placeholder="请输入诊断" style="width: 255px;float: right;margin-left: 10px;display: none;" maxlength="50" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">
                            家庭成员有无 &nbsp;<br />
                            类似病例：</label>
                        <div class="layui-input-block">
                            <input name="radlikeness" type="radio" value="1" title="有" />
                            <input name="radlikeness" type="radio" value="2" title="无" checked="checked" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">上报情况：</label>
                        <div class="layui-input-block">
                            <input class="layui-input" type="text" id="txtsbqk" placeholder="请输入上报情况" style="width: 486px;" maxlength="100" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">病情追踪：</label>
                        <div class="layui-input-block" >
                            <table id="tabsymptom" class="tab_sysptom" style="border-collapse:collapse; border-spacing: 0; width: 100%; color: black;border: 1px solid #000;">
                                <thead>
                                    <tr>
                                        <td style="width: 150px;">日期</td>
                                        <td style="width: 218px;">症状</td>
                                        <td style="width: 65px;"></td>
                                    </tr>
                                </thead>
                                <tbody id="diseaserecord"></tbody>
                            </table>
                            <a class="layui-btn" style="vertical-align: top;margin-top: 5px;" id="btnadd">添加</a>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">离园日期：</label>
                        <div class="layui-input-block" style="min-height: 30px; margin-left: 0px; position: relative; float: left; width: 150px;">
                            <input autocomplete="off" class="layui-input alendar-padding" type="text" id="txtlydate" placeholder="离园日期" style="width: 150px; display: inline-block;" />
                            <i class="layui-alendar">
                                <img id="iconlydate" src="../../images/cal_icon.png" style="width: 14px;" /></i>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">返园日期：</label>
                        <div class="layui-input-block" style="min-height: 30px; margin-left: 0px; position: relative; float: left; width: 150px;">
                            <input autocomplete="off" class="layui-input alendar-padding" type="text" id="txtfydate" placeholder="返园日期" style="width: 150px; display: inline-block;" />
                            <i class="layui-alendar">
                                <img id="iconfydate" src="../../images/cal_icon.png" style="width: 14px;" /></i>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">返园症状检查：</label>
                        <div class="layui-input-block">
                            <input class="layui-input" type="text" id="txtfycheck" placeholder="请输入返园症状检查" style="width: 486px;" maxlength="100" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">复课证明：</label>
                        <div class="layui-input-block">
                            <input class="layui-input" type="text" id="txtfkzm" placeholder="请输入备注" style="width: 486px;" maxlength="100" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">照片：</label>
                    <div class="layui-input-block">
                        <div class="coverart" style="padding-bottom: 10px;">
                            <div class="upload-con">
                                <div id="divlistfiledata" class="pic-show img_wrapper" style="display: none; text-align: left; float: left;" lay-verify="chkfile">
                                </div>
                                <div style="display: block; width: 100%; margin: 0 auto; float: left;">
                                    <div id="uploadBtn" style="margin: 0px;" lay-verify="fileurl">
                                        <img src="../../images/downloadico.png" style="width: 69px; height: 67px; margin: 14px 0 0 0px;" />
                                    </div>
                                    <p style="line-height: 11px;">点击上传照片</p>
                                    <p style="line-height: 11px;">支持扩展名：*jpg*.pjpeg;*.jpeg;*.png;*.gif，大小不超过10MB</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div style="display: none;">
                    <div class="layui-input-block">
                        <a id="btnok" class="layui-btn" lay-submit="lay-submit">立即提交</a>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <script type="text/javascript" data-main="../../js/richang/diseaseadd" src="../../sys/require.min.js"></script>
</body>
</html>
