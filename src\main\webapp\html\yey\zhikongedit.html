﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>园所质控</title>
    <link type="text/css" rel="stylesheet" href="../../css/reset.css"/>
    <link type="text/css" rel="stylesheet" href="../../layui-btkj/css/layui.css"/>
    <link rel="stylesheet" type="text/css" href="../../css/icon.css"/>
    <link rel="stylesheet" type="text/css" href="../../css/commonstyle-layui-btkj.css"/>
    <link rel="stylesheet" type="text/css" href="../../css/style.css"/>
    <style type="text/css">
        .isview {
            margin-top: 0;
        }

        .layui-table-view {
            margin: 0px;
        }

        .layui-input-inline.isview {
            width: auto;
        }
.default-form .layui-form-label {
  width: 140px;
}
		.layui-input-block{margin-left: 160px;}
        /*.default-form .layui-form-label{width:200px;}*/
		.layui-form-item{margin-top: 10px;}
			.layui-alendar{top:10px;}
    </style>
</head>
<body>
<div class="layui-page" style="padding: 0 30px;">
    <form id="form" action="" class="layui-form form-display" lay-filter="formOk">
        <div class="default-form" style="margin:10px;">
            <div class="layui-form-item">
                <label class="layui-form-label"><em>*</em>园所名称：</label>
                <div class="layui-input-block">
                    <div class="layui-input-inline isedit" style="display:none;">
                        <div id="selyeyid" class="xm-select-demo" style=""></div>
                        <input type="hidden" id="txtyeyid" value="" lay-verify="requiredyeyid"/>
                    </div>
                    <div class="layui-input-inline isview" style="display:none;">
                        <label id="lbyeyid" style="display: contents;"></label>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label"><em>*</em>硬件标准：</label>
                    <div class="layui-input-block">
                        <div class="layui-input-inline isedit" style="display:none;">
                            <input type="number" maxlength="20" class="layui-input txtscore" id="txtyingjian" lay-reqtext="硬件标准必填项" placeholder="请输入分数" lay-verify="required"/>
                        </div>
                        <div class="layui-input-inline isview" style="display:none;">
                            <label id="lbyingjian" style="display: contents;"></label>
                        </div>
                        分
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label"><em>*</em>生活管理健康教育：</label>
                    <div class="layui-input-block">
                        <div class="layui-input-inline isedit" style="display:none;">
                            <input type="number" maxlength="20" class="layui-input txtscore" id="txtshenghuo" lay-reqtext="生活管理健康教育必填项" placeholder="请输入分数" lay-verify="required"/>
                        </div>
                        <div class="layui-input-inline isview" style="display:none;">
                            <label id="lbshenghuo" style="display: contents;"></label>
                        </div>
                        分
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label"><em>*</em>饮食营养：</label>
                    <div class="layui-input-block">
                        <div class="layui-input-inline isedit" style="display:none;">
                            <input type="number" maxlength="20" class="layui-input txtscore" id="txtyinshi" lay-reqtext="饮食营养必填项" placeholder="请输入分数" lay-verify="required"/>
                        </div>
                        <div class="layui-input-inline isview" style="display:none;">
                            <label id="lbyinshi" style="display: contents;"></label>
                        </div>
                        分
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label"><em>*</em>疾病预防：</label>
                    <div class="layui-input-block">
                        <div class="layui-input-inline isedit" style="display:none;">
                            <input type="number" maxlength="20" class="layui-input txtscore" id="txtjibing" lay-reqtext="疾病预防必填项" placeholder="请输入分数" lay-verify="required"/>
                        </div>
                        <div class="layui-input-inline isview" style="display:none;">
                            <label id="lbjibing" style="display: contents;"></label>
                        </div>
                        分
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label"><em>*</em>心理与五官保健：</label>
                    <div class="layui-input-block">
                        <div class="layui-input-inline isedit" style="display:none;">
                            <input type="number" maxlength="20" class="layui-input txtscore" id="txtxinli" lay-reqtext="心理与五官保健必填项" placeholder="请输入分数" lay-verify="required"/>
                        </div>
                        <div class="layui-input-inline isview" style="display:none;">
                            <label id="lbxinli" style="display: contents;"></label>
                        </div>
                        分
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label"><em>*</em>指标管理信息统计：</label>
                    <div class="layui-input-block">
                        <div class="layui-input-inline isedit" style="display:none;">
                            <input type="number" maxlength="20" class="layui-input txtscore" id="txtzhibiao" lay-reqtext="指标管理信息统计必填项" placeholder="请输入分数" lay-verify="required"/>
                        </div>
                        <div class="layui-input-inline isview" style="display:none;">
                            <label id="lbzhibiao" style="display: contents;"></label>
                        </div>
                        分
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label"><em>*</em>附加分：</label>
                    <div class="layui-input-block">
                        <div class="layui-input-inline isedit" style="display:none;">
                            <input type="number" maxlength="20" class="layui-input txtscore" id="txtfujia" lay-reqtext="附加分必填项" placeholder="请输入分数" lay-verify="required"/>
                        </div>
                        <div class="layui-input-inline isview" style="display:none;">
                            <label id="lbfujia" style="display: contents;"></label>
                        </div>
                        分
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label"><em>*</em>总分：</label>
                    <div class="layui-input-block">
                        <div class="layui-input-inline isedit" style="display:none;">
                            <input type="number" maxlength="20" class="layui-input" id="txtscore" lay-reqtext="分数必填项" placeholder="请输入分数" lay-verify="required"/>
                        </div>
                        <div class="layui-input-inline isview" style="display:none;">
                            <label id="lbscore" style="display: contents;"></label>
                        </div>
                        分
                    </div>
                </div>
            </div>

            <div class="layui-form-item" style="margin-top:10px;">
                <label class="layui-form-label">总体情况：</label>
                <div class="layui-input-block">
                    <div class="layui-input-inline isedit" style="display:none;width:550px;">
                        <textarea placeholder="请输入总体情况" class="layui-textarea" id="txtoverall"></textarea>
                    </div>
                    <div class="layui-input-inline isview" style="display:none;">
                        <label id="lboverall" style="display: contents;"></label>
                    </div>
                </div>
            </div>

            <div class="layui-form-item" style="margin-top:10px;">
                <label class="layui-form-label">主要问题：</label>
                <div class="layui-input-block">
                    <div class="layui-input-inline isedit" style="display:none;width:550px;">
                        <textarea placeholder="请输入主要问题" class="layui-textarea" id="txtquestion"></textarea>
                    </div>
                    <div class="layui-input-inline isview" style="display:none;">
                        <label id="lbquestion" style="display: contents;"></label>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label"><em>*</em>质控时间：</label>
                    <div class="layui-input-block">
                        <div class="layui-input-inline isedit" style="display:none;">
                            <input autocomplete="off" class="layui-input alendar-padding" type="text" id="txtverifieddate" placeholder="质控时间" style=" display: inline-block;" lay-verify="required"/>
                            <i class="layui-alendar"><img id="iconverifieddate" src="../../images/cal_icon.png" style="width: 14px;"/></i>
                        </div>
                        <div class="layui-input-inline isview" style="display:none;">
                            <label id="lbverifieddate" style="display: contents;"></label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label"><em>*</em>质控人员：</label>
                    <div class="layui-input-block">
                        <div class="layui-input-inline isedit" style="display:none;">
                            <input type="text" maxlength="100" class="layui-input" id="txtpersons" lay-reqtext="质控人员必填项" placeholder="请输入质控人员" lay-verify="required"/>
                        </div>
                        <div class="layui-input-inline isview" style="display:none;">
                            <label id="lbpersons" style="display: contents;"></label>
                        </div>
                    </div>
                </div>
            </div>
            <div style="display:none;">
                <div class="layui-input-block">
                    <a id="btnok" class="layui-btn" lay-submit=lay-submit>立即提交</a>
                </div>
            </div>
        </div>
    </form>
</div>
<script data-main="../../js/yey/zhikongedit" src="../../sys/require.min.js"></script>
</body>
</html>