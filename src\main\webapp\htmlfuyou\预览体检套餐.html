<!DOCTYPE html>
<html>
	    <head>
	    <title>预览体检套餐</title>
	    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
	    <script type="text/javascript" src="../layui-btkj/layui.js"></script>
	    <script type="text/javascript" src="../sys/jquery.js"></script>
	    <link rel="stylesheet" href="../css/style.css"/>
	    <link rel="stylesheet" href="../css/icon.css"/>
	    <script type="text/javascript">
	        document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
	    </script>
	    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
			<style>
			body {
				background: #fff;
			}
			.incr-chat {
				width: 850px;
				margin: 0px auto;
				padding: 0 40px
			}
			.physical-tab td{ padding:0 5px}
			</style>
	    </head>
<body>
      <section class="incr-chat" style="display: ;">
              <h3 class="noticeTitle bg1" style="margin-top:10px;"><span class="lanflag">预览体检套餐</span></h3>
	         <div class="physical-tab" style="margin: 10px 0;">
               <h3  style="text-align:center; line-height:40px; font-weight:bold; font-size:16px; margin-top:10px">【单人入职特惠体检套餐】的检查项目</h3>
               <table border="1" cellpadding="0" cellspacing="0" style="width: 90%;border-collapse: collapse; margin: 0px auto">
                       <tr style="background: #F1F1F1;">
                         <td colspan="2" style="width:20%;">体检项目</td>
                         <td style="width: 30%;">指标</td>
                         <td style="width: 30%;">指标意义</td>
                         <td style="width: 10%;">男性</td>
                         <td style="width: 10%;">女性</td>
                       </tr>
                       <tr>
                         <td rowspan="7"><b>科室检查</b></td>
                         <td style="text-align: left"><b>一般检查A</b></td>
                         <td style="text-align: left">身高、体重、体重指数、收缩压、舒张压</td>
                         <td style="text-align: left">通过仪器测量人体基本健康指标。例如：血压是否正常，超重或肥胖</td>
                         <td>√</td>
                         <td>√</td>
                       </tr>
                       <tr>
                         <td style="text-align: left"><b>内科</b></td>
                         <td style="text-align: left">心、肺、肝、胰、神经系统等检查</td>
                         <td style="text-align: left">通过视、听体格检查方法，检查</td>
                         <td></td>
                         <td></td>
                       </tr>
                       <tr>
                         <td style="text-align: left"><b>外科（男）</b></td>
                         <td style="text-align: left">皮肤、四肢关节、乳房</td>
                         <td style="text-align: left">通过视、听体格检查方法，检查</td>
                         <td></td>
                         <td></td>
                       </tr>
                       <tr>
                         <td style="text-align: left"><b>外科（女）</b></td>
                          <td style="text-align: left">皮肤、四肢关节、外生殖器</td>
                          <td style="text-align: left">通过视、听体格检查方法，检查</td>
                         <td></td>
                         <td></td>
                       </tr>
                       <tr>
                         <td style="text-align: left"><b>眼科</b></td>
                         <td style="text-align: left">视力、外延、眼底</td>
                         <td style="text-align: left">通过视、听体格检查方法，检查</td>
                         <td></td>
                         <td></td>
                       </tr>
                       <tr>
                         <td style="text-align: left"><b>耳鼻喉检查</b></td>
                         <td style="text-align: left">皮肤、四肢关节、乳房</td>
                         <td style="text-align: left">通过视、听体格检查方法，检查</td>
                         <td></td>
                         <td></td>
                       </tr>
                       <tr>
                         <td style="text-align: left"><b>听力</b></td>
                         <td style="text-align: left">皮肤、四肢关节、外生殖器</td>
                          <td style="text-align: left">通过视、听体格检查方法，检查</td>
                         <td></td> 
                          <td></td>
                       </tr>
                     </tbody>
                   </table>                 
            </div>
             <div style="text-align: center;margin-top: 30px;">
				  <button type="" class="layui-btn layui-btn-primary" style="border:1px solid #333333; padding:0 25px; color:#333333; border-radius:5px;">关闭</button>
		  </div>
        </section>
        
		<script>
		//Demo
		layui.use('form', function(){
		  var form = layui.form;
		  
		  //监听提交
		  form.on('submit(formDemo)', function(data){
		    layer.msg(JSON.stringify(data.field));
		    return false;
		  });
		});
		</script>
</body>
</html>
