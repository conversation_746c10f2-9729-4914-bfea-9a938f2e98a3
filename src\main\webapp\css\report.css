/*对话框箭头*/
.triangle-out-bottom{
    position: absolute;
    border: 6px solid transparent;
    border-right-color: #5ba7fd;
    bottom: -10px;
    left: 12px;
    margin-left: -6px;
    border-left:6px solid transparent;
    transform:rotate(-90deg);
    -ms-transform:rotate(-90deg); 	/* IE 9 */
    -moz-transform:rotate(-90deg); 	/* Firefox */
    -webkit-transform:rotate(-90deg); /* Safari å’Œ Chrome */
    -o-transform:rotate(-90deg); 	/* Opera */
}
/*复选框*/
.set-checkbox-blue input[type="checkbox"]{-webkit-appearance: none;width: 14px;height: 14px;border: 1px solid #cccccc;outline: none;border-radius: 2px;vertical-align: top;margin: 3px 5px;}
.set-checkbox-blue input[type="checkbox"]:checked{background: url(../images/report/icon_checkbox_HL.png) 50%;background-size:14px 14px;outline: none;border: none;border-radius: 1px;}
/*报告*/
.layui-col-xs5{width: 19.9999992%;}
.report-sub .mark-div{background: #4a90e2;font-size: 15px;color: #ffffff;padding: 7px 15px;min-width: 84px;max-width: 154px;display: inline-block;text-align: center;border-radius: 0 30px 30px 0;position: absolute;top: 20px;left: -8px;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;}
.report-sub{height: 272px;margin: 25px 14px 35px 14px;background: url(../images/report/booklet_bg.png) no-repeat center bottom;background-size: 100% 46px;padding: 0 20px;}
.report-cell{width: 184px;height: 236px;margin: 0 auto;position: relative;}
.report-cell .report-img{width: 184px;height: 236px;}
.report-cell .add-div{text-align: center;position: absolute;width: 100%;margin-top: 75px;cursor: pointer;}
.report-cell .add-div p{margin-top: 15px;font-size: 17px;color: #4a90e2;}
/*创建报告弹框*/
.caleninput{background: url(../images/report/icon_calen.png)no-repeat right 8px center;background-size: 18px;}
.report-form{border: 1px solid #dddddd;border-top: 2px solid #eeeeee;border-bottom: 2px solid #eeeeee;margin: 13px 16px;}
.report-form h5{background: #f8f8f8;color: #3a3a3a;font-size: 14px;padding: 8px 7px;border-bottom: 2px solid #ebebeb;font-weight: bold;}
.mark-num{display: inline-block;background: #4a90e2;color: #ffffff;width: 24px;height: 24px;line-height: 24px;text-align: center;border-radius: 50%;margin-right: 8px;font-weight: normal;}
.checkbox-sel{padding: 10px 15px;}
.checkbox-sel label{margin: 3px 30px 3px 0;display: inline-block;}
/*page1*/
.page-content{height: 859px;color: #333333;font-family: '黑体';position: absolute;top: 50%;left: 50%;transform: translate(-50%,-50%);width: 100%;text-align: center;font-size: 0;}
.page-cell{width: 604px;height: 859px;font-size: 14px;background-size: 100% 100%!important;position: relative;display: inline-block;vertical-align: top;text-align: left;}
/*.page1-lefttxt{margin-left: 140px;}*/
.page1-lefttxt h5{font-size: 30px;font-family: "zhankuxiaowei";border-bottom: 1px solid #AFD4EA;display: inline-block;}
.page1-lefttxt p{font-size: 15px;margin-top: 7px!important;color: #9C9A9A;font-family: "fangzhengxiyuanjianti";font-weight: lighter;}
.page1-lefttxt ul{font-size: 12px;margin-top: 30px;line-height: 28px;font-family: "fangzhengxiyuanjianti";font-size: 12px;font-weight: lighter;}
/*.btm-txt{font-size: 10px;color: #ffffff;position: absolute;bottom: 20px;width: 100%;text-align: center;}*/
/*.page1-rightimg{margin: 150px 66px 0 66px;}*/
.page1-righttxt{margin: 25px 50px;font-size: 11px;color: #666666;text-indent: 2em;line-height: 20px;}
/*.pagenum-left{width: 36px;height: 49px;background: url(../images/pagebg_left.png)no-repeat;background-size: 100% 100%;position: absolute;bottom: 7px;text-align: center;line-height: 55px;left: 7px;}*/
/*.pagenum-right{width: 36px;height: 49px;background: url(../images/pagebg_right.png)no-repeat;background-size: 100% 100%;position: absolute;bottom: 7px;text-align: center;line-height: 55px;right: 7px;}*/
.pagenum-left i{margin-left: 5px;}
.pagenum-right i{margin-right: 5px;}
.title-page{margin: 338px 0 0 20px;color: #18a094;text-align: center;}
.title-page h1{font-size: 45px;font-weight: bold;}
.title-page h5{font-size: 32px;font-weight: bold;margin-top: 18px;}
.page-name{color: #ffffff;font-size: 12px;position: absolute;right: 30px;top: 4px;}
.gray-sub{background: #f6f6f6;margin: 10px 50px;}
.graytxt-sub{font-size: 12px;color: #4f4f4f;display: inline-block;vertical-align: top;line-height: 22px;}
.page-num{position: absolute;bottom: 12px;font-size: 12px;border: 1px solid #ffffff;text-align: center;color: #ffffff;min-width: 14px;padding: 1px 3px;}
/*page2*/
.progressbar_1{
    background-color:#f0f0f0;
    height: 4px;
    width:68%;
    color:#222222;
    border-radius:0;
    margin: 5px 0;
    display: inline-block;
    vertical-align: middle;
    margin: 0 10px 0 5px;
}
.progressbar_1 .bar {
    background-color:#2890e7;
    height:4px;
    border-radius:0;
    position: relative;
}
.progressbar_1 .val-num{
    position: absolute;
    right: 4px;
    font-size: 12px;
    color:red;
    height: 4px;
    min-width: 20px;
}
.page-top{background: #e1efef;border-left: 9px solid #189f98;height: 32px;line-height: 32px;display: inline-block;padding: 0 20px 0 10px;margin-bottom: 15px;}
/*page3*/
.title-btn{background: #48c5bd;height: 20px;line-height: 20px;text-align: center;color: #ffffff;display: inline-block;border-radius: 20px;padding: 0 13px;font-size: 12px;}
.progress-sub{margin: 15px 0;font-size: 12px;color: #484848;}
.progress-sub label{width: 70px;text-align: right;display: inline-block;}
.remind-con{background: #ffebe0;margin: 35px 20px;font-size: 13px;padding: 5px 15px 7px 15px;border-radius: 5px;min-height: 38px;}
.appendix-div{font-size: 12px;color: #505050;margin: 20px 0;display: inline-block;}
.appendix-img{width: 18px;vertical-align: top;margin-top: -2px;}
/*page4*/
.proportion-sub{display: inline-block;min-width: 60px;text-align: center;}
.proportion-num{width: 36px;background: #5ba7fd;padding: 1px 12px;color: #ffffff;border-radius: 20px;position: absolute;bottom: 22px;}
.proportion-div{height: 14px;position: absolute;bottom: 0;}
/*page6*/
.advise-sub{padding: 24px 35px;background: #faf2e7;border: 2px dashed #ccad7e;height: 160px;border-radius: 14px;margin: 17px 0;color: #514841;font-size: 13px;line-height: 24px;}
.advise-num{width: 13px;height: 13px;line-height: 13px;border-radius: 50%;border: 1px solid #d47c34;background: #f98412;color: #ffffff;display: inline-block;text-align: center;margin-right: 5px;}
/*page9*/
.circle-total{width: 96px;height: 96px;margin: 15px auto;text-align: center;background: #4990e2;border-radius: 50%;color: #ffffff;}
.mark-sign{position: relative;width: 96px;display: inline-block;vertical-align: top;margin-top: 2px;}
.mark-sign:before{content: "";border-top: 1px solid #f36677;position: absolute;top: 0;width: 100%;left: 0;}
.mark-sign:after{content: "";width: 10px;height: 10px;border-radius: 50%;display: inline-block;border: 2px solid #f36677;box-sizing: border-box;position: absolute;right: -9px;top: -4px;}
.mark-name{display: inline-block;vertical-align: top;margin: -4px 0 0 6px;}
.mark-right{position: absolute;color: #4e4e4e;font-size: 12px;right: -75px;top: 50%;}
.total-sub{display: inline-block;margin: 30px 0;position: absolute;width: 100%;left: 0;z-index: 99;}
.total-con{position: relative;height: 77px;margin: 6px auto;}
.total-con:nth-of-type(2) .mark-sign:before{border-top: 1px solid #00aca0;}
.total-con:nth-of-type(2) .mark-sign:after{border: 2px solid #00aca0;}
.total-con:nth-of-type(3) .mark-sign:before{border-top: 1px solid #56d98b;}
.total-con:nth-of-type(3) .mark-sign:after{border: 2px solid #56d98b;}
.total-con:nth-of-type(4) .mark-sign:before{border-top: 1px solid #77b5fe;}
.total-con:nth-of-type(4) .mark-sign:after{border: 2px solid #77b5fe;}
/*page11*/
.num-bg{margin:40px 0 0 0;padding: 33px 35px;border-radius: 7px;height: 92px;}
.note-sub{background: #edf6ff;color: #494c51;font-size: 12px;padding: 15px;margin: 10px;border-radius: 5px;line-height: 20px;}
/*page13*/
.kind-table{background: #ffffff;margin: 10px 15px;padding: 10px;}
.kind-table table{background: #ffffff;}
.kind-table table td{padding: 4px;font-size: 12px;border-bottom: 1px solid #3988b3;text-align: center;}
.kind-table table td>div{padding: 3px 0;}
.kind-table table th{background: #3987b7;color: #ffffff;font-weight: normal;font-size: 13px;}
.kind-table table td:nth-child(1),.kind-table table th:nth-child(1){background: #81d3f8;color: #ffffff;}
/*page15*/
.tower-text{line-height: 20px;padding: 2px 0;margin: 2px 0;position: relative;}
.tower-text span{width: 85px;display: inline-block;font-size: 12px;}
.tower-text:after{content: '';border-bottom: 1px dashed #cdcac1;position: absolute;bottom: 0;width: 140%;right: 0;}
.tower-text:last-child:after{content: none;}
.tower-title{background: #0a8f96;color: #ffffff;padding: 2px 10px;border-radius: 20px;}
.mark-note{width: 13px;margin-right: 10px;}
/*page25*/
.phy-title{margin: 0 0 5px -18px;font-size: 13px;}
.phy-title img{width: 35px;vertical-align: middle;margin: -6px 5px 0 0;}
/*page30*/
.bulb-img{width: 8px;vertical-align: top;margin: 2px 5px 0 0;}
/*封面*/
.cover-txt{font-size: 16px;line-height: 38px;}
.cover-txt li span{color: #259b8f;font-weight: bold;}
.cover-txt li p{display: inline-block;}
.cover-title{font-size: 45px;font-weight: bold;margin: 225px 0 0 70px;color: #ffffff;color: #ffffff;}
/*目录*/
.menu-ul li{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: space-between;border-bottom: 1px dashed #ededed;height: 38px;font-size: 12px;font-family: "zhankuxiaowei";color: #656565;}
.menu-ul li span{background: #ffffff;margin-top: 35px;padding: 0 25px;min-width: 100px;text-align: right;}
.menu-ul li label{background: #ffffff;margin-top: 35px;padding: 0 25px;}
.menu-ul li.level1{font-size: 14px;margin-top: 14px;}
.menu-ul li.level1 label{font-weight: bold;color: #103844;}


/*眼科*/
.eye-main{display: -webkit-box;display: -webkit-flex;display: flex;clear: both;color: #34495E;}
.eye-dlex{display: -webkit-box;display: -webkit-flex;display: flex;margin-right: 15px;}
.report-form h3{font-size: 14px;padding:0px 15px; background: #EEF6FF;color: #34495E; height: 59px;}
.report-form h3 li{ float: left; width: 170px;padding:20px 0px;}
.report-form h1{font-size: 14px;padding:10px 15px; background: #EEF6FF;border-bottom: 1px solid #CBD6E1;color: #34495E; height: 24px;}
.eye-left{width: 50%;border-right: 1px solid #CBD6E1;display: inline-block;color: #34495E;}
.report-form .eye-left h3{height: 66px;background: #F6F6F6;border-bottom: 1px solid #CBD6E1;  height: 35px; padding:15px 15px;}
.eye-left .layui-form-label{ padding:8px 5px 5px 0px;width: auto;}
.eyelidlist span{ float: left;display: block; padding-top: 3px;color: #34495E;}
.eyelidlist-rt{ float: left;width: 80%;}
.eyelidlist-lt{display: block; clear: both; padding-bottom: 15px; overflow: hidden;}

.eyelidlist-txt{ padding: 10px 15px;}
.eyelidlist-txt p{ line-height: 19px; margin-bottom: 5px;}
.eye-flex{display: -webkit-box;display: -webkit-flex;display: flex; }

.eye-left .eye-dlex  .layui-form-label{width: 100px;  padding: 8px 0px 5px 15px;}