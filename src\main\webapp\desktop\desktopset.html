<!DOCTYPE html>
<html style="height: auto">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>桌面设置</title>
    <link rel="stylesheet" href="../css/icon.css">
	<link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../desktop.css">
    <style>
        html {
            background-color: #333;
        }
        .icon_add_ico{vertical-align: top;margin-right: 5px;}
        .icon_add_ico:before{color: #ffffff;}
        .lm-style-top{position: relative;height: 30px;line-height: 30px;font-size: 14px;}
        .lm-style-rt{line-height: 30px;}  
        .desktop-cell{margin: 18px 0 5px 0;}      	
    </style>
</head>
<body>
	<section style="min-width: 780px;margin: 0 auto;">
		<h5 class="set-tab">
			<button type="button" class="layui-btn"><i class="iconfont icon_bg"></i>背景设置</button>
			<button type="button" class="layui-btn layui-btn-primary"><i class="iconfont icon_skin"></i>皮肤设置</button>
			<button type="button" class="layui-btn layui-btn-primary"><i class="iconfont icon_desk"></i>桌面管理</button>
			<button type="button" class="layui-btn layui-btn-primary" style="display: none"><i class="iconfont icon_imagetxt"></i>图文设置</button>
		</h5>		
		<div style="background: #ffffff;min-height: 410px;" id="divpages">
			<!--背景设置-->
			<div>
				<div style="padding: 5px 10px;">
					<ul class="bg-cell" id="ulbgs">
						<!--<li>
							<img src="../images/desktop/setbg_img1.jpg" class="bgimg">
							<img src="../images/desktop/checkbox_greenbg.png" class="sel-checkbox">
						</li>
						<li><img src="../images/desktop/setbg_img2.jpg" class="bgimg"></li>
						<li><img src="../images/desktop/setbg_img3.jpg" class="bgimg"></li>
						<li><img src="../images/desktop/setbg_img4.jpg" class="bgimg"></li>
						<li><img src="../images/desktop/setbg_img5.jpg" class="bgimg"></li>
						<li><img src="../images/desktop/setbg_img1.jpg" class="bgimg"></li>
						<li><img src="../images/desktop/setbg_img2.jpg" class="bgimg"></li>
						<li><img src="../images/desktop/setbg_img3.jpg" class="bgimg"></li>
						<li><img src="../images/desktop/setbg_img4.jpg" class="bgimg"></li>
						<li><img src="../images/desktop/setbg_img5.jpg" class="bgimg"></li>-->
					</ul>
				</div>
				<ul class="bg-cell bgsmall-cell" id="ulbgcolors">
					<li style="background: #20222a;" color="#20222a"></li>
					<li style="background: #00bfee;" color="#00bfee"></li>
					<li style="background: #0093d8;" color="#0093d8"></li>
					<li style="background: #426ebb;" color="#426ebb"></li>
					<li style="background: #6475de;" color="#6475de"></li>
					<li style="background: #93459e;" color="#93459e"></li>
					<li style="background: #ab4ec0;" color="#ab4ec0"></li>
					<li style="background: #ad1c16;" color="#ad1c16"></li>
					<li style="background: #ff4931;" color="#ff4931"></li>
					<li style="background: #ff5960;" color="#ff5960"></li>
					<li style="background: #ff7009;" color="#ff7009"></li>
					<li style="background: #ffb55a;" color="#ffb55a"></li>
					<li style="background: #84d158;" color="#84d158"></li>
					<li style="background: #00b059;" color="#00b059"></li>
					<li style="background: #00c8ce;" color="#00c8ce"></li>
					<li style="background: #775739;" color="#775739"></li>
					<li style="background: #af7a26;" color="#af7a26"></li>
					<li style="background: #bd5f00;" color="#bd5f00"></li>
				</ul>
				<div style="padding: 5px 15px 10px 15px;display: none">
					<button type="button" class="layui-btn"><i class="iconfont icon_add_ico"></i>添加图片</button>
				</div>
			</div>			
			<!--皮肤设置-->
			<div style="display: none;">
				<div style="padding: 5px 10px;">
					<ul class="bg-cell bgmiddle-cell setColor" style="min-height: 348px;" id="ultheme">
						<li style="background: #ad1c16;"><img src="../images/desktop/checkbox_greenbg.png" class="sel-checkbox"></li>
						<li style="background: #ff4931;"></li>
						<li style="background: #ff5960;"></li>
						<li style="background: #ff7009;"></li>
						<li style="background: #ffb55a;"></li>
						<li style="background: #84d158;"></li>
						<li style="background: #00b059;"></li>
						<li style="background: #00c8ce;"></li>
					</ul>
				</div>
			</div>			
			<!--桌面管理-->
			<div style="display: none;">
				<div style="padding: 5px 10px;min-height: 345px;">
					<ul class="bg-cell bgmiddle-cell" style="vertical-align: top;" id="uldesktab">
						<li>
							<span class="order-num">1</span>
							<i class="iconfont icon_close_white"></i>
							<div class="desktop-cell">
								<img src="../images/desktop/top/icon_baoyu.png">
							</div>
							<div class="edit-txt">
								<input type="text" placeholder="保育">
							</div>
						</li>
						<li>
							<span class="order-num">2</span>
							<i class="iconfont icon_close_white"></i>
							<div class="desktop-cell">
								<img src="../images/desktop/top/icon_baojiao.png">
							</div>
							<div class="edit-txt">
								<input type="text" placeholder="保教">
							</div>
						</li>
						<li>
							<span class="order-num">3</span>
							<i class="iconfont icon_close_white"></i>
							<div class="desktop-cell">
								<img src="../images/desktop/top/icon_richang.png">
							</div>
							<div class="edit-txt">
								<input type="text" placeholder="日常">
							</div>
						</li>
						<li>
							<span class="order-num">4</span>
							<i class="iconfont icon_close_white"></i>
							<div class="desktop-cell">
								<img src="../images/desktop/top/icon_bangong.png">
							</div>
							<div class="edit-txt">
								<input type="text" placeholder="办公">
							</div>
						</li>
					</ul>
					<div class="add-desk" id="btnadddesk">
						<div class="desktop-cell">
							<img src="../images/desktop/icon_adddesk.png">
						</div>
						<p>添加新桌面</p>
					</div>
				</div>
				<div style="padding: 5px 15px 10px 15px;">
					<button type="button" class="layui-btn" id="btnsavedesk"><i class="iconfont icon_return2" style="margin-right: 5px;"></i>保存设置</button>
					<button type="button" class="layui-btn" id="btnresetdesk" style="background-color: gray;"><i class="iconfont icon_return2" style="margin-right: 5px;"></i>还原桌面设置</button>
					<span style="font-size: 12px;color: #666666;margin-left: 10px;"><i class="iconfont icon_remind3" style="margin-right: 3px;"></i>请谨慎操作！</span>
				</div>
			</div>			
			<!--图文设置-->
			<div style="display: none;">
				<ul class="bg-cell imagetxt-cell" style="min-height: 348px;">
					<li>	
						<div class="font16-txt">
							<img src="../images/desktop/settxt_img.png" class="imagetxt-img">
							<p>16号标题字</p>
							<img src="../images/desktop/checkbox_greenbg.png" class="sel-checkbox">
						</div>	
						<div class="exp-txtfont">
							<p>大图标：110px*110px</p>
							<p>文字：16px</p>
						</div>
					</li>
					<li>					
						<div class="font14-txt">
							<img src="../images/desktop/settxt_img.png" class="imagetxt-img">
							<p>14号标题字</p>
						</div>	
						<div class="exp-txtfont">
							<p>大图标：80px*80px</p>
							<p>文字：14px</p>
						</div>
					</li>					
				</ul>
				<div style="padding: 5px 15px 10px 15px;">
					<span style="font-size: 12px;color: #666666;margin-left: 10px;"><i class="iconfont icon_remind3" style="margin-right: 3px;"></i>注：切换后，需刷新页面。修改图标大小不会变更位置，可到桌面点击右键，弹出框中选择排列图标，您也可以通过拖动重新整理
       图标位置。</span>
				</div>
			</div>
			
		</div>
	</section>
	
	<script data-main="desktopset" src="../sys/require.min.js"></script>
</body>
</html>
