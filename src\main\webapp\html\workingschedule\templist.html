﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>临时排班列表</title>
    <link rel="stylesheet" type="text/css" href="../../css/reset.css"/>
    <link rel="stylesheet" type="text/css" href="../../layui-btkj/css/layui.css"/>
    <link rel="stylesheet" type="text/css" href="../../css/icon.css"/>
    <link rel="stylesheet" type="text/css" href="../../styles/tbstyles.css"/>
    <link href="../../plugin/flexigrid/css/tbflexigrid.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="../../css/commonstyle-layui-btkj.css"/>
    <link rel="stylesheet" href="../../css/medical.css"/>
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + top.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <style type="text/css">
        html, body {
            background: #EAEFF3;
            overflow: hidden;
        }

        .layui-form-item {
            display: inline-block;
            vertical-align: top;
            margin: 5px 5px 0px 0;
        }

        .layui-form-label {
            line-height: 28px;
        }

    </style>
</head>
<body>
<div class="">
    <div class="content-medical">
        <div class="layui-form layui-comselect" style="padding:10px;">
            <div class="layui-form-item" style="">
                <label class="layui-form-label">科室：</label>
                <div class="layui-input-inline">
                    <select id="dept" lay-search lay-filter="selectDept"></select>
                </div>
            </div>
            <div class="layui-form-item" style="">
                <label class="layui-form-label">门诊：</label>
                <div class="layui-input-inline">
                    <select id="mzId" lay-search lay-filter="selectMzId"></select>
                </div>
            </div>
            <div class="layui-form-item layarea">
                <label class="layui-form-label">医生：</label>
                <div class="layui-input-inline" style="float: left;width:150px;">
                    <select id="doctor" lay-search lay-filter="selectDoc"></select>
                </div>
            </div>
            <div class="layui-form-item">
                <button class="layui-btn form-search" style="margin-left: 5px;vertical-align: top;" id="btnsearch">查询</button>
            </div>
        </div>
    </div>
    <div id="content" style="width: 100%;">
        <div class="marmain-cen">
            <div class="content-medical" id="divtable">
                <table id="laytable" lay-filter="laytable"></table>
            </div>
        </div>
    </div>
</div>
<script data-main="../../js/workingschedule/templist" src='../../sys/require.min.js'></script>
</body>
</html>
