<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>短信中心</title>
<meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
<link rel="stylesheet" href="../css/reset.css"/>
<link rel="stylesheet" href="../css/style.css"/>
<link rel="stylesheet" href="../css/icon.css"/>
<link rel="stylesheet" href="../layui-btkj/css/layui.css">
<link rel="stylesheet" href="../css/commonstyle-layui-btkj.css">
<link rel="stylesheet" href="../css/medical.css">
<script type="text/javascript" src="../layui-btkj/layui.js"></script>
<style>
html, body {
	background: #EAEFF3;
}
.layui-form-onswitch em {
	margin-left: 0;
}
.scenarios .layui-tab-title {
	margin-left: 15px;
	border: 1px solid #d8d8d8;
	margin: 20px 0;
	border-radius: 3px;
	height: 30px;
}
.scenarios .layui-tab-title li {
	line-height: 30px;
	font-size: 14px;
	color: #34495E;
}
.scenarios .layui-tab-title .layui-this::after {
	border-bottom: none;
	border-top: none;
}
.scenarios .layui-tab-title .layui-this {
	border: 1px solid #4A90E2;
	background: #E8F3FF;
	color: #4A90E2;
}
.scenarios .layui-tab-title .layui-this::after {
	height: 30px;
}
.scenarios .layui-tab-title li:first-child {
	border-right: none;
}
</style>
</head>
<body>
<div class="content-medical"  style="margin: 0px;border-radius:0px;">
	<div class="layui-tab-brief tab-tit" style="margin-bottom: 0;padding: 0px 10px 0 0;">
		<ul id="ultop-tab" class="common-tab layui-tab-title">
			<li class="layui-this">短信套餐</li>
			<li>短信设置</li>
		</ul>
	</div>
</div>
<!--短信套餐-->
<div  style=" margin:10px 15px 8px 15px;height: auto; padding: 10px 0 15px 0; ">
	<div class="textmessage">
		<p style="color: #666;position: absolute;top:10px;right: 15px;cursor: pointer;">查看使用记录></p>
		<p style="color: #323F54;font-weight: bold;line-height: 35px;">剩余短信</p>
		<p><b style="font-size:48px; color: #4A90E2;">300</b>/条</p>
		<div class="verify-term">
			<div style="padding: 0px 15px">
				<div class="success-state">
					<div class="progressbar_1">
						<div class="bar" style="width: 50%;"></div>
					</div>
					<span><b style="color:#4A90E2;">200</b>条/500条（40%）</span> </div>
			</div>
		</div>
	</div>
	<div class="textmessage" style="padding: 20px 0;">
		<p style="color: #323F54;line-height: 35px;font-size:28px;">马上购买套餐</p>
		<p style="color: #666;line-height: 30px">一键通知沟通无阻</p>
		<ul class="textmessage-list">
			<li>
				<p><b style="font-size:48px;font-weight: none;">3w</b>/条</p>
				<p style="color:#FF6500;font-size: 30px; "> ¥2999</p>
				<p style="text-decoration:line-through ">¥3000</p>
				<button class="layui-btn" style="margin-top: 20px; padding: 0 44px;">立即购买</button>
			</li>
			<li>
				<p><b style="font-size:48px;font-weight: none;">4w</b>/条</p>
				<p style="color:#FF6500;font-size: 30px; "> ¥4750</p>
				<p style="text-decoration:line-through ">¥5000</p>
				<button class="layui-btn" style="margin-top: 20px; padding: 0 44px;">立即购买</button>
			</li>
			<li style="margin-right: 0;background: #FFFCF1; border: 1px solid #DED7B7;">
				<div class="welcome-txt"><img src="../images/medical/tuij.png" style="width:14px; margin-right: 5px;">最受欢迎</div>
				<p><b style="font-size:48px;font-weight: none;">10w</b>/条</p>
				<p style="color:#FF6500;font-size: 30px; "> ¥9000</p>
				<p style="text-decoration:line-through ">¥10000</p>
				<button class="layui-btn" style="margin-top: 20px; padding: 0 44px;">立即购买</button>
			</li>
		</ul>
		<p style="color: #666;cursor: pointer; clear: both; ">历史订单></p>
	</div>
	<div class="textmessage" style="padding: 20px 0;">
		<p style="color: #323F54;line-height: 35px;font-size:28px;">应用场景</p>
		<div class="scenarios">
			<ul class="layui-tab-title" style="display: inline-block;">
				<li>短信验证码</li>
				<li class="layui-this">系统通知</li>
				<li>业务消息提醒</li>
			</ul>
		</div>
		<p style="color: #666;line-height: 30px">在APP 或网站等账号注册、验证登录、密码安全认证、支付认证、身份认证时，下发到手机的文本验证码（数字/数字+字母）。</p>
		<div class="phoneimg">
			<div class="phonetxt"> 【云妇幼康】验证码：11001，该验证码可用
				
				于登录或注册，0分钟内有效；如非您本人
				
				操作，可忽略本消息 </div>
		</div>
	</div>
</div>
短信设置
<div id="commondiv" style="background-color: white; margin:10px 15px 8px 15px;height: auto; padding: 10px 0 15px 0; display: none;">
	<div class="order-cell text-verification">
		<h5>短信验证</h5>
		<div style="border: 1px solid #e4e4e4; padding: 10px 20px;">
			<h3>登录验证</h3>
			<p>云妇幼康】验证码：11001，该验证码可用于登录或注册，10分钟内有效；如非您本人操作，可忽略本消息
				<label>保持打开</label>
			</p>
		</div>
	</div>
	<div class="order-cell text-verification">
		<h5>短信验证</h5>
		<div style="border: 1px solid #e4e4e4; padding: 10px 20px;">
			<h3>预约成功通知</h3>
			<div class="tong-list">【云妇幼康】尊敬的用户您好，您有一个***套餐名**待确认，请及时使用当前手机号码登录云妇幼康小程序进行确认
				<form class="layui-form" style="position: absolute;right:0px;top:0px;">
					<input type="checkbox" name="switch" lay-skin="switch" lay-text="开启|关闭" lay-filter="switchmapstyle" value="1" id="cbmapstyle" checked>
					<div class="layui-unselect layui-form-switch" lay-skin="_switch" ><em>OFF</em><i></i></div>
				</form>
			</div>
			<div class="tong-list">【云妇幼康】尊敬的用户您好，您有一个***套餐名**待确认，请及时使用当前手机号码登录云妇幼康小程序进行确认
				<form class="layui-form" style="position: absolute;right:0px;top:0px;">
					<input type="checkbox" name="switch1" lay-skin="switch" lay-text="开启|关闭" lay-filter="switchmapstyle" value="1" id="cbmapstyle" checked>
					<div class="layui-unselect layui-form-switch" lay-skin="_switch" ><em>OFF</em><i></i></div>
				</form>
			</div>
		</div>
		<div style="border: 1px solid #e4e4e4; padding: 10px 20px; border-top: none;">
			<h3>体检结果送达通知</h3>
			<div class="tong-list">【云妇幼康】尊敬的用户您好，您于2022年3月2日进行的***套餐名**体检，报告已发送至云妇幼康小程序，请及时使用当前手机号码登录云妇幼康小程序进行查看
				<form class="layui-form" style="position: absolute;right:0px;top:0px;">
					<input type="checkbox" name="switch" lay-skin="switch" lay-text="开启|关闭" lay-filter="switchmapstyle" value="1" id="cbmapstyle" checked>
					<div class="layui-unselect layui-form-switch" lay-skin="_switch" ><em>OFF</em><i></i></div> 
				</form>
			</div>
			<div class="tong-list">【云妇幼康】尊敬的用户您好，您于2022年3月2日进行的***套餐名**体检，报告已发送至云妇幼康小程序，请及时使用当前手机号码登录云妇幼康小程序进行查看
				<form class="layui-form" style="position: absolute;right:0px;top:0px;">
					<input type="checkbox" name="switch1" lay-skin="switch" lay-text="开启|关闭" lay-filter="switchmapstyle" value="1" id="cbmapstyle" checked>
					<div class="layui-unselect layui-form-switch" lay-skin="_switch" ><em>OFF</em><i></i></div>
				</form>
			</div>
		</div>
		<div style="border: 1px solid #e4e4e4; padding: 10px 20px; border-top: none;">
			<h3>健康证获得通知</h3>
			<div class="tong-list">云妇幼康】尊敬的用户您好，您于2022年3月2日进行的***套餐名**健康证体检，电子健康证已发送至云妇幼康小程序，请及时使用当前手机号码登录云妇幼康小程序进行查看
				<form class="layui-form" style="position: absolute;right:0px;top:0px;">
					<input type="checkbox" name="switch" lay-skin="switch" lay-text="开启|关闭" lay-filter="switchmapstyle" value="1" id="cbmapstyle" checked>
					<div class="layui-unselect layui-form-switch" lay-skin="_switch" ><em>OFF</em><i></i></div>
				</form>
			</div>
			<div class="tong-list">【云妇幼康】尊敬的用户您好，您于2022年3月2日进行的***套餐名**体检，报告已发送至云妇幼康小程序，请及时使用当前手机号码登录云妇幼康小程序进行查看
				<form class="layui-form" style="position: absolute;right:0px;top:0px;">
					<input type="checkbox" name="switch1" lay-skin="switch" lay-text="开启|关闭" lay-filter="switchmapstyle" value="1" id="cbmapstyle" checked>
					<div class="layui-unselect layui-form-switch" lay-skin="_switch" ><em>OFF</em><i></i></div>
				</form>
			</div>
		</div>
	</div>
	<div class="order-cell text-verification">
		<h5>系统消息</h5>
		<div style="border: 1px solid #e4e4e4; padding: 10px 20px;">
			<h3>健康证到期通知</h3>
			<div class="tong-list">【云妇幼康】尊敬的用户您好，您有一个***套餐名**待确认，请及时使用当前手机号码登录云妇幼康小程序进行确认
				<form class="layui-form" style="position: absolute;right:0px;top:0px;">
					<input type="checkbox" name="switch" lay-skin="switch" lay-text="开启|关闭" lay-filter="switchmapstyle" value="1" id="cbmapstyle" checked>
					<div class="layui-unselect layui-form-switch" lay-skin="_switch" ><em>OFF</em><i></i></div>
				</form>
			</div>
			<div class="tong-list">【云妇幼康】尊敬的用户您好，您有一个***套餐名**待确认，请及时使用当前手机号码登录云妇幼康小程序进行确认
				<form class="layui-form" style="position: absolute;right:0px;top:0px;">
					<input type="checkbox" name="switch1" lay-skin="switch" lay-text="开启|关闭" lay-filter="switchmapstyle" value="1" id="cbmapstyle" checked>
					<div class="layui-unselect layui-form-switch" lay-skin="_switch" ><em>OFF</em><i></i></div>
				</form>
			</div>
		</div>
	</div>
</div>
<script>
    Demo
    layui.use('form', function () {
        var form = layui.form;
        监听提交
        form.on('submit(formDemo)', function (data) {
            layer.msg(JSON.stringify(data.field));
            return false;
        });
    });
</script>
</body>
</html>
