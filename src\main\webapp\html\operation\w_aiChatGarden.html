<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>AI健康问答园所情况</title>
    <link rel="stylesheet" type="text/css" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" type="text/css" href="../../css/icon.css"/>
    <link rel="stylesheet" type="text/css" href="../../styles/tbstyles.css"/>
    <link href="../../plugin/flexigrid/css/tbflexigrid.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css"/>
    <script type="text/javascript">
        // 检查 top.objdata 是否存在，如果不存在则设置默认主题
        var curtheme = (top && top.objdata && top.objdata.curtheme) ? top.objdata.curtheme : 'backstage';
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + curtheme + '.css" type="text/css" media="screen"/>');
    </script>

    <style type="text/css">
        html, body {
            background: #EAEFF3;
            overflow: hidden;
        }
        .layui-form-item {
            display: inline-block;
            vertical-align: top;
            margin-right: 15px;
            margin-bottom: 10px;
        }

        .layui-form-label {
            line-height: 28px;
            width: 110px;
        }

        .layui-input-inline {
            width: 120px;
        }

        .layui-form-mid {
            line-height: 28px;
            width: 30px;
            text-align: center;
        }

        /* 表格自适应样式 */
        #content {
            overflow-x: auto;
        }

        .layui-table-box {
            overflow-x: auto;
        }

        @media screen and (max-width: 1200px) {
            .layui-table-cell {
                padding: 5px 8px;
                font-size: 12px;
            }
        }

        @media screen and (max-width: 768px) {
            .layui-table-cell {
                padding: 3px 5px;
                font-size: 11px;
            }
        }

    </style>
</head>
<body>
<div class="marmain">
    <div class="content-medical">
        <div class="search-container layui-form layui-comselect" style="padding:10px;">
            <!-- 日期范围 -->
            <div class="layui-form-item">
                <label class="layui-form-label">开始日期：</label>
                <div class="layui-input-inline">
                    <input id="datestart" type="text" autocomplete="off" placeholder="开始日期" class="layui-input">
                </div>
                <div class="layui-form-mid">至</div>
                <div class="layui-input-inline">
                    <input id="dateend" type="text" autocomplete="off" placeholder="结束日期" class="layui-input">
                </div>
            </div>
            
            <!-- 分类 -->
            <div class="layui-form-item">
                <label class="layui-form-label">分类：</label>
                <div class="layui-input-inline">
                    <select id="category" lay-filter="category">
                        <option value="">请选择</option>
                        <option value="喂科">喂科</option>
                        <option value="口腔">口腔</option>
                        <option value="生长发育">生长发育</option>
                    </select>
                </div>
            </div>
            
            <!-- 预约号码 -->
            <div class="layui-form-item">
                <label class="layui-form-label">预约号码：</label>
                <div class="layui-input-inline">
                    <select id="appointno" lay-filter="appointno">
                        <option value="">请选择</option>
                        <option value="1">有预约</option>
                        <option value="0">无预约</option>
                    </select>
                </div>
            </div>
            
            <!-- 幼儿园 -->
            <div class="layui-form-item">
                <label class="layui-form-label">幼儿园：</label>
                <div class="layui-input-inline">
                    <select id="kindergarden" lay-filter="kindergarden">
                        <option value="">请选择</option>
                    </select>
                </div>
            </div>
            
            <!-- 年级 -->
            <div class="layui-form-item">
                <label class="layui-form-label">年级：</label>
                <div class="layui-input-inline">
                    <select id="grade" lay-filter="grade">
                        <option value="">请选择</option>
                        <option value="小班">小班</option>
                        <option value="中班">中班</option>
                        <option value="大班">大班</option>
                    </select>
                </div>
            </div>
            
            <!-- 班级 -->
            <div class="layui-form-item">
                <label class="layui-form-label">班级：</label>
                <div class="layui-input-inline">
                    <select id="classname" lay-filter="classname">
                        <option value="">请选择</option>
                    </select>
                </div>
            </div>
            
            <!-- 幼儿性别 -->
            <div class="layui-form-item">
                <label class="layui-form-label">幼儿性别：</label>
                <div class="layui-input-inline">
                    <select id="childsex" lay-filter="childsex">
                        <option value="">请选择</option>
                        <option value="男">男</option>
                        <option value="女">女</option>
                    </select>
                </div>
            </div>
            
            <!-- 体检年龄 -->
            <div class="layui-form-item">
                <label class="layui-form-label">体检年龄：</label>
                <div class="layui-input-inline">
                    <select id="checkage" lay-filter="checkage">
                        <option value="">请选择</option>
                        <option value="3">3岁</option>
                        <option value="4">4岁</option>
                        <option value="5">5岁</option>
                        <option value="6">6岁</option>
                    </select>
                </div>
            </div>
            
            <!-- 关键字（腹部） -->
            <div class="layui-form-item">
                <label class="layui-form-label">关键字：</label>
                <div class="layui-input-inline">
                    <select id="abdominal" lay-filter="abdominal">
                        <option value="">请选择</option>
                        <option value="1">是</option>
                        <option value="0">否</option>
                    </select>
                </div>
            </div>
            
            <!-- 家长性别 -->
            <div class="layui-form-item">
                <label class="layui-form-label">家长性别：</label>
                <div class="layui-input-inline">
                    <select id="parentsex" lay-filter="parentsex">
                        <option value="">请选择</option>
                        <option value="男">男</option>
                        <option value="女">女</option>
                    </select>
                </div>
            </div>
            
            <!-- 家长年龄 -->
            <div class="layui-form-item">
                <label class="layui-form-label">家长年龄：</label>
                <div class="layui-input-inline">
                    <select id="parentage" lay-filter="parentage">
                        <option value="">请选择</option>
                        <option value="20-30">20-30岁</option>
                        <option value="30-40">30-40岁</option>
                        <option value="40-50">40-50岁</option>
                    </select>
                </div>
            </div>
            
            <!-- 访问设备 -->
            <div class="layui-form-item">
                <label class="layui-form-label">访问设备：</label>
                <div class="layui-input-inline">
                    <select id="device" lay-filter="device">
                        <option value="">请选择</option>
                        <option value="安卓">安卓</option>
                        <option value="苹果">苹果</option>
                        <option value="PC">PC</option>
                    </select>
                </div>
            </div>
            
            <!-- 区域分类 -->
            <div class="layui-form-item">
                <label class="layui-form-label">区域分类：</label>
                <div class="layui-input-inline">
                    <select id="areatype" lay-filter="areatype">
                        <option value="">请选择</option>
                        <option value="城区">城区</option>
                        <option value="郊区">郊区</option>
                    </select>
                </div>
            </div>
            
            <!-- 孩子与家长关系 -->
            <div class="layui-form-item">
                <label class="layui-form-label">孩子与家长关系：</label>
                <div class="layui-input-inline">
                    <select id="relationship" lay-filter="relationship">
                        <option value="">请选择</option>
                        <option value="父子">父子</option>
                        <option value="母子">母子</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
            </div>
            
            <!-- 关键字输入框 -->
            <div class="layui-form-item">
                <label class="layui-form-label">关键字：</label>
                <div class="layui-input-inline" style="width:200px;">
                    <input id="txtkeyword" type="text" autocomplete="off" placeholder="请输入幼儿姓名或家长姓名" class="layui-input">
                </div>
            </div>
            
            <!-- 查询按钮 -->
            <div class="layui-form-item">
                <button class="layui-btn layui-btn-normal" id="btnsearch">查询</button>
            </div>
        </div>
    </div>
    <div id="content" style="width: 100%;">
        <div class="marmain-cen">
            <div class="content-medical" id="divtable">
                <table id="laytable" lay-filter="laytable"></table>
            </div>
        </div>
    </div>
</div>

<script data-main="../../js/operation/w_aiChatGarden" src='../../sys/require.min.js'></script>
</body>
</html>
