﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>膳食健康大数据2</title>
    <link rel="stylesheet" href="../css/reset.css"/>
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../plugin/jqcloud/jqcloud.css">
    <script type="text/javascript">
        document.write("<link rel=\"stylesheet\" id=\"linktheme\" href=\"../css/" + parent.objdata.curtheme + ".css\" type=\"text/css\" media=\"screen\"/>");
    </script>
    <!--[if lt IE 9]>
    <script src="../sys/html5shiv.min.js"></script>
    <![endif]-->
    <style>
        .popTitle {
            color: #fff;
        }

        .wrap,
        .circle,
        .percent {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
        }

        .wrap {
            background-color: #eee;
            position: absolute;
            top: 3px;
            left: 50% !important;
            margin-left: -30px;
        }

        .circle {
            box-sizing: border-box;
            border: 20px solid #eee;
            clip: rect(0, 80px, 80px, 33px);
        }

        .clip-auto {
            clip: rect(auto, auto, auto, auto);
        }

        .percent {
            box-sizing: border-box;
            top: -20px;
            left: -20px;
        }

        .left {
            transition: transform ease;
            border: 20px solid #fc784f;
            clip: rect(0, 30px, 80px, 0);
        }

        .right {
            border: 20px solid #fc784f;
            clip: rect(0, 80px, 80px, 30px);
        }

        .wth0 {
            width: 0;
        }

        .num {
            position: absolute;
            box-sizing: border-box;
            width: 46px;
            height: 46px;
            line-height: 46px;
            text-align: center;
            font-size: 12px;
            left: 7px;
            top: 7px;
            border-radius: 50%;
            background-color: #fff;
            z-index: 1;
        }

        .num span {
            font-size: 12px;
        }

        .business {
            left: 22%;
        }

        .sick {
            left: 42%;
        }

        .other {
            left: 62%;
        }

        .reason {
            position: absolute;
            bottom: -25px;
            font-size: 12px;
            width: 100%;
            text-align: center;
        }

        .business .num,
        .business .reason {
            color: #fc784f;
        }

        .sick .num,
        .sick .reason {
            color: #52bfef;
        }

        .other .num,
        .other .reason {
            color: #cccccc;
        }

        .sick .left,
        .sick .right {
            border-color: #52bfef;
        }

        .other .left,
        .other .right {
            border-color: #cccccc;
        }

        .stat-chart {
            position: relative;
            width: 100%;
            height: 114px;
        }
        .yingyang{
            margin-left: 25px;
        }
    </style>
</head>
<body>
<div class="statistics" style="width:100%;height: 880px;">
    <div class="statistics-list" style="width: 30%;height: 100%;">
        <div class="trend-con" style="height: 44%;margin: 10px;">
            <h3>2017年食谱公示指数</h3>
            <div style="text-align: center;">
                <div style="margin-top: 40px;display: inline-block;width: 18%;vertical-align: top;">
                    <li style="margin-bottom: 10px" id="up"><img src="../images/arrow_up.png"></li>
                    <ul class="btn-txt" id="ulChangeMonth" style="height:165px;overflow: auto;">
                        <li style="margin-top:  0px;display: none" class="yue1"><a class="opr-btn" style="width: 50px;background:  #00748b;">1月</a></li>
                        <li style="margin-top: 37px;display: none" class="yue1"><a class="opr-btn" style="width: 50px;background: #00748b;">2月</a></li>
                        <li style="margin-top: 37px;display: none" class="yue1"><a class="opr-btn" style="width: 50px;background:  #00748b;">3月</a></li>
                        <li style="margin-top:  0px;display: none" class="yue2"><a class="opr-btn" style="width: 50px;background: #00748b;">4月</a></li>
                        <li style="margin-top: 37px;display: none" class="yue2"><a class="opr-btn" style="width: 50px;background:  #00748b;">5月</a></li>
                        <li style="margin-top: 37px;display: none" class="yue2"><a class="opr-btn" style="width: 50px;background: #00748b;">6月</a></li>
                        <li style="margin-top:  0px;" class="yue3"><a class="opr-btn" style="width: 50px;background:  #00748b;">7月</a></li>
                        <li style="margin-top: 37px;" class="yue3"><a class="opr-btn" style="width: 50px;background: #00748b;">8月</a></li>
                        <li style="margin-top: 37px;" class="yue3"><a class="opr-btn" style="width: 50px;background:  #00748b;">9月</a></li>
                        <li style="margin-top:  0px;display: none" class="yue4"><a class="opr-btn" style="width: 50px;background: #00748b;">10月</a></li>
                        <li style="margin-top: 37px;display: none" class="yue4"><a class="opr-btn" style="width: 50px;background: #00748b;">11月</a></li>
                        <li style="margin-top: 37px;display: none" class="yue4"><a class="opr-btn" style="width: 50px;background:  #00748b;">12月</a></li>
                    </ul>
                    <li style="margin-top: 10px;" id="down"><img src="../images/arrow_down.png"></li>
                </div>
                <div style="display: inline-block;width: 80%;vertical-align: top;">
                    <table class="chart-table">
                        <thead>
                        <tr>
                            <th><span class="txt-label">周一</span></th>
                            <th><span class="txt-label">周二</span></th>
                            <th><span class="txt-label">周三</span></th>
                            <th><span class="txt-label">周四</span></th>
                            <th><span class="txt-label">周五</span></th>
                        </tr>
                        </thead>
                        <tbody class="pie">

                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="trend-con" style="height: 51%;margin: 10px;">
            <h3>区域幼儿进食量【ACFI值】</h3>
            <div style="clear: both; height:420px " id="radar">
                <!--  雷达图 -->
            </div>
        </div>
    </div>
    <div class="statistics-list" style="width: 50%;height: 100%;">
        <div style="height: 57.5%;margin: 10px 0;">
            <div>
                <div style="width: 29%;display: inline-block;vertical-align: top;">
                    <div class="ill-tit" style="width:100%;margin-right: 10px;">公示幼儿园地图展示</div>
                </div>
                <div style="width: 12%;display: inline-block;vertical-align: top;text-align: center;margin-top: 26px;">
                    <img src="../images/data_img1.png" style="width: 67px;vertical-align: bottom;">
                </div>
                <div style="width: 58%;display: inline-block;vertical-align: top;float: right;">
                	<span class="set-txt-btn"><img src="../images/data_set.png">设置</span>
                    <hr class="layui-bg-blue" style="margin-top: 41px;background-color: #014567 !important;">
                </div>
            </div>
            <div class="data-set"></div>
            <div style="height: 85%;">
                <div class="data-num" style="width: 215px;position: absolute;z-index: 10">
                    <span class="data-num-middle"><label>8</label></span>
                    <span class="data-num-middle"><label>2</label></span>
                    <span class="data-num-middle"><label>·</label></span>
                    <span class="data-num-middle"><label>1</label></span>
                    <span class="data-num-middle"><label>7</label></span>
                    <span class="data-num-end"><label>分</label></span>
                </div>
                <div style="height: 100%" id="divmap"></div>
            </div>
        </div>
        <div class="trend-con" style="height: 38%;margin: 10px 0;">
            <h3 style="position: relative;">
            	各营养素在食谱中的比例分析
            	<span style="position: absolute;right: 15px;"><img src="../images/data_set.png"><i style="color: #fff;margin-left: 5px;">设置</i></span>
            </h3>
            <div class="data-set" style="margin-top: 10px;text-align: left;">
                <span class="yingyang" id="energy"><img src="../images/data_set.png">能量</span>
                <span class="yingyang" id="fat"><img src="../images/data_set.png">脂肪</span>
                <span class="yingyang" id="protein"><img src="../images/data_set.png">蛋白质</span>
                <span class="yingyang" id="VC"><img src="../images/data_set.png">VC</span>               
            </div>
            <div style="clear: both; height: 260px" id="sandain">
                <!-- 散点图 -->
            </div>

        </div>
    </div>
    <div class="statistics-list" style="width: 20%;height: 100%;">
        <div class="trend-con" style="margin-bottom: 15px;height: 53%;margin: 10px;">
            <h3>实时公示幼儿园</h3>
            <ul class="chart-txt chart-label" style="height: 82%;overflow-y: auto;">
                <li><i class="color-blue">白云区建设幼儿园</i><span>89.21分</span></li>
                <li><i class="color-blue">白云区建设幼儿园</i><span>89.21分</span></li>
                <li><i class="color-blue">白云区建设幼儿园</i><span>89.21分</span></li>
                <li><i class="color-blue">白云区建设幼儿园</i><span>89.21分</span></li>
                <li><i class="color-blue">白云区建设幼儿园白云区建设幼儿园</i><span>89.21分</span></li>
                <li><i class="color-blue">白云区建设幼儿园</i><span>89.21分</span></li>
                <li><i class="color-blue">白云区建设幼儿园</i><span>89.21分</span></li>
                <li><i class="color-blue">白云区建设幼儿园</i><span>89.21分</span></li>
                <li><i class="color-blue">白云区建设幼儿园</i><span>89.21分</span></li>
                <li><i class="color-blue">白云区建设幼儿园</i><span>89.21分</span></li>
                <li><i class="color-blue">白云区建设幼儿园</i><span>89.21分</span></li>
                <li><i class="color-blue">白云区建设幼儿园</i><span>89.21分</span></li>
                <li><i class="color-blue">白云区建设幼儿园</i><span>89.21分</span></li>
                <li><i class="color-blue">白云区建设幼儿园</i><span>89.21分</span></li>
            </ul>
        </div>
        <div class="trend-con" style="margin-bottom: 15px;height: 42%;margin: 10px;" id="divalleOuter">
            <h3>过敏食物标签云</h3>
            <div id="divalle" style="height: 300px"></div>
        </div>
    </div>
</div>
<script type="text/javascript" src="../sys/jquery.js"></script>
<script type="text/javascript" src="../sys/arg.js"></script>
<script type="text/javascript" src="../layui-btkj/layui.js"></script>
<script type="text/javascript" src="http://api.map.baidu.com/api?v=2.0&ak=bkpk4sh4cFcEGqgqjUG79DfRGhqNS7W8"></script>
<script type="text/javascript" src="../plugin/echarts/echarts.min.js"></script>
<script type="text/javascript" src="../plugin/echarts/extension/bmap.min.js"></script>
<script type="text/javascript" src="../plugin/echarts/map/js/china.js"></script>
<script type="text/javascript" src="../plugin/nicescroll/jquery.nicescroll.min.js"></script>
<script type="text/javascript" src="../plugin/jqcloud/jqcloud-1.0.4.js"></script>
<script type="text/javascript" src="../js/show_recipe.js"></script>
</body>
</html>
