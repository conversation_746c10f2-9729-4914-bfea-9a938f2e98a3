<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>AI入园体检分析-体检详细列表页面</title>
    <link rel="stylesheet" type="text/css" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" type="text/css" href="../../css/icon.css"/>
    <link rel="stylesheet" type="text/css" href="../../styles/tbstyles.css"/>
    <link href="../../plugin/flexigrid/css/tbflexigrid.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css"/>
    <script type="text/javascript">
        // 检查 top.objdata 是否存在，如果不存在则设置默认主题
        var curtheme = (top && top.objdata && top.objdata.curtheme) ? top.objdata.curtheme : 'backstage';
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + curtheme + '.css" type="text/css" media="screen"/>');
    </script>

    <style type="text/css">
        .layui-table-view .layui-table td, .layui-table-view .layui-table th {
            padding:   0px;
            border-right: none;
        }
        .layui-form-item {
            display: inline-block;
            vertical-align: top;
            margin: 5px 5px 0px 0;
        }

        .layui-form-item .layui-input-inline,
        .layui-form-item .layui-form-label,
        .layui-form-item .layui-form-mid {
            display: inline-block;
            vertical-align: top;
        }

        .layui-form-label {
            line-height: 28px;
        }

        /* 强制分页显示在右边 */
        .layui-table-page .layui-laypage {
            float: right !important;
            margin: 0 !important;
        }

        .layui-table-page .layui-laypage span,
        .layui-table-page .layui-laypage a,
        .layui-table-page .layui-laypage input,
        .layui-table-page .layui-laypage button {
            float: left !important;
        }

        .layui-table-page .layui-laypage .layui-laypage-count {
            float: right !important;
            margin-right: 10px !important;
        }

        .layui-table-page .layui-laypage .layui-laypage-limits {
            float: right !important;
            margin-right: 10px !important;
        }

    </style>
</head>
<body>
<div class="marmain">
    <div class="content-medical">
        <div class="layui-form layui-comselect" style="padding:10px;">
            <!-- 第一行 -->
            <div class="layui-form-item"  style="padding-bottom:5px;">
                <div class="layui-form-item" style="margin-bottom: 10px;">
                <label class="layui-form-label" style="width: 100px;">体检安排：</label>
                <div class="layui-input-inline" style="width:160px; margin-right: 15px;">
                    <select id="physicalarrangement" lay-filter="physicalarrangement">
                        <option value="">请选择</option>
                    </select>
                </div>
                </div>
                <div class="layui-form-item" style="margin-bottom: 10px;">
                <label class="layui-form-label" style="width: 80px;">园所名称：</label>
                <div class="layui-input-inline" style="width:140px;">
                    <select id="kindergarten" lay-filter="kindergarten">
                        <option value="">请选择</option>
                    </select>
                </div>
                </div>
                <div class="layui-form-item" style="margin-bottom: 10px;">
                <label class="layui-form-label" style="width: 70px;">关键字：</label>
                <div class="layui-input-inline" style="width:140px; margin-right: 15px;">
                    <select id="keyword" lay-filter="keyword">
                        <option value="">请选择</option>
                    </select>
                </div>
                </div>
                <div class="layui-form-item" style="margin-bottom: 10px;">
                <label class="layui-form-label" style="width: 100px;">收费套餐：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="paidpackage" lay-filter="paidpackage">
                        <option value="">请选择</option>
                        <option value="0">收费套餐一</option>
                        <option value="1">收费套餐二</option>
                    </select>
                </div>
                </div>
                <div class="layui-form-item" style="margin-bottom: 10px;">
                <label class="layui-form-label" style="width: 100px;">体重/年龄：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="weightage" lay-filter="weightage">
                        <option value="">请选择</option>
                        <option value="0">正常</option>
                        <option value="1">异常</option>
                    </select>
                </div>
                </div>
                <div class="layui-form-item" style="margin-bottom: 10px;">
                <label class="layui-form-label" style="width: 80px;">身高/年龄：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="heightage" lay-filter="heightage">
                        <option value="">请选择</option>
                        <option value="0">正常</option>
                        <option value="1">异常</option>
                    </select>
                </div>
                </div>
                <div class="layui-form-item" style="margin-bottom: 10px;">
                <label class="layui-form-label" style="width: 80px;">体重/身高：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="weightheight" lay-filter="weightheight">
                        <option value="">请选择</option>
                        <option value="0">正常</option>
                        <option value="1">异常</option>
                    </select>
                </div>
                </div>
            </div>
            <!-- 第二行 -->
            <div class="layui-form-item" style="margin-bottom: 5px;">
                <div class="layui-form-item" style="margin-bottom: 10px;">
                <label class="layui-form-label" style="width: 80px;">营养不良：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="malnutrition" lay-filter="malnutrition">
                        <option value="">请选择</option>
                        <option value="0">低体重</option>
                        <option value="1">消瘦</option>
                        <option value="2">生长迟缓</option>
                    </select>
                </div>
                </div>
                <div class="layui-form-item" style="margin-bottom: 10px;">
                <label class="layui-form-label" style="width: 120px;">超重和肥胖：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="overweightandobesity" lay-filter="overweightandobesity">
                        <option value="">请选择</option>
                        <option value="0">超重</option>
                        <option value="1">肥胖</option>
                    </select>
                </div>
                </div>
                <div class="layui-form-item" style="margin-bottom: 10px;">
                <label class="layui-form-label" style="width: 70px;">贫血：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="anemia" lay-filter="anemia">
                        <option value="">请选择</option>
                        <option value="0">轻度</option>
                        <option value="1">中度</option>
                        <option value="2">重度</option>
                    </select>
                </div>
                </div>
                <div class="layui-form-item" style="margin-bottom: 10px;">
                <label class="layui-form-label" style="width: 100px;">发育评估：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="developmentalassessment" lay-filter="developmentalassessment">
                        <option value="">请选择</option>
                        <option value="0">无异常</option>
                        <option value="1">异常</option>
                        <option value="2">未检查</option>
                    </select>
                </div>
                </div>
                <div class="layui-form-item" style="margin-bottom: 10px;">
                <label class="layui-form-label" style="width: 100px;">体检年龄：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="physicalexaminationage" lay-filter="physicalexaminationage">
                        <option value="">请选择</option>
                        <option value="0">3岁</option>
                        <option value="1">4岁</option>
                        <option value="2">5岁</option>
                        <option value="3">6岁</option>
                    </select>
                </div>
                </div>
                <div class="layui-form-item" style="margin-bottom: 10px;">
                <label class="layui-form-label" style="width: 80px;">胸部：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="chest" lay-filter="chest">
                        <option value="">请选择</option>
                        <option value="0">无异常</option>
                        <option value="1">异常</option>
                        <option value="2">未检查</option>
                    </select>
                </div>
                </div>
                <div class="layui-form-item" style="margin-bottom: 10px;">
                <label class="layui-form-label" style="width: 80px;">腹部：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="abdomen" lay-filter="abdomen">
                        <option value="">请选择</option>
                        <option value="0">无异常</option>
                        <option value="1">异常</option>
                        <option value="2">未检查</option>
                    </select>
                </div>
                </div>
            </div>
            <!-- 第三行 -->
            <div class="layui-form-item" style="margin-bottom: 10px;">
                <div class="layui-form-item" style="margin-bottom: 10px;">
                <label class="layui-form-label" style="width: 80px;">口腔：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="oralcavity" lay-filter="oralcavity">
                        <option value="">请选择</option>
                        <option value="0">无异常</option>
                        <option value="1">异常</option>
                        <option value="2">未检查</option>
                    </select>
                </div>
                </div>
                <div class="layui-form-item" style="margin-bottom: 10px;">
                <label class="layui-form-label" style="width: 80px;">耳：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="ear" lay-filter="ear">
                        <option value="">请选择</option>
                        <option value="0">无异常</option>
                        <option value="1">异常</option>
                    </select>
                </div>
                </div>
                <div class="layui-form-item" style="margin-bottom: 10px;">
                <label class="layui-form-label" style="width: 70px;">听力：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="listening" lay-filter="listening">
                        <option value="">请选择</option>
                        <option value="0">通过</option>
                        <option value="1">未通过</option>
                        <option value="2">未检查</option>
                    </select>
                </div>
                </div>
                <div class="layui-form-item" style="margin-bottom: 10px;">
                <label class="layui-form-label" style="width: 100px;">眼外观：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="eyeappearance" lay-filter="eyeappearance">
                        <option value="">请选择</option>
                        <option value="0">无异常</option>
                        <option value="1">异常</option>
                        <option value="2">未检查</option>
                    </select>
                </div>
                </div>
                <div class="layui-form-item" style="margin-bottom: 10px;">
                <label class="layui-form-label" style="width: 100px;">视力：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="vision" lay-filter="vision">
                        <option value="">请选择</option>
                        <option value="0">正常</option>
                        <option value="1">低常</option>
                        <option value="2">未检查</option>
                    </select>
                </div>
                </div>
                <div class="layui-form-item" style="margin-bottom: 10px;">
                <label class="layui-form-label" style="width: 110px;">两次随访间患病：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="betweenvisit" lay-filter="betweenvisit">
                        <option value="">请选择</option>
                        <option value="0">无</option>
                        <option value="1">肺炎</option>
                        <option value="2">腹泻</option>
                        <option value="3">其他</option>
                    </select>
                </div>
                </div>
                <button class="layui-btn form-search" style="margin-left: 10px;" id="btnsearch">搜索</button>
            </div>

        </div>
    </div>
    <div id="content" style="width: 100%;height: 694px;">
        <div class="marmain-cen">
            <div class="content-medical" id="divtable">
                <table id="laytable" lay-filter="laytable" ></table>
            </div>
        </div>
    </div>
</div>

<script data-main="../../js/operation/w_AIYearHealthDataCompare_physicalexaminationresults" src='../../sys/require.min.js'></script>
</body>
</html> 