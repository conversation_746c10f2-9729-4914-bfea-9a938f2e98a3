<!DOCTYPE html>
<html>
<head>
<title>添加追访</title>
<meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
<link rel="stylesheet" href="../css/reset.css">
<link rel="stylesheet" href="../css/icon.css">
<link rel="stylesheet" href="../layui-btkj/css/layui.css">
<link rel="stylesheet" href="../css/commonstyle-layui-btkj.css">
<link rel="stylesheet" href="../css/medical.css">
<script type="text/javascript" src="../layui-btkj/layui.js"></script>
<style>
.view p {
	color: #34495E;
	font-size: 14px;
}
.layui-form-label {
	text-align: left;
	width: 80px;
	color: #778CA2;
}
	.datemain input, .datemain textarea {
  border: 1px solid #CBD6E1;
  color: #34495E;
}
</style>
</head>
<body>
<section>
	<div style="margin: 20px 30px;">
		<div class="form-list layui-form">
			<div class="layui-form-item " style="margin: 0;">
				<label class="layui-form-label" style="float: left;">营养不良：</label>
				<div class="layui-input-block" style="min-height: 30px;margin-left:0px;width:250px;float: left;">
					<select name="city" lay-verify="required">
						<option value="请选择">请选择</option>
					</select>
				</div>
				<label class=" layui-form-label" style="float: left;margin-left:70px;">肥 胖：</label>
				<div class="layui-input-block" style="min-height: 30px;margin-left:0px;width:250px;float: left;">
					<select name="city" lay-verify="required">
						<option value="请选择">请选择</option>
					</select>
				</div>
			</div>
			<div class="layui-form-item " style="margin: 10px 0 0 0;">
				<label class="layui-form-label" style="float: left;">贫 血：</label>
				<div class="layui-input-block" style="min-height: 30px;margin-left:0px;width:250px;float: left;">
					<select name="city" lay-verify="required">
						<option value="请选择">请选择</option>
					</select>
				</div>
				<label class=" layui-form-label" style="float: left;margin-left:70px;">视 力：</label>
				<div class="layui-input-block" style="min-height: 30px;margin-left:0px;width:250px;float: left;">
					<select name="city" lay-verify="required">
						<option value="请选择">请选择</option>
					</select>
				</div>
			</div>
			<div class="layui-form-item " style="margin: 10px 0 0 0;">
				<label class="layui-form-label" style="float: left;">听 力：</label>
				<div class="layui-input-block" style="min-height: 30px;margin-left:0px;width:250px;float: left;">
					<select name="city" lay-verify="required">
						<option value="请选择">请选择</option>
					</select>
				</div>
			</div>
				<div class="layui-form-item datemain" style="margin: 10px 0 0 0;">
				<label class="layui-form-label" style="float: left;">备 注：</label>
				<div class="layui-input-block" style="min-height: 30px;margin-left:0px;float: left;width:645px;">
				
					<textarea id="physical" rows="3" cols="20" style="width: 100%;height: 150px"></textarea>
			
				</div>
			</div>
		</div>
	</div>
	<div style="padding: 10px 0;text-align: center;background: #F8F8F8;position: fixed;bottom: 0;width: 100%;">
		<div style="margin-right: 15px;"> <a class="layui-btn gray-bg">取消</a> <a class="layui-btn">保存</a> </div>
	</div>
</section>
<script>
		//Demo
		layui.use('form', function(){
		  var form = layui.form;
		  
		  //监听提交
		  form.on('submit(formDemo)', function(data){
		    layer.msg(JSON.stringify(data.field));
		    return false;
		  });
		});
		</script> 
<script>
		layui.use(['form',  'laydate'], function(){			
		});
	</script>
</body>
</html>