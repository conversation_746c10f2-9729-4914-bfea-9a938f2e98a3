<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>排班统计</title>
    <link rel="stylesheet" type="text/css" href="../../css/reset.css"/>
    <link rel="stylesheet" type="text/css" href="../../layui-btkj/css/layui.css"/>
    <link rel="stylesheet" type="text/css" href="../../css/icon.css"/>
    <link rel="stylesheet" href="../../css/medical.css"/>
    <link rel="stylesheet" type="text/css" href="../../css/commonstyle-layui-btkj.css"/>
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <style type="text/css">
        html, body {
            background: #EAEFF3;
        }

        .layui-form-label {
            line-height: 28px;
        }
    </style>
    <script src="../../sys/html5shiv.min.js"></script>

</head>
<body>
<div class="marmain">
    <div class="content-medical">
        <div class="layui-form layui-comselect" style="position: relative; ">
            <div>
                <label class="layui-form-label">科室：</label>
                <div style="float: left;width:150px;">
                    <select id="dept_pid" lay-filter="dept_pid" name="parent"></select>
                </div>
            </div>
            <div>
                <label class="layui-form-label">门诊：</label>
                <div style="float: left;width:150px;">
                    <select id="dept_id" lay-filter="dept_id" name="mz"></select>
                </div>
            </div>
            <div>
                <label class="layui-form-label">医生：</label>
                <div style="float: left;width:150px;">
                    <select id="doctor_id" lay-filter="doctor_id" name="child"></select>
                </div>
            </div>
            <span class="layui-form-label" style="width: 70px;">开始时间：</span>
            <div class="layui-input-inline" style="height: 37px;">
                <input id="txtstratdate" name="txtstratdate" type="text" placeholder="开始时间" readonly class="layui-input"
                       style="width: 130px;"/>
                <img src="../../images/newicon/ico_calendar.png"
                     style="cursor: pointer;position: relative;top: -29px;width: 20px;left: 103px;">
            </div>
            -
            <div class="layui-input-inline" style="height: 37px;">
                <input id="txtenddate" name="txtenddate" type="text" placeholder="结束时间" readonly class="layui-input"
                       style="width: 130px;"/>
                <img src="../../images/newicon/ico_calendar.png"
                     style="cursor: pointer;position: relative;top: -29px;width: 20px;left: 103px;">
            </div>
            <button class="layui-btn blockcol  mgl-20" id="btnsearch" style="margin-left: 10px;vertical-align: top;">
                查询
            </button>
            <!--<button class="layui-btn blockcol  mgl-20" id="refresh">刷新</button>-->
        </div>
    </div>
    <div id="content" style="width: 100%;">
        <div class="marmain-cen">
            <div class="content-medical">
                <div class="tbmargin">
                    <table id="tabelList" lay-filter="tabelList"><!-- 这就是表格 -->
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<script data-main="../../js/workingscheduledetail/workingscheduledetail" src='../../sys/require.min.js'></script>
</body>
</html>
