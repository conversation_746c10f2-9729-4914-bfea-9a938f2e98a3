﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>家长预约情况</title>
    <link rel="stylesheet" type="text/css" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" type="text/css" href="../../css/icon.css"/>
    <link rel="stylesheet" type="text/css" href="../../styles/tbstyles.css"/>
    <link href="../../plugin/flexigrid/css/tbflexigrid.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css"/>
    <style type="text/css">
        html,
        body {
            background: #EAEFF3;
            overflow: hidden;
        }

        .layui-form-item {
            display: inline-block;
            vertical-align: top;
        }

        .layui-form-label {
            line-height: 28px;
            width: 110px;
        }

        .layui-input-inline {
            width: 180px;
        }
    </style>
</head>
<body>
<div class="marmain">
    <div class="content-medical">
        <div class="search-container layui-form layui-comselect">
            <!-- 日期范围 -->
            <div class="search-col date-range-container">
                <div class="layui-form-item">
                    <label class="layui-form-label">预约时间：</label>
                    <div class="layui-input-inline">
                        <input id="startDate" type="text" autocomplete="off" placeholder="开始日期" class="layui-input">
                    </div>
                    <div class="layui-form-mid">至</div>
                    <div class="layui-input-inline">
                        <input id="endDate" type="text" autocomplete="off" placeholder="结束日期" class="layui-input">
                    </div>
                </div>

                <!-- 预约挂号 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">预约挂号：</label>
                    <div class="layui-input-inline">
                        <select id="appointmentSuccess" lay-filter="isCancel">
                            <option value="">请选择</option>
                            <option value="1">预约成功</option>
                            <option value="0">取消预约</option>
                        </select>
                    </div>
                </div>

                <!-- 幼儿园 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">幼儿园：</label>
                    <div class="layui-input-inline">
                        <select id="gardenName" lay-filter="gardenName">
                            <option value="">请选择</option>
                            <option value="1">阳光幼儿园</option>
                            <option value="2">彩虹幼儿园</option>
                            <option value="3">希望幼儿园</option>
                        </select>
                    </div>
                </div>

                <!-- 幼儿性别 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">幼儿性别：</label>
                    <div class="layui-input-inline">
                        <select id="childGender" lay-filter="childGender">
                            <option value="">请选择</option>
                            <option value="1">男</option>
                            <option value="2">女</option>
                        </select>
                    </div>
                </div><br>

                <!-- 关键字 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">关键字：</label>
                    <div class="layui-input-inline">
                        <input id="keyword" type="text" autocomplete="off" placeholder="请输入幼儿姓名"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <button class="layui-btn layui-btn-normal" id="btnsearch">查询</button>
                </div>
            </div>
        </div>
        <div id="content" style="width: 100%;">
            <div class="marmain-cen">
                <div class="content-medical" id="divtable">
                    <table id="laytable" lay-filter="laytable"></table>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript" data-main="../../js/operation/w_parentAppointment_child.js"
            src="../../sys/require.min.js"></script>
</div>
</body>
</html>