﻿/*
日期： 
作者：
內容摘要:贫血管理
*/
var fpsetting = {
    arrcard: [], //学生的学号  用于上一个下一个
    curstuname: "",
    strsex: '',
    strbirthday: '',
    curstuno: ''//当前学生的学号
    , objtjinfo: {}
};

require.config({
    paths: {
        sys: "../../sys/system",
        jquery: "../../sys/jquery",
        editselect: "../../plugin/editselect/js/editselect",
        validator: "../../plugin/validator/js/validator",
        wdatepicker: "../../plugin/wdatepicker/wdatepicker"
    }, shim: {},
    waitSeconds: 0
});
var parentobj = null;
require(["jquery", "sys"], function () {
    require(["editselect", "validator", "wdatepicker"], function () {
        parentobj = jQuery.getparent().$("#win_" + Arg('mid')).children('iframe')[0].contentWindow;
        fpsetting.stuno = Arg("stuno");
        initclassdata(fpsetting.stuno);
        $('#chkvest').click(function () {
            check(1);
        });
        $("#btnsave").click(function (event, callback) {
            btnsave(fpsetting.stuno, callback);
        });
        $('#txtendtime').prop('disabled', true);
        $("#txtmanagetime").click(function () {
            WdatePicker({skin: 'whyGreen'});
        });
        $("#txtzdtime").click(function () {
            WdatePicker({skin: 'whyGreen'});
        });
        $("#txtendtime").click(function () {
            WdatePicker({skin: 'whyGreen'});
        });
    });
});

function check(type, t) {
    if (type == 1) {
        if ($("#chkvest")[0].checked) {
            $('#txtendtime').attr('require', true);
            $('#txtendtime').prop('disabled', false);
            $('#lbvest').html('<input id="txtvest0" name="radvest" type="radio" value="0" checked="checked"/><label for="txtvest0">痊愈</label>\
	                <input id="txtvest1" name="radvest" type="radio" value="1"/><label for="txtvest1">好转</label>\
	                <input id="txtvest2" name="radvest" type="radio" value="2"/><label for="txtvest2">未愈</label>\
	                <input id="txtvest3" name="radvest" type="radio" value="3"/><label for="txtvest3">失访</label>\
	                <input id="txtvest3" name="radvest" type="radio" value="4"/><label for="txtvest4">离园</label>\
	        		').show();
        } else {
            $('#txtendtime').attr('require', false);
            $('#txtendtime').prop('disabled', true);
            $('#txtendtime').val("");
            $('#lbvest').hide().html('');
        }
    } else {
        var v = $(t).val();
        if (isNaN(v)) {
            jQuery.getparent().layer.msg("请输入数字！");
            $(t).val("");
            return;
        }
        if (v < 0) {
            jQuery.getparent().layer.msg("请输入正确的数字！");
            $(t).val("");
            return;
        }
    }
};

/*
功能：加载数据
*/
function initclassdata(stuno) {
    $("#divtbody").html("");
    var curdate = jQuery.getparent().objdata.strtoday;
    jQuery.getparent().layer.load();
    $.sm(function (re, err) {
        if (err) {
            jQuery.getparent().layer.msg(err, {icon: 5});
            jQuery.getparent().layer.closeAll('loading');
        } else if (re && re[0]) {
            var objstu = re[0];
            //t.stuno,s.stuname,sex,c.claname,to_char(s.birthday,'YYYY-MM-DD') as birthday,to_char(t.managetime,'YYYY-MM-DD') as managetime,to_char(t.endtime,'YYYY-MM-DD') as endtime,idend,vest,zdname,to_char(t.zdtime,'YYYY-MM-DD') as zdtime,famhistory,remark
            fpsetting.curstuno = objstu.stuno;
            fpsetting.strbirthday = objstu.birthday;
            fpsetting.strsex = objstu.sex;
            fpsetting.curstuname = objstu.stuname;
            $("#txtname").html(objstu.stuname || "");
            $("#txtsex").html(objstu.sex || "");
            $("#txtbirthday").html(objstu.birthday || "");
            $("#txtmanagetime").val(objstu.managetime || "");//开始管理日期
            $("#txtzdname").val(objstu.zdname || "");
            $("#txtzdtime").val(objstu.zdtime || "");
            $("#txtfamhistory").val(objstu.famhistory || "");
            $("#txtremark").val(objstu.remark || "");

            $("#chkvest").val(objstu.idend);//
            if (objstu.idend == '1') {//是否结案1是0否
                $("#lbvest").show();
                $('#chkvest').prop('checked', true);
                $('input[name="radvest"][value="' + (objstu.vest || 0) + '"]').attr("checked", true);//转归
                $('#txtendtime').attr('require', true);
                $('#txtendtime').prop('disabled', false);
                $('#txtendtime').val(objstu.endtime || '');//结案日期
            } else {
                $('#chkvest').prop('checked', false);
                $('#txtendtime').attr('require', false);
                $('#txtendtime').prop('disabled', true);
                $('#txtendtime').val("");
                $("#lbvest").hide();
            }
            init(stuno);
        } else {
            jQuery.getparent().layer.msg("未查到有关数据！");
            jQuery.getparent().layer.closeAll('loading');
        }
    }, ["psychology.studetail", stuno, curdate]);
};

/*
功能：读取管理卡
*/
function init(stuno) {
    var strwhere = {};
    $.sm(function (re, err) {
        if (err) {
            jQuery.getparent().layer.msg(err, {icon: 5});
            jQuery.getparent().layer.closeAll('loading');
        } else {
            var obj = {};
            for (var i = 0; i < re.length; i++) {
                obj = re[i];
                fpsetting.objtjinfo[obj.id] = obj;
                setHtml(obj);
            }
            setHtml();
            jQuery.getparent().layer.closeAll('loading');
        }
    }, ["psychologycard.manage", stuno]);/*, strwhere*/
};

/*
设置肥胖明细记录行
*/
function setHtml(obj) {
    if (!obj) {
        obj = {};
    }
    //age,method,result,suggestion,steps,assess,write
    var len = $("#divtbody")[0].rows.length;
    var fno = len ? $($("#divtbody")[0].rows[len - 1]).attr('fno') : 0;
    fno = fno ? Math.floor(fno) + 1 : 1;
    var arrstr = ['<tr fno=' + fno + ' ftime="' + (obj.managetime ? obj.managetime : '') + '"><td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;padding:0 5px;">'];
    arrstr.push(obj.id ? '<input type="button" onclick="delrowevent(this,' + fno + ',' + obj.id + ');" id="btdelete' + fno + '" value="删除"  class="btn"  style=" margin-bottom:2px"/>' : '&nbsp;');
    arrstr.push('</td><td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;padding:0 5px;"><input type="text" id="txtdate' + fno + '" style="width: 95px;" class="Wdate" value="' + (obj.managetime ? obj.managetime : '') + '" onclick="initdate(' + fno + ');" onblur="changeDate(' + fno + ');" /></td>');//检查日期
    arrstr.push('<td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;padding:0 5px;"><label id="txtage' + fno + '" age="' + (obj.managetime ? parentobj.GetAge(fpsetting.strbirthday, obj.managetime) : "") + '">' + (obj.managetime ? parentobj.GetAge(fpsetting.strbirthday, obj.managetime, 'zh') : "") + '</label>&nbsp;</td>');
    //筛查方法
    arrstr.push('<td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">');
    arrstr.push('<textarea id="txtmethod' + fno + '" style="width:95%;" maxlength="10" class="tjzagl_textarea">' + (obj && obj.method ? obj.method : '') + '</textarea><span class="FancyInput__bar___1P3wW" ></span></td>');
    //筛查结果
    arrstr.push('<td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">');
    arrstr.push('<textarea id="txtresult' + fno + '" style="width:95%;" maxlength="100" class="tjzagl_textarea">' + (obj && obj.result ? obj.result : '') + '</textarea><span class="FancyInput__bar___1P3wW" ></span></td>');
    //医疗机构诊疗意见
    arrstr.push('<td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">');
    arrstr.push('<textarea id="txtsuggestion' + fno + '" style="width:95%;" maxlength="100" class="tjzagl_textarea">' + (obj && obj.suggestion ? obj.suggestion : '') + '</textarea><span class="FancyInput__bar___1P3wW" ></span></td>');
    //管理措施
    arrstr.push('<td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">');
    arrstr.push('<textarea id="txtsteps' + fno + '" style="width:95%;" maxlength="100" class="tjzagl_textarea">' + (obj && obj.steps ? obj.steps : '') + '</textarea><span class="FancyInput__bar___1P3wW" ></span></td>');
    //管理成效
    arrstr.push('<td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">');
    arrstr.push('<textarea id="txtassess' + fno + '" style="width:95%;" maxlength="100" class="tjzagl_textarea">' + (obj && obj.assess ? obj.assess : '') + '</textarea><span class="FancyInput__bar___1P3wW" ></span></td>');
    //签名
    arrstr.push('<td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">');
    arrstr.push('<input id="txtwrite' + fno + '" value="' + (obj && obj.write ? obj.write : '') + '" type="text" style="width:90%;" maxlength="20" class="tjzagl_input" /><span class="FancyInput__bar___1P3wW" ></span></td>');
    arrstr.push('</tr>');
    $("#divtbody").append(arrstr.join(""));
};

/*
功能：删除功能
*/
function delrowevent(obj, num, id) {
    jQuery.getparent().layer.confirm('请确认是否真的进行删除操作？', function (r) {
        $.sm(function (re, err) {
            if (err) {
                jQuery.getparent().layer.msg(err, {icon: 5});
            } else {
                $(obj).closest('tr').remove();
            }
            jQuery.getparent().layer.close(r);
        }, ["tb_psychtestmanage.delbyid", id]);
    });
};

/*
功能：初始化日期
*/
function initdate(num) {
    WdatePicker({
        skin: 'whyGreen', minDate: Arg("sdate"), maxDate: Arg("edate"), onpicking: function (dp) {
            var age = parentobj.GetAge(fpsetting.strbirthday, dp.cal.getNewDateStr());
            $("#txtage" + num).html(parentobj.getZhAgeByAge(age)).attr("age", age);
        }
    });
}

function changeDate(num) {
    var strdate = $("#txtdate" + num).val()
    var age = parentobj.GetAge(fpsetting.strbirthday, strdate);
    $("#txtage" + num).html(parentobj.getZhAgeByAge(age)).attr("age", age);
}

/*
功能：保存  
*/
function btnsave(stuno, cb) {
    if (!Validator.Validate($('#tabcard0').val(), 2)) {
        return;
    }
    jQuery.getparent().layer.load();
    var arrpm = [];
    var len = $("#divtbody")[0].rows.length;
    var fno = $($("#divtbody")[0].rows[len - 2]).attr("fno");
    //t.stuno,s.stuname,sex,c.claname,to_char(s.birthday,'YYYY-MM-DD') as birthday,to_char(t.managetime,'YYYY-MM-DD') as managetime,to_char(t.endtime,'YYYY-MM-DD') as endtime,idend,vest,zdname,to_char(t.zdtime,'YYYY-MM-DD') as zdtime,famhistory,remark
    var endtime = $("#txtendtime").val();
    var zdname = $.trim($("#txtzdname").val()),
        famhistory = $.trim($("#txtfamhistory").val()),
        remark = $.trim($("#txtremark").val()),//备注
        chkvest = $("#chkvest")[0].checked ? 1 : 0,//是否结案
        managetime = {datevalue: [$("#txtmanagetime").val() || ""]},//开始管理日期
        objendtime = {datevalue: [endtime || ""]},//结案日期
        zdtime = {datevalue: [$("#txtzdtime").val() || ""]},//诊断日期
        radvest = 'null';//转归
    if (chkvest == 1) {
        radvest = $('input[name="radvest"]:checked').val();//转归
        if (!endtime) {
            jQuery.getparent().layer.msg('请输入结案日期！');
            jQuery.getparent().layer.closeAll('loading');
            return;
        }
    }
    var strtip = '', arrdate = [];
    for (var i = 0; i < len; i++) {
        var num = $($("#divtbody")[0].rows[i]).attr("fno");
        var fno1 = $($("#divtbody")[0].rows[i]).attr("ftime");//检查时间
        var date = $("#txtdate" + num).val();//检查日期
        var txtage = $("#txtage" + num).attr("age");//年龄
        // if (!date) {//没有日期
        //     strtip += '第' + (i + 1) + '行信息不完整，数据将不会被处理</br>';
        // }
        if (date && $.inArray(date, arrdate) >= 0) {
            jQuery.getparent().layer.msg("第" + (i + 1) + "行日期" + date + "与其它行日期重复，请检查！", {area: '400px'});
            jQuery.getparent().layer.closeAll('loading');
            return;
        } else if (date) {
            arrdate.push(date);
        }
        if (date != "") {
            var arr = ["psychologycard.addmanage", fpsetting.stuno, date, txtage];
            //method,result,suggestion,steps,assess,write
            arr.push($("#txtmethod" + num).val());
            arr.push($("#txtresult" + num).val());
            arr.push($("#txtsuggestion" + num).val());
            arr.push($("#txtsteps" + num).val());
            arr.push($("#txtassess" + num).val());
            arr.push($("#txtwrite" + num).val());
            arrpm.push(arr);
        }
    }
    arrpm.push(["psychologycard.addstu", fpsetting.stuno, $.msgwhere(managetime), $.msgwhere(objendtime), chkvest, radvest, zdname, $.msgwhere(zdtime), famhistory, remark]);
    if (chkvest == 1) {//结案
        arrpm.push(["psychologycard.addclosecaserecord", fpsetting.stuno, fpsetting.curstuname, fpsetting.strsex, fpsetting.stuno, endtime, "", "", radvest, "", 4, jQuery.getparent().objdata.my.organid || 'null', fpsetting.yeyid || 'null']);
    }
    if (strtip) {
        jQuery.getparent().layer.confirm(strtip + '请确定是否真的进行保存操作?', function (r) {
            btsavesm(arrpm, stuno, r, cb);
        }, function (r) {
            jQuery.getparent().layer.closeAll('loading');
        });
    } else {
        btsavesm(arrpm, stuno, null, cb);
    }
};

function btsavesm(arrpm, stuno, r, cb) {
    $.sm(function (re, err) {
        if (err) {
            jQuery.getparent().layer.msg(err, {icon: 5});
            jQuery.getparent().layer.closeAll('loading');
        } else {
            if (stuno) {
                $('#txtendtime').attr('require', true);
                $('#txtendtime').prop('disabled', false);
            }
            jQuery.getparent().layer.closeAll('loading');
            jQuery.getparent().layer.msg('保存成功！');
            cb && cb();
            if (r)
                jQuery.getparent().layer.close(r);
        }
    }, arrpm, null, null, null, null, 1);
}