﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>个人信息</title>
    <link rel="stylesheet" href="../css/reset.css"/>
    <link rel="stylesheet" href="../css/style.css"/>
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <link rel="stylesheet" href="../plugin/layui-btkj/css/layui.css">
    <!--[if lt IE 9]>
    <script src='../sys/html5shiv.min.js'></script>
    <![endif]-->
</head>
<body>
<section class="pd10-20 bg4 ">
    <section class="personalInfo font14 cl1">
        <h2 class="noticeTitle bg1"><span class="lanflag" id="myprofile">修改个人信息</span></h2>
        <form class="cmxform clearfix pd20 bg6 layui-form" id="form" method="get" action="">
            <section id="divicon" class="fl personalImg">
                <img src="../images/default.png" alt="" class="otx-head"/>
                <p><span class="lanflag" id="lansize">尺寸</span>：96*96</p>
                <p><span class="lanflag" id="lanzhichi">支持</span>jpg、png、gif、bmp</p>
                <a class="cl2" id="btndelimg"><span class="lanflag" id="landelete">删除</span></a>
                <span id="btnup">
						<a class="lanflag" id="lanlocalup"><span class="lanflag" id="lanshangchuan">本地上传</span></a>
					</span>
            </section>
            <section class="fl">
                <div class="layui-form-item">
                    <label class="layui-form-label" id="lanrealname"><em>*</em>真实姓名：</label>
                    <div class="layui-input-inline" style="width:220px">
                        <input type="text" maxlength="30" id="txttruename" maxlength="30" name="truename" required
                               lay-verify="required" placeholder="真实姓名" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" id="lanusername"><em>*</em>用户名：</label>
                    <div class="layui-input-inline">
                        <input id='txtuname' type="text" maxlength="30" id="txttruename" maxlength="30" name="uname"
                               required lay-verify="required" placeholder="用户名" autocomplete="off" disabled="disabled"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" id="landepartment"><em>*</em>单位：</label>
                    <div class="layui-input-inline">
                        <input id='departSel' type="text" readonly maxlength="100" id="departSel" maxlength="30"
                               name="depart" required lay-verify="required" placeholder="单位" autocomplete="off"
                               disabled="disabled" class="layui-input">
                    </div>
                    <input type="hidden" id="txtdepart_id"/>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" id="lanmobile"><em>*</em>手机：</label>
                    <!--   <div class="layui-input-inline" style="width: 80px;">
                         <input type="text"  maxlength="2" id="txtprefix" style="width: 80px;" placeholder="国家代码" name="prefix" class="layui-input"/>
                     </div>
                     -->
                    <div class="layui-input-inline">
                        <input type="text" maxlength="13" lay-verify="required" id="txtmobile" placeholder="手机"
                               name="mobile" class="layui-input"/>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" id="lanemail"><em>*</em>邮箱：</label>
                    <div class="layui-input-inline">
                        <input type="text" id="txtemail" maxlength="30" name="email" required lay-verify="required"
                               placeholder="邮箱" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div>
                    <label for=""></label><a id="btnsave" class="btn-black" id="btnsave"><span class="lanflag"
                                                                                               id="lanok">保存</span></a><input
                        style="display:none;" type="submit" id="submit" value="保存"/>
                </div>
            </section>
        </form>
    </section>
</section>
<script type="text/javascript" src="../layui-btkj/layui.js"></script>
<script type="text/javascript" src="../sys/jquery.js"></script>
<script type="text/javascript" src="../sys/arg.js"></script>
<script type="text/javascript" src="../js/w_mine.js"></script>
</body>
</html>
