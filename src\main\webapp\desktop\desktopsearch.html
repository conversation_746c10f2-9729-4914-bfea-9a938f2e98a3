<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>页面导航</title>
    <link rel="stylesheet" href="../css/icon.css">
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../desktop.css">
    <style>
        html {
            background-color: #ffffff;
        }
        
        .lm-style-top {
            position: relative;
            height: 30px;
            line-height: 30px;
            font-size: 14px;
        }
        
        .lm-style-rt {
            line-height: 30px;
        }
    </style>
</head>
<body>
<section style="margin: 0 auto; min-height:390px; background:#ffffff;overflow: hidden">
    <div class="set-tab">
        <div class="def-search" style="float: left;height: 30px;width: 75%; min-width:100px;  margin:6px 0 0 0; ">
            <i class="iconfont icon_search" style="line-height:30px; height: 30px;"></i>
            <label style="line-height: 26px; height: 26px;"><input type="text" placeholder="请输入搜索关键字" style="line-height: 30px; height: 30px;" id="txtsearch"></label>
        </div>
        <button class="layui-btn" style="background: #3aa6e4;line-height: 30px; height: 30px; float: left;margin:6px 0 0 10px;" id="btnsearch">搜索</button>
    </div>
    <!--快速导航-->
    <!--<p class="menuLink"><label class="hasson">开始菜单</label> > <label class="hasson">保教工作</label> > <label>保教首页</label></p>-->
    <p class="menuLink"><label class="hasson" data-type="start">开始菜单</label></p>
    <div class="nav-mainmenu" id="divmainmenu">
        <ul id="ulmenutype">
            <li data-type="erbao">
                <img src="../images/desktop/top/icon_baojiao.png">
                <p>儿保</p>
            </li>
            <li data-type="tijian">
                <img src="../images/desktop/top/icon_houqin.png">
                <p>体检</p>
            </li>
            <li data-type="richang">
                <img src="../images/desktop/top/icon_richang.png">
                <p>日常</p>
            </li>
        </ul>
    </div>
    <!--快速导航子菜单-->
    <div class="nav-menu" id="divsonmenu" style="display: none">
        <ul>
            <!--<li><img src="../images/desktop/backgroud/icon_zhaoshengguanli.png" alt=""/>招生管理</li>-->
            <!--<li><img src="../images/desktop/backgroud/icon_zhaoshengguanli.png" alt=""/>招生管理</li>-->
            <!--<li><img src="../images/desktop/backgroud/icon_zhaoshengguanli.png" alt=""/>招生管理</li>-->
            <!--<li><img src="../images/desktop/backgroud/icon_zhaoshengguanli.png" alt=""/>招生管理</li>-->
            <!--<li><img src="../images/desktop/backgroud/icon_zhaoshengguanli.png" alt=""/>招生管理</li>-->
            <!--<li><img src="../images/desktop/backgroud/icon_zhinengfenban.png" alt=""/>智能分班</li>-->
            <!--<li><img src="../images/desktop/backgroud/icon_zaojiaoban.png" alt=""/>早教班管理</li>-->
        </ul>
    </div>
    <!--搜索结果-->
    <div class="nav-menu" id="divsearchresult" style="display: none">
        <ul>
            <!--<li><img src="../images/desktop/backgroud/icon_zhaoshengguanli.png" alt=""/>招生管理</li>-->
            <!--<li><img src="../images/desktop/backgroud/icon_zhinengfenban.png" alt=""/>智能分班</li>-->
            <!--<li><img src="../images/desktop/backgroud/icon_zaojiaoban.png" alt=""/>早教班管理</li>-->
        </ul>
    </div>
</section>
<div style="text-align: center;padding: 20px 0;margin: 0 20px;">
    <button class="layui-btn layui-btn-primary" style="line-height: 30px;height: 30px;" id="btnclose">关闭</button>
    <button class="layui-btn layui-btn-sm" style="line-height: 30px;height: 30px;display: none" id="btnliuchengdaohang">流程导航</button>
</div>
<script data-main="desktopsearch" src="../sys/require.min.js"></script>
</body>
</html>
