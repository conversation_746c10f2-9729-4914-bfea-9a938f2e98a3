<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>系统设置</title>
    <link rel="stylesheet" href="../css/reset.css"/>
    <link rel="stylesheet" href="../css/blue.css"/>
    <link rel="stylesheet" href="../css/style.css"/>
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../css/icon.css"/>
    <link rel="stylesheet" href="../styles/xbcomstyle.css">
    <style>
        body {
            background: #f7f7f7
        }

        .hide {
            display: none;
        }

        .formList label:first-child {
            width: 70px;
            text-align: left;
            padding: 9px 5px;
        }

        .layui-form-label {
            text-align: left;
            padding: 9px 5px;
            width: 98px;
        }

        .layui-input {
            width: 154px;
        }

        .layui-input-block {
            margin-left: 86px
        }

        textarea {
            border: 1px solid #e6e6e6
        }
        ul li{cursor:pointer;}
        #maplocate{cursor:pointer;}
        .hntimeselect{
           width:80px;
           padding-left:0px;
           text-align:center;
           border:1px solid #D2D2D2;
           color:black;
        }
        .connectline{
		   width: 50px;
		   border-top: 1px solid #D2D2D2;
		   margin-top: 17px;
		   margin-right: 5px;        	
        }
        .con{
            background: url(../images/tishi_bg.png)no-repeat;background-size: 296px 79px;
            color: red;
        }
        .con2{
            background: url(../images/tishi_bg.png)no-repeat;background-size: 296px 105px;
            color: red;
        }
        .layui-comthis.tjpad::after{width: 60px;height: 38px;}
        .legend_bjcyadd{position: relative;height: 34px; line-height: 34px;border-bottom: 1px solid #EAEAEA;}
        .incr-chat .div_field .layui-form-label {line-height: 80px;padding-bottom: 0;}
        .div_field .layui-input, .div_field .layui-select, .div_field .layui-textarea {margin-top: 20px;}
        .incr-chat .layui-form-label {width: 208px;}
        .div_field .layui-input-block {margin-left: 254px; line-height: 70px;}
        .div_field{border-bottom: none;}
        .incr-chat .div_field .layui-form-item { border-bottom: 1px solid #EAEAEA;}
		.div-organ .layui-form-label{text-align: left;width: 143px;}
		.formList .btn-black{line-height: 38px;}
	
    </style>
	
</head>
<body>
<section class="pd10-20">
    <div class="div-sysname" style="display:none;">
        <h3 class="noticeTitle bg1" style="margin-top: 10px;"><span class="lanflag">系统设置</span></h3>
        <div class="bg6 pd20 formList">
            <form class="layui-form">
                <div class="layui-form-item">
                    <label class="layui-form-label" style="line-height: 17px;">系统名称：</label>
                    <div class="layui-input-block" style="margin-left: 76px;">
                        <input id="sysname" type="text" name="sysname" required lay-verify="required" placeholder="请输入名称"
                               autocomplete="off" class="layui-input" style="width: 240px;display: inline">
                        <a id="btn_apply1" lay-filter="systemname" class="btn-black" style="margin-left: 7px">
                            <span class="lanflag">应用</span></a>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="divjoinset legend_bjcyadd" style="margin-top: 15px;display: none;">
        <label class="layui-comthis tjpad">加入设置</label>
    </div>
    <div class="divjoinset incr-chat" style="margin:15px 0;display: none;">
        <form class="layui-form">
            <section class="bg6">
                <div class="layui-form div_field">
                    <div>
                        <!--<div class="layui-form-item">-->
                        <!--<label class="layui-form-label">单位名称：</label>-->
                        <!--<div class="layui-input-inline">-->
                        <!--<input id="departname" type="text" name="departname" required lay-verify="required"-->
                        <!--placeholder="请输入单位名称"-->
                        <!--autocomplete="off" class="layui-input" style="width: 240px;display: inline">-->
                        <!--</div>-->
                        <!--</div>-->
                        <div class="layui-form-item" style="position: relative;">
                            <label class="layui-form-label">是否开启自动审核：</label>
                            <div class="layui-input-block">
                                <input type="radio" name="autoreview" value="1" title="是" lay-filter="approval">
                                <input type="radio" name="autoreview" value="0" title="否" checked="" lay-filter="notApproval">
                                <span style="margin-left: 50px;"></span>
                                <div id="waringA" style="display: inline-block; padding-top: 4px;vertical-align:top; color: #fb5c64;">
                                    不开启自动审核时，则需要对园所端提交的加入申请进行手动审核哦！
                                </div>
                                <div id="waringB" style="display: inline-block;color: #fb5c64;padding-top:4px;vertical-align:top;
    ">
                                    开启自动审核时，若园所输入正确的加入码即可自动关联该政府端，无人值守。
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item" id="joinNum" style="display: none">
                            <label class="layui-form-label">输入加入码：</label>
                            <div class="layui-input-inline">
                                <input style="width: 240px" id="joincode" type="text" name="joincode"
                                       placeholder=""
                                       autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item" id="isJoin" style="display: none">
                            <label class="layui-form-label">申请关联时是否必须输加入码：</label>
                            <div class="layui-input-block">
                                <input type="radio" name="ismustcode" value="1" title="是" lay-filter="join">
                                <input type="radio" name="ismustcode" value="0" title="否" checked="" lay-filter="notjoin">
                                <span style="margin-left: 50px;"></span>
                                <div id="waringC" style="display: inline-block;padding-top: 4px;vertical-align:top;color: #fb5c64;
    ">
                                    若只允许园所端输入正确的加入码才可关联该政府端时，则选择“是”，若除以上情况之外也支持不输入加入码而进行手动审核时，则选择“否”。
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </section>
            <div>
                <label></label><a lay-submit lay-filter="addSetting" class="layui-btn bluebtn" style="margin-left: 240px; margin-top: 10px;">
                <span class="lanflag  " id="lansave">应用</span></a>
            </div>
        </form>
    </div>
    <div class="div-organ" style="display:none;">
        <h3 class="noticeTitle bg1" style="margin-top: 10px;"><span class="lanflag">体检业务设置</span></h3>
        <div class="bg6">
        	<div class="pd10-20 layui-form">
                <div style="margin-top:15px">
                    <div class="layui-form-item">
                    	<label class="layui-form-label">上班时间设置：</label>
                    	<p class="layui-input-block" style="margin-left: 0px;display:inline-flex">
                    		<label class="layui-form-label" style="width: 70px;">全天：</label>
                    		<input id="alldaystart" name="alldaystart"  autocomplete="off" class="input-time hntimeselect" type="text" />
                    		<span class="connectline"></span>
                    		<input id="alldayend" name="alldayend" autocomplete="off" class="input-time hntimeselect" type="text" />
                    	</p>
                    </div>
                    <div class="layui-form-item" style="display:inline-flex">
                    	<div class="layui-input-block" style="margin-left: 151px;display:inline-flex">
                    		<label class="layui-form-label" style="width: 70px;">上午：</label>
                    		<input id="morningstart" name="morningstart" autocomplete="off" class="input-time hntimeselect" type="text" readonly style="background-color:#CCCCCC"/>
                    		<span class="connectline"></span>
                    		<input id="morningend" name="morningend" autocomplete="off" class="input-time hntimeselect" type="text" />
                    	</div>
                    	<div class="layui-input-block" style="display:inline-flex">
                    		<label class="layui-form-label" style="width: 70px;">下午：</label>
                    		<input id="afternoonstart" name="afternoonstart"  autocomplete="off" class="input-time hntimeselect" type="text" />
                    		<span class="connectline"></span>
                    		<input id="afternoonend" name="afternoonend" autocomplete="off" class="input-time hntimeselect" type="text" readonly style="background-color:#CCCCCC"/>
                    	</div>
                    </div>
                    <a id="btn_apply0" lay-filter="systemname" class="btn-black" style="margin: 20px auto 0 auto; display: block; width: 30px; border-radius: 5px; line-height: 40px">
                            <span class="lanflag">应用</span></a>
                 </div>
             </div>
        </div>
        <div class="bg6">
            <div class="pd10-20 layui-form">
                <div style="margin-top:15px">
                    <div class="layui-form-item">
                        <label class="layui-form-label" >体检可预约日期设置：</label>
                        <div class="layui-input-block" style="margin-left: 160px;">
                            <div class="date-setting">
                                <ul>
                                    <li>周日</li>
                                    <li>周一</li>
                                    <li>周二</li>
                                    <li>周三</li>
                                    <li>周四</li>
                                    <li>周五</li>
                                    <li>周六</li>
                                </ul>
                                <div class="layui-form" style="clear: both">
                                    法定节假日是否自动休息：
                                    <div class="layui-input-inline" style="float: none">
                                        <input type="radio" name="isautorest" value="1" title="是">
                                        <input type="radio" name="isautorest" value="0" title="否" checked="checked">
                                    </div>
                                </div>
                                <p style="clear: both;line-height: 38px;">需提前：<input type="number" name="advanceday" class="input-time" placeholder="0" oninput="if(value<0)value=0;">个工作日方可预约</p>
                                <p style="clear: both;line-height: 38px;">距体检日期前<input type="number" name="canceldays" class="input-time" placeholder="0" oninput="if(value<0)value=0;">个工作日不可取消预约</p>
                                <p style="clear: both;line-height: 38px;">最多可修改时间<input type="number" name="updatenum" class="input-time" placeholder="0" oninput="if(value<0)value=0;">次</p>
                            </div>
                        </div>
                        <a id="btn_apply2" lay-filter="systemname" class="btn-black" style="margin: 20px auto 0 auto; display: block; width: 30px; border-radius: 5px; line-height: 40px">
                            <span class="lanflag">应用</span></a>
                    </div>
                </div>
            </div>
        </div>
    
        <div class="bg6 pd20 mtop10">
            <div class="layui-form-item">
                <label class="layui-form-label">每天可接待人数：</label>
                <div class="layui-input-block" style="display: inline-flex; margin-left: 0;line-height: 38px;">
                    <input name="daycanjiedainum" lay-verify="daycanjiedainum" autocomplete="off" class="layui-input" type="number" style="display: inline-block" oninput="if(value<0)value=0;"><span style="margin-left: 5px">人</span>
                   	<span style="margin-left:30px">
                   		上午：<input name="morningcanjiedainum" style="padding-right:5px;text-align:center" class="input-time" type="number" oninput="if(value<0)value=0;"></input>人
                   	</span>
                   	<span style="margin-left:30px">
                   		下午：<input name="afternooncanjiedainum"  style="padding-right:5px;text-align:center" class="input-time" type="number" oninput="if(value<0)value=0;"></input>人
                   	</span>
                   	<span id="jiedainumstartdate" style="margin-left: 20px"></span>
                   	<!-- <span style="margin-left:30px">
                    	生效日期：<input id="jiedainumstartdate" style="padding-right:5px;text-align:center;margin-left: 20px" class="input-time" placeholder="生效日期"></input>
                    </span> -->
                    <div id="todayjiedainum"></div>
                </div>
            </div>
            <a id="btn_apply4" lay-filter="btndaycanjiedainum" class="btn-black" style="margin: 20px auto 0 auto; display: block; width: 30px; border-radius: 5px; line-height: 40px"><span class="lanflag">应用</span></a>
        </div>
        
        <div class="bg6 pd20 mtop10">
            <div class="layui-form-item">
                <label class="layui-form-label">体检机构名称：</label>
                <div class="layui-input-block">
                    <input name="companyname" lay-verify="companyname" autocomplete="off" class="layui-input" type="text" maxlength="50" style="width:600px">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">体检机构地址：</label>
                <div class="layui-input-block">
                    <input name="address" lay-verify="address" autocomplete="off" class="layui-input" type="text" maxlength="50" style="width:600px">
                    <span id="maplocate" style="position:absolute; left:680px; top:10px;"><i class="iconfont icon_location" style="float: left"></i><label class="location-txt" style="padding: 0;">点击图标定位</label></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">乘车路线：</label>
                <div class="layui-input-block">
                    <textarea name="busline" style="width: 580px;height: 102px; padding: 10px;"></textarea>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">联系电话：</label>
                <div class="layui-input-block">
                    <input name="phone" lay-verify="phone" autocomplete="off" class="layui-input" type="text">
                </div>
            </div>
            <a id="btn_apply3" lay-filter="systemname" class="btn-black" style="margin: 20px auto 0 auto; display: block; width: 30px; border-radius: 5px; line-height: 40px"><span class="lanflag">应用</span></a>
        </div>
    </div>
</section>
<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../sys/function.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../js/systemsetting.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../plugin/wdatepicker/wdatepicker.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>