﻿<!DOCTYPE html>
<html style="overflow: hidden;">
<head>
    <meta charset="utf-8" />
    <title>健康宣教</title>
    <link rel="stylesheet" href="../../css/reset.css" />
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" type="text/css" href="../../css/icon.css" />
    <link rel="stylesheet" type="text/css" href="../../css/style.css" />
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css" />
    <link rel="stylesheet" href="../../styles/xbcomstyle.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <style>
        body { background: #F4F5F7; }
        #js-saved .js-read-info label { display: none; }
        #js-sent .js-read-info .btnlb, #js-receive .js-read-info .btnlb { display: none; }
        #js-sent .qr-delay-enroll-forward, #js-saved .qr-delay-enroll-forward { display: none; }
        #js-sent .qr-delay-enroll-commitname, #js-saved .qr-delay-enroll-commitname { display: none; }
        #js-receive .qr-delay-enroll-time, #js-saved .qr-delay-enroll-time { display: none; }
        #js-receive .qr-delay-enroll-shenroll, #js-saved .qr-delay-enroll-shenroll { display: none; }
        #js-sent .noti-content h3, #js-saved .noti-content h3 { padding: 0 15px; }
        .layui-form .layui-form-item { margin-bottom: 15px; }
        .layui-input-block { margin-left: 0px; }
        .layui-input-inline { margin-left: 0px; margin-top: 5px; }
        .layui-form-item .layui-input-inline { width: auto; }
        .layui-form-select .layui-edge { border-top-color: transparent; right: 5px; top: 25%; }
        .layui-btn { background: #fff !important; border: 1px solid #4A90E2; color: #4A90E2; }
        .layui-btn:hover { color: #4A90E2; }
        .layui-table thead tr, .layui-table-header { background: #F8F8F8; color: #333333; }
        .layui-tab { background: #fff; }
        .layui-tab-brief > .layui-tab-more li.layui-this::after, .layui-tab-brief > .layui-tab-title .layui-this::after { border-bottom: none; }
        .layui-tab-title { border-bottom: none; }
        .noti-contentlist h5 { font-size: 16px; color: #34495E; font-weight: bold; line-height: 30px; height: 30px; cursor: pointer; }
        .noti-contentlist span { cursor: pointer; }
        .noti-content { margin: 0px 0px 10px 0px; position: relative; clear: both; }
        .noti-content:hover { border: 1px solid #fff; }
        /*切换tab图标*/
        .publish { background: url(../../images/healthimg/published.png)no-repeat; width: 35px; height: 35px; }
        .unpublish { background: url(../../images/healthimg/unpublished.png)no-repeat; width: 35px; height: 35px; }
        .received { background: url(../../images/healthimg/received.png)no-repeat; width: 35px; height: 35px; }
        .layui-this .publish { background: url(../../images/healthimg/published_lt.png)no-repeat; width: 35px; height: 35px; }
        .layui-this .unpublish { background: url(../../images/healthimg/unpublished_lt.png)no-repeat; width: 35px; height: 35px; }
        .layui-this .received { background: url(../../images/healthimg/received_lt.png)no-repeat; width: 35px; height: 35px; }
        .div-content { position: relative; }
		.noti-content dl dt {
		  width: 250px;

		}
		.noti-content dl dd{width: 82%;padding: 15px 0 0px 5px; line-height: 23px;}
		
		@media only screen and (max-width: 1700px) {
			.noti-content dl dd{width: 82%;}
		}
		@media only screen and (max-width: 1600px) {
			.noti-content dl dd{width:82%;}
		}
		@media only screen and (max-width: 1500px) {
			.noti-content dl dd{width: 80%;}
		}
		@media only screen and (max-width: 1400px) {
			.noti-content dl dd{width: 80%;}
		}
		@media only screen and (max-width: 1230px) {
			.noti-content dl dd{width: 75%;}
		}
		@media only screen and (max-width: 1000px){
			.noti-content dl dd{width:65%;}
		}

    </style>
</head>
<body>
    <div class="marmain">
        <div class="layui-tab layui-tab-brief" style="margin: 0;">
            <ul class="layui-tab-title" style="width: 300px; float: left;">
                <li class="layui-this btntab" data-status="1"><u class="publish" style="margin-right: 5px;"></u>已发布</li>
                <li class="btntab" data-status="0"><u class="unpublish" style="margin-right: 5px;"></u>未发布（草稿）</li>
            </ul>
            <div style="margin-right: 10px; line-height: 30px; margin-top: 7px;" class="layui-form">
                <input type="checkbox" value="1" name="chkrelease" lay-filter="chkrelease" title="只看我发布的">
               <input type="checkbox" value="1" name="chkisopen" lay-filter="chkisopen" title="是否对家长开放">
            </div>
            <div class="layui-form js-search" style="float: right; margin-top: -49px;">
                <div class="layui-form-item">
                    <button id="btnadd" class="layui-btn" style="height: 30px; line-height: 30px; float: left; color: #fff !important; background: #4A90E2 !important; margin-top: 16px; margin-right: 10px; padding: 0 12px;">新建健康宣教</button>
                    <div class="layui-input-inline" style="width: 270px; margin-right: 20px; margin-top: 16px;">
                        <input id="txtkey" required="" lay-verify="required" placeholder="输入关键字进行搜索" autocomplete="off" class="layui-input" style="height: 30px; padding-right: 30px;">
                        <img id="btnsearch" src="../../images/search.png" style="float: right; margin: -22px 10px;">
                    </div>
                </div>
            </div>
        </div>
        <!--已发布-->
        <div class="marmain-cen" id="js-sent">
            <div class="noti-content">
                <div class="columnlist" id="divtypeslist">
                </div>
                <div class="content-medical" style="margin: 5px 0;" id="divcontent">
                    <div id="divlist" style="overflow: auto;">
                    </div>
                    <div id="laypage" style="position: absolute; right: 30px;"></div>
                </div>
            </div>
        </div>
    </div>
    <script data-main="../../js/healthxj/healthpublicizelist" src="../../sys/require.min.js"></script>
</body>
</html>
