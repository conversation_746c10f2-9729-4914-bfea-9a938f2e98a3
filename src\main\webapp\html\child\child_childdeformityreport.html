﻿<!DOCTYPE html>
<html>
<head>
	<title>沈阳市0-6岁儿童残疾筛查管理统计表</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<link href="../../css/style.css" rel="stylesheet" type="text/css" />
	<link href="../../styles/tbstyles.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript">
		document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
	</script>
	<style>
		.divprintreport span {
			display: block;
			float: left;
			text-align: center;
		}
		.rytjdjedit_table td {
			white-space: normal;
			border: black solid 1px;
			line-height: 38px;
			text-align: center
		}
		.lineght {
			line-height: 17px;
			width: 25px;
			text-align: center;
		}
	</style>
</head>
<body>
<div id="divtable" class="rytjdjedit_table" style="width:96%;margin: 20px auto;">
	<div style="text-align:center;font-size:20px;font-weight:bold;" id="divcrbtitle">沈阳市0-6岁儿童残疾筛查管理统计表(    &nbsp;&nbsp;&nbsp;&nbsp;   年  &nbsp;&nbsp;&nbsp; 季度）

	</div>
	<div class="divprintreport" style="font-size:10pt;font-weight:bold; text-align:left; margin-top:10px;"> <input type="text" style="border:none; border-bottom:1px solid #000 ; width:100px;"  />  区（县、市）   </div>
	<table  cellspacing="0" cellpadding="0" border="2" style="margin:15px auto 0 auto;">
		<tbody>
		<tr>
			<td rowspan="3">单位</td>
			<td colspan="2">儿童数</td>
			<td colspan="7">初筛统计</td>
			<td colspan="12">转归统计</td>
		</tr>
		<tr>
			<td rowspan="2">≤3岁</td>
			<td rowspan="2">＜7岁</td>
			<td rowspan="2">应初筛儿童数</td>
			<td rowspan="2">实际初筛儿童数</td>
			<td rowspan="2">初筛阳性儿童数</td>
			<td colspan="4">初筛阳性儿童疑似残疾类型</td>
			<td rowspan="2">转介复筛儿童数</td>
			<td rowspan="2">实际复筛儿童数</td>
			<td rowspan="2">复筛阳性儿童数</td>
			<td colspan="5">复筛阳性儿童疑似残疾类型</td>
			<td colspan="2">未转介诊断儿童数（包括本季度+既往）</td>
			<td colspan="2">转介诊断儿童数（包括本季度+既往）</td>
		</tr>
		<tr>
			<td>视力残疾</td>
			<td>听力残疾</td>
			<td>肢体残疾</td>
			<td>发育偏异</td>
			<td>视力残疾</td>
			<td>听力残疾</td>
			<td>肢体残疾</td>
			<td>智力残疾</td>
			<td>孤独症</td>
			<td>本季度</td>
			<td>既往</td>
			<td>本季度</td>
			<td>既往</td>
		</tr>
		<tr id="trdeformityreport">
			<td>&nbsp;</td>
			<td>&nbsp;</td>
			<td>&nbsp;</td>
			<td>&nbsp;</td>
			<td>&nbsp;</td>
			<td>&nbsp;</td>
			<td>&nbsp;</td>
			<td>&nbsp;</td>
			<td>&nbsp;</td>
			<td>&nbsp;</td>
			<td>&nbsp;</td>
			<td>&nbsp;</td>
			<td>&nbsp;</td>
			<td>&nbsp;</td>
			<td>&nbsp;</td>
			<td>&nbsp;</td>
			<td>&nbsp;</td>
			<td>&nbsp;</td>
			<td>&nbsp;</td>
			<td>&nbsp;</td>
			<td>&nbsp;</td>
			<td>&nbsp;</td>
		</tr>
		</tbody>
	</table>

	<div class="divprintreport" style="font-size:10pt;font-weight:bold;text-align: left;
margin: 10px auto;"> <span class="lbselectdate0" style="width:25%;text-align: left;">  填报单位：</span> <span style="width:25%;">领导签字：</span><span style="width:25%;">填报人：</span><span style="width:25%;">日期：</span></div>

	<div  style="font-size:12px;
padding: 10px 0 0 0; clear:both; "><p>填表说明：1.2024年开始要掌握复筛阳性儿童的结局（异常还是正常，即诊断机构的就诊结果）。</p>
		<p>2.“既往未转介诊断儿童数”是指统计时限之前的未去诊断机构就诊的复筛阳性儿童数。</p>
		<p>3.“转介诊断儿童数”是指本季度发现的和既往未去就诊的复筛阳性儿童中在统计时间内去就诊的儿童总数。逻辑关系：U+V≤M+T;</p></div>
</div>

<div id="divprint" style="height: 0px; display: none;">
	<input id="btprint" type="button" style="display: none;" value="打印"/>
	<input id="btexcel" type="button" style="display: none;" value="导出excel"/>
</div>
<script type="text/javascript">
	var v = top.version;
	document.write('<script type="text/javascript" src="../../sys/jquery.js?v="' + v + '><' + '/script>');
	document.write('<script type="text/javascript" src="../../sys/arg.js?v="' + v + '><' + '/script>');
	document.write("<script type='text/javascript' src='../../sys/system.js?v='" + v + "><" + "/script>");
	document.write('<script type="text/javascript" src="../../layui-btkj/layui.js?v="' + v + '><' + '/script>');
	document.write('<script type="text/javascript" src="../../plugin/js/jquery.cookie.js?v="' + v + '><' + '/script>');
	document.write('<script type="text/javascript" src="../../plugin/contextmenu/js/jquery.contextmenu.js?v="' + v + '><' + '/script>');
	document.write('<script type="text/javascript" src="../../plugin/jqueryprint/jQuery.print.js?v="' + v + '><' + '/script>');
	document.write("<script type='text/javascript' src='../../plugin/reporttable/js/reporttable.js?v='" + v + "><" + "/script>");
	document.write("<script type='text/javascript' src='../../js/tong/medicalstandard.js?v=" + v + "'><" + "/script>");
	document.write("<script type='text/javascript' src='../../js/tongph/commonutil.js?v=" + v + "'><" + "/script>");
	document.write('<script type="text/javascript" src="../../js/child/childcommon.js?v="' + v + '><' + '/script>');
	document.write('<script type="text/javascript" src="../../js/child/childcountreport.js?v="' + v + '><' + '/script>');
</script>
</body>
</html>
