body,ul,p,dd,dl{margin:0;padding:0;}
body {font-family:"Microsoft yahei";font-size: 16px;background-size: 100% 100%;}
h1,h2,h3,h4,h5,h6{margin: 0; font-weight: normal;}
li{list-style:none;}
a {font-size:100%;vertical-align:baseline;background:transparent;text-decoration:none;outline:none;cursor:pointer;color:#7f7f7f;}
em{color:#f00;font-style:normal;}
i{font-style:normal;}
button,input{*overflow: visible;line-height: normal;font-family: 'microsoft yahei';}
textarea{resize:none;}
select{font-family:"Microsoft yahei";}
input,textarea{font-family:"Microsoft yahei"; font-size: 14px;}
input:focus,textarea:focus,button{outline: none;}
input[type='checkbox'],input[type='radio']{vertical-align: middle;border:0;}
th{text-align: center;}
img{border:0;}
table{width:100%;border-collapse: collapse;}
.pa{position: absolute;}
.pr{position: relative;}
.font12{font-size: 12px;}
.font14{font-size: 14px;}
.font16{font-size: 16px;}
.font18{font-size: 18px;}
.font36{font-size: 36px;}
.fl{float: left;}
.fr{float: right;}
.block{display: block;}
.pointer{cursor: pointer;}
.red{ color:#ff7550;}
.blue{ color:#1fb8b3}
.green{ color:#20e0bb}
.pink{ color:#fa4cbb}
.cred{color: #ff5a5a}
.sky-blue{color: #399bf7}
.sl-blue{color: #4A90E2}
.orange{color: #f98341;}
.blue-txt{color:#4A90E2; }
.yellow{color:#FFAA1C;}
/*color*/
.white{color:#fff;}

/*clearfix*/
.clearfix:after{
  content:".";
  display:block;
  height:0;
  clear:both;
  visibility:hidden;
}
.clearfix{
  display:inline-block;
}
* html .clearfix{
  height:1%;
}
.clearfix{
  display:block;
}
.marmain{margin:0 8px 8px 8px;}
.marmain.topmar{margin:5px 8px;}
.marmain-cen{margin: 5px 0px;}
.layui-comselect{padding: 5px 0px;}
.marleft{margin: 0 8px;}
.marmain15{ margin:5px 20px;}
#divdeskleft::after {
  content: "";
  width: 6px;
  position: absolute;
  top: 0px;
  bottom: 0;
  right: -6px;
  box-shadow: 5px 0px 5px -4px inset #DCE0E4;
}

.yslist {margin: 0 20px;}
.yslist .layui-form-label{text-align: left;}