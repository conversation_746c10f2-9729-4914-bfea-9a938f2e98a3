﻿<!DOCTYPE html>
<html style="overflow: hidden;">
<head>
    <meta charset="utf-8" />
    <title>健康宣教-自建库</title>
    <link rel="stylesheet" href="../../css/reset.css" />
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" type="text/css" href="../../css/icon.css" />
    <link rel="stylesheet" type="text/css" href="../../css/style.css" />
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css" />
    <link rel="stylesheet" href="../../css/medical.css">
    <style>
        html, body { background: #EAEFF3; }
		.layui-col-space10 > * { padding: 0 5px 10px 5px; }
    </style>
</head>
<body>
    <div class="layui-page">
        <div class="layui-form divsearch content-medical">
            <div class="layui-form">
                <div class="examination-number">
                    <div class="layui-form-item" style="display: inline-block; vertical-align: top;">
                        <div class="def-search" style="display: inline-block; vertical-align: top; margin: 0px 10px 0 10px;">
                            <label>
                                <input type="text" id="txtkey" placeholder="请输入文章标题" class="layui-input" style="width: 200px; float: left; margin-right: 10px;">
                            </label>
                            <button id="btnsearch" class="layui-btn form-search">搜索</button>
                            <button id="btnadd" class="layui-btn form-search" style="margin-left: 10px;">新建健康宣教</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="content-medical comcolumn" style="margin: 5px 0; " id="divcontent">
            <div class="allcontent-left" id="divleft">
                <div class="all-functions">
                    <ul id="divtypeslist">
                    </ul>
                </div>
                <div class="addfl-btn">
                    <button id="btnaddtypes" class="layui-btn form-search" style="background: #00AC9F !important;">新增分类</button>
                </div>
            </div>
            <div class="allcontent-right" id="divright">
				<div class="allcontent-details" style="margin: 0 20px;">
				  <h3 style="margin-top: 30px; text-align: center;">周卫国</h3>
					<h4 style="text-align: center; line-height: 30px; color: #939599; margin-top: 10px;">2023-08-21 14:30:24<span style="margin-left: 10px;">阅读：100</span></h4>
					<div class="detail-doc">                   
						<p style="text-indent: 2em;color: #303030;">擅长治疗小儿消化、系统疾病、小儿肝炎、小儿多动症、小儿皮肤病、小儿癫痫、小儿遗尿、小儿过敏性疾病、小</p>                </div>
				</div>
				
            </div>
        </div>
    </div>
    <script data-main="../../js/healthxj/healthstorelist" src="../../sys/require.min.js"></script>
</body>
</html>
