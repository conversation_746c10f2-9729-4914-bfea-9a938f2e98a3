/*粉色皮肤*/
/*color*/
.bg1{background: #e79bb9;}
.bg3{background: #e79bb9;}
.bg4{background: #f3f3f3;}/*浅灰*/
.bg5{background: #e9e9e9;}/*深灰*/
.cl1{color: #47494a;}
.cl2{color: #3398dc;}/*粉色*/


/*icon*/
u[class^=skinIcon],.layim_chateme .layim_chatsay .layim_zero,skinIcondenglu u,skinIconbq u{background-image:url(../images/icon-red.png);}

/*btn*/
.btn-big,.personalImg .webuploader-pick,.btn-black,.monthHead,.echarts-dataview button{background: #de575d;}
.popTitle,.fc-day-header{background: #e79bb9;}
.letterIndex{background: #ccc;}
.btn-border{border:1px solid #e79bb9;}
.btn-border:hover{background: #e79bb9;color: #fff;}
.btn-black:hover,.btn-big:hover{background: #d65b8c;}
.layui-layer-btn a.layui-layer-btn0,.btnTop a:hover,.btn-control:hover,.operateBtn a:hover{background:#e79bb9;border-color:#d65b8c;}

/*tab*/
.tab li:hover, .tab li.current{color: #fff; background:#e79bb9!important;border: 1px solid #e79bb9;}
.tab li.pulldown.current{color: #fff; background:#e79bb9 url(../images/icon_pulldown_white.png) no-repeat 75px center!important;background-size: 12px !important;border: 1px solid #e79bb9;}
.tab li.pulldown:hover{color: #fff; background:#e79bb9 url(../images/icon_pulldown_white.png) no-repeat 75px center!important; background-size: 12px !important;border: 1px solid #e79bb9;}


/*page*/
.dataTables_wrapper .dataTables_paginate .paginate_button.current, .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover,.dataTables_wrapper .dataTables_paginate .paginate_button:hover{ background: -webkit-linear-gradient(#e79bb9 0%, #d65b8c 100%);background: linear-gradient(#e79bb9 0%, #d65b8c 100%);color: #fff !important;border-color:#d65b8c;filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#e79bb9', endColorstr='#d65b8c', GradientType=0);}

.popTitle, .fc-day-header{background: #e79bb9;}
/*聊天窗口*/
.layim_chatmore{border-right:1px solid #d65b8c; background-color:#e79bb9;}
.layim_chatlist li:hover,.layim_chatlist .layim_chatnow,.layim_chatlist .layim_chatnow:hover{background-color:#e9b3c8;}
.layim_sendbtn{ background-color:#e79bb9;}
.layim_enter{border-left-color:#e9b3c8;}
.layim_sendbtn:hover,.layim_enter:hover{background-color:#d65b8c;}
.layim_sendtype span:hover,.layim_sendtype span:hover i{background-color:#e79bb9;color:#fff;}
.layim_chateme .layim_chatsay{background: #e9b3c8;}


/*左侧面板*/
.funList li{color: #e79bb9;}
/*.usualFun a:hover,.inner a:hover,*/.letterList li:hover,.letterList li.current,.btn-head:hover,.articleGray a:hover,.loadMore:hover,.innerHistory li:hover,.today_date,.alm_content p,.list .current,.ztree li a:hover,.foldList li p,.foldList li:hover,.dataTables_wrapper a,.otx-table a,.foldList li,.fc-sat .fc-day-num, .fc-sun .fc-day-num{color: #e79bb9;}
footer a.current,footer a:hover{color: #e79bb9;}
.curList .current,.curList a:hover{color: #753a51;}
.list li{border-bottom: 1px solid #e79bb9;}
.leftTitle{border-bottom: 1px solid #e79bb9; background: #e79bb9;}
.btn-lang:hover,.current.btn-lang{border-color:#e79bb9;color:#e79bb9;}
.list .face{border-color:#b8b8b8;}

/*智能中控*/
.btn-far:hover,.btn-near:hover{background:url(../images/distance-red.png);}
.fast:hover,.normal:hover,.slow:hover,.arrow-up:hover,.arrow-down:hover{background: url(../images/hover-pink.png);}
.numberList span:hover{background: url(../images/pink-big.png);}

/*通知公告*/
.noticeTitle a:hover{color:#e79bb9;}


/*日历*/
.fc-state-down,.fc-state-active,.fc-button:hover{border-color:#3398dc;}
.fc-planmeeting-button{border:0;border-radius: 0;}
.fc-year-button:hover,.fc-month-button:hover,.fc-agendaWeek-button:hover,.fc-agendaDay-button:hover,.fc-state-active{background: #e79bb9;color:#fff;}

/*会控*/
.meetingIndex,.fc-planmeeting-button{color:#fff; background: #e79bb9;}
.rankList span.current{background: #e79bb9;}

/*栏目管理*/
.meauList span{border-top: 2px solid #e79bb9; border-bottom: 2px solid #e79bb9;}
.meauList .inner a{color: #e79bb9;}
.inner a.selected{background: #f3d2df;}

/*单位管理*/
.jOrgChart .node,.formList .webuploader-pick{background:#e79bb9;}

/*tree*/
.ztree li a.curSelectedNode{background-color: #f3d2df;border: 1px solid #e79bb9; color: #e79bb9;}

/*会议控制*/
.meetMain{border-top: 4px solid #e79bb9;}
.submeetList{border-top: 4px solid #f3d2df;}

/*下拉多选*/
.ui-widget-header{background: #e79bb9; border-color:#e79bb9;}
.ui-state-hover, .ui-widget-content .ui-state-hover, .ui-widget-header .ui-state-hover, .ui-state-focus, .ui-widget-content .ui-state-focus, .ui-widget-header .ui-state-focus{color: #e79bb9;}
.ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active{background: none; border-color:#e79bb9;color: #e79bb9;}
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default{ border-color:#e79bb9;color: #e79bb9;}

/*幼儿园名称下拉背景色*/
.selectColor{background-color:#e79bb9 }

/*智慧数字幼儿园*/
/*桌面头部菜单*/
.ultopnums li.current,.ultopnums li:hover,.topultool li:hover,.topultool li.current {
    color: #ffffff;
    background: -webkit-linear-gradient(#e79bb9, #d65b8c); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(#e79bb9, #d65b8c); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(#e79bb9, #d65b8c); /* Firefox 3.6 - 15 */
    background: linear-gradient(#e79bb9, #d65b8c); /* 标准的语法 */
}
/*菜单切换*/
/*菜单切换*/
.menutab ul{color: #34495E!important;}
.menutab ul li{color: #F3F4F4;border-bottom:3px solid #D5D6D6; 
	background: #F3F4F4;		
	border-image: -webkit-linear-gradient(#F2F3F4,#D5D6D6) 40 20;
    border-image: -moz-linear-gradient(#F2F3F4,#D5D6D6) 40 20;		
	border-image: -o-linear-gradient(#F2F3F4,#D5D6D6) 40 20;
	border-image: linear-gradient(#F2F3F4,#D5D6D6) 40 20;}
.menutab ul li.current{color: #34495E!important; background: #fff;}
#divdesktop .scrollArea{background: none;}
#divmenulist .funList li:hover{background: #F2F2F2;color: #666!important;}
#divdeskleft:after{content: "";width: 20px;position: absolute;top: 0px;bottom: 0;right: -20px;box-shadow: 5px 0px 5px -4px inset #DCE0E4;
  
}
.funtop .desktop-logo .tabout-div{box-shadow: 0px 0px 5px 2px #d65b8c;}
.funtop .desktop-logo .tab-cell{box-shadow: 0px -8px 4px -5px inset #f3d2df;}

/*保教*/
.term-tab.current{background: #e07ea4;color: #ffffff;}
.term-tab.current .calendar-div{border: 1px solid #d45f8c;}
.term-list.redbg{background: #e79bb9;}
.calen-top h5{background: #e07ea4;}
.calen-txt p{color: #d56b94;}
.div-right .layui-table tbody.current{border: 2px solid #e79bb9;}
.div-right .layui-table tbody.current tr:nth-child(1) td:nth-child(1){background-color: #e79bb9;}
.div-right .layui-table td.pinkcurrent{background-color: #e79bb9;}
.workrest-left h5{background: #e07ea4!important;}
.workrest-right h5{background: #e79bb9!important;}
.time-work li.time-worksel{ background:#e79bb9;}
.week-tab.current{background: #e07ea4;}
.icon-list .layui-btn{background: #e07ea4!important;}
.protect-educate .plan-txt.current{background: #fd9d9d;color: #ffffff;border: 1px solid #e79bb9;}
.play-sub{border: 2px solid #e79bb9!important;}
.def-icon .icon_calen:before{color: #e79bb9!important;}
.opreate-icon .icon-txt .iconfont{color: #e79bb9!important;}
.protect-educate .plan-left-icon>div .iconfont:before{color: #e79bb9;}
.protect-educate .plan-left-icon>div .icon_ratio:before{border: 2px solid #e79bb9;}
.def-icon .icon_tableft:before,.def-icon .icon_tabright:before{color: #e79bb9;}
#a_unselect{color: #e79bb9!important;}
.upload-btn .opr_select{background: #e79bb9!important;}
.opr-btn.pay-save{background: #e79bb9!important;}
#pgriddivshipuarea.def-layui-table tr.current td:not(.tr-firsttd){background: #fbd6e4;}
#pgridplantable.def-layui-table tr.current td:not(.tr-firsttd){background: #fbd6e4;}
/*作息弹框按钮颜色*/
.opr-btn{background:#e79bb9;}
.cho-main .layui-btn{background: #e79bb9;}
.plan-con{border: 4px solid #e79bb9;}
.plan-leftspan.currentpink, .plan-rightspan.currentpink{background: #e79bb9;}
.plan-leftspan.currentred, .plan-rightspan.currentred{background: #eb5892;}
.left-contain .common-tab3 li.current a{color: #e79bb9;font-weight: bold;border-bottom: 2px solid #e79bb9;}
.left-contain .icon_search2:before{color: #e79bb9;}
.type-plan .iconfont.redicon2:before{color: #e79bb9;}
#divdt .layui-table tbody.current{border: 2px solid #e79bb9;}
#divdt .layui-table tbody.current tr:nth-child(1) td:nth-child(1){background-color: #e79bb9;}
#divdt .layui-table td.pinkcurrent{background-color: #e79bb9;}
#divdt .tdexist{background-color: #eb5892;}
.timetable-list .circle-label{background: #e79bb9;}
.teaching-list .teachadd-btn .teachlayui-btn{background: #e79bb9;}
.upload-list .layui-btn-normal, .teaching-list-bom .upload-right .layui-btn, .cover-list .layui-btn{background: #e79bb9;}
#add_pic .webuploader-pick{background: #e79bb9;}
.teaching-list .layui-btn{color: #e79bb9;}
.upload-btn .opr_save{background: #e79bb9!important;}
.upload-btn .opr_saveadd{background: #e79bb9!important;}
.course-opr .layui-tab-brief>.layui-tab-title .layui-this{color: #e79bb9;}
.course-opr .layui-tab-brief>.layui-tab-more li.layui-this:after,.course-opr .layui-tab-brief>.layui-tab-title .layui-this:after{border-bottom: 4px solid #e79bb9;}
#add_pic .webuploader-pick{background:#e79bb9;}
#add_pic .webuploader-pick, #add_video .webuploader-pick{background:#e79bb9;}
#add_audio .webuploader-pick{background:#e79bb9;}
#add_files .webuploader-pick{background:#e79bb9;}


