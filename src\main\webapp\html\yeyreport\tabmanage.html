﻿<!DOCTYPE html>
<html lang="en">
<head>
    <title>表格管理</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <link rel="stylesheet" href="../../css/reset.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <style type="text/css">
        html, body {
            background: #EAEFF3;
        }
    </style>
</head>
<body>
<div class="marmain">
    <div class="content-medical">
        <div class="layui-form layui-comselect" style="position: relative; ">
            <div class="layui-form-item" style="display: inline-block;vertical-align: top; margin-left:20px;">
                <label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 0px;line-height: 28px;">表格类型：</label>
                <div class="layui-input-block" style="min-height: 30px;margin-left:85px;width:200px;">
                    <select lay-filter="sel_type" id="sel_type">
                        <option value="">全部</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item" style="display: inline-block;vertical-align: top; margin-left:20px;">
                <label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 0px;line-height: 28px;">表格名称/描述：</label>
                <div class="layui-input-inline" style="min-height: 30px;width:150px;">
                    <input type="text" id="keyword" placeholder="表格名称/描述" class="layui-input">
                </div>
            </div>
            <button id="btnsearch" class="layui-btn form-search" style="vertical-align: top;margin-left: 10px;">查询</button>
        </div>
    </div>
    <div class="marmain-cen">
        <div class="content-medical">
            <table id="tabelList" lay-filter="test"></table>
        </div>
    </div>
</div>
<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/system.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../js/yeyreport/tabmanage.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>
