﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>加入设置</title>
    <link rel="stylesheet" href="../css/reset.css"/>
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../css/style.css"/>
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <!--[if lt IE 9]>
    <script src="../sys/html5shiv.min.js"></script>
    <![endif]-->
    <style>
    </style>
</head>
<body>
<section class="addset">
    <div class="bg4 pd10-20 layui-form">
        <div style="margin-top:15px">
            <div class="layui-form-item">
                <label class="layui-form-label">单位ID：</label>
                <div class="layui-input-inline">
                    HD0870717471
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">单位名称：</label>
                <div class="layui-input-inline">
                    北京市海淀区妇幼保健院
                </div>
            </div>
            <div class="layui-form-item" style="position: relative;">
                <label class="layui-form-label">是否开启自动审批：</label>
                <div class="layui-input-block">
                    <input type="radio" name="rdautoapprove" value="是" title="是"><div class="layui-unselect layui-form-radio"><i class="layui-anim layui-icon"></i><span>是</span></div>
                    <input type="radio" name="rdautoapprove" value="否" title="否" checked=""><div class="layui-unselect layui-form-radio layui-form-radioed"><i class="layui-anim layui-icon"></i><span>否</span></div>
                    <div class="layui-form-radio appr-txt"><img src="../images/light.png"><div class="appr-con"><p>开启自动审批模式后，幼儿园只要将ID与加入码均输入正确并提交后即可自动加入本大数据系统，请设置6位数字及字母组合的加入码并妥善保管哦~</p></div></div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">输入加入码：</label>
                <div class="layui-input-inline">
                    <input id="" type="password" name="password" required lay-verify="required" placeholder="" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">是否必须输入加入码：</label>
                <div class="layui-input-block">
                    <input type="radio" name="mustjoincode" value="是" title="是"><div class="layui-unselect layui-form-radio"><i class="layui-anim layui-icon"></i><span>是</span></div>
                    <input type="radio" name="mustjoincode" value="否" title="否" checked=""><div class="layui-unselect layui-form-radio layui-form-radioed"><i class="layui-anim layui-icon"></i><span>否</span></div>
                </div>
            </div>
            <div>
                <label></label><a id="" class="btn-black" style="margin-left: 120px; margin-top: 10px;"><span
                    class="lanflag" id="lansave">修改密码</span></a>
            </div>
        </div>
    </div>
</section>
<script type="text/javascript" src="../sys/jquery.js"></script>
<script type="text/javascript" src="../sys/arg.js"></script>
<script type="text/javascript" src="../layui-btkj/layui.js"></script>
<script type="text/javascript" src="../js/w_setting.js"></script>
</body>
</html>
