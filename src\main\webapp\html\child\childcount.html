﻿<!DOCTYPE html>
<html>
<head>
    <link rel="shortcut icon" href="/images/favicon.ico" type="image/x-icon" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="Copyright" content="Copyright 2016 by tongbang" />
    <meta name="Author" content="" />
    <meta name="Robots" content="All" />
    <title>体检报表</title>
    <link rel="stylesheet" href="../../styles/admin.css" type="text/css" />
    <link rel="stylesheet" href="../../styles/tbstyles.css" type="text/css" />
   	<link rel="stylesheet" href="../../styles/xbcomstyle.css">
	<link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../plugin/ehaiform/css/tbbaseform.css" type="text/css" />
    <link rel="stylesheet" href="../../plugin/editselect/css/editselect.css" type="text/css" />
    <style>
		body{
			 background: #F4F5F7;
            height:100%;
		    }
        .layui-table tbody tr:hover, .layui-table-hover{background: #fff!important;}
        .layui-table tbody tr p:hover, .layui-table-hover{background: #fff!important;cursor: pointer;}
        .layui-table td, .layui-table th{padding: 8px 5px;}
        .layui-table td div{padding: 0 6px 0 24px;position: relative;}
        .layui-form-item{ font-size: 14px; background: #fff; margin-bottom: 0px;}
        .common-tab li img{margin-top: 10px;}
        .layui-table[lay-size="lg"] td{padding: 8px 10px;}
        .layui-form-radio i{ font-size: 17px;}
        .layui-form-radio i:hover, .layui-form-radioed i{ color:#01a39a ;}
        .census-main {margin: 0 10px;}
        .layui-table th{ color: #333333;text-align: center; font-size: 16px; background:#F4F9FF; }
        .layui-table td.region{background: #F4F9FF;color: #333333;font-size: 16px;font-weight: normal;border-bottom: 1px solid #ccc;border-right:1px solid #ccc;}
        .layui-table td.region span {display: block;width: 13px;margin: 0 auto;}
        .census-main .health-list p{ padding: 0 0px 10px 0px;font-size: 14px;}
		.census-main .health-list p:hover{color:#5BBDEE;}
        .census-main .health-list p img{ margin-right: 5px;}
        .cal_ico{background:url(../../images/cal_icon_green.png) no-repeat right 5px center; }
        .p_tjcount{
            width: 280px;
        }
        /*.p_tjcount,.tdselyey{display: none;}*/
        /*.areacode_count{display: inline-block;}*/
    </style>
</head>
<body>
<div class="bodywidth" style="height:100%;min-width: 1000px;">
    <div class="content">
        <div id="form">
            <fieldset style="width:98%; margin:0 auto">
                <div class="legend_bjcyadd">
                   <i class="left-tit"></i><label id="lbtitle">统计条件</label>
                </div>
                <table cellpadding="0" cellspacing="0" class="tablestyle_field" style="width: 100%;margin-top: 10px;">
                    <tr>
                        <td class="tdspan" s>
                            <span>体检日期：</span>
                        </td>
                        <td>
                            <input type="text" name="" class="input-txt" id="djmonthstart" style="width:100px"><img id="icondjmonthstart" src="../../images/newicon/ico_calendar.png" style="cursor: pointer;position: relative;top: -1px;width: 20px;left: -30px;">
                            <span style="display: inline-block;margin-left: -20px;">至</span><input type="text" name="" class="input-txt" id="djmonthend" style="width:100px"><img id="icondjmonthend" src="../../images/newicon/ico_calendar.png" style="cursor: pointer;position: relative;top: -1px;width: 20px;left: -30px;">
                        </td>
                        <td class="tdspan" >
                            <span>出生日期：</span>
                        </td>
                        <td>
                            <input type="text" name="" class="input-txt" id="childbirday1" style="width:100px"><img id="iconchildbirday1" src="../../images/newicon/ico_calendar.png" style="cursor: pointer;position: relative;top: -1px;width: 20px;left: -30px;">
                            <span style="display: inline-block;margin-left: -20px;">至</span><input type="text" name="" class="input-txt" id="childbirday2" style="width:100px"><img id="iconchildbirday2" src="../../images/newicon/ico_calendar.png" style="cursor: pointer;position: relative;top: -1px;width: 20px;left: -30px;">
                        </td>
                        <td class="tdspan" >
                            <span>体检年月：</span>
                        </td>
                        <td>
							
                            <select name="selckage" lay-verify="required" lay-filter="selckage" id="selckage" style="width: 100px;">
                                <option value="">请选择</option>
                                <option value="0.01">1月</option>
                                <option value="0.03">3月</option>
                                <option value="0.06">6月</option>
                                <option value="0.08">8月</option>
                                <option value="1.00">1岁</option>
                                <option value="1.06">1.5岁</option>
                                <option value="2.00">2岁</option>
                                <option value="2.06">2.5岁</option>
                                <option value="3.00">3岁</option>
                                <option value="4.00">4岁</option>
                                <option value="5.00">5岁</option>
                                <option value="6.00">6岁</option>
                            </select>
							
                        </td>
                    </tr>
                </table>
            </fieldset>
            <fieldset class="grid_margin"  style="width:98%; margin:0 auto">
                <div class="legend_bjcyadd">
                    <i class="left-tit"></i><label id="lbtitle1">选择表格</label></div>
                <div class="census-main" style="margin: 10px auto;">
                    <table id="tab_reportlist" class="layui-table" style="width:100%;">
                        <tbody>
                        <tr>
<!--                            <td class="region"><span>通用</span></td>-->
                            <td>
                                <div class="health-list">
<!--                                    <p class="p_tjcount" id="p_allmdicalcount"><img src="../../images/xbimages/exlimg.png" width="23" height="26"/><span>全区幼儿体检人数</span></p>-->
<!--                                    <p class="p_tjcount" id="p_allchildcount"><img src="../../images/xbimages/exlimg.png" width="23" height="26"/>全区儿童数统计</p>-->
<!--                                    <p class="p_tjcount" id="p_childmdicalpeoples"><img src="../../images/xbimages/exlimg.png" width="23" height="26"/>全区幼儿体检人次数</p>-->
                                    <p class="p_tjcount" id="p_healthpj"><img src="../../images/xbimages/exlimg.png" width="23" height="26"/>全区健康管理档案评价表</p>
<!--                                    <p class="p_tjcount" id="p_hukouflow"><img src="../../images/xbimages/exlimg.png" width="23" height="26"/><span>全区幼儿户籍流动情况</span></p>-->
<!--                                    <p class="p_tjcount" id="p_hukou"><img src="../../images/xbimages/exlimg.png" width="23" height="26"/><span>全区幼儿户籍情况</span></p>-->
                                    <p class="p_tjcount" id="p_checkresultbysex"><img src="../../images/xbimages/exlimg.png" width="23" height="26"/>全区幼儿体检汇总（性别）</p>
                                    <p class="p_tjcount" id="p_childhealthyear"><img src="../../images/xbimages/exlimg.png" width="23" height="26"/>7岁以下儿童保健和健康情况年报表</p>
                                    <p class="p_tjcount" id="p_childeyehealth"><img src="../../images/xbimages/exlimg.png" width="23" height="26"/>0-6岁儿童眼保健和视力检查情况检索表</p>
                                    <p class="p_tjcount" id="p_childdeformity"><img src="../../images/xbimages/exlimg.png" width="23" height="26"/>0-6岁儿童残疾筛查管理统计表</p>
                                </div>
                            </td>
                            <td>
<!--                                <div class="health-list">-->
<!--                                    <p class="p_tjcount" id="p_checkresultbyage"><img src="../../images/xbimages/exlimg.png" width="23" height="26"/>全区幼儿体检汇总（年龄）</p>-->
<!--                                </div>-->
                                <div class="health-list">
                                    <p class="p_tjcount" id="p_basicserve"><img src="../../images/xbimages/exlimg.png" width="23" height="26"/>基本公共卫生服务项目监测</p>
                                    <p class="p_tjcount" id="p_highrisk"><img src="../../images/xbimages/exlimg.png" width="23" height="26"/>高危儿管理统计表</p>
                                    <p class="p_tjcount" id="p_childhealth"><img src="../../images/xbimages/exlimg.png" width="23" height="26"/>7岁以下儿童卫生保健报表</p>
                                    <p class="p_tjcount" id="p_childmalnu"><img src="../../images/xbimages/exlimg.png" width="23" height="26"/>0-6岁生长迟缓登记表(     年第   季度)</p>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </fieldset>
        </div>
    </div>
</div>
<script type="text/javascript">
    var v = top.version;
    document.write('<script type="text/javascript" src="../../sys/jquery.js?v="' + v + '><' + '/script>');
    document.write('<script type="text/javascript" src="../../sys/arg.js?v="' + v + '><' + '/script>');
    document.write("<script type='text/javascript' src='../../sys/system.js?v='" + v + "><" + "/script>");
    document.write('<script type="text/javascript" src="../../layui-btkj/layui.js?v="' + v + '><' + '/script>');
    document.write("<script type='text/javascript' src='../../sys/function.js?v='" + v + "><" + "/script>");
    document.write('<script type="text/javascript" src="../../js/child/childcount.js?v="' + v + '><' + '/script>');
</script>
<!--<script data-main="../js/child/childcount" src='../sys/require.min.js'></script>-->
</body>
</html>
