<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="X-UA-Compatible" content="IE=Edge，chrome=1"/>
  <meta name="renderer" content="webkit"/>
  <meta name="robots" content="noindex, nofollow"/>
  <meta charset="UTF-8"/>
  <title>个人中心</title>
  <link rel="stylesheet" href="../css/reset.css"/>
  <link rel="stylesheet" href="../css/icon.css"/>
  <link rel="stylesheet" href="../styles/tbstyles.css"/>
  <link rel="stylesheet" href="../layui-btkj/css/layui.css">
  <link rel="stylesheet" href="../css/style.css"/>
  <style>
    /*进度条*/
    .progressbar_1 {
      background-color: #e7ebef;
      height: 6px;
      width: 530px;
      color: #222222;
      border-radius: 10px;
      z-index: auto;
    }

    .progressbar_1 .bar {
      background-color: #ff8a50;
      height: 6px;
      border-radius: 10px;
      position: relative;
    }

    .secure-high .progressbar_1 .bar {
      background-color: #00cb5b;
    }

    .secure-middle .progressbar_1 .bar {
      background-color: #ff8a50;
    }

    .secure-low .progressbar_1 .bar {
      background-color: #dd3e2b;
    }

    .secure-high .rank-txt {
      color: #00cb5b;
    }

    .secure-middle .rank-txt {
      color: #ff8a50;
    }

    .secure-low .rank-txt {
      color: #dd3e2b;
    }

    /*个人中心*/
    .secure-lefttxt .iconfont:before, .secure-lefttxt .iconfont:after {
      color: #afb8bf;
      font-size: 24px;
      vertical-align: top;
      display: inline-block;
    }

    .secure-cell.current .secure-lefttxt .iconfont:before, .secure-cell.current .secure-lefttxt .iconfont:after {
      color: #00ac9f;
      font-size: 24px;
      vertical-align: top;
      display: inline-block;
    }

    .secure-con {
      position: relative;
      background: #ffffff;
      box-shadow: 0px 0px 15px 0px #eeeeee;
      border-radius: 10px;
      min-width: 960px;
      width: 63%;
      height: 680px;
      margin: 3% auto 0 auto;
      padding: 35px 30px 35px 42px;
    }

    .secure-left {
      width: 245px;
      height: 680px;
      border-right: 2px solid #eef1f4;
      font-size: 18px;
      display: inline-block;
      line-height: 30px;
    }

    .secure-pho {
      width: 102px;
      height: 102px;
      border-radius: 50%;
      background: #ffffff;
    }

    .secure-txt {
      margin-top: 37px;
      margin-left: 15px;
    }

    .secure-txt span {
      color: #292b33;
    }

    .secure-txt p {
      color: #b2b2b3;
    }

    .secure-right {
      height: 680px;
      padding: 0 0 0 35px;
      font-size: 18px;
      display: inline-block;
      vertical-align: top;
    }

    dl.secure-rank {
      border-bottom: 1px solid #eef1f4;
      padding: 3px 0 28px 0;
    }

    dl.secure-rank dt {
      display: inline-block;
    }

    dl.secure-rank dd {
      display: inline-block;
      vertical-align: top;
      margin: 20px 0 0 30px;
    }

    dl.secure-rank dd p {
      font-size: 12px;
      color: #b0b0b0;
      margin-top: 10px;
      font-size: 12px;
      color: #b0b0b0;
    }

    .secure-cell {
      background: #fafbfc;
      margin: 10px 0;
      height: 56px;
      padding: 20px 20px 20px 0;
    }

    .secure-lefttxt {
      height: 56px;
      border-right: 2px solid #eef1f4;
      text-align: center;
      display: inline-block;
      vertical-align: top;
      width: 114px;
      padding-top: 3px;
    }

    .secure-lefttxt p {
      font-size: 16px;
      color: #292b33;
      margin-top: 5px;
    }

    .secure-centertxt {
      display: inline-block;
      vertical-align: top;
      margin: 0 20px;
    }

    .secure-centertxt p {
      font-size: 12px;
      color: #b0b0b0;
      width: 350px;
      line-height: 18px;
      margin-top: 10px;
    }

    .secure-righttxt {
      display: inline-block;
      float: right;
      margin-top: -2px;
    }

    .secure-btn {
      border: none;
      background: #edfdfc;
      border: 1px solid #8ad1c9;
      font-size: 14px;
      color: #00ac9f;
      padding: 3px 10px;
      border-radius: 2px;
      margin: 2px 0;
    }

    .secure-btn.redstate {
      background: #fff5f3;
      border: 1px solid #e6bfbc;
      font-size: 14px;
      color: #dd3e2b;
    }
    .secure-btn.graybg{
      background: rgb(255, 255, 255);
      color: #b2b2b3;
      border: 1px solid #b2b2b3;
    }

    .exit-btn {
      color: #ffffff;
      font-size: 14px;
      background: #dd3e2b;
      padding: 12px 33px;
      border-radius: 30px;
      cursor: pointer;
    }
    .icon_exit:before{
      color: #ffffff;
    }
    /*弹框*/
    .setup-con {
      padding: 15px 0 0 0;
      width: 560px;
      margin: 0 auto;
    }

    .setup-con .layui-input {
      line-height: 35px;
      height: 35px;
    }

    .setup-con .layui-form-label {
      text-align: right;
    }

    .pay-btn a {
      padding: 0 5px;
    }

    .upload-shadow {
      cursor: pointer;
      display: inline-block;
      position: absolute;
      width: 100%;
      height: 45px;
      text-align: center;
      bottom: 0;
    }
    .webuploader-pick {
      padding: 0 !important;
      background-color: #00ac9f !important;
      width: 100%;
      height: inherit;
      font-size: 14px;
    }
    .webuploader-pick .icon_takepic{
      display: inline-block;
      margin: 3px;
      height: 22px;
      line-height: 24px;
      vertical-align: top;
    }
    .webuploader-pick .icon_takepic:after{
      font-size: 15px;
    }
    .layui-layer-nobg .layui-layer-content{
      text-align: center;
    }
    .icon_exit:before {
       content: "\e691";
       font-size: 16px;
       color: #ffffff;
     }
  </style>
  <link rel="stylesheet" href="../plugin/ueditor/third-party/webuploader/webuploader.css">
</head>
<body>
<section>
  <div class="secure-con">
    <div class="secure-left">
      <div style="width: 102px;height: 102px;position: relative;">
        <div class="upload-shadow">
          <div id="btnupimg" style="width: 100px;height: 30px;display: inline-block;margin-left: 110px;">
            <i class="iconfont icon_takepic"></i>上传头像
          </div>
        </div>
        <img class="secure-pho" id="headimage" src="../images/desktop/default.png">
      </div>
      <div class="secure-txt">
        <span>姓名</span>
        <p id="truename"></p>
      </div>
      <div class="secure-txt">
        <span>账号</span>
        <p id="uname"></p>
      </div>
      <div class="secure-txt">
        <span>角色</span>
        <p id="rolename"></p>
      </div>
<!--      <div class="secure-txt">-->
<!--        <span>开通时间</span>-->
<!--        <p id="opentime"></p>-->
<!--      </div>-->
      <div class="secure-txt" id="btnloginout">
        <a class="exit-btn"><i class="iconfont icon_exit" style="margin-right: 10px;"></i>退出登录</a>
      </div>
    </div>
    <div class="secure-right">
      <dl class="secure-rank secure-middle">
        <dt><img src="../images/secure_img.png"></dt>
        <dd>
          <div>安全等级<span class="rank-txt" style="margin-left: 25px;" id="totaltext">中</span></div>
          <div class="progressbar_1" style="margin-top: 10px;" id="totalbar">
            <div class="bar" style="width: 0%;"></div>
          </div>
          <p>您的账户存在安全风险，建议您设置安全性高的密码并定期更换密码</p>
        </dd>
      </dl>
      <div style="padding: 10px 0;">
        <div class="secure-cell current secure-high">
          <div class="secure-lefttxt">
            <i class="iconfont icon_password"></i>
            <p>登录密码</p>
          </div>
          <div class="secure-centertxt">
            <div class="progressbar_1" style="margin-top: 10px;width: 100%;position: relative;" id="pwdbar">
              <div class="bar" style="width: 0%;"></div>
              <span class="rank-txt" style="font-size: 16px;position: absolute;right: -40px;top: -8px;">高</span>
            </div>
            <p>安全性高的密码可以使账号更安全，建议您定期更换密码，密码至少由字母数字组成且不得少于6位数</p>
          </div>
          <div class="secure-righttxt">
            <button class="secure-btn" id="btnchangepwd">修改密码</button>
          </div>
        </div>
        <div class="secure-cell current">
          <div class="secure-lefttxt">
            <i class="iconfont icon_phone"></i>
            <p>手机绑定</p>
          </div>
          <div class="secure-centertxt">
            <p style="font-size: 14px;color: #292b33;" id="pmobile">您已绑定了的手机</p>
            <p>您的手机可用于直接登录，找回密码等</p>
          </div>
          <div class="secure-righttxt">
            <button class="secure-btn" id="btnchangemobile">手机换绑</button>
          </div>
        </div>
        <div class="secure-cell">
          <div class="secure-lefttxt">
            <i class="iconfont icon_wx" style="display: inline-block;height: 24px;"></i>
            <p>微信绑定</p>
          </div>
          <div class="secure-centertxt" id="divweixin">
            <p style="font-size: 14px;color: #292b33;">您未绑定微信</p>
<!--            <p>您可以直接使用微信扫一扫登录系统</p>-->
          </div>
          <div class="secure-righttxt">
            <button class="secure-btn" id="bindweixin" style="display: none">绑定微信</button>
            <button class="secure-btn redstate" id="unbindweixin" style="display: none">解除绑定</button>
          </div>
        </div>
        <div class="secure-cell">
          <div class="secure-lefttxt">
            <i class="iconfont icon_email"></i>
            <p>邮箱绑定</p>
          </div>
          <div class="secure-centertxt" id="pemail">
            <p style="font-size: 14px;color: #292b33;">您已绑定了的邮箱</p>
            <p>您还未绑定邮箱，绑定邮箱可提高账户的安全性</p>
          </div>
          <div class="secure-righttxt">
            <button class="secure-btn graybg" id="bindemail">邮箱换绑</button>
            <br>
            <button class="secure-btn redstate" id="unbindemail">解除绑定</button>
          </div>
        </div>
        <div class="secure-cell" id="divlockscreen">
          <div class="secure-lefttxt">
            <i class="iconfont icon_lockscreen"></i>
            <p>锁屏密码</p>
          </div>
          <div class="secure-centertxt">
            <div class="progressbar_1" style="margin-top: 10px;width: 100%;position: relative;" id="lockpwdbar">
              <div class="bar" style="width: 0%;"></div>
              <span class="rank-txt" style="font-size: 16px;position: absolute;right: -40px;top: -8px;">低</span>
            </div>
            <p>安全性高的密码可以使账号更安全，建议您定期更换密码</p>
          </div>
          <div class="secure-righttxt">
            <button class="secure-btn graybg" id="changelockscreenpwd" style="display: none">修改锁屏密码</button>
            <br>
            <button class="secure-btn redstate" id="btncearlockscreenpwd" style="display: none">清除锁屏密码</button>
          </div>
        </div>

<!--        <div class="secure-cell">-->
<!--          <div class="secure-lefttxt">-->
<!--            <i class="iconfont icon_email"></i>-->
<!--            <p>初始页面</p>-->
<!--          </div>-->
<!--          <div class="secure-centertxt" id="pemail">-->

<!--            <form class="layui-form">-->
<!--              <div class="layui-form-item">-->
<!--                <label class="layui-form-label"></label>-->
<!--                <div class="layui-input-block">-->
<!--                  <input type="radio" name="openpage" value="1" title="系统" lay-filter="rdsystem">-->
<!--                  <input type="radio" name="openpage" value="2" title="常用功能页面" checked lay-filter="rdlastopen">-->
<!--                  <a lay-submit lay-filter="applayopen" class="btn-black" style="margin-left: 7px">-->
<!--                    <span class="lanflag">应用</span>-->
<!--                  </a>-->
<!--                </div>-->
<!--              </div>-->
<!--            </form>-->
<!--          </div>-->
<!--          <div class="secure-righttxt">-->
<!--            <button class="secure-btn" id="btnused">应用</button>-->
<!--&lt;!&ndash;            <br>&ndash;&gt;-->
<!--&lt;!&ndash;            <button class="secure-btn redstate" id="unbindemail">解除绑定</button>&ndash;&gt;-->
<!--          </div>-->
<!--        </div>-->
      </div>
    </div>
  </div>
</section>

<!--修改密码弹框-->
<section class="setup-con" style="display: none;">
  <div class="layui-form-item layui-form" style="margin:0px 0  10px 30px ">
    <label class="layui-form-label" style="width:137px;">旧密码：</label>
    <div class="layui-input-block" style="margin:0 20px 0 144px;">
      <input name="title" lay-verify="title" autocomplete="off" placeholder="请输入旧密码" class="layui-input" type="text" style="display: inline-block;width:280px;">
    </div>
  </div>
  <div class="layui-form-item layui-form" style="margin:5px 0  10px 30px ">
    <label class="layui-form-label" style="width:137px;">新密码：</label>
    <div class="layui-input-block" style="margin-left:144px;">
      <input name="title" lay-verify="title" autocomplete="off" placeholder="请输入新密码" class="layui-input" type="text" style="width:280px; display: inline-block">
    </div>
  </div>
  <div class="layui-form-item layui-form" style="margin:5px 0  10px 30px ">
    <label class="layui-form-label" style="width:137px;">确认密码：</label>
    <div class="layui-input-block" style="margin-left:144px;">
      <input name="title" lay-verify="title" autocomplete="off" placeholder="请再次输入新密码" class="layui-input" type="text" style="width:280px; display: inline-block">
    </div>
  </div>
  <div class="pay-btn" style="margin: 20px 15px;padding: 20px;border-top: 1px solid #cccccc;">
    <a class="opr-btn">确定</a>
    <a class="opr-btn" style="background: #F1F1F1;border: 1px solid #E4E4E4;color: #3B3B3B;">取消</a>
  </div>
</section>

<!--手机换绑弹框-->
<section class="setup-con" style="display: none;">
  <div class="layui-form-item layui-form" style="margin:0px 0  10px 30px ">
    <label class="layui-form-label" style="width:137px;">原手机号：</label>
    <div class="layui-input-block" style="margin:0 20px 0 144px;">
      <input name="title" lay-verify="title" autocomplete="off" placeholder="请输入原绑定手机号" class="layui-input" type="text" style="display: inline-block;width:280px;">
    </div>
  </div>
  <div class="layui-form-item layui-form" style="margin:5px 0  10px 30px ">
    <label class="layui-form-label" style="width:137px;">新手机号：</label>
    <div class="layui-input-block" style="margin-left:144px;">
      <input name="title" lay-verify="title" autocomplete="off" placeholder="请输入新手机号" class="layui-input" type="text" style="width:280px; display: inline-block">
    </div>
  </div>
  <div class="layui-form-item layui-form" style="margin:5px 0  10px 30px ">
    <label class="layui-form-label" style="width:137px;">短信验证码：</label>
    <div class="layui-input-block" style="margin-left:144px;">
      <input name="title" lay-verify="title" autocomplete="off" placeholder="请再次输入短信验证码" class="layui-input" type="text" style="width:190px; display: inline-block">
      <div class="layui-form-mid layui-word-aux" style="float: initial;display: inline-block;"><a style="color: #2fa0ff;text-decoration: underline;">发送验证码</a></div>
    </div>
  </div>
  <div class="pay-btn" style="margin: 20px 15px;padding: 20px;border-top: 1px solid #cccccc;">
    <a class="opr-btn">确定</a>
    <a class="opr-btn" style="background: #F1F1F1;border: 1px solid #E4E4E4;color: #3B3B3B;">取消</a>
  </div>
</section>

<!--绑定邮箱弹框-->
<section class="setup-con" style="display: none;">
  <div class="layui-form-item layui-form" style="margin:0px 0  10px 30px ">
    <label class="layui-form-label" style="width:137px;">邮箱：</label>
    <div class="layui-input-block" style="margin:0 20px 0 144px;">
      <input name="title" lay-verify="title" autocomplete="off" placeholder="请输入邮箱" class="layui-input" type="text" style="display: inline-block;width:280px;">
    </div>
  </div>
  <div class="pay-btn" style="margin: 20px 15px;padding: 20px;border-top: 1px solid #cccccc;">
    <a class="opr-btn">确定</a>
    <a class="opr-btn" style="background: #F1F1F1;border: 1px solid #E4E4E4;color: #3B3B3B;">取消</a>
  </div>
</section>

<!--&lt;!&ndash;绑定微信弹框&ndash;&gt;-->
<!--<section class="setup-con" style="display: none;">-->
<!--  <div style="text-align: center;">-->
<!--    <img src="../images/tbzxico.png" style="width: 140px;">-->
<!--    <p style="font-size: 12px;color: #666666;">微信关注“童帮在线”幼儿园管理端<br>点击【账号绑定】输入本人手机号进行绑定。</p>-->
<!--  </div>-->
<!--  <div class="pay-btn" style="margin: 20px 15px;padding: 20px;border-top: 1px solid #cccccc;">-->
<!--    <a class="opr-btn" style="background: #F1F1F1;border: 1px solid #E4E4E4;color: #3B3B3B;">关闭</a>-->
<!--  </div>-->
<!--</section>-->
</body>
<script src="../sys/jquery.js"></script>
<script src="../sys/system.js"></script>
<script src="../sys/uploadutil.js"></script>
<script src="../layui-btkj/layui.js"></script>
<script src="../plugin/ueditor/third-party/webuploader/webuploader.js"></script>
<script data-main="../js/alterpwd" src="../sys/require.min.js"></script>
</html>
