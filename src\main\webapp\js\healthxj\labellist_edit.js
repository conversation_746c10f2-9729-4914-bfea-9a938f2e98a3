/**
 * 标签管理（标签库）编辑
 */
layui.config({
    base: './js/'
}).extend({ //设定模块别名
    system: '../../../sys/system'
});
var objdata = {
    objtoken: {},
    areacode: {}
    , where: {}
}, tableIns;
var parentobj = null;
var form = null;
var layer = null;
layui.use(['system', 'form'], function () {
    layer = layui.layer;
    form = layui.form;
    initEvent();//初始化事件
    initData();//初始化数据
});

function initEvent() {

}

function initData() {
     //编辑时加载数据
     getEditInfo();
}

function getEditInfo() {
    var id = Arg("id");
    if (!id) return;
    if (id) {
        $.sm(function (re, err, obj) {
            if (err) {
                parent.layer.msg('获取数据失败，请稍后再试！');
                return;
            }
            if (obj) {
                // 处理复选框数据：将数值转换为布尔值
                if (obj.is_active !== undefined) {
                    obj.is_active = obj.is_active == 1;
                }
                form.val('formMain', obj);
            }

        }, ['labellist.getbyid', id]);
    }
}


//保存
function saveEvent(callback) {
    var data = form.val('formMain');
    if (!data.labelname) {
        parent.layer.msg('请输入标签名称');
        return;
    }
    if (!data.category) {
        parent.layer.msg('请选择标签分类');
        return;
    }
    if (!data.algorithm_type) {
        parent.layer.msg('请选择算法类型');
        return;
    }
    if (!data.algorithm_content) {
        parent.layer.msg('请输入算法代码');
        return;
    }

    // 处理复选框值：如果未选中，设置为0；如果选中，设置为1
    data.is_active = data.is_active ? 1 : 0;

    $.sm(function (re, err) {
        if (err) {
            parent.layer.msg('保存失败，请稍后再试！');
            return;
        }
        parent.layer.msg('保存成功');
        if (callback) callback();
    }, ['labellist.add', JSON.stringify(data), $.msgwhere({ id: [Arg('id') || 0] })])
}
