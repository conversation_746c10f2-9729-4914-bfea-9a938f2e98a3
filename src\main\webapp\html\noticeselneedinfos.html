<!DOCTYPE html>
<html>
<head>
    <title>选择报名资料</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <link rel="stylesheet" href="../plugin/zTree/css/zTreeStyle/zTreeStyle.css" type="text/css">
    <link rel="stylesheet" href="../css/reset.css"/>
    <link rel="stylesheet" href="../css/style.css"/>
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../plugin/formSelect/formSelects-v4.css"/>
    <link rel="stylesheet" href="../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../css/medical.css">
    <!--[if lt IE 9]>
    <script src='sys/html5shiv.min.js'></script>
    <![endif]-->
    <style>
        html, body {
            background: #fff;
            color: #333333;
            height: 100%;
        }
    </style>
</head>
<body>
<div class="content-medical" id="container" style="height: 100%;">
    <div class="tbmargin" style="margin: 0 20px;">
        <div id="divnoticeinfos" style="margin: 30px 30px;" class="layui-input-inline">
            <input type="checkbox" name="txtallsel" style="margin-top: -3px;margin-right: 5px;vertical-align: middle;" id="txtallsel"/>
            <label for="txtallsel">全选</label>
            <ul id="ulselneedinfo"></ul>
        </div>
    </div>
</div>
<script>
    var v = top.version;
    document.write('<script type="text/javascript" src="../sys/jquery.js?v=' + v + '"><' + '/script>');
    document.write('<script type="text/javascript" src="../sys/arg.js?v=' + v + '"><' + '/script>');
    document.write('<script type="text/javascript" src="../layui-btkj/layui.js?v=' + v + '"><' + '/script>');
    document.write('<script type="text/javascript" src="../plugin/formSelect/formSelects-v4.js?v=' + v + '"><' + '/script>');
    document.write('<script type="text/javascript" src="../js/noticeselneedinfos.js?v=' + v + '"><' + '/script>');
</script>
</body>
</html>