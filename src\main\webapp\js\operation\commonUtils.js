/*新增日期: 2025.7.8
作 者: 朱辽原
修改时间：
修改人：
內容摘要: 一些公共工具（页面跳转等）
*/
define(['jquery'], function ($) {
    // 全局layer对象，用于弹框显示
    let layer;

    // 动态加载layui的layer模块
    if (typeof layui !== "undefined") {
        if (parent.layer) {
            layer = parent.layer;
        } else {
            layui.use(["layer"], function () {
                layer = layui.layer;
                console.log("Common.js中Layer模块加载完成");
            });
        }
    }

    /*
     功能：通用弹框打开函数(带关闭按钮)
     参数说明：url - 页面URL, title - 弹框标题, callback - 成功回调函数
     返回值说明：无
     作者：朱辽原
     日期：2025.7.01
    */
    function openLayerDialogBtn(url, title, callback) {
        // 确保layer已加载
        if (!layer) {
            console.warn("Layer模块未加载，尝试获取全局layer对象");
            // 尝试从全局获取layer
            if (typeof parent.layui !== "undefined" && parent.layui.layer) {
                layer = parent.layui.layer;
            } else if (typeof layui !== "undefined" && layui.layer) {
                layer = layui.layer;
            } else if (typeof window.layer !== "undefined") {
                layer = window.layer;
            } else {
                console.warn("Layer模块仍未找到，使用备用方案打开页面");
                window.open(url, "_blank");
                return;
            }
        }

        try {
            layer.open({
                type: 2, // iframe层
                title: title || "AI健康问答",
                content: "/html/operation/" + url,
                area: ["100%", "100%"], // 覆盖屏幕100%
                maxmin: true, // 允许最大化最小化
                shadeClose: false, // 点击遮罩不关闭
                shade: 0.8, // 遮罩透明度
                skin: "layui-layer-rim", // 边框风格
                resize: true, // 允许拉伸
                scrollbar: false, // 屏蔽浏览器滚动条
                btn: ["关闭"], // 添加关闭按钮
                btnAlign: "c", // 按钮居中显示
                yes: function (index, layero) {
                    // 按钮点击回调
                    layer.close(index); // 关闭弹窗
                },
                success: function (layero, index) {
                    // 弹框打开成功后的回调
                    console.log("弹框打开成功:", title);
                    // 设置按钮为红色
                    let btn = layero.find(".layui-layer-btn0");
                    btn.css({
                        "background-color": "#FF5722",
                        "border-color": "#FF5722",
                    });

                    if (callback && typeof callback === "function") {
                        callback(layero, index);
                    }
                },
                end: function () {
                    // 弹框关闭后的回调
                    console.log("弹框已关闭:", title);
                },
            });
        } catch (error) {
            console.error("弹框打开失败:", error);
            // 备用方案：直接在新窗口打开
            window.open(url, "_blank");
        }
    }

    /*
     处理参数序列化，将对象类型参数转为 JSON 字符串
     参数说明：param - 参数对象
     返回值说明：URL 中的参数
    */
    function serializationParam(params = {}) {
        // 处理参数序列化，将对象类型参数转为 JSON 字符串
        return Object.entries(params).map(([key, value]) => {
            const encodedKey = encodeURIComponent(key);
            // 如果值是对象，将其序列化为 JSON 字符串
            const encodedValue = typeof value === 'object' && value !== null
                ? encodeURIComponent(JSON.stringify(value))
                : encodeURIComponent(value);
            return `${encodedKey}=${encodedValue}`;
        }).join('&');
    }

    /*
      功能：页面跳转
      参数说明：param - URL 中的参数对象
      返回值说明：无
     */
    function pageJump(url, params, title, callback) {
        try {
            // 合并默认参数和用户传入的参数
            params = {
                regional: localStorage.getItem('regional'),
                startDate: getFormattedDate(),
                endDate: getFormattedDate(),
                ...params
            };
            // 处理参数序列化，将对象类型参数转为 JSON 字符串
            const serializedParams = serializationParam(params)
            // 打开新页面
            openLayerDialogBtn(
                `${url}?${serializedParams}`,
                title || '标题',
                callback
            )
        } catch (error) {
            console.error('页面跳转失败:', error);
            // 备用方案：直接在新窗口打开
            window.open(url, '_blank');
        }
    }


    /*
     功能：表格中页面跳转
     参数说明：
       url - 目标URL
       params - URL参数字符串（JSON格式）
       title - 弹窗标题，默认为'便民服务牌数据详细'
       callback - 回调函数，默认为空函数
     返回值说明：无
    */
    function pageJumpToTable(url, params, title = '标题', callback) {
        try {
            // 解析并合并参数
            const parsedParams = params ? JSON.parse(decodeURIComponent(params)) : {};
            const finalParams = {
                regional: localStorage.getItem('regional'),
                startDate: getFormattedDate(),
                endDate: getFormattedDate(),
                ...parsedParams
            };
            // 处理参数序列化，将对象类型参数转为 JSON 字符串
            const serializedParams = serializationParam(finalParams)
            // 打开弹窗
            openLayerDialogBtn(`${url}?${serializedParams}`, title, callback || function () {
            });
        } catch (error) {
            console.error('页面跳转失败:', error);
            // 备用方案：直接在新窗口打开
            window.open(url, '_blank');
        }
    }

    /*
     功能：获取当前日期
     参数说明：无
     返回值说明：当前日期 (yyyy-MM-dd)
    */
    function getFormattedDate() {
        return formatDate()
    }

    /*
     功能：格式化日期格式
     参数说明：targetData - 需要转化的日期 (默认今天)  defaultYear - 当输入为 '月/日' 格式时使用的默认年份，默认为当前年份
     返回值说明：当前日期 (yyyy-MM-dd)
    */
    function formatDate(dateInput = new Date(), defaultYear = new Date().getFullYear()) {
        if (dateInput instanceof Date) {
            // 处理 Date 对象
            const year = dateInput.getFullYear();
            const month = String(dateInput.getMonth() + 1).padStart(2, '0');
            const day = String(dateInput.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        } else if (typeof dateInput === 'string') {
            // 处理日期字符串，支持 / 或 - 作为分隔符
            const separator = dateInput.includes('/') ? '/' : '-';
            const parts = dateInput.split(separator);

            if (parts.length === 2) {
                // 处理 '月/日' 或 '月-日' 格式
                const month = parseInt(parts[0], 10);
                const day = parseInt(parts[1], 10);
                const year = defaultYear;
                return `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
            } else if (parts.length === 3) {
                // 处理 '年/月/日' 或 '年-月-日' 格式
                const year = parseInt(parts[0], 10);
                const month = parseInt(parts[1], 10);
                const day = parseInt(parts[2], 10);
                return `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
            } else {
                throw new Error(`不支持的日期格式: ${dateInput}`);
            }
        } else {
            throw new Error(`不支持的参数类型: ${typeof dateInput}`);
        }
    }


    /*
      功能：根据周几生成日期
      参数说明：weekday - 周几 currentDate=new Date() - 参考日期，默认为今天
      返回值说明：指定周几的日期对象
     */
    function getDateByWeekday(weekday, currentDate = new Date()) {
        // 定义周几的映射关系
        const weekdayMap = {
            '周日': 0,
            '周一': 1,
            '周二': 2,
            '周三': 3,
            '周四': 4,
            '周五': 5,
            '周六': 6
        };

        // 处理字符串类型的周几
        if (typeof weekday === 'string') {
            weekday = weekdayMap[weekday.toLowerCase()];
            if (weekday === undefined) {
                throw new Error('Invalid weekday string');
            }
        }

        // 确保周几是 0-6 之间的数字
        if (typeof weekday !== 'number' || weekday < 0 || weekday > 6) {
            throw new Error('Weekday must be a number between 0 and 6');
        }

        const currentDay = currentDate.getDay(); // 当前是周几（0-6）
        const daysAhead = (weekday - currentDay + 7) % 7; // 计算需要加的天数

        // 创建新日期对象并设置为指定周几的日期
        const resultDate = new Date(currentDate);
        resultDate.setDate(currentDate.getDate() + daysAhead);

        return formatDate(resultDate);
    }

    /*
      功能：分布执行任务
      参数说明：tasks - 任务集合Array, sheduler - 任务调度器
      返回值说明：指定周几的日期对象
     */
    function performTask(tasks, sheduler) {
        let index = 0
        function _run() {
            sheduler((isGoOn) => {
                while (index < tasks.length && isGoOn()) {
                    tasks[index++]()
                }
                if (index < tasks.length) {
                    _run()
                }
            })
        }
        _run()
    }

    /*
    功能：
    参数说明：url - 请求路径, data - 请求参数, callback - 回调函数
    返回值说明：无
    */
    function smactionMockData(url, data, callback) {
        const startTime = Date.now();
        $.smaction(
            function (re, err) {
                if (re) {
                    if (re.data) {
                        callback(re.data)
                        const endTime = Date.now();
                        const executionTime = endTime - startTime;
                        console.log(`[${url}] --- 任务执行时间：${executionTime} 毫秒`);
                    } else {
                        callback(re)
                        const endTime = Date.now();
                        const executionTime = endTime - startTime;
                        console.log(`[${url}] --- 任务执行时间：${executionTime} 毫秒`);
                    }

                } else {
                    console.error('API请求失败:', err)
                }
            },
            data,
            {
                action: url,
                datastring: true
            }
        )
    }

    /*
    功能：
    参数说明：isLog - 是否打印日志  stack - 调用栈(获取行号和文件名) message - 打印信息(可传入多个)
    返回值说明：无
    */
    function logWithCondition(isLog, stack, ...message) {
        if (isLog) {
            const logObj = {
                fileName: '',
                lineNumber: 0
            }
            if (stack) {
                try {
                    const stackLines = stack.split('\n').filter(line => line.trim() !== '');
                    const targetLine = stackLines[1] || '';

                    const fileNameRegex = /([^/\\]+\.js)/;
                    const fileNameMatch = targetLine.match(fileNameRegex);
                    const fileName = fileNameMatch ? fileNameMatch[1] : null;
                    const fileNamePrefix = fileName ? fileName.replace('.js', '') : null;

                    const lineColumnRegex = /(\d+:\d+)(?=\s*\)?)|(\d+:\d+)$/;
                    const lineColumnMatch = targetLine.match(lineColumnRegex);
                    const lineColumn = lineColumnMatch ?
                        (lineColumnMatch[1] || lineColumnMatch[2]) : null;

                    if (fileNamePrefix && lineColumn) {
                        logObj.fileName = fileNamePrefix;
                        logObj.lineNumber = lineColumn;
                    }
                } catch (e) {
                    log(new Error().stack, 'stack 解析失败', e)
                    return
                }
            }
            console.log(`[${logObj.fileName} --- Line:${logObj.lineNumber}]`, ...message)

        }
    }
    function log(stack, ...message) {
        logWithCondition(true, stack, ...message)
    }

    return {
        openLayerDialogBtn, pageJump, pageJumpToTable, getFormattedDate, formatDate, getDateByWeekday, performTask, smactionMockData, log, logWithCondition
    }
})


