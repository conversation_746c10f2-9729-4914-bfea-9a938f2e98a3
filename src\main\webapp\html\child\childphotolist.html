﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>儿童健康档案</title>
    <link rel="stylesheet" type="text/css" href="../../css/reset.css"/>
    <link rel="stylesheet" type="text/css" href="../../layui-btkj/css/layui.css"/>
    <link rel="stylesheet" type="text/css" href="../../css/icon.css"/>
    <link rel="stylesheet" type="text/css" href="../../css/commonstyle-layui-btkj.css"/>
    <link rel="stylesheet" type="text/css" href="../../css/style.css"/>
    <link rel="stylesheet" href="../../css/medical.css"/>
    <style>
        html, body {
            background: #EAEFF3;
            overflow: hidden;
        }

        .layui-form-item {
            display: inline-block;
            vertical-align: top;
            margin: 5px 5px 0px 0;
        }

        .layui-form-label {
            line-height: 28px;
        }

        /*.layui-table-cell{height:auto;}*/
    </style>
</head>
<body>
<div class="marmain">
    <div class="content-medical">
        <div class="layui-form layui-comselect" style="padding:10px;">
            <div class="layui-form-item">
                <label class="layui-form-label">人员：</label>
                <div class="layui-input-inline">
                    <select id="upuserid" lay-filter="upuserid">

                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">录入状态：</label>
                <div class="layui-input-inline">
                    <select id="inputstate" lay-filter="inputstate">
                        <option value="">请选择</option>
                        <option value="1">已录入</option>
                        <option value="0">未录入</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">录入人数：</label>
                <div class="layui-input-inline">
                    <select id="inputucount" lay-filter="huk_state">
                        <option value="">请选择</option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <button class="layui-btn form-search" style="margin-left: 5px;vertical-align: top;" id="btnSearch">查询</button>
<!--                <button class="layui-btn form-search" style="margin-left: 5px;vertical-align: top;" id="btnadd">上传照片</button>-->
                <a class="layui-btn form-search" id="btnupload">上传照片</a>
            </div>
        </div>
    </div>
    <div id="content" style="width: 100%;">
        <div class="marmain-cen">
            <div class="content-medical" id="divtable">
                <table id="laytable" lay-filter="laytable"></table>
            </div>
        </div>
    </div>
</div>
<script data-main="../../js/child/childphotolist" src="../../sys/require.min.js"></script>
</body>
</html>
