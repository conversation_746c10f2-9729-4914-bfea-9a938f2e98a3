<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>AI健康问幼儿情况</title>
    <link rel="stylesheet" type="text/css" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" type="text/css" href="../../css/icon.css"/>
    <link rel="stylesheet" type="text/css" href="../../styles/tbstyles.css"/>
    <link href="../../plugin/flexigrid/css/tbflexigrid.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css"/>
    <script type="text/javascript">
        // 检查 top.objdata 是否存在，如果不存在则设置默认主题
        var curtheme = (top && top.objdata && top.objdata.curtheme) ? top.objdata.curtheme : 'backstage';
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + curtheme + '.css" type="text/css" media="screen"/>');
    </script>

    <style type="text/css">
        html, body {
            background: #EAEFF3;
            overflow: hidden;
        }
        .layui-form-item {
            display: inline-block;
            vertical-align: top;
            margin: 5px 5px 0px 0;
        }

        .layui-form-label {
            line-height: 28px;
        }

        /* 添加分页栏按钮样式 */
        .layui-table-page {
            text-align: right !important;
            position: relative !important;
        }

        /* 强制分页显示在右边 */
        .layui-table-page .layui-laypage {
            float: right !important;
            margin: 0 !important;
        }

        .layui-table-page .layui-laypage span,
        .layui-table-page .layui-laypage a,
        .layui-table-page .layui-laypage input,
        .layui-table-page .layui-laypage button {
            float: left !important;
        }

        .layui-table-page .layui-laypage .layui-laypage-count {
            float: right !important;
            margin-right: 10px !important;
        }

        .layui-table-page .layui-laypage .layui-laypage-limits {
            float: right !important;
            margin-right: 10px !important;
        }

        #btnclose {
            background-color: #FF5722 !important;
            color: #fff !important;
            position: absolute !important;
            left: 50% !important;
            transform: translateX(-50%) !important;
            top: 50% !important;
            margin-top: -14px !important;
            z-index: 1 !important;
            height: 28px !important;
            line-height: 28px !important;
            padding: 0 15px !important;
            font-size: 12px !important;
            border-radius: 2px !important;
        }
    </style>
</head>
<body>
<div class="marmain">
    <div class="content-medical">
        <div class="layui-form layui-comselect" style="padding:10px;">
            <!-- 开始日期 -->
            <div class="layui-form-item">
                <div class="layui-input-inline" style="width:120px; margin-right: 5px;">
                    <input id="datestart" type="text" autocomplete="off" placeholder="开始日期" class="layui-input">
                </div>
            </div>
            
            <!-- 至 -->
            <div class="layui-form-item">
                <div class="layui-form-mid">至</div>
            </div>
            
            <!-- 结束日期 -->
            <div class="layui-form-item">
                <div class="layui-input-inline" style="width:120px; margin-right: 20px;">
                    <input id="dateend" type="text" autocomplete="off" placeholder="结束日期" class="layui-input">
                </div>
            </div>
            
            <!-- 分类 -->
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 50px;">分类：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="category" lay-filter="category">
                        <option value="">请选择</option>
                        <option value="眼科">眼科</option>
                        <option value="口腔">口腔</option>
                        <option value="生长发育">生长发育</option>
                    </select>
                </div>
            </div>
            
            <!-- 预约号码 -->
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 80px;">预约号码：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="appointno" lay-filter="appointno">
                        <option value="">请选择</option>
                        <option value="1">有预约</option>
                        <option value="0">无预约</option>
                    </select>
                </div>
            </div>
            
            <!-- 幼儿性别 -->
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 80px;">幼儿性别：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="childsex" lay-filter="childsex">
                        <option value="">请选择</option>
                        <option value="男">男</option>
                        <option value="女">女</option>
                    </select>
                </div>
            </div>
            
            <!-- 体检年龄 -->
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 80px;">体检年龄：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="checkage" lay-filter="checkage">
                        <option value="">请选择</option>
                        <option value="3岁">3岁</option>
                        <option value="4岁">4岁</option>
                        <option value="5岁">5岁</option>
                        <option value="6岁">6岁</option>
                    </select>
                </div>
            </div>
            
            <!-- 幼儿园 -->
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 70px;">幼儿园：</label>
                <div class="layui-input-inline" style="width:120px; margin-right: 15px;">
                    <select id="kindergarten" lay-filter="kindergarten">
                        <option value="">请选择</option>
                    </select>
                </div>
            </div>
            
            <!-- 年级 -->
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 50px;">年级：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="grade" lay-filter="grade">
                        <option value="">请选择</option>
                        <option value="小班">小班</option>
                        <option value="中班">中班</option>
                        <option value="大班">大班</option>
                    </select>
                </div>
            </div>
            
            <!-- 班级 -->
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 50px;">班级：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="classname" lay-filter="classname">
                        <option value="">请选择</option>
                    </select>
                </div>
            </div>
            
            <!-- 区域分类 -->
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 80px;">区域分类：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="areatype" lay-filter="areatype">
                        <option value="">请选择</option>
                        <option value="城区">城区</option>
                        <option value="郊区">郊区</option>
                    </select>
                </div>
            </div>
            
            <!-- 孩子与家长关系 -->
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 120px;">孩子与家长关系：</label>
                <div class="layui-input-inline" style="width:120px; margin-right: 15px;">
                    <select id="relationship" lay-filter="relationship">
                        <option value="">请选择</option>
                        <option value="父子">父子</option>
                        <option value="母子">母子</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
            </div>
            
            <!-- 关键字输入框 -->
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 60px;">关键字：</label>
                <div class="layui-input-inline" style="width:200px; margin-right: 20px;">
                    <input id="txtkeyword" type="text" autocomplete="off" placeholder="请输入幼儿姓名或家长姓名" class="layui-input">
                </div>
            </div>
            
            <!-- 查询按钮 -->
            <div class="layui-form-item">
                <button class="layui-btn form-search" style="margin-left: 10px;" id="btnsearch">查询</button>
            </div>
        </div>
    </div>
    <div id="content" style="width: 100%;">
        <div class="marmain-cen">
            <div class="content-medical" id="divtable">
                <table id="laytable" lay-filter="laytable"></table>
            </div>
        </div>
    </div>
</div>

<script data-main="../../js/operation/w_aiChatChildSituation" src='../../sys/require.min.js'></script>
</body>
</html> 