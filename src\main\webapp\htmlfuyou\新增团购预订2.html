<!DOCTYPE html>
<html>
	    <head>
	    <title>新增团购预订</title>
	    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
	    <script type="text/javascript" src="../layui-btkj/layui.js"></script>
	    <script type="text/javascript" src="../sys/jquery.js"></script>
		<link rel="stylesheet" href="../css/reset.css">	
	    <link rel="stylesheet" href="../css/style.css"/>
	    <link rel="stylesheet" href="../css/icon.css"/>
		<link rel="stylesheet" href="../layui-btkj/css/layui.css">
	    <script type="text/javascript">
	        document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
	    </script>

			<style>
			body {
				background: #fff;
			}
			.incr-chat {
				width: 700px;
				margin: 0px auto;
				padding: 0 40px
			}
			.layui-input-block .layui-input {
				width: 483px;
			}
			</style>
	    </head>
<body>
      <section class="incr-chat" style="display: ;">
              <h3 class="noticeTitle bg1" style="margin-top:10px;"><span class="lanflag">新增团购预订</span></h3>
				  <div class="order-cen" > 
					   <p>企业名称: <font color="#999999">北京童话邦教育科技有限公司</font><span class="order-tc" style="margin-right: 100px; float: right">请选择</span></p>
                       <p>企业负责人／手机号: <font color="#999999"> 文海星／15300020001</font></p>
                   </div>
                  <div class="order-bot"> 
					  <p>选择套餐：<input type="text" name="" class="input-txt"><span class="order-tc">请选择</span></p>
					  <div style="margin-top:10px;">套餐单价：<span class="order-price">男 原价</span>¥120<span class="order-price">，</span> <span class="order-price">折后</span>
                           <div class="order-suspend dark-grey"><label>¥</label><input type="text" name="" class="input-pricetxt"><p>9.2折</p></div>
						  <span class="order-price">女 原价</span>¥150<span class="order-price">，</span> <span class="order-price">折后</span>
                           <div class="order-suspend dark-grey"><label>¥</label><input type="text" name="" class="input-pricetxt"><p>9.2折</p></div>
					  </div>
					  
					  <span class="order-list layui-form">
						按性别输入人数：
						<div class="layui-input-inline">
				            <input type="radio" name="needs" value="是" title="是"><div class="layui-unselect layui-form-radio"><i class="layui-anim layui-icon"></i><div>是</div></div>
				            <input type="radio" name="needs" value="否" title="否" checked=""><div class="layui-unselect layui-form-radio layui-form-radioed"><i class="layui-anim layui-icon"></i><div>否</div></div>
				        </div>
					</span>

			          <p>总人数：<input type="text" name="" class="input-peotxt">人</p>
					  <p class="order-alt">共计<label class="red-txt font30">¥696-¥720</label>之间</p>  
		       </div>
		       <div style="text-align: center;margin-top: 50px;">
				    <button type="" class="layui-btn" style="padding:0 25px; color:#fff; border-radius:5px; background:#4bb1ff; margin-right:20px">确定</button>
				    <button type="" class="layui-btn layui-btn-primary" style="border:1px solid #333333; padding:0 25px; color:#333333; border-radius:5px;">关闭</button>
		       </div>
        </section>
	
		<script>
		//Demo
		layui.use('form', function(){
		  var form = layui.form;
		  
		  //监听提交
		  form.on('submit(formDemo)', function(data){
		    layer.msg(JSON.stringify(data.field));
		    return false;
		  });
		});
		</script>
</body>
</html>
