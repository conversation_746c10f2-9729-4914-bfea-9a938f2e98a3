﻿body{-webkit-tap-highlight-color:rgba(0,0,0,0);font-size:16px;/* font-family:"microsoft yahei";*/ background: #fff; font-family:"冬青黑体简体中文 W3";}
.ane_content{ width:100%; height:100%;word-wrap:break-word;}
body{-webkit-tap-highlight-color:rgba(0,0,0,0);}
/* 字体颜色 */
.white{ color:#fff;}
.red,.check_update p.red,.check_update p.red a,.eatstuname.red{ color:#F00;}
.cl1{color:#8c8c8c;}
.cl2{color:#F79722;}
.cl3{color:#00c85e;}
.cl4{color:#E74C3C;}
.cl5{color:#7f7f7f;}
.cl6{color:#EA5414;}
.cl7{color:#009FE8;}
.cl8{color:#FF4C3C;}
.bg2{background:#a6a5aa;}
.bg3{background:#008CDC;}
.bg4{background:#FF4C3C;}
.bg5{background:#667279;}
.bg6{background:#00C85E;}
.bg7{background:#FF971D;}
.bg8{background:#98BA00;}
.bg9{background:#FFC700;}
.bg10{background:#FC015A;}
.blue{ color:#00cf91}
.green{ color:#20e0bb}
.pink{ color:#fa4cbb}
.clcolor{ color: #53cac3}
.whitebg{background:#fff;}
.blueclo,.campusdiv .blueclo{color: #27c2fc}
.gray-label{color: #999;font-size: 12px;}
.lightgreen{ color:#53cac4}
.orange{color:#ff805a}
.orange-yellow{color: #f7a502}
.leyellow{ color: #ff9000;}
.bold{ font-weight: bold}
.yellow-txt{ color: #fac134}
.gary-txt{color:#999999}
.emerald-txt{ color: #19bb72}
.gre-txt{ color:#76d571}
.redbor{color: #ff5555; border: 1px solid #ff5555; display: inline-block; line-height: 20px; border-radius: 25px; font-size: 10px; padding:0 8px; margin-left: 5px}
/* 通用样式 */
.bottom{width:100%; height:7px;}
.btn{font-size:16px; text-align:center; height:37px; line-height:37px; margin:15px 0; display:block;/* border-radius:5px;*/ color:#fff;box-shadow:0 0 1px #ccc,1px 1px 1px #ccc;}
.greenBtn{ background:#19bb72; cursor:pointer; }
.blueBtn{ background:#3590BF;}
.redBtn{ background:#ff4c3c;}
.whiteBtn{ background:#fff; color:#3371B8;}
.yellow-org{background:#ff9523}
.greyBtn{  background:#cccccc;}
.orgBtn{ background:#ffa922;}
.grayBtn{background:#B4B5B5;color:#969697; border:1px solid #999;}
.whiteBtn{background:none;color:#333333; border:1px solid #cccccc; cursor: pointer}
.darkgreenBtn{ background: #0fbb2b;}
.pinkBtn{ background:#fa4cbb}
.yellow-bg{background: #fbf8ec}
.emerald-bg{ background: #19bb72}
.lightblue-bg{ background: #27c2fc;}
.btn:active{opacity:0.8;}
.pd15{ padding:15px;}
.pd10{ padding:10px;}
.mg15{ margin:15px;}
.mgtop10{ margin-top:10px;}
.mgtop12{ margin-top:12px;}
.mgtop15{ margin-top:15px;}
.mgtop20{ margin-top:20px;}
.mgleft15{ margin:0px 15px 10px 15px; }
.mg10{ margin:10px;}
.mgtopnone{ margin-top:0px;}
.pdbtom15{ padding-bottom:15px;}
.pdtop10{ padding-top:10px;}
.bg1{background: #fff;}
.mgleft10{ margin-left:10px;}
.wxdown{background:#2C3E50;right:10px;position:absolute;top:40px;color:#fff; border-radius:3px; box-shadow:0 2px 3px rgba(0,0,0,.2);  padding:10px 15px; z-index:1;}
.wxdown li{ line-height:38px;position: relative;}
.wxdown:before{ content:""; border-style:solid; border-width:0 9px 10px; border-color:transparent transparent #2C3E50; position:absolute; top:-10px;right:15px;}
.fielddown{top:45px;}
.fielddown:before{right:9px;}
.green-gradient{background: #05cc93;align-items: center;justify-content: space-around;color: #ffffff;
	background: -webkit-linear-gradient(#15e3af, #00bf88); /* Safari 5.1 - 6.0 */
	background: -o-linear-gradient(#15e3af, #00bf88); /* Opera 11.1 - 12.0 */
	background: -moz-linear-gradient(#15e3af, #00bf88); /* Firefox 3.6 - 15 */
	background: linear-gradient(#15e3af, #00bf88); /* 标准的语法 */		}
.green-gradient01{background: #05cc93;}
.num{background: #f00;padding:1px 5px; color: #fff;font-size:12px;position: absolute;border-radius:100%;right:-4px;top:-4px; z-index: 9;}
.progresswrap{ background: linear-gradient(#6fbee5, #3a9ed8);width:50%; height:6px; margin:3px auto; border-radius:5px;}
.progress{height:100%; background:linear-gradient(-45deg, rgba(0, 0, 0, 0) 0px, rgba(0, 0, 0, 0) 25%, rgba(255, 255, 255, 0.2) 25%, rgba(255, 255, 255, 0.2) 50%, rgba(0, 0, 0, 0) 50%, rgba(0, 0, 0, 0) 75%, rgba(255, 255, 255, 0.2) 75%, rgba(255, 255, 255, 0.2)) repeat scroll 0 0 / 30px 30px rgba(0, 0, 0, 0); animation:1s linear 0s normal none infinite;-webkit-animation:1s linear 0s normal none infinite;}
@-webkit-keyframes loading {from{background-position:0 0;}to{background-position:30px 0;}}
@keyframes loading {from{background-position:0 0;}to{background-position:30px 0;}}
.out-container {
	position: absolute;
	top: 0px;
	bottom: 0;
	width: 100%;
	overflow: scroll;
	-webkit-overflow-scrolling: touch;
}
.weui-cell__ftp{
	font-size: 14px;
    color: #999999;
    padding-right: 10px;
}
.weui-flex_center{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;}

/*默认担单选按钮样式*/
.weui-cells_checkbox .weui-check:checked+.weui-icon-checked:before{font-size: 18px!important;}
.weui-cells_checkbox .weui-icon-checked:before{font-size: 20px!important;}
/*默认开关样式*/
.weui-switch-cp__box::before, .weui-switch::before {
    width: 45px!important;
	height: 25px!important;}
.weui-switch-cp__box:after, .weui-switch:after{height: 25px !important; width: 25px !important;}
.weui-switch, .weui-switch-cp__box{height: 27px!important; width:47px!important;}
/*取消before,after*/
.nobefore:before,.noafter:after{content: none;}
/*省略号*/
.ellipsis-clamp{overflow:hidden;text-overflow:ellipsis;display:-webkit-box; -webkit-box-orient:vertical;-webkit-line-clamp:2;}
/*没有数据的时候*/
.commonnodata{
	text-align: center;
	font-size: 14px;
	margin: 70px;
	color: #47494a;
	letter-spacing: 3px;
}
/*无数据*/
.no-infodiv{font-size: 12px;color: #999999;width: 100%;text-align: center;padding-top: 35%;}
/*标题*/
.def-title{font-size: 16px;color: #333333;border-bottom: 1px solid #eeeeee;padding: 12px 15px 12px 20px;background: #ffffff;position: relative;font-weight: bold;}
.def-title:after{content: "";width: 5px;height: 40%;background: #04c8ad;border-radius: 10px;position: absolute;left: 5px;top: 30%;}
.def-title.noafter:after{content: none;}
.orange-btn{display:table-cell;vertical-align:middle;background: #FAC132;border-radius: 20px;text-align: center;font-size: 12px;padding:5px 10px 4px 10px;color: #fff;}
.oran-txt{color: #FF6A38;}
.gree-txt{color: #58CAC2;}
.red-txt{color: #FF5B5B;}
.blue-txt{color: #399BF7;}
.grey-txt{color: #D0D0D0;}
/*输入框*/
.def-inptxt{border: none;outline: none;}
.def-inptxt::-webkit-input-placeholder{color: #cccccc;}
.defi-check input[type="checkbox"]{-webkit-appearance: none;width: 13px;height: 13px;border: 1px solid #ccc;border-radius: 1px;outline: none;}
.defi-check input[type="checkbox"]:checked{width: 13px;height: 13px;background: url(../images/check_bg.png)no-repeat 50% 50%;background-size: 13px 13px;}
.defi-check2 input[type="checkbox"]{-webkit-appearance: none;width: 15px;height: 15px;border: 1px solid #ccc;border-radius: 50%;outline: none;}
.defi-check2 input[type="checkbox"]:checked{border:none;width: 15px;height: 15px;background: url(../images/radio_bg.png)no-repeat 50% 50%;background-size: 15px 15px;}
.weui-cells_checkbox .weui-check:disabled + .weui-icon-checked:before{color: #E5E5E5 !important;}
/*取消cells上间距*/
.cells-top-nospace{margin: 0;}
.cells-top-nospace:before{content: none;}	        
/*iconfont图标*/
.icon-arrow-left:before{content: "\e647";font-size: 14px;margin-right:8px;height: 100%;display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;}
.icon-arrow-right:before{content: "\e883";font-size: 14px;margin-left:8px;height: 100%;display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;}
.icon-arrow-down:before{content: "\e610";font-size: 14px;margin-left:8px;height: 100%;display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;}
.icon-warn{position: relative;}
.icon-warn:before{content: "\e641";font-size: 16px;margin-left:8px;height: 100%;display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;color: #FF5858;margin-right: 5px;position: absolute;left: -28px;}
.icon-ask{position: relative;}
.icon-ask:before{content: "\e6b8";font-size: 16px;margin-left:8px;height: 100%;display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;color: #399BF7;margin-right: 5px;position: absolute;left: -28px;}
.icon-remind:before{content: "\e62f";font-size: 16px;height: 100%;align-items: center;justify-content: center;color: #A0ADC5;margin-right: 5px;}
/*单选框*/
.set-radio-style input[type="radio"]{-webkit-appearance: none;width: 14px;height: 14px;border: 1px solid #ccc;border-radius: 50%;outline: none;vertical-align: top;margin-top: 3px;}
.set-radio-style input[type="radio"]:checked{background: url(../images/sel-agree2.png) 50%;background-size:14px 14px;outline: none;border: 0;}
.set-radio-style input[type="radio"]{margin-right: 5px;}	
.set-radio-style .sel-radio{margin-left: 15px;}
.set-radio-style2 input[type="radio"]{-webkit-appearance: none;width: 14px;height: 14px;border: 1px solid #ccc;outline: none;}
.set-radio-style2 input[type="radio"]:checked{background: url(../images/sel-green.png) 50%;background-size:14px 14px;outline: none;border: 1px solid #53CAC3;}
.set-radio-style2 input[type="radio"]{margin-right: 5px;}	
.set-radio-style2 .sel-radio{margin-left: 15px;}
.set-radio-style3 input[type="radio"]{-webkit-appearance: none;position: relative;margin-right: 5px;width: 12px;height: 12px;background: #ffffff;border-radius: 50%;display: inline-block;border: 1px solid #d2d2d2;box-sizing: content-box;outline: none;}
.set-radio-style3 input[type="radio"]:checked{-webkit-appearance: none;border-radius: 50%;display: inline-block;border: 1px solid #03c7ad;box-sizing: content-box;}        
.set-radio-style3 input[type="radio"]:checked:before{content:"";display: inline-block;width: 6px;height: 6px;border-radius: 50%;background: #03c7ad;position: absolute;left: 3px;top: 3px;}
.def-radio input[type="radio"]{-webkit-appearance: none;width: 12px;height: 12px;background: #ffffff;border-radius: 50%;display: inline-block;border: 1px solid #d2d2d2;box-sizing: content-box;outline: none;}
.def-radio input[type="radio"]:checked{-webkit-appearance: none;width: 6px;height: 6px;background: #ffffff;border-radius: 50%;display: inline-block;border: 4px solid #00d1c9;box-sizing: content-box;}
.def-radio2 input[type="radio"]{-webkit-appearance: none;width: 25px;height: 25px;background: url(../images/radio_bg3.png);background-size: 100% 100%;border-radius: 50%;display: inline-block;outline: none;}
.def-radio2 input[type="radio"]:checked{-webkit-appearance: none;width: 25px;height: 25px;background: url(../images/radio_bg3_HL.png);background-size: 100% 100%;border-radius: 50%;display: inline-block;}
/*复选框*/
.set-checkbox-style input[type="checkbox"]{-webkit-appearance: none;width: 14px;height: 14px;border: 1px solid #ccc;outline: none;}
.set-checkbox-style input[type="checkbox"]:checked{background: url(../images/check_bg.png) 50%;background-size:14px 14px;outline: none;border: 1px solid #53CAC3;}
.set-checkbox-style input[type="checkbox"]{vertical-align: middle;margin-right: 7px;}
.set-checkbox-blue input[type="checkbox"]{-webkit-appearance: none;width: 14px;height: 14px;border: 1px solid #ccc;outline: none;}
.set-checkbox-blue input[type="checkbox"]:checked{background: url(../images/checkblue.png) 50%;background-size:14px 14px;outline: none;border: 1px solid #60AAFC;}
.set-checkbox-blue input[type="checkbox"]{vertical-align: middle;margin-right: 7px;}
.set-checkbox-green input[type="checkbox"]{-webkit-appearance: none;width: 15px;height: 15px;border: 1px solid #ccc;outline: none; border-radius: 50%;}
.set-checkbox-green input[type="checkbox"]:checked{background: url(../images/sel-green.png) 52%;background-size:14px 14px;outline: none;border: 1px solid #03c7ad;}
.set-checkbox-green input[type="checkbox"]{vertical-align: middle;margin-right: 7px;}
.set-checkbox-green2 input[type="checkbox"]{-webkit-appearance: none;width: 14px;height: 14px;border: 1px solid #ccc;outline: none;}
.set-checkbox-green2 input[type="checkbox"]:checked{background: url(../images/checkgreen.png) 50%;background-size:14px 14px;outline: none;border: 1px solid #03c7ad;}
.set-checkbox-green2 input[type="checkbox"]{vertical-align: middle;margin-right: 7px;}
.def-checkbox input[type="checkbox"]{-webkit-appearance: none;width: 12px;height: 12px;background: #ffffff;border-radius: 50%;display: inline-block;border: 1px solid #d2d2d2;box-sizing: content-box;outline: none;}
.def-checkbox input[type="checkbox"]:checked{-webkit-appearance: none;width: 6px;height: 6px;background: #ffffff;border-radius: 50%;display: inline-block;border: 4px solid #00d1c9;box-sizing: content-box;}
/*按钮*/
.cir-label-left{background: #FF5D5E;color: #fff;position: absolute;border-radius: 0 20px 20px 0;display:table-cell;vertical-align:middle;height:20px;line-height: 21px;font-size: 12px;padding:0 6px;}
.cir-label-right{background: #FF5D5E;color: #fff;position: absolute;padding: 5px 12px;border-radius:20px 0 0 20px;right: 0;top: 0;line-height: normal;font-size: 11px}
.weui-btn_opre{background:#FAC135;background: linear-gradient(left, #FF7C68 , #FF4360);margin: 0 15px;}
.weui-btn_red{background:#ff7878;background: linear-gradient(left, #ff7878 , #ff7878);margin: 0 15px;}
.weui-btn_opre-blue{background: linear-gradient(to top, #1c77ff , #739eff);margin: 0 15px;}
.weui-btn_empty{border: 1px solid #DCDCDD;color: #333 !important;}
.weui-btn_empty:after{border: 0 !important;}
.cir-left-mark{font-size: 12px;background: #FF3636;padding: 5px 20px 5px 10px;border-radius: 0 20px 20px 0;color: #ffffff;position: absolute;left: 0;z-index: 9;}
.redlabel{background: #FF3636;}
.grelabel{background: #00E05A;}
.graylabel{background: #D0D0D0;}
.weui-btn:after{content: none;}
.graybtn{background:#d5d5d5}  
.yellowbtn{background:#ffbd5f}
.bluebtn{background:#00d1c9}
.red-btn{background: #ff7352}
/*带边框的按钮*/
.borwhite-btn{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;padding: 0 10px;height: 25px;border: 1px solid #ffffff;border-radius: 4px;color: #ffffff;font-size: 12px;}
.border-btn{border-radius: 30px;font-size: 10px;padding: 2px 10px 1px 10px;margin-left: 5px;}
.bordergray{border: 1px solid #bfbfbf!important;color: #bfbfbf!important;}
.borderred{border: 1px solid #ff5a5a!important;color: #ff5a5a!important;}   
.bordergreen{border: 1px solid #00ce6b!important;color: #00ce6b!important;} 
.bordergreen2{border: 1px solid #00d1c9!important;color: #00d1c9!important;}
.borderblue{border: 1px solid #36baff!important;color: #36baff!important;}
.borderpink{border: 1px solid #e851ff!important;color: #e851ff!important;}
.borderorange{border: 1px solid #ff783c!important;color: #ff783c!important;}
.borderyellow{border: 1px solid #FFC842!important;color: #FFC842!important;}
.borderblue{background:#f7f7f7;border: 1px solid #00d1c9;color: #00d1c9}
/*底部按钮*/
.btn-bottom{background: #ffffff;padding: 15px 0;position: fixed;bottom: 0;width: 100%;box-shadow: 5px 5px 8px 5px #ececec;z-index: 9;}
/*切换*/
.commontab{display: flex;display: -webkit-flex;align-items:center;border: 1px solid #ffffff;border-radius: 4px;}
.commontab li{height: 100%;padding: 4px 10px 3px 10px;color: #ffffff;display: flex;display: -webkit-flex;align-items: center;justify-content:center;font-size: 12px;border-right: 1px solid #ffffff;}
.commontab li:last-child{border-right: none;}
.commontab li.current{background: #ffffff;color: #00d1c9;}	
.stat-tab{border: 1px solid #FAC132;display: flex;display: -webkit-flex;align-items: center;width: 188px;height: 27px;border-radius: 6px;}
.stat-tab li{font-size:15px;color:#7E7E7E;display: flex;display: -webkit-flex;align-items: center;justify-content:center;height: 28px;line-height: initial;position: relative;flex: 1;-webkit-flex: 1;}
.stat-tab li:after{content: "";border-right: 1px solid #FAC132;height: 27px;position: absolute;right: 0;top: 0;}
.stat-tab li:first-child{border-radius: 5px 0 0 5px;}
.stat-tab li:last-child{border-radius: 0 5px 5px 0;}
.stat-tab li:last-child:after{content: none;}
.stat-tab li.curr-stat{color: #fff;background: #FAC132;}
.tab-common2{font-size: 15px;color: #666666;border-bottom: 1px solid #eeeeee;height: 40px;background: #ffffff;align-items: center;justify-content: space-around;}
.tab-common2 li{position: relative;display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;height: 100%;}
.tab-common2 li.current{color: #19bb72;}
.tab-common2 li.current:after{content: "";position: absolute;bottom: 0;left: 0;width: 100%;border-bottom: 2px solid #19bb72;color: #19bb72;}
.tab-common2.tab-yellow li.current{color: #fac132;}
.tab-common2.tab-yellow li.current:after{border-bottom: 2px solid #fac132;color: #fac132;}
.tab-common3{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;background: #ffffff;height: 36px;line-height: 36px;font-size: 0;box-shadow: 0 4px 4px -3px #eeeeee;}
.tab-common3 li{display: -webkit-box;display: -webkit-flex;display: flex;-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;align-items: center;justify-content: center;font-size: 14px;color: #666666;position: relative;}
.tab-common3 li.current{color: #fcb303;}
.tab-common3 li.current:before{content: "";position: absolute;bottom: 0;left: 50%;margin-left: -16px;background: #fbc134;width: 32px;height: 3px;border-radius: 10px 10px 0 0;}
.tab-common3 li:after{content: "";width: 1px;height: 50%;position: absolute;right: 0;top: 25%;background: #eeeeee;}
.tab-common3 li:last-child:after{content: none;}
.tab-common4{padding: 0 10px;border-top: 1px solid #dddddd;border-bottom: 1px solid #dddddd;margin-top: 10px;align-items: center;justify-content: space-around;font-size: 14px;color: #666666;height: 39px;background: #ffffff;}
.tab-common4 li{position: relative;-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;text-align: center;}
.tab-common4 li.current a{background: #fac134;color: #ffffff;padding: 2px 6px;display: inline-block;min-width: 64px;border-radius: 20px;}
@media only screen and (max-width: 320px) {
	.tab-common4{padding: 0;}
}
.tab-common5{border: 1px solid #64b0f9;height: 38px;border-radius: 50px;}
.tab-common5 li{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;font-size: 14px;color: #666666;}
.tab-common5 li{border-right: 1px solid #64b0f9;}
.tab-common5 li.current{background: #64b0f9;height: 38px;color: #ffffff;}	
.tab-common5 li:first-child{border-radius: 50px 0 0 50px;}
.tab-common5 li:last-child{border-right: none;border-radius: 0 50px 50px 0;}
.tab-common6{display: -webkit-box;display: -webkit-flex;display: flex;justify-content: space-around;align-items:center;background: #ffffff;border-bottom: 1px solid #eeeeee;color: #666666;font-size: 13px;height: 35px;}
.tab-common6 li{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;width: 100%;height: 100%;position: relative;}
.tab-common6 li.current{color: #02c6ac;}
.tab-common6 li.current:after{content:"";height: 2px;width: 34px;position: absolute;bottom: 0;left: 50%;margin-left: -17px;background: #02c6ac;border-radius: 3px;}
.tab-common7{border: 1px solid #00cd91;margin: 10px 15px;border-radius: 3px;}
.tab-common7 li{position: relative;display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;height: 36px;font-size: 14px;color: #666666;}
.tab-common7 li.current{background: #00cd91;color: #ffffff;}
/*标签列表*/
.label-sign-list{flex-wrap: wrap;justify-content: flex-start;padding: 6px 15px 10px 11px;background: #ffffff;}
.label-sign-list span{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;background: #f4f4f4;margin: 4px 0 0 4px;color: #666666;font-size: 14px;height: 33px;border-radius: 4px;padding: 0 10px;}
.label-sign-list span.current{background: #fff8e8;color: #fac132;}
/*自定义信息列表*/
.default-infolist{border-top: 1px solid #e8e8e8;border-bottom: 1px solid #e8e8e8;background: #ffffff;}
.default-infolist .weui-cell{padding: 0 0 0 15px;}
.default-infolist .weui-cell:before{content: none;}
.default-infolist .weui-cell .infolist-txt{align-items: center;width: 100%;padding: 10px 15px 10px 0;border-bottom: 1px solid #e8e8e8;}
.default-infolist .weui-cell:last-child .infolist-txt{border-bottom: none;}	        
/*搜索*/
.search-box{border-bottom: 1px solid #eeeeee;background: #ffffff;height: 44px;padding: 0 15px;font-size: 14px;align-items: center;border-radius: 4px;}    	
.search-con{background: #f5f5f5;margin: 7px;border-radius: 4px;display: -webkit-box;display: -webkit-flex;display: flex;-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;align-items: center;}
.search-con input{-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;}    	
.search-con .icon_close2:before{font-size: 18px;color: #666666;}
.search-con .icon_search,.icon_close2{margin: 0 6px;}
/*自定义搜索框*/
.def-search .weui-search-bar{background-color: #ffffff;padding: 8px 15px;}	
.def-search .weui-search-bar__label{background: #F5F5F5;border-radius: 3px;}
.def-search .weui-search-bar__box .weui-search-bar__input{padding: 5px 0;}
.def-search .weui-search-bar__form:after{content: none;}
.def-search .weui-search-bar__form{border-radius: 3px;background: #F5F5F5;}
.def-search .weui-search-bar__cancel-btn{color: #19bb72;font-size: 14px;}
.def-search .weui-search-bar__box .weui-icon-search{top: 1px;}
.def-search .weui-search-bar:before{content: none;}
/*通用上传照片*/
.weui-uploader.uploader-define .weui-uploader__file{width: 62px;height: 62px;margin-right: 8px;margin-bottom: 8px;position: relative;}
.weui-uploader.uploader-define .weui-uploader__input-box{width: 62px;height: 62px;border: 1px dashed #d9d9d9;}
.weui-uploader.uploader-define .icon_close{position: absolute;right: 0;width: 18px;height: 18px;line-height: 18px;text-align: center;background: rgba(0,0,0,0.5);border-radius: 0 0 0 6px;}
.weui-uploader.uploader-define .icon_close:before{color: #ffffff;font-size: 14px;}
/*省略号*/
.text-elli{text-overflow: ellipsis;overflow: hidden;white-space: nowrap;}
.text-elliclamp{}
/*.text-elliclamp{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2; }*/
/*带视频图标的遮罩*/
.shadow-div{display:-webkit-box;display:-webkit-flex;display:flex;align-items: center;justify-content: center;background: rgba(0,0,0,0.6);position: absolute;top: 0;width: 100%;}
.shadow-div .icon_play{position: absolute;display:-webkit-box;display:-webkit-flex;display:flex;align-items: center;justify-content: center;}
/*复选框*/
.dif-select select{border: 1px solid #E9E9E9;height: 30px;line-height: 30px;}
/*三角标志*/
.triangle-mark{width: 0;height: 0;border:17px solid;border-color:#399BF7 transparent transparent #399BF7;position: absolute;top:0;left:0;}
.triangle-mark .triangle-txt{position:absolute;top:-13px;left:-19px;display:-webkit-box;display:-webkit-flex;display:flex;transform: rotate(-45deg);-ms-transform: rotate(-45deg);-webkit-transform: rotate(-45deg);font-size: 10px;color:#fff;width: 30px;}
/* 列表 */
.cul{border-top: 1px solid rgba(0,0,0,.1);border-bottom: 1px solid rgba(0,0,0,.1); }
.cul li{position: relative;margin-left:15px;border-bottom: 1px solid rgba(0,0,0,.1); line-height: 2.5;}
.cul li .icon35{top:13px;}
.cul li:last-child{border:0;}
.cul label,.cul dt{color:#7f7f7f;font-size: 14px;padding-right: 15px;}
.cul dd{overflow: hidden;}
/*自定义列表间距*/
.def-weui-cells{margin-top: 0;font-size: 15px;}	
.def-weui-cells .weui-cell{padding: 19px 15px;}
/*自定义间距*/
.def-space{background: #ffffff;padding: 0 15px;margin-top: 10px;} 
/*取消cell cells边框线*/     
.weui-cells.def-content:after,.weui-cells.def-content:before,.weui-cell.def-content:after,.weui-cell.def-content:before{content: none;}
/*cell cells边框线左右间距相等*/
.weui-cells.def-content-equal:after,.weui-cells.def-content-equal:before,.weui-cell.def-content-equal:after,.weui-cell.def-content-equal:before{right: 15px;}
/*遮罩层/弹框*/
.shadow-con{position: fixed;top: 0;bottom: 0;left: 0;right: 0;background: rgba(0,0,0,0.5);z-index: 10;display:-webkit-box;display:-webkit-flex;display:flex;align-items: center;justify-content: center;}
.shadow-box{position: relative;background: #ffffff;margin: 0 15px;border-radius: 6px;font-size: 13px;color: #333333;padding:20px 0;width: 100%;}
/*遮罩层底部/弹框*/
.shadow-botcon{position: fixed;top: 0;bottom: 0;left: 0;right: 0;z-index: 9;display:-webkit-box;display:-webkit-flex;display:flex;}
.shadow-botbox{position: relative;background: #ffffff;border-radius:10px;font-size: 13px;color: #333333;padding:0 15px 20px 15px;width: 100%;  box-shadow: inset 0 -1px 14px #dfdfdf; top:100px; }
/*评论/弹框*/
.shadow-com{position: fixed;top: 0;bottom: 0;left: 0;right: 0;z-index: 9;display:-webkit-box;display:-webkit-flex;display:flex;background: rgba(0,0,0,0.5);}
.shadow-combox{position:absolute;background: #f7f7f7;font-size: 13px;color: #333333;padding:0 15px 20px 15px;width: 100%;bottom:0px; }
/*含下划线的输入框*/
.line-bto-input{border-bottom: 1px solid #E0E0E0;height: 30px;line-height: 30px;display: inline-block;margin-bottom: 14px;}
.line-bto-input input{border: none;text-align: center;}
/* 表单的切换 */
.seltext{padding:8px 10px;height:44px; background:#a0b5cb;width:70%;}
ul.seltext li{border:2px solid #fff; border-radius:4px;height:26px;line-height:24px;text-align: center;}
.seltext span{border-right:2px solid #fff;display: inline-block;width:33.333%;font-size: 16px;}
.seltext li span:last-child{border:0;}
.seltext span.current{color:#157EFB;background: #fff;}
.selSex{width:50%;}
.selSex span{width:50%;color:#c0c0c0;}
/* head的切换 */
ul.selHead{height: 34px; border:2px solid #157EFB;border-radius: 4px;margin:15px;line-height:30px;}
.selHead li{float: left;text-align: center;border-right:2px solid #157EFB;color:#157EFB;}
.selHead span{ display:inline-block;}
.selHead li:last-child{border:0;}
.selHead li.current{color:#fff; background:#157EFB;}
.sel25 li{width:25%;}
.sel33 li{width:33.333%;}
.sel50 li{width:50%;}

.tab-common{background: #fff;height:40px;line-height: 40px;text-align: center;border-bottom:1px solid #F1F1F1;}
.tab-common span{width: 33.3%;display: inline-block;font-size: 14px;height:40px;}
.tab-common span.curr-tab{color: #FAC135; border-bottom: 3px solid #FAC135;}
/* head的切换2 */
ul.selHead2{height:20px; line-height:20px;margin:15px 0;width:100px;font-size:14px;}
.selHead2 li{float: left;text-align: center;color:#ccc;}
.selHead2 span{ display:inline-block;}
.selHead2 li:last-child{border:0;}
.selHead2 li.current{color:#000;}
/* 形态计算 */
ul.calHead,.calHead li{border-color:#00c85e; color: #00c85e;}
.calHead li.current{background: #00c85e;}
.askday ul.selHead{width:160px;margin:5px 0;}
.askday ul.selHead li{margin:0;height: 30px; line-height:30px;padding: 0;}

/* listblock */
.listblock{ border:1px solid #ccc; word-wrap:break-word;}
.listblock p{ margin-top:10px;}
.listblock h4{ line-height:1; padding-top:6px;}
.article{border:1px solid #ccc;border-radius:6px;padding:10px;}
.article h3{margin-bottom: 10px;}
.alltime{text-align:center;color:#999;display: block;margin:10px 0;font-size: 12px;}
.alltime span{margin-right: 5px;}
.success{color:#fff;margin:0 5%; text-align:center; position: fixed;top:100px;width:90%;padding:15px; border-radius: 4px; box-sizing:border-box; background:rgba(0,0,0,.7);z-index:9999;}
.transparent{background:rgba(0,0,0,.5); width:100%; height:100%; overflow:hidden; position:fixed; top:0; left:0; z-index:90;}

/* topback */
.topback{text-align: center;background:rgba(255, 255, 255, 0.7);height: 40px;line-height: 40px; border-bottom: 1px solid #ccc;}
.topleft,.topright{display: inline-block;height: 40px;color:#157EFB; position: absolute;}
.topleft{left: 10px;}
.topright{right: 10px;}

/*poplist*/
.selpopout{border-radius:6px; margin:10% auto; width: 80%;line-height:36px;}
.selpoptitle{border-bottom:1px solid #157efb;color:#157efb; text-align: center;}
.selpopul li{border-bottom:1px solid #ccc;padding:0 10px;}
.selpopul li:last-child{border: 0;}

/* 小图标 */
u,.icon{text-decoration:none;display:inline-block; vertical-align:middle;background: url("../images/icon.png") no-repeat scroll 0 0 rgba(0, 0, 0, 0);-webkit-background-size:300px auto; background-size:300px auto;}
.icon1{ background-position:-216px -66px; width:12px; height:20px; position:absolute; top:11px;left:15px;}
.icon4,.icon5,.icon6,.icon7,.icon8{ width:40px; height:40px; margin-top:6px;}
.icon4{ background-position:-199px -65px;}
.icon5{ background-position:0 -65px;}
.icon6{ background-position:-48px -65px;}
.icon7{ background-position:-99px -65px;}
.icon8{ background-position:-149px -65px;}
.icon9{ background-position:0 0;}
.icon10{ background-position:-66px 0;}
.icon11{ background-position:-133px 0;}
.icon12{ background-position:-199px 0;}
.icon13{ background-position:-266px -33px; width:32px; height:32px;}
.icon14,.icon15{ width:25px; height:22px;}
.icon14{ background-position:-266px -66px;}
.icon15{ background-position:-266px -100px;}
.icon16,.icon17{ width:40px; height:40px; position:absolute; top:0;}
.icon16:active,.icon17:active{ background:#78ccb5;}
.icon16{
	left: 10px;
}
.icon17{ right:10px;}
.icon16:before,.icon17:before{ content:""; display:block; position:absolute; width:10px; height:10px; border-top:2px solid #d4d4d4; border-right:2px solid #d4d4d4; top:13px; right:12px; -webkit-transform:rotate(45deg);}
.icon17:before{right:0; left:13px;}
.icon16:before{-webkit-transform:rotate(-135deg);}
.icon18{ background-position:-266px 0; /*right:53px; top:7px; */width:24px; height:24px; margin-top:-7px}
.icon19{ background-position:-283px -166px;}
.icon20{ background-position:-266px -166px;}
.icon19,.icon20{ margin-top:-4px; width:7px; height:12px;}
.icon21{ background-position:0 -116px;}
.icon22{ background-position:-66px -117px;}
.icon23{ background-position:-133px -117px;}
.icon24{ background-position:-199px -117px;}
.icon25{ background-position:-50px -180px;}
.icon26{ background-position:-83px -180px;}
.icon27{ background-position:-116px -180px;}
.icon28,.icon29,.icon32{width:15px; height:15px;margin-right: 2px;}
.icon29{ background-position:-266px -133px;}
.icon30{ background-position:-266px -183px;width:15px; height:24px; left: 12px; top:8px;}
.icon31{ background-position:0 -174px; width:26px; height:26px; vertical-align: top; margin-left: 10px;}
.icon32{ background-position:-283px -150px;}
.icon33{ background-position:-258px -262px; width:20px; height:20px; margin:30px auto 15px;}
.icon34{ background-position:-257px -242px; width:18px; height:17px;margin-right: 15px;}
.icon35{ background-position:-266px -149px; width:12px; height:16px;top:33px;right:15px;}
.icon36{ background-position:-66px -216px;}
.icon37{ background-position:-132px -216px;}
.icon38{ background-position:-265px -517px; left:10px; top:7px; width:28px; height:28px;}
.icon39,.icon40{width:17px; height:16px;margin-right:8px;}
.icon39{ background-position:-274px -242px;}
.icon40{ background-position:-283px -217px;}
.icon41{ background-position:-200px -242px;width:24px; height:24px;top:-7px;right:-7px;}
.icon42{ background-position:0 -233px;width:26px; height:26px;margin-right:9px;}
.icon43,.icon44,.icon45{width:24px; height:18px;margin-right:3px;}
.icon43{ background-position:-167px -275px;}
.icon44{ background-position:-199px -281px;}
.icon45{ background-position:-228px -282px;}
/*.icon46{ background-position:-255px -217px;}*/
.icon47{ background-position:0 -206px;width:25px; height:26px;}
.icon48{ background-position:-233px -263px;width:18px; height:18px;position: absolute;left: -22px;top:10px;}
.icon49{ background-position:-279px -263px;width:18px; height:18px;margin-right: 8px;}
.icon50{ background-position:-233px -184px;}
.icon51{ background-position:-183px -184px;}
.icon52{ background-position:-200px -183px;}
.icon28{ background-position:-282px -132px;}
.icon53{ background-position:-216px -184px;}
.icon54{ background-position:-200px -267px;}
/* field down */
.icon55{background-position:-192px -200px;}
.icon56{background-position:-217px -200px;}
.icon57{background-position:-242px -200px;}
.icon58{background-position:0 -262px;}/*xingxing*/
.icon59{background-position:-33px -217px;}/*ren*/
.icon60{background-position:-283px -183px;}/*liuyan*/
.icon65{background-position:-190px -217px;}
.icon66{background-position:-216px -217px;}
.icon67{background-position:-240px -217px;}
.icon68{background-position:-18px -261px;}
.icon69{background-position:-33px -233px;}
.icon70{background-position:-40px -259px;}
.icon71{background-position:-258px -200px;width:15px;height:15px; right:15px;top:19px;}
.icon72{background-position:-227px -241px;width:22px;height:22px; right:2px;bottom:2px;}
.icon73{background-position:0 -283px;width:15px;height:12px; top:-6px;left:-7px;}
.icon74{background-position:-16px -283px;width:16px;height:16px; right:-9px;top:-6px;}
.icon75{background-position:-68px -277px; left:-35px;}
.icon76{background-position:-92px -277px; right:15px;}
.icon75,.icon76{width:21px;height:21px; position: absolute;top:10px;}
.icon77{background-position:-116px -276px;width:16px;height:20px; right:15px;top:10px;}
.icon78{background-position:0 -300px;}
.icon79{background-position:-66px -300px;}
.icon80{background-position:-133px -300px;}
.icon81{background-position:-200px -300px;}
.icon82{background-position:0 -365px;}
.icon83{background-position:-66px -365px;}
.icon84{background-position:-132px -273px; left:10px; top:6px; width:27px; height:27px;}
/*.icon85{background-position:-267px -359px;width:30px;height:20px;}
*/
.icon86{background-position:-133px -365px;}
.icon87{background-position:-200px -365px;}
.icon88{background-position:0 -431px;}
.icon89{background-position:-66px -431px;}
.icon90{background-position:-133px -431px;}
.icon91{background-position:-200px -431px;}
.icon92{background-position:2px -500px;}
.icon93{background-position:-65px -500px;}
.icon94{background-position:-131px -500px;}
.icon95{background-position:-198px -500px;}
/* new */
.picon1{background-position:3px -565px;}
.picon2{background-position:-32px -565px;}
.picon3{background-position:-73px -564px;}
.picon4{background-position:-118px -564px;}
.current .picon1,.picon1:active{background-position:3px -598px;}
.current .picon2,.picon2:active{background-position:-32px -598px;}
.current .picon3,.picon3:active{background-position:-73px -597px;}
.current .picon4,.picon4:active{background-position:-118px -597px;}
.picon5{background-position:-150px -566px;}
.picon6{background-position:-200px -566px;}
.picon7{background-position:-250px -566px;}
.picon8{background-position:-150px -616px;}
.picon9{background-position:-200px -616px;}
.picon10{background-position:-258px -286px;}/*knowledge*/
.picon11{background-position:-258px -322px;}
.picon12{background-position:-258px -358px;}
.picon13{background-position:-258px -393px;}
.picon14{background-position:-258px -429px;}
.picon15{background-position:-258px -465px;}
.current .picon10{background-position:-0 -638px;}
.current .picon11{background-position:-43px -638px;}
.current .picon12{background-position:-85px -638px;}
.current .picon13{background-position:0 -674px;}
.current .picon14{background-position:-43px -674px;}
.current .picon15{background-position:-85px -674px;}/*knowledge*/
.picon16{background-position:-277px -619px; width:17px;height:22px;}
.picon17{background-position:-256px -618px; width:16px;height:15px;}
.picon18{background-position:-253px -638px;}
.picon19{background-position:-267px -544px;}
.picon18,.picon19{width:26px;height:22px; margin:0 6px;}
.picon20,.picon21,.picon22,.picon23{ width:42px;height:44px;margin:10px 0;}
.picon20{background-position:-130px -665px;}
.picon21{background-position:-169px -665px;margin-left:30px;}
.picon22{background-position:-210px -665px;}
.picon23{background-position:-256px -665px;margin-left:30px;}
.picon24{background-position:0 -711px; width:18px;height:18px;margin-left:3px;}
.picon25{background-position:-20px -711px; width:25px;height:20px; right:30px;top:3px;}
.picon26{background-position:-92px -277px;width:21px;height:21px; right:0; top:0;}
.picon27,.picon28,.picon29{width:18px;height:18px;}
.picon27{background-position:-46px -710px;width:18px;height:18px;}
.picon28{background-position:-66px -710px;}
.picon29{background-position:-85px -710px;left:0; bottom:2px;}/*xiaoren*/
.picon30{background-position:-105px -710px;}
.picon31{background-position:-141px -710px;}
.picon32{background-position:-176px -710px;}
.picon33{background-position:-212px -710px;}
.current .picon30,.picon30:active{background-position:-105px -738px;}
.current .picon31,.picon31:active{background-position:-141px -738px;}
.current .picon32,.picon32:active{background-position:-176px -738px;}
.current .picon33,.picon33:active{background-position:-212px -738px;}
.picon34,.picon35{width:32px;height:30px;top:17px;}
.picon34{background-position:-254px -707px;right:15px;}
.picon35{background-position:-254px -736px;right:60px;}
.picon36,.picon37,.picon38{width: 26px;height: 26px;margin-right: 3px;}
.picon36{background-position:0 -731px;}
.picon37{background-position:-28px -731px;}
.picon38{background-position:-55px -731px;}

.picon39{background-position:0 -766px;}
.picon40{background-position:-32px -766px;}
.picon41{background-position:-66px -766px;}
.picon42{background-position:-100px -766px;}
.picon43{background-position:-166px -766px;width:15px;height:20px; margin-right:8px;}
.picon44{background-position:-283px -200px;width:17px;height:16px; float: right; margin:13px 15px;cursor: pointer;}
.picon45{background-position:-266px -150px;width:10px;height:15px; float: right; margin-top:13px;}
.picon46{background-position:-183px -766px; width:26px;height:25px;right: 15px; top: 8px;}/*erweima*/
.picon47{background-position:-215px -765px; width:24px;height:20px;margin-top:10px;}/*camera*/

.ane_contentcenter{position:absolute;top:0;bottom:52px;width:100%; overflow-y:auto;overflow-x:hidden;}

/* 框架底部 */
.ane_footer{background:#fbfbfb;border-top: 1px solid rgba(0,0,0,.2); color: #7f7f7f;font-size:12px;position:absolute;bottom:0; left:0; width:100%;  height:52px; text-align:center; overflow:hidden; z-index:100;}
.ane_footer a{display: block;}
.ane_footer a:link,.ane_footer a:visited{color:rgba(0,0,0,0.5);}
.ane_footer ul{display:table; table-layout:fixed; width:100%; height: 100%;}
.ane_footer li{display: table-cell;}
.ane_footer u{ width:38px; height:32px; display:block; margin:0 auto;position:relative;}
.ane_footer u.picon4{ width: 30px;}
.ane_footer .current a{color:#157efb; margin:0;}
.ane_footer img{width:50px; height:50px; vertical-align: bottom;}

/* 框架底部 更多 */
.show_content { z-index:100;}
.show_content u{ width:50px; height:50px;}
.show_content h6{ width:50px; height:16px; color:#fff;}
.show_content a{
	width:50px;
	height:66px;
	position:fixed;
	bottom:-6px;
	left:50%;
	margin-left: -25px;
	z-index:99;
	text-align: center;
	-webkit-transition: -webkit-transform 200ms;
}
.bclose a{bottom: -17px;}
.bclose h6{display: none;}
.bopen h6{display: block;}
.bopen a:nth-child(1){
	-webkit-transform: translate(-100px, -60px) rotate(720deg);
}
.bclose a:nth-child(1){
	-webkit-transform: translate(0, 0) rotate(0deg);
}
.bopen a:nth-child(2){
	-webkit-transform: translate(-60px, -100px) rotate(720deg);
}
.bclose a:nth-child(2){
	-webkit-transform: translate(0, 0) rotate(0deg);
}
.bopen a:nth-child(3){
	-webkit-transform: translate(0, -120px) rotate(720deg);
}
.bclose a:nth-child(3){
	-webkit-transform: translate(0, 0) rotate(0deg);
}
.bopen a:nth-child(4){
	-webkit-transform: translate(60px, -100px) rotate(720deg);
}
.bclose a:nth-child(4){
	-webkit-transform: translate(0, 0) rotate(0deg);
}
.bopen a:nth-child(5){
	-webkit-transform: translate(100px, -60px) rotate(720deg);
}
.bclose a:nth-child(5){
	-webkit-transform: translate(0, 0) rotate(0deg);
}
.bopen img{
	-webkit-transform: rotate(135deg);
	-webkit-transition: -webkit-transform 200ms;
}
.bclose img{
	-webkit-transform: rotate(0deg);
	-webkit-transition: -webkit-transform 200ms;
}

/* 体质报告 */
.bodyName,.bodyTest{ height:52px; line-height:52px; background:#f79722; z-index:2;}
.bodyName{ position:fixed; width:100%; top:0;}
.bodyName img,.bodyTest img{width:40px; height:40px; border:1px solid #fff; vertical-align:middle; margin:-3px 8px 0 15px; display:inline-block;}
.bodyName span{ font-weight:normal;margin-right:15px;}
.bodyMsg{ padding-bottom:11px; margin-top:52px;}
.bodyMsg label,.bodyMsg span{ display:inline-block; width:50%; line-height:32px;-webkit-box-sizing:border-box;box-sizing:border-box;}
.bodyMsg span{ background:#FEF7EF; padding-left:10px;}
.bodyMsg label{ text-align:right; padding-right:10px;}
.bodyMsg li:last-child span{ background:#f79722; color:#fff;}
.bodyList{padding:8px 15px;}
.bodyList ul{ margin-bottom:20px;}
.bodyList li{ line-height:30px;}
.bodyList .line{border-bottom:1px solid #ccc;}
.bodyList h6{ border-bottom:1px solid #00AEFF; color:#00AEFF; line-height:2;}
.bodyList label{display:inline-block; color:#7f7f7f;margin-left: 8px;}
.bodyList span{ display:inline-block; float:right;}
.bodyList i{ margin-left:17px;}
.bodyTest img{ margin:0; left:10px; top:5px;}
.bodyTest p{ margin-left:65px;}
.bodyTest span{right:10px;}
.divbabys{position:fixed;width:100%;top:50px;}

/* 体检报告 */
.bodytest ul:nth-of-type(1) li{ position:relative;}
.bodytest ul:nth-of-type(1) span{padding-right:65px;}
.bodytest ul:nth-of-type(1) i{ position:absolute; right:0;}

.login input:first-child,.login{ border-bottom:1px solid rgba(0,0,0,.2);}
/* bind2 */
.login_content .bodyName span{ font-weight:bold;}
.login_content h6,.bind h6{ color:rgba(0,0,0,.3); padding:0 15px; float:left; line-height:28px;}
.bind p{ line-height:1; padding-top:6px;}
.bind .bodyName img{ margin-top:5px;}
.help{ color:#fff; background:#21292D; border-radius:5px; box-shadow:0 2px 3px rgba(0,0,0,.2); width:150px; height:44px; line-height:44px; left:50%; margin-left:-75px; text-align:center; top:-46px; z-index:11;}
.help:after{content:""; display:inline-block; position:absolute; border-style:solid; border-width:10px 10px 0 10px; border-color:#21292D transparent transparent transparent; left:68px; bottom:-9px;}
/* bind3 */
.login_content li.pa{ top:15px; left:15px;}
.login_content li.pa span{width:42px; height:42px;padding:0;}
.bind .icon72{right:0;}
.classlist{text-align:center;width:100%;position:fixed;bottom:0; max-height: 300px; overflow:auto;}
.classlist span{width:90px; display:inline-block;margin:6px 0;}
.shBtn .btn{width:49%; display: inline-block;}
.shBtn .btn:last-child{margin-left: 2%;}

/* 园所介绍 */
.yeylist{ height:52px;background:#f79722;}
.yeylist li{float:left; width:20%; text-align:center;}
.yeyContent p{ line-height:24px;}
.yeylist .current u{ opacity:0.5;}


/* teacher */
.teacher img{width:50px; height:50px; margin-right:8px;}
.teacher h5{line-height: 2;}
.teacher{border-bottom: 1px solid #ccc; color:#000; min-height: 80px;}

/* 园所公告 */
.announce img{margin:10px 0;}
.announce h6{ margin-top:6px;}
.announce a.cl1{ margin-bottom:5px;}
.add{width:26px; height:26px;position:fixed; top:10px; right:10px; background-position:-149px -183px; z-index:500;}

/* 食谱 */
.re_table{ width:100%; margin-top:40px;}
.re_table tr{height:44px;}
.re_table .type{ width:50px;  text-align:center}
.re_table .type img{ width:47px; height:47px}
.re_table .content{border-bottom:1px solid rgba(0,0,0,0.2); padding:10px 0;width:85%;}
.re_table tr:last-child .content,.re_table tr:last-child td:last-child{border:0;}
/*.re_table tr:nth-of-type(2n+1) li:before{background:#23a8e0;}
.re_table tr:nth-of-type(2n) li:before{background:#E74C3C; }*/
/*.re_table tr:nth-of-type(2n+1) li:before,.re_table tr:nth-of-type(2n) li:before{ content:""; display:inline-block; width:5px; height:5px;margin:3px 5px; }
*/
li.lifood:before{background:#23a8e0; content:""; display:inline-block; width:5px; height:5px;margin:3px 5px;}
li.lifoodred:before{background:#FF441E; content:""; display:inline-block; width:5px; height:5px;margin:3px 5px;}
li.lifoodpink:before{background:#fa5e96; content:""; display:inline-block; width:5px; height:5px;margin:3px 5px;}
li.lifoodgree:before{background:#60CF19; content:""; display:inline-block; width:5px; height:5px;margin:3px 5px;}

.re_table li{ line-height:30px; font-size:13px;}
.re_table li span{ font-size:13px; text-align:left; color:#333333;float: right }
.re_table  span.yysl{ line-height:30px; display:block; font-size:13px;}
.re_table td{ vertical-align:middle;}
.re_table td:last-child{border-bottom:1px solid rgba(0,0,0,0.2); padding:0 12px 0 12px; margin-left:5px;}
.recipeList{ padding-top:10px;}
.recipeList span{ display:inline-block;}
.recipeList li span:first-child{ color:#7f7f7f;}
.recipeList li span:last-child{ float:right;}
.recipeList li{ border-bottom:1px solid #ccc; line-height:33px; padding:0 13px 0 20px;}
.recipeList li:first-child{ border-bottom-color:#00AEFF;font-size:14px; padding:0 15px;}
.recipeList li:first-child span{color:#00AEFF;}
.h5none{color:#7F7F7F; text-align:center; top:150px;width:100%;}
.pageloading{position:fixed;top:200px;width:100%;height:100%;text-align:center;z-index:2;}
.reimglist{ margin:10px;}
.reimglist li{ float:left; width:30%; position:relative; margin-right:4%; margin-bottom:10px;}
.reimglist li:nth-of-type(3n){ margin-right:0;}
.reimglist img{ width:100%;display:block;}
.reimglist p{ font-size:12px; color:#fff; background:rgba(0,0,0,.5); width:100%; height:24px; line-height:24px; overflow:hidden; position:absolute; bottom:0; padding-left:10px;}
.trcanwz{ display:block; text-align:center;}
/* 食谱日历  */
.recipe h3,.datehead{text-align:center; height:40px; line-height:42px;background:#f3f3f7; z-index:9;color: #686868;}
.calendar{background:#f3f3f7;text-align: center;line-height: 2.1em;position:fixed;top:0;width:100%; z-index:10;color: #686868; border-bottom: 1px solid #dddddd;}
.calendar a{color: #686868;}
.calendar{width:100%;border-collapse:collapse; font-family:"arial"; font-size:12px;}
.calendar .tdzhoumo{color:#333333; }
.calendar .onData{  border-radius:2px;background:url(../images/shipubg.png) no-repeat center center;/* width:42px; */height:28px;/* border-radius:10px; */cursor:pointer;background-size:60% 100%; }
.calendar .onSelect{ background-color: #a1daa8;color: #fff;font-weight: bold;}
.calendar .onToday{background:url(../images/todaypic.png) no-repeat  center center; color:#ff632d; background-size:60% 100%; margin-bottom:5px;}

.onLeave{text-align:right;vertical-align: text-bottom;font-size: 12px;color: red;}
.idCalhead{height:40px; line-height:40px; font-size:20px;}
.idCalhead .icon16{left:20px;}
.idCalhead .icon17{right:20px;}
.calendar tr.font12{ background:#fff; line-height:40px; border-bottom: 1px solid #dddddd; color:#333333; font-size:13px}

/* 育儿课堂 */
.feedwrap li{border-bottom: 1px solid #D8D8D8;padding-left:90px;position: relative;height:80px; line-height:80px;}
.feedwrap ul li:last-child{border:0;}
.feedwrap u:nth-of-type(1){left: 15px;top:12px;}
.feedwrap a{color:#D94C3C;margin-right:15px;}
.feedwrap li:last-child h4{line-height:60px;}
.feedwrap p{line-height:1; margin-top:-14px;}

/* linkus */
.location p{position:relative; margin:0 0 25px 35px;min-height: 24px;}
.location u{width:28px; height:30px; position:absolute; left:-33px; top:-5px;}
.maplink{text-decoration: underline; font-style:italic;}
.divbox{border:0;}

/* 使用帮助 */
.helplist a{display: block;border-bottom: 1px solid #ccc; padding-left: 15px;line-height: 40px;}
.helpdetail{padding:0 15px;}
.helpdetail h4{color:#6083A8;border-bottom: 1px solid rgba(0,0,0,.1);padding:8px 0;}
.helpdetail p{line-height: 24px; margin-top:3px;}
.helpWrap u{-webkit-transform:rotate(90deg);transform:rotate(90deg);}
.helpWrap li:last-child u{-webkit-transform:rotate(0deg);transform:rotate(0deg);}
.helpWrap li.up u{-webkit-transform:rotate(-90deg);transform:rotate(-90deg);}

/* 幼儿请假 */
.askday li{border-bottom: 1px solid rgba(0,0,0,.1);height:44px; line-height: 44px; overflow: hidden;margin-left:15px;padding-right: 15px;}
.askday .askname{ float:left; width:70px; color:#7F7F7F;font-size: 14px;}
.askday textarea{border:0;}
.asklink{ text-decoration:underline; color:#00AEFF;text-align:center;margin-top: 30px;}
.qjhead{line-height:40px; text-align:center; background:#8cdcc6;}

/* 请假详细页 */
.askdetail .atTime{color:rgba(0,0,0,0.5); text-align:right; padding-right:10px;}
.askdetail li{ border-bottom:1px solid rgba(0,0,0,.1);padding:10px 10px 10px 0; margin-left:25px;}
.askdetail li:last-child{border:none;}
.askdetail label{ font-size:14px; color:#7f7f7f; float:left; width:60px;}
.askdetail span{ font-size:16px; display:table-cell; vertical-align:middle; padding-left:20px;}
/* new */
.asktoday{text-align: center;border:1px solid #ccc;border-radius: 6px;}
.asktoday table{width:100%;line-height:42px; border-collapse: collapse;border-radius: 6px;}
.asktoday table td{border-bottom:1px solid #ccc;}
.asktoday table tr:first-child td{border-bottom: 1px solid #00AEFF; font-size: 16px; color:#00AEFF;}
.askHead{text-align: left;background: #fff;padding:10px 15px;font-size: 14px; border-bottom: 1px solid #ccc;}
.asktoday td.cl6{text-decoration: underline;}
.headlist{border-top:1px solid #ccc;border-bottom:1px solid #ccc; margin-top:15px;padding-bottom:15px;}
.headlist a.imgblock,.headlist li{float: left;text-align: center;font-size: 12px; color:#7F7F7F;width:33%;margin-top:15px;}
.headlist a.pname{text-align: center;font-size: 12px; color:#7F7F7F;padding:1px;}
.headlist img{display:block;margin:0 auto;}

/* 请假原因 */
.askreason input{margin-left: 10px;}
.reasonWrap{margin:15px; border-radius: 6px; background: #fff;border:1px solid #ccc; padding:0 10px;}
.reasonWrap h4{color:#00AEFF; border-bottom:1px solid #00AEFF;text-align:center;border-radius:6px 6px 0 0; line-height: 42px;}
.reasonWrap dl{padding:10px 0;border-bottom:1px solid #ccc;}
.reasonWrap dl:last-child{border:0;}
.reasonWrap dt{float: left; color:#7F7F7F;font-size: 14px;}
.reasonWrap dd{margin-left:60px; overflow:hidden;}

/* 在线报名详细页 */
.joindetail .askname{width:100px;}

/* joinus */
.history{ text-decoration:underline; color:#7f7f7f; margin-left:10px;}
.joinus .askname{width:100px; color:#7f7f7f;}
.joinus input{font-size: 16px;height:100%;line-height:100%;}

/* comment */
.commentAll{background: #e9ebed;padding:10px;border-radius: 6px; margin:10px;}
.commentAll:before{content:""; border-style:solid; border-width:0 9px 10px; border-color:transparent transparent #e9ebed; position:absolute; top:-10px;left:15px;}
.comment{ margin-top:10px;}
.comlist span{ color:#6083A8;}
.comment p{border-top:1px solid rgba(0,0,0,.15); line-height:2.5; text-align:right;}
.comlist{font-size:14px;margin:0 0 5px 25px;}
.comlist img{vertical-align: middle;}
.commentAll .icon29{top:5px;}
.divlike .icon28{top:6px;}
.likelist{margin-left:25px;}
.likelist a{color:#6083A8;font-size:12px;}

/* 新的公告 */
.newcontent article{margin:10px 0;}
.voice{text-align: center; margin-top: 10px;}
.voice a{display: inline-block;background: #53B5DF; border-radius: 5px; width:100px; height: 34px;}
.sendMain{color:#00AEFF;font-size: 18px; text-decoration: underline; font-style: italic; background: #4d5357; text-align: center;}

/* 时间倒计时 */
.countdown{line-height: 42px;padding:0 10px;border-bottom:1px solid rgba(0,0,0,.2);}
.timecontent .btn{width:49%;float: left;margin:0;}
.timecontent p .btn:last-child{margin-left:2%;}
.chatfoot{position: fixed;bottom: 0;left: 0;background: #f7f7f7;z-index: 99;width: 100%;border-top:1px solid rgba(0,0,0,.3);padding:5px 10px;}
.chatRight{top:50%; margin-top:-14px;right:0;}
.sendBtn{width:37px; text-align: center; line-height:28px;border-radius: 5px;display: inline-block;background:#00AEFF;}
.chatTxt{margin:0 80px 0 0;}
.chatTxt textarea{width: 100%;border-radius:6px;border:1px solid rgba(0,0,0,.1);padding:5px; box-sizing:border-box; height: 30px;vertical-align: middle;}
.chatRight .icon47{margin-right: 7px;}
/* joinustj */
.wxtitle{text-align: center; line-height:1;margin-bottom: 15px;}
.divseldate input{margin-left:15px;line-height:28px;font-size: 16px;}
.divseldate div{height: 44px; line-height: 44px; padding-left:15px;font-size: 14px; border-bottom: 1px solid #ccc;}
.divtjinfo{width:100%;border-top: 1px solid #ccc; border-collapse:collapse;line-height: 44px;font-size: 14px;}
.divtjinfo td{width:50%;border-bottom: 1px solid #ccc;padding:0 15px;}
.divtjinfo td:last-child{text-align: right;color:#000;font-size: 16px;}
.bmxq{border:1px solid #ccc; border-radius: 6px;}
.bmxq table{width: 100%;border-radius: 6px; text-align: center;border-collapse:collapse;line-height: 44px;}
.bmxq td{width: 25%;border-bottom: 1px solid #ccc;}
.bmxq table tr:first-child td{border-bottom: 1px solid #00AEFF;font-size: 16px;}

/* joinuslist */
.bmlist{text-align:center;border-top:1px solid #ccc; border-bottom: 1px solid #ccc;padding: 15px 0px;}
.bmlist a{color: #00AEFF;text-decoration: underline; font-style: italic;height:35px;}
.joinNotice{padding:15px;color:#7f7e78;}

/* new add */
.adddown{background:rgba(0, 0, 0, 0.8); font-size:14px;color:#fff; border-radius: 6px; padding:5px 20px; left: 45%;top:100px;}
.add_up,.add_down{display: inline-block;right:50%;margin-right: -10px;border-style:solid; border-width:0 9px 10px; border-color:transparent transparent rgba(0,0,0,.8);}
.add_up{top:-10px;}
.add_down{bottom: -10px;-webkit-transform: rotate(-180deg);transform: rotate(-180deg);}

/* new 班级活动 */
.plbar span{color:#7F7F7F;}
.plbar span a{margin-left:10px;}
.showimg{margin:12px 0;text-align: center;border-bottom: 1px solid rgba(0,0,0,.1);padding-bottom:10px;}
.showimg a{border-radius: 100%;margin:0 5px;position: relative;width:50px; height: 50px;background:#d8d8d8; display: inline-block;}
.showimg img{margin-top:9px;}
.showimg a:first-child.current{background: #F39C12;}
.showimg a:nth-of-type(2).current{background: #2ECC71;}
.showimg a:nth-of-type(3).current{background: #3498DB;}
.showimg a:last-child.current{background: #667279;}

/* 留言 */
.msglink{right:15px;top:16px;}
.msgarea{z-index: 999;}
.msgarea .icon33{margin:0px 20px;}
.msgarea .icon34{margin:0;}
.msgarea textarea{border:0;width:100%;padding:0 10px;-webkit-box-sizing:border-box;box-sizing:border-box;height:48px;}
.msgarea,.msglist{border-style:solid;border-color:rgba(0,0,0,.1);border-width:1px 0;}
.msgarea .sendBtn{margin-right:15px;}
.msglist img{width:40px; height: 40px;}
.msglist p{margin-left: 50px;}
.msglist p.font14{margin:10px 0 0 0;}
.msgdown li{margin-left:24px;}
.msgbaby{padding:15px 0;}
.msgbaby li{float: left;width:100px; text-align: center;}


 /* 宝宝信息 */
.babyinfo{border-top: 1px solid rgba(0,0,0,.1);border-bottom: 1px solid rgba(0,0,0,.1); }
dl.babyinfo{padding:10px 15px;}
.babyinfo li{position: relative;margin-left:15px;border-bottom: 1px solid rgba(0,0,0,.1); line-height: 2.5;}
.babyinfo li:last-child{border:0;}
.babyinfo label,.babyinfo dt{color:#7f7f7f;font-size: 14px;padding-right: 15px;}
.babyinfo dd{overflow: hidden;}

 /* 宝宝的一天 */
.onewrap{margin-top: 40px;}
.onewrap h4,.onewrap li:last-child h4{line-height: 2;padding-top: 12px;}
.onewrap h5{line-height: 1;}
.classli {padding-top: 40px;}
.onewrap a.history{color: #52c5cc;font-size: 14px;right:15px;}
.classli li{border-bottom: 1px solid #ccc;padding:10px 15px;}
.classli .btnbabylist{margin-left: 20px;color: #d94c3c;}
.classli h5 span{margin-right:15px;line-height: 2;}

 /* 选择幼儿园 */
/* .title{margin-left:10px;font-family: Arial;}*/
.sellist li{font-size:10px;font-family: Arial;}
.sellist .current{background: #ccc;font-size: 20px;}
.ulyey{min-height:20px; border-top: 1px solid rgba(0,0,0,.1); border-bottom: 1px solid rgba(0,0,0,.1);}
.yeydata img{width:30px;height:30px;margin:7px 5px 0 0;}
.yeydata{margin-left:10px; border-bottom: 1px solid rgba(0,0,0,.1);height: 44px;line-height: 44px;}
.ulyey li:last-child{border:0;}

 /* 详细页面happy */
.happyhead{text-align: center;}
.happyhead u{width:60px; height:60px;margin:10px auto 0;}
.happylist{border-top: 1px solid rgba(0,0,0,.2); margin-top: 20px;}
.happylist h3{border-bottom: 1px solid #00aeff;text-align: center;line-height: 2;}
.happylist h3 i{display: inline-block;}
.eat h3,.mood h3{padding-left: 40px;}
.eat h3 i,.mood h3 i{width:20%; color:#00aeff;}
.eat li span,.mood li span{display: inline-block;width:20%;text-align: center;}
.mood li span,.mood h3 i{width: 50%;}
.happylist li{position:relative;border-bottom: 1px solid rgba(0,0,0,.2);height:80px;line-height: 80px;text-align: center; margin-left: 45px;}
.eat li{height:58px;line-height:58px;}
.happylist time{font-size: 12px;left:-33px; position: absolute;color: #7f7f7f;}
.happylist span.number{right:15px; position: absolute;}
.happylist li img{vertical-align: middle;}
.ricehead li{float: left;width:33.3%;text-align: center;}
.moodhead li{float: left;width:50%;text-align: center;}
 /* 轮播图片 */
.imgbox{width:100%;overflow:hidden;margin:0;padding:0;height:200px;background:#c0c0c0;}
.imgbox img{vertical-align: top;}
.imgbox ul{clear:both;}
.imgbox ul li{float:left;width:380px;height:264px;overflow:hidden;text-align:center;}
.imgbox ul li img{width:100%;height:100%;}
.boxtext{color:#fff; font-size:14px;text-indent: 5px; background: rgba(0,0,0,.5);height:34px;line-height: 34px;left: 0;bottom:0;width:100%;}
.boxcircle{right:10px;bottom:5px;}
.boxcircle a{display: inline-block; margin-left: 5px; width:8px;height:8px;border-radius: 100%;background:rgba(255,255,255,.5);}
.boxcircle a.current{background: #fff;}

/*functionlist*/
.functionlist img{right:10px;top:5px;width: 40px;height: 40px;}
.functionlist a{font-size:14px;height:50px;line-height: 40px;padding:5px 10px; position:relative; border-top:1px solid #ccc;}

/* me head */
.wxmsg{padding:10px 15px;}
.wxmsg span img{width:93px;height:93px; border-radius: 6px;}
.wxmsg p{margin-left:60px; line-height:24px;}
.babylist{margin:0 15px; text-align: center;}
/*.babylist a{width: 49%; display: inline-block;}*/

/* 选择班级 */
.selclass{width: 70%; border-radius: 6px; box-shadow: 0 0 4px #ccc;}
.selclass h4{border-bottom: 1px solid #157EFB; text-align: center; line-height: 40px;}
.selclass dl{border-bottom: 1px solid #ccc; color:#157EFB;}
.selclass dt{font-weight: bold; line-height: 32px; margin-left: 15px;}
.selclass dd{margin-left:70px; overflow: hidden;}
.selclass dd span{display: inline-block;width:75px; overflow: hidden; height:30px; line-height: 30px; text-align: left;}
.fold{background: #157EFB;width:24px; color:#fff; text-align: center; top:45%;z-index:999;font-size: 14px;}
.fold:before{top:-24px; -webkit-transform:rotate(270deg); transform:rotate(270deg);}
.fold:after{bottom:-24px;}
.fold:before,.fold:after{content:""; position:absolute;  border-style:solid;border-color:transparent transparent transparent #157EFB; border-width:0 0 24px 24px;right:0;}

/* onedaytj */
.ultjgnd
{
    height:50px;
    line-height:50px;
}
.ultjgn li
{
    width:33.3%;
    float:left;
    text-align:center;
    color:#7f7f7f;
    margin-top: 20px;
}
.ultjgn li span{color:#000;}
.ultjgn li.current
{
    color:Red;
}
.ultjgn u{width: 60px;height: 58px;}
.ultjgn h5{line-height: 22px;}

/* mybaby */
.ulbabylist li{float: left; width: 80px; height: 30px;line-height: 30px;overflow: hidden;text-align:center;}
.ulbabylist li.current{background:#ddd;}
.selclasslist li{height: 40px;line-height: 40px;padding-left: 10px; border-bottom: 1px solid #d8d8d8;}

/* newhome */
.newhome{padding: 10px 0 10px 10px;}
.newhome li{float: left; width:49%; height: 76px; margin-top:6px; position:relative; overflow: hidden;margin-right:1%;}
.newhome li a{color: #fff; float: left;width: 100%;height: 100%;}
.newhome li img.pa{ right:10px; bottom:6px;width:56px;}
.newhome h4{position:absolute;width: 100%; left:0;top:0;padding-left: 5px; line-height:30px;z-index:3;}
.minelist li{width:33.33%;text-align: center; margin:10px 0;float: left;}
.minelist img{width:62px;vertical-align: middle;}
.minelist div{width:70px;height:70px; line-height:68px; margin:0 auto 5px; border-radius: 100%;}
.homenewslist li{padding:10px 10px 0; min-height: 90px;border-bottom:1px solid #E5E5E5;}
.homenewslist h6,.planhead h6{padding:5px 10px;color:#595757;font-size:16px;}
.homenewslist img{width: 70px;height:70px; border-radius: 6px;}
.homenewslist p{margin-left:80px; line-height:22px;}
.homenewslist time,.noread{color: #B2B2B2;}
.noread{margin-right: 10px;}
.imgopacity{background: rgba(0,0,0,.2);top:0;left: 0; z-index: 2;position: absolute;width: 100%;height: 100%;}

/* new pclass */
.pclassbaby{margin-top: 10px;border-bottom: 1px solid rgba(0,0,0,.1);border-top: 1px solid rgba(0,0,0,.1);}
.pclassbaby img{width: 30px;height: 30px;}
.pclassbaby p{margin-left:40px;border-bottom: 1px solid rgba(0,0,0,.1);line-height: 30px;padding-bottom: 5px;margin-bottom: 5px;}
.pclassbaby a:last-child p{border:0;padding:0;margin-bottom: 0;}

/* 形态计算器 */
.counter{width: 100%;top:0;left: 0;height: 44px;line-height: 44px;padding:0 10px;}
.counterform{margin-top: 60px;}
.counterform input,.calculatorform span{text-align: right;float: right; line-height: 44px;}
.counterlist{margin-bottom: 50px;}
.counterlist h6{color:#00c85e; border-bottom-color:#00c85e;}
.counterform .askname{font-size: 16px;}
.counterfoot,.immuneBtn{background: rgb(255,255,255);height: 50px; border-top: 1px solid rgba(0,0,0,.2); width: 100%;bottom: 0;left:0;}
.counterfoot li{float: left;width: 25%;text-align: center;}
.counterfoot u{display: block;margin:3px auto 0; width:34px;height:28px;}
.counterfoot .current{color:#00c85e;}

/* 成长记录列表 */
.growlist{margin-bottom: 50px;}
.growlist li{min-height:64px;border-bottom: 1px solid rgba(0,0,0,.1);line-height: 22px;color:#7f7f7f; padding:10px 15px;}
.growlist div{width:50%;}
.growlist div:last-child{padding-left:24px;}
.growlist h4 span{color:#000;}
.growtitle{font-size:12px;border-bottom:1px solid rgba(0,0,0,.1);padding:5px 15px;}

/* 计划免疫列表 */
.immunelist li{border-bottom: 1px solid rgba(0,0,0,.1);padding:10px 15px; line-height: 22px;}
.immunelist span.fr{color:#fff; border-radius: 4px; font-size: 12px;padding: 0 4px;}
.immuneBtn{padding: 8px;}
.immuneBtn a{float:left;background: #00c85e; border-radius: 4px; color: #fff;text-align: center;line-height: 36px;}
.immuneBtn a:first-child{width: 66%;}
.immuneBtn a:last-child{width:32%;margin-left:2%;}
.immuneNone a{background: #fff;color: #000;border:1px solid rgba(0,0,0,.15);}

/* 帐号管理 */
.btnhalf .btn{float: left;width:49%;}
.btnhalf .btn:first-child{margin-right: 1%;}
.btnhalf .btn:last-child{margin-left: 1%;}

.birthList img{width: 40px;height: 40px; margin-right: 10px;}
.birthList h6{padding:5px 10px;}
.birthdate{right:10px;top:22px; color:#7f7f7f;font-size: 14px;}
.birthList li{border-bottom: 1px solid rgba(0,0,0,.1);padding:5px 10px; min-height: 52px;}

/* cpls */
.stulist li.block{width: 33.33%;float:left;text-align:center;margin:5px 0;}
.stulist .icon72{z-index: 3;}
.stulist p{position: absolute;bottom: 0; height: auto; width: 100%; color:#000;font-size:14px;}
.stulist img{width: 100%;height: 100%; vertical-align:top;}
.stulist ul.selHead,.stulist .oneuser{margin: 0 5px;}
.stulist ul.selHead{border-radius:0;}
.stulist img{width: 100%;height: 100%;}
.stulist ul.selHead,.stulist .oneuser{margin: 5px;}

/*recipr 0516*/
.recipeHead{background: #7f7f7f;line-height: 20px;padding:7px 15px;color:rgba(255,255,255,.5);}
.recipeHead u{width:20px;height:20px;}
.recipeHead span{display: inline-block;width: 50%;}
.recipeHead span:first-child{text-align: right;padding-right: 10px; border-right: 1px solid rgba(255,255,255,.5);}
.recipeHead span:last-child{text-align: left;padding-left: 10px;}
.recipeSearch li{float:left;width: 50%;background: #00c85e;color: #fff; text-align: center;border-bottom: 1px solid rgba(0, 0, 0, 0.2);}
.recipeSearch div{height: 40px;line-height: 40px; }
.recipeSearch li.current{background: #eeedf3;color: #00c85e;}
.recipeSearch input{border:0;width: 100%;height:40px; line-height: 40px; text-align: center;}
.recipeSearch u{width:20px;height:20px; margin-left: 5px;}
.searchDown li{background-size:16px 16px; border-bottom: 1px solid rgba(0,0,0,.2);height: 42px;line-height: 42px;padding-left:40px;}
.searchDown li:active{background-color:rgba(0, 0, 0, 0.2);}
.clearHistory{text-align: center; cursor: pointer;}
.recipeTitle{background: #eeedf3;padding:4px 15px;}
.eatWhat{ height: 40px;line-height: 40px;padding:0 15px;}
.foodList{border-top: 1px solid #ccc;border-bottom: 1px solid #ccc;line-height:40px;}
.foodList dt{float: left; color: #7f7f7f;padding:0 10px;}
.foodList dd{border-bottom: 1px solid #ccc; margin-left: 52px; position: relative;padding-right: 60px;}
.foodList dl:last-child dd{border:0;}
.foodList dd span{margin-right: 15px;}
.foodList i,.goodsSelect i{display:inline-block;color: #fff;font-size: 12px;border-radius: 4px;padding:2px 9px; line-height: 16px; position: absolute;right: 10px;top:10px;}
.foodList i.normalText,.goodsSelect i.normalText{background: #157efb;}
.foodList i.smallText,.goodsSelect i.smallText{background: #e74c3c;}
.whiteBack{background: #fff;}
.fruitlist{padding:10px 15px;line-height:24px;}
.fruitlist span{margin-right: 15px;}

/* 出勤list */
.dutylist dl{border-top:1px solid rgba(0,0,0,.1);}
.dutylist dt{color:#7f7f7f; float: left; text-align: center;width: 50%;padding-top:12px;}
.dutylist dd{margin-left: 50%;position: relative;padding:12px 0; text-align: center; border-left:3px solid rgba(0,0,0,.1);}
.dutylist span{display: inline-block;width: 12px;height: 12px;border-radius: 12px;position: absolute;left: -7px;top:18px;}
.dutylist .dot01{background: #00c85e;}
.dutylist .dot02{background: #facf04;}
.dutylist .dot03{background: #e74c3c;}
.dutylist .dot04{background: #5337d6;}

/* 一周睡眠 */
.sleeplist div.pr{width: 60px;margin:0 auto;}
.sleeplist .icon72{bottom: 0;right: 0;}
.sleephead{height: 44px; line-height: 44px; padding:0 15px; background: #bdbfc0;}
.sleeplist .icon48{margin-right: 5px;position: static;}

/*采购单list*/
.buyLink{margin-right: 15px; line-height: 28px;}
.buyLink a{margin-left: 15px;}
.buySearch{border-top: 1px solid rgba(0,0,0,.1);border-bottom: 1px solid rgba(0,0,0,.1);}
.buySearch li{margin-left: 15px;position: relative;height: 40px;line-height: 40px;}
.buySearch input{border: 0;}
.buySearch li:first-child,.goodsSelect li{border-bottom:1px solid rgba(0,0,0,.1);}
.buyDetail h3{text-align: right; width: 55px;}
.buyDetail dd:nth-of-type(1){padding-top: 10px;}
.buyDetail dd{border-left:2px solid #b2b2b2; margin-left:70px;}
.buyDetail dt{position: absolute;left:12px;top:10px;color: #7f7f7f; width: 45px;text-align: right;}
.buyDetail dt:after,.buyDetail h3:after{content: '';display: block;position: absolute;width: 8px;height: 8px;border-radius: 100%;background: #b2b2b2;top:6px;right: -18px;}
.buyDetail h3:after{width:6px;height:6px;top:18px;right: -19px;}
.buyDetail .unfinish dt:after{background: #fc015a;right:-20px;border:2px solid #fff;}
.buyDetail span:first-child,.buytHead span:first-child{width:75px;text-align: right;color: #7f7f7f; display: inline-block; margin:0 15px 0 10px;}
.buyDetail .unfinish i{color:#fc015a;}
.buyDetail .finish i{color:#7f7f7f;}
.buyDetail i{margin-left: 14px;}

/*采购单detail*/
.buytHead{line-height:30px;padding-bottom: 10px;}
.buytHead span:first-child{width: 85px;}
.goodsSelect li{height: 40px;line-height: 40px;margin-left: 15px;position: relative;}
.goodsSelect span{width: 33%; height: 100%; overflow: hidden; display: inline-block;}
.goodsSelect span:nth-of-type(2),.checkList span:nth-of-type(2),.checkList span:nth-of-type(3){text-align:center; color: #7f7f7f;}
.goodsSelect u{top:9px;right:15px;left: auto;}
.buyimglist li{width: 32%;margin-right: 2%;}
.dateSelect li{width: 14%;border-right: 1px solid #ccc;text-align: center;height: 40px;position: relative; float: left;}
.dateSelect li:nth-of-type(6),.dateSelect li:nth-of-type(7){width: 15%;}
.dateSelect p{position: relative;z-index: 2;}
.dateSelect p:nth-of-type(2){font-size: 10px;color:rgba(0,0,0,.5); line-height: 18px;}
.dateSelect .back{position: absolute;height: 100%;background: #f79722;top:0;left: 0;z-index: 1;}

/*验收列表*/
.checkList li:first-child{margin-left: 0;padding-left: 10px; height: 28px;line-height: 28px;}
.checkList li:first-child span{color: #fff;}
.checkList li:first-child u{width: 20px; height: 20px;top:4px; right: 15px;}
.checkList span{width: 25%}
.checkText textarea{width: 100%; border: 1px solid #ccc; box-sizing: border-box;}
.goodsSelect i.normalText{background: #b2b2b2;}


/*绑定*/

#anec_content{ width:100%; word-wrap:break-word; background:#F4F3F8; padding-top:15px; overflow:hidden}
.wxmsg{ border-top:1px solid #DDDDDD;border-bottom:1px solid #DDDDDD;}
.jcbanding{ display:inline-block; float:left; padding:34px 10px 0 5px; font-size:15px;}
.newtitle{ padding:0 8px; line-height:50px;font-size:14px; height:50px;border-bottom:1px solid #DDDDDD; background:#F4F3F8; font-weight:normal}
.jtelephone{ border:0px; float:left}
.login input{width:80%; height:20px; line-height:normal; padding-left:8px; line-height:20px; color:#333333; font-size:14px }
.login{margin:0 8px;height:50px; padding-top:15px}
.tel{text-align: center; margin-top:8px; font-size:12px; }
.tel a{ color:#ff2727}
.divzhgl{ overflow:auto}


.yzmbd{margin:5px  auto;  overflow:hidden;  }
.yzmbdinpuz{width:49%; float:left;padding-left:8px; }
.yzmbdinput{ border:none; height:28px; line-height:normal; line-height:28px; color:#333333; font-size:14px; /*border-radius:3px;*/ border-bottom:1px solid #DDDDDD;  border-left:none; border-top:none; border-right:none; margin:0 8px 0 10px; width:90%}
.yzmbdyzm{ border:1px solid #53cac4 ; font-size:14px; color:#53cac4 ; width:33%;height:28px; line-height:28px; border-radius:3px; text-align:center; cursor:pointer; display:block; float:left}


.FancyInput__bar___1P3wW:after,.FancyInput__hasError___332t5.FancyInput__bar___1P3wW:before{background:#eb5815}
.FancyInput__bar___1P3wW{position:relative;display:block}
.FancyInput__bar___1P3wW:after,.FancyInput__bar___1P3wW:before{content:'';height:3px;width:0;bottom:0;position:absolute;background:#53cac4;transition:all .2s ease}
.FancyInput__bar___1P3wW:before{left:50%}.FancyInput__bar___1P3wW:after{right:50%}
.ssjh_xhx:focus~.FancyInput__bar___1P3wW:after,.ssjh_xhx:focus~.FancyInput__bar___1P3wW:before,.yzmbdinput:focus~.FancyInput__bar___1P3wW:after,.yzmbdinput:focus~.FancyInput__bar___1P3wW:before{width:50%}



/*解除绑定*/
.zpicon17{width:10px;height:17px; display:block; padding-top:1px}
.zpicon17 img{width:10px;}
.gxbg{width:219px; margin:8px auto 0 auto; min-height:143px; background:url(../images/bdbg.jpg) no-repeat; background-size:100% 100%;}
.gxbg p span{ display:inline-block; width:45%; text-align:left;/*overflow: hidden;*/vertical-align:top;text-overflow: ellipsis;line-height: 18px;}
.gxbg p span:first-child{text-align:right;}
.gxbg_div{ padding:35px 0; width:160px; margin:0 auto; font-size:12px; line-height:18px}
.gxbg_div p{ /*height:18px; overflow: hidden;*/}
.update{ background:#FFA922; font-size:11px; color:#FFF; width:60px; height:30px; line-height:32px; float:right; border-radius:40px; text-align:center; cursor:pointer; margin:30px 0 0 8px; display:block}
.gxbg_div p label{ border: 1px solid #53cac4;
    border-radius: 15px;
    color: #53cac4;
    cursor: pointer;
    display:inline-block;
    font-size: 10px;
    line-height: 15px;
    text-align: center;
    width: 43px;  }

.xszpicon17{width:100px; padding-top:5px; font-size:13px; font-weight:normal; line-height:17px}
.xszpicon17 img{ vertical-align:middle; margin-top:-3px; padding-right:3px; height:18px}
.zjcbanding{ display:inline-block; float:left; padding:20px 10px 0 5px; font-size:15px;}
.zcheckdj{  width:90%; margin:15px auto; overflow:hidden; border-bottom:1px solid #DDDDDD; padding-bottom:5px}
p.zcheckdj:before{background:#FE632D; content:""; display:inline-block; width:4px; height:22px; float:left; margin-right:5px}
.zcheckdj span{ line-height:22px;}
.jcsf{ width:80%; margin:10px auto; padding:10px 0; overflow:hidden; clear:both}

/*幼儿请假*/
.yemain{ margin:0 auto; overflow:hidden}
.yemain01{ width:90%; margin:35px auto 10px auto; border-radius:4px; border:1px solid #DDDDDD; }
.yemain01 .yemain01_div{ width:90%; margin:0 auto; border-bottom:1px solid #DDDDDD; overflow:auto}
.yemain01_div p{ float:left;  text-align:center; color:#010101; line-height:30px; height:30px; font-size:14px}
.yemain01_div p.yeleft{ text-align:left;width:33%;}
.yemain01_div p.yecen{ width:23%;}
.yemain01_div p.yeright{ text-align:center;width:43%;}
/*.yemain01_div .yebtn{ text-align:center;background:#FFA922;  color:#FFF; width:28px; height:23px; line-height:23px;border-radius:4px; cursor:pointer; margin:3px 10px 0 0px; border:0;font-size:14px}*/
.yemain01_div .yebtn{
    text-align: center;
    background: #FFA922;
    color:#000;
    width: 75px;
    height: 23px;
    line-height: 23px;
    border-radius: 4px;
    cursor: pointer;
    margin: 3px 10px 0 0px;
    border: 0;
    font-size: 14px;
    display: block;
    right: 6%;
    position: absolute;
}
.yemain02{width:90%; margin:0 auto; overflow:hidden}
.yemain02 .yemaindiv{width:100%; margin:0 auto; border-bottom:1px solid #DDDDDD; overflow:hidden; padding:5px 0 5px 0}
.yemaindiv img{ width:52px; height:52px; float:left; vertical-align:middle; margin-top:13px }
.yemainright{ float:left; padding-left:5px}
.yemainright span{ line-height:20px;font-size:12px; color:#666666; display:block;}
.font30{ font-size:15px; color:#000000}


/*幼儿出勤率*/
/*.yemain_cq{ width:90%;  margin:25px auto 10px auto; border-radius:4px; border:1px solid #DDDDDD; overflow:hidden }*/

.yemain_cq{ width:90%;  margin:50px auto 10px auto; border-radius:4px; border:1px solid #DDDDDD; }
.yemain_cq table{ color:#010101;  font-size:14px;  width:90%; margin:0  auto}
.yemain_cq td{line-height:20px; vertical-align:middle;border-bottom:1px solid #ddd;  color:#333333;padding:8px 0;word-break: break-all;}
.yemain_cq .unbottom{border-bottom:none;}
.yecqqbg{ color:#e25321}
.yemain_cq td.unline{ color:#010101}
.yemain_cq table input.yecqbtn{ text-align:center;background:#FFA922;  color:#FFF; width:50px; line-height:1;  border-radius:4px; cursor:pointer; border:0;font-size:14px; padding:5px 0;}
.yemain_cq img{ width:23px; height:0.650px; vertical-align:middle}
.yecq_ph{ text-align:center;background:#c5c5c5;  color:#FFF; width:23px; line-height:23px;  border-radius:25px; cursor:pointer; margin:3px 0 0 0px;font-size:11px; display:block }
.yemain_cq table .yecqqbg td:nth-child(2){max-width: 65px}

.yemain_cq1{ width:100%;  margin:36px auto 10px auto; }



/*幼儿出勤*/
.yemain{ overflow:hidden; }
.yemain_ls{ width:100%; height:35px; line-height:35px; margin:40px auto 0 auto; color:#fff; background:#53cac3;  }
.yemain_ls p{ /*width:50%;*/ float:left; /*line-height:39px; */font-size:15px;/* height:39px;*/}
.yemain_ls p.yeleft{ text-align:left; padding-left:15px;  }
.yemain_ls p.yeright{ text-align:right; padding-right:8px;}
.yemain_ls .yemian-sel{margin-left: 15px;margin-right:43px;margin-top:5px;height:25px;float: left;font-size: 0;}
.yemain_ls .yemian-sel li{cursor:pointer;display: inline-block;background:#29a8a0;width: 50px;font-size: 15px;text-align: center;line-height: 27px;height: 25px;}
.yemain_ls .yemian-sel li.light-sel{background: #fff;color: #fe6a55;}
.yemain_ls .yemian-right{float: right;margin-right: 5px;}



/*.ye_search{ background:#F2F2F2; height:21px; color:#333333; width:94%; clear:both }*/
.ye_search{ background:#F2F2F2; /*height:6px;*/ color:#333333; width:100%; clear:both }
.txtwbk{ border:0;color:#333333 ;background:#F2F2F2;padding-left:15px; line-height:21px; width:100%;height:21px;   }
.ye_search1{ background:#F2F2F2; height:11px; color:#333333; width:100%; clear:both }


.onechuqinstu{height:42px;line-height:42px;padding:0 5px 0 20px;border-bottom: 1px solid #ddd;display:-webkit-box;display: -webkit-flex;display: flex; clear:both}
.onechuqinstu ul{display: inline-block; width: /*74%*/100%;}
.onechuqinstu ul li{    font-size: 13px;
    /*text-align:right;*/
    height: 39px;
    float: left;
    width: 25%;}
.onechuqinstu ul.uleat li{width: 33%;}
.onechuqinstu h2{text-overflow:ellipsis;white-space:nowrap;text-align:left;width:20%;min-width:50px;font-size: 14px;margin-right: 10px;font-weight:normal;}
.onechuqinstu ul li a{cursor: pointer;text-align:center;line-height:20px;width: 35px;height: 20px;display: inline-block;}
.onechuqinstu ul li a.delete{ border: none;color: black;text-decoration: line-through;}
.onechuqinstu ul li a.zc-btn{border:1px solid #ffa922;color: #ffa922;text-decoration:none;}
.onechuqinstu ul li a.bj-btn{border:1px solid #ff2727;color: #ff2727;text-decoration:none;}
.onechuqinstu ul li a.sj-btn{border:1px solid #25c1ed;color: #25c1ed;text-decoration:none;}
.onechuqinstu ul li a.other-btn{border:1px solid #53cac3;color:#53cac3;text-decoration:none;}
.onechuqinstu .name-con{position: relative;}
.onechuqinstu .name-con i{position: absolute;left: 5px;top:-11px;}
.onechuqinstu .name-con i img{width: 11px;height: 11px;margin: 3px 3px;}

.onechuqinstu ul li a.eat-btn{border:1px solid #53cac3;color:#53cac3;/*font-weight: bold;*/background: #ebf7ef;}
.onechuqinstu ul li a.noeat-btn{color: #ffa922;text-decoration: underline;}
.onechuqinstu ul li .din-sel{line-height: 15px;margin-top:5px;}
.onechuqinstu ul li .din-sel p a{padding-bottom:4px;width:30px;line-height: 15px;display: inline-block;position: relative;}
.onechuqinstu ul li .din-sel p a::after{padding-bottom:2px;display:block;content: "";border-bottom: 1px solid #ddd;position: absolute;width: 100%;}
.onechuqinstu:last-child{border-bottom: none;}
.onechuqinstu  .sel-span{color: #e6693d;}
.one-din ul li{width: 33.3%;}
.thingres-span,.ill-span,.othres-span,.editor-span,.parent-span,.teacher-span{margin-left:3px;vertical-align:text-bottom;font-size: 7px;width: 17px;height: 17px;line-height:17px;border-radius: 50%;display: inline-block;color: #fff;text-align: center;}

.parent-span{background: #12B7F5;}
.teacher-span{background: #FD784B;}
.ill-span{background: #02CE9A;}
.othres-span{background: #C955F1;}
.thingres-span{background: #6998FD;}
@media only screen and (max-width:460px ) {
	.ill-span,.thingres-span,.othres-span,.editor-span,.parent-span,.teacher-span{width:25px ;height: 25px;line-height: 26px;vertical-align: top;margin-top: 7px;}
	.onechuqinstu ul li a{width: 30px;color: #7f7f7f;}
}
.dinner-num{width: 92%;margin: auto;height: 37px;line-height: 37px;font-size: 0;}
.dinner-num input{width:83%;outline:none;border: none;border-bottom: 1px solid #FED3CE;text-align: center;}
.dinner-num label{width: 26%;display: inline-block;font-size: 12px;}
.dinner-num span{width: 22%;display: inline-block;font-size: 12px;}
.oneeat{height:39px;line-height:39px;padding:0 5px 0 13px;border-bottom: 1px solid #ddd;display:-webkit-box;display: -webkit-flex;display: flex; clear:both}
.oneeat ul{display: inline-block; width: 74%;}
.oneeat ul li{ font-size: 13px;text-align:center;height: 39px;float: left;width: 25%;}
.oneeat ul.uleat li{width: 33%;}
.oneeat h2{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;text-align:left;width:20%;min-width:50px;font-size: 14px;margin-right: 10px;font-weight:normal;}
.oneeat ul li a{cursor: pointer;text-align:center;line-height:20px;width: 40px;height: 20px;display: inline-block;}
.oneeat ul li a.delete{ border: none;color: black;text-decoration: line-through;}
.oneeat ul li a.zc-btn{border:1px solid #ffa922;color: #ffa922;text-decoration:none;}
.oneeat ul li a.bj-btn{border:1px solid #ff2727;color: #ff2727;text-decoration:none;}
.oneeat ul li a.sj-btn{border:1px solid #25c1ed;color: #25c1ed;text-decoration:none;}
.oneeat ul li a.other-btn{border:1px solid #53cac3;color:#53cac3;text-decoration:none;}

.oneeat ul li a.eat-btn{border:1px solid #53cac3;color:#53cac3;/*font-weight: bold;*/background: #ebf7ef;}
.oneeat ul li a.noeat-btn{color: #ffa922;text-decoration: underline;}
.oneeat ul li .din-sel{line-height: 15px;margin-top:5px;}
.oneeat ul li .din-sel p a{padding-bottom:4px;width:30px;line-height: 15px;display: inline-block;position: relative;}
.oneeat ul li .din-sel p a::after{padding-bottom:2px;display:block;content: "";border-bottom: 1px solid #ddd;position: absolute;width: 100%;}
.oneeat:last-child{border-bottom: none;}
.oneeat  .sel-span{color: #e6693d;}


.letter{right:5px;top:85px;text-align: center;cursor: pointer;z-index: 9;position: fixed;font-size: 9px;color: #53cac3; line-height:12px;}
.none{ border-bottom:none}
.bgzd{ background:#ffa922; color:#fff; line-height:20px;  font-size:13px; padding:5px}
.zqiandao{ background:#ffa922; color:#fff; border-radius:15px; text-align:center;line-height:25px;  padding:5px 10px;}
.yemain_cqgb{ background:#53cac3; color:#fff; position:relative;height:45px;font-size:13px; font-weight:bold; line-height:45px;}
.yemain_bfl{ position:absolute; background:url(../images/tobg.png) no-repeat; width:75px; height:52px; background-size:100%; display:inline-block; text-align:center; line-height:68px;font-size:15px; font-weight:bold; bottom:2px; font-family:Arial, Helvetica, sans-serif; color:#F00}
.yemain_bfzleft{ float:left; width:45%; display:inline-block; text-align:center}
.yemain_bfzright{ float:left; width:55%; display:inline-block; text-align:right; padding-right:5%}

/*幼儿出勤改版*/
.yebg1{ background:#29a8a0; height:25px; line-height:26px; text-align:center; display:inline-block; padding:0 10px}
.yerightdiv{ float:right; width:60%;}
.yerightdiv p{font-size: 13px;margin-right: 5px;}
.yebg1.hover{background:#fff; color:#fe6a55}
.yebanner{width:100%;  background:url(../images/cqbanner.jpg) no-repeat; background-size:100%; height:85px}
.yebanner p{ text-align:right; padding-right:25px; float:right; clear:both}
.yebanner label{ color:#ff0d0d; font-size:22px; padding:0 3px; line-height:23px; display:inline-block; float:left; font-weight:bold;margin-top:5px}
.yebleft1 img{ width:103px; height:29px; float:left; margin-top:5px}
.yebleft2 img{ width:16px; height:25px; float:left;margin-top:5px}
.yebleft3 img{ width:120px; height:23px;}
/*幼儿出勤签到*/

.qiandaodiv{ width:80%; text-align:center; margin:5px 0 0 14px; overflow:hidden;}
.qiandaodiv .wqiandao{/*border:1px solid #ff9900;*/ color:#ff9900; border-radius:15px; text-align:center;line-height:25px; line-height:1; /*padding:5px 10px;*/display:inline-block; margin:13px 0 0 5px; float:left  }
.qiandaodiv .wqiandaoimg{ width:30px; height:27px; display:inline-block;float:left}
.wqiandaoimg img{ width:100%; vertical-align:middle}
.yeqidaomain ul{ float:left; width:80%; }
.yeqidaomain li{ float:left; width:20%; color:#999999; font-size:12px;line-height:25px; text-align:center; cursor:pointer;}
.yeqidaomain li.chehui{ background:#CCCCCC; color:#fff; /*border-radius:15px; */text-align:center;line-height:25px; }
.parent-qj .perqj{position:relative;font-size:14px;height: 37px;background: #fff;    border-bottom: 1px solid #ddd;color: #ff9900;text-align: center;line-height: 37px;}
.greet-btn{width: 40px;height: 20px;display: inline-block;background: #53CAC4;line-height: 22px;text-align: center;color: #fff;font-size: 12px;position: absolute;top:50%;margin-top:-10px;right: 0;margin-right: 25px;cursor: pointer;}
.atten-btn{cursor:pointer;margin-left:8px;height: 20px;line-height: 22px;background: #FF6A56;color: #fff;width:75px;text-align: center;font-size: 12px;position: absolute;top:50%;margin-top:-10px;left: 0;}
#divhomeunleave{position: relative}
.label-txt1{background: #58CAC2;}
.label-txt2{background: #ccc;}
.label-txt3{background: #24C1EC;}
.label-txt1,.label-txt2,.label-txt3{margin-top:2px;position:absolute;float:left;display:inline-block;width: 15px;text-align:center;height: 39px;line-height: 13px;font-size: 6px;font-style: normal;color: #fff;}
/*幼儿前一天出勤*/
.yemain_lsqj p{ line-height:25px; color:#999999;font-size:12px;}
.idcanlenbg{ background:#FFF; padding-bottom:5px}

 /*二维码分享*/
 .wxewm_div{ width:95%; overflow:hidden;border-bottom:1px solid #dddddd; float:right; padding:15px 0;}
.wxewm_div .wxzleft{ float:left;width:138px; }
.wxzleft p{ width:113px; height:113px }
.wxzleft  img, .wxzright img{ width:100%; cursor:pointer}
.wxzleft span{ display:block; color:#333333; height:40px; line-height:40px; font-size:18px; text-align:center;width:113px; }
.wxewm_zdiv{ width:304px; margin:0 auto}
.wxzright{float:left;width:138px; margin-top:10px}
.wxzright p{ width:44px; height:39px; display:block }
.wxzright .wxewm{border:1px solid #12b7f5; color:#12b7f5; border-radius:5px; text-align:center; line-height:1; padding:5px 10px;  display:inline-block; width:115px; float:right;margin-bottom:5px;cursor:pointer}


/*学生切换*/

.yeqjdiv{ background:#F4F3F8; border-bottom:1px solid #DDDDDD; overflow:hidden; padding:15px 0 15px 0; width:100% }
.prebtn{  width:13px; position:absolute; top:63px; left:8% }
.nextbtn{  width:13px;position:absolute; top:63px; right:8% }
.prebtn img,.nextbtn img{ vertical-align:middle;width:13px; height:22px; cursor:pointer;}
.yeqjdiv_zd{ margin:0 auto; width:100%; overflow:hidden}
.yeqjdiv ul{  overflow:hidden;  margin:0 0 0 0; height:25px; width:80%}
.yeqjdiv li{  width:25%; margin-right:10px;color:#686868; cursor:pointer; display:inline-block }
.yeqjdiv li img{ width:52px; height:52px; border-radius:25px}

.yeqjdiv li p{ width:77px; text-align:center; font-size:13px; line-height:25px; height:25px;text-overflow:ellipsis;overflow:hidden;white-space:nowrap}
.yeqjdiv li.hover{ color:#ff632d}

/*就餐登记*/

   .layermbtn span {
            background: #13b7f6 none repeat scroll 0 0;
            border-radius: 5px;
            color: #fff;
            cursor: pointer;
            font-size: 14px;
            height: 39px;
            margin-left: 10px;
            text-align: center;
            width: 36%;
        }
        .yecqbtn{ text-align:center;background:#FFA922;  color:#FFF; width:50px; line-height:1;  border-radius:4px; cursor:pointer; border:0;font-size:14px; padding:5px 0;}
        .dining, .dining1{
            width: 90%;
            margin: 0px auto 15px auto;
            padding-left: 19px ;
            border-radius: 1px;
            border: 1px solid #DDDDDD;
            height:1.650px;
        }
  .zdining{ margin-top:50px;}

        .dining .contleft{ float:left; width:69%; overflow:hidden}
        .dining > div >{float:left; text-align: center;  margin: auto;  }
		.dining .content1{ width: 48%; height:40px; line-height:40px;display:inline-block;border-bottom: 1px solid #DDDDDD; text-align:center; float:left; }
		.dining span.mleft{ text-align:left}
        .dining .content2{ width: 48%; height:40px; line-height:40px;display:inline-block; text-align:left;  text-align:center; float:left; }
        .dining .content3,.dining1 .content3{ height: 80px;line-height:80px; width:30%; float:left;}
/*就餐登记改版*/
.eatstu-tit{
	width: 20%;
	color: #999999;
	margin: 0 auto;
}
.footbtn{ position: absolute; height: 47px; bottom: 0; width: 100%; z-index: 999;}
.footbtn p{line-height: 45px; text-align: center; color: #fff; font-size: 16px;}
.footbtn p.btnleft{ width: 60%;}
.footbtn p.btnright{ width: 40%;}
.dinner-botom{ position: relative; top: 8px; padding-right: 10px; margin-bottom: 30px; }
.dinner-botom p{ background:#faf9f2; border: 1px solid #f1efe5; border-radius: 3px; font-size: 10px; line-height: 25px; padding: 0 10px;align-items: center; display: flex;}
.mui-popover-arrow {
	position: absolute;
	z-index: 1000;
	top: -25px;
	left: 9px;
	overflow: hidden;
	width: 13px;
	height: 26px
}
.mui-popover-arrow:after {
	position: absolute;
	top: 19px;
	left: 0;
	width: 13px;
	height: 13px;
	content: ' ';
	-webkit-transform: rotate(45deg);
	transform: rotate(45deg);
	border-radius: 3px;
	background: #faf9f2;
}
/*保健医*/


     .bzdining{
            width: 90%;
            margin: 50px auto 15px auto;
            border-radius: 4px;
            border: 1px solid #DDDDDD;
            font-size: 0.30px;
            font-weight: bold;
        }
        .zdining1{height:63px; border-bottom:1px solid #DDDDDD; width:95%; margin:0 auto}
        .zdining2{height:49px;border-bottom:1px solid #DDDDDD;  width:90%; margin:5px auto 0 auto}
        .zdining1  .zcontent1,.zdining1 .zcontent2{float: left;height:63px; }
        .zdining1 .zcontent1,.zdining2 .zcontent1{width:70px; } /* 15% 65%*/
        .zdining1 .zcontent2, .zdining2 .zcontent2{width:24%; }
        .zdining1 .zcontent2>p{height:20px;margin:auto;line-height:20px;width:40px;text-align:center; }
	    .zdining1 p em{ color:#e25321; font-style:normal; border-bottom:none}
		 .zdining1 .zcontent2 :nth-child(odd){margin-top:18px;border-bottom:1px solid #DDDDDD;}
         .zdining2 .zcontent1{ float:left; line-height:49px}
        .zdining2 .content1, .zdining2 .zcontent2{float: left; height:49px;line-height:49px; text-align:center}
        .eat{width: 6.50px;height:0.650px;background-color: #ffa922;color: #ffffff;border-radius: 5%;}
        .eatp{width: 55px;height:28px;border:1px solid #ffa922;background-color: #FFA922; }
        .zcontent2 img{width: 55px;height:28px; vertical-align:middle}
		.zdining2.znone{ border-bottom:none}


/*2017.3.28消息页面*/

.messagediv{width:100%;overflow:auto; position:absolute; right:0}
.messagediv .messagemain{width:100%; margin:0 auto; border-bottom:1px solid #e8e8e8; overflow:hidden; padding:8px 0 8px 0; }
.messagemain img{ width:47px; height:47px; float:left; vertical-align:middle; border-radius:50%; /*border:1px solid #D1D0CE*/}
.messageright{ float:left; padding:3px 0 0 5px; width:65%;}
.messageright span{ line-height:20px;font-size:12px; color:#333333; display:block;}
.messagezright{ position:absolute;right:0;text-align:right; padding:3px 10px 0 0px; color:#999999; font-size:13px;}
.xiaoxibg{display:block; background:#FC1C1C; border-radius:50%;  min-width:15px; color:#fff; font-size: 10px; text-align: center; vertical-align: middle;height:15px;   cursor:pointer; line-height:15px; float:right}
.messagezright span{line-height:20px;  display:block;}
.noxx{ text-align:center;}
.noxx img{ width:115px; height:92px; margin-top:30px}
.noxxdiv{ text-align:center; color:#999999; font-size:13px; line-height:20px; }

/*宝宝页面*/
.baby-top img{width: 100%;}

.class-list .class-icon{width: 18px;height: 16px;margin-right: 7px;vertical-align: top;margin-top:13px;}
.class-list .arr-right{width: 8px;height: 13px;float: right;margin-top:15px;cursor: pointer;}
.class-list .arr-down{width: 13px;height: 8px;float: right;margin-top:18px;cursor: pointer;}
.class-list {border-bottom:1px solid #e8e8e8;height:44px;line-height:44px;font-size:15px;margin-left: 12px;margin-right: 12px;}
.class-list i{font-style: normal;color: #e25321;}
.class-list span { display:inline-block; overflow: hidden; }
.class-list .bindname { max-width: 46%; white-space: nowrap; text-overflow : ellipsis;}
.unconcern{color: #12b7f5;font-size: 12px;margin-left: 10px;}

.baby-main{position: relative;margin:0 auto; border-bottom:1px solid #ddd; margin:0 12px 0 12px; padding: 10px 0 10px 0;}
.baby-main dt{width: 47px;height: 47px;margin-top:-3px;float: left;position: relative;}
.baby-main dt img{ width:47px; height:47px; float:left; border-radius:50%; margin-right: 9px;}
.baby-main dd{width:100%;margin-top:2px;}
.baby-main dd p{margin-left: 55px;}
.baby-main dt img.heart-icon{width: 18px;height: 15px;margin-top:2px;vertical-align: top;margin-left: 15px;position: absolute;right: -14px;top:-2px;border-radius: 0;}
.baby-main dd img.phone-icon{width: 8px;height: 13px;vertical-align: top;margin-top:4px;margin-right: 5px;margin-left: 1px;}
.baby-main dd p{ height:20px;line-height:20px;font-size:15px; color:#333333;}
.tel-num{font-size: 13px;color: #666;}
.baby-main .img-right{position:absolute;right:0;top:0;float:right; margin-top:21px;}
.baby-main .img-right img{text-decoration:none;cursor:pointer;width: 20px;height: 20px;vertical-align: top;margin-left: 6px;}
.baby-main .mark-btn{vertical-align:middle;cursor:pointer;margin-top:1px;margin-left:5px;border: 2px solid #ccc;border-radius: 50%;display: table-cell;width: 20px;height: 18px;font-size: 12px;line-height: 18px;text-align: center;}
.baby-main .label-a{background: #53CAC3;color: #fff;font-size: 12px;display: inline-block;line-height: 20px;margin-left: 10px;padding: 0 5px}
.baby-main .label-date{color: #FD9500;margin-left: 10px;}
.spflag{border-spacing: 6px 0;vertical-align: top;display: inline-block;margin-top: 1px;}
/*2017.3.29意见反馈*/
.cloud{padding-top: 15px;}
.cloud-con{width: 100%;background: url(../images/cloud.png) no-repeat;height: 254px;background-size: 100% 254px;padding-top: 15px;}
.cloud .feed-top1{width:100%;color:#999;position: absolute;top:95px;font-size: 14px;line-height: 20px;}
.cloud .feed-top1 p{width: 75%;margin:0 auto;padding-right: 10px;}
.cloud .feed-top2{position: absolute;width: 100%;top:180px;}
.cloud .feed-top2 a{cursor:pointer;width: 125px;height: 35px;background: #12b7f5;display: inline-block;line-height: 35px;color: #fff;font-size: 16px;}

/*我*/
.pay-top{position:relative;width: 100%;height: 1.250px;background: url(../images/pay_top.png) no-repeat;background-size: 100%;border-bottom: 1px solid #ddd;}
.pay-top dl dt img{width: 50px;height: 50px;border: 2px solid #fff;border-radius: 50%;line-height: 60px;margin-top:5px;margin-left: 5px;}
.pay-top dl dt{width: 70px;height: 60px;float: left;}
.pay-top dl dd{font-size: 15px;margin-top:8px;float: left;height: 45px;line-height: 45px;margin-left: 10px;}

.pay-top dl dd img{vertical-align: middle;display: inline-block;margin-right:5px;margin-top:-5px;margin-left: 10px;width: 18px;height: 15px;}
.pay-top div img{width: 8px;height: 13px;display: inline-block;vertical-align: top;margin-top:1px}

/*无考勤信息*/
.kqnoxx{ text-align:center; background:url(../images/nochuqin.png) no-repeat; width:230px; height:170px; margin:30px auto 0 auto; background-size:100%}
.kqnoxx p{ text-align:center; color:#129b9b; font-size:20px; line-height:20px; padding-top:95px;}
@media only screen and (max-width: 400px) {
	.pay-top div img{margin-top:3px;}
}
@media only screen and (max-width: 360px) {
	.pay-top div img{margin-top:3px;}
}
.pay-top div{margin-top:13px;padding-left: 13px;}
.pay-top .span1{font-size: 10px;}
.pay-top .span2{font-size: 12px;color: #ff632d;display: inline-block;position: absolute;top:23px;right: 15px;}
.pay-top .span2 img{cursor: pointer;}

.top-mess{background: #F3F3F7;height: 1.250px;position: relative;}
.index-top{background: #fff;border-top: 1px solid #ddd;position: absolute;top: 0;}
.index-top dl dd{text-align: left;margin-top:8px;}
.index-top dl dd img{width: 8px;height: 13px;margin-left: 0;}
.index-top dl dd>span{display: block;line-height: 24px;}
.index-top dl dd span i{font-style:normal}
.index-top dl dd span.spanname{font-size: 15px;}
.index-top dl dd span.spantel{font-size: 10px;}
.index-top .span2{}
.index-top .span2 img{vertical-align: middle;margin-left: 10px;}
.index-top .span2 i{background:red;height:70px;display:inline-block;}
.mine-pic{width: 100%;}
.mine-pic img{width: 100%;}

.con-list{background: #fff;margin:0;text-align: center;height: auto;position: relative; }
.con-list .tab-list{width: 96%;margin:0 auto;text-align: left;padding:0 0 15px 0;font-size: 0;}
.con-list .tab-list img{width: 45px;height: 45px;}
.con-list .tab-list dl{cursor:pointer;height:70px;display: inline-block;width: 25%;margin-top:15px;text-align: center;}
.con-list .tab-list dl dd{font-size: 11px;margin-top:7px;}

.line-right{
	height: 207px;
	width: 1px;
	border-right: 1px solid;
	position: absolute;
	top:20px;
	left:50%;
	border-image: -webkit-linear-gradient( rgba(255,255,255,0), rgba(222,222,222,1),rgba(255,255,255,0)) 20 30;
    border-image: -moz-linear-gradient( rgba(255,255,255,0), rgba(222,222,222,1),rgba(255,255,255,0)) 20 30;
	border-image: linear-gradient( rgba(255,255,255,0), rgba(222,222,222,1),rgba(255,255,255,0)) 20 30;
	}

.line-top{
	width: 207px;
	height: 1px;
	border-top: 1px solid;
	position: absolute;
	top:55%;
	left:50%;
	margin-left: -103px;
	border-image: -webkit-linear-gradient( to right,rgba(255,0,0,0), rgba(222,222,222,1),rgba(255,0,0,0)) 30 20;
    border-image: -moz-linear-gradient(  to right,rgba(255,0,0,0), rgba(222,222,222,1),rgba(255,0,0,0)) 30 20;
	border-image: linear-gradient( to right,rgba(255,0,0,0), rgba(222,222,222,1),rgba(255,0,0,0)) 30 20;
	}

@media only screen and (min-width: 640px) {
	.line-top{
		width: 250px;
		margin-left: -125px;
	}
}
@media only screen and (min-width: 780px) {
	.line-top{
		width: 300px;
		margin-left: -150px;
	}

}
@media only screen and (min-width: 1080px) {
	.line-top{
		width: 350px;
		margin-left: -3.250px;
	}
}
@media only screen and (min-width: 1200px) {
	.line-top{
		width: 400px;
		margin-left: -200px;
	}
}
 
.date-time{height: 24px;margin:20px 0 18px 0;}
.title-date{border-radius:3px;height: 24px;width: 150px;color: #fff;font-size: 12px;background: #ddd;margin:0 auto;line-height: 24px;}

/*dl.talk-left{text-align: left;height: auto;}
dl.talk-left .tishi{float:left;border-radius:4px;position: relative;margin-top:-0.6350px;width:auto;word-wrap:break-word;max-width:3.2250px;border:1px solid #e5e4e5;min-height:30px;height: auto;}


dl.talk-right{text-align: right;height: auto;clear: both;}
dl.talk-right .tishi{border-radius:4px;background:#ff632d;float:right;margin-right:65px;position:relative;right:0;margin-top:-35px;min-width:5px;word-wrap:break-word;max-width:3.2250px;border:1px solid #ff632d;min-height:35px;height: auto;}
*//*dl.talk-right .tishi em,.tishi span{font-style:normal;font-size:13px;position:absolute;right:-7px;top:0.150px;color:#ff632d;}
dl.talk-right .tishi span{right:-6px;color:#ff632d;} */

.end-pic img{width: 100%;margin-top:20px;}
.alqijia{ width:100%; background:#f3f3f7; line-height:35px; height:35px; text-align:center; margin-top:10px}



/*详情信息*/
.detail-top{border-bottom: 1px solid #ddd;background: #fff;}
.bor-n{border: none;}
.img-r{position:absolute;right:0;top:0;float:right; margin-top:0.4350px; font-size: 0;}
.img-r img{text-decoration:none;cursor:pointer;width: 18px;height: 15px;vertical-align: top;margin-left: 6px;}
.baby-txt i{font-style: normal;font-size: 10px;color: #666;}
.txt-len{width: 100%;}

.detail-baby{background: #fff;border-bottom: 1px solid #ddd;}
.detail-baby .detail-ul li:first-child{border: none;}
.detail-ul{margin: 0 12px 0 12px;}
.detail-ul li{border-top: 1px solid #ddd;line-height: 20px;font-size: 14px;padding: 13px 0}
.detail-ul li span{float: right;color: #999;}
.detail-ul li .iconfont{vertical-align: top;color: #ccc;}
.detail-ul li .detail-le{width: 30%;display: inline-block;}
.detail-ul li .detail-ri{width: 70%;display: inline-block;float: none;vertical-align: top;}

.detail-fam{background: #fff;margin-top: 10px;border-top: 1px solid #ddd;border-bottom: 1px solid #ddd;}
.detail-fam .det-f1{font-size:0;color:#12b7f5;height: 43px;line-height: 43px;margin-left: 12px;margin-right: 12px;}
.detail-fam .det-f1 img{width: 20px;height: 20px;vertical-align: top;margin-top:12px;}
.detail-fam .det-f1 span{font-size: 14px;margin-left: 6px;margin-right: 6px;}


.btn-send {width: 100%;position:absolute;bottom:0;text-align:center;height:38px;line-height:38px;font-size: 18px;}
.btn-send a {color: #fff;}

/*呼叫客服弹框*/
.popbox{background:#fff;height: auto;margin: auto;border-radius: 10px;position: relative}
.box-top{background: #12b7f5;height: 40px;font-size: 15px;color: #fff;line-height: 40px;text-align: center;}
.box-top img{cursor:pointer;width: 11px;height: 11px;position: absolute;top:15px;right: 15px;}

.box-content{font-size: 15px;height: 2.0150px;padding-top: 30px;text-align: center;}
.box-content p{line-height: 20px;}
.box-content .box-span1{color: #ff632d;}

.sel-btn {position:absolute;bottom:0;width:100%;border-radius: 0 0  10px 10px;border-top:1px solid #ddd;}
.sel-btn a{position: relative;cursor:pointer;color:#999;width: 49.9%;display: inline-block;float: left;height: 40px;font-size: 15px;line-height: 43px;text-align: center;}
.sel-btn a:first-child{border-radius: 0 0 0 10px;}
.sel-btn a:last-child{border-radius: 0 0 10px 0;}
.sel-btn a.sel-current{color: #27c2fc;}
.sel-btn a:first-child:after{content: "";position: absolute;border-right:1px solid #ddd;height: 40px;right: 0;}



/*选择关联幼儿园弹框*/
.text-box{font-size: 12px;color: #333333;padding: 10px;}
.text-box span{color: #ff642e;}

.sel-box .sel-div{cursor:pointer;position:relative;width: 90%;margin:auto;border:1px solid #ddd;margin-bottom: 5px;word-break: break-all;}
.sel-box h2{font-size: 12px;padding-top: 5px;padding-left: 8px;line-height: 14px;color: #333;}
.sel-box p{font-size: 10px;padding-bottom: 5px;padding-left: 8px;line-height: 14px;color: #666;}
.sel-box img{position:absolute;bottom:0;right:0;width: 19px;height: 16px;}
.sel-box  .sel-active{border: 1px solid #ff642f;background: #fff0eb;}

.con-btn{cursor:pointer;font-size: 15px;background: #12b7f5;width: 90%;height: 35px;margin:auto;text-align: center;line-height: 35px;color: #fff;margin-top:14px;}

/*绑定手机号不存在弹框*/
.nonexist-img{margin:auto;text-align: center;}
.nonexist-img img{width: 1.50px;height: 1.50px;margin-top:30px;}
.nonexist-txt{font-size: 14px;width: 65%;margin:auto;line-height: 0.4150px;margin-top: 18px;}


.onetalk .voi-tishi{position: absolute;left:-65px;top:30px;width: 150px;position: relative}
.voi-tishi .notice-p{width: 100px;}
.voi-tishi .notice-p img{width: 12px;height: 15px;}
.voi-time .timer{background: darkcyan;right: 0;position: absolute;margin-right: -20px;}
/*.triangle {width: 0;height: 0;border-top: 5px solid transparent;border-right: 6px solid #ddd;border-bottom: 5px solid transparent;position: absolute;left:-6px;top:9px;}
.triangle2 {width: 0;height: 0;border-top: 5px solid transparent;border-right: 6px solid #fff;border-bottom: 5px solid transparent;position: absolute;left:-6px;top:9px;}*/

.add-btn{text-align: center;}
.add-btn img{margin:10px auto;width: 35px;height: 35px;}


.talk{
	/*position: absolute;*/
	overflow: auto;left: 0;width: 100%;
	-webkit-overflow-scrolling: touch;
  	overflow-y: scroll;
}
.talk dl{clear: both;}
.talk dl img.touxiang,.talk dl img.touxiang{width: 35px;height: 35px;border-radius: 50%;cursor: pointer;}
.talk dl p{text-align:left;font-size: 13px;padding: 5px 8px 5px 10px;line-height: 20px;white-space: pre-wrap;word-break: break-all;}

.talk .tishi p img.voices{cursor:pointer;margin-right:50px;width: 12px;height: 15px;display:inline-block;vertical-align: top;}
.talk .tishi p img.facial{width: 0.3150px;height: 15px;vertical-align: top;margin-top:3px;float: left;margin-right: 3px;}
/*.talk .tishi img.chat-pic{width: 2.4250px;}*/

.talk-left .touxiang{margin-left: 12px;}
.names{overflow: hidden;text-overflow: ellipsis;white-space:nowrap;position: absolute;top:-18px;font-size: 12px;color: #999;width: 190px;}
.talk-right .names{right: 0;}
.chat-pic{border-radius: 4px;/*margin-bottom: -3px;*/}

.talk-left .triangle {width: 0;height: 0;border-top: 5px solid transparent;border-right: 6px solid #ddd;border-bottom: 5px solid transparent;position: absolute;left:-6px;top:9px;}
.talk-left .triangle2 {width: 0;height: 0;border-top: 5px solid transparent;border-right: 6px solid #fff;border-bottom: 5px solid transparent;position: absolute;left:-6px;top:9px;}
/*.talk-right .triangle {width: 0;height: 0;border-top:  5px solid transparent;border-left:  6px solid #ff632d;border-bottom:  5px solid transparent;position: absolute;right:-6px;top:9px;}
*/.talk-right .triangle2 {width: 0;height: 0;border-top:  5px solid transparent;border-left:  5px solid #ff632d;border-bottom:  5px solid transparent;position: absolute;right:-5px;top:9px;}

dl.talk-left{text-align: left;height: auto;z-index: -9;}
dl.talk-left .tishi{top:15px;background: #fff;float:left;border-radius:4px;position: relative;margin-bottom:15px;width:auto;word-wrap:break-word;max-width:162px;border:1px solid #e5e4e5;min-height:30px;height: auto;font-size: 0;}
.voi-time{position: absolute;bottom:0;right:0;display: inline-block;margin-right: -28px;margin-bottom: 4px;font-size: 10px;}
.voi-time i{font-style: normal;float: left;color: #b5b5b5;}
.voi-time u{width: 5px;height: 5px;background: #ff3131;float: right;border-radius: 50%;top: 40%;position: absolute;}
.tishi-time{}

.touxiang{border-radius: 50%;}
dl.talk-right{text-align: right;height: auto;clear: both;}
dl.talk-right img.touxiang{margin-right: 15px;}
dl.talk-right .tishi{margin-bottom: 15px;border-radius:4px;background:#ff632d;float:right;margin-right:60px;position:relative;right:0;margin-top:-22px;min-width:5px;word-wrap:break-word;max-width:162px;min-height:30px;height: auto;font-size: 0;}
dl.talk-right .tishi p{color: #fff;text-align: left;}
dl.talk-right .tishi p img.voices2{margin-left:50px;margin-right: 0;cursor: pointer;}
.talk-right .voi-time{position: absolute;left: 0;margin-left: -0.6250px;}
.talk-right .voi-time u{float:left;position: absolute;left: 18px;}

.shadow-pic{border-radius:4px;width: 100%;height: 100%;background: rgba(0,0,0,0.3);}
.shadow-pic img.load{cursor:pointer;position: absolute;top:50%;margin-top:-8px;left:50%;margin-left: -8px;width: 0.3150px;height: 0.3150px;}
.shadow-pic span{position: absolute;bottom: 5px;left: 5px;color: #eae3d9;}
.shadow-pic img.vedio{width: 0.250px;height: 0.250px;position: absolute;bottom: 5px;right: 5px;}


/*关联宝宝*/
.rela-cure{padding-top:14px;padding-left: 12px;padding-right: 12px;width: 100%;height: 4.5150px;}
.cure-txt{background: url(../images/rela_pic.png)no-repeat;background-size:100% 4.5150px;width: 100%;height: 4.5150px;}
.rela-cure .rela-txt{width:250px;margin:0 auto;font-size: 12px;padding-top:115px;}
.rela-cure .rela-txt .rela-hint1{color: #ff632d;}
.rela-cure .rela-txt .rela-hint2{margin-top: 10px;line-height: 20px;width: 200px;}
.rela-cure .rela-txt .rela-hint2 p{overflow: hidden;white-space: nowrap;text-overflow: ellipsis;}


.rela-baby{margin-top:40px;width: 100%;padding-left: 19px;padding-bottom: 10px;}
.rela-baby .rela-left{width: 55%;float: left;}
.rela-baby .rela-right{width: 40%;float: left;margin-bottom: 15px;}

.rela-baby  .rela-step{position:relative;}
.rela-step .step-con{color:#fff;position:relative;font-size:10px;background: #e6a855;width: 160px;border-radius: 2px;}
.rela-step .step-con2{background: #abc564;}
.rela-step .step-con p{margin-left:25px;padding-top: 3px;padding-bottom:3px;line-height: 13px;padding-right: 5px;}
.rela-step .step-con img{position:absolute;top:-15px;left:0;width: 25px;height: 30px;}
.rela-step .img-line{width: 0.250px;height: 62px;margin-top: 5px;margin-bottom: 5px;}
.rela-step:nth-of-type(1) .img-line{margin-left: 60px;}
.rela-step:nth-of-type(2) .img-line{margin-left: 2.100px;}
.rela-step:nth-of-type(3) .img-line{margin-left: 30px;}
.rela-step .rela-color1{color: #946525;}
.rela-step .rela-color2{color: #608b34;}

.rela-right img{margin-left: -10px;display: block;}
.rela-right img:nth-of-type(1){width: 70px;height: 70px;margin-left: 35px;margin-top:-5px;}
.rela-right img:nth-of-type(2){width: 2.8150px;height: 2.7350px;margin-top: 35px;}
.rela-right img:nth-of-type(3){width: 2.3400px;height: 138px;margin-top:50px;}
@media only screen and (max-width: 360px) {
	.rela-right img:nth-of-type(2){
		margin-top:50px;
	}
	.rela-right img:nth-of-type(3){
		margin-top: 90px;
	}
}

.share{clear: both;border-top:1px solid #d3d2d2;display:-webkit-box;display: -webkit-flex;display: flex;height: 50px;line-height: 50px;}
.share a{cursor:pointer;margin-left:0.7150px;width: 50px;height: 25px;background: #ffa922;color: #fff;font-size: 14px;text-align: center;line-height: 25px;margin-top:13px;}
.share p{color: #12b7f5;font-size: 12px;text-align: center;-webkit-box-flex:1;-webkit-flex:1;flex: 1;}
.share p span{text-decoration: underline;cursor: pointer;}


/*意见反馈-feedback2*/
.cloud{padding-top: 15px;}
.cloud-con{width: 100%;background: url(../images/cloud.png) no-repeat;height: 254px;background-size: 100% 254px;padding-top: 15px;}
.cloud .feed-top1{width:100%;color:#999;position: absolute;top:95px;font-size: 14px;line-height: 20px;}
.cloud .feed-top1 textarea{font-size:14px;border:none;outline:none;width: 75%;margin:0 auto;padding-right: 10px;}
.cloud .feed-top1 textarea::-webkit-input-placeholder{font-size: 14px;line-height: 20px;}
.cloud .feed-top1 textarea::-moz-placeholder{font-size: 14px;line-height: 20px;}
.cloud .feed-top1 textarea:-ms-input-placeholder {font-size: 14px;line-height: 20px;}
.cloud .feed-top2{position: absolute;width: 100%;top:180px;}
.cloud .feed-top2 a{cursor:pointer;width: 125px;height: 35px;background: #12b7f5;display: inline-block;line-height: 35px;color: #fff;font-size: 16px;}

.date-time{height: 24px;margin:20px 0 18px 0;}
.title-date{border-radius:3px;height: 24px;width: 150px;color: #fff;font-size: 12px;background: #ddd;margin:0 auto;line-height: 24px;}

.end-pic img{width: 100%;margin-top:20px;}

.alqijia{ width:100%; background:#f3f3f7; line-height:35px; height:35px; text-align:center; margin-top:10px}

/*你还没有红包记录*/


.nored_bao{width:100%;margin:0 auto;}
.nored_bao_pic{width:56%;margin:0 auto;margin-top: 30%}

.nored_bao_pic dl dt img{width: 2.100px}
.nored_bao_pic dl dt{text-align: center;}
.nored_bao_pic dl dd{font-size: 13px;color: #999999;text-align: center;margin-top: 24px;}

.redbao_list{width:100%;margin:0 auto;background-color: #fff}

.redbao_list_title{width:100%;background-color:#fff;line-height: 40px}

.redbao_list_title span{width:100%;display:inline-block;font-size:13px;color:#d91e06;line-height: 45px}
.redbao_list_title i.iconfont{position: absolute;right: 12px;color: #D4D4D4;}
.redbao_list_title span:before {
    background: #d91e06;
    content: "";
    display: inline-block;
    width: 2px;
    height: 15px;
    margin: 10px 10px -2px 0px;
}

.redbao_list_time{width:100%;height:40px;background-color:#fd784b}
.redbao_list_time p{font-size:14px;line-height:40px;text-align:center;color:#fff; float: left;}
.redbao_list_time div{margin:0 auto;margin-left: 30px}
.redbao_list_time p span{
    width: 15px;
    height: 15px;
    background-color: #7c5450;
    border-radius: 4px;
       padding: 1px 3px 1px 3px;
    margin-left: 5px;
    margin-right: 5px;
    font-size: 12px;}

.redbao_list_titles{width:100%;height:45px;background-color:#fff}
.redbao_list_titles span{width:100%;display:inline-block;font-size:14px;color:#333333; line-height: 45px;padding-left:10px;border-bottom: 1px solid #eee;}
.redbao_list_titles span:after {
    content: "";
    display: inline-block;
    /* width: 5px; */
    height: 25px;
    background: url(../images/arr_right.png) no-repeat;
    float: right;
    padding-right: 10px;
    margin-top: 15px;
    background-size: 100%;
	 margin-right: 5px
}

.redcolor span{color:#12b7f5}
.redcolor span:before{background-color:#12b7f5}
.redbao_table{width:100%;margin:0 auto;border-top: 1px solid #eee;padding-bottom: 10px}
.redbao_table  h2{font-size: 13px;font-weight: initial;width: 87%;margin: 0px auto;color:#fd784b;margin-top:10px;margin-bottom:5px}

.redbao_table ul{font-size:13px;font-weight: initial;margin: 0 auto;color:#7f7f7f;}

.redbao_table ul li{margin-bottom:5px}
.redbao_table ul li label{text-align: right;width: 1.7150px;display: inline-block}
.redbao_table ul li span{width:65%;display: inline-flex;line-height: 18px}


/*红包攻略*/

.bonu_divs{width: 100%;background: url(../images/reds.png) no-repeat;     min-height: 5.150px;background-size: 100%}

.bonu_title{font-size:15px;text-align:center;color:#fff;padding-top:25px}
.bonu_zis{    width: 89%;
    margin: 0 auto;
    background-color: #ff6a63;
    border: 1px dashed #f8ef24;
    border-radius: 10px;
    padding: 20px;
	    margin-top: 30px
	}

.bonu_zis_a{font-size:14px;color:#f8ef24}
.bonu_zis p{font-size: 14px;
    color: #fff;
    padding-top: 5px;}
.bonu_zis p span{

	display: inline-flex;
	font-size: 14px;
    color: #fff;
	width: 82%

}

@media screen and (width:768px){
.bonu_divs{min-height: 6.150px;}
.bonu_jilus{    margin-top: 10px}
}

.bonu_jiang{width:100%;margin:0 auto;background:url(../images/red_bg.jpg) no-repeat;background-size:100%;height:150px;}

.bonu_jilu table tr td div{width:90px;height:90px;border:5px solid #12b7f5;border-radius:50%;text-align: center;margin: 0 auto}

.bonu_jilu table tr td{text-align:center}

.bonu_jilu table{width: 99%;margin: 0 auto;padding-top: 20px;}

.bonu-shou{display:inline-block;background-color:#c5d8e1;padding: 3px 0.150px 3px 0.150px;font-size:15px}
.bonu-shous{display:inline-block;background-color:#c6e9fa;padding: 3px 0.150px 3px 0.150px;font-size:15px}

.bonu_shu span{    display: block;margin:0 auto; font-size:15px;padding-bottom: 5px}
.jiangli{width: 50px;border-bottom: 1px solid #ccc;    padding-top: 15px}

.bonu_foot div{float:left;font-size:15px;margin-left:15px;}
.bonu_foots{width:100%;margin:0 auto;}

.bonu_foot{width:100%;margin:0 auto;    overflow: hidden}

.bonu_foot div span img{    width: 45px;padding-left: 15px;}

.bonu_diqu span{    width: 2.200px;
    display: inline-block;
    background-color: #dcf4fe;
    text-align: left;
    padding: 5px;
    padding: 5px 3px 5px 5px;
    font-size: 16px


	}

.red_bonus{    width: 50px;
    height: 50px;
    border: 3px solid #999999;
    display: block;
    border-radius: 50%;
    text-align: center;
    padding-top: 11px}



/*bonu_jilus*/


.bonu_jilus table tr td span{}
.bonu_jilus table tr td div{width:50px;height:50px;border:5px solid #cccccc;border-radius:50%;text-align: center;margin: 0 auto;float: right;}
.bonu_jilus table tr td{text-align:left; font-size: 13px;}
.bonu_jilus{margin-bottom: 10px}
.bonu_jilus table{width: 98%;margin: 0 auto;    border-bottom: 1px dashed #dddddd;padding-bottom: 5px}
.bonu-shou_one{display:inline-block;background-color:#c5d8e1;     padding: 0.150px 8px 5px 10px;}
.bonu-shous_one{display: inline-block; padding: 0.150px 8px 5px 10px;margin-right: 10px}

.bonu_shu_one span{
    display: block;
    margin: 0 auto;
    font-size: 13px;
    padding-bottom: 5px;
	color:#e36355
}
.jiangli_one{padding-top: 14px;}
.bonu_shu_one .jiangli_ones{padding-top: 14px; color:#12b7f5;}
.bonu_table_one{width:27%;}

.look_list{width:100%;margin:0 auto;background-color: #fff}

.look_list_title{width:100%;height:45px;background-color:#fff}

.look_list_title span{width:100%;display:inline-block;font-size:15px;color:#333333;    line-height: 45px;padding-left:10px;border-bottom:1px solid #eee}
/*幼儿园缴费处理*/

.jhmy_jlmen{
	width:90%;
	margin:0  auto;
	border-left:1px solid #dddddd;
	margin-top: 15px

}
.jhmy_right u.jluico{
	background:url(../images/yuan1.png) no-repeat;
	width:10px;
	height:10px;
	background-size:100%;
	display:block;
	position:absolute;
	margin-left:-5px;
	margin-top: 14px;

}
.jhmy_right u.jluico:first-child{
	margin-top: 0;
}
.jhmy_right u.jluicos{
	background:url(../images/yuan2.png) no-repeat;
	width:11px;
	height:11px;
	background-size:100%;
	display:block;
	position:absolute;
	margin-left:-5px;
	margin-top: 19px;

}


.jhmy_right ul {
    width: 96%;
    margin: 0 0 0 10px;

	margin-top: 12px;
}
.jhmy_right ul:first-child{
	margin-top: 0;
}
.jhmy_right li.gry {
    color: #948f8f;
    text-align: left;

    text-overflow: ellipsis;
	    font-size: 13px
}
.jhmy_right li.grys {
    color: #333333;
    text-align: left;
    position: relative;
    top: 3px;
    text-overflow: ellipsis;
    font-size: 14px
}
.tj_foots{
    width: 91%;
    margin: auto;
    height: 25px;
    border-top: 1px solid rgba(222,222,222,1);

    margin-top: 25px
	}
.tj_foots h2{
    font-size: 13px;
    text-align: center;
    color: #ff1e1e;
    font-weight: initial;
    padding-top: 15px

	}

.tj_foots_pic{
	    margin-top: 60px;
}

.tj_foots_pic img{width:100%;margin:0 auto}


	.tj_foots p{
    font-size: 13px;
    text-align: left;
    color: #ff1e1e;
    font-weight: initial;
    padding-top: 15px;
    line-height: 10px

	}
.ti_foots_ff{

	width: 98%;
    height: 35px;
    background-color: #53cac3;
    line-height: 35px;
    text-align: center;
    font-size: 15px;
    color: #fff;
    margin: 0 auto;
}

/*病假详情弹框*/
.askleave-box{position:fixed;top:0;left:0;right:0;bottom:0;background: rgba(0,0,0,0.2);}
.askleave{background:#fff;border-radius:10px;margin: 0 auto;font-size: 14px;width: 250px;text-align:center;position: absolute;top:50%;margin-top:-3.250px;left: 50%;margin-left: -125px;}
.askleave h5{border-radius:10px 10px 0 0;font-size: 15px;background: #53CAC3;color: #fff;height: 37px;line-height: 37px;}
.askleave ul li{padding:0 10px;clear:both;line-height: 40px;border-bottom: 1px solid #ECECEC;min-height: 40px;}
.askleave ul li label{float: left;}
.askleave ul li span{float: right;}
.ask-date{position: relative;}
.ask-date p{border-bottom:1px solid #ECECEC;margin-right: 50px;}
.ask-date p:nth-child(2){border: none;}
.ask-date .date-num{border-radius:7px;position:absolute;top:0;right:0;margin-top:9px;border: 1px solid #A3DFDD;height: 63px;width: 30px;text-align:center;line-height: 20px;padding-top: 3px;}
i{font-style: normal;}
.ask-type{font-size:0;width: 143px;height: 27px;display: inline-block;border-radius: 10px;border: 1px solid #FE632D;}
.ask-type i{position:relative;cursor:pointer;color:#666666;text-align:center;display: inline-block;vertical-align:top;width: 33.33%;font-size: 14px;height: 26px;line-height: 26px;}
.ask-type .sel-type{background: #FE632D;color: #fff;}
.ask-type i:nth-child(1){border-radius: 9px 0 0 9px;}
.ask-type i:nth-child(3){border-radius: 0 9px 9px 0;}
.ask-type i:after{content: "";border-right: 1px solid red;right: 0;position: absolute;height: 26px;}
.ask-type i:nth-child(3):after{border-right:none ;}
.askleave textarea{outline:none;width: 160px;height: 65px;border: 1px solid #F0F0F0;margin-top: 6px}
.ask-name input,.get-time input{height:25px;line-height:25px;margin-top:8px;text-align: right;font-size: 14px;border: none;outline: none;}
.ask-name input:-ms-input-placeholder {color: #FE632D;}
.ask-name input::-webkit-input-placeholder{color: #FE632D;}
.detail-btn{font-size: 0;border-radius: 0 0 10px 10px;}
.detail-btn a{width: 50%;position:relative;display: inline-block;height: 43px;line-height: 43px;font-size: 15px;}
.detail-btn a:nth-child(1):after{content: "";position: absolute;height: 43px;right: 0;border-right: 1px solid #ececec;}
.ill-type{clear: both;margin-bottom: 10px;}
.ill-type i{cursor:pointer;display: inline-block;height: 20px;line-height: 20px;padding: 0 3px;margin-left: 2px;margin-right: 2px;font-size: 12px;}
.ill-type i.ill-btn{background: #FE632D;color: #fff;border-radius: 3px;}
/*修改食谱*/
.recipe-list li{height: 25px;line-height: 25px;}
.recipe-list li label{position: relative;}
.recipe-list li label:after{content: "";position: absolute;border-bottom: 1px solid red;bottom: 0;left:0;width: 100%;}
.recipe-list li span { float: right;}
.recipe-list li i{color: #F88A65;display: inline-block;}
.recipe-list li i img{width: 15px;height: 12px;margin-top: 8px;vertical-align: top;margin-left: 5px;cursor: pointer;}
.recipe-top{height: 0.7150px;line-height: 0.7150px;background: #f3f3f7;padding-left: 10px}
.recipe-top img{width: 18px;height: 18px;vertical-align: top;margin-top: 0.17250px;margin-right: .50px}
.food-con .add-con{margin-left: 11px;}
.food-con .addpic-list{margin-top: 10px;}
.food-con .edd-pic span{cursor: pointer;}
.food-con .add-pic{cursor: pointer;}
.recipe-list em{font-style: normal;}
/*服药登记*/
.tit-history{margin:auto 10px;text-align:center;height: 40px;line-height: 40px;position: relative;}
.tit-history .tit-left img{margin-right:5px;width: 16px;height: 14px;font-size: 14px;vertical-align: top;margin-top: 14px;}
.tit-history .tit-left{float: left;cursor: pointer;position: absolute;left:0;}
.tit-history .tit-center{font-size: 15px;}
.tit-history .tit-right{font-size: 10px;float: right;position: absolute;right:0;}
.medi-btn{width: 50px;height: 20px;line-height: 20px;background: #12b7f5;display: inline-block;color: #fff;}
.check-sec{margin-bottom: 10px;background: #fff;padding-bottom:15px ;}
.check-tit{position:relative;border-bottom:1px solid #E8E8E8;background: #fff;height: 43px;line-height: 43px;font-size: 14px;}
.check-tit .check-left{color:#FF6A56;font-size: 15px;display: inline-block;position: relative;padding-left: 10px;}
.check-tit .check-left:before{content: "";position: absolute;border-left:4px solid #FF6A56;height: 15px;left: 0;top:50%;margin-top:-8px;}
.check-tit .check-right{cursor:pointer;font-size: 14px;float: right;margin-right: 12px;color: #666;position: absolute;right: 0;}
.check-tit .teacher-left{color: #FB9600;}
.check-tit .teacher-left:before{content: "";position: absolute;border-left:4px solid #FE9900;height: 15px;left: 0;top:50%;margin-top:-8px;}
.check-tit .mine-left{color: #12B8F6;}
.check-tit .mine-left:before{content: "";position: absolute;border-left:4px solid #12B8F6;height: 15px;left: 0;top:50%;margin-top:-8px;}
.check-list{background: #fff;border-bottom: 1px solid #e8e8e8;}
.check-list li{padding-top:13px;padding-bottom:13px;line-height:18px;border-bottom: 1px solid #e8e8e8;margin: auto 10px;}
.check-list li label{font-size: 15px;display: inline-block;width: 35%;}
.check-list li label img{width: 18px;height: 18px;vertical-align: top;margin-right: 10px;}
.check-list li .medi-right{font-size: 14px;color: #666;vertical-align:top;display: inline-block;width: 63%;text-align: right;}
.check-list>li:last-child{border-bottom: none;}
.check-list li p{line-height: 25px;font-size: 14px;}
.check-list li .use-color{color: #5EB14A;}
.check-list li .use-color2{color: #F85141;}
.check-list li input[type="radio"]{outline:none;-webkit-appearance: none;width: 19px;height: 19px;border: 1px solid #e8e8e8;border-radius: 50%;margin-right: 5px;}
.check-list li input[type="radio"]:checked{background: url(../images/radio_sel.png) center center; background-size: 19px 19px;}
.check-list li .sel-inp span{margin-left: 10px;}
.medi-tab{height: 43px;line-height: 43px;margin: auto 10px;border-bottom: 1px solid #e8e8e8;overflow-x: auto;white-space: nowrap;}
.medi-tab span{color:#A4A4A4;height: 39px;line-height: 39px;display: inline-block;margin-left: 8px;padding: 0 5px;cursor: pointer;}
.medi-tab span.medi-sel{color: #53CAC4;border-bottom: 2px solid #53CAC4;}
.posi-stute{position: relative;}
.posi-img{position: absolute;bottom: 0;right: 100px;width: 1.3400px;height: 43px;}
.use-txt{margin-top:13px;margin-left: 28px;color: #6A6A6A;}
.use-txt span{margin-right: 10px;}
.medi-method{/*margin-left:25px;*/font-size:14px;vertical-align:top;width: 60%;display: inline-block;line-height: 18px;color: #666;}
.medi-pho{width: 1.8250px;height: 1.8250px}
.pho-list{font-size: 0;margin: 0;}
.pho-medi{display: inline-block;width: 33.3%;text-align: center;margin-top: 10px;vertical-align: top}
.pho-medi div{position: relative;width: 91px;height: 91px;margin: auto;overflow: hidden;}
.arrow-right{width: 8px;height: 13px;vertical-align: top;margin-top: 15px;margin-left: 10px;}
.arrow-down{width: 13px;height: 8px;vertical-align: top;margin-top: 18px;margin-left: 10px;}
.use-btn a{margin-top:15px;}
.use-btn{padding-bottom: 15px;}
.edi-mess{text-align: center;}
.textarea-inp{margin:0 auto;width: 90%;border: 1px solid #e8e8e8;height: 59px;padding: 5px}
.edi-mess .edi-inp{font-size:14px;color:#686868;text-align: left;padding: 0 10px;height: 43px;line-height: 43px;border-bottom: 1px solid #e8e8e8;}
.edi-inp span img{margin-right:10px;width: 13px;height: 18px;vertical-align: top;margin-top:13px;}
.edi-inp a{font-size:12px;text-align:center;float: right;background: #53CAC4;color: #fff;width: 33px;height: 22px;line-height: 22px;display: inline-block;margin-top: 0.250px;}
.edi-inp span{cursor: pointer;}
.edi-inp i{float:right;display: inline-block;color: #12B7F5;}
.edi-inp i img{width: 15px;height: 15px;vertical-align: top;margin-top: 14px;margin-right: 5px;}
.edi-press{padding: 18px 0;}
.edi-press h5{font-size: 14px;margin-bottom: 15px;}
.edi-press img{width: 91px;height: 91px;cursor: pointer;}
.pub-btn{background: #12B8F6;color: #fff;height: 20px;line-height: 21px;display: inline-block;padding: 1px 5px}
.check-list .use-time{font-size: 0;color: #666;}
.check-list .use-time .time-left{width: 30%;display: inline-block;}
.check-list .use-time>div{text-align: right;display: inline-block;width: 70%;vertical-align: top;}
.check-list .use-time p{height: 38px;line-height: 38px;margin-top: -8px}
.check-list .use-time p img{width: 15px;height: 15px;vertical-align: top;margin-top:12px;cursor: pointer;}
.check-list .use-time input[type="text"]{border:1px solid #ECECEC;width: 115px;height: 22px;margin-left: 5px;margin-right: 5px;}
.check-list .use-time select{border:1px solid #ECECEC;padding:0 5px;height: 22px;}
.check-list .use-time .use-dose{width: 80%;margin-top: 8px;font-size: 14px;}
.check-list .use-time .use-dose em{border:1px solid #ececec;height: 22px;line-height: 22px;display: inline-block;width: 132px;}
.check-list .use-time .use-dose input[type="text"]{font-size: 10px;width: 35px;margin: 0;height: 21px;border: none;vertical-align: top;outline: none;}
.check-list .use-time .use-dose select{width: 90px;font-size: 10px;height: 21px;border: none;vertical-align: top;outline: none}
.sel-label{width: 90% !important;}
.sel-label select{width: 60px;}
.repar-sel{color: #12B8F6;text-decoration: underline;cursor: pointer;}
/*.pho-list div{position: relative;}*/
.pho-list div span{cursor: pointer;background: rgba(6,6,6,0.5);top: 0;right: 0;font-size: 15px;position: absolute;top: 0;right: 0;width: 20px;height: 20px;line-height: 20px;color: #fff;text-align: center;display: inline-block;}
.add-div{border: 1px dashed #e8e8e8;}
.add-div img{width: 22px;height: 19px;margin-top:20px;}
.add-div p{font-size: 10px;color: #999;}
.sear-top{background: #fff;height: 44px;line-height: 44px;font-size: 0;border-bottom: 1px solid #E6E6E6;}
.sear-top img{width: 16px;height: 16px;vertical-align: top;margin-top:14px;margin-left: 10px;}
.search-left,.search-right{display: inline-block;vertical-align: top;}
.search-left{width: 80%;}
.search-left input{border:none;height: 25px;line-height: 25px;vertical-align: top;margin-top: 10px;margin-left: 5px;width: 80%;}
.search-right{cursor:pointer;color:#666;width: 20%;font-size: 14px;text-align: center;position: relative}
.search-right:before{content: "";border-left: 1px solid #E6E6E6;position: absolute;height: 30px;left: 0;margin-top: 7px}
.sear-tit{color:#666;height: 34px;line-height: 34px;font-size: 14px;padding-left: 10px;padding-right: 10px;}
.search-list{padding:3px 10px 15px 10px;}
.search-list li{font-size:14px;border: 1px solid #e8e8e8;display: inline-block;padding:3px 5px;margin-top: 5px;margin-right: 5px;}
.sear-tit a{color: #A0A0A0;float: right;}
.search-div{margin-bottom: 10px;background: #fff;}
.sear-txt{position: absolute;width: 100%;top:45px;min-height: 240px;}
.ill-typical{font-size: 0;height: 40px;line-height: 40px;border-bottom: 1px solid #d3d2d2;}
.ill-typical li{width: 25%;display: inline-block;font-size: 15px;text-align: center;color: #666;position: relative;}
.ill-typical li:after{content: "";position: absolute;right: 0;top:0;border-right: 1px solid #e6e6e6;height: 20px;margin-top:10px;}
.ill-typical li:last-child:after{border: none;}
.curr-btn{color: #1AB3ED;}
.medicine-list li{height: 40px;line-height: 40px;border-bottom: 1px solid #e6e6e6;margin: 0 10px;font-size: 13px;color: #666;}
/*.medicine-list li span{font-size:10px;margin-right:9px;width: 15px;height: 15px;display: inline-block;background: #D9D9D9;line-height: 15px;text-align: center;color: #fff;border-radius: 50%;}
*/.medi-name{width: 65%;margin-right: 10px;height: 22px;border: 1px solid #ececec;}
.medicine-list li span{color: #3AC0F3;}
.talk-left dd{margin-top:-15px;}
.repair-bill{margin: 0 15px;font-size: 13px;padding-bottom: 10px;}
.repair-bill input{margin-right: 5px}
.green-color{color: #58B040;}
.red-color{color: #F83F2D;}
.rep-btn{color: #7AD1CB;text-decoration: underline;float: right;font-size: 13px;}
.sel-btn1{font-size:11px;margin-right:5px;height: 21px;display: inline-block;color: #fff;display: inline-block;line-height: 24px;padding: 0 5px;}
.medi-check{width: 49px;margin-right:10px;height: 20px;line-height:22px;float:right;padding:0 3px;margin-top:6px;font-size: 10px;display: inline-block;text-align: center;color: #fff;border:1px solid #fff;}
.ye-left{padding-left:10px;padding-right: 5px;}
.medi-ul{color: #333;}
.medi-ul .sel-li{color: #A0A0A0;}
.medi-ul li a.medi-color1{color: #53CAC3;border: 1px solid #53CAC3;/*margin-top: 10px;*/}
.medi-ul li a.medi-color2{color: #FFA930;border: 1px solid #FFA930;/*margin-top: 10px;*/}
.medi-ul li a.medi-color3{color: #56CFF0;border: 1px solid #56CFF0;/*margin-top: 10px;*/}
.medi-ul li i{display: inline-block;width: 84%;text-align: right;float: right;}
.medi-ul li span{float: none;}
.medi-ul li span a{float: none;}
.medi-ul li em{color: #53CAC3;}
.medi-tit{width: 65px;margin-right:10px;height: 20px;line-height:22px;float:right;padding:0 3px;/*margin-top:6px;*/border: none;font-size: 10px;display: inline-block;text-align: center;color: #333;}
.font-tit{font-size: 14px;}
/*确认订单*/
.bill-top dl dt img{border-radius: 0;}
.bill-top .pay-txt{font-size: 12px;color: #3C3C3C;}
.bill-top .pay-num{font-size: 12px;color: #F83C3B;margin-left: 10px;}
.bill-top .pay-num i{font-size: 15px;color: #F83C3B;}
.sure-bill .detail-top{border-bottom: none;}
.sure-bill .baby-main{border-bottom: none;}
.sure-bill .baby-main img{border-radius: 0;}
.sure-bill .detail-ul{background: #fff;margin: 0;padding: 0 12px 0 12px;margin-top: 10px;}
.sure-bill .detail-ul li:first-child{border-top: none;}
.sure-bill .detail-ul li span{color: #434343;}
.bill-note{color: #A0A0A0;font-size: 12px;text-align: center;margin-top: 13px;}
.bill-num{color: #FA6261;font-size: 12px;text-align: center;position: absolute;bottom: 25px;width: 100%;}
.medi-name{width: 65%;margin-right: 10px;height: 22px;border: 1px solid #ececec;}
/*班级服药信息*/
.yemain_ls .medi-top{font-size: 0;}
.yemain_ls .medi-top span{display: inline-block;width: 50%;font-size: 13px;text-align: center;}
.yemain_ls .medi-top span img{width: 13px;height: 8px;vertical-align: top;margin-top:14px;margin-left:5px ;}
.yemain_ls .medi-top span a{color: #fff;}
.classmedi{padding: 0 15px;margin-top: 5px;}
.classmedi li{margin-bottom:8px;height: 50px;line-height:50px;border: 1px solid #D7D7D7;font-size: 11px;color: #333;}
.classmedi li span{float: right;display: inline-block;width: 50%;text-align: center;}
.classmedi li label{display: inline-block;width: 42%;text-align: center; overflow: hidden; white-space : nowrap; text-overflow: ellipsis;}

/*过敏管理*/
.alle-con{background: #fff;min-height: 62px;font-size: 0;position: relative;padding: 0 5px;}
.alle-con .alle-left{width: 26%;height: 62px;}
.alle-con .alle-left img{width: 23px;height: 23px;margin-top: 9px;}
.alle-con .alle-left,.alle-con ul{display: inline-block;vertical-align: top;font-size: 14px;}
.alle-con .alle-left .type-div{position: absolute;top:50%;margin-top: -31px;width:24%;height: 62px;text-align: center;}
.alle-con ul{width: 74%;margin: 15px 0;}
.alle-con ul img{cursor:pointer;margin:0 7px;width: 18px;height: 18px;vertical-align: top;margin-top: 7px;}
.alle-con ul li{line-height: 32px;height:32px;font-size: 0;}
.alle-con ul li span{text-align:center;width: 42%;display: inline-block;font-size: 14px;}
.alle-con ul li i{margin-right: 5px;}
.alle1{color: #FED144;}
.alle2{color: #FE980F}
.alle3{color: #FF0302;}
.alle-con ul li label{    text-align:left;text-overflow: ellipsis;white-space: nowrap;overflow: hidden;width: 65px;display: inline-block;vertical-align: top;}
.popbox .alle-con{border-bottom: 1px solid #F1F1F1;}
.alle .sel-btn{position: static;height: 40px;background: #fff;border-top: 0;}
.alle .sel-btn a{width: 100%;border-right: 0;}
.alle .alle-p{height: 44px;line-height: 44px;padding-left: 15px;font-size: 15px;border-bottom: 1px solid #f1f1f1;}
.alle .alle-p span{color: #FD673E;}

.alle-tab{width: 100%;}
.alle-tab .tab-tit{background: #53CAC4;color: #fff;}
.alle-tab tr{color:#323232;height: 40px;line-height: 25px;} /*折行时行高调整*/
.alle-tab tr td{text-align: center;border-bottom: 1px solid #eee;}

/*问候宝宝家长*/
.greet-box {background: #fff;}
.greet-box .box-top{background: #53CAC4;}
.greet-con h5{color: #333;font-size: 13px;padding: 6px 15px;height: 20px;line-height: 20px}
.greet-box i{color: #F86F43;}
.greet-con .greet-right{float: right;}
.greet-con .greet-right label{margin-right: 5px;}
.greet-con .greet-right em {color: #333;}
.greet-box input[type="radio"]{vertical-align:top;outline:none;-webkit-appearance: none;width: 19px;height: 19px;border: 1px solid #e8e8e8;border-radius: 50%;margin-right: 5px;}
.greet-box input[type="radio"]:checked{background: url(../images/sel_btn.png) center center; background-size: 19px 19px;}
.greet-box .label-txt1,.label-txt2,.label-txt3{margin-top: 0;height: 40px;}
.light-info{height: 3.50px;overflow-y: auto;border-bottom: 1px solid #E6E6E6;}
.light-info dl.sel-dl{border: 1px solid #ddd;margin:0 15px;margin-bottom:10px;position: relative;height: 40px;}
.light-info dl.sel-dl dd{font-size:13px;margin-left:23px;margin-top:3px}
.light-info dl.sel-dl .btn-lgt{cursor:pointer;width: 57px;height: 20px;background: #ff632d;color: #fff;display: inline-block;font-size: 11px;text-align: center;line-height: 20px;margin-left: 13px;}
.context-text dl.pay-checked{border: 1px solid #ff632d;}
.context-text dl dd img{width: 10px;height: 12px;vertical-align: top;margin-top: 2px;margin-right: 5px;}
.context-text dl dd p{height: 17px;line-height: 17px}
.context-text dl dd p.pho-num{color: #6D6D6D;}
.context-text dl .checkicon{width: 25px;height: 25px;position: absolute;right:0;bottom:0;}
.light-info dl.sel-dl dt{position: absolute;right:25px;background: none;top: 50%;margin-top: -10px;}
.light-info dl.sel-dl dt img.img-first{display:inline-block;width: 20px;height: 20px;}
.light-info dl.sel-dl dt img.img-second{display:inline-block;width: 13px;height: 11px;position: absolute;right: -5px;top:-5px;}
.light-info dl.sel-dl dt .rig-div{height: 20px;width:20px;display: inline-block;position: relative;}
.greet-txt{margin: 0 15px;}
.greet-txt h5{font-size: 13px;margin: 8px 0;}
.greet-txt .edi-con{border: 1px solid #E6E6E6;}
.greet-txt .edi-txt{height: 95px;padding: 10px 10px 0 10px;}
.greet-txt .edi-txt textarea{height: 70px;width: 100%;border: none;}
.greet-txt .edi-txt p{text-align: right;color: #7E7E7E;}
.greet-txt .edi-con h6{padding:0 5px;color: #7E7E7E;border-top:1px solid #E6E6E6;height: 25px;line-height: 25px;}
.greet-txt .edi-con a{float: right;color: #61BEFB;text-decoration: underline;}
.greet-box .sel-btn{height: 40px;position: static;margin-top: 15px;}
.greet-box .sel-btn a.sel-current{color: #53CAC4;}
/*服药管理使用时间*/
.state-dl{min-height: 52px;border: 1px solid #ddd;margin-top: 10px;position: relative}
.state-dl em{height: 100%;margin-top: 0;line-height: 16px;}
.state-dl em i{width: 15px;display: inline-block;height: 50px;position: absolute;left: 0;top: 50%;margin-top: -25px;}
.state-dl em.label-txt1{background: #58B040}
.state-dl em.label-txt2{background: #ccc;}
.state-dl dt{color:#484848;margin-left:15px;text-align:center;font-size:14px;display: inline-block;width: 25%;vertical-align: top;line-height: 52px;border-right: 1px solid #ddd;position: absolute;top: 0;bottom: 0;}
.state-dl dt i{height: 30px;line-height: 13px;display: inline-block;position: absolute;top: 50%;left: 50%;width: 40px;margin-left: -20px;margin-top: -9px;font-size: 10px;}
.state-dl dd{display: inline-block;vertical-align: top;margin-left: 95px;padding: 5px 0;}
.state-dl dd p{font-size:12px;line-height: 20px;color: #666;}
.state-dl .state-bot{margin: 0;padding: 0 8px;}
/*没有服药登记*/
.no-medi{background: #fff;text-align: center;padding: 40px 0;}
/*使用时间弹框*/
.greet-box .use-ul ul li label{width: 40%;}
.greet-box .use-ul ul li span{width: 60%;}
.greet-box .use-ul ul li textarea{width: 100%;height: 64px;border: 1px solid #E8E8E8;border-radius: 3px;}
.greet-box .use-ul ul li input{width: 96%;border: 1px solid #e8e8e8;}
.greet-box .use-ul ul li select{width: 97.5%;border: 1px solid #e8e8e8;}
.greet-box .use-ul ul{border-bottom: none;}
.light-love{background: #fbf9c8;height: 29px;}
.light-love .light-left{float: left;height: 29px;line-height: 29px;font-size: 12px;color: #ff6a34;}
.light-love .light-left img{width: 18px;height: 19px;vertical-align: top;margin:5px 5px 0 15px}
.light-love .light-right{background: #ff6a34;float: right;margin-top:5px;margin-right:15px;}
.light-love .light-right a{width: 57px;height: 20px;display: inline-block;color: #fff;font-size: 11px;text-align: center;line-height: 20px;}

/*搜索*/
.search-school{background: #fff;margin-top:15px;padding-top: 10px;border-top: 1px solid #d3d2d2;}
.search-txt{width: 100%;}
.search-txt .search_bef{font-size:12px;border-radius:4px;width: 93%;margin:auto;height: 23px;line-height: 25px;background: #ebecee;}
.search-txt .search_bef img{width: 12px;height: 12px;vertical-align: top;margin-top: 7px;margin-right: 5px;}
.search-txt .search_aft{border-radius:4px;background:#ebecee;text-align:left;width: 93%;margin:0 auto;height: 23px;line-height: 23px;}
.search-txt .search_aft input{background:#ebecee;float:left;width:85%;height: 23px;border: none;vertical-align: top;outline: none;}
.search-txt .search_aft img{background:#ebecee;width: 12px;height: 12px;vertical-align: top;margin-top: 6px;margin-right: 5px;}
.search-txt .search_aft p{border-radius:4px;width: 89%;float: left;background:#ebecee;}
.search-txt .search_aft .cancel-btn{cursor:pointer;font-size: 9px;display: inline-block;height: 23px;vertical-align: top;}
.search-txt .search_aft i{border-radius:10px;background:#ebecee;float:left;margin-left:9px;text-align:center;width: 8%;height: 23px;vertical-align:top;display: inline-block;}
.search-school ul{margin-top:13px}
.search-school ul li{text-align: left;font-size: 13px;margin-left: 22px;height: 25px;line-height: 25px;}
.search-school ul li span{color: #ff632d;}
/*修改班级服药管理*/
.medi-class ul li i{width: auto;}
.medi-class ul li label{width: 40%;display: inline-block;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;height: 22px;line-height: 22px;vertical-align: top}


/*查看通知详情*/
.notice-detail{padding-bottom: 25px;}
.notice-detail .notidetail-tit h3{font-size: 15px;padding: 15px 13px;text-align: center;}
.notice-detail .notidetail-tit .noti-tit-list div p{display: inline-block;font-size: 12px;color: #999;margin: 0 12px;}
.notice-detail .notidetail-tit .noti-txt-con{font-size: 14px;padding: 12px;line-height: 22px;}
.notice-detail .notidetail-tit .down-txt dl dt{float: left;}
.notice-detail .notidetail-tit .down-txt dl dd{font-size: 12px;margin-left: 35px;}
.notice-detail .notidetail-tit .down-txt dl{clear: both;margin: 8px 12px;}
.notice-detail .end-txt{font-size: 12px;text-indent: 12px}
/*上级通知*/
.search-school .mark-stat i{font-size: 12px;font-family:"微软雅黑";line-height: 14px}
.mark-stat{font-size: 15px;position: relative;}
.mark-stat i{min-width: 17px;padding:3px 2px;display: inline-block;color: #fff;background: #FE1C1D;font-size: 6px;position: absolute;top:-5px;text-align: center;line-height: 11px;height:17px;border-radius: 50%;}
.higher-list .baby-main dt img{border-radius: 2px;width: 56px;height: 46px;}
.higher-list .baby-main dd .highlilst-p1 label{width:180px;display:inline-block;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;}
.higher-list .baby-main dd .highlilst-p1 span{float: right;color: #999;font-size: 12px;}
.higher-list .baby-main dd .highlilst-p2 {white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width:220px;}
.higher-list .baby-main dd p{margin-left: 65px;font-size: 15px;}
.higher-list .baby-main{padding: 10px 13px;margin: 0;}
.higher-list .baby-main .mark-read{position: absolute;top: 0;left: 0;width: 19px;}
.higher-list .baby-main {position: relative;}
@media only screen and (min-width:400px ) {
	.higher-list .baby-main dd .highlilst-p1 label{width:220px;}
	.higher-list .baby-main dd .highlilst-p2{width: 290px}
}
@media only screen and (min-width:500px ) {
	.higher-list .baby-main dd .highlilst-p2{width: 330px}
}
/*选择发送类型*/
.sel-sendtit{font-size: 15px;text-align: center;margin: 70px auto 25px auto ;}
.sel-sendtype{width: 74%;margin: 0 auto 20px auto;border-radius: 7px;padding: 28px 0;font-size: 0;}
.sel-sendtype dt img{width: 50px;vertical-align: middle;/*margin: 0 20px;*/}
.sel-sendtype dt{display: inline-block;width: 40%;text-align: center}
.sel-sendtype dd{display: inline-block;width:60%;height: 30px;line-height: 30px;font-size: 14px;vertical-align: top;margin-top: 10px;}
.sel-sendtype .iconfont{vertical-align: middle;float: right;margin-right: 15px;}
/*修改班级通知*/
.sel-sendclass{color: #D4D4D4;font-size:13px;}
.sel-sendclass input{width: 90%;border: none;outline: none; }
.classimg{position:relative;display: inline-block;}
.classmessage{position:absolute;right: -6px;
    top: -0.10px;
}


/*选择群发人群*/
.notice-user{position: absolute;width:100%;top:0;bottom:113px;left:0;right:0;overflow-y:scroll;}
.posi-btn{position: absolute;bottom:0;width:100%;}
.per-num{height: 43px;line-height:43px;background: #fff;border-bottom: 1px solid #F1F1F1;font-size: 13px;padding: 0 12px;}
.sel-sendper{font-size: 14px;background: #fff;margin-bottom: 10px;}
.sel-tit{height: 43px;line-height:43px;border-bottom: 1px solid #F1F1F1;padding-left: 12px;}
.sel-sendper input{margin-right: 10px;vertical-align: top;margin-top: 16px;}
.sel-sendper i.iconfont,.prevarticle-con i.iconfont{color: #D4D4D4;vertical-align: middle;float: right;margin-right: 12px;font-size:16px;}
ul.sel-person{margin-left: 37px;}
ul.sel-person li{line-height:43px;}
ul.sel-person li .person-tit,.previewmain .person-tit{border-bottom: 1px solid #F1F1F1;}
ul.sel-person li:nth-child(1) .person-tit{border-top: none;}
.person-con label{height: 50px;line-height:50px;border-bottom: 1px dashed #F1F1F1 !important;display: block}
.person-con input{float: right;margin-right: 15px}
.person-con label:last-child{border-bottom: none;}
.person-con label img{width: 34px;height: 34px;border-radius: 50%;vertical-align: top;margin-top: 8px;margin-right: 8px;}
.person-con label input{margin-top: 18px;}
.person-con .unbind{margin-left: 10px;margin-right: 10px;  color: #E0E0E0;}
/*切换幼儿园*/
.rela-remind{font-size: 12px;line-height: 20px;padding:10px 15px;}
.tab-sellist{background: #fff;margin-bottom: 10px;}
.tab-sellist .messagemain{position: relative;padding: 12px 15px;border-top: 1px solid #f1f1f1;}
.tab-sellist .messagemain img,.tab-sellist .messageright{float: none;display: inline-block;vertical-align: top}
.tab-sellist .messagezright{top: 0;}
.mark-tit{position:relative;height: 44px;line-height: 44px;font-size: 14px;padding-left: 10px;width: 94%;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;}
.mark-tit:before{content: "";position: absolute;left:0;top:13px;height: 18px;width: 3px;background: #FAC135;display: inline-block;}
.tab-sellist .messagezright input{vertical-align: top;}
.tab-sellist .messagezright{padding: 0;top: 27px;right: 15px;}
.tab-sellist .messageright .per-name{font-size: 15px;}
.tab-sellist .messageright .per-sign{color: #696969;}
.tab-sellist .messageright{padding-top: 5px;}
/*修改首页*/
.index-content{position: absolute;width: 100%;top:0;bottom: 1.50px;overflow-y: scroll;}
.index-message{position:relative;height: 175px;width: 100%;background:url(../images/index_bg.png)no-repeat;background-size:100% 175px;}
.index-message .index-top{background: none;border: 0;top: 22%;}
.index-message .tab-school{background: #FF6021;color: #fff;font-size: 10px;height: 20px;line-height: 21px;display: inline-block;padding: 0 8px;border-radius: 0 10px 10px 0;position: absolute;top: 6px;left: 0;}
.index-message .point-num{background: #FAC135;color: #fff;font-size: 10px;height: 20px;line-height: 20px;display: inline-block;padding: 0 5px;border-radius: 10px 0 0 10px;position: absolute;top: 6px;right: 0;}
.index-message .point-num img{width: 11px;vertical-align: top;margin-top: 5px;margin-right: 3px}
.index-message .point-num .iconfont{font-size: 10px;margin-left: 5px;}
.message-con{position: absolute;bottom: 15px;font-size: 10px;color: #fff;margin-left: 10px;}
.mess-num-list{display: inline-block;margin-left: 13px;}
.mess-num-list span{font-size: 15px;}
.index-message .pay-top dl dd{margin-left: 0;color: #fff;height: 52px;display: -webkit-box;display: -webkit-flex;display: flex;justify-content: center;flex-direction: column;}
.index-message .pay-top dl dt img{width: 55px;height: 55px;border: 1px solid #fff;}
.index-message .pay-top dl dt{width: 80px;}
.index-message .index-top dl dd span.spanname{white-space: nowrap;width: 88%;overflow: hidden;text-overflow: ellipsis;font-size: 14px;}
.index-message .index-top dl dd>span{line-height: 20px;font-size: 11px;}
.con-list .mark-tit{text-align: left;border-bottom: 1px solid #F4F4F4;width: 100%;}
.work-area:before{background: #1B9BFC;}
.tea-list{padding-bottom: 25px;}
.tea-list .tab-list dl{width: 33.3%;}
.tea-list .tab-list img{width: 50px;height: 50px; border-radius: 25px}
.con-list dl dt{position: relative;}
.tea-list .tab-list dl .iconfont{display:inline-block;width: 50px;height: 50px;line-height:50px;border-radius:50%;background: #EEEEEE;font-size: 25px;color: #ddd;}
.mark-notice{color:#fff;display:inline-block;background: #F9573F;font-size: 6px;position: absolute;top: -18%;left: 65%;min-width: 15px;height: 15px;line-height: 15px;border-radius: 10px;padding: 0 3px;}
@media only screen and (min-width:768px ) {
	.index-message .tab-school{font-size: 16px;height: 34px;line-height: 34px;padding: 0 16px;border-radius: 0 20px 20px 0;top: 12px;}
	.index-message{height: 350px;background-size:100% 350px;}
	.index-message .pay-top dl dt img{width: 100px;height: 100px;border: 1px solid #fff;}
	.index-message .index-top dl dd span.spanname{font-size: 22px;margin-bottom: 5px;}
	.index-message .index-top dl dd>span{font-size: 20px;}
	.index-message .index-top dl dd>span{line-height: 28px;}
	.index-message .pay-top dl dt{width: 160px;height: 100px;}
	.index-message .pay-top dl dd{height: 100px;}
	.con-list .tab-list dl{height: 120px;}
	.con-list .tab-list img{width: 64px;height: 64px;}
	.con-list .tab-list dl dd{font-size: 18px;margin-top: 16px;}
	.mark-tit{height: 60px;line-height: 60px;font-size: 16px;padding-left: 20px;}
	.mark-tit:before{top: 19px;height: 20px;width: 4px;}
	.index_yszq{height: 204px !important;}
	.index_yszq p{height: 60px;display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;}
	.index_yszq p lable{font-size: 18px !important;}
	.index_yszq p img{width: 30px !important;}
	.indexdiv{height: 204px;}
	.footer-menu dl{height: 70px !important;}
	.footer-menu dl img{width: 30px !important;height: 30px !important;margin-top: 10px !important;}
	.footer-menu dl dd{font-size: 14px !important;}
}
/*我的*/
.mine-con .messagemain{padding: 20px 15px;border-bottom: 1px solid #F1F1F1;}
.mine-tab{font-size: 0;padding: 15px 0;}
.mine-tab .mine-tab-list{display: inline-block;width: 50%;font-size: 12px;text-align: center;position: relative;}
.mine-tab .mine-tab-list img{width: 25px;margin-bottom: 5px;}
.mine-tab .mine-tab-list:nth-child(1):after{content: "";border-right: 1px solid #F4F4F4;position: absolute;right: 0;top:12%;height: 30px;}
.mine-con .messagemain .messagezright{top: 40px;}
.mine-con .messagemain .messageright .per-sign{color: #9C9C9C;}
.ul-list-detail{background: #fff;font-size: 14px;margin-bottom: 8px;}
.ul-list-detail .mark-img{width: 16px;vertical-align: top;margin-top: 14px;margin-right: 12px}
.ul-list-detail li{height: 44px;line-height: 44px;padding: 0 15px 0 0;margin-left: 15px}
.ri-arrow{width: 10px;height: 16px;vertical-align: top;margin-top: 14px;float: right}
.ul-list-detail li{border-bottom: 1px solid #F4F4F4;}
.ul-list-detail li:last-child{border-bottom: none;}
/*修改通知切换*/
.tab-notice{font-size: 0;background: #fff;height:40px;line-height: 40px;text-align: center}
.tab-notice span{width: 33.3%;display: inline-block;font-size: 12px;height:40px;}
.tab-notice span.curr-tab{color: #53CAC3; border-bottom: 3px solid #53CAC3;}


/*班级相册*/
.phototitlediv{background:#00cd91;color: #fff;  font-size: 15px; padding: 0 0 0px 0px; width: 100%; height: 40px; line-height: 40px; z-index: 10000 }
.phototitlediv .iconfont{font-size: 15px; cursor: pointer; font-weight:bold; padding:10px 5px 10px 15px;}
.photo_main,.photo_cen{  overflow: hidden;}
.photo-top{background: #fff;height: 37px;}
.photo-top img{height: 20px; vertical-align: top;margin-top:9px;margin-right: 8px;}
.phonav-con {display: inline-block;margin: 0 auto 0px auto; width: 100%;  }
.phonav-con li { position:relative;font-size:12px;cursor: pointer; display: inline-block;height:37px;line-height: 37px;float: left;width: 50%;text-align: center;}
.phonav-con li.curr-btn a{color: #00cd91;display: inline-block;position: relative }
.phonav-con li.curr-btn a:after{content: "";border-bottom: 2px solid #00cd91;width: 120%;position: absolute;bottom: 0;left: 50%;margin-left: -60%;}
.phonav-con li:active {  color: 00cd91;border-bottom: 3px solid #00cd91;}
.phonav-con li:after{content: "";border-right: 1px solid #ddd;height: 23px;position: absolute;right: 0;top: 9px;}
.phonav-con li:last-child:after{content: none}
.photo_main .weui-card img{ width: 9px; height: 15px; cursor: pointer;}
.photo_main .weui-cell::before{border-top: 1px  solid #e5e5e5;}
.photo_main .con-img{ width:67px; height: 52px; cursor: pointer; margin-right: 10px; border-radius: 3px}
.greylist{ color:#D0D0D0 }
.phototitle{ color: #ff632d; font-size:16px; padding: 10px 0 10px 15px ;font-size: 12px; }
.phototitle .set-checkbox-style{margin-left: 10px;}
.phototitle .set-checkbox-style input{vertical-align: top;margin-top: 3px;}
.photo_upload{ overflow: hidden;}
.photo-message { float: left; position: relative; margin-right: 8px}
.photo-message-bg {background: #ffb541;
    border-radius: 50%;
    color: #fff;
    cursor: pointer;
    display: block;
    float: right;
    font-size: 12px;
    height: 15px;
    line-height: 14px;
    text-align: center;
    vertical-align: middle;
    width: 15px;content: '\2714';
}
.photo-message-pot{ position: absolute;top:-4px; right:-3px;}
.photo-message .photo-ctype{display: inline-block;  padding:3px 8px; border-radius:18px;  text-align: center;font-size: 12px; color: #999999; margin:0 auto 15px auto; cursor: pointer; border: 1px solid #e8e8e8;position: relative}
.photo-message .photo-ctype a{color: #999999;}
.photo-message .orgclo{border: 1px solid #ffa922; color: #ffa922; }
.photo-message .iconfont{ font-size: 12px}
.photo-details{background: #fff;padding: 5px 0 10px 0;}
.photo-details p{ padding-left: 15px; font-size: 13px; padding-top:5px}
.dis-content{border-bottom: none;}
.dis-content dl dd{margin: 0 0 0 8px;height: 48px;line-height:20px;padding-top:13px;text-align: left}

/*查看照片视频*/
.select-time{ margin:10px 0 0 0; border-bottom: 1px solid #e8e8e8; padding:10px 15px; background: #fff}
.photo_cen .weui-cells_checkbox{float:right; color:#333333;margin-right: 12px;}
.photo_cen .weui-cells_checkbox .weui-check:checked + .weui-icon-checked::before{ color:#53cac3}
.select-time select{ border-radius:30px;  padding:5px 25px 5px 10px;border: solid 1px #e8e8e8;appearance:none;
-moz-appearance:none;-webkit-appearance:none;background: url("../images/arrow.png") no-repeat scroll right  center transparent; float: right
}
.photo-mainpic{background: #fff; padding-top: 15px;}
.select-time select::-ms-expand { display: none; }
.photo-pic{position: relative;overflow: hidden; }
.photo-stat{display: inline-block;position: relative;left:23px; width: 92%; border-left: 3px  solid #25a29b; overflow: hidden; top:28px; padding-bottom: 20px;}
.photo-stat .estat-gary{color: #ddd !important;}
.photo-pic .iconfont{background: #fff; position: absolute; left:18px; z-index: 1000;bottom: 0px; display: block; top:53px; width: 10px; height: 10px; border: 2px solid #25a29b; border-radius: 7px;}
.photo-stat:last-child:after{content: none;}
.photo-stat p{ padding-left:20px; line-height: 20px;}
.photo-list .medi-pho {
    height: auto;
    width: auto;
	/*height: 88px;*/
	/*width: 88px;*/
}
.photo-list {
    font-size: 0;
    margin:10px;
}
.photo-medi {
    display: inline-block;
    margin-top: 10px;
    text-align: center;
    vertical-align: top;
    width: 33.3%;
}
.photo-medi div {
    height: 88px;
    margin: auto;
    position: relative;
    width: 88px;
}
.time-star {
    background:url(../images/time_bg.png) no-repeat;
}

.time-star {
    background-size: 88px 34px;
    color: #fff;
    font-size: 12px;
    height: 34px;
    left: 10px;
    line-height:25px;
    position: absolute;
    text-align: center;
    top: 0px;
    width: 88px;
}
.pull-down{ text-align: center; font-size: 13px; padding: 20px 0; color:#999999}
.uploadpic{ width: 21px; padding-right:5px;vertical-align: top;margin-top: 8px}
.pho-medi div span{ cursor: pointer;display: inline-block;left: 50%;position: absolute;top: 50%;transform: translate(-50%, -50%); padding:30px}
.pho-medi div span img{ width: 30px; height: 30px;}


/*照片详情*/
.photo-con{position: absolute;top: 0;bottom: 42px;width: 100%;background: url(../images/photo_det.jpg) center center / cover no-repeat;}
.photo-bot{border-top:1px solid #DFDFE1;background: #F8F8F8;color:#A3A3A3;height: 42px;line-height:42px;font-size:12px;text-align:center;position: fixed;bottom: 0;width: 100%;}
.photo-bot .left-btn{background: #12B7F5;color: #fff;height:22px;line-height:22px;float: left;padding: 0 8px;margin: 10px 0 0 15px;}
.photo-bot .right-label{float: right;color: #FF5D44;margin-right: 15px;}
.photo-bot .right-label .iconfont{vertical-align: bottom;margin-right: 3px;}
.photo-bot .left-label{float: left;margin-left: 15px;color: #12B7F5;}
.photo-bot .left-label img{width: 17px;}
.photo-bot .left-label label{position:relative;display: inline-block;width: 17px;height: 17px;line-height:17px;text-align: center;vertical-align: top;margin-top: 13px;}
.photo-bot .left-label label .mark-ri{position: absolute;top: -8px;color: #12B7F5;font-size: 12px;}
.descri-photo{background: rgba(0,0,0,0.5);position: absolute;bottom: 0;font-size: 7px;color: #fff; width: 100%; }
.descri-photo p{padding: 10px;}
.descri-top{background: rgba(0,0,0,0.5);position: absolute;top: 0;font-size: 7px;color: #fff;width: 100%; padding: 10px 0;}
.descri-top span{ display:inline-block;padding:0 15px; font-size:13px ; color: #fff; }
.descri-top  .iconfont{cursor: pointer; padding-right:10px}
.connect-title{background: #fff;padding:10px 12px  15px  12px;  margin-top: 10px; clear: both; overflow: hidden}
.connect-title textarea{width: 98%;border:1px solid #e8e8e8; padding: 5px; font-size: 14px}
.connect-title input{border:none;border:none;margin-bottom: 8px;outline:none;}
.connect-title input::-webkit-input-placeholder{line-height:18px }
.connect-title input::-moz-placeholder {line-height:18px }
.connect-title input:-ms-input-placeholder {line-height:18px }
.content-p{margin-bottom: 8px;font-size: 13px;}
.content-p i{font-style: normal;color: Red;}
/*园长特权*/
.privi-top{height: 60px;background: #fff;text-align: center}
.enr-detail{margin-top: 10px;}
.enroll-tit{border-bottom: 1px solid #F5F5F5;}
.enroll-tit span{color: #333;width: auto;}
.enroll-tit span:before{background-color: #FAC135;}
.enroll-tit a{color: #60AEF9;border-radius: 20px;border:1px solid #60AEF9;padding: 3px 10px;font-size: 10px;float: right;margin: 10px 12px 0 0;line-height: normal}
.enr-progress{background: #fff;padding: 20px 12px;font-size: 0;text-align: center}
.enr-progress .enr-pro-list{display: inline-block;width: 50%;font-size: 14px;vertical-align: top;}
.enr-detail .no-mess{background: #fff;}
.enr-txt-list li{color:#666;font-size: 14px;padding: 13px 13px 13px 0;border-bottom: 1px solid #F2F2F2;margin-left: 13px}
/*个人信息*/
.person-message .weui-cells{margin-top: 0;margin-bottom: 9px;border-bottom: 1px solid #F2F2F2;}
.person-message .weui-cells:before,.person-message .weui-cells:after{content: none;}
.person-message .weui-cells .weui-cell__bd{color: #373737;font-size: 15px;}
.person-message .weui-cells .weui-cell__ft{font-size: 14px;}
.person-message .weui-btn::after{ border: none}
.weui-cell__pft, .examine-main .weui-cells .weui-cell__pft{font-size: 14px;color: #999999;}
.mine-top-tit{border-bottom: 1px solid #F2F2F2;background: #fff}
.info-tit{background: #fff}
.info-tit .person-info{font-size:0;color:#12b7f5;height: 20px;line-height: 20px;margin-right: 12px;}
.info-tit .person-info img{width: 20px;vertical-align: top;}
.info-tit .person-info span{font-size: 14px;margin-left: 6px;margin-right: 6px;}
.send-mess{margin: 0;height: 40px;line-height: 40px;font-size: 18px;width: 100%;position: absolute;bottom: 0;z-index: 999;}
.send-mess .greenBtn{margin: 0;height: 40px;line-height: 40px;font-size: 18px;}
/*查看关联家长*/
.check-person{background: none}
.check-person .check-list li label{font-size: 12px}
.check-person .medi-right{font-size: 11px!important;}
.check-person .phone-num{width: 10px;vertical-align: top;margin-top:3px;margin-right: 5px}
.fill-btn{width: 100%!important;}
.sel-btn a.fill-btn:after{content: none}
/*查看群组消息*/
.group-list{padding-bottom: 25px;margin-bottom: 10px;}
.group-list .tab-list{padding: 0;}
.group-list .tab-list dl{width: 25%;}
.group-list .tab-list dl dd{font-size: 12px;color: #666;}
.group-list .tab-list img{width: 50px;height: 50px; border-radius: 25px}
.group-list .tab-list dl .iconfont{display:inline-block;width: 50px;height: 50px;line-height:50px;border-radius:50%;background: #EEEEEE;font-size: 25px;color: #ddd;}
.check-slide .weui-switch:checked,.check-slide .weui-switch-cp__input:checked ~ .weui-switch-cp__box{/*border-color: #53CAC3;background-color: #53CAC3;*/}
/*园长助手*/
.secondary-stat{/*background: #53CAC4;*/color: #fff;display: flex;display:-webkit-flex;padding: 30px 0 19px 0;justify-content: center;background: #05cc93;
	background: -webkit-linear-gradient(#15e3af, #00bf88); /* Safari 5.1 - 6.0 */
	background: -o-linear-gradient(#15e3af, #00bf88); /* Opera 11.1 - 12.0 */
	background: -moz-linear-gradient(#15e3af, #00bf88); /* Firefox 3.6 - 15 */
	background: linear-gradient(#15e3af, #00bf88); /* æ ‡å‡†çš„è¯­æ³• */
}
.stat-item-list{width: 150px;display: flex;display:-webkit-flex;align-items: center;flex-direction: column;justify-content: center;}
.stat-item-list .stat-mess+.stat-mess{margin-top: 25px;}
.stat-item-num{width: 200px;display: flex;display:-webkit-flex;align-items: center;justify-content: center;position: relative }
.stat-mess{font-size: 10px;text-align: center}
.stat-item-list .stat-mess p:nth-child(1){padding-bottom: 4px;margin-bottom: 4px;position: relative}
.stat-item-list .stat-mess p:nth-child(1):after{content: " ";position: absolute;right: 0;left:0;height: 1px;bottom: 0;border-top: 1px solid #fff;color: #fff;-webkit-transform-origin: 100% 0;transform-origin: 100% 0;-webkit-transform: scaleY(0.5);transform: scaleY(0.5);}
.label-act-btn{border: 1px solid #fff;border-radius: 20px;font-size: 10px;padding: 4px 10px 2px 10px;}
.count-stat{display: flex;display:-webkit-flex;justify-content: space-between;align-items: center;color: #fff;}
.count-stat .devide-num:nth-child(1){padding-left: 20px;border-bottom-right-radius:40px 15px;}
.count-stat .devide-num:nth-child(2){padding-right: 20px;border-bottom-left-radius:40px 15px;}
.devide-num{background: #00bf88;display: flex;display:-webkit-flex;justify-content: space-around;width: 50%;padding-bottom: 30px;}
.count-stat-list{display: flex;display:-webkit-flex;align-items: center;flex-direction: column;}
.count-stat-list .count-num{border: 1.5px solid #fff;width: 50px;height: 50px;border-radius: 50%;display: flex;display:-webkit-flex;justify-content: center;align-items: center;flex-direction: column}
.count-stat-list .count-num p:nth-child(1){font-size: 17px;}
.count-stat-list .count-num p:nth-child(2){font-size: 8px;}
.count-stat-list .exp-count-num{font-size: 10px;margin-top: 8px}
.multiple-div{padding: 15px 15px 10px 15px;border-radius: 15px;}
.multiple-div .mul-table-top{height: 44px;font-size: 15px;background: #00cd91;color: #fff;border-radius: 15px 15px 0 0;display: flex;display:-webkit-flex;align-items: center;justify-content: center;}
.mul-stat-num{display: flex;display:-webkit-flex;align-items: center;justify-content:space-around;background: #fff;padding: 15px 0;border-radius: 0 0 15px 15px;border-right: 1px solid #e8e8e8;border-bottom: 1px solid #e8e8e8;border-left: 1px solid #e8e8e8}
.mul-stat-num .iconfont{color: #CDCDCD;}
.multiple-stat{display:flex;display:-webkit-flex;align-items:center;font-size:15px;padding:0 20px;height: 65px;background: #fff;border: 1px solid #E9F0F6;border-radius: 15px;margin: 0 15px 10px 15px;}
.multiple-stat .iconfont{background: #00cd91;color:#fff;width: 31px;height: 31px;border-radius: 50%;display: flex;display:-webkit-flex;align-items: center;justify-content: center;float: right;}
.multiple-stat .mul-right{flex: 1;webkit-flex: 1;text-align: right}
.multiple-stat .mul-img{width: 30px;margin-right: 8px;}
/*综合指数分析*/
.mul-stat-top{height: 44px;padding:0 15px;font-size:14px;background: #fff;border-bottom: 1px solid #EBEBEB;display: flex;display:-webkit-flex;align-items: center;}
.label-num{border: 1px solid #FD575D;border-radius: 20px;font-size: 12px;padding: 4px 10px;color: #FD575D;}
.mul-right-label{display: flex;display:-webkit-flex;align-items: center;flex: 1;webkit-flex: 1;justify-content: flex-end;}
.mulsel-num-list{display: flex;display:-webkit-flex;align-items: center;font-size:15px;padding: 0 15px 0 0;margin-left:15px;height: 75px;border-bottom: 1px dashed #E8E8E8;}
.mulsel-con{background: #fff;}
.gray-txt{color: #999;font-size: 12px;}
.gray-txt2{color: #666;font-size: 12px;}
.mulsel-num-list .mul-right-num{color: #FD575D;font-size: 15px;display: flex;display:-webkit-flex;flex: 1;webkit-flex: 1;justify-content: flex-end;}
.mulsel-num-list:last-child{border-bottom: none}
/*班级请假数据分析*/
.analysis-top{background:#fff;font-size:15px;height: 44px;border-bottom: 1px solid #EFEFEF;display:flex;display:-webkit-flex;align-items:center;justify-content: space-between;padding: 0 15px;}
.analysis-top select{width: 92px;height: 25px;line-height: 25px;border: 1px solid #EDEDED;}
.line-chart-con{background: #fff;}
.progress-chart-con{height: 80px;background: #fff;margin-top: 10px;padding: 0 15px;}
.pie-chart-con{height: 240px;background: #fff;margin-top:10px;padding: 0 15px;}
/*出勤数据分析*/
.chart-show{padding: 15px 0;display: flex;display: -webkit-flex;flex-direction: column;justify-content: center;align-items: center;}
.chart-show .chart-tab-list{display: flex;display: -webkit-flex;width: 100%;justify-content: center;}
.chart-show .chart-tab-other{display: flex;display: -webkit-flex;align-items: center;justify-content: center;}
.chart-show table{background: #fff;border-collapse: collapse;}
.chart-show table.tab-rigt-con tr td:nth-child(1){    border-left: none;}
.chart-show table.tab-rigt-con{overflow-x: scroll;}
.chart-show table.tab-rigt-con td{width: 50%;}
.chart-detail{width: 96px;display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;flex-wrap: wrap;justify-content: center;}
.chart-show table tr{height: 34px;}
.chart-show table thead tr{font-size:14px;color:#fff;background: #FFA751;height: 46px;text-align: center;}
.chart-show table thead tr td.td-first{position: relative}
.chart-show table thead tr td.td-first p:nth-child(1){position: absolute;top: 5px;right:7.5px;}
.chart-show table thead tr td.td-first p:nth-child(2){position: absolute;bottom:5px;left: 7.5px;}
.chart-show table thead tr td{border-right: 1px solid #FFC286;width: 33.3%;}
.chart-show table tbody tr td{font-size:12.5px;border: 1px solid #dddddd;text-align: center;width: 25%;color: #666;}
.td-first{background: url(../images/td_bg.png);background-size: 100% 43px;position: relative;}
.blue-label{color: #3C9CF7;text-decoration: underline;}
.chart-show .side-one{width: 96px;}
.chart-show .side-two{width: 240px;}
@media only screen and (max-width: 320px) {
	.chart-show .side-two{width: 200px;}
}
/*@media only screen and (min-width: 375px) and (max-width: 414px) {	
	.chart-show .side-one{width: 115px;}
	.chart-show .side-two{width: 230px;}
	.chart-show .chart-detail{width: 112px;}
}*/
.chart-show .side-one tbody tr td p:nth-child(1){margin:0 auto;width: 85px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap}
/*通知公告数据分析*/
.notice-top{background: #fff;display: flex;display:-webkit-flex;align-items: center;height: 100px;justify-content: center;}
.sel-calendar{background: #53CAC4;padding:10px 0;color: #fff;font-size: 14px;display: flex;display:-webkit-flex;align-items: center;justify-content: center;height: 34px;}
.sel-calendar i.iconfont{margin-left: 5px;}
.calendar-con{display: flex;display:-webkit-flex;align-items: center;justify-content: center;margin: 0 10px}
.label-print{border:1px solid #fff;border-radius: 20px;font-size: 12px;display: flex;display:-webkit-flex;align-items: center;justify-content: center;margin-left: 5px;height:22px;padding: 0 9px;line-height: normal}
/*一日食谱数据分析*/
.chart-list{margin-bottom: 10px;}
.mul-chart-tit{height: 44px;border-bottom: 1px solid #F2F2F2;background: #fff;font-size: 15px;display: flex;display:-webkit-flex;align-items: center;justify-content: center;}
.mul-chart-con{background: #fff;padding: 15px}
.mul-photo-list{background: #fff;}
.mul-photo-tit{height: 52px;display: flex;display:-webkit-flex;align-items: center;padding: 0 15px;border-bottom: 1px solid #F2F2F2;}
.mul-photo-tit a{border: 1px solid #E1E1E1;color:#999;font-size: 12px;width:78px;height:23px;border-radius: 20px;display:flex;display:-webkit-flex;align-items: center;justify-content: center;}
.mul-photo-tit a+a{margin-left: 10px;}
.mul-photo-tit a.sel-way{color: #FFB23F;border: 1px solid #FFB23F;}
.check-photo .messagemain{padding: 12px 15px 12px 0;margin-left: 12px;border-top: none;border-bottom: 1px solid #F1F1F1;}
.check-photo .per-sign{font-size: 14px;}
.check-photo .messagemain{width:100%; margin:0 auto; border-bottom:1px solid #e8e8e8; overflow:hidden; padding:7.5px 15px 7.5px 15px; }
.check-photo .messagemain img{ width:46.2px; height:46.2px; float:left; vertical-align:middle; border-radius:50%; /*border:1px solid #D1D0CE*/}
.check-photo .messageright{ float:left; padding:2.5px 0 0 5px; width:65%;}
.check-photo .messageright span{ line-height:20px;font-size:12px; color:#333333; display:block;}
.check-photo .messagezright{ position:absolute;right:0;text-align:right; padding:2.5px 10px 0 0; color:#999999; font-size:13px;}
.check-photo .xiaoxibg{display:block; background:#FC1C1C; border-radius:50%;  width:15px; color:#fff; font-weight: bold; font-size: 12px; text-align: center; vertical-align: middle;height:15px;   cursor:pointer; font-weight:bold; line-height:15px; float:right}
.check-photo .messagezright span{line-height:20px;  display:block;}
.check-photo font{font-size: 15px;}
.check-photo .messagezright{top: 20px}
/*修改通知*/
.swi-con .enroll-tit span:before{background-color: #25B8F2;}
/*微信聊天请假时间*/
.time-label{color: #999;font-size: 12px;}
/*修改通知图片同步保存按钮*/
.def-swith-div{padding: 0;font-size: 13px;margin-top: 8px;color:#999;}
.define-swith{height: 25px;width: 45px}
.define-swith:before{background-color:#ddd;height: 25px;width: 45px }
.define-swith:after{width: 25px;height: 25px}
/*首页新增园所专区*/
.indexdiv { background: #fff; clear: both; width: 100%;height: 72px;}
.index_yszq{ width: 100%; margin: 0 auto;height: 102px;background: #ffffff;}
.index_yszq p{ width: 49.5%; float: left; text-align: center; padding: 10px 0; cursor: pointer}
.index_yszq p.nline{ border-right: 1px dashed #E8E8E8}
.index_yszq p img{ width: 25px; vertical-align: middle}
.index_yszq p:active{box-shadow: 3px 3px 3px 3px rgba(6, 6, 6, 0.1); display: inline-block }
.index_yszq p lable{font-size: 15px; padding-right:30px; display: inline-block; line-height: 30px;}
.index_yszq p.znline{ border-bottom: 1px dashed #E8E8E8}

/*幼儿园列表*/
.school-mess-list .messageright{display: flex;display: -webkit-flex;align-items: center;flex: 1;margin-left: 5px}
.school-mess-list .messagemain{width:95%;display: flex;display: -webkit-flex;}

/*打卡*/
.check_work{ background: #fff;  overflow: hidden}
.check_work .weui-grid{box-sizing: border-box; padding:10px 20px 0 20px;width: 25%;}
.check_work .weui-grid__icon, .check_word .weui-grid__icon { height: 45px; width: 45px;}
.check_work .weui-grid__label, .check_word .weui-grid__label { color: #333333;font-size: 14px;}
.check_work .weui-grid::before{ border-right: none;}
.check_work .weui-grids::before{border-top:none}
.check_work .weui-grid::after{border-bottom: none}
.check_work .weui-grid__label span{ height: 27px; display: block; width: 45px; text-align: center; margin: 0 auto; }
.check_work .current .weui-grid__label span{ border-bottom: 4px solid #ffb93d;color: #ffb93d;  padding-bottom: 6px;display: block; }
.time-tit {border-bottom: 1px solid #f1f1f1; padding: 10px 15px 10px 15px; margin-top: 10px; background: #fff; font-size: 14px;}
.time-tit img{ width:20px; vertical-align: middle; padding-right:5px}
.time-tit i.iconfont {color: #053087;cursor: pointer;float: right;vertical-align: middle;font-size: 12px;line-height: 22px }
.pos-tit{ padding: 5px 15px 5px 15px;  background: #fff; font-size: 14px;}
.pos-tit label{ font-size: 12px}
.pos-tit .up-pos,.pos-add .up-pos{ float: right;color: #399bf7;cursor: pointer; text-decoration: underline;font-size: 12px;}
.pos-add{color:#999999;background: #fff;padding: 0 15px 10px 15px; font-size: 12px; }
.pos-add .greebg,.check_update .greebg {border: 1px solid #53cac4; color:#53cac4; padding:0px 13px;  border-radius: 10px; margin-right: 10px;font-size: 12px;height: 20px; line-height: 20px; display: inline-block;}
.pos-add .greebg2,.check_update .greebg2 {border: 1px solid #31C655; color:#31C655; padding:0px 13px;  border-radius: 10px;font-size: 12px;height: 20px; line-height: 20px; display: inline-block;}
.pos-add .orangebg,.check_update .orangebg{border: 1px solid #ff805a; color:#ff805a; padding:0px 13px;  border-radius: 12px; margin-right: 10px;font-size: 12px;height: 20px; line-height: 20px; display: inline-block;}
.pos-add .redbg,.check_update .redbg{border: 1px solid #ff1d1e; color:#ff1d1e; padding:0px 8px;  border-radius: 12px; margin-right: 10px;font-size: 12px;height: 20px; line-height: 20px; display: inline-block;}
.pos-add .yellow-orangebg,.check_update .yellow-orangebg,.yellow-orangebg{border: 1px solid #fac132; color:#fac132; padding:0px 13px;  border-radius: 12px; margin-right: 10px;font-size: 12px;height: 20px; line-height: 20px; display: inline-block;}
.pos-add .pinkbg,.check_update .pinkbg{border: 1px solid #ff7293; color:#ff7293; padding:0px 13px;  border-radius: 12px; margin-right: 10px;font-size: 12px;height: 20px; line-height: 20px; display: inline-block;}
.pos-add .darkgreebg,.check_update .darkgreebg{border: 1px solid #0fbb2b; color:#0fbb2b; padding:0px 13px;  border-radius: 12px; margin-right: 10px;font-size: 12px;height: 20px; line-height: 20px; display: inline-block;}
.check_work_time{ background: #fff; margin-top: 10px; padding-top: 20px}
.check_work_list{position: relative;  overflow: hidden;}
.check_stat{display: inline-block;position: relative;left:21px; width: 92%; border-left: 3px  solid #e2e2e2;  top:0px; padding-bottom: 20px;/*height: 180px;*/}
.check_cen{position: relative;overflow: hidden; /*height: 180px;*/ }
.check_cen .greebgico{background: #53cac4; position: absolute; left:17px; z-index: 1000;bottom: 0px; display: block; top:0px; width: 10px; height: 10px; border-radius: 7px;}
.check_cen .greybgico{background: #b2b2b2; position: absolute; left:17px; z-index: 1000;bottom: 0px; display: block; top:0px; width: 10px; height: 10px; border-radius: 7px;}
.check_cen .orangeico{background: #ff805a; position: absolute; left:17px; z-index: 1000;bottom: 0px; display: block; top:0px; width: 10px; height: 10px; border-radius: 7px;}
.check_cen:last-child:after{content: none;}
.check_cen p{ padding-left:15px; line-height:12px; font-size: 12px; color: #999999}
.daka-normal{background:#53cac4;border-radius:50%;color: #fff;margin:20px auto; width:120px; height:120px; box-shadow: 1px 0 9px 13px rgba(220, 244, 243, 0.65);}
.daka-notin{background:#cccccc;border-radius:50%;color: #fff;margin:20px auto; width:120px; height:120px; box-shadow: 1px 0 9px 13px rgba(248, 248, 248, 0.65);}
.daka-waiqin{background:#ff805a;border-radius:50%;color: #fff;margin:20px auto; width:120px; height:120px; box-shadow: 1px 0 9px 13px rgba(255, 232, 225, 0.55);}
.daka-late,.daka-late2,.daka-zaotui,.daka-zaotui2{background:#ff1d1e;border-radius:50%;color: #fff;margin:20px auto; width:120px; height:120px; box-shadow: 1px 0 9px 13px rgba(255, 232, 225, 0.55);}
.daka-normal label,.daka-notin label,.daka-waiqin label,.daka-late label,.daka-late2 label,.daka-zaotui label,.daka-zaotui2 label{ font-size: 19px;line-height:35px;padding-top:32px;color: #fff;text-align: center;display: block;}
.daka-notin label{
	font-size: 14px;
}
.daka-normal span,.daka-notin span,.daka-waiqin span,.daka-late span,.daka-late2 span,.daka-zaotui span,.daka-zaotui2 span{ font-size: 12px; display: block; text-align: center}
/*打卡成功*/
.clock_success{ border-radius:5px; margin: 0 auto; width:200px;   }
.clock_green{background:#53cac4;  }
.clock_orange{background:#ff805a;  }
.clock_pink{background:#ff7293;  }
.clock_success h3{ text-align: center; color: #fff; font-size: 19px; line-height:80px}
.off-duty{background: #f1f3f3; width: 100px; height: 40px; border-top-left-radius: 2px; border-top-right-radius: 2px; margin: 0px auto; color: #666666}
.off-duty span{ display: block; float: left; padding:4px 10px; line-height: 16px; width: 10px; font-size: 12px}
.off-duty p{  text-align: center; font-size:23px; line-height:40px; }
.off-des{ text-align: center; color: #fff; font-size: 14px; line-height: 40px}
.off-btn{ border-top: 1px solid #e9e9ea; color: #fff; font-size: 16px; line-height:50px; text-align:center; margin-top: 20px}
.clock_success  i{ background: #ffffff; border-radius: 50%;  width: 20px; height: 20px; vertical-align: middle; display: inline-block; margin-right: 5px}
.clock_green .iconfont{  font-size: 15px; line-height:20px; color:#53cac4; }
.clock_orange .iconfont{  font-size: 15px; line-height:20px; color:#ff805a; }
/*更新打卡时间*/
.check_update{ overflow: hidden}
.check_update h3{ font-size: 14px; font-weight: normal; padding-left: 15px; margin: 10px 0  4px 0; }
.check_update_time {color: #399bf7;  }
.check_update_time a{ text-decoration: underline; margin-right: 10px;color: #399bf7; font-size: 12px; }
.check_update p{line-height: 26px; display: flex; align-items: center;}
.check_update_time .check_update_right{ text-decoration: none; }
.check_update_time .check_update_right .iconfont{vertical-align: middle}
/*下班打卡*/
.off_work{ border-radius: 5px; overflow: hidden; margin: 0 auto}
.off_work .weui-cell{border-bottom: 1px dashed #e5e5e5; margin:0 15px;padding: 10px 0}
.off_work .weui-cells::before{border-top:none}
.off_work .weui-cells::after{border-bottom:none}
.off_work h3{ line-height: 40px; text-align: center; color:#000000; font-size: 18px; }
.off_work_tit{background: #fff;padding: 10px 15px;}
.off_work_tit textarea{width: 93%;height:60px;padding: 10px;margin-top: 10px;border: 1px solid #ECECEC;}
.off_work_tit input{width: 93%;height:20px;padding: 10px;margin-top: 10px;border: 1px solid #ECECEC;}
/*打卡页面的请假*/
.daka_askleave{ overflow: hidden}
.daka_askleave .weui-cells{margin-top: 10px;}
.leave_rea{ display: inline-block; line-height: 20px; width: 20px; height: 20px; border-radius: 50%; background: #f5f5f5; font-size: 11px; text-align: center; margin-right:5px}
/*查看请假详情*/
.check-ask-top{background: #fff;}
.exam-content .per-info{border-bottom:none}
.per-info{height: 75px;display: flex;display: -webkit-flex;align-items: center;padding-right: 15px;margin-left: 15px;}
.per-info dt{display: flex;display: -webkit-flex;}
.per-info dd{font-size: 15px;margin-left: 10px;}
.exam-content .per-info dd{display: flex;display: -webkit-flex;justify-content: space-between;flex: 1;-webkit-flex: 1;flex-direction: column;}
.exam-content .per-info dd .exam-dd-p2{font-size: 10px;color: #A7A7A7;}
.exam-content .per-info dd .exam-con-p{display: flex;display: -webkit-flex;align-items: center;justify-content: space-between;}
.per-info .set-checkbox-style{display:flex;display:-webkit-flex;align-items:center;flex: 1;justify-content: flex-end;}
.stat-label{display: flex;display: -webkit-flex;flex:1;-webkit-flex:1;justify-content: flex-end; color:#999999; font-size: 12px}
.askleave-list{padding: 10px 15px;border-top: 1px dashed #F3F3F3;font-size: 14px;}
.askleave-list li{display: flex;display: -webkit-flex;justify-content: space-between;padding: 2px 0;}
.askleave-list p{width: 70%;display: flex;display: -webkit-flex;justify-content: flex-end;color: #666;}
.pro-tit{height: 44px;background: #fff;margin-top: 10px;font-size: 14px;display: flex;display: -webkit-flex;align-items: center;padding: 0 15px;border-bottom:1px solid #F2F2F2}
.pro-tit .iconfont{margin-right: 5px;}
.exam-content{background: #fff;padding-bottom: 10px;}
.exam-content .per-info{position: relative;display: inline-block;min-height: 75px;height: auto;width:100%;padding: 0;margin: 0;}
.exam-content .per-info dt{float: left;margin-left: 15px;}
.exam-content .per-info dt img{margin-top: 15px;z-index: 999;}
.exam-content .per-info dd{padding-top: 32px;padding-left: 10px;margin-right: 15px;}
.exam-content .per-info:after{position:absolute;content: "";border-right: 1px dashed #F0F0F0;height: 100%;bottom: -16px;left: 38px;}
.exam-content .per-info:last-child:after{content: none;}
.ask-pic-list{flex-wrap: wrap;justify-content: flex-start !important;}
.ask-pic-list img{margin: 5px 0;}
@media only screen and (width: 320px) {
	.ask-pic-list{width: 78%!important;}
}
.opr-lu-list{z-index:999;width: 100%;height: 48px;line-height: 48px;font-size:15px;background: #fff;position: fixed;bottom:0;border-top: 1px solid #E9E9E9;}		
.opr-lu-list li{text-align: center;position: relative;}
.opr-lu-list li:after{content: "";height: 50%;border-right: 1px solid #E0E0E0;position:absolute;right: 0;top: 25%;}
.opr-lu-list li:last-child:after{border-right: none;}
/*园长特权（功能导航）*/
.mana-list{font-size:16px;height: 110px;width: 67%;margin: 20px auto;padding:0 30px;display: flex;display: -webkit-flex;align-items: center;justify-content: space-between;border-radius: 5px;}
.mana-list .iconfont{font-size: 18px;}
.mana-txt{display: flex;display: -webkit-flex;align-items: center;justify-content: center;}
.mana-txt img{margin-right: 5px;}
.pink-bg{background: #FFE6FE;color: #EC35E3;}
.oran-bg{background: #FFE7D1;color: #F75724;}
.gree-bg{background: #E3F8F8;color: #49BCBC;}
.blue-bg{background: #EDF1FF;color: #5C89F5;}
/*查看打卡详情*/
.pun-det{background:#fff; padding-top: 10px;}
.pun-tit{ background: #fff}
.pun-tit span{  height:25px;   font-size: 15px; color: #333333;display:inline-block; line-height: 25px;  }
.pun-tit .lightgreen{ color: #53cac4; }
.pun-tit i{display:inline-block; width:3px; height:25px;line-height: 25px; margin-right:5px;vertical-align: middle; }
.pun-det .weui-cells{ margin-top: 10px;}
.pun-det .weui-cell__ftp,.pun-det .weui-cell__ft{ color: #666666; font-size: 14px;}
.pun-det .weui-cell__ftp .iconfont{margin-left: 10px; color:#cccccc; font-size: 14px;}
.pun-det .weui-cell::before{ border-top: 1px dashed #e5e5e5}
.pun-drop-down{margin-left: 70px;padding-bottom: 3px}
.pun-drop-down p{font-size: 12px; color:#666666; line-height: 20px; }
.pun-drop-div{ padding-bottom:5px;border-bottom: 1px dashed #e5e5e5;}
.pun-drop-div span{ color:#999999; font-size: 10px; display: block; line-height: 18px;}
.pun-drop-div:last-child{border-bottom: none;padding-bottom:0}
/*考勤设置*/
.att-set{ overflow: hidden;}
.att-tit{background: #fff; padding:10px 15px;overflow: hidden; }
.att-tit span{ display: inline-block; line-height: 20px; font-size: 15px}
.att-tit .addbtn,.addbtn{border: 1px solid #fac132; color:#fac132; padding:3px 13px;  border-radius:20px;font-size: 12px;  float: right}
.addbtn .iconfont{ font-size: 12px;}
.att-set-div { margin-top: 10px;background: #fff;overflow: hidden;padding: 10px 0  }
.att-set-div p{ color:#999999; line-height: 20px; font-size: 10px; padding: 0 15px;overflow:hidden;text-overflow:ellipsis;}
.att-set-mem .iconfont{ font-size: 15px;margin-right:5px; vertical-align: middle;}
.att-set-div .edi-div{ border-top: 1px solid #e5e5e5; overflow: hidden; height: 40px; margin-top: 10px; }
.att-set-div .edi-btn{border: 1px solid #399bf7; color:#399bf7; padding:3px 0;  border-radius:20px;font-size: 12px;  text-align: center;margin: 10px auto 0 auto; width: 100px; display: block}
/*选择考勤班次*/
.att-set .weui-cells{ margin-top: 0}
.att-set .weui-cell::before{ border-top: 1px dashed #e5e5e5}
.att-set .weui-cells::after,.att-set .weui-cells::before{ left:15px;}
.att-set .weui-cells_checkbox .weui-check:checked + .weui-icon-checked::before{ color: #fac132;}
.att-set .weui-cells_checkbox  .weui-icon-checked::before{font-size: 19px;}
.att-set .weui-cell__bd{  color:#999999; font-size: 14px; }
.att-set .weui-cell__ft{ color:#419ff7; font-size: 14px;display:flex; align-items: center;}
/*编辑考勤班次*/
.att-edi .weui-cells{ margin-top:10px;}
/*一周考勤天*/
.att-day,.att-edi{ overflow: hidden;}
.att-day .weui-cells{ margin-top:0px;}
.att-day .weui-cell::before,.att-edi .weui-cell::before{ border-top: 1px dashed #e5e5e5}
.att-day .weui-cell__bd p,.att-edi .weui-cell__bd p{ color: #333333; font-size: 15px;}
.att-day .weui-cell__ft { color:#999999; font-size: 14px;  }
.att-checkbox input[type="checkbox"]{ -webkit-appearance: none;width: 14px;height: 14px;border: 1px solid #ccc;outline: none;}
.att-checkbox input[type="checkbox"]:checked{background: url(../images/sel_date.png) 50%;background-size:14px 14px;outline: none;border: 0;}
.att-checkbox input[type="checkbox"]{vertical-align: middle;margin-right: 7px;}	
/*编辑考勤组*/
.att-edi .weui-cell__ftp img{vertical-align: top}
.att-edi .weui-switch:checked, .att-edi .weui-switch-cp__input:checked ~ .weui-switch-cp__box{ background-color: #53cac3;
    border-color: #53cac3;}

/*新增编辑考勤班次*/
.rest-time .weui-cell__bd p{ color: #666666; font-size: 13px;}
.rest-time .weui-cell::before{border-top: none}
.rest-time{background: #f7f7f7; margin: 15px; border-radius:4px; position: relative }
.del_cancelimg{ position: absolute; right: -5px; top:-6px; width: 13px; cursor: pointer}
/*暂无打卡*/	
.check-top{background:#fff;font-size:14px;height: 44px;margin-top:10px;display: flex;display: -webkit-flex;align-items: center;justify-content:space-between;border-bottom: 1px solid #F7F7F7;padding: 0 15px;}
.check-top .check-top-img{/*display: flex;display: -webkit-flex;*/align-items: center;}
.check-top .check-top-right{font-size: 12px;color: #053087;}
.no-record{background: #fff;text-align: center;padding: 60px 0;}
/*我的考勤*/
.check-stat-list{background: #fff;margin-top: 10px;}
.stat-tab-con{height: 58px;display: flex;display: -webkit-flex;align-items: center;justify-content: center; position: relative}	
.stat-date{background: #53CAC4;color: #fff;font-size: 15px;height: 40px;display: flex;display: -webkit-flex;align-items: center;justify-content: space-around;}
.atten-txt-list .weui-cell{flex-direction: column;}
.atten-top{display:flex;display:-webkit-flex;width: 100%;}
.atten-con{font-size: 12px;color: #666;width: 100%;padding:8px 0;border-bottom: 1px dashed #f2f2f2;padding-bottom: 5px;}
.atten-con:last-child{border-bottom: none;}
.atten-p2{font-size: 10px;color: #999;width: 100%;margin-top: 5px;}
/*新增临时调班*/
.tem-shi{background: #f7f7f7; border-radius:3px;height: 22px;line-height: 23px; display:inline-block;padding: 4px 10px ;position: relative; margin: 0px 20px 10px 0}
.tem-shi.graybg{background: #fff; border: 1px solid #919191;}
.tem-shi.greenbg{background: #fff;border: 1px solid #53CAC4; color:#53CAC4 }
.tem-cancel{color: #666666; font-size: 13px; text-align: center}
.tem-cancel img{position: absolute;right: -9px;top: -5px; cursor: pointer}
.weui-class i:nth-child(3){margin-right: 0}
/*考勤日统计*/
.kaoqinritongjiquanziouter{width:auto;height: 150px;border-top: 1px solid #F7F7F7;background-color: #ffffff;display: flex;display:-webkit-flex;align-items: center;justify-content: center;position: relative}
.atten-table{background: #fff;margin-top: 10px}
.atten-table .chart-show table tbody tr td{height: 35px;line-height: 37px;}
.atten-table .chart-show table tr td p{display: block;  height: 35px;line-height: 37px;}
.green-tab-txt{color: #00b95b;}
.oran-tab-txt{color: #f9573f;}
.yellow-tab-txt{color: #FAC132;}
.pink-tab-txt{color: #FF7393;}
.black-tab-txt{color: #333333;}
.grey-tab-txt{color: #999999;}
.sel-person-list{position:relative;font-size: 14px;height: 45px;display: flex;display: -webkit-flex;align-items: center;justify-content: space-between;padding-right: 15px;}
.sel-person-list:before{content: "";position: absolute;height: 20px;width: 3px;background: #FAC135;}
.sel-person-list .class-name{margin-left: 15px;}
.sel-person-list .iconfont{font-size: 16px;}
.rep-for-btn{ border: 1px solid #399bf7; color:#399bf7; padding:0 12px;height: 22px;  border-radius: 15px;font-size: 12px;display: flex;display: -webkit-flex;align-items: center; position: absolute; right: 15px; cursor:pointer; top:17px}
.sel-person-list.sel-width .class-name{width: 180px;}

@media only screen and (max-width:360px ) {
	.sel-person-list.sel-width .class-name{width: 160px;}
}
@media only screen and (min-width:375px ) {
	.sel-person-list.sel-width .class-name{width: 180px;}
}
@media only screen and (width:320px ) {
	.sel-person-list.sel-width .class-name{width: 130px;}
}
/*授权管理*/
.appr-mana{margin-bottom: 10px;position: relative;}
.appr-mana .per-info{background: #fff;margin: 0;padding-left: 15px;}
.appr-mana .per-info dd{width: 100%;z-index: 9;}	
.appr-mana .per-info dd p{display: flex;display: -webkit-flex;justify-content: space-between;align-items: center;}
.appr-mana .opr-time{background: #fff;height: 48px;border-top: 1px solid #F4F4F4;justify-content: center;}
.appr-mana .opr-time .stat-enter{margin: 0 6px;}
.appr-mana .stat-label{position: absolute;right: 2px;}
.opr-time label{margin-right: 10px;}
.opr-time{display: flex;display: -webkit-flex;align-items: center;font-size: 14px !important;}
/*已审批*/
.per-info{border-bottom: 1px solid #F2F2F2;height: 75px;display: flex;display: -webkit-flex;align-items: center;padding-right: 10px;margin-left: 15px;}
.per-info dd{font-size: 15px;margin-left: 10px;}
.per-info .set-checkbox-style{display:flex;display:-webkit-flex;align-items:center;flex: 1;justify-content: flex-end;}
/*新增考勤组*/
.def-inp2{height: 34px;border: 1px solid #DADADA;outline: none;padding: 0 8px;}	
.check-inp{display: flex;display: -webkit-flex;flex-direction: column;}	
.check-title{font-size: 15px;display:flex;display:-webkit-flex;align-items:center;padding: 10px 15px 2px 15px;}
.remark-div{background: #fff;margin-top: 10px;padding: 10px 15px;}
.remark-div textarea{width: 93%;height:60px;padding: 10px;margin-top: 10px;border: 1px solid #ECECEC;}
/*请假*/
.upload-img{margin-top: 10px;background: #fff;padding: 10px 15px;}
.upload-img h5{font-size: 15px;}
.upload-img h5 span{font-size: 12px;}
.upload-img-list{display: flex;display: -webkit-flex;align-items: center;justify-content:flex-start;}
.upload-img-list li{width: 70px;height: 70px;position: relative;}
.upload-img-list li+li{margin-left: 10px;}
.upload-img-list li i.iconfont{background: rgba(0,0,0,0.5);position: absolute;right: 0;top: 0;width: 12px;height: 12px;}
.upload-img .pho-list{font-size: 0;margin: 0;}
.upload-img .pho-medi{display: inline-block;width: 25%;text-align: center;margin-top: 10px;vertical-align: top}
.upload-img .pho-medi div{position: relative;width: 100%;height: 70px;margin: auto;box-sizing: border-box;}
.upload-img .pho-medi div p{width:100% !important;}
.upload-img .pho-list div span,.upload-img .weui-uploader__file span{padding:0;cursor: pointer;background: rgba(6,6,6,0.5);top: 0 !important;right: 0 !important;left:inherit;font-size: 15px;position: absolute;transform:inherit;width: 18px;height: 18px;line-height: 16px;color: #fff;text-align: center;display: inline-block;}
.upload-img .add-medi{width: 70px;height: 70px;border: 1px solid #E6E6E6;}
.upload-img .add-medi {display: flex;display: -webkit-flex;align-items: center;justify-content: center;}
.upload-img .weui-uploader__file{ position:relative}
/*补卡申请*/
.appr-img .medi-pho{width: 50px;height: 50px;border-radius: 50%;}
.appr-img .pho-medi{width: 20%;}
.appr-img .add-medi{align-items: flex-start;}
.appr-img .add-medi{border: none;}
.appr-img .add-medi .iconfont{border:1px solid #e6e6e6;width: 50px;height: 50px;border-radius: 50%;}
/*考勤打卡公用*/
.out-container .def-top{margin-top: 0 !important;}
.out-container .def-top:before{content: none !important;}
.stat-enter{border: 1px solid #FFB954;padding:0 5px;color:#FFB954;font-size: 12px;height: 23px;min-width: 60px;display: flex;display: -webkit-flex;align-items: center;justify-content: center;border-radius: 20px;}
.stat-enter+.stat-enter{margin-left: 8px;}
.def-inp,.def-sel{height: 28px;border-radius: 20px;border:1px solid #DADADA;outline: none;padding: 0 15px;margin-right: 8px;}
/*消息中心*/
.mess-center-top{height: 46px;background: #fff;display: flex;display: -webkit-flex;align-items: center;justify-content: space-around;font-size: 15px;}
.mess-center-top li{display: flex;display: -webkit-flex;align-items: center;height: 100%;position: relative;}
.mess-center-top li .weui-badge{top: 3px!important;right: -6px!important;}
.mess-center-top li a{padding: 0 10px;}
.mess-center-top a.curr-mess{display: flex;display: -webkit-flex;align-items: center;height: 100%;border-bottom: 2.5px solid #58CAC2;color:#58CAC2;}
.mess-no-con{position:absolute;top:56px;bottom:0;width:100%;background:#fff;display: flex;display: -webkit-flex;align-items: center;flex-direction: column;}
.mess-no-con img{margin-bottom: 20px;margin-top: 40%;}
.mess-no-con p{font-size: 14px;color: #A6A6A6;}
.mess-person{background: #fff;margin-top: 10px;padding:12px 12px 10px 12px;}
.mess-person-top{display: flex;display: -webkit-flex;padding-bottom: 10px;border-bottom: 1px solid #F0F0F0;}
.mess-person-top .mess-pho{}
.mess-person-top .mess-txt{display: flex;display: -webkit-flex;flex:1;-webkit-flex:1;flex-direction: column;margin-left: 9px;}
.mess-person-top .mess-com-tit{display: flex;display: -webkit-flex;align-items: center;justify-content: space-between;font-size: 14px;}
.mess-person-bottom{display: flex;display: -webkit-flex;padding-top: 10px;}
.mess-person-bottom .mess-txt{margin-right: 14px;}
.comment-txt{margin-top:8px;font-size:15px;padding:5px 10px;display:inline-block;background: #F2F2F2;border-radius: 4px;position: relative;width: auto;word-wrap: break-word;min-height: 20px;height: auto;}
.comment-txt .triangle{width: 0;height: 0;border-top: 6px solid transparent;border-right: 7px solid #F2F2F2;border-bottom: 6px solid transparent;position: absolute;left: -6px;top: 11px;}
/*导出报表*/
.expo-repo{margin-top:10px }
.expo-repo .weui-cells{margin-top:0px !important;}	
.expo-repo .weui-cell:before{border-top: 1px solid #e5e5e5;}		
.expo-repo	.weui-cells::before{border-top: none}
.check-title label{ display: inline-block; position: absolute;right: 15px; cursor: pointer}
/*体测指导*/
.red-left:before{background:#FF5A5A}
.phy-list{background: #fff;padding-bottom: 15px;margin-bottom: 10px;}			
.phy-list p{font-size: 12px;margin: 0 10px 0 15px;color: #707070;line-height: 20px;}
.phy-list .phy-p-tit{font-size: 14px;color: #FF485F;margin: 5px 15px;}
/*修改下班打卡结果*/
.stat-box{background: #fff;margin: 0 auto;border-radius: 10px;}
.stat-box h5{height: 45px;font-size: 17px;display: flex;display: -webkit-flex;align-items: center;justify-content: center;padding-top: 10px;}
.popbox-ul li{line-height: 35px;display: flex;display: -webkit-flex;justify-content: space-between;margin:0 15px;border-bottom: 1px dashed #DFDFDF;font-size: 13px;}
.popbox-ul li:last-child{border-bottom: none;}
.sel-opr-btn{height: 45px;display: flex;display: -webkit-flex;align-items: center;border-top: 1px solid #F2F2F3;margin-top: 10px;}
.sel-opr-btn li{width: 50%;border-right: 1px solid #F2F2F3;box-sizing: border-box;height: 100%;display: flex;display: -webkit-flex;align-items: center;justify-content: center;}
.sel-opr-btn li:last-child{border-right: 0;}
.sel-opr-btn li.curr-opr{color: #409EF7;}
.popbox-ul .stat-tab li{margin: 0;justify-content: center;}
.stat-box .stat-tab,.stat-box .stat-tab li{height: 24px;line-height: 24px;}
.stat-box .stat-tab li:after{height: 24px;}
/*查看打开月历*/
.leave-style{ background: #fff; color: #333333; font-size: 14px; line-height: 40px; }
.leave-style label{ width: 6px; height: 6px; vertical-align: middle;border-radius: 50%; display: inline-block; margin: 0 5px 0 15px}
/*园所二维码*/			
.scan-content .code-scan{position:relative;background: #fff;margin-bottom: 10px;padding-top: 10px;}
.scan-content .code-scan:nth-of-type(1){padding-top: 0;}
.scan-content .code-scan:before{content: "";position: absolute;bottom:-22px;left:25px;width: 8px;height: 34px;background: url(../images/link_icon.png)no-repeat;background-size: 8px 34px;z-index: 9;}
.scan-content .code-scan:after{content: "";position: absolute;bottom:-22px;right:25px;width: 8px;height: 34px;background: url(../images/link_icon.png)no-repeat;background-size: 8px 34px;z-index: 9;}
.scan-content .code-scan:last-child:before,.scan-content .code-scan:last-child:after{content: none;}			
.code-scan .weui-cells .weui-cell__bd{font-size: 15px;}
.code-scan .weui-cells .weui-cell__ft{font-size: 14px;}
.code-scan .weui-cells .weui-form-preview__item{display:-webkit-box;display:-webkit-flex;display:flex;}
.code-scan .weui-cells .weui-form-preview__item .weui-form-preview__label{font-size: 15px;color: #333;}
.code-scan .weui-cells .weui-form-preview__item .weui-form-preview__value{font-size: 15px;color: #999;}
.redbg-tit{position: relative;display:-webkit-box;display:-webkit-flex;display:flex;align-items:center;justify-content:center;background: #FFE6E6;color: #FF485F;font-size: 15px;height:44px;}
.link-img{position: absolute;right: 15px;}
/*幼儿请假*/
.askleave-stat .secondary-stat{height: 160px;padding: 49px 15px 19px 15px;}
.askleave-stat .label-act-btn{border: none;}
.askleave-stat .devide-num{padding-bottom: 0;height: 30px;}
.askleave-stat .num-stat{align-items: center;}
.askleave-stat .deepen-txt{background: #44BBB6;display: table-cell;padding: 2px;font-size: 12px;}
.num-stat{display:-webkit-box;display:-webkit-flex;display:flex;justify-content: space-around;width: 80%;}
ul.askle-list{display:-webkit-box;display:-webkit-flex;display:flex;justify-content: space-between;height: 50px;background: #fff;}
ul.askle-list li{display:-webkit-box;display:-webkit-flex;display:flex;flex-direction:column;width: 25%;align-items: center;justify-content: center;position: relative;}
ul.askle-list li:after{content: "";position: absolute;right:0;border-right: 1px solid #F0F0F0;height: 70%;}
ul.askle-list li:nth-child(5):after{content: none;}
ul.askle-list li .askle-num{font-size: 15px;}
ul.askle-list li .askle-date{font-size: 10px;color:#999; }
ul.askle-list li.current{background: #FFF7E4;color: #F7A607;}
#divclasslist ul.askle-list li:nth-child(1){border-bottom: 4px solid #08D2FB;}
#divclasslist ul.askle-list li:nth-child(2){border-bottom: 4px solid #29FF6E;}
#divclasslist ul.askle-list li:nth-child(3){border-bottom: 4px solid #E27707;}
#divclasslist ul.askle-list li:nth-child(4){border-bottom: 4px solid #E42D02;}
#divclasslist ul.askle-list li:nth-child(5){border-bottom: 4px solid #CECECE;}
.askle-class-total ul.askle-list li{border-bottom: none;color: #999;}
.askle-class-total ul.askle-list li:nth-child(3){background: none;color: #999;}
.askle-class-total ul.askle-list li:after{content: "";position: absolute;right:0;border-right: 1px solid #F0F0F0;height: 70%;}
.stat-time{position:relative;padding-top:5px;font-size: 9px;}
.stat-time:after{content: " ";position: absolute;right: 0;left:0;height: 1px;top: 0;border-top: 1px solid #fff;color: #fff;-webkit-transform-origin: 100% 0;transform-origin: 100% 0;-webkit-transform: scaleY(0.5);transform: scaleY(0.5);}
.green-left:before {background: rgb(0, 205, 145);}
.askle-class{margin-top: 10px;}
.askle-class .sel-person-list{height: 36px;}
.askle-class .sel-person-list .iconfont{color: #ccc;}
.askle-sum-num{display:-webkit-box;display:-webkit-flex;display:flex;justify-content: center;background: #fff;}
.askle-sum{height: 44px;background: #fff;display:-webkit-box;display:-webkit-flex;display:flex;justify-content: space-around;align-items: center;width: 96%;}
.askle-sum .sum-date{font-size: 12px;color: #999;}
.askle-sum a{display:-webkit-box;display:-webkit-flex;display:flex;align-items: center;justify-content: center;display:table-cell;vertical-align:middle;height:25px;padding:0 6px;line-height:25px;border-radius: 16px;}
.askle-sum a.askle-gold{border: 1px solid #FAC458;color:#FAC458;font-size: 10px}
.askle-sum a.askle-pink{border: 1px solid #FFA0B6;color:#FFA0B6;font-size: 10px}
.askle-sum a.askle-purple{border: 1px solid #D6AEFF;color:#D6AEFF;font-size: 10px}
.askle-sum a.askle-blue{border: 1px solid #7CBDFA;color:#7CBDFA;font-size: 10px}
.askle-detail{background: #fff;border-top: 1px solid #E8E8E8;position: relative;}
.askle-detail .per-info{border-top: 0;border-bottom: 1px dashed #E8E8E8;}
.divaskdetail.nodata{
	text-align: center;
	padding: 30px;
	background: #fff;
}
.askle-detail .per-info dd{-webkit-box-flex:1;-webkit-flex:1;flex: 1;}
.askle-greet{color: #4FA4F8;border: 1px solid #4FA4F8;font-size: 11px;height:24px;line-height:24px;padding:0 12px;border-radius: 15px;display: table-cell;vertical-align: middle;}
ul.askle-bill{padding: 10px 15px;}
ul.askle-bill li{display:-webkit-box;display:-webkit-flex;display:flex;font-size: 12px;line-height: 24px;color: #A2A2A2 }
ul.askle-bill li .bill-left{display:-webkit-box;display:-webkit-flex;display:flex;justify-content:space-between;width: 48px;text-align: right;}
ul.askle-bill li .bill-right{-webkit-box-flex:1;-webkit-flex:1;flex: 1;}
ul.askle-bill li .bill-left .font-spa{display:-webkit-box;display:-webkit-flex;display:flex;justify-content: space-between;width: 100%}
.askle-mark{width: 34px;position: absolute;top: 0;left: 0;}
/*请假轨迹*/
.askle-tend{margin-bottom: 10px;background: #fff;}
.askle-tend .mul-table-top{display:-webkit-box;display:-webkit-flex;display:flex;height: 44px;font-size: 15px;background: #00cf91;color: #fff;align-items: center;justify-content: center;}
.askle-tend .mul-stat-num{border: none;border-radius: 0;padding: 20px}
.askle-tend .stat-tab{width: 280px;margin: 15px auto 0 auto;}
.askle-tend .stat-tab li{-webkit-box-flex:inherit;-webkit-flex:inherit;flex: inherit;}
.askle-tend .stat-tab li.tab-txt{-webkit-box-flex:1;-webkit-flex:1;flex: 1;}
/*幼儿出勤*/
.participate-stat .stat-item-list{flex-direction: inherit;}
.participate-stat .label-act-btn{border: none;}
.participate-stat .devide-num{padding-bottom: 0;height: 30px;}
.participate-stat .secondary-stat{padding: 0;height: 120px;}
.participate-stat .stat-item-list .stat-mess p:nth-child(1){width: 40px;}
.tab-parti{/*background: #53CAC4;*/padding: 15px 0 35px 0;}
.tab-parti .stat-tab{border: 1px solid #fff;height: auto;margin: 0 auto;}
.tab-parti .stat-tab li{color: #fff;}
.tab-parti .stat-tab li.curr-stat{background:#fff;color: #00cd91;}
.tab-parti .stat-tab li:after{border-right: 1px solid #fff;}
.red-border-txt{width:76px;text-align:center;color: #FF6565;border: 1px solid #FF6565;font-size: 12px;height: 24px;line-height: 24px;border-radius: 15px;display: table-cell;vertical-align: middle;}
.red-border-txt img{vertical-align: top;margin-top: 4px;margin-right: 3px;}
.parti-detail .per-info{height: 60px;margin-left: 35px}
.parti-detail .per-info dd{-webkit-box-flex:1;-webkit-flex:1;flex: 1;}
.divclasslist{background: #fff;}
.divclasslist .per-info{height: 60px;}
.divclasslist .per-info:nth-child(1){border-top: none}
.divclasslist .per-info .info-pic{display:-webkit-box;display:-webkit-flex;display:flex;}
.divclasslist .per-info .info-txt{-webkit-box-flex:1;-webkit-flex:1;flex: 1;font-size: 15px;margin-left: 10px;}
.parti-detail{background: #fff;position: relative}
.sort-num{width: 22px;height: 22px;line-height:22px;border-radius:50%;text-align:center;background: #D2D2D2;display: table-cell;vertical-align: middle;font-size: 12px;color: #fff;}
/*请假单详情*/
.askle-box .popbox-ul li{align-items: center;}
.askle-box .stat-tab{border: 1px solid #00cd91;}
.askle-box .stat-tab li{color: #00cd91;border-bottom: none}
.askle-box .popbox-ul .stat-tab li{display: table-cell;text-align: center;font-size: 12px;}
.askle-box .stat-tab li.curr-stat{background: #00cd91;color: #fff}
.askle-box .stat-tab li:after{border-right: 1px solid #00cd91;}
.select-type{width: 100%;justify-content: space-between;color: #7C7C7C;position: relative;}
.select-type i{position: relative;}
.select-type i{text-align: center;}
.select-type i:last-child:after{border: none;}
.select-type i:after{content: "";height:14px;position: absolute;right:0;top:50%;margin-top:-7px;border-right: 1px solid #D3D3D3;}
/*出勤点名*/
.oneblock{width: 86%;margin: 0 auto;display:-webkit-box;display:-webkit-flex;display:flex;align-items: center;justify-content: center;height:24px;line-height:24px;}
.chuqinstuname,.eatstuname{
	width: 20%;
	color: #383838;
	margin: 0 auto;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	align-items: center;
	justify-content: center;
}
.spchuqindm,.speatopt{
	width:80%;
	margin: 0 auto;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	align-items: center;
	justify-content: center;
}
.leavtype0 a,.leavtype1 a,.leavtype2 a,.leavtype3 a,.leavtype4 a{border-radius: 2px;position:relative;display:-webkit-box;display:-webkit-flex;display:flex;align-items: center;justify-content: center;width: 52px;font-size: 12px; }
.leavtype0 a{border: 1px solid #cccccc;}
.leavtype1 a .shuaka{position: absolute; left: 2px; width: 5px;}
.leavtype1 a .shuaka i.round{ display: block; width:5px; height: 5px;  border-radius: 50%; background: #ff6464; margin-bottom:2px; vertical-align: middle }
.leavtype1 a .shuaka i.round.song{ background: green; }
.leavtype1 a .shuaka i.round.jie{ background: #ff6464; }
.classchuqintab .leavtype1.current a,.speatopt .leavtype1.current a{border:1px solid #58CBC3;color: #58CBC3!important;}
.classchuqintab .leavtype2.current a{border:1px solid #F5A428;color: #F5A428!important;}
.classchuqintab .leavtype3.current a{border:1px solid #FF6F90;color: #FF6F90!important;}
.classchuqintab .leavtype4.current a{border:1px solid #C184FF;color: #C184FF!important;}
.classchuqintab .leavtype1 .txt-bg{background: url(../images/green_bg.png)no-repeat;width: 16px;height: 15.5px;position: absolute;right: 0;top: 0;}
.classchuqintab .leavtype2 .txt-bg{background: url(../images/yellow_bg.png)no-repeat;width: 16px;height: 15.5px;position: absolute;right: 0;top: 0;}
.classchuqintab .leavtype3 .txt-bg{background: url(../images/pink_bg.png)no-repeat;width: 16px;height: 15.5px;position: absolute;right: 0;top: 0;}
.classchuqintab .leavtype4 .txt-bg{background: url(../images/purple_bg.png)no-repeat;width: 16px;height: 15.5px;position: absolute;right: 0;top: 0;}
.classchuqintab .leavtype1 .exp-mark{background: #53CAC3;}
.classchuqintab .leavtype2 .exp-mark{background: #F3A338;}
.classchuqintab .leavtype3 .exp-mark{background: #FF6F90;}
.classchuqintab .leavtype4 .exp-mark{background: #C184FF;}
.classchuqintab .leavtype1,.classchuqintab .leavtype2,.classchuqintab .leavtype3,.classchuqintab .leavtype4{position: relative;}
.exp-mark{border-radius:2px 0 0 2px;font-size: 10px;position: absolute;left: 0;display:-webkit-box;display:-webkit-flex;display:flex;flex-direction: column;align-items:center;justify-content:center;height: 100%;line-height: 11px;padding: 0 1px;}
/*选中的时候有背景*/
.current .exp-mark{background: #F5F5F5;color: #fff;}
.exp-mark i.yellow-exp{color: #FAC458;}
.exp-mark i.blue-exp{color: #4BC7EE;}
.tim-mark{font-size: 5px;transform: rotate(45deg);-ms-transform: rotate(45deg);-webkit-transform: rotate(45deg);position: absolute;right: -1px;top: -7px;color: #fff;}
.absent-tab{display:-webkit-box;display:-webkit-flex;display:flex;align-items:center;justify-content:space-between;padding:0 20px;color:#999;font-size:12px;height: 42px;background: #fff;}
.absent-tab li{height: 42px;line-height: 42px;display:-webkit-box;display:-webkit-flex;display:flex;align-items: center;justify-content: center;}
.absent-tab li.curr-tab{color: #FAC132;border-bottom: 2px solid #FAC132;}
.classchuqintab{background: #fff;}
.classchuqintab .oneblock a{font-size: 12px;color: #999;}
.classchuqintab .weui-cell:before{left: 0;}
.icon-mark{display:-webkit-box;display:-webkit-flex;display:flex;flex-direction: column;align-items: center;justify-content: center;position: absolute;height: 100%;}
.classchuqintab .toptip{ background:#ffa922; color:#fff; line-height:22px;  font-size:14px; padding:5px}
/*就餐登记*/
.dinner-con{background: #fff;margin-left: 15px;}
.dinner-con h5{font-weight: normal;justify-content: space-between;padding-right: 15px;height: 58px;align-items: center}
.classbabylist{justify-content: space-between;}
.classbabylist li{position:relative;height:48px;padding-right: 15px;display:-webkit-box;display:-webkit-flex;display:flex;justify-content: space-between;align-items:center;border-top: 1px solid #F4F4F4;font-size: 15px;}
.classbabylist li .din-mark{display:-webkit-box;display:-webkit-flex;display:flex;align-items:center;justify-content:center;text-align:center;position: absolute;left:-15px;top:6px;background: #52D2CC;width: 14px;height:36px;line-height:14px;font-size: 11px;padding-top:2px;color: #fff;}
.din-num{border-top: 1px solid #EDEDED;width: 45px;text-align: center;}
.btnnoeat{display:-webkit-box;display:-webkit-flex;display:flex;align-items: center;justify-content: center;color: #FBCC58;border-bottom: 1px solid #FBCC58;font-size: 12px;line-height:16px;}
input.sum-inp{border: none;border-bottom: 1px solid #FAA296;width: 84%;outline: none;text-align: center;line-height: 18px ;}
.dinner-sum{padding-right: 15px;border-top: 1px solid #EDEDED;/*height: 44px;*/font-size: 12px;align-items: center;}
.dinner-con h5 .oneblock{width: 86%;margin: 0 auto;display:-webkit-box;display:-webkit-flex;display:flex;align-items: center;justify-content: center;flex-direction: column;}
.dinner-sum .oneblock{flex-direction: column;justify-content: flex-end;height: 42px}
.dinner-stat{display:-webkit-box;display:-webkit-flex;display:flex;}
.dinner-stat p{color: #9C9C9C;display:-webkit-box;display:-webkit-flex;display:flex;flex-direction:column;align-items:center;justify-content:center;margin:0 5px;font-size: 12px;}
.dinner-stat p span{color: #9C9C9C;border-bottom: 1px solid #E8E9E9;}
.stat-card{position: relative;margin-left: 3px;padding-left: 3px;}
.stat-card:before{content: "";position: absolute;left: 0;height: 60%;top: 20%;width: 1px;background: #dddddd;}
.dinner-stat .stat-card p span{border-bottom: none;border-top: 1px solid #E8E9E9;}
/*出勤点名（家长请假）*/
.weui-cell:before {
    border-top: 1px solid #e5e5e5;
}
.askle-cells .weui-cell{position: relative;padding-left: 20px}
.askle-cells .icon-mark{position: absolute;left: 30px;}
.askle-cells .oneblock{width: 100%;margin-right: 10px;}
.recom-div{display:-webkit-box;display:-webkit-flex;display:flex;align-items:center;justify-content:center;color: #FF5A5A;height: 40px;border-top: 1px solid #F3F3F3;font-size: 12px;}
.askle-show{border-bottom: 1px solid #F5F5F5;}
.askle-show .oneblock{margin-right: 0;}
.askle-show .weui-cell{padding-left: 50px;}
.askle-show ul.askle-bill{position: relative;padding-left:30px ;}
.askle-show ul.askle-bill:before{content:"";position: absolute;top:-15px;left:15px;width: 8px;height: 34px;background: url(../images/link_icon.png)no-repeat;background-size: 8px 30px;}
.askle-show ul.askle-bill:after{content:"";position: absolute;top:-15px;right:15px;width: 8px;height: 34px;background: url(../images/link_icon.png)no-repeat;background-size: 8px 30px;}
/*幼儿出勤（应到未请假）*/
.greet-list{margin-bottom: 10px;}
.greet-list .gray-txt .iconfont{color: #ccc;font-size: 14px}
.greet-label{margin-left: 6px;background: #399BF7;color: #fff;font-size: 11px;display: table-cell;vertical-align: middle;padding: 1px 5px 0px 5px;border-radius: 2px;}
.ungreet-label{margin-left: 6px;background: #ccc;color: #fff;font-size: 11px;display: table-cell;vertical-align: middle;padding: 1px 5px 0px 5px;border-radius: 2px;}
.greet-list .per-info .greet-name{display:-webkit-box;display:-webkit-flex;display:flex;align-items: center;}
.greet-name .current{height: 24px;line-height: 24px;margin-left: 10px}
.rela-person{position: relative;margin-left: 10px;}
.rela-person .person-img{width: 20px;height: 20px;}
.rela-person .wx-img{width: 13px;position: absolute;top: -7px;right:-6px;}
.askle-form{background: #fff;padding: 20px 0 10px 0;}
.askle-div{border: 1px solid #5C3F41;margin: 0 15px;border-radius: 4px;position: relative;padding: 26px 15px 10px 15px;}
.askle-img{position: absolute;left: 50%;margin-left: -135px;top:0;margin-top: -13px}
.askle-div h5{font-size: 15px;}
.askle-div p{font-size: 13px;text-indent: 26px;}
/*请假-图例说明*/
.legend-list{background: #fff;margin-bottom: 10px;}  
.legend-list .legend-tit{height: 40px;line-height: 40px;font-size: 15px;color: #ff5a5a;font-weight: bold;padding: 0 15px;}    
.legend-list .legend-txt{display: -webkit-box;display: -webkit-flex;display: flex;padding:10px 15px 10px 0;line-height: 20px;font-size: 12px;color: #999;margin: 0 0 0 15px;border-top: 1px solid #F4F4F4;}
.legend-list ul.classbabylist li{padding:0 20px;}
.legend-list .icon-mark{left: 14px;}
.legend-list .leavtype1 a,.legend-list .leavtype2 a,.legend-list .leavtype3 a,.legend-list .leavtype4 a{color: #A8A8A8;}
.legend-list .askle-detail-show{background:#F3F4F6;padding-top: 10px;}
.legend-list .askle-detail-show .stat-box{width: 85%;padding-bottom: 15px;}
/*家长接宝宝详情页*/
.meet-baby{ overflow: hidden;display: -webkit-box;display: -webkit-flex;display: flex;justify-content: space-around;width:70%; margin: 20px auto 0 auto}
.meet-stat-list{display: flex;display:-webkit-flex;align-items: center;flex-direction: column;}
.meet-stat-list .meet-num{border: 3px solid #008af4;width: 100px;height:100px;border-radius: 50%;display: flex;display:-webkit-flex;justify-content: center;align-items: center;flex-direction: column;line-height: 20px }
.meet-stat-list .meet-num p:nth-child(1){font-size: 32px;}
.meet-stat-list .meet-num p:nth-child(2){font-size: 12px; }
.meet-num i{font-size:12px }
.alrmeet-babytit{line-height: 20px; font-size: 12px; color: #fff; padding-left: 12px; margin-top: 10px }
.alrmeet-baby{ overflow: hidden; margin-right: 10px;}
.alrmeet-babydiv{float: left;padding: 10px 0 0 0; width: 33.33333333%;box-sizing: border-box; }
.alrmeet-badylist{ color: #0090ff; background: #fff;  border-radius: 5px; padding:15px 10px; margin-left: 10px;}
.alrmeet-bodyleft{float:left; font-size:15px; font-weight: bold;margin-bottom:5px;line-height:20px}
.alrmeet-bodyright{background: url(../images/boby_icobg.png) no-repeat; color: #fff; width:35px;height: 20px;background-size: 100% 100%;text-align: center;font-size: 11px;line-height:20px;display: inline-block;float: right; }
.alrmeet-badylist p{ clear: both; border-top:1px solid #d7d7d7; padding-top:5px; font-size:12px}
.alrmeet-body-obj{margin:0 5px 0 5px;}
.alrmeet-body-obj label{border: 1px solid #fff;color: #fff;font-size: 15px;font-weight: bold;border-radius: 4px;padding: 2px 7px; display: inline-block; width: 23%; text-align: center;  margin:5px 0px 5px 5px}
@media screen and (min-width:300px) and (max-width:360px) {
	.alrmeet-badylist{ padding:10px 5px;margin-left: 5px;}
    .alrmeet-baby{ margin-right: 5px;}
}
/*摄像头管理-有无摄像头*/
.camera-con{margin: 0 15px 0 10px;display:-webkit-box;display:-webkit-flex;display:flex;flex-wrap: wrap;}
.camera-con li{width: 33.3%;height: 135px;margin-bottom: 5px;}
.camera-list{/*border: 1px solid #D0D0D0;*/height: 135px;margin-left: 5px;box-sizing: border-box;position: relative;text-align: center; border-radius: 8px;    background: #4dbdc8;}
.camera-list .state-label{background: url(../images/labelbg_gray.png);display: inline-block;width: 48px;height: 48px;background-size: 48px 48px;position: absolute;right: 0;top: 0;    border-top-right-radius: 8px;}
.camera-list .state-label i{
    position: absolute;
    top: 7px;
    right: 1px;
	font-size: 10px;
	color: #ffffff;
	display: inline-block;
	transform:rotate(45deg);
	-ms-transform:rotate(45deg); 	/* IE 9 */
	-moz-transform:rotate(45deg); 	/* Firefox */
	-webkit-transform:rotate(45deg); /* Safari 和 Chrome */
	-o-transform:rotate(45deg); 	/* Opera */
}
.camera-list p{font-size: 13px;color: #ffffff;font-weight: bold;text-align: center;margin: 0 5px;}
.camera-list.current .state-label i{position: absolute;top: 7px;right: 5px;}
.camera-list.current{border: none;background: #4dbdc8;}
.camera-list.current .state-label{background: url(../images/labelbg_gre.png);border-top-right-radius: 8px;}
.camera-list.current .icon_camera:before{color: #fff;}
.camera-list .icon_camera:before{color: #fff;}
.camera-list.current p{color: #fff;}
.icon_camera{margin-top: 25px;display: inline-block;height: 60px;line-height: 60px;}
.mark-pro{background: #FF3636;height: 18px;font-size: 10px;color: #ffffff;display:-webkit-box;display:-webkit-flex;display:flex;align-items: center;justify-content: center;padding: 0 10px 0 4px;border-radius: 0 10px 10px 0;position: absolute;top: 8px;left: 0;}
.mark-pro .icon_remind{display:-webkit-box;display:-webkit-flex;display:flex;align-items: center;justify-content: center;padding-right: 2px;}
.delete-img{position: absolute;top: 54px;right: 23px;}
/*摄像头管理-摄像头信息*/
.control-area{height: 225px;position: relative;}
.control-area h5{position: absolute;top: 0;font-size: 15px;color: #ffffff;width: 100%;text-align: center;margin: 7px 0;z-index: 9;}
.control-con{/*position:  absolute;width:  100%;top: 225px;bottom:  98px;*/align-items:  center;justify-content: space-around;}
.opr-detail{background: #3391dc;border-radius: 28px;width: 140px;height: 40px;font-size: 12px;/*flex-direction: column;*/align-items: center;justify-content: center; margin-right: 15px;}
.opr-detail .iconfont{height: 24px;line-height: 26px;}			
.opr-camera{width: 144px;height: 144px;background: #D0D0D0;border-radius: 50%;align-items: center;justify-content: center;}
.opr-camera .icon_camera:before{font-size: 70px;}
.opr-camera.current{background: #ffffff;box-shadow: 0 0 2px 15px #0075c0;}
.def-switch{display:-webkit-box;display:-webkit-flex;display:flex;align-items: center;justify-content: center;}
.def-switch .weui-switch-cp__box:before{background-color: #fff!important;} 
.def-switch .weui-switch-cp__box:after{content: "关";font-size: 13px;display:-webkit-box;display:-webkit-flex;display:flex;align-items: center;justify-content: center; color: #fff}
.def-switch .weui-switch-cp__input:checked ~ .weui-switch-cp__box, .weui-switch:checked{border-color: #fff }
.def-switch .weui-switch-cp__input:checked~.weui-switch-cp__box:after{content: "开";}
.def-switch .weui-switch,.def-switch .weui-switch-cp__box{width: 60px;height: 32px;border-radius: 20px;border: 1px solid #135E8F;}
.def-switch .weui-switch-cp__box:after,.def-switch .weui-switch:after{width: 28px;height: 28px;border-radius: 50%;top: 1px; color: #999999; background:#d6d6d6 }
.def-switch .weui-switch-cp__box:before,.def-switch .weui-switch:before{width: 60px;height: 30px;border-radius: 20px;background:#FFF }
.def-switch .weui-switch-cp__input:checked~.weui-switch-cp__box:after,.def-switch .weui-switch:checked:after{-webkit-transform: translateX(30px);transform: translateX(30px);color: #fff; background:#3391dc}



.def-switch .weui-switch-cp__input:checked~.weui-switch-cp__box,.def-switch .weui-switch:checked{background-color: #ffffff;}				
.opr-menu{display:-webkit-box;display:-webkit-flex;display:flex;align-items: center;justify-content: center;position: fixed;background: #267fe1;bottom: 20px;height: 66px; margin: 0 20px; border-radius: 35px; width:90% }
.opr-menu li{position: relative;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;align-items: center;justify-content: center;flex-direction: column;font-size: 12px;color: #ffffff;}
.opr-menu li:after{content:"";border-right: 1px solid #ffffff;width: 1px;height: 70%;position: absolute;top: 15%;right: 0;}
.opr-menu li:last-child:after{border-right: none;}
.opr-menu li .iconfont{display:-webkit-box;display:-webkit-flex;display:flex;height: 34px;line-height: 34px;}
.opr-menu li .weui-switch-cp{margin-bottom: 5px;}
.opr-menu.no-line{height: 97px;}
.opr-menu.no-line li:after{border-right: none;}
.opr-menu.no-line li .weui-switch-cp.current,.opr-menu.no-line li .weui-switch-cp.current .iconfont:after{color: #00E05A;}
.control-video{margin: 30px 20px 5px 10px;align-items: center;justify-content:center;}
@media only screen and (max-width: 360px) {
	.control-video{margin: 30px 20px 5px 10px;}
}
@media only screen and (max-height: 570px) {
	.opr-camera{width: 120px;height: 120px;}
	.def-switch .weui-switch-cp{margin-top: 3px!important;}
	.opr-menu.no-line{height: 70px;}
	.opr-camera .icon_camera:before{font-size: 60px;}
	.control-con{bottom: 71px!important;}
}
/*视频监控*/
.opr-video{height: 50px;background: #ffffff;align-items: center;justify-content:space-between;padding: 0 0 0 15px;}
.opr-video ul li{position: relative;display:-webkit-box;display:-webkit-flex;display:flex;align-items: center;justify-content: center;font-size: 13px;color: #999999;padding: 0 15px;}
.opr-video ul li:after{content: "";border-right: 1px solid #999999;position: absolute;right: 0;width: 1px;height: 60%;}
.opr-video ul li:last-child:after{border-right: none;}
.opr-video .icon_time:before{font-size: 20px;}
.opr-video .iconfont{margin-right: 3px;}
/*添加摄像头*/
.line-bto-input .icon_time:before{color: #53b8ff;font-size: 22px;}
.def-weui-cells .weui-cell__bd{ font-size: 15px;}
.def-weui-cells.set-time .weui-cell{padding: 13px 20px;background: #F7F7F7;}
.def-weui-cells .weui-cell{ color: #fff}
.def-weui-cells .weui-cell::before{ border-top:none}
.def-weui-cells .set-time .weui-cell__ft span{margin-right: 10px;}
.def-weui-cells .weui-switch-cp__input:checked ~ .weui-switch-cp__box,.def-weui-cells .weui-switch:checked{border-color: #fff;background-color: #fff}
.def-weui-cells .weui-switch,.def-weui-cells .weui-switch-cp__box{border:1px solid #dfdfdf;background-color:#dfdfdf;}
.def-weui-cells .weui-switch-cp__box:before,.def-weui-cells .weui-switch:before{border-radius:15px;background-color:#fff;}
.def-weui-cells .weui-switch-cp__box:after,.def-weui-cells .weui-switch:after{background-color:#dfdfdf;box-shadow:0 1px 3px rgba(0,0,0,.4);}
.def-weui-cells .weui-cells::before{ border-top: 1px solid #3395d8}
/*.weui-cells.def-weui-cells:after, .weui-cells.def-weui-cells:before{content: none;}*/
.shadow-box .weui-cells.def-weui-cells .weui-cell__bd{-webkit-box-flex: inherit;-webkit-flex: inherit;flex: inherit;}
.shadow-box .weui-cells.def-weui-cells .weui-cell__ft{-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;}
.shadow-box .btn-false{position: absolute;top: 5px;right: 5px;}
.shadow-box .btn-false:before{color: #D9D9D9;}
.shadow-box .left-name{width: 110px;text-align: right;}
.shadow-box .mon-name{width: 110px;text-align: left;}
.range-label{flex-wrap: wrap;margin: 0 25px;}
.range-label li{position: relative;border: 1px solid #EAEAEA;font-size: 13px;color: #999999;padding: 8px 12px;min-width: 30.3%;margin: 4px 1.5%;text-align:center;border-radius: 5px;box-sizing: border-box;}
.range-label li.current{border: 1px solid #53B8FF;color: #53B8FF;}
.range-label .icon_check{position: absolute;top: -11px;right: -3px;}
/*通用设置*/
.set-tit{color: #fff;font-size: 15px;display:-webkit-box;display:-webkit-flex;display:flex;align-items: center;justify-content: space-between;margin: 0px 15px 8px 15px; padding-top: 15px}											
.set-public{display:-webkit-box;display:-webkit-flex;display:flex;align-items: center;padding: 30px 15px; background:#fff }
.set-locks{display:-webkit-box;display:-webkit-flex;display:flex;align-items: center;justify-content: center;background: #4bbeff;width: 65px;height: 65px;border-radius: 50%;}
.set-public .weui-flex__item{display:-webkit-box;display:-webkit-flex;display:flex;align-items: center;justify-content: center;flex-direction: column;border-right: 1px solid #ffffff;}
.set-public .weui-flex__item p{font-size: 12px;color: #b9b9b9;margin-top: 5px;}
.set-public .weui-flex__item:last-child{border-right: none;}
/*修改消息*/
.message-cell .weui-cell{background: #ffffff;margin: 7px 15px;box-shadow: 0 0 5px #eeeeee;}	
.label-list{align-items: center;justify-content: center;background: #ffffff;height: 65px;box-shadow: 0 0 5px #eeeeee;}
.label-list li{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;align-items: center;justify-content: center;color: #d1d1d1;border-right: 1px solid #D6D6D6;box-sizing: border-box;height: 32px;font-size: 12px;}
.label-list li:last-child{border-right: none;}
.label-list li .iconfont{margin-right: 7px;position: relative;}
.label-list li.current{color: #F4C343;}
.label-list li.current .iconfont{color: #F4C343;}
/*园长特权（修改）*/
.privi-list{margin: 7px 15px!important;border-radius: 4px;}
.privi-txt{font-size: 12px;color: #999999;border-top: 1px solid #ededed;margin: 0 9px;padding: 12px 0;}
/*查看群组消息*/
.group-mess p{font-size: 12px;color: #333333;}
.group-mess p a{color: #333333;}
.index_ico lable{ font-size: 10px}
.group-mess  img{height: 45px; width: 45px; }
.group-mess .weui-grid{width:20%; padding: 8px 0 ; box-sizing: border-box;}
.group-mess .weui-grid__icon{ height: 45px;width: 45px;}
.group-mess .weui-grid__label{color: #333333;font-size: 12px;}
.group-mess .weui-grids::before{border-top: none}
.group-mess .weui-grid::before{border-right: none}
.group-mess .weui-grid::after{border-bottom: none}
.indexdiv_item{ padding-bottom: 10px;}
.group-mess{ padding-bottom: 20px; background:#fff; overflow:hidden}
.group-mess .weui-grid{padding: 20px 10px 0 10px;}
/*生日祝福*/
.bir-cake{ background:#fff; border-radius:5px; margin:30px; overflow:hidden; text-align:center; padding:20px 0}
.bir-cake h3{ color:#333333; font-size:15px}
.bir-cake p{ color:#999999; font-size:13px; padding:0 40px; line-height:20px; text-align:left}
.bir-cakelist p{font-size: 12px;color: #333333;}
.bir-cakelist p a{color: #333333;}
.bir-cakelist .weui-grid__icon img{height: 50px; width: 50px;margin: 0px auto; padding-top:5px}
.bir-cakelist .weui-grid{width:20%; padding: 8px 0 ; box-sizing: border-box; }
.bir-cakelist .weui-grid__icon{ height: 60px;width: 60px; background:#fff; border-radius:5px; text-align:center;}
.bir-cakelist .weui-grid__icon.selectbg{ border:2px solid #45a5ff}
.bir-cakelist .weui-grid__label{color: #333333;font-size: 12px;}
.bir-cakelist .weui-grids::before{border-top: none}
.bir-cakelist .weui-grid::before{border-right: none}
.bir-cakelist .weui-grid::after{border-bottom: none}
.bir-cakelist{ padding-bottom: 20px; overflow:hidden}
.bir-cakelist .weui-grid{padding: 0px 10px 0 10px;}

@media screen and (min-width:300px) and (max-width:375px) {
.bir-cakelist .weui-grid__icon img{height: 40px; width: 40px;}
.bir-cakelist .weui-grid__icon{ height: 50px;width: 50px;}
.bir-cakelist .weui-grid{padding: 0px 0px 0 10px;}
.bir-cakelist .weui-grid{width:19%; }
}
/*查看生日祝福*/
.viewbirlist{width: 90%;height: 85%;top:5%;bottom:10%;left:5%;right: 5%; z-index: -1; position: absolute; }
.viewbirlist img{ display: block;outline: none;border:0;height: 100%;width: 100%;}
.viewbirbot{ position:absolute;z-index: 1000; text-align: center; overflow: hidden; top: 80px; width: 100%;}
.viewbirbot img{ width: 100px; height: 100px; margin: 0 auto}
.viewbirbot h3{ font-size: 15px; color: #333333; line-height: 25px;}
.viewbirbot p{ background:url(../images/viewbir_bg.jpg);  width: 70%;   background-repeat: repeat; margin: 0 auto; text-align: left; line-height: 35px; font-size: 13px; text-indent: 2em;color: #999999;}
.viewbir-infor{width: 70%; margin: 20px auto 0 auto; text-align: right;font-size: 13px;color: #999999;
	display: -webkit-box;display: -webkit-flex;display: flex;-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;align-items: center;justify-content: flex-end; }
.viewbir-infor img{width: 40px; height: 40px; vertical-align: middle; margin: 0 5px 0 0 }

/*幼儿园*/
.kinder{ margin-top:10px}
.kinder .weui-cell__hd{ font-size: 15px; position: relative; width: 80px;}
.kinder .weui-cell__ft{ text-align:left; font-style: normal; font-size: 15px;}
.kinder .weui-cell__ft img{text-align: right; cursor: pointer; float: right;
    height: 27px;
    vertical-align: middle;
    width: 27px;}
.kinder .weui-cell { background:#fff;}
.kinder .weui-cell::before{border-top:1px solid #dfdfdf}
.kinder .weui-cells::before{ border-top:none}
.kinder .weui-cells::after{ border-bottom:none}
.kinder .weui-cell:first-child::before { padding-top: 5px;
    display: none;
}
.kinder .weui-cell__bd p{ color:#999999; font-size:13px;}
/*新版消息-家长聊天*/
.message-top{background: #00cd91;color: #ffffff;height: 45px;align-items: center;justify-content: space-between;padding: 0 15px;}

/*聊天输入面板*/
.send-btn a{border-radius: 30px;background: #21D2C8;color: #ffffff;font-size: 15px;height: 40px;display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;}

/*全民种草趴-我种值的园所*/
.park-main{ overflow:hidden}
.parkdiv{ margin:0 15px;}
.parklist{ position:relative; background:#ffffff; margin-top:25px; border-radius:5px; padding:5px 15px 10px 15px; }
.parklist img{ position:absolute; right:10px; top:-16px}
.parklist p{ font-family:"微软雅黑"; font-size:13px; color:#333333;line-height:25px}
.park-bor{border-bottom:1px solid #d2d2d2; padding:5px 0}
.parklist.park-selbg{ background:#4aacff}
.parklist.park-selbg p{color:#fff;}
.park-selbg .park-bor{ border-bottom:1px solid #fff}
.parklist p b{ font-weight:bold; color:#666666 }
.parklist.park-selbg p b{ font-weight:bold; color:#fff }
/*全民种草趴-种草趴活动规则*/
.rule-tab{display: flex;display: -webkit-flex;align-items:center;height: 55px; background:#fff}
.rule-tab li{display: flex;display: -webkit-flex;-webkit-box-flex:1;-webkit-flex:1;flex: 1;align-items: center;justify-content:center;font-size: 15px;}
.rule-tab li a{height:55px;display: flex;display: -webkit-flex;align-items: center;justify-content:center;color: #999999;}
.rule-tab li a.curr-rule{color: #ff651a;border-bottom: 2px solid #ff651a; width:100px}
.plant-party{ background:#fec028;padding-bottom: 10px;}
.child-money { margin:0 auto;}
.plant-party h3,.child-money h3{ color:#333333; font-size:13px;font-weight: bold;}
.plant-party  label{ color:#333333}
.child-money p{ color:#999999; font-size:13px; line-height:22px; text-indent:2em;}
.plant-party .rule-top{background: url(../images/recom_rule.jpg)no-repeat;height: 351px;background-size:100% 351px;position: relative;}
.plant-party .rule-top-con{position: absolute;bottom: 64px;color: #000000;font-size: 12px;margin: 0 40px;line-height: 18px;}
.plant-party .rule-top-con h5{color: #5c2000;font-size: 13px;font-weight: bold;text-align: center;margin-bottom: 23px;}
.plant-party .rule-step{background: #ffffff;border: 1px solid #3c0202;position: relative;margin: 45px 15px;padding: 20px 14px;font-size: 13px;color: #000000;z-index: 1;}
.plant-party .rule-step h5{font-size: 13px;color: #000000;}
.plant-party .step-bg{background: #bc92ff;height: 100%;position: absolute;top: 2px;bottom: 0;left: 11px;right: 18px;border: 1px solid #000;z-index: 0;}
@media only screen and (max-width:359px) {
	.plant-party .rule-top-con{font-size: 11px;margin: 0 30px;}
}
.rule-txt{position: relative;}
/*全民种草趴-童帮币*/
.money_bg{height: 115px;background: url(../images/money_bg.jpg)no-repeat;background-size: 100% 115px;}
.money_bg p{ font-size:12px; color:#ffffff; padding-left:20px;}
.money_bg p i{ color:#fcff00; font-size:12px}
.money_bg p label{ color:#fcff00; font-size:48px;}
.expen-detailed{ margin-top: 10px;justify-content: center; align-items: center}
.expen-right span{ width:40px; height: 40px; position: relative;background:#fff; border: 1px solid #c2e89a; border-radius: 50%; text-align: center; display:inline-block; z-index:999; }
.expen-right{position:absolute;right:60px;}
.expen-right span img{margin-top:6px; }
.expen-right label{position:absolute; left:36px; top:8px; background:#00da6c; color: #fff; width:35px; border-bottom-right-radius:20px; border-top-right-radius:20px; padding-left: 10px; font-size: 12px;line-height: 25px}
.child-money-top{height: 271px;background: url(../images/currency_topbg.jpg)no-repeat;background-size: 100% 271px;}
.child-money-top h3{ padding-top:125px; line-height:30px; color:#ef5701; font-size:18px; text-align:center}
.child-money-top p{ font-size:12px; color:#ef5701;  text-align:center; width:167px; margin:0 auto; line-height:20px}
.child-money-cen{ overflow:hidden;background: #521778;}
.child-money-cen img{ width:100%; margin:0 auto;}
.child-money-cenbot{margin:0px 20px; overflow:hidden;position:relative; top:-15px;}
.child-money-cenbg{background: url(../images/currency_bg.png) repeat-y; width:100%;height:auto; background-size:100% auto; padding:20px 0;}
.child-money-cenbg p{ color:#C9B8DA;font-size:13px; text-indent:2em; padding:0 20px;word-break:break-all; }
.child-money-cenbg h3{ color:#fff; font-size:13px;font-weight: bold; padding:0 20px}
.child-money-cenbg h2{ color:#3389ff; font-size:13px;font-weight: bold; padding:0 20px;}
.child-money-cenbg .examplebg{ background:#873eb6; padding:10px;margin:10px 20px; text-align:0; border-radius:5px}
.child-money-cen .botimg{position: relative;bottom: 15px;}
@media screen and (min-width:450px) and (max-width:480px) {
	.child-money-cenbot{margin:0px 30px; top:-17px;}
	.child-money-cen .botimg{bottom: 17px;}
}
@media screen and (min-width:480px) and (max-width:550px) {
	.child-money-cenbot{margin:0px 30px; top:-20px;}
	.child-money-cen .botimg{bottom: 20px;}
}
@media screen and (min-width:550px) and (max-width:650px) {
	.child-money-cenbot{margin:0px 40px; top:-20px;}
	.child-money-cen .botimg{bottom: 20px;}
}
@media screen and (min-width:650px) and (max-width:768px) {
	.child-money-cenbot{margin:0px 45px; top:-25px;}
	.child-money-cen .botimg{bottom: 25px;}
}
@media screen and (min-width:768px) and (max-width:1900px) {
	.child-money-cenbot{margin:0px 60px; top:-30px;}
	.child-money-cen .botimg{bottom: 30px;}
}
/*全民种草趴-推荐园所*/
.recomcion-sum{position:relative;margin-top: 4px;height: 164px;background: url(../images/recomcoin_bg.jpg)no-repeat;background-size: 100% 164px;}
.coin-sum{align-items: center;justify-content: center;flex-direction: column;width: 126px;height: 126px;background: url(../images/coin_bg.png)no-repeat;background-size: 126px 126px;margin-right: 13%;}
.coin-btn{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;width: 92px;height: 30px;background: red;margin: 8px 0 8px 13%;font-size: 12px;color: #ffffff;border-radius: 5px;}
.coin-btn .icon_gift:before,.coin-btn .icon_coin:before{font-size: 16px;margin-right: 5px;display: -webkit-box;display: -webkit-flex;display: flex;}
.coin-btn.gradyellow{
	  background: -webkit-linear-gradient(#ffec4c, #ffc436);
	  background: -o-linear-gradient(#ffec4c, #ffc436);
	  background: -moz-linear-gradient(#ffec4c, #ffc436);
	  background: linear-gradient(#ffec4c, #ffc436); 
	  box-shadow: 0 1px 5px #ffe5a4;
	 }
.coin-btn.gradorange{
	  background: -webkit-linear-gradient(#ffa44d, #ff6f36);
	  background: -o-linear-gradient(#ffa44d, #ff6f36);
	  background: -moz-linear-gradient(#ffa44d, #ff6f36);
	  background: linear-gradient(#ffa44d, #ff6f36); 
	  box-shadow: 0 1px 5px #ffc6b0;
	 }
.recom-sign{align-items: center;justify-content: center;position: absolute;top: 0;right: 0;height: 27px;color: #ffffff;font-size: 11px;background: #5892f0;padding: 0 8px;}
.recom-sign img{position: absolute;left: -10.5px;}
.recom-list{border-bottom: 1px solid #D2D2D2;margin: 0 15px;padding: 5px 0;}
.recom-list .weui-cell{padding: 10px 0;}
.recom-list:last-child{border-bottom: none;}
.coin-cir{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;flex-direction: column;line-height: 18px;width: 64px;height: 64px;border-radius: 50%;}
.cir-mark{width: 8px;height: 8px;border-radius: 50%;display: inline-block;}
.pack-txt{width: 16px;height: 22px;display: inline-block;position: relative;margin-right: 3px;}
.pack-txt label{background: #ff6afd;width: 10px;height: 8px;border-radius: 1px;font-size: 6px;display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;color: #ffffff;position: absolute;right: -3px;top: 2px;}
@media only screen and (max-width: 340px) {
	.recom-list .weui-cell__hd{padding: 0 10px 0 10px!important;margin-right: 10px!important;}
}
.gift-list{background: #ffffff;}
.gift-list .weui-cell:before{content: none;}
.gift-list .weui-cell{border-bottom: 1px solid #e5e5e5;margin: 0 15px;padding: 10px 0;background: #ffffff;}
.gift-list .weui-cell:last-child{border-bottom: none;}
.change-btn{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;background: #2b99ff;font-size: 14px;color: #ffffff;height: 30px;padding: 1px 10px 0 10px;min-width: 44px;border-radius: 5px;}
/*全民种草趴-确认订单*/
.one-info{width: 100%;height: 38px;align-items: center;justify-content: space-between;}
.goods-info{color: #333333;font-size: 13px;padding: 7px 0;}	        
.goods-info li{margin: 5px 0;}
.opr-bottom{position: fixed;bottom: 0;height: 70px;background: #ffffff;width: 100%;align-items: center;justify-content: space-between;}
.opr-bottom p{display: -webkit-box;display: -webkit-flex;display: flex;margin-top: 10px;justify-content: space-between;width: 100%;margin: 0 15px;}
.logis-con{background: #ffffff;padding: 0 15px;
margin-top: 10px;}
.logis-result-cen{ margin: 20px 0px 0 20px; padding-bottom: 20px;}
.logis-div{display: block; clear: both}
.logis-stat{display: inline-block;position: relative;margin-left:15px; }
.logis-stat:last-child:after{content: none;}
.logis-stat p{ line-height: 20px;width: 200px;top: 0px; position: absolute; left: 20px; font-size: 13px}
.pro-circle{width: 21px;height: 21px;border-radius: 50%; position: absolute; left:-11px; z-index: 1000;bottom: 0px; display: block; top:0px; background: #fff}
.pro-circle label{width: 15px;height: 15px;display:block;border-radius: 50%;margin: 3px auto; text-align:center}
.logis-div .bluepro{color: #62afff !important;}
.logis-div .bluepro label{background: #62afff;}
.logis-div .bluepro .pro-circle{border: 1px solid #62afff; }
.logis-div .graypro .pro-circle{border: 1px solid #dbdbdb; }
.logis-div .graypro label{background: #dbdbdb;}
.logis-div .graypro p{color:#999999}
.logis-stat p i{ display:block}
.pro-line{vertical-align: top;margin: 0;height: 90px;width: 2px;display: inline-block;}
.pro-line.blueline{background: #9fceff;}
.pro-line.grayline{background: #dbdbdb;}
.pro-line.grayline.linenone{height: 0;}
.pro-line.blueline.linenone{height: 0;}


/*购买弹框*/
.opr-panel{background: #ffffff;position: absolute;bottom: 0;left: 0;right: 0;width: 100%;}
.opr-inp{justify-content: center;margin: 10px 0 15px 0;}
.opr-inp input[type="button"]{-webkit-appearance:none;outline:none;width: 25px;height: 25px;border-radius: 3px;background: #ececec;border: none;color: #cacaca;font-size: 20px;line-height: 25px;}
.opr-inp input[type="text"]{-webkit-appearance:none;outline:none;width: 56px;height: 25px;border-radius: 3px;background: #ececec;border: none;color: #999999;font-size: 13px;text-align: center;margin: 0 5px;}
.warn-txt .btn-warn:before{color: #ff7070;font-size: 16px;}
.warn-txt{font-size: 12px;color: #ff7070;align-items: center;margin: 5px 15px;}
.opr-panel .icon_shut{position: absolute;right: 18px;top: 5px;width: 30px;height: 30px;align-items: center;justify-content: center;}
.opr-panel .icon_shut:before{color: #333333;font-size: 18px;}
.shadow-con .btn-false.whitetxt:before{color: #ffffff;}
.goods-info.goods-inp{}
.goods-info.goods-inp li{display: -webkit-box;display: -webkit-flex;display: flex;margin: 12px 0;}
.goods-info.goods-inp li .fill-txt{height: 25px;display: -webkit-box;display: -webkit-flex;display: flex;border-bottom: 1px solid #E8E8E8;-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;justify-content: space-between;}
.goods-info.goods-inp li .fill-txt input{border: none!important;}
.goods-info.goods-inp li select{-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;border:1px solid #E8E8E8;height: 24px;}
/*童帮币攻略*/
.recom-txt{background: #ffae42;}
.recom-tit{align-items: center;justify-content: center;margin: 0 15px;padding: 5px 0 0 0;}        	        
.recom-tit span{align-items: center;justify-content: center;width: 100px;height: 32px;color: #ffffff;font-weight: bold;font-size: 13px;border-radius: 30px;background: #f5811e;margin: 0 12px;}	        
.recom-tit i{height: 1px;background: #f5811e;}
.recom-step{position: relative;background: #ffffff;margin: 45px 15px 8px 15px;border-radius: 3px;color: #ffae42;font-size: 13px;padding: 15px;}
.recom-step h6{font-size: 13px;font-weight: bold;margin-bottom: 5px;}
.recom-step p{text-indent: 26px;}	    	
.recom-tel{position: relative;margin-top: 5px;color: #333333;font-size: 15px;font-weight: bold;border-top: 1px solid #333333;border-right: 1px solid #333333;border-bottom: 1px solid #333333;border-radius: 3px;padding: 0 12px 0 25px;}
.recom-tel .icon_tel2{position: absolute;left: -10px;top:0;width: 26px;height: 26px;line-height: 26px;}
.recom-link .weui-cell .weui-cell__hd{width: 42%;}
.recom-link a{color: #333333;}
.recom-step .mark-step{position: absolute;left: 50%;margin-left: -30px;top: -26px;width: 61px;height: 33px;background: url(../images/recom_stepbg.png)no-repeat;background-size: 61px 33px;display: inline-block;color: #000000;font-size: 12px;font-weight: bold;text-align: center;line-height: 22px;}
@media only screen and (max-width: 340px) {
	.recom-link .weui-cell .weui-cell__hd{
		width: 45%;
	}
}
@media only screen and (min-width: 411px) {
	.recom-link .weui-cell .weui-cell__hd{
		width: 40%;
	}
}
/*我的礼品*/
.change-gift{margin-bottom: 10px;}        
.change-gift .weui-cell:before{content: none;}
/*一日食谱*/
.total-table{background:#fff;color: #333;border-top:1px solid #ddd;}
.total-table th{font-size:14px;text-align: center;height: 38px;color: #12b7f5;font-style: normal;}
.total-table td{font-size:12px;height:33px;border-top: 1px solid #e8e8e8;border-right: 1px solid #e8e8e8;}
.total-table tr.first-tb td{text-align: center;padding: 0;}
.total-table td:nth-child(2n+1){width: 62%;padding-left: 40px;}
.total-table td:nth-child(2n){border-right: none;text-align: center;}
.total-table td img{width: 16px;height: 16px;vertical-align: top;margin-top:-1px;margin-right: 10px;}
.bt-top{background:#f3f3f7;color:#333333;height: 38px;text-align: left;padding-left: 15px;line-height: 38px;font-size: 15px;}
.bt-top img{width: 19px;height: 14px;margin-right: 5px;vertical-align: top;margin-top:12px}
.total-table .progressbar_1{
	background-color:#f0f0f0;
	height:12px;
	width:85%;
	color:#222;
	border-radius:10px;
	margin:auto;

}
.total-table .progressbar_1 .bar {
	background-color:#f5906b;
	height:12px;
	width:0;
	border-radius:10px;
	position: relative;
	padding: 0;
}
.total-table .progressbar_1 .val-num{position: absolute;right: 2px;font-size: 9px;color:#fff ;height: 12px;line-height: 14px;display: inline-block;min-width: 10px;}
.bt-list{background: #fff;border-top:1px solid #ddd;}
.bt-list img{display: block;margin:0 auto;margin-bottom: 7px;}
.bt-list td{text-align: center;font-size: 11px;}
.bt-list td{height: 33px;border-top: 1px dashed #d5eab7;border-right: 1px dashed #d5eab7;}
.bt-list tbody{background: #97ca4c;}
.bt-list th{height: 56px;font-size: 12px;font-style: normal;}
.bt-list th img{width: 18px;height: 18px;}
.bt-list .tr1 td{border-top: none;}
.bt-list tr span{font-weight:bold;display:inline-block;width: 19px;height: 17px;line-height:20px;background: #cbe5a6;text-align: center;}
.bt-list tr td:last-child{border-right: none;}
.re_table .type,.zre_table .type{padding-left: 13px;margin-right: 10px;}
.re_table .type, .zre_table .type{padding-right: 9px;}
li.lifood:before,li.lifoodred:before,li.lifoodpink:before,li.lifoodgree:before,li.lifoodred:before{border-radius: 50%;}
.re_table li span, .zre_table li span{color: #999;}
.recipe-list em{color: #333333;}
.recipe-list{margin: 5px 0;}

/*查看报名名单*/
.enroll_top{height: 90px;background: url(../images/name_list.jpg)no-repeat;background-size: 100% 110px; padding-top: 20px;}
.enroll_top p{color:#fff; font-size: 13px; margin: 0 auto; width:250px; }
.enroll_top span{display: block; margin: 10px auto; width:140px; height:30px;  text-align: center;  font-size: 13px; line-height: 30px;border-radius: 20px; cursor: pointer}
.enroll_top span.yellowbg{background:#fcff00; color: #000000 }
.enroll_top span.whitebg{background:#ffffff; color: #2f8be9 }
.enroll_top span.graybg{background:#ffffff; color: #ff4138}
.enroll_top span.redbg{background:#ffffff; color: #ff4138}
.enroll-cen{background: #fff; border-radius: 4px; margin: 10px 20px; padding-bottom: 3px}
.enroll-list{background:#fff; border-radius: 5px; margin: 10px 15px 0px 15px}
.enroll-list .weui-cell:last-child{border-bottom: none;}
.enroll-list .weui-cell:before{ border-top:none}
.enroll-list p{color: #333333;font-size: 15px}
.enroll-list p.enroll-bot{color: #999999;font-size: 11px}
.enroll-list p.enroll-bluebot{color: #00d1c9;font-size: 12px}
.enroll-navtit{color:#333333;font-size: 15px;  text-align: center;margin-top: 10px;}
.enroll-list .weui-cell{padding: 5px 0px;border-bottom: 1px dashed #dddddd;}
.enr-line-tit{text-align: center; width: 170px; margin:0px auto;display: -webkit-box;display: -webkit-flex;display: flex; align-items: center; padding: 15px 0;}
.enr-line{vertical-align: top;height: 2px;width: 70px;display: inline-block;}
.enr-line.grayline{background: #999999;}
.enr-peo{margin:0 20px;color: #999999;font-size: 13px; width:100px}
.enr-tab{display: flex;display: -webkit-flex;align-items:center;height: 30px; margin: 10px 0 5px 0;}
.enr-tab li{display: flex;display: -webkit-flex;-webkit-box-flex:1;-webkit-flex:1;flex: 1;align-items: center;justify-content:center;font-size: 15px;}
.enr-tab li a{height:30px;display: flex;display: -webkit-flex;align-items: center;justify-content:center;color: #999999;}
.enr-tab li a.curr-rule{color: #00d1c9;border-bottom: 2px solid #00d1c9; width:66px}
/*查看通知详情*/
.enroll-time{background: #fff; overflow: hidden; padding: 0 10px 10px 10px;margin: 0 10px;border-radius: 4px;}
.enroll-detail{padding: 12px 0;justify-content: center; align-items: center;border-bottom: 1px solid #dddddd;}
.enroll-right span{width:80px; height: 30px; text-align: center; line-height: 30px;font-size: 13px; display: block; border-radius: 25px}
.enroll-right span.bluebg{background:#86bcff; color: #fff}
.enroll-time .enr-weui-grids{display: -webkit-box;display: -webkit-flex;display: flex; align-items: center;justify-content: center; width: 300px; margin: 12px auto 17px auto;}
.enr-weui-grids img{width: 35px; height: 35px; margin: 0 4px;border-radius: 3px;}
.enroll-time P{text-align: center;font-size: 13px;color: #999999; }
.enroll-time P a{text-decoration: underline; color:#0054ff; margin-left:5px}
/*查看通知详情评论*/
.write_comment{position: absolute;bottom: 15px; width: 100%; text-align: center }
.write_comment input{background: #f4f4f4;border: none;border-radius: 30px; line-height: 34px;width:90%; margin: 0 auto; padding-left: 15px}
/*查看点赞*/
.help-weui-grids{margin: 10px -5px; width: 100%;}
.help-weui-grids img{width: 40px; height: 40px; border-radius: 3px;}
.help-weui-grids label{float: left; text-align: center; display: block;margin: 2px 4px;}
.help_bot{position: absolute;bottom:20px; width: 100%; text-align: center}
.help_botbg{text-align: center; width:82px; height:82px; margin: 0 auto; border-radius: 50%; font-size: 12px; padding-top: 25px; }
.help_botbg.gray_bg{ background:#f7f7f7; color:#cdcdcd } 
.help_botbg.violet_bg{background:#FFE9E1; color:#FF8258;} 
.help_botbg span{display: block;margin-top: 5px;}
.help_botbg.violet_bg span{color:#2F3237;}
.send_btn{ background: #00d1c9; border-radius: 3px; line-height: 25px; text-align: center; color: #fff; font-size: 12px;  width: 50px; float: right;}
/*新建外部链接*/
.exter-btn{width:100%; position: absolute; bottom: 30px}
.exter-btn a{border-radius: 30px;color: #ffffff;font-size: 15px;height: 40px;display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;}
.dic-main-div{background:#ffffff; box-shadow: 1px 1px 1px 1px #f3f3f3;  width: 200px; margin: 0 auto; position: relative; background: #fff; border-radius: 25px; }
.arrow-up {border-bottom: 10px solid #f5f5f5;border-left: 10px solid transparent; border-right: 10px solid transparent;height: 0;overflow: hidden; width: 0;position: absolute;top: -10px; left: 91px;}
.arrow-up1 { border-bottom: 8px solid #fff;border-left: 8px solid transparent;border-right: 8px solid transparent; height: 0; left: 93px; overflow: hidden; position: absolute;top: -8px;width: 0;}
.dic-main-div .weui-grid{box-sizing: border-box; padding:10px 0px 5px 0px;width: 33.33%;}
.dic-main-div .weui-grid__icon, .check_word .weui-grid__icon { height:20px; width: 45px; }
.dic-main-div .weui-grid__label, .check_word .weui-grid__label { color: #999999;font-size: 12px;text-align: center; width: 100%;}
.dic-main-div .weui-grid::before{ border-right: none;}
.dic-main-div .weui-grids::before{border-top:none}
.dic-main-div .weui-grid::after{border-bottom: none;}
.dic-main-div .weui-grids:after {border-left: none;}
.dic-main-div .weui-grid__icon+.weui-grid__label{margin-top: 0}
/*主题*/
.theme_bg{ overflow: hidden}
.theme_bg img{width: 100%}
.theme_bg_list{align-items: center;justify-content: center;background: #ffffff;border: 1px solid #00d1c9;border-radius: 50%;width: 40px;height: 40px; color:#00d1c9; font-size: 12px;}
.theme-com{position: fixed;top: 0;bottom: 0;left: 0;right: 0;z-index: 9;display:-webkit-box;display:-webkit-flex;display:flex;}
.theme-combox{position:absolute;background: #f7f7f7;font-size: 13px;color: #333333;padding:0 0px 20px 0px;width: 100%;bottom:0px;}
.theme-combox .weui-grids{ width: 100% }
.theme-com .weui-grid{box-sizing: border-box; padding:10px 5px 0 5px;width: 20%;}
.theme-com .weui-grids:before{border-top: none}
.theme-com .weui-grid__icon{ height: 110px; width:98%;}
.theme-com .weui-grid__label { color: #333333;font-size: 13px; align-items: center}
.theme-com .weui-grid::before{ border-right: none;}
.theme-com .weui-grids::before{border-top:none}
.theme-com .weui-grid::after{border-bottom: none}
.theme-com .weui-grid__label span{ height: 27px; display: block; width: 45px; text-align: center; margin: 0 auto;}
.theme-com .weui-grid:active{background-color:transparent}
.theme-com .weui-grid__icon.current{border:2px solid #00cc90;}

/*通知类型*/
.not_type{background: #fff; padding:15px 10px; margin-bottom: 10px; overflow:hidden}
.not_type p{ width: 100%; border-bottom: 1px solid #e2e2e2; line-height: 30px; color:#333333; font-size: 13px; font-weight: bold}
.not-ctypediv{ margin:10px 0px 0px 0px; clear: both; overflow: hidden;}
.not-ctype{display: inline-block; width:95%; padding:5px 8px; border-radius:18px;  text-align: center;font-size:13px; color: #666666; margin:0 auto 15px auto; cursor: pointer; border: 1px solid #dcdcdc;}
.not-ctype a{color: #666666;}
.not-ctypediv .blueclo{border: 1px solid #00d1c9; color: #fff;background:#00d1c9;}
.greclo{border: 1px solid #53CAC4; color: #53CAC4; }
.not-ctypediv .not-message {position: relative; width:25%; float: left;}

/*通知公告*/	        
.notify-tit{height: 50px;background: #00cd91;color: #ffffff;font-size: 12px;align-items: center;justify-content: center;padding: 0 15px;position: relative;}
.notify-tit .commontab li{min-width: 68px;}
@media only screen and (max-width: 359px) {
	.notify-tit{padding: 0 10px;}
	.notify-tit .commontab li{min-width: inherit;padding: 4px 8px 3px 8px;}
}
.message-num{margin: 8px auto;background: #585858;color: #ffffff;font-size: 12px;align-items: center;padding: 5px;border-radius: 2px;}
.message-num .icon-arrow-right:before{font-size: 15px;margin-left: 5px;}	
.onenotice{background: #ffffff;margin: 0 10px 10px 10px;border-radius: 5px;padding-top: 5px;position: relative;}
.onenotice .weui-cell:before{border-top: none;}
.onenotice .notify-txt{margin: 0 12px;}
.onenotice .notify-txt h5{color: #333333;font-size: 15px;font-weight: normal;padding-bottom: 3px;}
.onenotice .notify-txt p{color: #999999;font-size: 12px;line-height: 16px;padding-bottom: 10px;}
.onenotice .notify-txt a{color: #00a7fe;font-size: 12px;line-height: 16px;padding-bottom: 10px;text-decoration: underline;display: inline-block;word-break: break-all;}
.onenotice .notify-opr{height: 40px;background: #ffffff;align-items: center;justify-content: space-between;color: #999999;font-size: 12px;margin: 7px 12px 0 12px;border-top: 1px solid #e7e7e7;}
.onenotice .stick-mark{background: #ff4e4e;color: #ffffff;font-size: 11px;padding: 5px 8px;border-radius: 0 0 6px 6px;position: absolute;top: 0;right: 42px;}
.onenotice .arr-down-icon{position: absolute;top: 2px;right: 10px;}
.onenotice .arr-down-icon:before{color: #d9d9d9;font-size: 16px;}
.onenotice .border-btn{margin-left: 7px;align-items: center;font-size: 11px;padding: 3px 10px 2px 10px;}
.onenotice .border-btn .iconfont{height: 12px;display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;}
.enroll-txt{background: #eeeeee;color: #333333;font-size: 12px;padding: 3px 10px;border-radius: 20px;}
.enroll-mark{background: #00cd91;position: absolute;top: 32px;right: 0;font-size: 11px;color: #ffffff;height: 17px;padding: 0 5px;z-index: 9;}
/*通知预览*/
.job-opr{position:fixed;width:100%;bottom:0;left: 0;justify-content: space-between;height: 49px;background: #ffffff;font-size: 12px;color: #666666;box-shadow:10px 10px 10px 10px #e8e8e8;}
.job-icon{flex-direction: column;width: 15%;}
.job-icon span{margin-top: 2px;}
.music-panel{align-items: center;justify-content: space-between;background: #f5f5f5;height: 44px;border-radius: 3px;margin-bottom: 10px;}
.music-panel .iconfont{height: 28px;margin: 0 12px;}
.preview-con{margin-bottom: 65px;}
.preview-con.preview-bg1{padding: 30px 15px;background: url(../images/jobdetail_bg.png);background-size: 100%;}
.tab-bg{position: absolute;width: 100%;position: absolute;bottom: 0;z-index: 99;overflow-x: auto;padding:  0 7px;font-size: 14px;color: #333333;text-align: center;}
.tab-bg p{background: #ffffff;margin: 0 -15px;height: 47px;line-height: 47px;}
.tab-bg .noticebg-cell.current img,.tab-bg .noticebg-cell.current .def-bg{border: 2px solid #00cd91;border-radius: 2px;}
.tab-bg .noticebg-cell.current p{color: #00cd91;position: relative;}
.tab-bg .noticebg-cell.current p:after{content: "";height: 2px;background: #00cd91;width: 50%;position: absolute;left: 25%;bottom: 0;}
.sel-noticebg{background: #f9f9f9;border-radius: 15px 15px 0 0;position: fixed;bottom: 0;width: 100%;box-shadow: 0 10px 10px 10px #e8e8e8;}
.sel-noticebg h5{align-items: center;justify-content: space-between;border-bottom: 1px solid #e3e3e3;border-radius: 15px 15px 0 0;padding: 0 15px;height: 48px;background: #ffffff;}
.noticebg-cell{width: 67px;margin: 0 8px;}
.noticebg-cell img,.noticebg-cell .def-bg{width: 67px;height: 102px;background: #ffffff;display: block;margin: 8px 0;}
.notice-oprbg{height: 48px;border-top: 1px solid #dddddd;position: absolute;bottom: 0;width: 100%;background: #ffffff;z-index: 9;}
/*消息中心*/
.notice-con{margin-top: 7px;background: #ffffff;}
.notice-cell{padding-bottom: 10px;border-bottom: 1px solid #dddddd;}
.notice-con .notice-cell:last-child{border-bottom: none;}
.notice-title .weui-cell__hd img{width: 41px;margin-right: 16px;display: block;border-radius: 3px;}
.notice-title .weui-cell__bd{font-size: 15px;}
.notice-title .weui-cell__bd span{font-size: 14px;color: #666666;margin-left: 5px;}
.notice-title .weui-cell__ft{font-size: 12px;color: #999999;}
.notice-panel{background: #f9f9f9;margin: -5px 15px 0 70px;}
.notice-panel h5{font-weight: normal;margin: 0 10px;padding: 7px 0;border-bottom: 1px dashed #dddddd;}
.notice-panel .weui-cell{padding: 10px;}
.notice-panel .weui-cell__hd{position: relative;margin-right: 10px;}
.notice-panel .weui-cell__hd img{width: 65px;height: 65px;display: block}
/*选择通知类型弹框*/
.noticesel-con{flex-wrap: wrap;justify-content: center;font-size: 16px;color: #ffffff;margin-top: 50px;}
.noticecell{align-items: center;width: 110px;height: 72px;margin: 10px 7px;position: relative;border-radius: 4px;}
.noticecell label{height: 25px;position: absolute;bottom: -12px;left: 50%;margin-left: -12px;}
.noticecell p{margin-left: 10px;}
/*投票*/
.vote-content{position: absolute;top: 0;bottom: 57px;width: 100%;overflow: scroll;}   
.vote-list{background: #ffffff;border-radius: 6px;padding: 12px 12px 0 12px;margin-bottom: 10px;}
.vote-list h4{font-size: 14px;color: #333333;font-weight: normal;border-bottom: 1px dashed #dddddd;padding-bottom: 10px;margin-bottom: 10px;}
.greenmark-cir{width: 6px;height: 6px;display: inline-block;border-radius: 50%;background: #00cd91;vertical-align: middle;margin-right: 5px;}
ul.vote-select{color: #333333;font-size: 14px;padding: 5px 0;}
ul.vote-select li>div{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: space-between;}
ul.vote-select li{display: -webkit-box;display: -webkit-flex;display: flex;align-items: inherit;justify-content: center;flex-direction: column;padding: 12px 0;border-bottom: 1px solid #dcdcdc;}
ul.vote-select li:last-child{border-bottom: none;}
ul.vote-select li input[type="radio"],ul.vote-select li input[type="checkbox"]{margin-right: 5px;}
ul.vote-select li input[type="text"]{background: #E3E3E3;color: #999999;font-size: 13px;outline: none;border: none;height: 30px;margin: 5px 0;border-radius: 20px;padding: 0 12px;}
.vote-select.vote-per li{padding: 6px 0 6px 12px;border-bottom: none;}
.vote-panel{background: #ffffff;position: fixed;bottom: 0;width: 100%;align-items: center;justify-content: space-between;padding: 0 15px;height: 57px;border-top: 1px solid #e1e1e1;}
.vote-panel .icon_comment3,.vote-panel .icon_thumb2,.vote-panel .icon_set2,.vote-panel .templet{position: relative;align-items: center;justify-content: center;background: #ffffff;border: 1px solid #00d1c9;border-radius: 50%;width: 40px;height: 40px;}
.vote-panel .templet{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;color: #00d1c9;font-size: 12px;}
.vote-panel a{color: #ffffff;font-size: 15px;padding: 7px 25px;border-radius: 30px;}
@media only screen and (max-width: 330px) {
	.vote-panel a{padding: 7px 20px;}
}
/*进度条样式*/ 
.vote-list .progressbar_1{ 
	-webkit-box-flex:1;
	-webkit-flex:1;
	flex: 1;
    background-color:#dcdcdc; 
    height:5px;
    width:150px; 			
    color:#222;
	border-radius: 15px;
} 
.vote-list .progressbar_1 .bar { 
    background-color:#86bcff;
    height:5px;
    width:0;
	border-radius: 15px;
} 
.vote-detial{margin-bottom: 10px;}
/*投票活动*/
.cover-pic{height: 150px;background: #00cd91;position: relative;}
.cover-pic h5{position: absolute;top: 15px;left: 15px;color: #ffffff;font-size: 17px;font-weight: normal;text-decoration: underline;}
.cover-pic .change-cover{position: absolute;right: 0;bottom: 20px;align-items: center;justify-content: center;background: #006648;color: #ffffff;font-size: 12px;width: 90px;padding: 3px 0;border-radius: 20px 0 0 20px;}
.theme-add{position: relative;justify-content: center;padding: 15px 0;}
.theme-add span{align-items: center;justify-content: center;background: #ffffff;color: #00cd91;border: 1px solid #00cd91;font-size: 13px;padding: 1px 13px 0 13px;border-radius: 7px;}
.theme-add .icon_add_icon:before{color: #00cd91;}
.theme-add:before{content: " ";position: absolute;left: 15px;top: 0;right: 15px;height: 1px;color: #e5e5e5;-webkit-transform-origin: 0 0;transform-origin: 0 0;-webkit-transform: scaleY(.5);transform: scaleY(.5);z-index: 2;border-top: 1px solid #e5e5e5;}
.theme-add.def-content:before{content:none;}
.img-option{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;margin-top: 10px;}
.img-option .icon_delete{width: 30px;height: 30px;border: 1px solid #ff7171;border-radius: 50%;display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;margin-left: 10px;}
.img-option .icon_delete:before{color: #ff7171;font-size: 22px;}
.inp-option .icon_pic2{background: #00cd91;}
.inp-option .icon_pic2:before{font-size: 18px;margin-top: 2px;}
.inp-option .icon_minus{background: #ff8b8b;}
.inp-option .icon_minus:before{font-size: 16px;margin-top: 2px;}			
.inp-option .icon_pic2,.inp-option .icon_minus{align-items: center;justify-content: center;width: 30px;height: 30px;border-radius: 50%;margin-left: 5px;}
.inp-option input[type="text"]{background: #f7f7f7;border: none;height: 37px;padding: 0 15px;border-radius: 20px;width: 70%;margin-right: 5px;outline: none;}
/*新版通知-搜索*/
/*.unread{ display: inline-block; background:#f5f5f5; border-radius: 20px;padding: 0 10px; color:#666666; font-size: 12px;  margin-left: 20px; }*/
/*新版通知-新建图文消息（添加项目）*/
.notice-opr{align-items: center;justify-content: space-between;margin: 0 10px;height: 43px;border-top: 1px solid #e7e7e7;}
.notice-opr-arr{height: 100%;align-items: center;}
.notice-opr-arr .iconfont{height: 100%;display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;}
.notice-opr-arr .iconfont+.iconfont{margin-left: 5px;}
.notice-add{height: 24px;line-height: 24px;text-align: center;margin-top: 7px;}
.notice-add .icon_add_icon:before{color: #e2e2e2;height: 24px;line-height: 24px;}
/*新版通知-编辑文字*/
.notice-text{margin: 10px 15px;font-size: 15px;}
.notice-text p{text-indent: 30px;color: #333333;}
/*选择群发人群*/
.person-sel{align-items: center;justify-content: space-between;padding: 0 15px;height: 60px;background: #ffffff;font-size: 13px;}
ul.person-sel{margin-left: 45px;padding: 0;flex-direction: column;height: auto;}
ul.person-sel li{display: -webkit-box;display: -webkit-flex;display: flex;flex-direction: column;align-items: center;justify-content: space-between;width: 100%;}
.person-sel-list{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: space-between;width: 100%;border-bottom: 1px solid #ededed;}
.person-sel-list a{height: 60px;border-bottom: 1px solid #ededed;padding-left: 0;padding-right: 15px;}
.person-sel-list a:last-child{border-bottom: none;}
.person-sel-list .icon-arrow-rgt,.person-sel-list .icon-arrow-btm{margin-right: 15px;}
.person-sel .icon-arrow-rgt,.person-sel .icon-arrow-btm{width: 20px;height: 24px;}
/*食品采购-订单详情*/
.picking-one{ background:url(../images/order-greenbg.jpg) no-repeat; width: 100%;background-size: 100% 120px;}
.picking-two{ background:url(../images/order-yellowbg.jpg) no-repeat; width: 100%;background-size: 100% 120px;}
.picking-three{ background:url(../images/order-redbg.jpg) no-repeat; width: 100%;background-size: 100% 120px;}
.picking-four{ background:url(../images/order-violetbg.jpg) no-repeat; width: 100%;background-size: 100% 120px;}
.picking-five{ background:url(../images/order-bluebg.jpg) no-repeat; width: 100%;background-size: 100% 120px;}
.picking-six{ background:url(../images/order-redbg.jpg) no-repeat; width: 100%;background-size: 100% 120px;}
.picking-seven{ background:url(../images/order-graybg.jpg) no-repeat; width: 100%;background-size: 100% 120px;}
.picking-tit{color: #ffffff;font-size: 15px; line-height: 30px; margin-top: 5px}
.picking-litit{color: #ffffff;font-size: 12px;}
.picking-litit img{vertical-align: middle; margin-right: 5px;}
.order-detail{ background: #fff; }
.icon_order_good,.icon_address{ background: #19bb72; width: 25px; height: 25px;justify-content: center;border-radius: 50%; margin-right: 5px;}
.order-good-tit{ padding-left: 30px; font-size: 12px; color: #999999;} 
.weui-flex-cen.green-clr{color:#19bb72 }
.icon_address{ background:#52a7ff }
.remarks-tit{ font-size:12px;color:#666666; line-height: 40px; padding: 0 15px}
.order-list{ margin-bottom: 10px; overflow: hidden;background: #fff;}
.order-detail .weui-cell{ padding:15px 15px 10px 15px;}
.order-tit{font-size: 15px;color: #333333;font-weight: bold;line-height: 17px;margin-bottom: 8px;}
.order-del{margin-top: 10px; overflow:hidden;}
.order-del span{cursor: pointer;background: #fff; margin: 15px; line-height: 30px; height: 30px; display: block; font-size:12px; color: #333333; border: 1px solid #efefef; border-radius: 20px; width: 100px; text-align: center; float: right;}
.collect-tit{  margin-left: 15px; line-height: 28px; height: 28px; display:inline-block; font-size:12px; border-radius: 20px; width: 80px; display: flex; justify-content: center; align-items: center;}
.collect-tit.greenbg{ border: 1px solid #19bb72; color: #19bb72; }
.collect-tit.greybg{ border: 1px solid #e3e3e3; color: #333333; }
.collect-tit.yellowbg{ border: 1px solid #fbc134; color: #fbc134; }
.collect-tit.redbg{ border: 1px solid #fe7a72; color: #fff; background-color: #fe7a72; padding: 0 10px;}
.order-list.grey-cor,.weui-cells.grey-cor{ background:#e5e5e5; margin-bottom:0; }
.order-list.grey-cor .bottom-bor{ border-bottom: 1px solid #d6d6d6;}
.grey-cor .bottom-bor{ border-bottom: 1px solid #d6d6d6;}
.collect-tit.greenbg-lt{ border: 1px solid #00cd91; color: #00cd91; background: #e6fbf5; }
.collect-tit.yellowbg-lt{ border: 1px solid #ff9c2c; color: #ff9c2c; background: #fff3e5; }
.replace-cen{position:relative;top:7px;}
.replace-cen p{ background: #fe7a72; line-height: 20px; padding: 0 10px; color: #fff; font-size: 10px; width:92px; border-radius: 3px; }
.down-arrow {width: 0;height: 0;border-width: 8px;border-style: solid;border-color: #fe7a72 transparent transparent transparent; position: absolute; top:20px; left:50px;} 
/*食品采购-采购订单入库*/
.footer-pur{ justify-content: space-between;height: 50px; background: #fff; position: absolute; bottom: 0; width: 100%; color:#999999; font-size: 12px; }
.footer-pur label{margin: 0 15px;}
.footer-pur span{ background:#19bb72;  text-align: center; color: #fff; display: inline-block; border-radius: 20px; font-size: 12px; padding: 5px 15px; margin-right: 15px;}
.sel-time{ line-height: 25px; background:#fafafa; color: #666666; text-align: center; border: 1px solid #eeeeee; border-radius: 3px;}
.footbtn-bot{ position: absolute; height: 50px; bottom: 0; width: 100%; z-index: 999; background: #fff;}
.ware-tit { background: #fff; padding:12px 15px ; border-bottom: 1px solid #eeeeee}
.footbtn-bot span,.ware-tit span{margin-right: 10px;  cursor: pointer;background:#19BB72; display: inline-block; width:70px;  border-radius: 20px; line-height:30px;  color: #fff; text-align: center; font-size: 12px;}
.green-tit{ color: #00be6a; font-size: 13px;}
/*手动入库*/
.food-type .search-school{margin-top: 0;border-top:1px solid #ebedef; padding-top: 0;}
.food-type ul{/*border-top: 1px solid #eeeeee;*/border-bottom: 1px solid #eeeeee;}
.food-type ul li{margin-left: 0;padding: 0 0.2rem;}
/*.food-type ul li:hover{background: #EDEDED;}*/
.food-type .area-con{position: static; margin-top: 10px;}
.food-type .sear-ul{position: absolute;width: 100%;background: #fff;z-index: 999;margin-top: 0.2rem;min-height: 3rem;}
.food-type .area-left li.sel-pro{position: relative;color: #333333;}
.food-type .area-left li.sel-pro img{margin-left: 0;}
.food-type .area-left li.sel-pro:before{content: "";position: absolute;left:0;border-left: 4px solid #19bb72;height: 0.37rem; top:0.24rem;}
.food-type .area-left,.food-type .area-right{height: 9.66rem;overflow-y: auto;overflow-x: hidden;}
.food-type .area-left img{width: 0.33rem;height: 0.33rem;vertical-align: top;margin-top: 0.27rem;margin-right: 0.1rem;}
.food-type .area-left li{padding-left: 0.2rem;font-size: 0.28rem;}
.food-type .area-right{border-bottom: none;}
.food-type .area-right li{border-bottom: 1px solid #eeeeee;padding-left: 0;font-size: 0.28rem;padding-right: 0;}
.food-type .area-right li label{display: inline-block;width: 100%;padding-left: 0.1rem;cursor: pointer;height: 0.87rem;}
.food-type .area-right li label:hover{background: #EDEDED;}
.radio-type input[type="checkbox"]{float:right;-webkit-appearance: none;vertical-align: top;margin-top:0.255rem;width: 0.36rem;height: 0.36rem;border: 1px solid #ccc;border-radius: 50%;margin-right: 0.3rem;}
.radio-type input[type='checkbox']:checked{background: url(../images/sel-green3.png) center center;background-size: 0.36rem 0.36rem;border: none;}
.food-type .bot-sel{background: #ffffff;height: 0.98rem;line-height: 0.98rem;}
.food-type .bot-sel label{margin-left: 0.2rem;font-size: 0.3rem;}
.food-type .bot-sel label span{color: #F95E28;}
.food-type .bot-sel a{margin-right: 15px; border-radius: 25px; height: 0.55rem;line-height: 0.55rem; display: inline-block;background: #19bb72;width:1.3rem;text-align: center;color: #fff;font-size: 0.28rem;}
.area-content{background: #fff;border-bottom: 1px solid #ddd;}
.area-top{font-size: 0;}
.area-top .areatop-div{color:#12b7f5;text-align:center;height: 0.8rem;line-height:0.8rem;vertical-align:top;width: 50%;font-size: 0.3rem;display: inline-block;}
.area-top .areatop-div:first-child img{width: 0.13rem;height: 0.09rem;vertical-align: top;margin-top:0.35rem;margin-left: 0.1rem;}
.area-top .areatop-div:last-child img{width: 0.36rem;height: 0.36rem;vertical-align: top;margin-top:0.22rem;}
.area-top .areatop-div span,.area-top .areatop-div img{cursor: pointer;}
.area-con {border-top:1px solid #eeeeee;text-align:left;font-size: 0;position: absolute;top: 0.8rem;bottom: 0;left:0;right:0;background: #fff;}
.area-con2{border-top:1px solid #eeeeee;}
.area-con ul{display: inline-block;width: 50%;font-size: 0.3rem;vertical-align: top;}
.area-con ul li{height: 0.87rem;line-height: 0.87rem;padding-left: 0.6rem;}
.area-con ul li.unpro{color: #999999;text-indent: 0.7rem;}
.area-con ul li.sel-pro{background: #fff;}
.area-con ul li.sel-pro img{width: 0.28rem;height: 0.28rem;vertical-align: top;margin-top:0.32rem;margin-left: 0.2rem;}
/*.area-con ul li.sel-pro span{color: #12b7f5;}
*/.area-con ul.area-left{background: #F7F9FB;}
.area-con ul.area-left li{/*border-bottom: 1px solid #dddddd;*/cursor: pointer;}
.area-con ul.area-left li:last-child{border-bottom: none;}
.markto1,.markto2{margin-right:0.2rem;border-radius:50%;font-size:0.14rem;width: 0.5rem;text-align:center;height: 0.5rem;line-height:0.5rem;display: inline-block;color: #fff;}
.markto1{background: #12b7f5;}
.markto2{background: #f8896c;}
.food-txt span{    text-overflow: ellipsis;overflow: hidden;white-space: nowrap;display: inline-block;width: 75%;}
.area-left span{    text-overflow: ellipsis;overflow: hidden;white-space: nowrap;display: inline-block;width: 75%;}
.area-right span{    text-overflow: ellipsis;overflow: hidden;white-space: nowrap;display: inline-block;width: 70%; padding-left: 5px;}
.make-sure,.make-sure2{cursor:pointer;margin-top:0.5rem;margin-left:4%;width: 92%;display: inline-block;text-align: center;color:#fff;line-height:0.75rem;height: 0.75rem;font-size: 0.36rem;}
.make-sure{background: #12b7f5;}
.make-sure2{background: #FE5E5E;}
.warehouse-type{ width: 120px; border: 1px solid #eeeeee; background:#ffffff; border-radius: 3px; height: 30px; }
@media screen and (min-width:767px) and (max-width:1920px) {
	.divclasslist .per-info .info-txt{ font-size:16px}
	.gray-txt{ font-size:14px;}
	.stat-mess{ font-size:14px;}
	.count-stat-list .exp-count-num{ font-size:14px;}
	.dinner-stat p{ font-size:14px;}
	.classchuqintab .oneblock a{ font-size:14px;}
	.red-border-txt{ font-size:14px;}
	.sel-person-list{ font-size: 16px;}
	ul.askle-list li .askle-num{ font-size: 16px;}
	.chart-show table tbody tr td{ font-size: 14px;}
	}
/*食品采购-全部订单*/
.purchase-list{background: #ffffff;margin: 7px 0;border-top: 1px solid #eeeeee;}
.purchase-list h5{align-items: center;justify-content: space-between;padding: 0 15px;height: 38px;background: #ffffff;}
.purchase-pic-con{background: #fafafa;}
.purchase-pic-con .weui-cell .weui-cell__bd img{width: 64px;height: 64px;border: 2px solid #ffffff;border-radius: 3px;margin-right: 4px;}
.purchase-list .border-btn{width: 61px; padding: 2px 5px 1px 5px;}
@media only screen and (max-width: 320px) {
	.search-con{margin: 5px;}
	.purchase-list .border-btn{width:61px;}
	.purchase-list .border-btn{padding: 2px 5px 1px 5px;}
	.search-box{padding: 0 5px;}
	.purchase-pic-con .weui-cell .weui-cell__bd img{width: 55px;height: 55px;}
}	
/*食品采购-收货操作*/
.things-list{background: #ffffff;font-size: 13px;border-top: 1px solid #eeeeee;border-bottom: 1px solid #eeeeee;margin-bottom: 10px;}
.things-list h5{align-items: center;padding: 0 15px;height: 35px;font-size: 12px;color: #666666;justify-content: space-between;}
.things-txt{border-top: 1px solid #eeeeee;text-align: center;color: #666666;}        
.things-list .btn-add:before{color: #19bb72;}
.things-list .btn-delete:before{color: #bbbbbb;}        
.things-list.things-add .things-txt{padding: 0 15px;height: 48px;}
.things-list.things-add input[type="text"]{width: 93%;height: 30px;background: #f6f6f6;border: none;text-align: center;}
.things-list.things-add input[type="text"].current{border: 1px solid #19bb72;background: #ffffff;}
/*食品采购-物流信息*/
.logistics-con{background: #ffffff;margin: 0 15px 0 15px;padding: 0 10px;height: 100%;}
.logistics-list{padding-left: 25px;position: relative;padding: 7px 0 14px 25px;min-height: 30px;}
.logistics-icon{background: #fff;width: 22px;height: 36px;position: absolute;left: -11px;top: 0;}
.logistics-icon .iconfont{color: #aaaaaa;width: 20px;height: 20px;border: 1px solid #aaaaaa;background: #ffffff;border-radius: 50%;}      
.logistics-list.current .iconfont{color: #ffffff;background: #19bb72;border: 1px solid #19bb72;}
.logistics-list h6{font-size: 14px;color: #333333;font-weight: bold;}
.logistics-list p{font-size: 12px;color: #999999;}
.logistics-list.current h6{color: #19bb72;}
.remind-txt{position: absolute;background: red;height: 34px;width: inherit;background: rgba(255,255,255,0.9);padding: 0 10px;border-radius: 5px;z-index: 9;top: 85px;}
/*食品采购-库存*/
.table-list{font-size: 12px;color: #333333;font-weight: bold;}
.table-list .list-cell input[type="text"]{width: 50px;font-size: 12px;border: none;border-bottom: 1px solid #dddddd;}
.table-list .list-cell:nth-child(2n+1){background: #f8f9fa;}
.table-list .list-cell:nth-child(2n+1) input[type="text"]{background: #f8f9fa;}
.table-list .list-cell{padding: 14px;align-items: center;}
.table-list .list-cell:first-child{color: #828f99;background: #f5f8fa;}
.table-list .list-cell .weui-flex__item{margin: 0 5px;}
.scale-num{background: #dfebed;color: #a4bdc1;margin: 0 20px;font-size: 15px;text-align: center;border-radius: 10px;}
.table-list.table-red{background: #fbf8f8;font-weight: normal;color: #ec5d45;}
.table-list.table-red .list-cell{padding: 5px 14px;}
.table-list.table-red .list-cell:nth-child(2n+1){background: transparent;}
.table-list.table-red .list-cell:first-child{border-bottom: 1px solid #f3e5e5;background: #fcf3f3;padding: 7px 14px;color: #666666;}
.mark-text{font-size: 10px;color: #ffffff;background: #fcb550;position: absolute;top: 0;left: 0;padding: 0 5px;border-radius: 0 0 5px 0;}
.total-ul{font-size: 12px;color: #828f99;}
.total-ul>div{padding: 0 8px;position: relative;}
.total-ul>div:after{content: "";border-right: 1px solid #bcc3c9;height: 8px;position: absolute;right: 0;top: 5px;}
.total-ul>div:last-child:after{content: none;}
.info-list{font-size: 12px;color: #666666;padding: 9px 25px;line-height: 24px;}
.info-list span{font-size: 13px;color: #333333;font-weight: bold;}
.sel-date{align-items: center;font-size: 12px;color: #b1b6c3;font-weight: bold;line-height: 20px;}
.border-rgt{position: relative;}
.border-rgt:after{content: "";border-right: 1px solid #eeeeee;height: 50%;position: absolute;right: 0;top: 25%;}
.border-rgt:last-child::after{content: none;}
.gray-state{font-size: 12px;color: #666666;background: #f4f4f4;padding: 2px 10px;border-radius: 3px;margin: 0 15px;}
.sign-label{width: 17px;height: 17px;color: #ffffff;border-radius: 4px 0 4px 4px;font-size: 11px;margin: 3px 7px 0 0;}
.kucun-sub{font-size:12px;color: #666666;padding: 10px 0 0 15px;}
.kucun-cell{padding: 0 15px 10px 0;border-bottom: 1px solid #eeeeee;}
.kucun-sub:last-child .kucun-cell{border-bottom: none;}
.title-sel{align-items: center;justify-content: space-between;padding: 10px 12px;font-size: 15px;color: #333333;font-weight: bold;}
.state-sel{border: 1px solid #e3e4e7;position: relative;margin: 0 5px 6px 5px;border-radius: 5px;padding: 9px 12px 6px 12px;min-height: 43px;}
.state-sel .mark-state{width: 19px;height: 19px;position: absolute;top: -1px;left: -1px;}
.state-sel.current{background: #e7f4ff;border: 1px solid #4492ff;}
.sign-state{background: #fa825c;font-size: 10px;color: #ffffff;position: absolute;right: 0;top: 0;padding: 0 5px;border-radius: 0 5px 0 5px;}
.circle-opr{width: 30px;height: 30px;border: 1px solid #eeeeee;border-radius: 50%;margin: 0 5px;}
.kucun-top{background: #ffffff;align-items: center;justify-content: space-between;padding: 8px 6px;border-bottom: 1px solid #eeeeee;}
/*调查问卷*/
.que-list{background: #fff; margin-bottom:10px;}
.que-name{ display: flex;align-items:center;min-width:250px; white-space:nowrap; text-overflow:ellipsis; -o-text-overflow:ellipsis; overflow:hidden; font-size: 16px; color:#333333;padding: 7px 10px;}
.que-list .weui-cells{ margin-top: 0; }
.que-name .iconfont{ font-size: 18px;}
.que-name span{border-radius: 2px; display:inline-block; text-align: center; justify-content: center; align-items: center; line-height: 22px; height: 22px; font-size: 12px; color: #fff;background:#53e89f; padding: 0 7px; margin-left: 5px;margin-right: 5px;}
.que-name span.redbg{background:#f88c64;}
.que-name span.greybg{background:#c9c9c9;}
.que-name span.bluebg{background:#64b7f2;}
.que-img{ width:40px; height: 40px; cursor: pointer; margin-right: 10px; border-radius: 50px}
.que-bot{padding:10px 15px; color:#666666; font-size: 13px; border-top: 1px solid #DDDDDD; line-height: 25px;}
.que-bot ul{width: 100%;overflow: hidden;}
.que-bot li{ line-height: 20px; text-align: center; position: relative;  width: 25%; float: left;}
.que-bot li::after{content: "";border-right: 1px solid #f0f0f0;height: 20px;position: absolute;right: 0;top:9px;}
.que-bot li:last-child:after,.que-bot li:nth-child(3):after{content: none;}
.analysis-btn{float: right;background:#3fb0ff; color:#fff; font-size: 12px; line-height: 25px; height: 25px; width: 74px; border-radius: 25px;display:block; margin-top: 7px; justify-content:center;}
/*调查问卷提交情况*/
.parent-cen{padding:10px 15px; color:#666666; font-size: 13px; border-top: 1px solid #DDDDDD; line-height: 25px; overflow: hidden;}
.parent-tit{display: block;}
.read-top{width: 100%; height:50px;}
.read-top span{ display: inline-block;background: #fac134;
width: 72px;text-align: center;float: left;color: #fff;border-top-right-radius: 30px;border-bottom-right-radius: 30px;margin:5px 2px 5px 0px;height: 40px;}
.read-top i{ display: block;font-size: 24px;line-height: 17px; margin-top:4px; }
.read-top label{ display: block; font-size:12px;  }
.read-cen{clear: both}
.read-cen .weui-cells{ margin: 0px 15px 10px 15px; border: 1px solid #DDDDDD;}
.read-cen .weui-cell:before{ left: 0;}
.read-cen .weui-cell__bd{font-size: 15px; color:#333333;}
.read-cen .icon_newye{ margin-right: 5px;}
.read-cen .icon_telye{ margin-left: 3px;}
.read-cen .weui-cell__bd span{ font-size: 12px; color: #999999;}
/*调查问卷-详情*/
.questionnaier-list{margin-top: 7px;padding-bottom: 7px;background: #ffffff;border-bottom: 1px solid #dddddd;}
.questionnaier-list h5{height: 45px;background: #f8f8f8;color: #333333;font-size: 16px;border-top: 1px solid #dddddd;border-bottom: 1px solid #dddddd;}
.opr-div{padding: 0 15px 7px 15px;}
.opr-div p{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;min-height: 33px;color: #666666;font-size: 15px;padding: 10px 0;position: relative;}
.opr-div .radio-list{align-items: center;justify-content: space-between;color: #999999;font-size: 14px;margin: 0 10px;}
.opr-div .radio-list .current,.opr-div .checkbox-list .current{color: #fac134;}
.opr-div .textarea-list{border: 1px solid #dddddd;width: 100%;height: 68px;text-align: center;}
.opr-div .textarea-list textarea{border: none;width: 95%;height: 54px;margin-top: 7px;}
.opr-div .checkbox-list label{display: block;margin: 7px 0;color: #999999;font-size: 14px;}
.opr-div .txt-list p{color: #666666;font-size: 12px;padding: 2px 0;}
.questionnaier-list.txt-panel{border-bottom: none;padding: 0;}
.questionnaier-list.txt-panel .opr-div{border-bottom: 1px solid #dddddd;}
.yellowmark{background: #fac134;width: 8px;height: 15px;border-radius: 0 5px 5px 0;position: absolute;top: 20px;left: -15px;}
.questionnaier.set-radio-style input[type="radio"],.cost-main .set-radio-style input[type="radio"]{-webkit-appearance: none;width: 17px;height: 17px;border: 1px solid #ccc;border-radius: 50%;outline: none;vertical-align: top;margin-top: 2px;}
.questionnaier.set-radio-style input[type="radio"]:checked,.cost-main .set-radio-style input[type="radio"]:checked{background: url(../images/radio_bg2.png) 50%;background-size:17px 17px;outline: none;border: 0;}
.questionnaier.set-radio-style input[type="radio"],.cost-main .set-radio-style input[type="radio"]{margin-right: 5px;}
.questionnaier.set-radio-style .sel-radio,.cost-main .set-radio-style .sel-radio{margin-left: 15px;}
.questionnaier.set-checkbox-style input[type="checkbox"]{-webkit-appearance: none;width: 17px;height: 17px;border: 1px solid #ccc;outline: none;border-radius: 50%;}
.questionnaier.set-checkbox-style input[type="checkbox"]:checked{background: url(../images/checkbox_bg2.png) 50%;background-size:17px 17px;outline: none;border: none;}
.questionnaier.set-checkbox-style input[type="checkbox"]{vertical-align: top;margin-right: 7px;margin-top: 2px;}
/*调查问卷-统计分析*/
.question-analysis{ margin-bottom: 10px;border-top: 1px solid #dddddd }
.question-analysis span{text-align: center; display: block; font-size:16px; color: #333333; font-weight: bold; line-height: 50px; background:#f8f8f8;}
.question-analysis table{background: #fff;border-collapse: collapse;width: 100%;}
.question-analysis table tr{height: 34px;}
.question-analysis table thead tr{font-size:13px;color:#333333;background: #f7f1da;height: 46px;text-align: center;border-top: 1px solid #dddddd;width:12.5%; }
.question-analysis thead tr td{border-right: 1px solid #dddddd; color: #666666; }
.question-analysis table tbody tr td:first-child{border-left: none;}
.question-analysis table tbody tr td:last-child,.question-analysis thead tr td:last-child{border-right: none;}
.question-analysis table tbody tr td{font-size:13px;border: 1px solid #dddddd;text-align: center;color: #666;}
/*库存管理*/
.stock-list{ width: 90%; margin:0px auto; overflow: hidden;}
.stock-list .stock-img{width: 100%; position: relative;}
.stock-list .stock-img img{ width: 100%;}
.stock-list p{color: #fff; font-size:16px;font-weight: bold; position: absolute; top:10px; left: 20px;}
.incre-btn{position: absolute;bottom: 10px;width: 100%; z-index: 1000;}
/*新建手动入库*/
.warehous-tit{ width: 100%; background:#f9f9f9; position: relative;}
.warehous-smtit{ color: #999999;background:#fff;  font-size: 14px; padding: 10px 15px;border-bottom: 1px solid #e5e5e5; margin-top: 10px; overflow: hidden;}
.warehous-tit ul{font-size: 15px;color: #666666;height: 40px;justify-content: space-around;}
.warehous-tit li{overflow: hidden;width:33.3%;padding: 5px 0;}
.warehous-tit li.current{  background:-webkit-linear-gradient(#fff,#fbfbfb); 
  background:-o-linear-gradient(#fff,#fbfbfb);
  background:-moz-linear-gradient(#fff,#fbfbfb);
  background:linear-gradient(#fff,#fbfbfb); }
.warehous-tit li span{ color: #666666; font-size: 12px; line-height: 14px; display: block; padding-left: 10px;}
.warehous-tit li p{ color: #333333; font-size: 15px; line-height: 18px; padding-left: 10px;}
.addfood{  background: #fbc134; border-radius:15px; height: 28px;display: flex; display: -webkit-flex; align-items: center;margin: 3px 5px 3px 0;color: #fff; font-size: 12px;justify-content: center; width: 73px; float: right;}
.warehous-txt{border: none; text-align: right;background: none;}
.warehous-txt::-webkit-input-placeholder{ color:#cccccc; }    /* 使用webkit内核的浏览器 */
.warehous-txt:-moz-placeholder{color:#cccccc;}                  /* Firefox版本4-18 */
.warehous-txt::-moz-placeholder{color:#cccccc;}                  /* Firefox版本19+ */
.warehous-txt:-ms-input-placeholder{color:#cccccc;}  
.warehous-sel{z-index: 999; width:122px; background-color:#ffffff;box-shadow: 0px 3px 3px 1px rgba(6,6,6,0.1);position: absolute;top: 41px;border-radius: 3px;left: 10px;font-size: 12px; padding: 15px 0;}
.warehous-sel:before,.price-elastic:after{content:"";display:block;border-width:10px;position:absolute; top:-20px;left:82px; border-color: transparent transparent #ffffff transparent; border-style: dashed dashed solid dashed;font-size:0; line-height:0;}
.warehous-sel:after{top:-18px; border-color: transparent transparent #ffffff transparent;} 
.warehous-sel p{text-align: center; line-height: 30px; font-size:16px; color: #999999;}
.warehous-sel p.current{color:#333333;}
/*斤数计算器*/
.catty-tit P{ border-bottom: 1px solid #eeeeee; border-top: 1px solid #eeeeee; color:#999999;font-size:12px;line-height: 30px;background:#fff;padding-left:10px; }
.catty-tit{ background:#fafafa; }
.catty-tit li{ float: left; width: 50%; font-size: 14px; color:#666666; margin-bottom: 10px;}
.catty-tit li input{vertical-align: middle; margin-right: 10px;}
.catty-tit ul{ margin:10px;}
.num-tit{background: #fff; overflow: hidden; clear: both }
.num-tit li{ float: left; width: 48%; font-size: 14px; color:#333333;  padding:0 1% 15px 1%; border-bottom: 1px solid #eeeeee; margin-top: 15px; position: relative;}
.num-tit li::before{content: "";position: absolute;left: 0;border-left: 1px solid #eeeeee;height: 30%;
top: 25%; }
.num-tit li input:focus{border:1px solid #999999; background:#fafafa;}
.num-tit li input{background:#fafafa; text-align: center;line-height:35px; width: 95px; height: 35px;border:1px solid #eeeeee; border-radius: 5px; margin: 0 5px; }
/*暂无消息*/
.no-mess{ position: absolute;top:50%;left:50%;width:100%;transform:translate(-50%,-50%);text-align: center;}
.no-mess img{ width:212px;}
.no-mess p{ font-size: 14px; color:#999999 }
/*完成收货确认提示*/
.opr-list{border: 1px solid #eeeeee;background: #ffffff;padding: 8px 15px;margin-bottom: 10px;position: relative;cursor: pointer; border-radius: 5px;}
.opr-list.current{border: 1px solid #19bb72;}
.opr-list .icon_check2{position: absolute;bottom: 5px;right: 0;width: 20px;height: 20px;}
.opr-list.current .icon_check2:before{color: #19bb72;}
/*统计栏目*/
.total-date{border-radius: 5px 5px 0 0;background: #00cd91;height: 38px;align-items: center;justify-content: space-between;padding: 0 15px;font-size: 15px;color: #FFFFFF;}
.total-num{border-bottom: 1px solid #eeeeee;margin: 0 10px;height: 77px;font-size: 12px;color: #666666;align-items: center;justify-content: space-around;text-align: center;}									
.total-num li{position: relative;}
.total-num li:before{content: "";position: absolute;right: 0;top: 35%;width: 1px;height: 40%;background: #eeeeee;}
.total-num li:last-child:before{content: none;}
.order-cost{padding: 0 11px;background: #ffffff;font-size: 12px;margin: 0 15px 7px 15px;}
.order-cost li{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;height: 34px;border-bottom: 1px dashed #eeeeee;}
.order-cost li:last-child{border-bottom: none;}
.order-cost li .val-num{color: #fcb303;font-size: 10px;}			
.order-cost .progressbar_1{ 
	background-color:none; 
    height: 5px; 
    width:70%;    
	border-radius:20px;				
} 
.order-cost .progressbar_1 .bar { 
    background-color:#1ac75f; 
    height:5px; 
	border-radius:20px;
} 
/*维修上报切换*/
.repair_work{ overflow: hidden}
.repair_work .weui-grids{background: #fff;}
.repair_work .weui-grid{box-sizing: border-box; padding:10px 0px 0 0px;width: 25%;background: #fff; }
.repair_work .weui-grid__icon, .check_word .weui-grid__icon { height: 40px; width: 40px;}
.repair_work .weui-grid__label, .check_word .weui-grid__label { color: #666666;font-size: 14px;}
.repair_work.weui-grid__icon+.weui-grid__label{ margin-top: 1;}
.repair_work .weui-grid::before{ border-right: none;}
.repair_work .weui-grids::before{border-top:none}
.repair_work .weui-grid::after{border-bottom: none}
.repair_work .weui-grids:after{border-left: none;}
.repair_work .weui-grid__label span{ height: 27px; display: block; text-align: center; margin: 0 auto; }
.repair_work .current .weui-grid__label span{ color: #ffb93d;  padding-bottom: 6px;display: block; }
.yellowbg.icon_star::before{ color:#FFB600; }
.repair-add-bg{background: url(../images/repair/add-btnimg1.png) no-repeat; padding-bottom: 2px; width: 123px; height:30px; background-size: 100% 100%;  font-size: 15px; color:#4c91ff; }
.repair-add{box-shadow: 0 5px 5px -1px inset #efefef;}
/*新增4*/.repair-add .icon_add4:before{content: "\e88a";display:inline-block;font-size: 19px;color: #4c91ff; margin-right: 5px;}
.weui-cell.repair-add:before{border-top: none;}

/*维修上报统计公用部分*/
.count-main .report-form{background: #fff; overflow: hidden; padding: 10px 0; position: relative; margin-top: 8px;}
.count-main .report-form p{ position: relative; }
.count-main .report-tit{background: #fac134;width:3px;height: 18px;border-radius:5px;position: absolute;top: 5px;left: 6px;}
.count-main .report-form p{border-bottom: 1px solid #DDDDDD; padding-left: 15px; padding-bottom: 5px; }
.count-main .report-form h3{ text-align: center; color:#999999; font-size: 15px;font-weight: normal; border-bottom: 1px solid #DDDDDD;border-top: 1px  solid #DDDDDD; margin-bottom:5px;}
.count-main .people-sta{ background: #fff; overflow: hidden; margin-top: 0px;}
.count-main .refresh-bg{ display: block; float: left; width: 21px; height:21px; line-height:23px; text-align: center; border-radius:50%;}
.count-main .people-txt{margin:0 15px 0px 15px;  display: block; font-size:14px; color: #fff;  border-radius: 20px; width: 100px; text-align: center; }
.count-main .ryellow-bg{ background: #e5a31f;}
.count-main .rgreen-bg{ background: #0cbd9a;}
.count-main .rred-bg{ background: #eb5537;}
.count-main .violet-bg{background: #9d56ee}
.count-main .rblue-bg{ background: #3091f2;}
.count-main .people-list ul{ margin-top: 6px; overflow: hidden;}
.count-main .people-leftbg{background:url(../images/statistical2.png) no-repeat;  width: 100%; background-size: 100% 117px; margin: 0 auto; height: 102px; padding-top: 15px;}
.count-main .people-rightbg{background:url(../images/statistical3.png) no-repeat;  width: 100%; background-size: 100% 117px; margin: 0 auto; height: 102px; padding-top: 15px;}
.count-main .people-bg{background:url(../images/statistical1.png) no-repeat;  width: 100%; background-size: 100% 117px; margin: 0 auto 10px auto; height: 102px; padding-top: 15px; }
.count-main .people-list{overflow: hidden; border-bottom: 1px solid #DDDDDD; padding:0px 15px 15px 15px;}
.count-main .people-list li{ float: left; width:49%; margin-right:5px;}
.count-main .people-list li:last-child{margin-right: 0px;}
.count-main .font44{ font-size: 44px; margin:10px 15px 0 20px; color: #fff}
.count-main .icon_calen2{padding-left: 10px;margin-left: 10px;border-left: 1px solid #dddddd;}
.time-detail{ width: 100%; overflow: hidden; color: #333333; font-size: 14px; padding-bottom: 10px;}
.time-detail li{ margin: 0 10px; border-bottom: 1px dashed #DDDDDD; overflow: hidden; padding: 5px 0;}
.time-detail li:last-child{border-bottom: none;}
.time-detail li:first-child{margin: 0 0px; border-bottom: 1px  solid #DDDDDD;border-top: 1px  solid #DDDDDD;}
.time-detail li span{width: 33.333%;float: left;  text-align: center; line-height: 25px;}
.time-detail li span img{margin-right: 5px; vertical-align: middle;}
.count-main .stat-seltab,.count-main .stat-seltab-myaudit{float: left;}
.count-main .stat-seltab i,.count-main .stat-seltab-myaudit i{display: inline-block; text-align:center; font-size: 14px; margin-right: 15px;}
.count-main .stat-seltab i.current,.count-main .stat-seltab-myaudit i.current{background:#fac134; color: #fff; border-radius: 20px; padding: 3px 10px; }
.count-main .cost-form{padding: 0 15px 15px 15px;border-top: 1px solid #DDDDDD; overflow: hidden; }
.count-main .cost-form p{margin-top: 10px;}
.count-main .cost-form .leftbor{border-left: 1px solid #EDEDED; padding-left:25px}
.count-main .report-formbot{padding: 10px 15px 10px 15px; overflow: hidden;}
.img-tit{ width: 100%; color: #999999; font-size: 12px; margin-top: 4px;}
/*维修费用公用*/
.weui-rtit{position: absolute; bottom:15px; text-align: right; width: 100%;font-size: 12px; right: 15px; color: #999999;}
.cost-main .img-tit{ width: 100%; color: #999999; font-size: 12px; margin-top: 4px;}
.repair-div{background: #fff;margin-top: 7px;padding: 10px 15px;overflow: hidden;}
.repair-div textarea{width: 93%;height:60px;padding: 10px; border: none;}
.repair-div h3{ border-bottom: 1px solid #eeeeee;overflow: hidden; font-size: 15px; font-weight: normal;}
.repair-div .icon_sound:before{ color:#5d8ff7; font-size:18px;}
.voice-bg{ display: block; float: left; background:#DDE9FF; width: 30px; height:30px; text-align: center; border-radius:50%;}
.repair-div p{ font-size: 14px; color: #999999;}
.approval-label{ position: absolute;top:10px; right:100px; text-align: center; }
.search-rpeo{background:#ff0000; border-radius:10px;width: 15px; height: 15px;display:inline-block; color: #fff;font-size: 10px;margin-top: -3px;line-height: 15px;text-align: center;}
/*费用弹框公用*/
.accurate-costs{z-index: 999;background-color:#f7f7f7;border-radius: 5px;font-size: 12px; position: absolute; top:45px; right: 10px; padding: 5px 0; }
.accurate-costs:before,.accurate-costs:after{content:"";display:block;border-width:10px;position:absolute; top:-20px;right:12px; border-color: transparent transparent #f7f7f7 transparent;border-style: dashed dashed solid dashed;font-size:0; line-height:0;}
.accurate-costs:after{top:-18px; border-color: transparent transparent #f7f7f7 transparent;} 
.accurate-costs p{ text-align: left; padding:0 10px; font-size: 12px; color: #666666; line-height: 25px;}
.accurate-costs p span{ width: 50px; display:inline-block;font-size: 12px;color: #fff; border-radius:4px;padding: 1px 5px; text-align: center;margin-left:7px; line-height:normal;}
/*提醒公用图片大小*/
.remind-img{ width: 40px; height: 40px; border-radius: 50%; vertical-align: middle; margin-right: 10px;}
.remindpr-img{ width: 30px; height: 30px; border-radius: 50%; vertical-align: middle; margin-right:-10px;}
.contacts-tit{ font-size: 14px; padding-left: 15px; color: #999999; line-height: 30px;}
.remindpr-rt{width:143px; white-space:nowrap; text-overflow:ellipsis; -o-text-overflow:ellipsis; overflow:hidden; font-size: 15px; color: #d8d8d8; padding-right: 2px;}
.remindpr-rt i{ margin-left: 10px; display:inline-block;}
.addreport-btn{  background: #ddeaff; border-radius: 5px; height: 25px;display: flex; display: -webkit-flex; align-items: center;margin: 3px 8px 3px 0px;color: #4c91ff; font-size: 14px;justify-content: center; width:52px;   position: absolute; right: 0px; top:4px;}
/*录入服务*/
.list-title{position: relative;font-size: 16px;color: #181818;}
.list-title:before{content: "";width: 5px;height: 20px;background: #f1c353;border-radius: 0 2px 2px 0;position: absolute;left: 0;top: 50%;margin-top: -10px;} 
.service-txt{background: #ffffff;padding: 0 15px;margin-bottom: 7px;border-top: 1px solid #dddddd;border-bottom: 1px solid #dddddd;}
.service-txt h5{align-items: center;justify-content: space-between;font-size: 16px;color: #333333;height: 45px;border-bottom: 1px solid #dddddd;}
.service-txt p{font-size: 14px;color: #999999;text-indent: 28px;padding: 10px 0;}
.area-num{height: 75px;background: #ffffff;margin: 10px 15px;border-radius: 3px;} 	   
.area-numcell{-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;flex-direction: column;position: relative;}     	  
.area-numcell:after{content: "";width: 1px;height: 40%;background: #eeeeee;position: absolute;right: 0;}  	  
.area-num .area-numcell:last-child:after{content: none;}  	
.area-numcell2{-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;height: 75px;flex-direction: column;margin: 0 5px;border-radius: 3px;}
/*弹框底部按钮*/
.box-oprbtn{border-top: 1px solid #dddddd;}
.box-oprbtn a{border-right: 1px solid #dddddd;color: #666666;height: 45px;font-size: 15px;}
.box-oprbtn a.current{color: #399bf7;}
.box-oprbtn a:last-child{border-right: none;}

/*新版通知*/
.enroll-list .report-tit{ display: inline-block; background: #00cd91;width:4px;height: 18px;position: absolute;top:12px;left: 16px; font-size: 16px; color: #333333; border-top-left-radius:10px;border-top-right-radius:10px;border-bottom-left-radius: 10px;border-bottom-right-radius:10px;}
.enroll-list .com-tit p{border-bottom: 1px solid #DDDDDD; padding:9px 15px 10px 30px;  overflow: hidden; position: relative;}
.enroll-list .icon_tel4::before{ content: "\e682";font-size: 20px;color: #60adf8;}
.weui-btn_opre {
    background: -webkit-linear-gradient(left, #FF7C68, #FF4360);
    background: linear-gradient(left, #FF7C68, #FF4360);
    margin: 0 15px;
}

.weui-btn_blue {
    background: -webkit-linear-gradient(left, #53b8ff, #53b8ff);
    background: linear-gradient(left, #53b8ff, #53b8ff);
    margin: 0 15px;
}

.weui-btn_gray {
    background: -webkit-linear-gradient(left, #b8b8b8, #b8b8b8);
    background: linear-gradient(left, #b8b8b8, #b8b8b8);
    margin: 0 15px;
}

.weui-btn_empty {
    border: 1px solid #DCDCDD;
    color: #333 !important;
}

    .weui-btn_empty:after {
        border: 0 !important;
    }

    .form .error { border-bottom: solid 1px red!important; }