﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <link rel="shortcut icon" href="/images/favicon.ico" type="image/x-icon" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="Copyright" content="Copyright 2016 by tongbang" />
    <meta name="Author" content="" />
    <meta name="Robots" content="All" />
    <title>此表由保健人员填写</title>
    <link href="../../styles/admin.css" rel="stylesheet" type="text/css" />
    <link href="../../styles/tbstyles.css" type="text/css" rel="stylesheet" />
    <link href="../../plugin/ehaiform/css/tbbaseform.css" rel="stylesheet" type="text/css" />
    <link href="../../plugin/editselect/css/editselect.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../plugin/jquery_tabs/css/tbjquery.tabs.css" type="text/css"
          media="print, projection, screen" />
	<style>

		.rytjdjedit_table td{border:#eee solid 1px}
        /*滚动条样式*/
        html{
            color: #000;
            font-size: .7rem;
            font-family: "\5FAE\8F6F\96C5\9ED1",Helvetica,"黑体",Arial,Tahoma;
            height: 100%;
        }
        /*滚动条样式*/
        html::-webkit-scrollbar {
            display:none;
            width: 6px;
            height: 6px;
        }
        html:hover::-webkit-scrollbar{
            display:block;
        }
        html::-webkit-scrollbar-thumb {
            border-radius: 3px;
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
            border: 1px solid rgb(255, 255, 255);
            background: rgb(66, 66, 66);
        }
        html::-webkit-scrollbar-track {
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
            border-radius: 0;
            background: rgba(0,0,0,0.1);
        }
        .full_disease {
            display: inline-block;
            background: #eee;
            padding:0 10px;
            width:60px;
            text-align: center;
        }
		.paddtop label{display: inline-block; margin-right: 15px;}
		.paddtop input{margin-right: 3px; vertical-align: top;margin-top: 8px;}
	</style>
</head>
<body>
    <div class="bodywidth" style="min-width: 920px;">
        <div class="content">
            <div class="layui-form layui-comselect" style="position: relative;padding: 10px 10px 5px 10px;">
                <div style="margin:10px 0;">
                    <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                        <label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 10px;line-height: 28px;">选择学年：</label>
                        <div class="layui-input-inline" style="min-height: 30px;width:180px;">
                            <select lay-filter="sel_xuenian" id="xuenian">
                                <option value="">请选择</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                        <label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 10px;line-height: 28px;">班级：</label>
                        <div class="layui-input-inline" style="min-height: 30px;width:180px;">
                            <select lay-filter="sel_classno" id="classno">
                                <option value="">请选择</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                        <label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 10px;line-height: 28px;">选择幼儿：</label>
                        <div class="layui-input-inline" style="min-height: 30px;width:180px;">
                            <select lay-filter="sel_stuno" id="stuno">
                                <option value="">请选择</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <table class="layui-table">
                <colgroup>
                    <col width="150">
                    <col width="150">
                    <col width="150">
                    <col width="150">
                    <col width="150">
                    <col width="150">
                    <col width="150">
                    <col width="150">
                </colgroup>
                <tbody>
                <tr>
                    <td>姓名</td>
                    <td>
                        <span id="stuname"></span>
                    </td>
                    <td>性别</td>
                    <td>
                        <span id="sex"></span>
                    </td>
                    <td>年龄</td>
                    <td>
                        <span id="age"></span>
                    </td>
                    <td>班级</td>
                    <td>
                        <span id="classname"></span>
                    </td>
                </tr>
                <tr>
                    <td colspan="2">伤害发生日期</td>
                    <td colspan="6">
                        <input id="occtime" type="text" style="border:none;border-bottom:1px solid #5F5F5F;"/>
                    </td>
                </tr>
                <tr>
                    <td>伤害发生地点</td>
                    <td colspan="7" class="paddtop">
                        <label><input type="radio" name="address" value="1"/>1.户外活动场地</label>
                        <label><input type="radio" name="address" value="2"/>2.活动室</label>
                        <label><input type="radio" name="address" value="3"/>3.寝室</label>
                        <label><input type="radio" name="address" value="4"/>4.卫生间</label>
                        <label><input type="radio" name="address" value="5"/>5.盥洗室</label>
                        <label><input type="radio" name="address" value="6"/>6.其他</label><span>(请说明<input id="address_text" type="text" style="border:none;border-bottom:1px solid #5F5F5F;"/>)</span>
                    </td>
                </tr>
                <tr>
                    <td>伤害发生时活动</td>
                    <td colspan="7" class="paddtop">
                        <label><input type="radio" name="activity" value="1"/>1.玩耍娱乐</label>
                        <label><input type="radio" name="activity" value="2"/>2.吃饭</label>
                        <label><input type="radio" name="activity" value="3"/>3.睡觉</label>
                        <label><input type="radio" name="activity" value="4"/>4.上厕所</label>
                        <label><input type="radio" name="activity" value="5"/>5.洗澡</label>
                        <label><input type="radio" name="activity" value="6"/>6.行走</label>
                        <label><input type="radio" name="activity" value="7"/>7.乘车</label>
                        <label><input type="radio" name="activity" value="8"/>8.其他</label><span>(请说明<input id="activity_text" type="text" style="border:none;border-bottom:1px solid #5F5F5F;margin-right: 15px;"/>)</span>
                        <label><input type="radio" name="activity" value="9"/>9.不知道</label>
                    </td>
                </tr>
                <tr>
                    <td>损伤的部位</td>
                    <td colspan="3">
                        <input id="damaged_area" type="text" style="border:none;border-bottom:1px solid #5F5F5F;"/>
                    </td>
                    <td>损伤恢复时间</td>
                    <td colspan="3">
                        <input id="recoverytime" type="text" style="border:none;border-bottom:1px solid #5F5F5F;"/>
                    </td>
                </tr>
                <tr>
                    <td>转归类型</td>
                    <td colspan="3" class="paddtop">
                        <label><input type="radio" name="conversion" value="1"/>1.痊愈</label>
                        <label><input type="radio" name="conversion" value="2"/>2.好转</label>
                        <label><input type="radio" name="conversion" value="3"/>3.残疾</label>
                        <label><input type="radio" name="conversion" value="4"/>4.死亡</label>
                    </td>
                    <td>当班责任人</td>
                    <td colspan="3">
                        <input id="person" type="text" style="border:none;border-bottom:1px solid #5F5F5F;"/>
                    </td>
                </tr>
                <tr>
                    <td>简述伤害发生经过（对损伤过程作综合描述）</td>
                    <td colspan="7">
                        <textarea id="process" style="width:100%;height:100%;border:none;"></textarea>
                    </td>
                </tr>
                <tr>
                    <td>医疗处理：（医院的最后诊断和治疗意见）</td>
                    <td colspan="7">
                        <textarea id="medical" style="width:100%;height:100%;border:none;"></textarea>
                    </td>
                </tr>
                <tr>
                    <td>伤害原因分析</td>
                    <td colspan="7" class="paddtop">
                        <label><input type="radio" name="reason" value="1"/>1.交通事故</label>
                        <label><input type="radio" name="reason" value="2"/>2.跌伤(跌、摔、滑、绊)</label>
                        <label><input type="radio" name="reason" value="3"/>3.被下落物击中(高处落下物)</label>
                        <label><input type="radio" name="reason" value="4"/>4.锐器伤(刺、割、扎、划)</label>
                        <label><input type="radio" name="reason" value="5"/>5.钝器伤(碰、砸)</label>
                        <label><input type="radio" name="reason" value="6"/>6.烧烫伤(火焰、高温固/液体、化学物质、锅炉、烟火、爆竹炸伤)</label>
                        <label><input type="radio" name="reason" value="7"/>7.溺水(经医护人员救治存活)</label>
                        <label><input type="radio" name="reason" value="8"/>8.动物伤害(狗、猫、蛇等咬伤、蜜蜂、黄蜂等刺蜇)</label>
                        <label><input type="radio" name="reason" value="9"/>9.窒息(异物，压、闷、捂窒息，鱼刺/骨头卡喉) </label>
                        <label><input type="radio" name="reason" value="10"/>10.中毒(药品、化学物质、一氧化碳等有毒气体 , 农药, 鼠药, 杀虫剂，  腐败变质食物除 外)</label>
                        <label><input type="radio" name="reason" value="11"/>11.电击伤(触电、雷电)</label>
                        <label><input type="radio" name="reason" value="12"/>12.他伤/攻击伤</label>
                    </td>
                </tr>
                <tr>
                    <td>园领导意见</td>
                    <td colspan="7">
                        <textarea id="opinions" style="width:100%;height:100%;border:none;"></textarea>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <script type="text/javascript">
        var v = top.version;
        document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../sys/function.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../js/richang/tuoyucommon.js?v='" + v + "><" + "/script>");
        document.write("<script type='text/javascript' src='../../js/richang/tuoyuyouershanghaidetail.js?v='" + v + "><" + "/script>");
    </script>
</body>
</html>
