﻿
.weui-flex{display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex;}
/*表格从写*/
.layui-table th, .layui-table td{height: 40px;line-height: 40px;}
.layui-table td, .layui-table th{font-size: 13px;}
.layui-input, .layui-select, .layui-textarea{border-radius: 2px;}
.layui-table thead tr, .layui-table-click, .layui-table-header,  .layui-table-mend, .layui-table-patch, .layui-table-total, .layui-table-total tr{background:#FCFCFC;color: #34495E;}
.layui-table,input,select{color: #34495E;}
.layui-table{margin: 0;}
/*新版日历*/
.layui-comselect .layui-alendar{width: auto;height: auto;left: 10px;position: absolute; top:9px;cursor: pointer;}
.default-btn{min-width: 44px;height: 25px;line-height: 19px;margin: 3px;text-align: center;padding: 3px 8px;box-sizing: border-box;font-size: 13px;display: inline-block;color: #ffffff;border-radius: 2px;vertical-align: top;cursor: pointer;}
.layui-form-radio > i:hover, .layui-form-radioed > i {color: #3C99FC;}
.select-open{border: 1px solid #DDDDDD; }
.layui-laydate .layui-this{background-color: #3C99FC !important;}
.cuptop{background: #ffffff; margin: 10px 0;  border-radius: 5px; color: #4C5E71;}
/*栏目重写*/
.content-medical{/*background: #ffffff;*/ /*margin: 10px 0 0px 0;  border-radius: 5px;*/ color:#34495E;}
.manufacturer-txt{color: #34495E;font-weight: bold; font-size: 14px;line-height: 30px;}
.layui-table-view{ margin: 0;}
.layui-btn.addbtn{height: 30px; line-height: 30px; background:#4A90E2;border-radius: 2px; }
.tbmargin{margin:0px 0px 8px 0px; }
.comtitletop{padding:8px 15px; height: 30px; border-bottom: 1px solid #DCDFE6;color: #34495E;font-size: 14px;}
.comtitletopbor{padding:8px 15px; border-bottom: none;color: #34495E;font-size: 14px;}
.comtitletopbor.comtitletoptop{padding:4px 15px;}

.conference-sta {display: flex; height: 150px; margin: 0 10px 10px 10px;}

.ans-conference-sta {
  display: flex;
  height: 192px;
  margin: 0 10px 10px 10px;
	
}
.ye-conference-sta {
  height: 147px;
  margin: 0 10px 10px 10px;
	  display: flex;
  
}
.kindergartenimg{ position: relative; width: 100%; height: 100%; text-align: center;}
.total-panel{ position: absolute;            
    top:50%;            
    left:50%;            
    width:100%;            
    transform:translate(-50%,-50%);            
    text-align: center; display: flex; align-items: center; justify-content: space-between;}


.total-answers{ position: absolute;  top: 0; left: 0; width: 100%;  }            

.num-pt{ text-align: left; padding:20px 20px; font-size: 16px; color: #fff;}
.total-bot{border-top: 1px solid rgba(230,233,240,0.3);height: 46px; line-height: 46px;clear: both; padding: 0 20px;text-align: center; position: relative;}
.num-hb{font-size: 14px;}
.rise-ltimg{width: 17px; height: 16px; margin: 0 5px;}
.total-answers .num-label{font-size: 30px;}
.total-list{ margin-bottom: 10px; height: 72px;}
.total-bot span{display: inline-block; padding: 0 20px;}
.total-bot span::after{ content: ""; height: 14px; border-right:1px solid rgba(230,233,240,0.3);position: absolute;top: 16px;margin-left: 20px;}
.total-bot span:last-child::after{border-right:none;}
.total-div {
  height: 100%;
  margin-left: 8px;
  display: inline-block;
  vertical-align: top;
  flex: 1;
  color: #fff;
 position: relative;

}
.total-numbg{ float: left; margin-left: 5px; text-align: left;}
.num-label{font-size: 32px;}
.num-peo{font-size: 16px;}
.total-left{float: left;display: flex;}
.total-right{ float: right; padding-right: 15px;}
.kinder-ltimg{float: left; width: 60px; height: 60px; margin-right: 5px; margin-left: 10px;}
.total-right span{font-size: 18px;}


/*上升箭头*/
.icon_arrow_goup:after{
	color: #fff!important;
}
/*下降箭头*/
.icon_arrow_decline:after{
	color: #fff!important;
}



.content-medical{ padding: 5px 0 10px 0; }

.cenmartop{position: absolute;top:55px; overflow: hidden;left: 0;right: 0px;}

/*体检对比*/

.visiting-statis{margin: 0px 0px 0px 10px; display: flex;-webkit-box-flex: 1;   /* OLD - iOS 6-, Safari 3.1-6 */  -moz-box-flex: 1;    /* OLD - Firefox 19- */  -webkit-flex: 1;   /* Chrome */ -ms-flex: 1;/* IE 10 */ }
.safestat-cen{height: 380px;text-align: center;position: relative;background: #fff;border-radius: 4px 4px 4px 4px;margin:0px 10px 10px 0;  flex: 1;-webkit-box-flex: 1;   /* OLD - iOS 6-, Safari 3.1-6 */  -moz-box-flex: 1;    /* OLD - Firefox 19- */  -webkit-flex: 1;   /* Chrome */ -ms-flex: 1;/* IE 10 */ }
.safestat-tit{ text-align: left; font-size:14px; color: #333333; overflow: hidden;  border-bottom: 1px solid #eeeeee; height: 40px; padding-right:15px;}
.safestat-tit span{ display: inline-block; line-height:40px; padding-left: 20px;font-size: 14px;color: #34495E; }
.safestat-tit .layui-btn{ line-height: 30px;height: 30px; color: #fff;}
.safestat-tit .layui-btn-primary{color: #fff;opacity: 0.8;}
.left-tit{ display: inline-block; background: #FF4B69;width:4px;height: 14px; border-radius: 2px; position: absolute;top:12px;left: 10px; font-size: 14px;}
.left-tit.greenbg{background: #00C553;}
.layui-form-mid {
  padding: 9px 0px 9px 9px  !important;

}


.heighrow{height: 790px;}
.columnlist{ margin: 15px 0px 0 15px; background: rgba(22,24,35,0.02);border-radius: 4px 4px 4px 4px; flex: 1;-webkit-box-flex: 1;   /* OLD - iOS 6-, Safari 3.1-6 */  -moz-box-flex: 1;    /* OLD - Firefox 19- */  -webkit-flex: 1;   /* Chrome */ -ms-flex: 1;/* IE 10 */ }
/*.hit100{ height: 100%;}*/
.hit50{ height: 50%;}
.colunwit{ position: absolute;top: 40px;bottom: 15px; left: 0px;right: 15px;}
.columnlist h3{font-size: 14px;color: #34495E;  line-height: 30px; text-align: left; padding-left: 10px;}

.ye-tjbg{line-height: 30px; font-size: 16px; color: #34495E; flex: 1;}



/*健康宣教大数据*/
.colunwit .layui-table th,.colunwit .layui-table td{height: 32px;line-height:32px;color: #34495E;}
.colunwit .layui-table[lay-size="lg"] td,.colunwit .layui-table[lay-size="lg"] th{padding: 0px 10px;}
.colunwit .layui-table tbody tr:hover,.colunwit .layui-table-hover{background: #f5f2c8!important;}				
.colunwit .layui-table td,.colunwit .layui-table th{padding: 0 5px; position: relative; border: none;}
.colunwit .layui-table th{color: #34495E; background: #F7F8FA;}
.rank-label{background: #778CA2;width: 20px;height: 20px;line-height: 20px;border-radius: 50%;display: inline-block;font-size: 14px;color: #fff;text-align: center; margin-right: 5px;}
.rank-label.redbg{background:#FF4B69;}
.rank-label.yellowbg{background:#F3C96B;}
.rank-label.orangebg{background:#FF7A3C;}
/*便民服务大数据*/
.visiting-div{ flex: 1;}
.safestat-cen.height770{ height: 770px;}
/*AI问答统计*/
ul.chart-list {color: #34495E; padding: 0 0px 0px 15px; }
ul.chart-list li { line-height: 35px; height: 35px; position: relative; text-align: left;}
ul.chart-list li label { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; width: 180px; display: inline-block; vertical-align: middle; }
ul.chart-list li span { float: right; }
.chart-list li:first-child:before { display: none; }
.color-txtdate {float: right;}
.color-red{color: #FF4B69; }
.color-orange{color: #FF7A3C;}
.color-yellow{color: #F3C96B;}
.chart-list label i{margin-right: 5px;}
/*公用样式按钮下一行*/
.btn-next{width: 213px;display: inline-block;}
.btn-nextlt,.btn-nextfl{display: inline-block; vertical-align: top;}
.line-gt{height:37px;}
@media screen and (min-width:1000px) and (max-width:1400px){
	.line-gt{height: 42px;}
	.btn-nextfl{margin-bottom: 5px;}
	}
/*左右距离*/
.layui-tab-brief.tab-tit{padding: 0px 10px 0 0;
margin-top:0px;}
