﻿
.weui-flex{display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex;}
/*表格从写*/
.layui-table th, .layui-table td{height: 40px;line-height: 40px;}
.layui-table td, .layui-table th{font-size: 13px;}
.layui-input, .layui-select, .layui-textarea{border-radius: 2px;}
.layui-table thead tr, .layui-table-click, .layui-table-header,  .layui-table-mend, .layui-table-patch, .layui-table-total, .layui-table-total tr{background:#FCFCFC;color: #34495E;}
.layui-table,input,select{color: #34495E;}
.layui-table{margin: 0;}
.layui-form-item .layui-form-checkbox[lay-skin="primary"] {
  margin-top: -1px;
}
#fypgdiv p{margin-top: 10px;}
/*新版日历*/
.layui-comselect .layui-alendar{width: auto;height: auto;left: 10px;position: absolute; top:9px;cursor: pointer;}
.default-btn{min-width: 44px;height: 25px;line-height: 19px;margin: 3px;text-align: center;padding: 3px 8px;box-sizing: border-box;font-size: 13px;display: inline-block;color: #ffffff;border-radius: 2px;vertical-align: top;cursor: pointer;}
.layui-form-radio > i:hover, .layui-form-radioed > i {color: #3C99FC;}
.select-open{border: 1px solid #DDDDDD; }
.cuptop{background: #ffffff; margin: 10px 0;  border-radius: 5px; color: #4C5E71;}
/*栏目重写*/
.content-medical{background: #ffffff; /*margin: 10px 0 0px 0;  border-radius: 5px;*/ color:#34495E;}
.manufacturer-txt{color: #34495E;font-weight: bold; font-size: 14px;line-height: 30px;}
.layui-table-view{ margin: 0;}
.layui-btn.addbtn{height: 30px; line-height: 30px; background:#4A90E2;border-radius: 2px; }
.tbmargin{margin:0px 0px 8px 0px; }
.comtitletop{padding:8px 15px; height: 30px; border-bottom: 1px solid #DCDFE6;color: #34495E;font-size: 14px;}
.comtitletopbor{padding:8px 15px; border-bottom: none;color: #34495E;font-size: 14px;}
.comtitletopbor.comtitletoptop{padding:4px 15px;}
/*录入管理*/
.catalog-tit{
    background: #F2F8FF;
    border: 1px solid #A1C4EE;
    height: 22px;
    line-height: 22px;
    border-radius: 30px;
    display: inline-block;
    color: #333333;
    margin:0px 0px 0 10px;
    font-size: 12px;
    cursor: pointer;
    padding: 0 10px;
}
.catalog-num{ color:#4A90E2;font-weight: bold;}
/*录入文件夹*/
.folder-ul{font-size: 0;margin: 5px 10px;}
.folder-ul li{display:inline-block;width: 14.2%;font-size: 14px;vertical-align: top;position: relative;}
.folder-cell {margin: 5px;padding: 10px 11px 10px 11px;position: relative; border: 1px solid #F2F2F2;}
.folder-cell:hover{background:#F9F9F9;border: 1px solid #F2F2F2;display:block;}
.folder-cell p.folder-txt{text-align: left;font-size: 14px;color:#333; margin: 20px 0 5px 0; font-weight: bold;}
.folder-cell p{font-size: 12px;}
.folder-cell h6{text-align: center; width:149px; height: 142px; width: 100%; }
.folder-cell h6 img{ width:151px;}
.class-tit{color:#999999;font-size: 13px;}
.foldermore{position: absolute;right:10px;top:10px; z-index: 999;cursor: pointer; color:#999999;background:#EEEEEE; border-radius: 10px;font-size: 12px;padding: 0 5px;}
/*录入文件夹*/
.pagenext{background:#F3F3F3; border-radius: 20px;font-size: 12px;padding:10px 10px; margin: 10px auto;width: 300px; text-align: center;}
.pagenext span img{ margin:0 10px;  width: 18px; height: 18px; cursor: pointer;}
.drag-box{ position: absolute; top:0;height: 100%; left:90px;right:15px;}
.drag-img{height:100%;}
.drag-img img{width: 100%; height:100%;}
.nextimg{width:59px; height: 80%; left: 0;position:absolute; top:30px;}
.nextimg img{ width: 100%;height:60%;}
.rightnextimg{width:24px; height: 80%; right:-40px;position:absolute; top:35px;overflow: hidden;}
.rightnextimg img{width: 100%;height:60%;}
/*栏目重写*/
.noticeTitle{font-weight: bold;border-top-left-radius:5px;border-top-right-radius:5px;background: #D1DEEB; height: 45px; line-height: 45px; padding: 0 10px; font-size: 15px;}
.noticeTitle,.noticeTitle a{color: #34495E;}
.formList label{width: 101px;text-align: right;display: inline-block;}
.formList input[type="text"] {
    border: 1px solid #ccc;
    height: 35px;
    line-height: 35px;
    padding: 0 8px;
    border-radius:4px;
}
.formList input[type="text"] {
    width: 520px;
}
.formList select {
    border: 1px solid #ccc;
    height: 35px;
    line-height: 35px;
    padding: 0 4px;
    border-radius: 0;
    color: #6C6C6C;
    margin-left: -4px;
    border-radius:4px;
}

.layui-input.form-ordernum{padding-left: 35px;}
.knowledgebox,.recipeinfobox{cursor: pointer;}
/*查看*/
.title-info{font-weight: bold;border-bottom: 1px solid #eeeeee;height: 40px;line-height: 40px;color: #34495E;margin-bottom: 10px;font-size: 14px;} 
.form-list{margin: 10px; position: relative; padding: 5px 0px;color:#798EA4;font-size: 14px;}
.form-list .layui-form-label{padding: 5px 0; }
.form-list .layui-input-block{ color:#34495E;line-height: 30px; }
.kinderone li{line-height: 35px; padding-left: 10px;font-size: 14px; height: 35px; padding:0 10px;}
.kindertwo li{line-height: 35px; padding:0 10px 0 20px;font-size: 14px; height: 35px; position: relative;}
.kindertwo li .editim-btn{position: absolute; left:0px;top:-1px;}
.kindertwo li:hover .editim-btn{display: block !important; cursor: pointer;}
.kindertwo span{float: left;white-space: nowrap;text-overflow: ellipsis;-o-text-overflow: ellipsis;overflow: hidden; width:140px;text-align: left;}
.kindertwo label{float: right;}
.kindertwo li:hover{background:#F6F6F6; }
.kinder-time{padding: 0 10px 0 20px;}
/*添加园所*/
.gardenhouse-list{margin: 0px auto; overflow: auto;width:100%;	
	display:-webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;}
.gardenhouse-left{ float: left; padding: 20px 30px;flex: 1;}
.gardenhouse-rihgt{float:right; margin-left:20px;margin-right: 20px; width:270px;  background: #FCFCFC; border-top-left-radius: 8px;  border-top-right-radius:8px; }
.gardenhouse-rihgt h3{background:#FFF0D6; border-top-left-radius: 8px;  border-top-right-radius:8px; border-bottom: 1px solid #F1ECE3; height: 45px; line-height: 45px; font-size: 14px; font-weight: bold; padding: 0 10px;}
/*入园体检设置*/
.examination-number{ padding: 10px 10px;overflow: hidden;}
.rilibg label{margin-right: 10px}
.operation-label{padding:0 5px;width: 250px; background: #F1F7FF;margin: 5px;display: inline-block; }	
.operation-label .triangle-left{width: 0;height: 0;border-top: 5px solid transparent;border-right: 7px solid #F1F7FF;border-bottom: 5px solid transparent;position: absolute;left: -10px;top:8px;}
.operation-label .triangle-left2{width: 0;height: 0;border-top: 4px solid transparent;border-right: 6px solid #F1F7FF;border-bottom: 4px solid transparent;position: absolute;left: -9px;top: 9px;}
.operation-label  .layui-form-radio{margin-right: 10px; color:#34495E;}
.datemain input,.datemain textarea{border:1px solid #CBD6E1; color:#34495E;}
.datemain{width: 100%; margin: 5px 0;color:#34495E;}
.setup-time{background: #F5F7FA; padding: 10px 10px 0 20px; position: relative;border-bottom:1px solid #E7ECF0; }
.operation-label label{padding-top: 4px;}
.setup-con{display: flex;}
.setup-timeone{padding:0 0 10px 0; }
.linebg{color: #6E7D8C; display: inline-block; padding: 8px 5px;vertical-align: top;}
/*幼儿园入园体检*/
.child-medicallt{background: #ffffff;padding: 10px 0;border-radius: 5px;box-shadow: 0 0 10px 3px #D3D7DB; }
.child-medicalrt{background: #ffffff;padding: 10px 0;position: absolute;left: 0;top: 8px;right: 8px;bottom:8px; box-shadow: 0 0 10px 3px #D3D7DB;border-radius: 5px;}
/*预约体检报名名单*/
.catalog-tit{
    background: #F2F8FF;
    border: 1px solid #A1C4EE;
    height: 22px;
    line-height: 22px;
    border-radius: 30px;
	display: inline-block;
    color: #666;
    margin:0px 0px 0 10px;
	font-size: 12px;
	cursor: pointer;
	padding: 0 10px;
}
.catalog-num{ color:#4A90E2;font-weight: bold;}
/*个人中心*/
.layui-tab-title1 {border-bottom: 1px solid #DDDDDD; font-size:0;position: relative; height: 35px; background: #FAFAFA;}
.layui-tab-title1  li{height:35px;display:inline-block;vertical-align:middle;
 font-size:14px;
 line-height:35px;
 min-width:65px;
 padding:0 15px;
 text-align:center; cursor:pointer; color: #A1A1A1;
}
.layui-tab-title1 li:first-child {
    /*border-right: none;*/
}
.layui-tab-title1  li.layui-this{background: #ffffff;color: #333333; border-bottom: none; line-height: 36px; height: 36px;}
.layui-tab-title1 .layui-this::after {
    position: absolute;
    left: 0;
    top: 0;
    content: "";
    width:96px;
    height: 35px;
    border-right:1px solid #DDDDDD;
    border-bottom-color: #FAFAFA;
    box-sizing: border-box;
    pointer-events: none;
}

/*订单管理*/
.order-cell .icon_form,.order-cell .icon_local,.order-cell .icon_form2{margin-right: 4px;}
.pro-list{text-align: center;display: inline-block;vertical-align: top;height: 90px;}
.pro-circle{width: 36px;height: 36px;border-radius: 50%;display: inline-block;margin: 3px 0;}
.pro-circle label{width: 26px;height: 26px;display: inline-block;border-radius: 50%;margin-top: 5px;}
.pro-circle.bluepro{border: 1px solid #62afff;}
.pro-circle.bluepro label{background: #62afff;}
.pro-circle.redpro{border: 1px solid #ff5c5c;}
.pro-circle.redpro label{background: #ff5c5c;}
.pro-circle.graypro{border: 1px solid #dbdbdb;}
.pro-circle.graypro label{background: #dbdbdb;}
.pro-line{vertical-align: top;margin: 39px -31px 0 -31px;width: 26%;display: inline-block;}
.pro-line.blueline{border: 1px dashed #15BC83;}
.pro-line.grayline{border: 1px dashed #D2D2D2;}
.order-img{width: 42px;height: 42px;line-height: 42px;margin: 5px 0;}
.order-cell{margin: 15px 38px;}
.order-cell h5{padding: 10px;background: #F9F9F9;color: #34495E;font-size: 14px;font-weight: bold;border: 1px solid #e4e4e4;border-bottom: none;}
.order-cell ul{color: #333333;font-size: 14px;padding: 10px 30px;}
.order-cell ul li{margin: 8px 0;}
.order-cell .icon_form,.order-cell .icon_local,.order-cell .icon_form2{margin-right: 4px;}
.eseal-list{background: #ffffff;padding: 10px;position: relative;line-height: 20px; height: 189px;}

.eseal-list p{font-size: 13px;color: #34495E;margin-top: 3px;}
.eseal-list i{color: #798EA4;font-size: 13px;display: inline-block; text-align: right;float: left; margin-right: 10px;}
.eseal-list label{display: block;}
.eseal-oprbtn{font-size: 12px;margin-top: 8px;clear: both; border-top: 1px solid #E8E8E8; padding:10px 0 0 0; text-align: right;}
.eseal-oprbtn .layui-btn{line-height: 30px; height: 30px;}
.eseal-oprbtn .layui-btn.gray-bg,.layui-btn.gray-bg{background:#fff!important; border: 1px solid #DEDEDE;color: #34495E;}
.eseal-oprbtn .layui-btn.green-bg{background:#00AC9F!important; }
.eseal-list.borderlist{box-sizing: border-box; border-radius: 3px;}
.print-lt{position: absolute;right: 10px; top: 10px;}
.print-lt a{margin-left: 10px;color:#34495E;  font-size: 12px;vertical-align: middle;cursor: pointer;line-height: 24px;}
.eseal-defimg{vertical-align: top;width: 90px;height:90px;margin-right: 10px;}
.eseal-listdiv{display:-webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex}
.eseal-list .graymark{width: 48px;height: 48px;background: url(../images/medical/attgray.png);position: absolute;top: 0px;left:0px;z-index: 999;} 	
.eseal-list .graymark.green{width: 46px;height: 45px;background: url(../images/medical/attgreen.png);}
.eseal-list .graymark.yellow{width: 46px;height: 45px;background: url(../images/medical/attyellow.png);}
.eseal-list .graymark.red{width: 46px;height: 45px;background: url(../images/medical/attred.png);}
.eseal-list .graymark	i{top: 16px;left: 9px;transform: rotate(-45deg);font-size: 12px;position: absolute;top: 7px;left: -2px;color: #ffffff;}
.eseal-list .graymark.red i{left: 5px;}
.blue-lt{color:#5C9BE5;text-decoration: underline; }
/*短信设置*/
.order-cell.text-verification{margin: 10px 20px 0 20px;}
.text-verification h3{color:#34495E;font-size: 14px;line-height: 30px;}
.text-verification p,.tong-list{color: #798EA4; font-size: 12px;line-height: 30px;position: relative;}
.text-verification p label{float: right; color:#666; }
.textmessage{text-align: center;padding: 10px 15px;position: relative;background-color: white; margin-bottom: 10px;overflow: hidden;}
.textmessage ul.textmessage-list{margin: 0px auto; width: 730px; overflow: hidden; padding: 20px 0;}
.textmessage-list li{border: 1px solid #EDEDED;  float: left; margin-right: 20px; color: #34495E; font-size: 18px;  border-radius: 5px;width:220px; padding: 50px 0; position: relative;}
.phoneimg{background: url(../images/medical/phone_imgbg.png) no-repeat; width: 380px; height: 719px; margin: 20px auto;background-size: 100% 100%; padding-top: 100px;}
.textmessage .phonetxt{text-align: left;background:#E9E9EC; padding: 10px; margin: 20px 80px 0 40px;border-radius: 10px;}
.welcome-txt{background: #FF6B4F; border-bottom-left-radius: 0px; border-bottom-right-radius: 20px; border-top-left-radius: 20px; border-top-right-radius: 20px;padding: 2px 10px; top:-12px;right:-16px;display:inline-block; font-size:14px; color: #fff; position: absolute; }
.welcome-txt img{vertical-align: top;margin-top: 3px;}
/*短信套餐*/
.verify-term{background:#F6FAFF;position:relative;width: 100%; margin: 10px 0px;padding:20px 0;font-size: 14px;color: #4C5E71;}
.verify-term:before{content:"";display:block;border-width:8px;position:absolute; top:-15px;left:50%; border-color: transparent transparent #ffffff transparent;
        border-style: dashed dashed solid dashed;font-size:0; line-height:0;}
.verify-term:after{top:-14px; border-color: transparent transparent #ffffff transparent;} 
.verify-con{font-size: 12px;color: #666666;border-top: 1px solid #dddddd;padding: 10px 0;line-height: 24px;}
.state-txt{color: #ed4f4c;}	
.success-state{ display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex; position: relative;}			
.success-state .state-txt{color: #0fd69c;}
.progressbar_1{ 
	background-color:#eeeeee; 
    height: 5px; 
    width:80%;    
    color:#222222; 
	border-radius:10px;
     margin: 8px 20px 8px 10px;
} 
.progressbar_1 .bar { 
    background-color:#4A90E2; 
    height:5px; 
	border-radius:10px;
	position: relative;
}
.kindergarten-tit{overflow: hidden;text-overflow: ellipsis;white-space: nowrap;margin: 0 15px 0 10px; width:70px; display:inline-block;}
.success-state.yellow{color:#FD9B5B;}
.success-state.yellow .progressbar_1 .bar{ background-color:#FD9B5B;}

/*微信支付*/
.trade-txt{background:#FDF8F4;font-size: 14px; color:#FE8E44; padding: 5px 10px; display: block;  }
.trade-list{background: #F6F7F9;;font-size: 14px; padding: 10px 10px; display: block; margin-top: 10px; position: relative;}
.pay-list {margin-top: 15px;}
.pay-list li{border: 1px solid #CBD6E1; width: 30%; float: left; line-height:80px; height: 80px; margin-right: 20px; color: #34495E; font-size: 18px;  border-radius: 5px; font-weight: bold; text-align: center; cursor: pointer;}
.pay-list li.current{border: 2px solid #4A90E2; }
.pay-list li img{margin-right: 10px;}
.code-left{padding: 10px; border: 1px solid #DBDBDB; text-align: center;width: 166px;float: left;}
.code-left p{color:#798EA4; margin: 10px 0 0 0;}
.code-left p img{float: left;margin-right: 5px;margin-top: 4px;}
.code-left label{display: block; text-align: left;font-size: 12px;line-height: 17px;}
@media screen and (max-width: 1400px) and (min-width: 1280px){
	.loginContent .loginArea {
		height: 440px;
	}
}
/*食谱管理*/
.info-list{margin: 8px 0 7px -7px;}
.info-list li{display: inline;font-size: 14px;border-right: 1px solid #e1e1e1;padding: 0 10px 0 7px;cursor: pointer;}
.info-list li:last-child{border: none;}
.recipeinfo-cell{ margin: 10px 0;padding: 0 0 10px 0;border-bottom: 1px solid #eeeeee;min-height: 120px;position: relative;}
.recipeinfo-cell:last-child{padding-bottom: 0;border: none;}
.opr-label a{color: #4f94e3;font-size: 14px;text-decoration: underline;margin: 0 5px;}
.recipeinfo-cell .white-tit{width: 210px;white-space: nowrap;text-overflow: ellipsis;-o-text-overflow: ellipsis;overflow: hidden;display: block;}
/*公示模板设置*/
.module-con{font-size: 0;white-space: nowrap;overflow-x: auto;}
.module-cell{text-align: center;display: inline-block;font-size: 14px;margin: 0 20px 0 0;vertical-align: top;}
.module-sub{margin: 0 20px;padding: 15px 0;border-bottom: 1px solid #e7ebf4;position: relative;}
.tab-btn{width: 19px;height: 50px;cursor: pointer;position: absolute;}
.sel-recipe{padding: 13px 15px;}
.sel-recipe.current{background: #eff5f7;}
/*用户管理*/
.energy-panel{width: 150px;height: 36px;line-height: 36px;background: url(../images/recipes/energy_bg.png);background-size: 100% 100%;color: #65a700;font-weight: bold;font-size: 20px;text-align: right;position: absolute;top: 3px;right: 185px;}
.mark-tishi{font-size: 12px;color: #d0051e;background: #f5e5e5;padding: 1px 6px;border-radius: 0 10px 10px 10px;}
.energy-ball{width: 190px;height: 166px;background: url(../images/recipes/energy_ball.png);background-size: 100% 100%;color: #65a700;font-weight: bold;font-size: 30px;margin: 20% auto;}
.energy-con{position: fixed;top: 42px;bottom: 0;left: 241px;right: 0;background: rgba(0,0,0,0.5);z-index: 9;}
/*体检套餐设置*/
.Physical-exam{width:100%;height: 50px;background-color: #fff; line-height: 50px;position: fixed;bottom: 0;box-shadow: 0 5px 5px -4px inset #D3D7DB; color: #34495E;font-size: 15px;}
.checkleft{width: 180px;height:100%; float: left;background-color: #fff;padding: 10px 0 0 15px; /*margin:8px; border-radius: 5px;*/ color: #34495E; }
.checkleft a{color: #34495E;}
/*日历*/
.fc-toolbar{background:#EEF8FC; color:#34495E;}
/*公用样式按钮下一行*/
.btn-next{width: 213px;display: inline-block;}
.btn-nextlt,.btn-nextfl{display: inline-block; vertical-align: top;}
.line-gt{height:37px;}

/*健康宣教*/
.health-list{background: #F7F8FA;position: relative;line-height: 20px; height: 160px;border: 1px solid #F7F8FA;}
.health-list h3{font-size: 16px;color: #34495E;line-height: 25px; padding-top: 10px;}
.health-list h3:hover,.noti-contentlist h5:hover{color: #5195F8;}
.health-list p{font-size: 13px;color: #999;margin-top: 3px;}
.health-list i{color: #798EA4;font-size: 13px;display: inline-block; text-align: right;float: left; margin-right: 10px;}
.health-list label{display: block;}
.health-list i.category-txt{color: #666666;display: inline-block;}
.health-oprbtn{font-size: 14px; text-align: right;position: absolute;right: 10px; bottom: 0px;line-height: 40px;}
.health-oprbtn .a-del{width: auto;}
.health-oprbtn img.a-del {width: 18px;}
.health-list .martop{position: absolute;left: 210px; bottom: 10px;}
.columnlist span{line-height: 35px; height: 35px; font-size: 14px; text-align: center;display: inline-block; margin-left: 7px; padding: 0 20px; cursor: pointer;}
.columnlist {padding: 15px 10px 0 10px;}
.columnlist span.column01{background:  rgba(81, 149, 248, 0.20);  color: #5195F8; border: 1px solid rgba(81, 149, 248, 0.10)}
.columnlist span.column01.current{background:  rgba(81, 149, 248, 1); color: #fff;border: 1px solid rgba(81, 149, 248, 1)}
.columnlist span.column02{background:  rgba(50, 193, 55, 0.20);  color: rgba(50, 193, 55, 1); border: 1px solid rgba(50, 193, 55, 0.10);}
.columnlist span.column02.current{background:  rgba(50, 193, 55, 1); color: #fff;border: 1px solid rgba(50, 193, 55, 1)}
.columnlist span.column03{background:  rgba(118, 146, 247, 0.20);  color: rgba(118, 146, 247, 1); border: 1px solid rgba(118, 146, 247, 0.10);}
.columnlist span.column03.current{background: rgba(118, 146, 247, 1); color: #fff; border: 1px solid rgba(118, 146, 247, 1)}

.columnlist span.column04{background:  rgba(255, 139, 15, 0.20);  color: rgba(255, 139, 15, 1); border: 1px solid rgba(255, 139, 15, 0.10)}
.columnlist span.column04.current{background:  rgba(255, 139, 15, 1); color: #fff;border: 1px solid rgba(255, 139, 15, 1)}

.columnlist span.column05{background:  rgba(255, 66, 78, 0.20);  color:rgba(255, 66, 78, 1); border: 1px solid rgba(255, 66, 78, 0.10)}
.columnlist span.column05.current{background:  rgba(255, 66, 78, 1); color: #fff;border: 1px solid rgba(255, 66, 78, 1)}

.columnlist span.column06{background:  rgba(29, 170, 208, 0.20);  color:rgba(29, 170, 208, 1); border: 1px solid rgba(29, 170, 208, 0.10)}
.columnlist span.column06.current{background:  rgba(29, 170, 208, 1); color: #fff; border: 1px solid rgba(29, 170, 208, 1)}
.health-list.borderlist{box-sizing: border-box; border-radius: 3px; color: #5195F8;}
.print-lt{position: absolute;right: 10px; top: 10px;}
.print-lt a{margin-left: 10px;color:#34495E;  font-size: 12px;vertical-align: middle;cursor: pointer;line-height: 24px;}
.health-defimg{vertical-align: top;width: 200px;height:160px;margin-right: 10px;cursor: pointer;}
.doctor-defimg{vertical-align: top;width: 130px;height:130px;margin-right: 10px;cursor: pointer;}
.doc-defimg{vertical-align: top;width: 131;height:131px;margin-right: 10px; cursor: pointer;}
.health-listdiv{display:-webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;}
.health-listdiv.pad10{padding: 10px;}

.health-listdiv.pad10 h3{padding-top: 0; padding-bottom: 5px;}
.health-list.hedoc{height: 153px;}
.health-listdiv  h3 span{font-size: 14px;color: #34495E; font-weight: normal;}
.health-listdiv  h5 {font-size: 14px;color: #666666;}
.health-listdiv  h5 span{font-size: 12px;color: #FFFFFF;background: #60CABB;border-radius: 2px;display: inline-block;padding: 0px 3px; text-align: center; margin-right: 10px;}
.detail-doc p{font-size: 14px;color: #999999; margin-top: 15px;}
.content-medical.comcolumn{min-width:1200px; height: 700px;margin: 0 8px;}


/*.allcontent-left{color: #34495E;left: 15px; display: inline-block;background: #F7F8FA;width:200px;position: absolute;top:15px;bottom: 15px;}*/
.allcontent-left{color: #34495E; display: inline-block;background: #F7F8FA;width:200px; margin: 15px; position: relative; float: left;}
.allcontentg-txt{background:#F8F6E6; line-height:33px; padding: 0 10px; border-top: 1px solid #E9E9E9; border-bottom: 1px solid #E9E9E9;}
.add-icoimg{ float: right;font-size: 12px;}
.allcontent-left li:hover,.allcontent-left li.hover{background: #5195F8;color: #FFFFFF;}
.allcontent-left li {border-bottom: 1px solid rgba(217, 236, 255, 1);background: rgba(81, 149, 248, 0.10);color: #5195F8; font-size: 14px;cursor: pointer;line-height: 36px; padding-left: 25px; height: 36px; position: relative;}
.allcontent-left li .editim-btn,.hamactivity-list li .editim-btn{position: absolute; right: 10px;top:0;}
.allcontent-left li:hover .editim-btn,.hamactivity-list li:hover .editim-btn{display: block !important; cursor: pointer;}
/*.allcontent-right{height: 100%;display: inline-block;position: absolute;left: 205px;right: 0px;}*/
.allcontent-right{height: 100%;display: inline-block;position: absolute;left: 210px;}
.addfl-btn{position: absolute; bottom: 20px; text-align: center; width: 100%;}
.health-list.current{border: 1px solid RGBA(74, 144, 226, 1); cursor: pointer;}
.health-list i.selico{width: 32px;height: 33px;background: url(../images/healthimg/sel.png);position: absolute;bottom: -1px;right:-10px; cursor: pointer;}
.health-list.current i.selico{width: 32px;height: 33px;background: url(../images/healthimg/selHL.png);position: absolute;bottom: -1px;right:-10px; cursor: pointer;}

/*科室设置*/
.noti-contentlist{background: #F7F8FA;
  margin: 10px 15px;}
.keshi-right{display: block;margin-left: 225px;}
.keshi-left{display: inline-block; width: 45%;}
.addtxt{background: #FFFFFF;border-radius: 2px 2px 2px 2px;font-size: 14px; border: 1px solid #4A90E2;color: #4A90E2; text-align: center; padding: 2px 10px; cursor: pointer;}
.offices-setup{background: rgba(41,211,198,0.05);
border-radius: 2px; padding: 10px; position: relative;overflow: hidden; margin-bottom: 10px;}
.offices-setup.bluebg{background: rgba(74,144,226,0.05);}
.offices-setup.redbg{background: rgba(255,122,144,0.05);}
.offices-setup.yellowbg{background: rgba(255,197,81,0.05);}
.offices-setup.pinkbg{background: rgba(255,149,97,0.05);}
.offices-setup.greenbg{background: rgba(101,217,144,0.05);}
.offices-setup.lgbluebg{background: rgba(131,141,246,0.05);}

.offices-list{margin: 0 80px;position: relative; padding: 5px 0px;color:#798EA4;font-size: 14px; }
.offices-list .layui-form-label{padding: 5px 0; line-height: normal;color: #666666; text-align: right; width: 110px; }
.offices-list .layui-input-block{ color:#34495E;line-height: 30px;  }
.layui-tit{font-weight: bold;color: #34495E; text-align: center; padding-left: 68px;line-height: 30px;}
.mar-left{ margin-left: 20px; display: inline-block;}
.a-del{width: 18px; margin-left: 15px;}
.offices-imgbg{width: 60px; position: absolute;  top: 10px; left: 10px; background: #62C0B6; height: 112px; display: flex; align-items: center;}
.offices-imgbg span { color: #fff; display: block; width: 15px;  margin: 0 auto;}

.offices-setup.bluebg .offices-imgbg{background: #4192ED;}
.offices-setup.redbg .offices-imgbg{background: #FF7A90;}
.offices-setup.yellowbg .offices-imgbg{background: #FFA41C;}
.offices-setup.pinkbg .offices-imgbg{background: #FF5500;}
.offices-setup.greenbg .offices-imgbg{background: #33C96B;}
.offices-setup.lgbluebg .offices-imgbg{background: #5E69DF;}
.colbg{width: 30px;height: 30px;background: #62C0B6;border-radius: 2px 2px 2px 2px; display: block; margin-right: 5px;float: left;text-align: center;
  color: #fff;
  line-height: 30px;
cursor: pointer;}
/*号源类型设置*/
.info-cell{background: rgba(74,144,226,0.05);border-radius: 2px 2px 2px 2px;color: #34495E; padding: 13px 15px;font-size: 14px; position: relative;}
.good-sel-rt .a-del{margin-left: 5px; cursor: pointer;}
.good-sel-rt{position: absolute; top:13px; right:15px;}
.reservation-list{ padding:5px 0; margin-bottom: 30px; overflow: hidden;}
.reservation-list li{ margin-bottom:10px; text-align: center;display: block;border-radius:10px;border: 1px solid #F5F7F9;background: #F5F7F9;color: #939599; width: 30%; float:left; margin-right: 10px; line-height: 20px; padding:5px 0;} 
.reservation-list label{display: block;}
.reservation-list li.current{background: rgba(81,149,248,0.1);
border-radius: 10px;
border: 1px solid #4A90E2;}
/*儿童档案*/
.health-img{border-radius: 0px 0px 0px 0px;
border: 1px solid #CBD6E1;}
.health-img p img{ width: 857px;
height: 420px; padding: 10px; }
.health-btn{ width: 100%; display: block;  text-align: center; padding: 10px 0; border-top:1px solid #CBD6E1; height: 25px;}
.health-btn img{ }
.health-btn span{width: 24px;
height: 24px;
background: #FFFFFF;
	color: #CBD6E1;
border-radius: 12px;
border: 1px solid #CBD6E1; display:inline-block; margin-right: 10px; line-height: 24px; cursor: pointer;}
.health-btn span.current{
background: #4A90E2;
color: #FFFFFF;
border: 1px solid #4A90E2;}
.layui-btn.seedit{line-height: 24px; height: 24px; border-radius: 0;padding: 0 10px;}
.scan-sign{font-size: 14px;color: #4A90E2;}
.datatxt{width: 21px; height: 21px; vertical-align: top; margin-left: 1px; vertical-align: top; margin-top: 7px; cursor: pointer;}
.layui-table.borrgt  th, .layui-table.borrgt td {
	border-right: none; line-height: normal;}
.layui-tablelist  th, .layui-tablelist td { line-height: normal;}
.bluelt{color: #C0C8D1;}
.layui-tablelist{  overflow-x: auto; /* 水平滚动条 */
white-space: nowrap;display: flex;}
.layui-tablelist table {
	width: 200px;
	display: inline;
}

.layui-table.current{background: #EEF6FF;}
.entering-list span{line-height: 37px;}
.entering-list{display: flex; justify-content: center;}
.entering-list .layui-input{ float: left; }
.entering-list  .layui-form-select .layui-input{width: 80px; height: 26px; line-height: 26px; }
.entering-list .layui-form-select .layui-edge{top: 10px}

.entering-list .layui-input,.layui-table.bluebg .layui-input{background-color:transparent;}
.layui-table.bluebg td{background: #EEF6FF;}
@media screen and (min-width:1000px) and (max-width:1400px){
	.line-gt{height: 42px;}
	.btn-nextfl{margin-bottom: 5px;}
	}


/*左右距离*/
.layui-tab-brief.tab-tit{padding: 0px 10px 0 0;
margin-top:0px;}
