<!DOCTYPE html>
<html>
<head>
    <title>通知公告</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <link rel="stylesheet" href="../css/reset.css"/>
    <link rel="stylesheet" href="../css/style.css"/>
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../plugin/ueditor/third-party/webuploader/webuploader.css"/>
    <link rel="stylesheet" href="../styles/xbcomstyle.css">
    <link rel="stylesheet" href="../css/icon.css"/>
	<link rel="stylesheet" href="../css/medical.css">
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>

    <!--[if lt IE 9]>
    <script src='sys/html5shiv.min.js'></script>
    <![endif]-->
    <style>
        body {
            background: #F4F5F7;
        }

        .layui-btn-primary {
            background-color: #EDEFF2;
        }

        #js-cover input, #js-attache input {
            opacity: 0;
            filter: alpha(opacity=0);
            cursor: pointer;
            display: none;
        }

        #js-saved .js-read-info label {
            display: none;
        }

        #js-sent .js-read-info .btnlb, #js-receive .js-read-info .btnlb {
            display: none;
        }

        #js-receive .qr-delay-enroll-time, #js-saved .qr-delay-enroll-time {
            display: none;
        }

        #js-receive .qr-delay-enroll-shenroll, #js-saved .qr-delay-enroll-shenroll {
            display: none;
        }

        #js-sent .noti-content h3, #js-saved .noti-content h3 {
            padding: 0 15px;
        }

        .layui-form-label {
            padding-bottom: 0;
            width: 180px;
            line-height: 30px;
            display: block;
            float: none;
            text-align: left;
        }

        .layui-form .layui-form-item {
            margin-bottom: 15px;
        }

        .layui-input-block {
            margin-left: 0px;
        }

        .layui-input-inline {
            margin-left: 0px;
            margin-top: 5px;
        }

        .layui-textarea {
            width: 95%;
        }

        .layui-form-item .layui-input-inline {
            width: auto;
        }

        .layui-form-select .layui-edge {
            border-top-color: transparent;
            right: 5px;
            top: 35%;
        }

        .grid-demo {
            margin-left: 30px;
        }

        .layui-input, .layui-textarea, .layui-select {
            height: 36px;
        }

        .layui-btn {
            background: #fff !important;
            border: 1px solid #4A90E2;
            color: #4A90E2;
        }

        .layui-btn:hover {
            color: #4A90E2;
        }

        .layui-form-radioed, .layui-form-radioed > i, .layui-form-radio:hover * {
            color: #333333;
        }

        .edui-default .edui-editor {
            border: none !important;
        }

        .webuploader-pick {
            background: #fff !important;
            border: 1px solid #4A90E2;
            color: #4A90E2;
        }

        .webuploader-pick {
            color: #4A90E2 !important;
            width: 88px;
        }

        .webuploader-pick a {
            color: #4A90E2 !important;
        }

        .webuploader-container {
            margin-top: 0px;
        }

        .layui-table thead tr, .layui-table-header {
            background: #F8F8F8;
            color: #333333;
        }

        .layui-tab {
            background: #fff;
        }

        .layui-tab-brief > .layui-tab-more li.layui-this::after, .layui-tab-brief > .layui-tab-title .layui-this::after {
            border-bottom: none;
        }

        .layui-tab-title {
            border-bottom: none;
            width: 500px;
        }

        .qr-show-detail h5 {
            font-size: 16px;
            color: #333;
            font-weight: bold;
            line-height: 30px;
            height: 30px;
        }

        .noti-content {
            margin: 15px 20px 0px 20px;
        }

        .select-txt {
            padding: 0 10px;
            color: #4A90E2;
            margin-right: 10px;
            line-height: 32px;
            font-size: 12px;
            height: 32px;
            background: #ECF5FF;
            border-radius: 2px;
            border: 1px solid #D9ECFF;
            display: inline-block;
            margin-bottom: 5px;
            vertical-align: top;
        }
        .txtweight{
            display: inline-block;
        }
    </style>
</head>
<body>
<!--新建通知公告-->
<section style="">
    <div id="new-notice" class="layui-row"
         style="width:80%;margin:0 auto;background: #fff; border-top-left-radius: 12px;border-top-right-radius:12px;">
        <!--        <h3 style="border-bottom: 1px solid #E6E6E6; color: #4A90E2;  padding:13px  20px; ">新建公告</h3>-->
        <div class="layui-col-xs10 layui-col-sm10 layui-col-md10  bg6">
            <div class="layui-row grid-demo">
                <div class="layui-col-xs10 layui-col-sm10" style="width: 100%;">
                    <div class="site-text site-block">
                        <form class="layui-form js-to-type" action="">
                            <div class="layui-form-item">
                                <label class="layui-form-label"><i class="layui-mark-red">*</i>群发对象:</label>
                                <div class="layui-input-inline" style="width:480px; z-index: 1001;">
                                    <select id="sel_totype" lay-filter="sel_totype" name="city" lay-verify="required">
                                        <option value=""></option>
                                        <option value="ey">辖区机构</option>
                                    </select>
                                </div>
                                <input type="button" class="layui-btn js-choose-user blockbg" value="选择"
                                       style="margin-top: 5px;height: 36px;width: 88px;">
                                <span id="spselareainfo" style="display: inline-block;vertical-align: middle;"></span>
                            </div>
                            <div id="divareainfo"></div>
                            <div id="btnmoredetail" style="color: blue;display: none;">详情</div>
                        </form>
                        <form class="layui-form" action="">
                            <div id="notice-type-isnshow" class="layui-form-item">
                                <label class="layui-form-label"><i class="layui-mark-red">*</i>通知类型:</label>
                                <div class="layui-input-inline">
                                    <input id="txt-js-notice-type" type="text" readonly="readonly" required="" lay-verify="required"
                                           placeholder="" autocomplete="off" class="layui-input"
                                           style="width:480px;">
                                </div>
                                <input type="button" class="layui-btn js-notice-type blockbg" value="选择类型" style="margin-top: 5px;height: 36px;width: 88px;"><label><b id="b-citynoticetype" style="color: red;"></b></label>
                                <!--    <a class="layui-form-mid layui-word-aux js-notice-type" style="color: #3aa6e4  !important;text-decoration: underline;margin-top: 14px;">选择类型</a>-->
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label"><i class="layui-mark-red">*</i>标题:</label>
                                <div class="layui-input-block" style="padding-top:5px;">
                                    <input type="text" name="title" required="" lay-verify="required"
                                           placeholder="50字以内..." autocomplete="off" class="layui-input js-title">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label"><i class="layui-mark-red">*</i>正文内容:</label>
                                <div class="layui-input-block" style="margin-top: 5px;">
                                    <div id="js-editor" style="border: 1px solid #e6e6e6; height: 258px;"></div>
                                    <div class="js-attache-list">
                                        <!--                                        <div class="layui-col-xs6 layui-col-sm6 layui-col-md6"></div>-->
                                        <!--                                        <div class="layui-col-xs6 layui-col-sm6 layui-col-md6"></div>-->
                                        <div id="js-attache" class="heightnum"
                                             style="position:relative;padding: 10px 0 0 0;"><img
                                                src="../images/add_icolt.png" style="float: left;margin-top: 4px;"><a
                                                style="float: left;line-height: 23px;">上传附件</a></div>
                                        <div style="line-height: 24px;margin-left: 10px; color: #999999; width: 200px;display: inline-block;">
                                            单个文件上传不要超过200MB
                                        </div>
                                    </div>
                                    <div id="filllist"></div>
                                </div>
                            </div>
                            <div id="noticecontent"></div>
<!--                            <div id="divisnaddexpert" class="layui-form-item" style="display: none;">-->
<!--                                <label class="layui-form-label" style="padding-left: 0;"><i-->
<!--                                        class="layui-mark-red">*</i>是否添加专家:</label>-->
<!--                                <div class="layui-input-inline qr-expert">-->
<!--                                    <input class="radisnaddexpert" name="qr-expert" type="radio" checked="checked"-->
<!--                                           value="0" title="否">-->
<!--                                    <input class="radisnaddexpert" name="qr-expert" type="radio" value="1"-->
<!--                                           title="是">-->
<!--                                </div>-->
<!--                            </div>-->
<!--                            <div id="divenrollinfo" style="display: none;">-->
<!--                                <div id="divselexpert" class="layui-form-item expert-is" style="display: none;">-->
<!--                                    <label class="layui-form-label" style="padding-left: 0;"><i-->
<!--                                            class="layui-mark-red">*</i>专家:</label>-->
<!--                                    <div id="divexpertinfo">-->
<!--                                    </div>-->
<!--                                    <input id="btnselexpert" type="button" class="layui-btn js-choose-expert blockbg"-->
<!--                                           value="选择" style="margin-top: 5px;height: 36px;width: 88px;">-->
<!--                                </div>-->

<!--                                <div class="layui-form-item expert-is" style="display: none;">-->
<!--                                    <label class="layui-form-label" style="padding-left: 0;"><i-->
<!--                                            class="layui-mark-red">*</i>讲座时间:</label>-->
<!--                                    <div class="layui-input-inline" style="margin-top: 5px;width: 200px;">-->
<!--                                        <input id="txtlecturestarttime"-->
<!--                                               class="layui-input gray-select cal_ico txtweight"-->
<!--                                               type="text" require="true" datatype="Date" format="ymdhms"-->
<!--                                               msg="请选择讲座开始时间"-->
<!--                                               style="width: 200px;"/>- -->
<!--                                        <input id="txtlectureendtime" class="Wdate txtweight" type="text" require="true"-->
<!--                                               datatype="Date" format="ymdhms" msg="请选择讲座结束时间" style="width: 160px;"/>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                                <div class="layui-form-item expert-is" style="display: none;">-->
<!--                                    <label class="layui-form-label" style="padding-left: 0;"><i-->
<!--                                            class="layui-mark-red">*</i>讲座地点:</label>-->
<!--                                    <div class="layui-input-inline" style="margin-top: 5px;width: 200px;">-->
<!--                                        <input id="txtlectureaddress" type="text" name="title" required=""-->
<!--                                               lay-verify="required"-->
<!--                                               placeholder="请输入讲座地点" autocomplete="off" class="layui-input">-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                                <div class="layui-form-item expert-is">-->
<!--                                    <label class="layui-form-label" style="padding-left: 0;"><i-->
<!--                                            class="layui-mark-red">*</i>是否允许报名:</label>-->
<!--                                    <div class="layui-input-inline qr-enroll">-->
<!--                                        <input class="radqrenroll" name="qr-enroll" type="radio" checked="checked"-->
<!--                                               value="0"-->
<!--                                               title="不允许">-->
<!--                                        <input class="radqrenroll" name="qr-enroll" type="radio" value="1" title="允许">-->
<!--                                    </div>-->
<!--                                    <div>-->
<!--                                        <div class="layui-form-label" style="padding-left: 0;"><span-->
<!--                                                style="display: inline-block;float: left;margin-top: 7px;"><i-->
<!--                                                class="layui-mark-red">*</i>请选择截止报名日期：</span>-->
<!--                                            <div class="layui-input-inline" style="margin-top: 5px;width: 200px;">-->
<!--                                                <input id="txtendtime" class="layui-input gray-select cal_ico txtweight"-->
<!--                                                       type="text" require="true" datatype="Date" format="ymdhms"-->
<!--                                                       msg="请正确输入活动结束时间" style="width: 200px;"/>-->
<!--                                                &lt;!&ndash;                                        <input id="txtendtime" class="Wdate txtweight" type="text" require="true" datatype="Date" format="ymdhms" msg="请正确输入活动结束时间" style="width: 160px;"/>&ndash;&gt;-->
<!--                                            </div>-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                                <div class="layui-form-item" id="divisnneedinfo" style="display: none;">-->
<!--                                    <label class="layui-form-label" style="padding-left: 0;"><i-->
<!--                                            class="layui-mark-red">*</i>报名是否需要提交资料:</label>-->
<!--                                    <div class="layui-input-inline qr-info">-->
<!--                                        <input name="qr-info" type="radio" checked="checked" value="0" title="不需要">-->
<!--                                        <input name="qr-info" type="radio" value="1" title="需要">-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                                <div class="layui-form-item" id="divneedinfos" style="display: none; margin-bottom: 0;">-->
<!--                                    <label class="layui-form-label" style="padding-left: 0;"><i-->
<!--                                            class="layui-mark-red">*</i>报名需要提交的资料:</label>-->
<!--                                    <span id="selneedinfos" class="layui-btn layui-btn-mini"-->
<!--                                          style="margin: 5px 0 0 0px; width: 88px;">选择</span>-->
<!--                                    &lt;!&ndash;                                <button style="height: 30px;line-height: 30px;width: 100px;margin: 15px 0 0 10px;" class="layui-btn bluebtn" id="selneedinfos" >选择</button>&ndash;&gt;-->
<!--                                </div>-->
<!--                                <div class="layui-form-item">-->
<!--                                    <label class="layui-form-label" style="padding-left: 0;width: auto;">-->
<!--                                        <div style="display:block; text-align: left;text-align: left;line-height: 25px;"-->
<!--                                             id="divseledneedinfos" class="layui-input-block"></div>-->
<!--                                    </label>-->
<!--                                </div>-->
<!--                                <div class="layui-form-item" id="divisnprocess" style="display: none; ">-->
<!--                                    <label class="layui-form-label" style="padding-left: 0;"><i-->
<!--                                            class="layui-mark-red">*</i>报名资料是否需要自己审核:</label>-->
<!--                                    <div class="layui-input-inline qr-process">-->
<!--                                        <input name="qr-process" type="radio" value="0" title="不需要">-->
<!--                                        <input name="qr-process" type="radio" checked="checked" value="1" title="需要">-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                            <div id="divtreattype" style="display:none;">-->
<!--                                <div class="layui-form-item expert-is" style="display: none;">-->
<!--                                    <label class="layui-form-label" style="padding-left: 0;"><i-->
<!--                                            class="layui-mark-red">*</i>坐诊时间:</label>-->
<!--                                    <div class="layui-input-inline" style="margin-top: 5px;width: 200px;">-->
<!--                                        <input id="txtsittingtimestart"-->
<!--                                               class="layui-input gray-select cal_ico txtweight"-->
<!--                                               type="text" require="true" datatype="Date" format="ymdhms"-->
<!--                                               msg="请选择坐诊开始时间" style="width: 200px;"/>- -->
<!--                                        <input id="txtsittingtimeend" class="Wdate txtweight" type="text" require="true"-->
<!--                                               datatype="Date" format="ymdhms" msg="请选择坐诊结束时间" style="width: 160px;"/>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                                <div id="divdepartment" class="layui-form-item expert-is" style="display: none;">-->
<!--                                    <label class="layui-form-label" style="padding-left: 0;"><i-->
<!--                                            class="layui-mark-red">*</i>所属科室:</label>-->
<!--                                    <div id="divdepartmentidinfo">-->
<!--                                    </div>-->
<!--                                    <input id="btndepartmentid" type="button"-->
<!--                                           class="layui-btn js-choose-department blockbg" value="选择"-->
<!--                                           style="margin-top: 5px;height: 36px;width: 88px;">-->
<!--                                </div>-->
<!--                                <div class="layui-form-item expert-is" style="display: none;">-->
<!--                                    <label class="layui-form-label" style="padding-left: 0;"><i-->
<!--                                            class="layui-mark-red">*</i>是否允许预约挂号:</label>-->
<!--                                    <div class="layui-input-inline qr-enroll">-->
<!--                                        <input class="radisnregister" name="qr-enroll" type="radio" checked="checked"-->
<!--                                               value="0"-->
<!--                                               title="不允许">-->
<!--                                        <input class="radisnregister" name="qr-enroll" type="radio" value="1"-->
<!--                                               title="允许">-->
<!--                                    </div>-->
<!--                                    <div>-->
<!--                                        <div class="layui-form-label" style="padding-left: 0;"><span-->
<!--                                                style="display: inline-block;float: left;margin-top: 7px;"><i-->
<!--                                                class="layui-mark-red">*</i>请选择截止预约挂号时间：</span>-->
<!--                                            <div class="layui-input-inline" style="margin-top: 5px;width: 200px;">-->
<!--                                                <input id="txtregisterendtime"-->
<!--                                                       class="layui-input gray-select cal_ico txtweight"-->
<!--                                                       type="text" require="true" datatype="Date" format="ymdhms"-->
<!--                                                       msg="请选择截止预约挂号时间" style="width: 200px;"/>-->
<!--                                            </div>-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
                            <div id="divsenddepart" class="layui-form-item">
                                <label class="layui-form-label"><i class="layui-mark-red">*</i>发文单位:</label>
                                <div class="layui-input-block" style="padding-top: 5px;">
                                    <input type="text" name="title" required="" lay-verify="required"
                                           placeholder="30字以内..." autocomplete="off" class="layui-input js-from-depart"
                                           maxlength="30" style="width: 95%;">
                                </div>
                            </div>
                            <div id="divcover" class="layui-form-item">
                                <label class="layui-form-label">封面:</label>
                                <div class="layui-input-block">
                                    <div><img src="../images/notice_cover.jpg"
                                              style="width:180px;height: 180px;border: 1px solid #EDEDED;"> <span
                                            id="js-cover" class="layui-btn"
                                            style="position:relative;vertical-align: middle; line-height: 37px;height: 37px;">上传封面</span>
                                        <span style="display: inline-block; vertical-align: middle; color:#999999; ">提示：图片类型为jpg,png</span>
                                    </div>
                                </div>
                            </div>
                            <div id="divsummary" class="layui-form-item layui-form-text">
                                <div class="layui-form-label">
                                    <label> 摘要: </label>
                                </div>
                                <div class="layui-input-block" style="padding-top: 5px;">
                                    <textarea name="desc" placeholder="选填(100字以内)，如果不填写会默认抓取正文前54个字"
                                              class="layui-textarea js-summary" style="padding-top: 10px;"></textarea>
                                    <span class="layui-btn layui-btn-mini qr-get-summary"
                                          style="margin-top: 10px;  background: #fff !important;margin-top: 10px;height: 36px; line-height: 35px; width: 88px;border: 1px solid #4A90E2;color: #4A90E2;">获取摘要</span>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="layui-form-item"
                 style="margin: 10px 0; text-align: center; border-top: 1px solid #E6E6E6; padding-top: 10px;">
                <div class="layui-input-block">
                    <button id="btnforward" class="layui-btn btnoperate" lay-submit="" lay-filter="formforward"
                            style="background:#4A90E2 !important; color: #fff !important;width: 140px; height: 48px; line-height: 48px;display: none;">
                        转发
                    </button>
                    <button class="layui-btn bluebtn btnoperate" lay-submit="" lay-filter="formDemo"
                            style="background:#4A90E2 !important; color: #fff !important;width: 140px; height: 48px; line-height: 48px;">
                        发布
                    </button>
                    <button class="layui-btn yellowbtn btnoperate" lay-submit="" lay-filter="formDemo2"
                            style="background:#4A90E2 !important; color: #fff !important;width: 140px; height: 48px; line-height: 48px;">
                        保存草稿
                    </button>
                    <button id="btncancel" class="layui-btn yellowbtn"
                            style="background:#4A90E2 !important; color: #fff !important;width: 140px; height: 48px; line-height: 48px;">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<!--选择通知公告类型-->
<section id="notice-type-list" style="margin: 10px;display: none;">
    <div style="text-align: left;">
        <button class="layui-btn layui-btn-small js-add-type" style="background:#4A90E2 !important; color: #fff; ">
            新增类型
        </button>
    </div>
    <table class="layui-table">
        <colgroup>
            <col width="150">
            <col width="200">
            <col>
        </colgroup>
        <thead>
        <tr>
            <th style="width: 17.5%;text-align: center;">选择</th>
            <th style="width: 47.5%;text-align: center;">类型名称</th>
            <th style="width: 35%;text-align: center;">操作</th>
        </tr>
        </thead>
        <tbody class="js-body">
        </tbody>
    </table>
</section>

<!--添加通知公告类型-->
<section id="add-notice-type" style="margin: 0px 10px;display: none;">
    <form class="layui-form" action="">
        <div class="layui-form-item" style="width: 450px;margin: auto;">
            <label class="layui-form-label" style="height: 35px;line-height: 30px;"><i class="layui-mark-red">*</i>类型名称</label>
            <div class="layui-input-inline" style="margin-top: 4px;width: 100%;">
                <input id="txtnoticetypename" type="text" required="" lay-verify="required" placeholder="10字以内"
                       autocomplete="off" class="layui-input">
            </div>
        </div>
    </form>
</section>
<script>
    var v = top.version;
    document.write('<script type="text/javascript" src="../sys/jquery.js?v=' + v + '"><' + '/script>');
    document.write('<script type="text/javascript" src="../sys/arg.js?v=' + v + '"><' + '/script>');
    document.write('<script type="text/javascript" src="../sys/uploadutil.js?v=' + v + '"><' + '/script>');
    document.write('<script type="text/javascript" src="../layui-btkj/layui.js?v=' + v + '"><' + '/script>');
    document.write('<script type="text/javascript" src="../plugin/ueditor/third-party/webuploader/webuploader.min.js?v=' + v + '"><' + '/script>');
    document.write('<script type="text/javascript" charset="utf-8" src="../plugin/ueditor/ueditor.config.js?v=' + v + '"><' + '/script>');
    document.write('<script type="text/javascript" charset="utf-8" src="../plugin/ueditor/ueditor.all.min.js"?v=' + v + '"><' + '/script>');
    document.write('<script type="text/javascript" src="../js/noticeadd.js?v=' + v + '"><' + '/script>');
</script>
</body>
</html>
