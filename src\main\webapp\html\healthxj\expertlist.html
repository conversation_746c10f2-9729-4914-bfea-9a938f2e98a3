﻿<!DOCTYPE html>
<html style="overflow: hidden;">
<head>
    <meta charset="utf-8" />
    <title>专家名医-自建库</title>
    <link rel="stylesheet" href="../../css/reset.css" />
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" type="text/css" href="../../css/icon.css" />
    <link rel="stylesheet" type="text/css" href="../../css/style.css" />
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css" />
    <link rel="stylesheet" href="../../css/medical.css">
    <style>
        html, body { background: #EAEFF3; }
    </style>
</head>
<body>
    <div class="layui-page">
        <div class="layui-form divsearch content-medical" style="padding: 10px;">
            <div class="layui-form-item">
                <div class="def-search" style="display: inline-block; vertical-align: top; margin: 0px 10px 0 10px;">
                    <select id="seltype">
                        <option value="">请选择</option>
                        <option value="1">讲座型中医儿科专家</option>
                        <option value="2">治疗型中医儿科专家</option>
                    </select>
                </div>
                <div class="def-search" style="display: inline-block; vertical-align: top; margin: 0px 10px 0 10px;">
                    <input type="text" id="txtkey" placeholder="请输入医生姓名" class="layui-input" style="width: 200px; float: left; margin-right: 10px;">
                </div>
                <div class="def-search" style="display: inline-block; vertical-align: top; margin: 0px 10px 0 10px;">
                    <button id="btnsearch" class="layui-btn form-search">查询</button>
                </div>
            </div>
        </div>
        <div class="content-medical comcolumn" style="margin: 5px 0;" id="divcontent">
            <div class="columnlist" style="padding: 20px 10px 0 20px;">
                <button id="btnadd" class="layui-btn form-search" style="background: #00AC9F !important;">添加专家名医</button>
            </div>
            <div class="layui-row layui-col-space10" style="margin: 10px 15px;overflow: auto;" id="divlist" >
            </div>
            <div id="laypage" style="position: absolute; right: 30px;"></div>
        </div>
    </div>
    <script data-main="../../js/healthxj/expertlist" src="../../sys/require.min.js"></script>
</body>
</html>
