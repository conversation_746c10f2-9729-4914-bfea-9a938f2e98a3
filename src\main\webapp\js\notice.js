/**
 * 通知公告
 * 分为5类，通过参数type判断，（type=1 && comefrom=ypt）为市级普通通知，（type=1 && comefrom=fuyou）为区县普通通知，2.培训通知，3专家讲座通知，4专家坐诊通知。1，2市妇幼、区县、医院端都可以发送，存储在市端，（type=1 && comefrom=fuyou），3，4只有区县可以发送，存储在区县端
 *
 */
layui.config({
    base: './js/'
}).extend({ //设定模块别名
    system: '../../sys/system',
    promise: '../../plugin/promise/promise'
});
var objdata = {
    myid: parent.objdata.my.id,
    myPhone: parent.objdata.my.mobile,
    noticeCache: { //缓存查询过的通知公告
        sent: [],
        saved: [],
        receive: []
    },
    pageConf: { //分页总配置
        pageNum: 10 //每页条数
    },
    strSearch: "" //搜索关键字
    , sendnotice: {}//已发送通知
    , receivenotice: {}//已接收的通知
    , totypename: {"e": "医疗保健机构", "y": "托幼机构", "ey": "医疗保健机构名称/托幼机构", 'p': "小程序家长端","employee": "小程序教职工端", "doctor": "小程序医生端"}
    , getdatafromypt: 0//读取存储在市级的通知
}, laydate;
layui.use(['system', 'promise', 'form', 'laydate'], function () {
    form = layui.form;
    laydate = layui.laydate;
    if(Arg("type") == 2 || (Arg("comefrom") && Arg("comefrom") == 'ypt' && Arg("type") == 1)){//2培训通知,1市级普通通知  Arg("comefrom") == 'ypt'代表接收的市妇幼通知
        objdata.getdatafromypt = 1;
        parent.getnoticenum(0);//处理接收通知个数显示
        if(Arg("type") == 2){
            if(parent.istuoyou()){//如果是托幼
                $("#addbtn").remove();
            } else {
                $("#addbtn,.li-tab").show();
            }
        } else {
            $("#addbtn").remove();
            $(".li-tab").hide();
        }
    } else if(Arg("type") == 1 || Arg("type") == 3 || Arg("type") == 4){//1普通通知,3专家讲座，4专家坐诊
        $("#addbtn,.li-tab").show();
        $("#li-received").hide();
        if(Arg("type") == 1 && parent.ishospital()){//普通通知，且是医院机构登录才有已接收的通知
            $("#li-received").show();
        } else {
            parent.getnoticenum(0);//处理接收通知个数显示
        }
    }
    initEvent();
    objdata.enrollBtn = undefined;
    // getweidunum();
});
//UI模板
var templete = {
        noticeListTem: '<div class="noti-content" data-id="{{id}}" data-sendtype="{{sendtype}}" data-totype="{{totype}}">' +
	        '{{statusIconTem}}'+
            '<dl style="" class="qr-show-detail">' +
            '<dt><img src="{{cover1}}" style="width:180px;" class="qr-show-detail"></dt>' +
            '<dd style="">' +
            '<h5 style="cursor: pointer;" class="checknotice"><span class="notice-title">{{title}}</span><span class="notice-label noti-tit-right" style="margin: 0;height:auto;"><span>{{typename}}</span></span></h5>' +
            '<span class="qr-show-detail" style="padding: 10px 0;display: block;">{{summary}}</span>' +
            '<p style="font-size: 12px;">' +
            '   <span style="display: {{attShow}};margin-right:15px"><img src="../images/association_ico.png" style="margin-right: 6px;">{{attachelen}}</span>' +
            // '   <label class="lb-enrollstate" style="display: {{enrollNumShow}};cursor: pointer;">\n' +
            // '       <img src="../images/nonread.png" style="width: 18px;vertical-align: top;margin-top: 11px;">\n' +
            // '       <span class="b-number">{{enrollnum}}</span>人报名' +
            // '   </label>\n' +
            '   <label style="display: {{enrollShow}}; color: {{enrollColor}}"><span>{{enrollStatus}}</span></label>' + //本人报名信息
            '{{noticeenrolltime}}' +
            // '   <label class="qr-about-read" data-readstatus="1" style="margin: 0 15px;cursor: pointer;display: {{enrollreadShow}};" data-num="{{readernum}}"><img src="../images/nread.png" style="width: 18px;vertical-align: top;margin-top: 11px;margin-right: 5px">{{readernum}}个已读</label>\n' +
            // '   <label class="qr-about-read" data-readstatus="0" data-num="{{unreadNum}}" style="display: {{enrollnotreadShow}};cursor: pointer;"><img src="../images/nonread.png" style="width: 18px;vertical-align: top;margin-top: 11px;margin-right: 5px"><i class="layui-mark-red">{{unreadNum}}</i>个未读</label>\n' +
            '   <span style="display: {{noticebtn}}" class="enroll-label noti-tit-right">' +
            '       <label class="layui-btn layui-btn-mini qr-print-code" style="height: 24px;line-height: 24px;margin-left:10px;color:#fff !important; background:#4A90E2 !important; padding: 0 10px; font-size: 12px;display: {{printcodeshow}}">打印二维码</label><label class="layui-btn layui-btn-mini qr-about-read" style="height: 24px;line-height: 24px;margin-left:10px;color:#fff !important; background:#4A90E2 !important; padding: 0 10px; font-size: 12px;">阅读记录&nbsp; &nbsp;{{readernum}}|{{unreadNum}}</label><label class="layui-btn layui-btn-mini lb-enrollstate" style="height: 24px;line-height: 24px;margin-left:10px;color:#fff !important; background:#4A90E2 !important; padding: 0 10px; font-size: 12px;display:{{enrollNumShow}}">报名情况&nbsp; &nbsp;{{enrollnum}}</label><label class="layui-btn layui-btn-mini lb-signstate" style="height: 24px;line-height: 24px;margin-left:10px;color:#fff !important; background:#4A90E2 !important; padding: 0 10px; font-size: 12px;display:{{wxsignNumShow}}">签到情况&nbsp; &nbsp;{{wxsignnum}}</label>' +
            '       <label class="layui-btn layui-btn-mini qr-delay-enroll-forward" style="height: 24px;line-height: 24px;margin-left:10px;color:#fff !important; background:#4A90E2 !important; padding: 0 10px; font-size: 12px;display: {{forwardshow}}">转发</label><label class="layui-btn layui-btn-mini qr-delay-enroll-commitname" style="height: 24px;line-height: 24px;margin-left:10px;color:#fff !important; background:#4A90E2 !important; padding: 0 10px; font-size: 12px;display: {{subnoticeusers}}">提交报名名单</label><label class="layui-btn layui-btn-mini qr-delay-enroll-shenroll" style="height: 24px;line-height: 24px;margin-left:10px;color:#fff !important; background:#4A90E2 !important; padding: 0 10px; font-size: 12px;display: {{enrollsheshow}}">{{btncheckenrollname}}</label><label class="layui-btn layui-btn-mini qr-delay-enroll-srcnotice" style="height: 24px;line-height: 24px;margin-left:10px;color:#fff !important; background:#4A90E2 !important; padding: 0 10px; font-size: 12px;display: {{srcnoticeshow}}">查看原通知</label>' +
            '   </span>' +
            '</p>' +
            '<p style="border-top: 1px solid #E6E6E6;">' +
            '<span>发布机构: <label class="layui-color-gary2">{{fromdepart}}</label></span>' +
            '<span style="margin-left: 15px;">群发对象: <label class="layui-color-gary2">{{totypename}}</label></span>' +
            '<span style="margin-left: 15px;">发布人： <label class="layui-color-gary2">{{userName}}</label></span>' +
            '<span style="margin-left: 15px;">发布时间： <label class="layui-color-gary2">{{publishtime1}}</label></span>' +
            '<label style="margin-left: 15px;display: none;">接收人群</label>' +
            '<label class="qr-to-user-list layui-color-gary2" style="display: none;"></label>' +
            '<span class="js-read-info" style="float: right;">' +
            '   <label style=" display: inline-block;color: #4A90E2" class="btnlb notice-edit-label qr-edit"><img class="" src="../images/addimg.png">编辑</label>' +
            '   <label style="float: right; display: inline-block" class="btnlb notice-del-label qr-del"><img src="../images/del_icon.png">删除</label>' +
            '   <label style="float: right; display: {{showOrHide}}" class="notice-del-label a-del"><img src="../images/del_icon.png" >删除</label>' +
            '</span></p>' +
            '</dd>' +
            '</dl>' +
            '</div>',
        noContent: '<div style="margin-top: 100px;text-align: center"><img src="../images/nodata.png" /></div>',
        noticeenrolltime: '   <span style="display: {{enTimeShow}}"><label class="time-txt" style="color:#FE3939"><img class="" style="display:{{btnenrolltimeshow}};cursor: pointer;" format="ymdhms" src="../images/time_01.png">报名截止时间</label> <label class="time-num" data-enrolltime = "{{enrolltime}}">{{enrollTime}}</label><img class="qr-delay-enroll-time" src="../images/addimg.png" style="margin-left: 6px; cursor: pointer;display:{{btnenrolltimeshow}}">' +
            '   &nbsp;&nbsp;&nbsp;<label class="end-label" style="display:  {{endTimeShow}}; color: red;">已截止报名</label></span>' +
            '   <input datatype="Date" format="ymdhms" style="display: none;">',
    },
    /**
     * 替换模板数据 模板与数据中的变量名完全一致
     * @param template {String} 模板字符串
     * @param model {Object} 模板数据
     * @returns {*} {strHtml} 数据替换后的模板字符串
     */
    renderTemp = function (template, model) {
        var reg = /\{\{\w+\}\}/g;
        template = template.replace(reg, function (regStr) {
            var reg2 = /\{\{(\w+)\}\}/g,
                key = reg2.exec(regStr)[1];
            return model[key];
        });
        return template;
    };

//初始化事件
function initEvent() {
    $(window).on("resize", function () {
        $(".div-content").height($(window).height() - $(".layui-tab").height() - ($(".notice-pages").height() || 50) - 16);
    }).trigger("resize");
    $(".div-content").niceScroll({autohidemode: true});
    var initlist = 0;
    if(Arg("noticeid") || (Arg("comefrom") == 'ypt' && Arg("type") == 1) || parent.istuoyou()){//查询已接收通知 从弹窗/市级通知/托幼用户登录进入时直接进入已接受通知
        initlist = 1;
    }
    //选项卡切换
    $(".layui-tab-title li").off("click").on("click", function (ev) {
        if ($(this).hasClass("layui-this")) {
            return;
        }
        $(this).addClass("layui-this").siblings().removeClass("layui-this");
        var index = $(this).index();
        switch (index) {
            case 0 : {//    已发布通知公告
                objdata.enrollBtn = undefined;
                objdata.curType = "sent";
                $("#js-sent").show().siblings().hide().eq(0).show();
                handleSent();
                break;
            }
            case 1 : {//    已接收通知公告
                objdata.curType = "receive";
                $("#js-receive").show().siblings().hide().eq(0).show();
                handleReceived();
                break;
            }
            case 2 : {//    未发布通知公告
                objdata.enrollBtn = undefined;
                objdata.curType = "saved";
                $("#js-saved").show().siblings().hide().eq(0).show();
                handleSaved();
                break;
            }
        }
    }).eq(initlist).trigger("click");
    //点击搜索
    $("#imgsearch").off("click").on("click", function (ev) {
        var strSearch = $.trim($("#qr-search").val());
        objdata.strSearch = strSearch;
        switch (objdata.curType) {
            case "sent" : {
                objdata.sentHasCli = false;
                objdata.noticeCache.sent = [];
                handleSent();
                break;
            }
            case "saved" : {
                objdata.saveHasCli = false;
                objdata.noticeCache.saved = [];
                handleSaved();
                break;
            }
            case "receive" : {
                objdata.receivedHasCli = false;
                objdata.noticeCache.receive = [];
                handleReceived();
                break;
            }
        }
    });
    //Enter键触发搜索
    $("#qr-search").off("keyup").on("keyup", function (ev) {
        if (ev.keyCode === 13) {
            //    enter键
            $("#imgsearch").trigger("click");
        }
    });

    //我要报名
    function textEnrollInfo(noDetailW, conBox, noticeid, curPage, index, isnneedinfo) {
        if (isnneedinfo == "1") {
            openenrolllist(noticeid);
        } else {
            var objwhere = {};
            if (parent.iserbao()) {
                objwhere.organid = [parent.objdata.my.organid];
            } else if (parent.istuoyou()) {
                objwhere.yeyid = [parent.objdata.my.yeyid];
            }
            $.sm(function (re, err) {
                if (re && re[0]) {
                    var inputss = conBox.find("input");
                    inputss.eq(0).val(re[0][1]);
                    inputss.eq(1).val(re[0][2]);
                }
            }, ['chdc.user.information', $.msgwhere(objwhere)]);
            noDetailW.layer.open({
                type: 1,
                title: "填写报名信息",
                content: conBox,
                area: ["400px", "400px"],
                btn: ["取消", "提交"],
                btnAlign: "c",
                success: function (layero, index) {
                    var my = parent.objdata.my,
                        inputs = conBox.find("input");
                    inputs.eq(0).val(my.username).end().eq(1).val(my.mobile);
                    layero.find(".layui-layer-btn a").eq(0).prop("class", "").end().eq(1).prop("class", "").css({
                        background: "#1E9FFF",
                        borderColor: "#1E9FFF",
                        color: "#ffffff"
                    });
                },
                yes: function (index, layero) {
                    noDetailW.layer.close(index);
                },
                btn2: function (_index, layero) {
                    var my = parent.objdata.my,
                        inputs = conBox.find("input"),
                        enrollman = $.trim(inputs.eq(0).encodeval()),
                        enrollmobile = $.trim(inputs.eq(1).encodeval()),
                        enrollnum = $.trim(inputs.eq(2).encodeval()),
                        enrollmark = conBox.find("textarea").encodeval();
                    if (!enrollman || !enrollmobile || !enrollnum) {
                        noDetailW.layer.msg("必填项不可为空");
                        return false;
                    }
                    checkEnrollEnd(noticeid, objdata.myPhone, 0).then(function (data) {
                        var objwhere2 = {};
                        if (parent.ishospital()) {
                            objwhere2.organid = [parent.objdata.my.organid];
                        }
                        if (parent.istuoyou()) {
                            objwhere2.yeyid = [parent.objdata.my.yeyid];
                        }
                        if (data[0] === "end") {
                            noDetailW.layer.msg("报名已截止");
                        } else if (!(data[1] == 0 || data[1] == 2)) {
                            noDetailW.layer.confirm("报名信息已经更改，请刷新后继续？", {icon: 3, title: "提示", btn: ["刷新", "取消"], btnAlign: "c"}, function (index2) {
                                noDetailW.layer.close(index2);
                                objdata.receivedHasCli = false;
                                objdata.noticeCache.receive.splice(curPage, 1);
                                handleReceived(curPage);
                                parent.layer.close(index);
                            }, function (index2) {
                                noDetailW.layer.close(index2);
                            });
                        } else {
                            $.sm(function (re, err) {
                                if (!err && re) {
                                    noDetailW.layer.msg("报名成功");
                                    objdata.receivedHasCli = false;
                                    objdata.noticeCache.receive.splice(curPage, 1);
                                    handleReceived(curPage);
                                    setTimeout(function () {
                                        noDetailW.layer.close(_index);
                                        parent.layer.close(index);
                                    }, 800)
                                } else {
                                    noDetailW.layer.msg("报名失败,请稍后再试");
                                    noDetailW.layer.close(_index);

                                }
                            }, ["notice.enroll", enrollman, enrollmobile, enrollnum, enrollmark, noticeid, parent.objdata.my.mobile, parent.objdata.my.fuyouid, $.msgwhere(objwhere2)]);
                        }
                    });
                }
            });
        }
    }

    //检查报名是否已经截止
    function checkEnrollEnd(noticeId, mobile, yeyid) {
        return new Promise(function (resolve, reject) {
            $.sm(function (re, err) {
                if (re && !err) {
                    resolve(re[0]);
                }
            }, ["notice.enrollIsEnd", noticeId, mobile])
        });
    }
    //已发布和已接收通知详情
    $("body").off("click", ".checknotice").on("click", ".checknotice", function (ev) {
        var _this = this;
        objdata.enrollBtn = "";
        if($(_this).closest("#js-saved").length > 0){
            return false;
        }
        var curnoti = $(_this).closest(".noti-content");
        var noticeId = curnoti.attr("data-id"),
            arrBtn = ["关闭"],
            curIndex = curnoti.index(),
            curPage = Number(curnoti.parent().next().attr("data-pageCur") - 1) || 0;
        var curnotice = null;
        objdata.curNoticePro = objdata.noticeCache[objdata.curType][curPage];
        objdata.curNoticePro.then(function (data) {
            var tmp = data[curIndex];
            curnotice = tmp;
            if (tmp.isenroll) {
                //    本通知允许报名
                if (objdata.curType === "receive") {
                    objdata.curNoMyEnroll = tmp.myenroll;
                    if (curnoti.find(".end-label").css("display") !== "none") {
                        //报名已经截止
                        objdata.enrollBtn = 3;
                        arrBtn = ["已截止报名(未报名)", "关闭"];
                        //如果报名截止，并且用户选择不报名，更改按钮状态
                        if (tmp.myenroll === 2) {
                            arrBtn = ["已截止报名(我已不报名)", "关闭"];
                        }
                        if (tmp.myenroll !== 1) {
                            return false;
                        }
                    }
                    switch (tmp.myenroll) {
                        case 0 : {
                            //    没有报名
                            arrBtn = arrBtn.concat(["我要报名", "不报名", "关闭"]);
                            objdata.enrollBtn = 0;
                            break;
                        }
                        case 1 : {
                            if (objdata.enrollBtn === 3) {
                                arrBtn = ["已截止报名(我已报名)", "我的报名历史", "关闭"];
                                objdata.enrollBtn = 4;
                            } else {
                                if(tmp.enrollshednum > 0){
                                    arrBtn = ["已报名", "我的报名历史", "关闭"];
                                }else{
                                    arrBtn = ["已报名", "我的报名历史", "取消报名", "关闭"];
                                }
                                objdata.enrollBtn = 1;
                            }
                            break;
                        }
                        case 2 : {
                            //arrBtn = ["不报名"];
                            //objdata.enrollBtn = 2;
                            if (objdata.enrollBtn === 3) {
                                arrBtn = ["已截止报名(我已不报名)", "关闭"];
                                objdata.enrollBtn = 3;
                            } else {
                                arrBtn = arrBtn.concat(["取消不报名(我已不报名)", "关闭"]);
                                objdata.enrollBtn = 2;
                            }
                            break;
                        }
                    }
                } else {
                    arrBtn = ["关闭"];
                }
            }
        });
        parent.objdata.noticedetailindex = parent.layer.open({
            type: 2,
            title: '查看通知详情',
            area: ['52%', '75%'], //宽高
            btnAlign: 'c',
            content: 'html/noticedetail.html?noticeId=' + noticeId + "&mid=" + Arg("mid") + "&noticetype=" + objdata.curType + "&sendtype=" + curnotice.sendtype + "&fromnoticeid=" + curnotice.fromnoticeid + "&type=" + Arg("type") + "&comefrom=" + Arg("comefrom"),
            btn: arrBtn,
            success: function (layero, index) {
                jQuery.getparent().objdata.noticedetailiframe = layero.find('iframe')[0].contentWindow;
                parent.objdata.notiDetailId = layero.prop("id");
                if (objdata.enrollBtn === 1) {
                    layero.find(".layui-layer-btn a").eq(0).css({
                        borderColor: "#9B9B9B",
                        backgroundColor: "#9B9B9B"
                    }).end().eq(1).css({
                        background: "#ffffff",
                        color: "#1E9FFF"
                    }).end().eq(2).css({
                        background: "#ffffff"
                        // color: "#FE3939"
                    });
                } else if (objdata.enrollBtn === 0) {
                    layero.find(".layui-layer-btn a").eq(0).hide().end().eq(1).css({
                        borderColor: "#1CA89D",
                        background: "#1CA89D",
                        color: "#ffffff"
                    }).end().eq(2).css({
                        borderColor: "#E4E4E4",
                        background: "#F1F1F1",
                        color: "#3B3B3B"
                    });
                } else if (objdata.enrollBtn === 2) {
                    layero.find(".layui-layer-btn a").eq(0).hide().end().eq(1).css({
                        borderColor: "#9B9B9B",
                        backgroundColor: "#9B9B9B"
                    })
                } else if (objdata.enrollBtn === 3) {
                    //报名已经结束
                    layero.find(".layui-layer-btn a").eq(0).css({
                        borderColor: "#9B9B9B",
                        backgroundColor: "#9B9B9B"
                    })
                } else if (objdata.enrollBtn === 4) {
                    //已经报名过的活动已截止报名
                    layero.find(".layui-layer-btn a").eq(0).css({
                        borderColor: "#9B9B9B",
                        backgroundColor: "#9B9B9B"
                    }).end().eq(1).css({
                        background: "#ffffff",
                        color: "#1E9FFF"
                    });
                }
                if (curnoti.find(".stat-mark").length > 0 && curnoti.find(".stat-mark").attr("data-isread") !== "0") {
                    if (objdata.curType === "sent") {
                        layero.find(".layui-layer-btn a").eq(1).prop("id", "qr-enroll-print").hide();
                    }
                    return;
                }
                modreadstatus(curnoti, noticeId, curnotice.sendtype);
            },
            yes: function (index, layero) {
                if(objdata.enrollBtn == 3){//关闭
                    parent.layer.msg("报名已截止");
                    return false;
                }
                var ndCon = layero.find("iframe")[0].contentWindow.$("#container");
                if (ndCon.css("display") === "none") {
                    ndCon.show().next().hide();
                    var notiDetailId = parent.objdata.notiDetailId;
                    parent.$("#" + notiDetailId).find(".layui-layer-btn").children().eq(0).text("关闭").next().hide();
                    return;
                }
                parent.layer.close(index);
            },
            btn2: function (index, layero) {
                var my = parent.objdata.my;
                var noDetailW = layero.find("iframe")[0].contentWindow,
                    conBox = noDetailW.$("#qr-enroll-info");
                switch (objdata.curNoMyEnroll) {
                    case 0 :
                        if(objdata.enrollBtn == 3){//关闭
                            return true;
                        }
                        //    我要报名
                        checkEnrollEnd(noticeId, objdata.myPhone, 0).then(function (data) {
                            if (data[0] === "end") {
                                noDetailW.layer.msg("报名已截止");
                            } else if (!(data[1] == 0 || data[1] == 2)) {
                                noDetailW.layer.confirm("报名信息已经更改，请刷新后继续？", {icon: 3, title: "提示", btn: ["刷新", "取消"], btnAlign: "c"}, function (index2) {
                                    noDetailW.layer.close(index2);
                                    objdata.receivedHasCli = false;
                                    objdata.noticeCache.receive.splice(curPage, 1);
                                    handleReceived(curPage);
                                    parent.layer.close(index);
                                }, function (index2) {
                                    noDetailW.layer.close(index2);
                                });
                            } else {
                                textEnrollInfo(noDetailW, conBox, noticeId, curPage, index, data[3]);//data[3]是否允许填写报名资料
                            }
                        });
                        break;
                    case 1 :
                        var noticedetailinfo = jQuery.getparent().objdata.noticedetailiframe;
                        if (noticedetailinfo && noticedetailinfo.objdata.isnneedinfo == "1") {//
                            openenrolllist(noticeId, "detail");
                        } else {
                            //展现我的报名信息
                            var cancelBtn = ["关闭"];
                            if (objdata.enrollBtn !== 4) {
                                cancelBtn = ["取消", "修改"];
                            }
                            var objwhere2 = {};
                            if (parent.ishospital()) {
                                objwhere2.organid = [my.organid];
                            }
                            if (parent.istuoyou()) {
                                objwhere2.yeyid = [my.yeyid];
                            }
                            noDetailW.layer.open({
                                type: 1,
                                title: "我的报名信息",
                                content: conBox,
                                area: ["400px", "400px"],
                                btn: cancelBtn,
                                btnAlign: "c",
                                success: function (layero, index) {
                                    var my = parent.objdata.my,
                                        inputs = conBox.find("input");
                                    inputs.val("");
                                    if (objdata.enrollBtn !== 4) {
                                        layero.find(".layui-layer-btn a").eq(0).prop("class", "").end().eq(1).prop("class", "").css({
                                            background: "#1E9FFF",
                                            borderColor: "#1E9FFF",
                                            color: "#ffffff"
                                        });
                                    } else {
                                        layero.find(".layui-layer-btn a").eq(0).css({
                                            background: "#1E9FFF",
                                            borderColor: "#1E9FFF",
                                            color: "#ffffff"
                                        });
                                        layero.find(".layui-form input").prop({
                                            disabled: "disabled"
                                        });
                                        layero.find(".layui-form .layui-form-text .layui-textarea").prop({
                                            disabled: "disabled"
                                        });
                                    }
                                    $.sm(function (re, err) {
                                        if (!err && re) {
                                            var objInfo = re[0];
                                            inputs.eq(0).val(objInfo.enrollman).end().eq(1).val(objInfo.enrollmobile).end().eq(2).val(objInfo.enrollnum);
                                            conBox.find("textarea").val(objInfo.enrollmark);
                                        } else {
                                            noDetailW.layer.msg("查看报名信息失败,请稍后再试");
                                            noDetailW.layer.close(index);
                                        }
                                    }, ["notice.getEnrollInfo", noticeId, my.fuyouid, $.msgwhere(objwhere2)]);
                                },
                                yes: function (index, layero) {
                                    noDetailW.layer.close(index);
                                },
                                btn2: function (_index, layero) {
                                    var inputs = conBox.find("input"),
                                        enrollman = inputs.eq(0).encodeval(),
                                        enrollmobile = inputs.eq(1).encodeval(),
                                        enrollnum = inputs.eq(2).encodeval(),
                                        enrollmark = conBox.find("textarea").encodeval();
                                    if (!enrollman || !enrollmobile || !enrollnum) {
                                        noDetailW.layer.msg("必填项不可为空");
                                        return false;
                                    }
                                    checkEnrollEnd(noticeId, objdata.myPhone, 0).then(function (data) {
                                        if (data[0] === "end") {
                                            noDetailW.layer.msg("报名已截止");
                                        } else if (data[1] != 1) {
                                            noDetailW.layer.confirm("报名信息已经更改，请刷新后继续？", {icon: 3, title: "提示", btn: ["刷新", "取消"], btnAlign: "c"}, function (index2) {
                                                noDetailW.layer.close(index2);
                                                objdata.receivedHasCli = false;
                                                objdata.noticeCache.receive.splice(curPage, 1);
                                                handleReceived(curPage);
                                                parent.layer.close(index);
                                            }, function (index2) {
                                                noDetailW.layer.close(index2);
                                            });
                                        } else {
                                            $.sm(function (re, err) {
                                                if (!err && re) {
                                                    noDetailW.layer.msg("修改报名成功");
                                                    objdata.receivedHasCli = false;
                                                    objdata.noticeCache.receive.splice(curPage, 1);
                                                    handleReceived(curPage);
                                                    setTimeout(function () {
                                                        noDetailW.layer.close(_index);
                                                        parent.layer.close(index);
                                                    }, 800)
                                                } else {
                                                    noDetailW.layer.msg("修改报名失败,请稍后再试");
                                                    noDetailW.layer.close(_index);

                                                }
                                            }, ["notice.enroll", enrollman, enrollmobile, enrollnum, enrollmark, noticeId, my.fuyouid, $.msgwhere(objwhere2)]);
                                        }
                                    });
                                    return false;
                                }
                            });
                        }
                        break;
                    case 2 :
                        //不报名
                        //parent.layer.close(index);
                        //break;
                        if(objdata.enrollBtn == 3){
                            return true;
                        }
                        // 取消不报名
                        checkEnrollEnd(noticeId, objdata.myPhone, 0).then(function (data) {
                            if (data[0] === "end") {
                                noDetailW.layer.msg("报名已截止");
                            } else if (!(data[1] == 1 || data[1] == 2)) {
                                noDetailW.layer.confirm("报名信息已经更改，请刷新后继续？", {icon: 3, title: "提示", btn: ["刷新", "取消"], btnAlign: "c"}, function (index2) {
                                    noDetailW.layer.close(index2);
                                    objdata.receivedHasCli = false;
                                    objdata.noticeCache.receive.splice(curPage, 1);
                                    handleReceived(curPage);
                                    parent.layer.close(index);
                                }, function (index2) {
                                    noDetailW.layer.close(index2);
                                });
                            } else {
                                noDetailW.layer.open({
                                    title: "提示",
                                    type: 1,
                                    btn: ["取消", "确定"],
                                    btnAlign: "c",
                                    area: ["350px", "150px"],
                                    content: "<div style='text-align: center; margin-top: 20px;'>确认要取消不报名吗？</div>",
                                    success: function (layero, _index) {
                                        layero.find(".layui-layer-btn a").eq(0).prop("class", "").end().eq(1).prop("class", "").css({
                                            background: "#1E9FFF",
                                            borderColor: "#1E9FFF",
                                            color: "#ffffff"
                                        });
                                    },
                                    yes: function (_index, layero) {
                                        noDetailW.layer.close(_index);
                                    },
                                    btn2: function (_index, layero) {
                                        $.sm(function (re, err) {
                                            if (!err && re) {
                                                noDetailW.layer.msg("取消不报名成功");
                                                objdata.receivedHasCli = false;
                                                objdata.noticeCache.receive.splice(curPage, 1);
                                                handleReceived(curPage);
                                                setTimeout(function () {
                                                    noDetailW.layer.close(_index);
                                                    parent.layer.close(index);
                                                }, 800);
                                            } else {
                                                parent.layer.msg("取消报名失败,请稍后再试");
                                                noDetailW.layer.close(_index);
                                            }
                                        }, ["notice.cancelEnroll", noticeId, my.fuyouid]);
                                        return false;
                                    }
                                });
                            }
                        });
                        break;
                    case undefined :
                        //    打印报名表
                        parent.layer.open({
                            type: 2,
                            title: "打印报名表",
                            content: "html/allenroll.html?v=" + Arg("v") + "&dataid=" + noticeId + "&notiname=" + curnoti.find("h3")[0].childNodes[0].nodeValue,
                            area: ["100%", "100%"],
                            btn: ["打印预览", "关闭", "导出Excel"],
                            btnAlign: "c",
                            success: function (layero, index) {
                                layero.find('iframe')[0].contentWindow.allEnrollInfo();
                            },
                            yes: function (index, layero) {
                                layero.find('iframe')[0].contentWindow.$('#printview').trigger('click');
                            },
                            btn3: function (index, layero) {
                                layero.find('iframe')[0].contentWindow.$('#btexcel').trigger('click');
                            }
                        });
                }
                return false;
            },
            btn3: function (index, layero) {
                var noDetailW = layero.find("iframe")[0].contentWindow,
                    conBox = noDetailW.$("#qr-enroll-info"),
                    my = parent.objdata.my;
                switch (objdata.curNoMyEnroll) {
                    case 0 : {
                        //    不报名
                        checkEnrollEnd(noticeId, objdata.myPhone, 0).then(function (data) {
                            if (data[0] === "end") {
                                noDetailW.layer.msg("报名已截止");
                            } else if (!(data[1] == 0 || data[1] == 2)) {
                                noDetailW.layer.confirm("报名信息已经更改，请刷新后继续？", {icon: 3, title: "提示", btn: ["刷新", "取消"], btnAlign: "c"}, function (index2) {
                                    noDetailW.layer.close(index2);
                                    objdata.receivedHasCli = false;
                                    objdata.noticeCache.receive.splice(curPage, 1);
                                    handleReceived(curPage);
                                    parent.layer.close(index);
                                }, function (index2) {
                                    noDetailW.layer.close(index2);
                                });
                            } else {
                                noDetailW.layer.open({
                                    title: "提示",
                                    type: 1,
                                    btn: ["取消", "确定"],
                                    btnAlign: "c",
                                    area: ["350px", "150px"],
                                    content: "<div style='text-align: center; margin-top: 20px;'>确认不报名吗？</div>",
                                    success: function (layero, _index) {
                                        layero.find(".layui-layer-btn a").eq(0).prop("class", "").end().eq(1).prop("class", "").css({
                                            background: "#1E9FFF",
                                            borderColor: "#1E9FFF",
                                            color: "#ffffff"
                                        });
                                    },
                                    yes: function (_index, layero) {
                                        noDetailW.layer.close(_index);
                                    },
                                    btn2: function (_index, layero) {
                                        $.sm(function (re, err) {
                                            if (!err && re) {
                                                noDetailW.layer.msg("您已确认不报名");
                                                objdata.receivedHasCli = false;
                                                objdata.noticeCache.receive.splice(curPage, 1);
                                                handleReceived(curPage);
                                                setTimeout(function () {
                                                    noDetailW.layer.close(_index);
                                                    parent.layer.close(index);
                                                }, 800);
                                            } else {
                                                parent.layer.msg("不报名失败,请稍后再试");
                                                noDetailW.layer.close(_index);
                                            }
                                        }, ["notice.notenroll", noticeId, my.fuyouid]);
                                        return false;
                                    }
                                });
                            }
                        });
                    }
                        break;
                    case 1 : {
                        if(curnotice.enrollshednum > 0 || objdata.enrollBtn == 4){
                            return true;
                        }
                        // 取消报名
                        checkEnrollEnd(noticeId, objdata.myPhone, 0).then(function (data) {
                            if (data[0] === "end") {
                                noDetailW.layer.msg("报名已截止");
                            } else if (data[1] != 1) {
                                noDetailW.layer.confirm("报名信息已经更改，请刷新后继续？", {icon: 3, title: "提示", btn: ["刷新", "取消"], btnAlign: "c"}, function (index2) {
                                    noDetailW.layer.close(index2);
                                    objdata.receivedHasCli = false;
                                    objdata.noticeCache.receive.splice(curPage, 1);
                                    handleReceived(curPage);
                                    parent.layer.close(index);
                                }, function (index2) {
                                    noDetailW.layer.close(index2);
                                });
                            } else {
                                noDetailW.layer.open({
                                    title: "提示",
                                    type: 1,
                                    btn: ["取消", "确定"],
                                    btnAlign: "c",
                                    area: ["350px", "150px"],
                                    content: "<div style='text-align: center; margin-top: 20px;'>确认要取消报名吗？</div>",
                                    success: function (layero, _index) {
                                        layero.find(".layui-layer-btn a").eq(0).prop("class", "").end().eq(1).prop("class", "").css({
                                            background: "#1E9FFF",
                                            borderColor: "#1E9FFF",
                                            color: "#ffffff"
                                        });
                                    },
                                    yes: function (_index, layero) {
                                        noDetailW.layer.close(_index);
                                    },
                                    btn2: function (_index, layero) {
                                        $.sm(function (re, err) {
                                            if (!err && re) {
                                                noDetailW.layer.msg("取消报名成功");
                                                objdata.receivedHasCli = false;
                                                objdata.noticeCache.receive.splice(curPage, 1);
                                                handleReceived(curPage);
                                                setTimeout(function () {
                                                    noDetailW.layer.close(_index);
                                                    parent.layer.close(index);
                                                }, 800);
                                            } else {
                                                parent.layer.msg("取消报名失败,请稍后再试");
                                                noDetailW.layer.close(_index);
                                            }
                                        }, ["notice.cancelEnroll", noticeId, my.fuyouid]);
                                        return false;
                                    }
                                });
                            }
                        });
                        break;
                    }
                    case 2:
                        return true;
                        break;

                }
                return false;
            }
        });
    });
    //新增通知公告
    $("#addbtn").click(function () {
        openAddPage();
    });
    $("#js-sent").off("click", ".a-del").on("click", ".a-del", function (ev){//删除已发布
        //    删除已发布的通知(用户自删除)
        var noticeId = $(this).closest(".noti-content").attr("data-id");
        new Promise(function (resolve, reject) {
            parent.layer.open({
                title: "提示",
                content: "<div style='color: red;'>删除后将收不到通知反馈，比如阅读记录、签到情况，请谨慎操作。</div>",
                btn: ["确定", "取消"],
                yes: function (index) {
                    var arrsm = ["notice.selfDelNotice", noticeId];
                    if(objdata.getdatafromypt){
                        arrsm = ["notice.selfDelNoticeFromYpt", noticeId];
                    }
                    $.sm(function (re, err) {
                        if (!err && re) {
                            parent.layer.close(index);
                            resolve();
                        } else {
                            parent.layer.msg("删除失败！")
                        }
                    }, arrsm);
                }
            });
        }).then(function () {
            objdata.noticeCache.sent = [];
            objdata.sentHasCli = false;
            parent.layer.msg("删除通知功能！")
            // $("#js-sent").children().eq(0).html("");
            handleSent();
        });
    });
    //编辑通知草稿
    $("#js-saved").off("click", ".qr-edit").on("click", ".qr-edit", function (ev) {
        var nId = $(this).closest(".noti-content").attr("data-id");
        openAddPage(nId);
    });
    //删除通知草稿
    $("#js-saved").off("click", ".qr-del").on("click", ".qr-del", function (ev) {
        var nId = $(this).closest(".noti-content").attr("data-id");
        new Promise(function (resolve, reject) {
            parent.layer.open({
                title: "提示",
                content: "<div style='color: red;'>删除后将收不到通知反馈，比如报名名单、阅读情况、也无法发证，请谨慎操作。</div>",
                btn: ["确定", "取消"],
                yes: function (index) {
                    var arrsm = ["notice.delNotice", nId];
                    if(objdata.getdatafromypt){
                        arrsm = ["notice.delNoticeFromYpt", nId];
                    }
                    $.sm(function (re, err) {
                        if (!err && re) {
                            parent.layer.close(index);
                            resolve();
                        } else {
                            parent.layer.msg("发送消息失败")
                        }
                    }, arrsm);
                }
            });
        }).then(function () {
            objdata.noticeCache.saved = [];
            objdata.saveHasCli = false;
            $("#js-saved").children().eq(0).html("");
            handleSaved();
        });
    });
    //阅读情况
    $("#js-sent").off("click", ".qr-about-read").on("click", ".qr-about-read", function (ev) {
        var noticontent = $(this).closest(".noti-content");
        var noticeid = noticontent.attr("data-id");
        var totype = noticontent.attr("data-totype");
        var readstatus = $(this).attr("data-readstatus");
        var expertype = objdata.sendnotice[noticeid].expertype;
        parent.layer.open({
            type: 2,
            title: '查看阅读情况',
            // skin : 'layui-layer-rim', //加上边框
            btnAlign: 'c',
            area: ['100%', '100%'],
            btn: ["关闭"],
            content: 'html/noticereadlist.html?v=' + (Arg("v") || 1) + "&mid=" + (Arg("mid") || "") + "&noticeid=" + noticeid + "&totype=" + totype + "&readtype=isread" + "&readstatus=" + readstatus + "&expertype=" + expertype + "&type=" + Arg("type") + "&comefrom=" + Arg("comefrom")
        });
        return false;
    });
    //报名情况
    $("#js-sent").off("click", ".lb-enrollstate").on("click", ".lb-enrollstate", function (ev) {
        var noticontent = $(this).closest(".noti-content");
        var noticeid = noticontent.attr("data-id");
        var sendtype = noticontent.attr("data-sendtype");
        var totype = noticontent.attr("data-totype");
        var url = "noticereadlist";
        // if(totype == "p"){//发送小程序家长
        //     url = "noticesignlist";
        // }
        parent.layer.open({
            type: 2,
            title: '查看报名情况',
            // skin : 'layui-layer-rim', //加上边框
            btnAlign: 'c',
            area: ['100%', '100%'],
            btn: ["关闭"],
            content: 'html/' + url + '.html?v=' + (Arg("v") || 1) + "&mid=" + (Arg("mid") || "") + "&noticeid=" + noticeid + "&totype=" + totype + "&type=isenroll" + "&comefrom=" + Arg("comefrom")
        });
        return false;
    });
    //签到情况
    $("#js-sent").off("click", ".lb-signstate").on("click", ".lb-signstate", function (ev) {
        var noticontent = $(this).closest(".noti-content");
        var noticeid = noticontent.attr("data-id");
        var sendtype = noticontent.attr("data-sendtype");
        var totype = noticontent.attr("data-totype");
        parent.layer.open({
            type: 2,
            title: '查看签到情况',
            // skin : 'layui-layer-rim', //加上边框
            btnAlign: 'c',
            area: ['100%', '100%'],
            btn: ["关闭"],
            content: 'html/noticesignlist.html?v=' + (Arg("v") || 1) + "&mid=" + (Arg("mid") || "") + "&noticeid=" + noticeid + "&totype=" + totype
        });
        return false;
    });
    //打印二维码
    $("#js-sent").off("click", ".qr-print-code").on("click", ".qr-print-code", function (ev) {
        var noticontent = $(this).closest(".noti-content");
        var noticeid = noticontent.attr("data-id");
        var sendtype = noticontent.attr("data-sendtype");
        var totype = noticontent.attr("data-totype");
        parent.layer.open({
            type: 2,
            title: '正在打印二维码',
            // skin : 'layui-layer-rim', //加上边框
            btnAlign: 'c',
            area: ['100%', '100%'],
            btn: ["打印预览","下载","关闭"],
            content: 'html/noticeprintqrcode.html?v=' + (Arg("v") || 1) + "&mid=" + (Arg("mid") || "") + "&noticeid=" + noticeid + "&totype=" + totype,
            yes: function (index, layero) {
                layero.find('iframe')[0].contentWindow.$("#btnprint").trigger("click");
                return false;
            },
            btn2: function (index, layero) {
                layero.find('iframe')[0].contentWindow.$("#btndownload").trigger("click");
                return false;
            }
        });
        return false;
    });
    //延迟报名时间
    $("#js-sent").off("click", ".qr-delay-enroll-time").on("click", ".qr-delay-enroll-time", function (ev) {
        return false;
    });
    //通知转发
    $("#js-receive").off("click", ".qr-delay-enroll-forward").on("click", ".qr-delay-enroll-forward", function (ev) {
        var _this = $(this);
        var noticeid = _this.closest(".noti-content").attr("data-id");
        var sendtype = _this.closest(".noti-content").attr("data-sendtype");
        var noticetitle = _this.closest(".noti-content").find(".notice-title").html();
        parent.layer.open({
            type: 2,
            title: '正在转发培训通知-【' + noticetitle + "】",
            btnAlign: 'c',
            area: ['100%', '100%'],
            // btn: ["确定", "关闭"],
            content: 'html/noticeadd.html?v=' + (Arg("v") || 1) + "&mid=" + (Arg("mid") || "") + "&noticeid=" + noticeid + "&sendtype=" + sendtype + "&isforward=1" + "&type=" + Arg("type") + "&comefrom=" + Arg("comefrom"),
            success: function (layero, index) {
                parent.objdata.noticeAddFrame = layero.find('iframe')[0];
                var curnoti = _this.closest(".noti-content");
                if (curnoti.find(".stat-mark").length > 0 && curnoti.find(".stat-mark").attr("data-isread") !== "0") {
                    return;
                }
                var curPage = Number(curnoti.parent().next().attr("data-pageCur") - 1) || 0;
                objdata.curNoticePro = objdata.noticeCache[objdata.curType][curPage];
                modreadstatus(curnoti, noticeid, sendtype);
            }
            // , yes: function (index, layero) { //或者使用btn1
            //     layero.find('iframe')[0].contentWindow.subEnroll(function () {
            //         jQuery.getparent().layer.close(index);
            //         parent.objdata.noticedetailindex ? jQuery.getparent().layer.close(parent.objdata.noticedetailindex) : "";
            //         location.reload();
            //     });
            // }
        });
        return false;
    });
    //提交报名名单
    $("#js-receive").off("click", ".qr-delay-enroll-commitname").on("click", ".qr-delay-enroll-commitname", function (ev) {
        var noticeid = $(this).closest(".noti-content").attr("data-id");
        var sendtype = $(this).closest(".noti-content").attr("data-sendtype");
        var noticetitle = $(this).closest(".noti-content").find(".notice-title").html();
        openenrolllist(noticeid);
        return false;
    });
    //报名资质审核
    $("#js-sent").off("click", ".qr-delay-enroll-shenroll").on("click", ".qr-delay-enroll-shenroll", function (ev) {
        var noticeid = $(this).closest(".noti-content").attr("data-id");
        var sendtype = $(this).closest(".noti-content").attr("data-sendtype");
        var noticetitle = $(this).closest(".noti-content").find(".notice-title").html();
        parent.layer.open({
            type: 2,
            title: '报名资格审核-【' + noticetitle + "】",
            // skin : 'layui-layer-rim', //加上边框
            btnAlign: 'c',
            area: ['100%', '100%'],
            btn: ["关闭"],
            content: 'tongph/noticesignuplist.html?v=' + (Arg("v") || 1) + "&mid=" + (Arg("mid") || "") + "&noticeid=" + noticeid + "&sendtype=" + sendtype + "&certids=" + objdata.sendnotice[noticeid].certids,
        });
        return false;
    });
    //查看原通知
    $("#js-sent").off("click", ".qr-delay-enroll-srcnotice").on("click", ".qr-delay-enroll-srcnotice", function (ev) {
        var noticeid = $(this).closest(".noti-content").attr("data-id");
        // var sendtype = $(this).closest(".noti-content").attr("data-sendtype");
        // var noticetitle = $(this).closest(".noti-content").find(".notice-title").html();
        parent.objdata.noticedetailindex = parent.layer.open({
            type: 2,
            title: '查看通知详情',
            area: ['52%', '75%'], //宽高
            btnAlign: 'c',
            content: 'html/noticedetail.html?noticeId=' + objdata.sendnotice[noticeid].fromnoticeid + "&mid=" + Arg("mid") + "&sendtype=" + objdata.sendnotice[noticeid].sendtype + "&type=" + Arg("type") + "&comefrom=" + Arg("comefrom"),
            btn: ["关闭"],
            success: function (layero, index) {

            }
        });
        return false;
    });
}

function reloadPage() {
    $("#imgsearch").trigger("click");
}
/**
 * 新增通知公告
 * @param id
 * @param name
 */
function openAddPage(id, name) {
    parent.layer.open({
        type: 2,
        title: (id ? "编辑通知" : "新建通知公告"),
        // skin : 'layui-layer-rim', //加上边框
        btnAlign: 'c',
        area: ['100%', '100%'],
        // btn: ["关闭"],
        content: 'html/noticeadd.html?v=' + (Arg("v") || 1) + "&mid=" + (Arg("mid") || "") + (id ? "&noticeid=" + id : "") + "&type=" + Arg("type") + "&comefrom=" + Arg("comefrom"),
        success: function (layero, index) {
            parent.objdata.noticeAddFrame = layero.find('iframe')[0];
        }
    });
}

/**
 * 获取已发布或已保存通告
 */

function getNoticeList(index, type) {
    var sendSta = (type === "sent" ? 1 : 0),
        pageELe = (type === "sent" ? "#sent-page" : "#saved-page"),
        strWhere = {},
        pageNum = objdata.pageConf.pageNum,
        offset = 0; //分页偏移量
    strWhere = {fromid: [objdata.myid, sendSta, parent.objdata.my.roletype,parent.objdata.my.fuyouid]};
    if (objdata.strSearch !== "") {
        strWhere.strSearch = [objdata.strSearch];
    }
    if (parent.ishospital()) {
        strWhere.organid = [parent.objdata.my.organid];
    }
    strWhere.noticeflag = [Arg("type")];
    if (index === 0) {
        //    首次进入某一大类通知
        return new Promise(function (resolve, reject) {
            var arrsm = ["notice.getPage", pageNum, $.msgwhere(strWhere)];
            if(objdata.getdatafromypt){
                arrsm = ["notice.getPageFromYpt", pageNum, $.msgwhere(strWhere)];
            }
            $.sm(function (re, err) {
                if (!err && re) {
                    resolve(re[0]);
                } else {
                    parent.layer.msg("获取通知公告失败！")
                }
            }, arrsm);
        }).then(function (data) {
            //创建缓存数组
            if (type === "sent") {
                objdata.noticeCache.sent.length = data.pagecount;
            } else if (type === "saved") {
                objdata.noticeCache.saved.length = data.pagecount;
            }
            handlePage(data, pageELe);
            return new Promise(function (resolve, reject) {
                var arrsm = ["notice.selSentNotice", $.msgwhere(strWhere), pageNum, offset];
                if(objdata.getdatafromypt){
                    arrsm = ["notice.selSentNoticeFromYpt", $.msgwhere(strWhere), pageNum, offset];
                }
                $.sm(function (re, err) {
                    if (!err && re) {
                        resolve(re);
                    } else {
                        parent.layer.msg("获取通知公告失败！")
                    }
                }, arrsm);
            })
        });
    }
    return new Promise(function (resolve, reject) {
        var offset = index * pageNum;
        var arrsm = ["notice.selSentNotice", $.msgwhere(strWhere), pageNum, offset];
        if(objdata.getdatafromypt){
            arrsm = ["notice.selSentNoticeFromYpt", $.msgwhere(strWhere), pageNum, offset];
        }
        $.sm(function (re, err) {
            if (!err && re) {
                resolve(re);
            } else {
                parent.layer.msg("获取通知公告失败！");
            }
        }, arrsm);
    });
}

/**
 * 获取接收到的通告
 */

function getReceNotice(index) {
    var pageELe = "#receive-page",
        strWhere = {},
        pageNum = objdata.pageConf.pageNum,
        offset = index * pageNum; //分页偏移量
    strWhere.fuyouid = [parent.objdata.my.fuyouid];
    if (objdata.strSearch !== "") {
        strWhere.strSearch = [objdata.strSearch];
    }
    var objwhere = {};
    objwhere.fuyouid = [parent.objdata.my.fuyouid];
    objwhere.noticeflag = [Arg("type")];
    strWhere.noticeflag = [Arg("type")];
    if (parent.iserbao()) {
        objwhere.organid = [parent.objdata.my.organid];
        strWhere.organid = [parent.objdata.my.organid];
    } else if (parent.istuoyou()) {
        objwhere.yeyid = [parent.objdata.my.yeyid];
        strWhere.yeyid = [parent.objdata.my.yeyid];
    }
    return new Promise(function (resolve, reject) {
        var arrsm = ["notice.receGetPage", objdata.pageConf.pageNum, $.msgwhere(objwhere)];
        if(objdata.getdatafromypt){
            arrsm = ["notice.receGetPageFromYpt", objdata.pageConf.pageNum, $.msgwhere(objwhere)];
        }
        $.sm(function (re, err) {
            if (!err && re) {
                re[0].mobile = objdata.myPhone;
                resolve(re[0]);
            } else {
                parent.layer.msg("获取接收通知失败！")
            }
        }, arrsm);
    }).then(function (data) {
        if (data.total === 0) {
            return new Promise.reject("无通知");
        }
        if (index === 0) {
            objdata.noticeCache.receive.length = data.pagecount;
            handlePage(data, pageELe);
        }
        return new Promise(function (resolve, reject) {
            var arrsm = ["notice.selReceivedNotice", pageNum, offset, $.msgwhere(strWhere)];
            if(objdata.getdatafromypt){
                arrsm = ["notice.selReceivedNoticeFromYpt", pageNum, offset, $.msgwhere(strWhere)];
            }
            $.sm(function (re, err) {
                if (!err && re) {
                    resolve(re);
                } else {
                    parent.layer.msg("获取接收通知失败！")
                }
            }, arrsm);
        });
    }).then(function (data) {
        //获取发送用户的名字
        if (data.total === 0 || !data.length) {
            return ([data, false]);
        }
        var arrSm = ["notice.getUname"],
            tmp = [];
        for (var i = 0; i < data.length; i++) {
            tmp.push(data[i].fromid);
        }
        arrSm.push($.msgwhere({ids: $.msgpJoin(tmp)}));
        return new Promise(function (resolve, reject) {
            $.sm(function (re, err) {
                if (!err && re) {
                    resolve([data, re]);
                } else {
                    parent.layer.msg(err)
                }
            }, arrSm);
        });
    }).then(function (data) {
        if (typeof data[1] !== "boolean") {
            objdata.noticeCache.receive.objUsers = groupDataById(data[1]);
        }
        return data[0];
    }).catch(function () {
        $("#js-receive").children().eq(0).html(templete.noContent).next().hide();
        // parent.layer.msg("暂无接收通知");
    });
}

//报名时间组件
function makeEnrollTem(curNotice) {
    var moment = parent.moment;
    var enT = moment(curNotice.enrolltime)._d,
        now = new Date();
    curNotice.setEntime = "inline-block";
    curNotice.enTimeShow = "inline-block";
    curNotice.timeInfo = "报名进行中";
    curNotice.enrollTime = retTime(enT);
    curNotice.endTimeShow = "none";
    if (+enT <= +now) {
        //报名已经结束
        curNotice.endTimeShow = "inline-block";
        //    报名结束超过7天，不允许延迟报名
        if ((+now - +enT) / (1000 * 60 * 60 * 24) >= 7) {
            curNotice.setEntime = "none";
            curNotice.enTimeShow = "none";
            curNotice.btnenrolltimeshow = "none";
        }
    }
    return renderTemp(templete.noticeenrolltime, curNotice);
}

/**
 * 更新阅读状态
 * @param curnoti
 * sendtype发送对象（fyyypt.市妇幼，e.妇幼，ehos.医院'）
 */
function modreadstatus(curnoti, noticeid){
    curnoti.find(".stat-mark").attr("data-isread", "1");
    var numLabels = curnoti.find(".js-read-info .qr-about-read"),
        readNum = Number(numLabels.eq(0).attr("data-num")),
        unreadNum = Number(numLabels.eq(1).attr("data-num"));
    readNum++;
    numLabels.eq(0).text(readNum + "个已读");
    unreadNum--;
    numLabels.eq(1).html('<i class="layui-mark-red">' + (unreadNum < 0 ? 0 : unreadNum) + '</i>个未读');
    curnoti.find(".stat-mark").removeClass("unread-mark");
    //    插入用户pc端阅读记录，更新通知主表的阅读统计
    var my = parent.objdata.my;
    var objwhere = {};
    if (parent.ishospital()) {
        objwhere.organid = [my.organid];
    }
    if (parent.istuoyou()) {
        objwhere.yeyid = [my.yeyid];
    }
    var arrsm = [["notice.addPcReader", my.id, noticeid, my.fuyouid, $.msgwhere(objwhere)], ["notice.upNoticeCount", noticeid]];
    if(objdata.getdatafromypt){
        arrsm = [["notice.addPcReaderFromYpt", my.id, noticeid, my.fuyouid, $.msgwhere(objwhere)], ["notice.upNoticeCountFromYpt", noticeid]];
    }
    $.sm(function (re, err) {
        if (!err && re) {
            parent.getnoticenum(0);
            // parent.messagenum();
            // parent.totalRemindNum();
        } else {
            parent.layer.msg("发送消息失败")
        }
    }, arrsm);
    objdata.curNoticePro.then(function (data) {
        curIndex = curnoti.index();
        data[curIndex].isread = "PC";
    });

}

/**
 * 查询已发布公告
 * @param index
 * @param pageFlag
 */
function handleSent(index, pageFlag) {
    if (objdata.sentHasCli && !pageFlag) {
        return;
    }
    index = (index || 0);
    objdata.sentHasCli = true;
    if (typeof objdata.noticeCache.sent[index] === "undefined") {
        objdata.noticeCache.sent[index] = getNoticeList(index, "sent");
    }
    objdata.noticeCache.sent[index].then(function (noticeList) {
        if (noticeList.length === 0) {
            // parent.layer.msg("暂无发布公告");
            $("#js-sent").children().eq(0).html(templete.noContent);
            return;
        }
        var arrHtml = [];
        for (var i = 0; i < noticeList.length; i++) {
            var curNotice = noticeList[i];
            objdata.sendnotice[curNotice.id] = curNotice;
            if (!curNotice.sendstatus) {
                continue;
            }
            if (!curNotice.cover) {
                curNotice.cover = '../images/notice_cover.jpg';
            }
            curNotice.userName = parent.objdata.my.username;
            curNotice.unreadNum = curNotice.tousernum - curNotice.readernum;
            curNotice.unreadNum < 0 ? curNotice.unreadNum = 0 : null;
            curNotice.cover1 = (curNotice.cover.indexOf('notice_cover.jpg') > -1 ? "" : ossPrefix) + curNotice.cover + "?x-oss-process=image/resize,m_fill,h_180,w_180";
            curNotice.publishtime1 = "发布于" + curNotice.publishtime;
            curNotice.attShow = curNotice.attachelen > 0 ? "inline-block" : "none";
            curNotice.enrollShow = "none";
            curNotice.readstatus = "";
            curNotice.srcnoticeshow = "none";
            if(curNotice.fromnoticeid){
                curNotice.srcnoticeshow = "inline-block";
            }
            curNotice.showOrHide = "inline-block";
            var isnenrollshow = "none";
            if(curNotice.isenroll){
                isnenrollshow = 'inline-block';
            }
            // curNotice.lbneedenrollshow = isnenrollshow;
            curNotice.btnenrolltimeshow = isnenrollshow;
            curNotice.noticeenrolltime = curNotice.isenroll ? makeEnrollTem(curNotice) : "";
            curNotice.enrollNumShow = isnenrollshow;//报名情况按钮
            curNotice.enrollsheshow = isnenrollshow;
            if(curNotice.isnneedinfo != 1){//不需要提交报名资料时不显示报名资质审核按钮，报名是否需要提交资料 0 不需要 1 需要
                curNotice.enrollsheshow = "none";
            }
            if(curNotice.isnprocess == 1){
                curNotice.btncheckenrollname = "报名资质审核";
            } else {
                curNotice.btncheckenrollname = "查看报名信息";
            }
            curNotice.printcodeshow = "none";
            curNotice.wxsignNumShow = "none";
            if(curNotice.totype == "p" && curNotice.expertype == 1){//打印二维码, 类型为讲座型的时候
                curNotice.printcodeshow = 'inline-block';
                curNotice.wxsignNumShow = 'inline-block';
                curNotice.enrollNumShow = "none";
            }
            curNotice.totypename = objdata.totypename[curNotice.totype] || "";
            arrHtml.push(renderTemp(templete.noticeListTem.replace("{{statusIconTem}}", ""), curNotice));
            // showToUserList(curNotice.id, curNotice.totype, i);
        }
        $("#js-sent").children().eq(0).html(arrHtml.join(""));
        $("#js-sent .js-read-info .btnlb").hide();
        laydate.render({
            elem: ".qr-delay-enroll-time",
            type: 'datetime',
            // format: 'yyyy-MM-dd',
            isInitValue: true,
            showBottom: true,
            done: function (value, date, endDate) {
                var _this = $(this.elem);
                // _this.html("改截止时间");
                var noticeid = _this.closest(".noti-content").attr("data-id");
                if (noticeid) {
                    parent.layer.confirm("确认延长报名时间？", {icon: 3, title: "提示", btnAlign: "c"}, function (index) {
                        var arrsm = ["notice.upEnrollTime", noticeid, value];
                        if(objdata.getdatafromypt){
                            arrsm = ["notice.upEnrollTimeFromYpt", noticeid, value];
                        }
                        $.sm(function (re, err) {
                            if (!err && re) {
                                _this.closest(".noti-content").find(".time-num").html(retTime(parent.moment(value)._d));
                                if (parent.moment(value)._d.getTime() > +new Date()) {
                                    _this.closest(".noti-content").find(".end-label").hide();
                                }
                                parent.layer.close(index);
                            } else {
                                parent.layer.msg("更改报名时间失败,请稍后再试");
                            }
                        }, arrsm);

                    }, function (index) {
                        parent.layer.close(index);
                    });
                }
            }
        });
    });
}

//查询未发布的公告
function handleSaved(index, pageFlag) {
    if (objdata.saveHasCli && !pageFlag) {
        return;
    }
    index = (index || 0);
    objdata.saveHasCli = true;
    if (typeof objdata.noticeCache.saved[index] === "undefined") {
        objdata.noticeCache.saved[index] = getNoticeList(index, "saved");
    }
    objdata.noticeCache.saved[index].then(function (noticeList) {
        if (noticeList.length === 0) {
            // parent.layer.msg("暂无公告草稿");
            $("#js-saved").children().eq(0).html(templete.noContent);
            return;
        }
        var arrHtml = [];
        for (var i = 0; i < noticeList.length; i++) {
            var curNotice = noticeList[i];
            if (curNotice.sendstatus) {
                continue;
            }
            if (!curNotice.cover || curNotice.cover == '//tbfile.oss-cn-beijing.aliyuncs.com/fuyou/notice/hPDczFHzDP1507701159679.jpg') {
                curNotice.cover = '../images/notice_cover.jpg';
            }

            curNotice.userName = parent.objdata.my.username;
            // curNotice.unreadNum = curNotice.tousernum - curNotice.readernum;
            // curNotice.unreadNum < 0 ? curNotice.unreadNum = 0 : null;
            curNotice.cover1 = (curNotice.cover.indexOf('notice_cover.jpg') > -1 ? "" : ossPrefix) + curNotice.cover + "?x-oss-process=image/resize,m_fill,h_180,w_180";
            curNotice.publishtime1 = "保存于" + curNotice.publishtime;
            curNotice.enrollShow = "none";
            curNotice.readstatus = "";
            curNotice.attShow = curNotice.attachelen > 0 ? "inline-block" : "none";
            curNotice.noticeenrolltime = "";
            curNotice.enrollNumShow = "none";//curNotice.isenroll ? "inline-block" : "none";
            curNotice.printcodeshow = "none";//二维码
            curNotice.wxsignNumShow = "none";//签到情况
            curNotice.totypename = objdata.totypename[curNotice.totype] || "";
            arrHtml.push(renderTemp(templete.noticeListTem.replace("{{statusIconTem}}", ""), curNotice));
            // showToUserList(curNotice.id, curNotice.totype, i);
        }
        $("#js-saved").children().eq(0).html(arrHtml.join(""));
    });
}

/**
 * 查询接收到的公告
 */
function handleReceived(index, pageFlag) {
    // if (objdata.receivedHasCli && !pageFlag) {
    //     return;
    // }
    if(objdata.getdatafromypt){
        parent.getnoticenum(0);//通知数据
    }
    index = (index || 0);
    // objdata.receivedHasCli = true;
    if (typeof objdata.noticeCache.receive[index] === "undefined") {
        objdata.noticeCache.receive[index] = getReceNotice(index);
    }
    objdata.noticeCache.receive[index].then(function (noticeList) {
        if (noticeList.length === 0) {
            $("#js-receive").children().eq(0).html(templete.noContent).next().hide();
            // parent.layer.msg("暂无接收通知");
            return;
        }
        var arrHtml = [];
        for (var i = 0; i < noticeList.length; i++) {
            var curNotice = noticeList[i],
                tempTem = "";
            objdata.receivenotice[curNotice.id] = curNotice;
            if (!curNotice.cover || curNotice.cover == '//tbfile.oss-cn-beijing.aliyuncs.com/fuyou/notice/hPDczFHzDP1507701159679.jpg') {
                curNotice.cover = location.origin + '/images/notice_cover.jpg';
            }
            curNotice.userName = curNotice.fromname;
            curNotice.unreadNum = curNotice.tousernum - curNotice.readernum;
            curNotice.unreadNum < 0 ? curNotice.unreadNum = 0 : null;
            curNotice.publishtime1 = "发布于" + curNotice.publishtime;
            curNotice.cover1 = (curNotice.cover.indexOf('notice_cover.jpg') > -1 ? "" : ossPrefix) + curNotice.cover + "?x-oss-process=image/resize,m_fill,h_180,w_180";
            curNotice.showOrHide = "none";
            curNotice.attShow = curNotice.attachelen > 0 ? "inline-block" : "none";
            curNotice.enrollShow = curNotice.isenroll ? "inline-block" : "none";
            curNotice.enrollStatus = curNotice.myenroll ? (curNotice.myenroll === 1 ? "我已报名" : "不报名") : "未报名";
            curNotice.enrollColor = curNotice.myenroll ? (curNotice.myenroll === 1 ? "#1AA094" : "inherit") : "#DF474F";
            curNotice.enrollnotreadShow = "none";//curNotice.isenroll ? "inline-block" : "none";
            var endstatus = curNotice.enrolltime ? DateCompare(curNotice.curtimestamp, curNotice.enrolltime) : true;//当前时间与截止时间相比较
            if(curNotice.fromnoticeid || endstatus || parent.istuoyou()){
                curNotice.forwardshow = 'none';
            }
            curNotice.enrollNumShow = "none";//curNotice.isenroll ? "inline-block" : "none";
            if(curNotice.isenroll){//允许报名，且不是托幼机构登录的
                curNotice.noticeenrolltime = makeEnrollTem(curNotice);
                // if(parent.istuoyou()){
                //     curNotice.subnoticeusers = "none";
                // }
            }else{
                curNotice.noticeenrolltime = "";
                curNotice.subnoticeusers = "none";
            }

            curNotice.printcodeshow = "none";
            curNotice.wxsignNumShow = "none";
            curNotice.totypename = objdata.totypename[curNotice.totype] || "";

            // curNotice.readstatus = curNotice.isread != "0" ? "stat-mark" : "unread-mark";
            if (curNotice.isread !== "0") {
                tempTem = templete.noticeListTem.replace("{{statusIconTem}}", '<i class="stat-mark" data-isread="' + curNotice.isread + '"></i>')
            } else {
                tempTem = templete.noticeListTem.replace("{{statusIconTem}}", '<i data-isread="0" class="stat-mark unread-mark"></i>')
            }
            arrHtml.push(renderTemp(tempTem.replace("{{statusIconTem}}", ""), curNotice));
        }
        $("#js-receive").children().eq(0).html(arrHtml.join(""));
        $("#js-receive .js-read-info .btnlb").hide();
    });
}
//公告分页底部组件
/**
 * 分页组件 UI基于layUI框架
 * @param param {Object} 分页有关统计参数
 *      param.total {Number} 总条数
 *      param.pagecount {Number} 总页数
 * @param eleId {String} 页码组件box选择器
 */
function handlePage(param, eleId) {
    var pageCount = param.pagecount,
        domPage = $(eleId);
    if (pageCount <= 1) {
        domPage.html("").hide();
        return;
    }

    function init() {
        initPageUI();
        changePage();
    }

    //初次生成分页UI
    function initPageUI() {
        var arrHtml = ['<a class="qr-prev">上一页</a>', '<a class="selected">1</a>', '<em style="display: none;">...</em>'];
        for (var i = 2; i <= pageCount; i++) {
            //    页数大于6时，从第四页开始直到倒数第三页，中间的页码都用一个 ... 表示
            if (pageCount >= 6) {
                if (i === 4) {
                    arrHtml.push('<em>...</em>');
                } else if (i > 4 && i < pageCount - 1) {
                    continue;
                } else if (i === pageCount) {
                    arrHtml.push('<em style="display: none;">...</em>');
                    arrHtml.push('<a>' + i + '</a>');
                } else {
                    arrHtml.push('<a>' + i + '</a>');
                }
            } else {
                arrHtml.push('<a>' + i + '</a>');
            }
        }
        arrHtml.push('<a class="qr-next">' + '下一页' + '</a>');
        arrHtml.push('<em>共' + pageCount + '页</em>');
        //    直接跳转到具体某一页
        arrHtml.push('<span class="pages-skip">转到第<input type="text">页<a class="skip-sure">确定</a></span>');
        domPage.html(arrHtml.join("")).show();
        domPage.attr("data-pageCount", pageCount);
        domPage.attr("data-pageCur", "1");
    }

//    页码切换
    function changePage() {
        domPage.off("click", "a").on("click", "a", function (ev) {
            var tar = ev.target || ev.srcElement,
                tarClass = $(tar).prop("class"),
                pageCur = Number(domPage.attr("data-pageCur"));
            switch (tarClass) {
                case "" : {
                    //    点击某一个页码
                    var tmpCurPage = Number($(tar).text());
                    renderPage(tmpCurPage, pageCount);
                    if (objdata.curType === "sent") {
                        handleSent(tmpCurPage - 1, true);
                    } else if (objdata.curType === "saved") {
                        handleSaved(tmpCurPage - 1, true);
                    } else if (objdata.curType === "receive") {
                        handleReceived(tmpCurPage - 1, true);
                    }
                    break;
                }
                case "qr-prev" : {
                    //    上一页
                    if (pageCur === 1) {
                        return;
                    }
                    renderPage(pageCur - 1, pageCount);
                    if (objdata.curType === "sent") {
                        handleSent(pageCur - 2, true);
                    } else if (objdata.curType === "saved") {
                        handleSaved(pageCur - 2, true);
                    } else if (objdata.curType === "receive") {
                        handleReceived(pageCur - 2, true);
                    }
                    break;
                }
                case "qr-next" : {
                    //    下一页
                    if (pageCur === pageCount) {
                        return;
                    }
                    renderPage(pageCur + 1, pageCount);
                    if (objdata.curType === "sent") {
                        handleSent(pageCur, true);
                    } else if (objdata.curType === "saved") {
                        handleSaved(pageCur, true);
                    } else if (objdata.curType === "receive") {
                        handleReceived(pageCur, true);
                    }
                    break;
                }
                case "skip-sure" : {
                    //    输入框输入具体某一页
                    var inputPage = Number($.trim($(tar).prev().val()));
                    if (isNaN(inputPage) || inputPage === pageCur || inputPage > param.pagecount || inputPage < 1) {
                        layer.msg("输入页码不合法");
                        return
                    }
                    renderPage(inputPage, pageCount);
                    if (objdata.curType === "sent") {
                        handleSent(inputPage - 1, true);
                    } else if (objdata.curType === "saved") {
                        handleSaved(inputPage - 1, true);
                    } else if (objdata.curType === "receive") {
                        handleReceived(inputPage - 1, true);
                    }
                    break;
                }
                case "selected" : {
                    //    当前页
                    return;
                }
            }
        });
    }

    /**
     * 切换页码时重新渲染分页UI
     * @param index {Number} 要展现的页码
     * @param pageCount {Number} 页码总数
     */
    function renderPage(index, pageCount) {
        var arrHtml = ['<a class="qr-prev">上一页</a>', '<a>1</a>'],
            domPageChi = domPage.children(),
            replaceEle = domPageChi.slice(0, domPageChi.length - 3);
        domPage.attr("data-pageCur", index);
        //默认大于等于6时出现省略号
        if (pageCount < 6) {
            domPageChi.removeClass("selected");
            for (var i = 0; i < domPageChi.length; i++) {
                if (domPageChi.eq(i).text() === index.toString()) {
                    domPageChi.eq(i).addClass("selected");
                    break;
                }
            }
            return;
        } else if (pageCount === 6) {
            //始终只有一个...出现
            if (index < 3) {
                arrHtml.pop();
                for (var i = 1; i <= pageCount; i++) {
                    if (i === 4) {
                        arrHtml.push('<em>...</em>');
                    } else if (i > 4 && i < pageCount - 1) {
                        continue;
                    } else {
                        i === index ? (arrHtml.push('<a class="selected">' + i + '</a>')) : (arrHtml.push('<a>' + i + '</a>'));
                    }
                }
            } else if (index === 3) {
                //    ...出现在后部分
                for (var i = 2; i <= index + 1; i++) {
                    i === index ? (arrHtml.push('<a class="selected">' + i + '</a>')) : (arrHtml.push('<a>' + i + '</a>'));
                }
                arrHtml.push('<em>...</em>');
                arrHtml.push('<a>' + pageCount + '</a>');
            } else {
                //    ...出现在前部分
                arrHtml.push('<em>...</em>');
                for (var i = pageCount - 3; i <= pageCount; i++) {
                    i === index ? (arrHtml.push('<a class="selected">' + i + '</a>')) : (arrHtml.push('<a>' + i + '</a>'));
                }
            }
        } else {
            // 两个...和一个...情况二选一
            if (index < 3) {
                arrHtml.pop();
                for (var i = 1; i <= pageCount; i++) {
                    if (i === 4) {
                        arrHtml.push('<em>...</em>');
                    } else if (i > 4 && i < pageCount - 1) {
                        continue;
                    } else {
                        i === index ? (arrHtml.push('<a class="selected">' + i + '</a>')) : (arrHtml.push('<a>' + i + '</a>'));
                    }
                }
            } else if (index === 3) {
                //    ...出现在后部分
                for (var i = 2; i <= index + 1; i++) {
                    i === index ? (arrHtml.push('<a class="selected">' + i + '</a>')) : (arrHtml.push('<a>' + i + '</a>'));
                }
                arrHtml.push('<em>...</em>');
                arrHtml.push('<a>' + pageCount + '</a>');
            } else if (index >= pageCount - 2) {
                //    ...出现在前部分
                arrHtml.push('<em>...</em>');
                for (var i = pageCount - 3; i <= pageCount; i++) {
                    i === index ? (arrHtml.push('<a class="selected">' + i + '</a>')) : (arrHtml.push('<a>' + i + '</a>'));
                }
            } else {
                //    出现两个...
                arrHtml.push('<em>...</em>');
                arrHtml.push('<a>' + (index - 1) + '</a>');
                arrHtml.push('<a class="selected">' + index + '</a>');
                arrHtml.push('<a>' + (index + 1) + '</a>');
                arrHtml.push('<em>...</em>');
                arrHtml.push('<a>' + pageCount + '</a>');
            }
        }
        replaceEle.remove();
        replaceEle = null;
        domPage.prepend(arrHtml.join(""));
    }

    init();
}

function groupDataById(arrData) {
    var obj = {};
    for (var i = 0; i < arrData.length; i++) {
        var cur = arrData[i];
        obj[cur.id] = cur.uname;
    }
    return obj;
}

function retTime(enT) {
    return '<i>' + enT.getFullYear() + '</i>' + '-<i>' + (enT.getMonth() + 1) + '</i>' + '-<i>' + enT.getDate() + '</i>' + ' <i>' + enT.getHours() + '时</i>' + '<i>' + enT.getMinutes() + '分</i>';
}

function openenrolllist(noticeid, type) {
    var arrbtn = ["提交", "取消"];
    var curNotice = objdata.receivenotice[noticeid];
    var endstatus = DateCompare(curNotice.curtimestamp, curNotice.enrolltime);//当前时间与截止时间相比较
    if ((type && type == "detail") || endstatus) {//只查看
        arrbtn = ["关闭"];
    }
    jQuery.getparent().layer.open({
        type: 2,
        title: '填写报名信息',
        shadeClose: false,
        area: ['100%', '100%'],
        content: 'html/fuyouenrolllist.html?v=' + (Arg("v") || 1) + '&mid=' + Arg("mid") + "&noticeid=" + noticeid + "&type=" + type + "&noticeflag=" + Arg("type") + "&comefrom=" + Arg("comefrom"),
        btn: arrbtn,
        yes: function (index, layero) { //或者使用btn1
            if((type && type == "detail") || endstatus){
                jQuery.getparent().layer.close(index);
            }else{
                // parent.layer.confirm("确定要提交当前报名名单吗？", function (index2) {
                    layero.find('iframe')[0].contentWindow.subEnroll(function () {
                        jQuery.getparent().layer.close(index);
                        parent.objdata.noticedetailindex ? jQuery.getparent().layer.close(parent.objdata.noticedetailindex) : "";
                        location.reload();
                    });
                    // parent.layer.close(index2);
                // });
            }
        }
    });
}

function getweidunum(){
    var noreadnum = 0;
    if(Arg("type") == 1){
        noreadnum = parent.$("#msgtotalnum").attr("ptnoticenum");
    }else if(Arg("type") == 2){
        noreadnum = parent.$("#msgtotalnum").attr("pxnoticenum");
    }else{
        noreadnum = parent.$("#msgtotalnum").html();
    }
    noreadnum > 0 ? $("#receivenum").show().html(noreadnum) : $("#receivenum").hide();
}