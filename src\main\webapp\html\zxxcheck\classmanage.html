<!DOCTYPE html>
<html>
<head>
    <title>年度设置</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="../../styles/xbcomstyle.css">
    <link rel="stylesheet" href="../../styles/tbstyles.css"/>
    <link rel="stylesheet" href="../../css/reset.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">

    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + top.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <style type="text/css">
        html, body {
            background: #EAEFF3;
            overflow: hidden;
        }

        #btnadd  {
            color: white;
        }

        #btnreset {
            color: white;
        }
        .legend_bjcyadd{line-height: 39px;}
        .left-tit{top:12px}
        /*按钮被禁用时的样式*/
        .layui-btn.disabled-btn {
            /* 降低亮度的样式，例如改变背景色、文字颜色、透明度等 */
            background-color: #ccc;
            color: #888;
            opacity: 0.6; /* 透明度 */
            cursor: not-allowed; /* 禁用时的鼠标样式 */
        }

        .layui-table-view .layui-table th{border-color: #eee;}

    </style>
</head>
<body>
<div class="marmain" style="min-width: 950px;">
    <div class="content-medical">
        <div class="layui-form" style="padding: 10px 10px 5px 10px;">
            <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                <label class="layui-form-label" style="padding: 8px 0px 5px 10px; text-align: left;">学年：</label>
                <div class="layui-input-inline" style="min-height: 30px;width:200px;">
                    <select lay-filter="schoolyear" id="schoolyear" lay-search>
                    </select>
                </div>
            </div>
            <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                <label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 10px;line-height: 28px;">班级名称：</label>
                <div class="layui-input-inline" style="min-height: 30px;width:128px;">
                    <select lay-filter="claname" id="claname" lay-search>
                        <option value="">请选择</option>
                    </select>
                </div>
            </div>
            <button id="btnsearch" class="layui-btn form-search" style="vertical-align: top;">查询</button>
            <button id="btnadd" class="layui-btn form-search" style="vertical-align: top;">添加</button>
            <button id="btnimportclass" class="layui-btn form-search" style="vertical-align: top;">导入班级</button>
        </div>
    </div>
    <div id="content" style="width: 100%;">
        <div class="marmain-cen">
            <div class="content-medical" id="divtable">
                <table id="laytable" lay-filter="laytable"></table>
            </div>
        </div>
    </div>
</div>
<script data-main="../../js/zxxcheck/classmanage" src='../../sys/require.min.js'></script>
</body>
</html>
