// 2016.04.11
// 郭玉峰
require.config({
    paths: {
        system: '../sys/system',
        jquery: '../sys/jquery'
    },
    waitSeconds: 0

});
var objdata = {};
require(['jquery', 'system'], function () {
    var frompagename = Arg("frompagename");
    var fromindex = parent.layer.getFrameIndex(frompagename);
    var body =  parent.layer.getChildFrame('body', fromindex);
    var _doms = body.find("#uldesktab").children();
    var arrdeskdata = [];
    _doms.each(function () {
        var menukey = $(this).data('menukey');
        if(menukey){
            arrdeskdata.push({
                menukey:menukey
            })
        } else {
            var menuid = $(this).data('menuid');
            if(menuid){
                arrdeskdata.push({
                    menuid:menuid
                })
            }
        }
    })

    //var arrdeskdata = parent.objdata.objmy.arrdeskdata;
    for (var i = 0; i < arrdeskdata.length; i++) {
        if(arrdeskdata[i].menukey){
            $("#uldefault").children('[type="'+ arrdeskdata[i].menukey +'"]').remove();
        }
    }
    var strhtml = '';
    for (var i = 0; i < parent.objdata.arrgroupsort.length; i++) {
        var curmenu = parent.objdata.objMenu[parent.objdata.arrgroupsort[i]];
        if (curmenu) {
            var ischeckd = false;
            for (var j = 0; j < arrdeskdata.length; j++) {
                if(arrdeskdata[j].menuid&&arrdeskdata[j].menuid==curmenu[0]){
                    ischeckd = true;
                    break;
                }
            }
            if(!ischeckd){
                strhtml += '<li mid="' + curmenu[0] + '"><img src="../' + curmenu[8] + '" alt=""/>' + curmenu[2] + ' <label class="set-checkbox-style"><input type="checkbox"></label></li>';
            }
        }
    }
    $("#ulmenu").html(strhtml)
    $("#uldefault,#ulmenu").on('click','li',function (e) {
        if($(e.target).is('input')){
            return;
        }
        $(this).find('input').prop('checked',!$(this).find('input').prop('checked'));
    })
    
});
function btnok(cb) {
    var arrkey = [];
    $("#uldefault").find('input:checkbox:checked').each(function () {
        var obj = {
            menukey:$(this).parent().parent().attr('type'),
            image:$(this).parent().parent().find('img').attr('src'),
            name:$(this).parent().parent().text()
        }
        arrkey.push(obj);
    })
    var arrid = [];
    $("#ulmenu").find('input:checkbox:checked').each(function () {
        var obj = {
            menuid:$(this).parent().parent().attr('mid'),
            image:$(this).parent().parent().find('img').attr('src'),
            name:$(this).parent().parent().text()
        }
        arrid.push(obj);
    })
    cb(arrkey,arrid);
}