<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>矢量图标库</title>
    <meta name="keywords" content="幼儿健康大数据管理平台"/>
    <meta name="description" content="幼儿健康大数据管理平台"/>
    <meta name="Author" content="larry"/>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="Shortcut Icon" href="/favicon.ico"/>
    <!-- load css -->
    <link rel="stylesheet" type="text/css" href="../layui-btkj/css/layui.css" media="all">
    <link rel="stylesheet" type="text/css" href="//at.alicdn.com/t/font_bmgv5kod196q1tt9.css">
    <link rel="stylesheet" type="text/css" href="../css/backstage.css" media="all">
    <link rel="stylesheet" type="text/css" href="../css/style.css">
    <link rel="stylesheet" type="text/css" href="../css/icon.css">
    <style>
        .site-doc-icon{margin-bottom: 50px; font-size: 0;/*text-align: center;*/margin: 20px;}
        .site-doc-icon li{display: inline-block; vertical-align: middle; width: 127px; line-height: 25px; padding: 20px 0; margin-right: -1px; margin-bottom: -1px; border: 1px solid #e2e2e2; font-size: 14px; text-align: center; color: #666; transition: all .3s; -webkit-transition: all .3s;}
        .site-doc-icon li .layui-icon{display: inline-block; font-size: 36px;}
        .site-doc-icon li .fontclass{display: none;}
        .site-doc-icon li .name{color: #c2c2c2;}
        .site-doc-icon li:hover{background-color: #f2f2f2; color: #000;}
    </style>
</head>
<body>
<ul class="site-doc-icon">
    <li ico="#xe66f;">
        <i class="iconfont">&#xe66f;</i>
        <div class="name">常用功能</div>
        <div class="code">&amp;#xe66f;</div>
    </li>
    <li ico="#xe6b9;">
        <i class="iconfont">&#xe6b9;</i>
        <div class="name">数据展示</div>
        <div class="code">&amp;#xe6b9;</div>
    </li>
    <li ico="#xe653;">
        <i class="iconfont">&#xe653;</i>
        <div class="name">幼儿园管理</div>
        <div class="code">&amp;#xe653;</div>
    </li>
    <li ico="#xe685;">
        <i class="iconfont">&#xe685;</i>
        <div class="name">统计分析</div>
        <div class="code">&amp;#xe685;</div>
    </li>
    <li ico="#xe705;">
        <i class="iconfont">&#xe705;</i>
        <div class="name">系统管理</div>
        <div class="code">&amp;#xe705;</div>
    </li>
    <li ico="#xe654;">
        <i class="iconfont">&#xe654;</i>
        <div class="name">栏目管理</div>
        <div class="code">&amp;#xe654;</div>
    </li>
    <li ico="#xe73e;">
        <i class="iconfont">&#xe73e;</i>
        <div class="name">膳食健康大数据</div>
        <div class="code">&amp;#xe73e;</div>
    </li>
    <li ico="#xe64c;">
        <i class="iconfont">&#xe64c;</i>
        <div class="name">疾病健康大数据</div>
        <div class="code">&amp;#xe64c;</div>
    </li>
    <li ico="#xe60a;">
        <i class="iconfont">&#xe60a;</i>
        <div class="name">幼儿园分布图</div>
        <div class="code">&amp;#xe60a;</div>
    </li>
    <li ico="#xe600;">
        <i class="iconfont">&#xe600;</i>
        <div class="name">区域管理</div>
        <div class="code">&amp;#xe600;</div>
    </li>
    <li ico="#xe6af;">
        <i class="iconfont">&#xe6af;</i>
        <div class="name">展示页面</div>
        <div class="code">&amp;#xe6af;</div>
    </li>
    <li ico="#xe694;">
        <i class="iconfont">&#xe694;</i>
        <div class="name">工作台</div>
        <div class="code">&amp;#xe694;</div>
    </li>
    <li ico="#xe66d;">
        <i class="iconfont">&#xe66d;</i>
        <div class="name">用户管理</div>
        <div class="code">&amp;#xe66d;</div>
    </li>
    <li ico="#xe648;">
        <i class="iconfont">&#xe648;</i>
        <div class="name">角色管理</div>
        <div class="code">&amp;#xe648;</div>
    </li>
    <li ico="#xe605;">
        <i class="iconfont">&#xe605;</i>
        <div class="name">数据字典</div>
        <div class="code">&amp;#xe605;</div>
    </li>
    <li ico="#xe63a;">
        <i class="iconfont">&#xe63a;</i>
        <div class="name">修改密码</div>
        <div class="code">&amp;#xe63a;</div>
    </li>
    <li ico="#xe640;">
        <i class="iconfont">&#xe640;</i>
        <div class="name">日历</div>
        <div class="code">&amp;#xe640;</div>
    </li>
    <li ico="#xe980;">
        <i class="iconfont">&#xe980;</i>
        <div class="name">测试数据页面</div>
        <div class="code">&amp;#xe980;</div>
    </li>
    <li ico="#xe66c;">
        <i class="iconfont">&#xe66c;</i>
        <div class="name">通知公告</div>
        <div class="code">&amp;#xe66c;</div>
    </li>
    <li ico="#xe6da;">
        <i class="iconfont">&#xe6da;</i>
        <div class="name">系统设置</div>
        <div class="code">&amp;#xe6da;</div>
    </li>
    <li ico="#xe61c;">
        <i class="iconfont">&#xe61c;</i>
        <div class="name">chdc设置</div>
        <div class="code">&amp;#xe61c;</div>
    </li>
    <li ico="#xe625;">
        <i class="iconfont">&#xe625;</i>
        <div class="name">消息中心</div>
        <div class="code">&amp;#xe625;</div>
    </li>
</ul>
<script type="text/javascript" src="../sys/jquery.js"></script>
<script type="text/javascript" src="../sys/arg.js"></script>
<script>
    var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
    var type = Arg("type");
    $(document).ready(function () {
        $('.site-doc-icon li').on('click', function () {
            var ico = $(this).attr("ico");
            var win = parent.$("#win_" + window.Arg("mid")).find('iframe')[0].contentWindow;
            if (type=="lanmu"){
                win.$("#txtmicon").val(ico);
                win.$("#little").html("&"+ico);
            }else if(type=="lanmuzu"){
                win.$("#txtlmzICON").val(ico);
                win.$("#big").html("&"+ico);
            }
            parent.layer.close(index);
        });
    });
</script>
</body>
</html>
