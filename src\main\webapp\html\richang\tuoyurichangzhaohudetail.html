﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <link rel="shortcut icon" href="/images/favicon.ico" type="image/x-icon" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="Copyright" content="Copyright 2016 by tongbang" />
    <meta name="Author" content="" />
    <meta name="Robots" content="All" />
    <title>表1-2托育机构日常照护记录表</title>
    <link href="../../styles/admin.css" rel="stylesheet" type="text/css" />
    <link href="../../styles/tbstyles.css" type="text/css" rel="stylesheet" />
    <link href="../../plugin/ehaiform/css/tbbaseform.css" rel="stylesheet" type="text/css" />
    <link href="../../plugin/editselect/css/editselect.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../plugin/jquery_tabs/css/tbjquery.tabs.css" type="text/css"
          media="print, projection, screen" />
	<style>

		.rytjdjedit_table td{border:#eee solid 1px}
        /*滚动条样式*/
        html{
            color: #000;
            font-size: .7rem;
            font-family: "\5FAE\8F6F\96C5\9ED1",Helvetica,"黑体",Arial,Tahoma;
            height: 100%;
        }
        /*滚动条样式*/
        html::-webkit-scrollbar {
            display:none;
            width: 6px;
            height: 6px;
        }
        html:hover::-webkit-scrollbar{
            display:block;
        }
        html::-webkit-scrollbar-thumb {
            border-radius: 3px;
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
            border: 1px solid rgb(255, 255, 255);
            background: rgb(66, 66, 66);
        }
        html::-webkit-scrollbar-track {
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
            border-radius: 0;
            background: rgba(0,0,0,0.1);
        }
        .full_disease {
            display: inline-block;
            background: #eee;
            padding:0 10px;
            width:60px;
            text-align: center;
        }
		.wftflist .layui-form-label{ width: 99px; text-align: left;
}
	</style>
</head>
<body>
    <div class="bodywidth" style="min-width: 920px;">
        <div class="content">
            <div class="layui-form layui-comselect" style="position: relative;padding: 10px 10px 5px 10px;">
                <div style="margin:10px 0;">
                    <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                        <label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 10px;line-height: 28px;">日期：</label>
                        <div class="layui-input-inline" style="min-height: 30px;width:180px;">
                            <input id="recordtime" type="text" placeholder="日期" readonly class="layui-input"/>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                        <label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 10px;line-height: 28px;">班级：</label>
                        <div class="layui-input-inline" style="min-height: 30px;width:180px;">
                            <select lay-filter="sel_classno" id="classno">
                                <option value="">请选择</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                        <label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 10px;line-height: 28px;">幼儿：</label>
                        <div class="layui-input-inline" style="min-height: 30px;width:180px;">
                            <select lay-filter="sel_stuno" id="stuno">
                                <option value="">请选择</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div id="eat" style="margin: 10px 0;" class="wftflist">
                    <h6 style="padding-left:10px;">进食：</h6>
<!--                    <div class="eat_item">-->
<!--                        <div class="layui-form-item" style="display: inline-block;vertical-align: top;">-->
<!--                            <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">进食餐次：</label>-->
<!--                            <div class="layui-input-inline" style="min-height: 30px;width:180px;">-->
<!--                                <select id="eat_canbie_0">-->
<!--                                    <option value="">请选择</option>-->
<!--                                    <option value="早餐">早餐</option>-->
<!--                                    <option value="早点">早点</option>-->
<!--                                    <option value="午餐">午餐</option>-->
<!--                                    <option value="午点">午点</option>-->
<!--                                    <option value="晚餐">晚餐</option>-->
<!--                                </select>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                        <div class="layui-form-item" style="display: inline-block;vertical-align: top;">-->
<!--                            <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">进食时间：</label>-->
<!--                            <div class="layui-input-inline" style="min-height: 30px;width:180px;">-->
<!--                                <div class="rytj_tz">-->
<!--                                    <input type="text" class="txtweight" id="eat_time_0" readonly maxlength="20" style="width: 95%;"/>-->
<!--                                    <span class="FancyInput__bar___1P3wW"></span>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                        <div class="layui-form-item" style="display: inline-block;vertical-align: top;">-->
<!--                            <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">进食情况：</label>-->
<!--                            <div class="layui-input-inline" style="min-height: 30px;width:300px;padding-top:6px;">-->
<!--                                <input type="radio" name="eat_text_0" value="正常" title="正常" lay-filter="eat_text_0">-->
<!--                                <input type="radio" name="eat_text_0" value="不正常" title="不正常" lay-filter="eat_text_0">-->
<!--                                <div class="rytj_tz" style="display: inline-block;width: 50%">-->
<!--                                    <input type="text" class="txtweight" id="eat_text_0" maxlength="20" style="width: 95%;display: none;"/>-->
<!--                                    <span class="FancyInput__bar___1P3wW"></span>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
                </div>
                <button id="btneat" class="layui-btn form-search" style="vertical-align: top;margin-left:10px;">添加进食</button>
                <div id="sleep" style="margin: 10px 0;" class="wftflist">
                    <h6 style="padding-left:10px;">睡眠：</h6>
<!--                    <div class="sleep_item">-->
<!--                        <div class="layui-form-item" style="display: inline-block;vertical-align: top;">-->
<!--                            <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">睡眠时间：</label>-->
<!--                            <div class="layui-input-inline" style="min-height: 30px;width:180px;">-->
<!--                                <div class="rytj_tz">-->
<!--                                    <input type="text" class="txtweight" id="sleep_time_0" readonly maxlength="20" style="width: 95%;"/>-->
<!--                                    <span class="FancyInput__bar___1P3wW" ></span>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                        <div class="layui-form-item" style="display: inline-block;vertical-align: top;">-->
<!--                            <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">时长：</label>-->
<!--                            <div class="layui-input-inline" style="min-height: 30px;width:180px;">-->
<!--                                <div class="rytj_tz">-->
<!--                                    <input type="text" class="txtweight" id="sleep_duration_0" maxlength="20" style="width: 95%;"/>-->
<!--                                    <span class="FancyInput__bar___1P3wW" ></span>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
                </div>
                <button id="btnsleep" class="layui-btn form-search" style="vertical-align: top;margin-left:10px;">添加睡眠</button>
                <div id="active" style="margin: 10px 0;" class="wftflist">
                    <h6 style="padding-left:10px;">户外活动：</h6>
<!--                    <div class="active_item">-->
<!--                        <div class="layui-form-item" style="display: inline-block;vertical-align: top;">-->
<!--                            <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">户外活动时间：</label>-->
<!--                            <div class="layui-input-inline" style="min-height: 30px;width:180px;">-->
<!--                                <div class="rytj_tz">-->
<!--                                    <input type="text" class="txtweight" id="active_time_0" readonly maxlength="20" style="width: 95%;"/>-->
<!--                                    <span class="FancyInput__bar___1P3wW" ></span>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                        <div class="layui-form-item" style="display: inline-block;vertical-align: top;">-->
<!--                            <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">时长：</label>-->
<!--                            <div class="layui-input-inline" style="min-height: 30px;width:180px;">-->
<!--                                <div class="rytj_tz">-->
<!--                                    <input type="text" class="txtweight" id="active_duration_0" maxlength="20" style="width: 95%;"/>-->
<!--                                    <span class="FancyInput__bar___1P3wW" ></span>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
                </div>
                <button id="btnactive" class="layui-btn form-search" style="vertical-align: top;margin-left:10px;">添加户外活动</button>
                <div id="stool" style="margin: 10px 0;" class="wftflist">
                    <h6 style="padding-left:10px;">大便：</h6>
<!--                    <div class="stool_item">-->
<!--                        <div class="layui-form-item" style="display: inline-block;vertical-align: top;">-->
<!--                            <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">大便时间：</label>-->
<!--                            <div class="layui-input-inline" style="min-height: 30px;width:180px;">-->
<!--                                <div class="rytj_tz">-->
<!--                                    <input type="text" class="txtweight" id="stool_time_0" readonly maxlength="20" style="width: 95%;"/>-->
<!--                                    <span class="FancyInput__bar___1P3wW" ></span>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                        <div class="layui-form-item" style="display: inline-block;vertical-align: top;">-->
<!--                            <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">性状：</label>-->
<!--                            <div class="layui-input-inline" style="min-height: 30px;width:300px;padding-top:6px;">-->
<!--                                <input type="radio" name="stool_text" value="正常" title="正常" lay-filter="stool_text_0">-->
<!--                                <input type="radio" name="stool_text" value="不正常" title="不正常" lay-filter="stool_text_0">-->
<!--                                <div class="rytj_tz" style="display: inline-block;width: 50%">-->
<!--                                    <input type="text" class="txtweight" id="stool_text_0" maxlength="20" style="width: 95%;display: none;"/>-->
<!--                                    <span class="FancyInput__bar___1P3wW" ></span>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
                </div>
                <button id="btnstool" class="layui-btn form-search" style="vertical-align: top;margin-left:10px;">添加大便</button>
                <div id="urinate" style="margin: 10px 0;" class="wftflist">
                    <h6 style="padding-left:10px;">小便：</h6>
<!--                    <div class="urinate_item">-->
<!--                        <div class="layui-form-item" style="display: inline-block;vertical-align: top;">-->
<!--                            <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">小便时间：</label>-->
<!--                            <div class="layui-input-inline" style="min-height: 30px;width:180px;">-->
<!--                                <div class="rytj_tz">-->
<!--                                    <input type="text" class="txtweight" id="urinate_time_0" readonly maxlength="20" style="width: 95%;"/>-->
<!--                                    <span class="FancyInput__bar___1P3wW" ></span>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                        <div class="layui-form-item" style="display: inline-block;vertical-align: top;">-->
<!--                            <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">颜色：</label>-->
<!--                            <div class="layui-input-inline" style="min-height: 30px;width:300px;padding-top:6px;">-->
<!--                                <input type="radio" name="urinate_text_0" value="正常" title="正常" lay-filter="urinate_text_0">-->
<!--                                <input type="radio" name="urinate_text_0" value="不正常" title="不正常" lay-filter="urinate_text_0">-->
<!--                                <div class="rytj_tz" style="display: inline-block;width: 50%">-->
<!--                                    <input type="text" class="txtweight" id="urinate_text" maxlength="20" style="width: 95%;display: none;"/>-->
<!--                                    <span class="FancyInput__bar___1P3wW" ></span>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
                </div>
                <button id="btnurinate" class="layui-btn form-search" style="vertical-align: top;margin-left:10px;">添加小便</button>
                <div id="drink" style="margin: 10px 0;" class="wftflist">
                    <h6 style="padding-left:10px;">饮水：</h6>
<!--                    <div class="drink_item">-->
<!--                        <div class="layui-form-item" style="display: inline-block;vertical-align: top;">-->
<!--                            <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">饮水时间：</label>-->
<!--                            <div class="layui-input-inline" style="min-height: 30px;width:180px;">-->
<!--                                <div class="rytj_tz">-->
<!--                                    <input type="text" class="txtweight" id="drink_time_0" readonly maxlength="20" style="width: 95%;"/>-->
<!--                                    <span class="FancyInput__bar___1P3wW" ></span>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                        <div class="layui-form-item" style="display: inline-block;vertical-align: top;">-->
<!--                            <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">量：</label>-->
<!--                            <div class="layui-input-inline" style="min-height: 30px;width:180px;">-->
<!--                                <div class="rytj_tz">-->
<!--                                    <input type="text" class="txtweight" id="drink_num_0" maxlength="20" style="width: 95%;"/>-->
<!--                                    <span class="FancyInput__bar___1P3wW" ></span>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
                </div>
                <button id="btndrink" class="layui-btn form-search" style="vertical-align: top;margin-left:10px;">添加饮水</button>
                <div id="tiwen" style="margin: 10px 0;" class="wftflist">
                    <h6 style="padding-left:10px;">体温：</h6>
<!--                    <div class="tiwen_item">-->
<!--                        <div class="layui-form-item" style="display: inline-block;vertical-align: top;">-->
<!--                            <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">测量时间：</label>-->
<!--                            <div class="layui-input-inline" style="min-height: 30px;width:180px;">-->
<!--                                <div class="rytj_tz">-->
<!--                                    <input type="text" class="txtweight" id="tiwen_time_0" readonly maxlength="20" style="width: 95%;"/>-->
<!--                                    <span class="FancyInput__bar___1P3wW" ></span>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                        <div class="layui-form-item" style="display: inline-block;vertical-align: top;">-->
<!--                            <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">数值：</label>-->
<!--                            <div class="layui-input-inline" style="min-height: 30px;width:180px;">-->
<!--                                <div class="rytj_tz">-->
<!--                                    <input type="text" class="txtweight" id="tiwen_num_0" maxlength="20" style="width: 95%;"/>-->
<!--                                    <span class="FancyInput__bar___1P3wW" ></span>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
                </div>
                <button id="btntiwen" class="layui-btn form-search" style="vertical-align: top;margin-left:10px;">添加体温</button>
                <div class="layui-form-item" style="display: block;vertical-align: top;">
                    <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">其他：</label>
                    <div class="layui-input-inline" style="min-height: 30px;width:500px;">
                        <div class="rytj_tz">
                            <input type="text" class="txtweight" id="other" placeholder="其他是指精神、身体、行为、意外伤害等异常" maxlength="20" style="width: 95%;"/>
                            <span class="FancyInput__bar___1P3wW" ></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript">
        var v = top.version;
        document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../sys/function.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../js/richang/tuoyucommon.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../js/richang/tuoyurichangzhaohudetail.js?v=" + v + "'><" + "/script>");
    </script>
</body>
</html>
