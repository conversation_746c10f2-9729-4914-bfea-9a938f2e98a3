/*新增日期: 2025.06.18
作 者: 朱辽原
修改时间：
修改人：
內容摘要: 家长预约大数据数据来源（ mockjs ）
*/
require.config({
    paths: {
        mock: '../../plugin/mock/dist/mock',
        jquery: '../../sys/jquery',
        dataSource: './enterData',
        system: '../../sys/system',
        commonUtils: './commonUtils'
    },
    shim: {
        system: {
            deps: ['jquery']
        },
        dataSource: {
            deps: ['system', 'jquery', 'mock']
        }
    },
    waitSeconds: 0
})

require(['jquery', 'mock', 'commonUtils', 'dataSource', 'system'], ($, Mock, utils) => {
    const openLog = true
    // 启用请求拦截
    Mock.setup({
        timeout: '200-600' // 模拟网络延迟
    })

    /**
     * 统计卡片
     */
    Mock.mock(/\/bianminService\/stats/, 'post', function (option) {
        utils.logWithCondition(openLog, '拦截到 POST 请求:', option)
        let body = option.body
        if (typeof body == 'string' ) {
            body = JSON.parse(body)
        }
        const regional = body.regional;
        let gardenList = getGardenNameList({ regional })
        var kindergartenCount = gardenList.length
        var bianminCardCount = gardenList.filter(item => item.serviceCard > 0).length
        return Mock.mock({
            code: 0,
            msg: 'success',
            data: {
                kindergartenCount, // 幼儿园数
                bianminCardCount, // 便民牌数
                parentScanCount: Mock.Random.natural(150, 250), // 家长扫码预约数
                parentCancelCount: function () {
                    return Math.round(this.parentScanCount * 0.2); // 取消数约为预约数的20%
                }, // 家长取消预约数
                avgScanCount: Mock.Random.natural(2, 5), // 家长人均扫码数
            }
        })
    })

    /**
     * 每日扫码预约趋势
     */
    Mock.mock(/\/bianminService\/openTrendData/, 'post', option => {
        utils.logWithCondition(openLog, 61, option)
        let body = option.body
        if (typeof body == 'string' ) {
            body = JSON.parse(body)
        }
        // 解析请求中的日期范围
        const startDate = new Date(body.startDate); // 2025-06-24
        const endDate = new Date(body.endDate);     // 2025-06-24
        utils.logWithCondition(openLog, 65, startDate, endDate)

        const data = {
            xAxis: [],
            yAxis: []
        }

        utils.logWithCondition(openLog, data)

        // 计算日期范围内的所有日期
        // const dates = [];
        // const values = [];
        let currentDate = new Date(startDate);

        // 计算时间跨度（天）
        const timeSpan = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
        // 决定是否需要显示年份（超过30天显示年份）
        const showYear = true;

        // 生成日期数组和对应数值
        while (currentDate <= endDate) {
            // 格式化日期
            const year = currentDate.getFullYear();
            const month = currentDate.getMonth() + 1;
            const day = currentDate.getDate();

            // 根据是否需要显示年份决定格式
            data.xAxis.push(showYear ? `${year}-${month}-${day}` : `${month}/${day}`);

            // 判断日期特征
            const dayOfWeek = currentDate.getDay();
            const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
            const isHoliday = checkIfHoliday(currentDate); // 假设节假日

            // 基础值设置
            let baseValue = 200; // 工作日默认值
            if (isWeekend) baseValue = 120; // 周末较低
            if (isHoliday) baseValue = 80;  // 节假日最低

            // 学期周期影响 (3-6月,9-12月较高)
            const isSemesterPeak = (month >= 3 && month <= 6) || (month >= 9 && month <= 12);
            if (isSemesterPeak) baseValue *= 1.2;

            // 计算趋势值 (随时间缓慢上升)
            const daysFromStart = Math.floor((currentDate - startDate) / (1000 * 60 * 60 * 24));
            const trendValue = daysFromStart * 3; // 更平缓的增长

            // 添加随机波动 (周末波动更大)
            const randomRange = isWeekend ? 30 : 15;
            const randomVariation = Mock.Random.natural(-randomRange, randomRange);

            if (currentDate > new Date()) {
                data.yAxis.push(0) // 未来日期数据为0
            } else {
                // 确保最小值合理
                data.yAxis.push(Math.max(50, baseValue + trendValue + randomVariation));
            }

            // 辅助函数 - 简单节假日判断
            function checkIfHoliday(date) {
                const monthDay = (date.getMonth() + 1) + '-' + date.getDate();
                const holidays = ['1-1', '5-1', '10-1', '10-2', '10-3']; // 元旦、劳动节、国庆节
                return holidays.includes(monthDay);
            }

            // 移动到下一天
            currentDate.setDate(currentDate.getDate() + 1);
        }
        utils.logWithCondition(openLog, data)
        return Mock.mock({
            code: 0,
            message: 'success',
            data
        });
    })
    /**
     * 分小时预约/取消趋势
     */
    Mock.mock(/\/bianminService\/getAppointTrend/, 'post', (option) => {
        let body = option.body
        if (typeof body == 'string' ) {
            body = JSON.parse(body)
        }
        // 解析请求中的日期范围
        const startDate = new Date(body.startTime);
        const endDate = new Date(body.endTime);

        // 计算日期范围内的周末天数
        let weekendDays = 0;
        let currentDate = new Date(startDate);
        while (currentDate <= endDate) {
            const day = currentDate.getDay();
            if (day === 0 || day === 6) weekendDays++;
            currentDate.setDate(currentDate.getDate() + 1);
        }
        const totalDays = Math.floor((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1;
        const weekendRatio = weekendDays / totalDays;

        // 基础时间段
        const timeSlots = [];
        for (let i = 0; i < 24; i++) {
            let time = i + ':00';
            if (i < 10) {
                time = '0' + time;
            }
            timeSlots.push(time);
        }

        // 混合模式根据周末比例
        const appointData = timeSlots.map((_, i) => {
            const baseValue = Mock.Random.natural(100, 200);
            // 添加随机波动
            return baseValue + Mock.Random.natural(-15, 15);
        });
        utils.logWithCondition(openLog, appointData)


        return Mock.mock({
            code: 0,
            message: 'success',
            data: {
                xAxis: timeSlots,
                appointData: appointData,
                cancelData: function () {
                    // 取消率在15-25%之间，周末略高
                    return this.appointData.map((val, i) => {
                        const cancelRate = 0.15 + (timeSlots[i].includes('12:00') ? 0.05 : 0) +
                            (weekendRatio > 0.3 ? 0.03 : 0);
                        return Math.round(val * (cancelRate + Math.random() * 0.05));
                    });
                }
            }
        });
    }
    )

    /**
     * 分时段预约量对比
     */
    Mock.mock(/\/bianminService\/getAppointBarData/, 'post', () => {
        const xAxis = []
        for (let i = 0; i < 24; i++) {
            let time = i + ':00';
            if (i < 10) {
                time = '0' + time;
            }
            xAxis.push(time);
        }
        const yAxis = xAxis.map(() => Mock.Random.integer(100, 200))

        return Mock.mock({
            code: 0, // 自定义业务状态码
            message: 'success',
            data: {
                xAxis, yAxis
            }
        })
    }
    )

    // 拦截性别数据请求
    Mock.mock(/\/bianminService\/gender/, 'post', () => {
        const total = Mock.Random.natural(200, 1000)
        const male = total / 2
        const female = total - male
        return Mock.mock({
            code: 0,
            message: 'success',
            data: [
                {
                    name: '男性',
                    value: Math.floor(male)
                },
                {
                    name: '女性',
                    value: Math.floor(female)
                }
            ]
        })
    })

    // 拦截设备数据请求
    Mock.mock(/\/bianminService\/device/, 'post', () => {
        return Mock.mock({
            code: 0,
            message: 'success',
            data: [
                {
                    name: '安卓',
                    value: '@integer(100, 150)'
                },
                {
                    name: 'iOS',
                    value: '@integer(100, 150)'
                },
                {
                    name: '未知',
                    value: '@integer(100, 150)'
                }
            ]
        })
    })
    // 拦截年龄数据请求
    Mock.mock(/\/bianminService\/age/, 'post', () => {
        return {
            code: 0,
            message: 'success',
            data: [
                {
                    name: '26到35岁',
                    value: Mock.Random.natural(300, 400)  // 主要年龄段
                },
                {
                    name: '36到45岁',
                    value: Mock.Random.natural(250, 350)  // 次要主要年龄段
                },
                {
                    name: '46到60岁',
                    value: Mock.Random.natural(50, 100)   // 较少
                },
                {
                    name: '18岁到25岁',
                    value: Mock.Random.natural(30, 80)     // 很少
                },
                {
                    name: '60岁以上',
                    value: Mock.Random.natural(20, 50)    // 极少
                },
                {
                    name: '未知',
                    value: Mock.Random.natural(5, 15)     // 非常少
                }
            ]
        }
    })

    // 拦截区域数据请求
    Mock.mock(/\/bianminService\/region/, 'post', () => {
        return Mock.mock({
            code: 0,
            message: 'success',
            data: {
                // 模拟不同距离范围的家长数、幼儿数
                areas: [
                    {
                        distance: '1km',
                        parentCount: '@integer(10, 50)',
                        childCount: '@integer(10, 50)'
                    },
                    {
                        distance: '3km',
                        parentCount: '@integer(30, 80)',
                        childCount: '@integer(30, 80)'
                    },
                    {
                        distance: '5km',
                        parentCount: '@integer(50, 100)',
                        childCount: '@integer(50, 100)'
                    },
                    {
                        distance: '未知',
                        parentCount: '@integer(50, 100)',
                        childCount: '@integer(50, 100)'
                    }
                ]
            }
        })
    })

    // 获取关系数据
    Mock.mock(/\/bianminService\/relationship/, 'post', () => {
        const categories = [
            '爸爸',
            '妈妈',
            '爷爷',
            '奶奶',
            '外公',
            '外婆',
            '叔叔',
            '阿姨',
            '婶婶',
            '姑妈',
            '姑父',
            '伯父',
            '伯母',
            '舅舅',
            '舅妈',
            '哥哥',
            '姐姐',
            '其他'
        ];

        const values = [
            Mock.Random.natural(180, 220),  // 爸爸
            Mock.Random.natural(200, 250),  // 妈妈
            Mock.Random.natural(30, 60),    // 爷爷
            Mock.Random.natural(40, 70),    // 奶奶
            Mock.Random.natural(25, 50),    // 外公
            Mock.Random.natural(35, 60),    // 外婆
            Mock.Random.natural(5, 15),     // 叔叔
            Mock.Random.natural(5, 15),     // 阿姨
            Mock.Random.natural(3, 10),     // 婶婶
            Mock.Random.natural(3, 10),     // 姑妈
            Mock.Random.natural(2, 8),      // 姑父
            Mock.Random.natural(2, 8),      // 伯父
            Mock.Random.natural(2, 8),      // 伯母
            Mock.Random.natural(2, 8),      // 舅舅
            Mock.Random.natural(2, 8),      // 舅妈
            Mock.Random.natural(1, 5),      // 哥哥
            Mock.Random.natural(1, 5),      // 姐姐
            Mock.Random.natural(5, 15)      // 其他
        ];
        return Mock.mock({
            code: 0,
            message: 'success',

            data: {
                xAxis: categories,
                data: values
            }
        })
    })

    // 拦截年龄数据请求
    Mock.mock(/\/bianminService\/childAge/, 'post', () => {
        return {
            code: 0,
            message: 'success',
            data: [
                {
                    name: '3岁',
                    value: Mock.Random.natural(180, 220)  // 约35%
                },
                {
                    name: '4岁',
                    value: Mock.Random.natural(200, 250)  // 约40%
                },
                {
                    name: '5岁',
                    value: Mock.Random.natural(100, 150)   // 约20%
                },
                {
                    name: '6岁',
                    value: Mock.Random.natural(30, 60)     // 约5%
                },
                {
                    name: '7岁',
                    value: Mock.Random.natural(5, 15)      // 极少
                }
            ]
        }
    })

    // 核心数据：模拟幼儿园扫码量 Top5
    Mock.mock(/\/bianminService\/scan/, 'post', (option) => {
        const regional = option.body.regional
        // 先修正之前代码中的赋值错误，添加 scanCount 字段
        let gardens = getGardenNameList({ regional });
        utils.logWithCondition(openLog, gardens)
        for (let i = 0; i < gardens.length; i++) {
            gardens[i].scanCount = Mock.Random.natural(100, 200); // 修正了赋值语句
        }

        // 按 scanCount 从大到小排序，并只取前五个
        gardens = gardens
            .sort((a, b) => a.scanCount - b.scanCount)
            .slice(0, 5);
        return {
            // 状态码，可根据业务自定义
            code: 0,
            message: 'success',

            data: {
                // 幼儿园名称
                labels: gardens.map(item => ({
                    name: item.name,
                    id: item.id
                })),
                // 对应扫码量，设置合理的基准值和差异
                values: gardens.map(item => item.scanCount)
            }
        }
    })

    Mock.mock(/\/bianminService\/bianminServiceCard/, 'post', (option) => {
        let body = option.body
        if (typeof body == 'string' ) {
            body = JSON.parse(body)
        }
        const regional = body.regional;
        const requestDate = body.date ? new Date(body.date) : new Date();
        const month = requestDate.getMonth() + 1;

        let gardenData = getGardenNameList({ regional });

        // 区域基准值（不同区域活跃度不同）
        const regionalBase = {
            '海淀区': 1.2,
            '西城区': 1.1,
            '东城区': 1.0,
            '丰台区': 0.9,
            '石景山区': 0.8,
            '通州区': 0.7
        };

        // 月份调整系数（学期中较高，寒暑假较低）
        const monthFactor = (month >= 3 && month <= 6) || (month >= 9 && month <= 12) ? 1.0 : 0.7;
        utils.logWithCondition(openLog, gardenData)

        gardenData.forEach(item => {
            // 基础值考虑区域差异和月份
            let baseValue = 150 * regionalBase[item.regional] * monthFactor;

            // 幼儿园规模差异（根据名称长度简单模拟）
            const sizeFactor = 1 - (item.name.length - 10) * 0.02;
            baseValue *= sizeFactor;

            // 添加随机波动
            const parentScan = Math.round(baseValue * (0.8 + Math.random() * 0.4));

            item.parentScanCount = parentScan;
            // 成功率75%-85%，取消率15%-25%
            item.appointmentSuccessful = Math.floor(parentScan * (0.75 + Math.random() * 0.1));
            item.appointmentCancel = Math.floor(parentScan * (0.15 + Math.random() * 0.1));

            // 添加时间戳
            item.lastUpdate = requestDate.toISOString();
        });

        return {
            code: 0,
            message: 'success',
            data: gardenData,
            meta: {
                regional,
                month,
                dataType: 'bianminServiceCard'
            }
        };
    })

    /*
    功能：生成幼儿园名称列表
    参数说明：无
    返回值说明：幼儿园名称列表
    */
    function getGardenNameList(param) {
        let garden = []
        $.sm(function (re1, err1) {
            garden = re1
        }, ["garden.list", JSON.stringify(param)], null, null, { async: false });
        return garden
    }

})