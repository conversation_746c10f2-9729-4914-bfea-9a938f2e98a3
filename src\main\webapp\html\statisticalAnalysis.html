﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>填写上报报表</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <link rel="stylesheet" href="../css/reset.css" />
    <link href='../css/style.css' rel='stylesheet' />
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../css/icon.css">
    <!--[if lt IE 9]>
        <script src='../sys/html5shiv.min.js'></script>
    <![endif]-->
    <style>
        table.tabledata td.tdhead { border-top: 1px solid black; border-right: 1px solid black; font-weight: bold; text-align: center; vertical-align: middle; }
        table.tabledata td { border-top: 1px solid black; border-right: 1px solid black; text-align: center; vertical-align: middle; }
    </style>
</head>
<body>
    <section style="min-width: 1260px;">
        <div>
            <form class="layui-form" action="" style="width:90%;margin:0 auto;">
                <div class="layui-form-item" style="display: inline-block;margin: 15px 0 0 5px;">
                    <!--<label class="layui-form-label" style="width: 100px;">选择上报频率：</label>-->
                    <div class="layui-input-block" style="width: 210px;margin-left:0px;">
                        <select name="year" lay-verify="required" lay-filter="selyear">
                            <!--<option value="">全部</option>-->
                            <!--<option value="second">按次</option>-->
                            <!--<option value="month">按月</option>-->
                            <!--<option value="quarter">按季度</option>-->
                        </select>
                    </div>
                </div>
                <a class="layui-btn btn-excel" style="float:right;margin: 14px 20px 0 0;">导出Excel</a>
                <a class="layui-btn btn-print" style="float:right;margin: 14px 20px 0 0;">打印预览</a>
            </form>
        </div>
    </section>
    <div id="divcontent" style="margin:0 auto;text-align: center;width:90%;">
    </div>
    <script type="text/javascript" src="../sys/jquery.js"></script>
    <script type="text/javascript" src="../plugin/mock/dist/mock.js"></script>
    <script type="text/javascript" src="../js/bigdata_common.js"></script>
    <script type="text/javascript">
        var v = top.version;
        document.write("<script type='text/javascript' src='../sys/jquery.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../sys/arg.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../plugin/jqueryprint/jQuery.print.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../js/statisticalAnalysis.js?v=" + v + "'><" + "/script>");
    </script>
</body>
</html>
