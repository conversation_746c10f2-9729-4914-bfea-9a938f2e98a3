/**
部份样式复写
**/
#bar_desc{ padding-left:5px; color:#009db2 }
.layui-fluid{ padding:10px }
/*.layui-card-body{ padding:2px }*/
.layui-table-cell{ padding-left:3px; padding-right:3px }
.height-auto{ min-height:calc(100vh - 20px); height:auto }
.height-auto-tab{ min-height:calc(100vh - 70px); height:auto }
.height-scoll{ height:calc(100vh - 20px); overflow:auto; overflow-x:hidden; padding-bottom:0px }
.height-scoll1{ height:calc(100vh - 54px); overflow:auto; overflow-x:hidden; padding-bottom:0px }
.height-scoll2{ height:calc(100vh - 66px); overflow:auto; overflow-x:hidden; padding-bottom:0px }
.left{ float:left; }
.right{ float:right; }
.site-dropdown li{ color:#009db2; font-size:14px; }
.site-dropdown2 li{ color:#009db2; font-size:12px; }
.layui-btn-container{ float:left }
.layui-btn-container .layui-btn{ margin-right:6px; }
.layui-btn-container2 .layui-btn{ margin:0px; }
.btn-disabled, .btn-disabled:active, .btn-disabled:hover{ cursor:not-allowed !important; opacity:.4 }
/** brief top **/
.tab-brief-top{ padding-top:5px; margin-top:5px }
.tab-brief-top .layui-tab-brief{ padding-top:0; margin-top:0 }
.tab-brief-top .layui-input-wrap .table-search-text{ padding-left:30px }
.tab-brief-top select{ border:solid 1px #e6e6e6; height:30px; line-height:30px; border-radius:2px }

/****/
.width-0{ width:0% }
.width-1{ width:1% }
.width-2{ width:2% }
.width-3{ width:3% }
.width-4{ width:4% }
.width-5{ width:5% }
.width-6{ width:6% }
.width-7{ width:7% }
.width-8{ width:8% }
.width-9{ width:9% }
.width-10{ width:10% }
.width-11{ width:11% }
.width-12{ width:12% }
.width-13{ width:13% }
.width-14{ width:14% }
.width-15{ width:15% }
.width-16{ width:16% }
.width-17{ width:17% }
.width-18{ width:18% }
.width-19{ width:19% }
.width-20{ width:20% }
.width-21{ width:21% }
.width-22{ width:22% }
.width-23{ width:23% }
.width-24{ width:24% }
.width-25{ width:25% }
.width-26{ width:26% }
.width-27{ width:27% }
.width-28{ width:28% }
.width-29{ width:29% }
.width-30{ width:30% }
.width-31{ width:31% }
.width-32{ width:32% }
.width-33{ width:33% }
.width-34{ width:34% }
.width-35{ width:35% }
.width-36{ width:36% }
.width-37{ width:37% }
.width-38{ width:38% }
.width-39{ width:39% }
.width-40{ width:40% }
.width-41{ width:41% }
.width-42{ width:42% }
.width-43{ width:43% }
.width-44{ width:44% }
.width-45{ width:45% }
.width-46{ width:46% }
.width-47{ width:47% }
.width-48{ width:48% }
.width-49{ width:49% }
.width-50{ width:50% }
.width-51{ width:51% }
.width-52{ width:52% }
.width-53{ width:53% }
.width-54{ width:54% }
.width-55{ width:55% }
.width-56{ width:56% }
.width-57{ width:57% }
.width-58{ width:58% }
.width-59{ width:59% }
.width-60{ width:60% }
.width-61{ width:61% }
.width-62{ width:62% }
.width-63{ width:63% }
.width-64{ width:64% }
.width-65{ width:65% }
.width-66{ width:66% }
.width-67{ width:67% }
.width-68{ width:68% }
.width-69{ width:69% }
.width-70{ width:70% }
.width-71{ width:71% }
.width-72{ width:72% }
.width-73{ width:73% }
.width-74{ width:74% }
.width-75{ width:75% }
.width-76{ width:76% }
.width-77{ width:77% }
.width-78{ width:78% }
.width-79{ width:79% }
.width-80{ width:80% }
.width-81{ width:81% }
.width-82{ width:82% }
.width-83{ width:83% }
.width-84{ width:84% }
.width-85{ width:85% }
.width-86{ width:86% }
.width-87{ width:87% }
.width-88{ width:88% }
.width-89{ width:89% }
.width-90{ width:90% }
.width-91{ width:91% }
.width-92{ width:92% }
.width-93{ width:93% }
.width-94{ width:94% }
.width-95{ width:95% }
.width-96{ width:96% }
.width-97{ width:97% }
.width-98{ width:98% }
.width-99{ width:99% }
.width-100{ width:100% }
.lineheight36{ line-height:36px }
/**
**/
.table-search-title, .table-search-title-first{ font-size:15px; }
.width20{ width:20px }
.width40{ width:40px }
.width60{ width:60px }
.width80{ width:80px }
.width100{ width:100px }
.width120{ width:120px }
.width140{ width:140px }
.width150{ width:150px }
.width160{ width:160px }
.width180{ width:180px }
.width200{ width:200px }
.width220{ width:220px }
.width240{ width:240px }
.width250{ width:250px }
.width260{ width:260px }
.width280{ width:280px }
.width300{ width:300px }
.left5{ margin-left:5px }
.left10{ margin-left:10px }
.left20{ margin-left:20px }
.color_green{ color:green }
.hand{ cursor:pointer }
.hidden{ display:none }
.show{ display:inline }
.input-readonly{ background:#F8F8F8 }
.input-notnull:after{ content:'*'; color:#a33027 }
/**hidden shawow**/
.layui-table-fixed-l, .layui-table-fixed-r{ border:0px; box:0px; box-shadow:0px 0 8px rgba(0, 0, 0, 0) }
.layui-table-fixed .layui-table-header table, .layui-table-fixed .layui-table-body table{ border:0px }
.input-text, .table-search-text{ border:solid 1px #e6e6e6; height:28px; line-height:1.3; padding-left:5px; }
.input-text, .table-search-text{ outline:0; -webkit-appearance:none; transition:all .3s; -webkit-transition:all .3s; box-sizing:border-box }
.input-text{ height:38px; line-height:1.3; line-height:38px; border-width:1px; border-style:solid; background-color:#fff; border-radius:2px }
.table-search-text{ height:30px; line-height:38px; border-width:1px; border-style:solid; background-color:#fff; border-radius:2px }
.input-text, .table-search-text:hover{ border-color:#D2D2D2 !important }
.margin-10{ margin:10px }
.input-title{ font-weight:400; line-height:40px; padding:0px 10px 0px 30px; }
.input-title1{ font-weight:400; line-height:40px; padding:0px 10px 0px 10px; }
.card-search{ position:relative; height:42px; line-height:42px; padding:0 15px; border-bottom:1px solid #f6f6f6; color:#333; border-radius:2px 2px 0 0; font-size:14px }
.card-search .layui-input-wrap .table-search-text{ padding-left:30px }
.table-search-title{ font-weight:400; line-height:40px; padding:0px 5px 0px 10px; }
.table-search-title-first{ font-weight:400; line-height:40px; padding:0px 5px 0px 0px; }
.card-search .layui-input-wrap .table-search-text{ padding-left:30px }
.layui-card-header{; padding-left:6px }
.SearchTable select{ border:solid 1px #e6e6e6; height:30px; line-height:30px; border-radius:2px }
.t_list{ font-size:13px; }
.t_list1{ display:none; }
.t_list tr th{ height:28px; padding:4px 3px 2px 3px; border:1px solid #d7d7d7; border-left:1px solid #ffffff; border-top:0px solid #ffffff; background:url(../images/table_tit_bg.gif); }
.t_list tr td{ height:28px; font-size:13px; padding:3px 2px; border-bottom:1px solid #d7d7d7; border-left:1px solid #d7d7d7; overflow:hidden; white-space:nowrap; text-overflow:ellipsis; }
.t_list tr th.td_left{ text-align:left; }
.t_list tr td.td_left{ text-align:left; }
.t_list tr td.td_right{ text-align:right; }
.t_list tr td.td_right_f00{ text-align:right; color:#ff0000; }
.t_list input{ font-size:13px; height:22px; line-height:22px; }
.ext_label{ width:auto; padding-left:15px; padding-right:10px; }
.btn-nav{ background-color:#475E76; padding:0px 25px 0px 15px }
.btn-add{ background-color:#379DCC }
.btn-transfer{ background:#718fcd; opacity:0 }
.btn-edit{ background-color:#718fcd }
.btn-del{ background-color:#e93881 }
.btn-del-more{ background-color:#968852 }
.color-danger{ background-color:#E8654B }
.color-grey{ background-color:#CACFD3 }
.color-info{ background-color:#4CA9E7 }
.color-primary{ background-color:#3CC9AC }
.color-warn{ background-color:#F4D100 }
.color-success{ background-color:#79C667 }
.color-inverse{ background-color:#475E76 }
.color-1{ color:#ef4464 }
.color-2{ color:#fad259 }
.color-3{ color:#d22e8d }
.color-4{ color:#03dee0 }
.color-5{ color:#d05c7c }
.color-6{ color:#bb60b2 }
.color-7{ color:#433e7c }
.color-8{ color:#009db2 }
.color-9{ color:#024b51 }
.color-10{ color:#0780cf }
.color-11{ color:#765005 }
.bg-1{ background:#ef4464 }
.bg-2{ background:#fad259 }
.bg-3{ background:#d22e8d }
.bg-4{ background:#03dee0 }
.bg-5{ background:#d05c7c }
.bg-6{ background:#bb60b2 }
.bg-7{ background:#433e7c }
.bg-8{ background:#009db2 }
.bg-9{ background:#024b51 }
.bg-10{ background:#0780cf }
.bg-11{ background:#765005 }
.btn-green{ background-color:green }
.btn-yellow{ background-color:yellow }
.btn-blue{ background-color:blue }
.btn-red{ background-color:red }
.btn-purple{ background-color:purple }
.btn-ref{ background-color:#FFB800 }
.btn-pre{ background-color:#FFB800 }
.btn-close{ background-color:#43B3C1 }
.btn-update{ background-color:#FFB800 }
.btn-search{ background-color:#FFB800 }
.btn-search-adv{ background-color:#1E9FFF }
.b{ font-weight:bold }
.tree-selectd{ background:#C0C1BE }
.layui-tree-txt{ margin-left:-9px; padding:0 5px 0 5px }
.tree-card{ min-height:calc(100vh - 30px); overflow:hidden }
.padding-1{ padding:1px }
.padding-2{ padding:2px }
.padding-5{ padding:5px }
.padding-10{ padding:10px }
.padding-15{ padding:15px }
/**
自定义dropdown
**/
.layui-drop ul{ padding:0px; margin:1px }
.layui-drop li{ border-radius:2px; margin-bottom:1px; padding:0px; height:28px; line-height:28px }
.layui-drop .layui-menu-body-title{ text-align:center; color:#ffffff; font-size:13px; }
.drop-warn li{ background:#FF5722 }
.drop-warn li:hover{ background:#F27A54 }
/**渐变色**/

.btn-change-1{
	background:-webkit-linear-gradient(left, #81b1f1 0%, #604CEC 100%);
	background:-moz-linear-gradient(left, #81b1f1 0%, #604CEC 100%);
	background:-ms-linear-gradient(left, #81b1f1 0%, #604CEC 100%);
	background:-o-linear-gradient(left, #81b1f1 0%, #604CEC 100%);
}
.btn-change-2{
	background:-webkit-linear-gradient(left, #F1BD8B 0%, #E98C2D 100%);
	background:-moz-linear-gradient(left, #F1BD8B 0%, #E98C2D 100%);
	background:-ms-linear-gradient(left, #F1BD8B 0%, #E98C2D 100%);
	background:-o-linear-gradient(left, #F1BD8B 0%, #E98C2D 100%);
}
.btn-change-3{
	background:-webkit-linear-gradient(left, #43B3C1 0%, #2F9688 100%);
	background:-moz-linear-gradient(left, #43B3C1 0%, #2F9688 100%);
	background:-ms-linear-gradient(left, #43B3C1 0%, #2F9688 100%);
	background:-o-linear-gradient(left, #43B3C1 0%, #2F9688 100%);
}
.btn-change-4{
	background:-webkit-linear-gradient(left, #E98C2D 0%, #D74D27 100%);
	background:-moz-linear-gradient(left, #E98C2D 0%, #D74D27 100%);
	background:-ms-linear-gradient(left, #E98C2D 0%, #D74D27 100%);
	background:-o-linear-gradient(left, #E98C2D 0%, #D74D27 100%);
}
.btn-change-5{
	background:-webkit-linear-gradient(left, #F4CA71 0%, #E8AF33 100%);
	background:-moz-linear-gradient(left, #F4CA71 0%, #E8AF33 100%);
	background:-ms-linear-gradient(left, #F4CA71 0%, #E8AF33 100%);
	background:-o-linear-gradient(left, #F4CA71 0%, #E8AF33 100%);
}
.btn-change-6{
	background:-webkit-linear-gradient(left, #E75356 0%, #E3262A 100%);
	background:-moz-linear-gradient(left, #E75356 0%, #E3262A 100%);
	background:-ms-linear-gradient(left, #E75356 0%, #E3262A 100%);
	background:-o-linear-gradient(left, #E75356 0%, #E3262A 100%);
}
.btn-change-7{
	background:-webkit-linear-gradient(left, #74A6F0 0%, #427DF1 100%);
	background:-moz-linear-gradient(left, #74A6F0 0%, #427DF1 100%);
	background:-ms-linear-gradient(left, #74A6F0 0%, #427DF1 100%);
	background:-o-linear-gradient(left, #74A6F0 0%, #427DF1 100%);
}
.btn-change-8{
	background:-webkit-linear-gradient(left, #87B740 0%, #6BAE07 100%);
	background:-moz-linear-gradient(left, #87B740 0%, #6BAE07 100%);
	background:-ms-linear-gradient(left, #87B740 0%, #6BAE07 100%);
	background:-o-linear-gradient(left, #87B740 0%, #6BAE07 100%);
}
.btn-change-9{
	background:-webkit-linear-gradient(left, #379DCC 0%, #0A85BE 100%);
	background:-moz-linear-gradient(left, #379DCC 0%, #0A85BE 100%);
	background:-ms-linear-gradient(left, #379DCC 0%, #0A85BE 100%);
	background:-o-linear-gradient(left, #379DCC 0%, #0A85BE 100%);
}
.btn-change-10{
	background:-webkit-linear-gradient(left, #4E4949 0%, #2A2A2A 100%);
	background:-moz-linear-gradient(left, #4E4949 0%, #2A2A2A 100%);
	background:-ms-linear-gradient(left, #4E4949 0%, #2A2A2A 100%);
	background:-o-linear-gradient(left, #4E4949 0%, #2A2A2A 100%);
}
.btn-change-11{
	background:-webkit-linear-gradient(left, #C78E7B 0%, #B06850 100%);
	background:-moz-linear-gradient(left, #C78E7B 0%, #B06850 100%);
	background:-ms-linear-gradient(left, #C78E7B 0%, #B06850 100%);
	background:-o-linear-gradient(left, #C78E7B 0%, #B06850 100%);
}
.btn-change-12{
	background:-webkit-linear-gradient(left, #998E34 0%, #CDB805 100%);
	background:-moz-linear-gradient(left, #998E34 0%, #CDB805 100%);
	background:-ms-linear-gradient(left, #998E34 0%, #CDB805 100%);
	background:-o-linear-gradient(left, #998E34 0%, #CDB805 100%);
}
.btn-change-13{
	background:-webkit-linear-gradient(left, #B06850 0%, #A1492E 100%);
	background:-moz-linear-gradient(left, #B06850 0%, #A1492E 100%);
	background:-ms-linear-gradient(left, #B06850 0%, #A1492E 100%);
	background:-o-linear-gradient(left, #B06850 0%, #A1492E 100%);
}
.btn-change-14{
	background:-webkit-linear-gradient(left, #CA93D2 0%, #D972E9 100%);
	background:-moz-linear-gradient(left, #CA93D2 0%, #D972E9 100%);
	background:-ms-linear-gradient(left, #CA93D2 0%, #D972E9 100%);
	background:-o-linear-gradient(left, #CA93D2 0%, #D972E9 100%);
}
.btn-change-15{
	background:-webkit-linear-gradient(left, #857A0B 0%, #BAA906 100%);
	background:-moz-linear-gradient(left, #857A0B 0%, #BAA906 100%);
	background:-ms-linear-gradient(left, #857A0B 0%, #BAA906 100%);
	background:-o-linear-gradient(left, #857A0B 0%, #BAA906 100%);
}
.btn-change-16{
	background:-webkit-linear-gradient(left, #857A0B 0%, #615908 100%);
	background:-moz-linear-gradient(left, #857A0B 0%, #615908 100%);
	background:-ms-linear-gradient(left, #857A0B 0%, #615908 100%);
	background:-o-linear-gradient(left, #857A0B 0%, #615908 100%);
}
.btn-change-17{
	background:-webkit-linear-gradient(left, #637A9D 0%, #415675 100%);
	background:-moz-linear-gradient(left, #637A9D 0%, #415675 100%);
	background:-ms-linear-gradient(left, #637A9D 0%, #415675 100%);
	background:-o-linear-gradient(left, #637A9D 0%, #415675 100%);
}
.btn-change-18{
	background:-webkit-linear-gradient(left, #4E4949 0%, #747474 100%);
	background:-moz-linear-gradient(left, #4E4949 0%, #747474 100%);
	background:-ms-linear-gradient(left, #4E4949 0%, #747474 100%);
	background:-o-linear-gradient(left, #4E4949 0%, #747474 100%);
}
.btn-change-19{
	background:-webkit-linear-gradient(left, #A17C57 0%, #DF8833 100%);
	background:-moz-linear-gradient(left, #A17C57 0%, #DF8833 100%);
	background:-ms-linear-gradient(left, #A17C57 0%, #DF8833 100%);
	background:-o-linear-gradient(left, #A17C57 0%, #DF8833 100%);
}
.btn-change-20{
	background:-webkit-linear-gradient(left, #e76c74 0%, #E75356 100%);
	background:-moz-linear-gradient(left, #e76c74 0%, #E75356 100%);
	background:-ms-linear-gradient(left, #e76c74 0%, #E75356 100%);
	background:-o-linear-gradient(left, #e76c74 0%, #E75356 100%);
}
.btn-change-21{
	background:-webkit-linear-gradient(left, #5AADD3 0%, #379DCC 100%);
	background:-moz-linear-gradient(left, #5AADD3 0%, #379DCC 100%);
	background:-ms-linear-gradient(left, #5AADD3 0%, #379DCC 100%);
	background:-o-linear-gradient(left, #5AADD3 0%, #379DCC 100%);
}
.bg-icon-1{ background-image:url('../icon/1_gray.png'); background-repeat:no-repeat; background-attachment:scroll; background-position:bottom left; }
.bg-icon-2{ background-image:url('../icon/2_gray.png'); background-repeat:no-repeat; background-attachment:scroll; }
.bt-icon-tl{ background-position:top left; }
.bt-icon-tc{ background-position:top center; }
.bt-icon-tr{ background-position:top right; }
.bt-icon-bl{ background-position:bottom left; }
.bt-icon-bc{ background-position:bottom center; }
.bt-icon-br{ background-position:bottom right; }
/**tabs style **/
.layui-tab-pink > .layui-tab-title .layui-this{ color:#EC4899 }
.layui-tab-pink > .layui-tab-title .layui-this:after{ border:0; border-bottom:2px solid #DB2777; }
/** talbe tips style**/
.table-tips{ color:#357b9c }
.table-tips span{ color:#ff5722 }
/** taboe td height 60px**/
.td60 td .layui-table-cell{ height:60px; line-height:60px; padding:0px; margin:0px }
.td60 td .line-2{ line-height:25px; padding-top:7px; padding-bottom:5px }
.td60 td .line-3{ line-height:18px; padding-top:3px; }
.td60 td .line-img{ border:1px; height:58px; width:90px }
.td60 td .title-2{ line-height:25px; padding-top:7px; overflow:visible; text-overflow:inherit; white-space:normal; word-break:break-all; }
.td60 td .title-3{ line-height:19px; padding-top:3px; overflow:visible; text-overflow:inherit; white-space:normal; word-break:break-all; }
/** taboe td height 70px**/
.td70 td .layui-table-cell{ height:70px; line-height:70px; padding:0px; margin:0px }
.td70 td .line-2{ line-height:26px; padding-top:9px; padding-bottom:9px }
.td70 td .line-3{ line-height:20px; padding-top:5px; }
.td70 td .line-img{ border:1px; height:66px; width:90px }
.td70 td .title-2{ height:52px; line-height:26px; margin-top:9px; overflow:hidden; text-overflow:inherit; white-space:normal; word-break:break-all; }
.td70 td .title-3{ height:60px; line-height:20px; margin-top:5px; overflow:hidden; text-overflow:ellipsis; white-space:normal; word-break:break-all; }
/** taboe td height 50px**/
.td50 td .layui-table-cell{ height:50px; line-height:50px; padding:0px; margin:0px }
.td50 td .line-2{ line-height:20px; padding-top:5px; padding-bottom:5px }
.td50 td .line-3{ line-height:16px; padding-top:1px; font-size:13px; }
.td50 td .line-img{ border:1px; height:46px; width:90px }
.td50 td .title-2{ height:40px; line-height:20px; padding:0px 5px 0px 5px; margin-top:5px; overflow:hidden; text-overflow:inherit; white-space:normal; word-break:break-all; }
.td50 td .title-3{ height:48px; line-height:16px; padding:0px 5px 0px 5px; margin-top:1px; font-size:13px; overflow:hidden; text-overflow:ellipsis; white-space:normal; word-break:break-all; }
/****/
.layui-layer-title{ border-bottom:1px solid #F0F0F0; background:#fafafa; color:#333333; height:40px; line-height:40px }
.layui-layer-content{ }
::-webkit-scrollbar{ width:5px; height:10px; background-color:rgba(250, 250, 250, 1); }
.height-auto ::-webkit-scrollbar{ width:5px; height:5px }
::-webkit-scrollbar-track{ box-shadow:inset 0 0 0px rgba(250, 250, 250, .5); border-radius:10px; background-color:rgba(250, 250, 250, .5); }
::-webkit-scrollbar-thumb{ border-radius:10px; box-shadow:inset 0 0 0px rgba(173, 173, 173, .5); background-color:rgba(173, 173, 173, .5); }
/****/
.layui-card .layui-table-view{ margin:0; }
/****/
.mid-center-child{ display:flex; justify-content:center; align-items:center; }
.mid-center{ position:absolute; top:50%; left:50%; transform:translate(-50%, -50%); text-align:center }
/**圆型头象**/
.user_head{ width:30px; height:30px; border-radius:50% }






