var objdata = {};
layui.config({
    base: './js/'
}).extend({ //设定模块别名
    system: '../../sys/system',
});
layui.use(['system', 'form'], function () {
    initData();
    initEvent();
});

function initData() {
    $.sm(function (re, err) {
        if (err) {
            return parent.layer.msg(err);
        }
        var json = {};
        for (var i = 0; i < re.length; i++) {
            var item = re[i].item;
            var userid = re[i].userid;
            var truename = re[i].truename;
            var arruser = [];
            var arrid = [];
            var arrname = [];
            if (json[item]) {
                arruser = json[item];
                arrid = json[item + '_id'];
                arrname = json[item + '_name'];
            }
            arruser.push(re[i]);
            arrid.push(userid);
            arrname.push(truename);
            json[item] = arruser;
            json[item + '_id'] = arrid;
            json[item + '_name'] = arrname;
        }
        var arrdom = $(".person");
        for (var i = 0; i < arrdom.length; i++) {
            arrdom.eq(i).html('');//清空页面人员
            var id = arrdom.eq(i).attr("id");
            if (json[id + '_name']) {
                arrdom.eq(i).html(json[id + '_name'].join(','));//填充人员到页面
                $("#btnadd" + id).html('编辑').show();
            } else {
                $("#btnadd" + id).html('添加').show();
            }
        }
        objdata.objItemUser = json;
    }, ["fuyou.useritem.selectuseritems", parent.objdata.my.roletype, parent.objdata.my.organid])
}

function initEvent() {
    $(".btnadd").click(function () {
        var item = $(this).data("item");
        var arrid = [];
        if (objdata.objItemUser[item + '_id']) {
            arrid = objdata.objItemUser[item + '_id'];
        }
        openwin(arrid, item);
    })
}

function openwin(arrid, item) {
    jQuery.getparent().layer.open({
        type: 2,
        title: '添加人员',
        shadeClose: false,
        area: ['800px', '70%'],
        content: 'html/common/selusers.html?v=' + (Arg("v") || 1) + '&mid=' + Arg("mid") + "&id=" + (arrid.length ? arrid.join(",") : 0),
        btn: ["确定", "取消"],
        yes: function (index, layero) { //或者使用btn1
            var arrdata = layero.find('iframe')[0].contentWindow.getseldata();
            var arrusers = arrdata[0];
            var arradd = [];//需要添加的人员
            for (var i = 0; i < arrusers.length; i++) {
                if (arrid.indexOf(arrusers[i].id) < 0) {//新添加的人员
                    arradd.push(arrusers[i]);
                }
            }
            // if (arradd.length == 0) {
            //     return jQuery.getparent().layer.msg("当前选择的用户都已添加");
            // }
            var arrpm = [];
            arrpm.push(["xcx.useritem.deluseritem", arrid.join(','), item]);
            for (var i = 0; i < arrusers.length; i++) {
                arrpm.push(["xcx.useritem.insertuseritem", arrusers[i].id, arrusers[i].name, item]);
            }
            $.sm(function(re,err){
                if(err){
                    jQuery.getparent().layer.msg(err);
                } else {
                    jQuery.getparent().layer.msg("保存成功！", {icon: 1});
                    initData();
                }
            },arrpm)
            jQuery.getparent().layer.close(index);
        }
    });
}
