<!DOCTYPE html>
<html>
<head>
    <title>体检项目设置</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="../../styles/xbcomstyle.css">
    <link rel="stylesheet" href="../../styles/tbstyles.css"/>
    <link rel="stylesheet" href="../../css/reset.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">

    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + top.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <style type="text/css">
        html, body {
            background: #EAEFF3;
            overflow: hidden;
        }

        #btnadd  {
            color: white;
        }

        #btnreset {
            color: white;
        }
        .legend_bjcyadd{line-height: 39px;}
        .left-tit{top:12px}
        /*按钮被禁用时的样式*/
        .layui-btn.disabled-btn {
            /* 降低亮度的样式，例如改变背景色、文字颜色、透明度等 */
            background-color: #ccc;
            color: #888;
            opacity: 0.6; /* 透明度 */
            cursor: not-allowed; /* 禁用时的鼠标样式 */
        }

        .layui-table-view .layui-table th{border-color: #eee;}

    </style>
</head>
<body>
<div class="">
    <div style="background-color: #fff">
        <div class="legend_bjcyadd" style="display: flex; flex-direction: row; justify-content: space-between;">
            <i class="left-tit"></i><label id="title-box-span"></label>
            <div style="margin-right: 15px;">
                <button class="layui-btn addbtn" id="btnreset">保存</button>
                <button class="layui-btn addbtn" id="btnadd">添加项目</button>
            </div>
        </div>
    </div>
    <div id="content" style="width: 100%;">
        <div class="marmain-cen">
            <div class="content-medical" id="divtable">
                <table id="laytable" lay-filter="laytable"></table>
            </div>
        </div>
    </div>
</div>
<script data-main="../../js/incheck/inchecklist" src='../../sys/require.min.js'></script>
</body>
</html>
