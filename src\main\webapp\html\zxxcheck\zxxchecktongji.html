﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>中小学体检统计</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <link rel="stylesheet" href="../../css/reset.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <!--<script type="text/javascript">-->
    <!--document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');-->
    <!--</script>-->
    <style type="text/css">
        html, body {
            background: #EAEFF3;
        }

        .btn-nextlt {
            margin-top: 10px;
        }

        .layui-form-label {
            width: 70px;
        }

        .layui-form .layui-form-item {
            margin-bottom: 5px;
        }

        .layui-form .layui-form-item, .layui-btn {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
<div class="marmain">
    <div class="content-medical">
        <div class="layui-form" style="padding: 10px 10px 5px 10px;">
            <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                <label class="layui-form-label" style="padding: 8px 0px 5px 10px; text-align: left;">关键字：</label>
                <div class="layui-input-inline" style="min-height: 30px;width:200px;">
                    <input type="text" placeholder="学生姓名 /预约码 /手机号 " class="layui-input" id="truename">
                </div>
            </div>

            <div class="layui-form-item" style="display: inline-block;vertical-align: top;height:39px;">
                <label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 10px;line-height: 28px;">预约时间：</label>
                <div class="layui-input-inline" style="float: none;width: auto;margin-right: 0px;">
                    <input id="txtyystratdate" type="text" style="width: 130px;" readonly placeholder="开始时间" class="layui-input"/>
                    <img id="iconyystartdate" src="../../images/newicon/ico_calendar.png" style="cursor: pointer;position: relative;top: -29px;width: 20px;left: 103px;">
                </div>
                <span class="linebg">-</span>
                <div class="layui-input-inline" style="float: none;width: auto;">
                    <input id="txtyyenddate" type="text" style="width: 130px; " readonly placeholder="结束时间" class="layui-input"/>
                    <img id="iconyyenddate" src="../../images/newicon/ico_calendar.png" style="cursor: pointer;position: relative;top: -29px;width: 20px;left: 103px;">
                </div>
            </div>
            <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                <label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 10px;line-height: 28px;">是否取消预约：</label>
                <div class="layui-input-inline" style="min-height: 30px;width:128px;">
                    <select lay-filter="sel_cktype" id="sel_cktype">
                        <option value="">请选择</option>
                        <option value="1">是</option>
                        <option value="0">否</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                <label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 10px;line-height: 28px;">性别：</label>
                <div class="layui-input-inline" style="min-height: 30px;width:128px;">
                    <select lay-filter="sel_sex" id="sel_sex">
                        <option value="">请选择</option>
                        <option value="男">男</option>
                        <option value="女">女</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                <label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 10px;line-height: 28px;">年龄：</label>
                <div class="layui-input-inline" style="min-height: 30px;width:128px;">
                    <select lay-filter="sel_age" id="sel_age">
                        <option value="">请选择</option>
                        <option value="6">6岁~</option>
                        <option value="7">7岁~</option>
                        <option value="8">8岁~</option>
                        <option value="9">9岁~</option>
                        <option value="10">10岁~</option>
                        <option value="11">11岁~</option>
                        <option value="12">12岁~</option>
                        <option value="13">13岁~</option>
                        <option value="14">14岁~</option>
                        <option value="15">15岁~</option>
                        <option value="16">16岁~</option>
                        <option value="17">17岁~</option>
                        <option value="13">13岁~</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                <label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 10px;line-height: 28px;">学校类型：</label>
                <div class="layui-input-inline" style="min-height: 30px;width:128px;">
                    <select lay-filter="sel_yeytype" id="sel_yeytype">
                        <option value="">请选择</option>
                        <option value="1">小学</option>
                        <option value="2">初中</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                <label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 10px;line-height: 28px;">学校名称：</label>
                <div class="layui-input-inline" style="min-height: 30px;width:128px;">
                    <select lay-filter="sel_yeyid" id="sel_yeyid">
                    </select>
                </div>
            </div>
            <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                <label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 10px;line-height: 28px;">年度：</label>
                <div class="layui-input-inline" style="min-height: 30px;width:128px;">
                    <select lay-filter="sel_schoolyear" id="sel_schoolyear">
                    </select>
                </div>
            </div>
            <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                <label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 10px;line-height: 28px;">班级：</label>
                <div class="layui-input-inline" style="min-height: 30px;width:128px;">
                    <select lay-filter="sel_class" id="sel_class">
                    </select>
                </div>
            </div>
            <button id="select" class="layui-btn form-search" style="vertical-align: top;">查询</button>
            <button id="btnimport" class="layui-btn form-search" style="vertical-align: top;">导出Excel</button>
<!--            <button id="btnprint" class="layui-btn form-search" style="vertical-align: top;">打印名单</button>-->
<!--            <button id="btnprintxx" class="layui-btn form-search" style="vertical-align: top;">打印信息表</button>-->
        </div>
    </div>
    <div class="layui-row marmain-cen">
        <!--    layui-col-xs5 layui-col-sm5 layui-col-md5-->
        <div class="" style="height: 100%;background: #ffffff;">
            <table id="tabelList" lay-filter="test"></table>
        </div>
    </div>
</div>
<script type="text/html" id="zizeng">
    {{d.LAY_TABLE_INDEX+1}}
</script>
<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/system.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../js/tong/tong.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../js/tong/tongpublic.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../plugin/wdatepicker/wdatepicker.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/function.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../js/zxxcheck/zxxchecktongji.js?v=" + v + "'><" + "/script>");
</script>
<!--<script data-main="../js/tongph/rytjlist" src='../sys/require.min.js'></script>-->
</body>
</html>
