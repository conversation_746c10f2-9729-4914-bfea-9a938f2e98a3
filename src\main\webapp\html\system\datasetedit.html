﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>餐费统计</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <link href="../../styles/admin.css" rel="stylesheet" type="text/css"/>
    <link href="../../css/reset.css" rel="stylesheet" type="text/css"/>
    <link href="../../styles/tbstyles.css" rel="stylesheet" type="text/css"/>
    <link href="../../css/style.css" rel="stylesheet" type="text/css"/>
    <link href="../../layui-btkj/css/layui.css" rel="stylesheet" type="text/css"/>
<!--    <link href="../../plugin/cropper/css/bootstrap.min.css" rel="stylesheet" type="text/css"/>-->
<!--    <link href="../../plugin/datepicker/css/bootstrap-datepicker3.min.css" rel="stylesheet" type="text/css"/>-->
<!--    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">-->
    <!--    <link href="../../plugin/editselect/css/editselect.css" rel="stylesheet" type="text/css"/>-->
    <style type="text/css">

        /*.laydate-time-show .layui-laydate-content .layui-laydate-list > li { width: 50% !important; }*/
        .layui-laydate-range {
            width: 568px!important;
            /*left: 110px;*/
            /*top: 116px!important;*/
        }
        .layui-form-label {
            width: 91px;
        }
        .layui-input-block{
            margin-left: 125px;
        }
    </style>
</head>
<body>
<!--编辑餐费统计信息-->
<div class="addbill layui-form radio-blue" style="margin: 0;padding: 0;height: 100%;">
    <div style="margin-top:20px;">
        <div class="layui-form-item">
            <label class="layui-form-label"><b style="display: inline-block; color: red;">*</b>知识库名称：</label>
            <div class="layui-input-block" style="width: 150px;">
                <input type="text" id="name" name="name" required lay-verify="required" autocomplete="off" placeholder="请输入知识库名称" class="layui-input" maxlength="30"/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><b style="display: inline-block; color: red;">*</b>系统标识：</label>
            <div class="layui-input-block" style="width: 150px;">
                <select id="utype">
                    <option value="1">园所</option>
                    <option value="2">家长</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><b style="display: inline-block; color: red;">*</b>知识库语言：</label>
            <div class="layui-input-block" style="width: 150px;">
                <select id="language">
                    <option value="Chinese">中文</option>
                    <option value="English">英文</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item layui-form">
            <label class="layui-form-label">描述：</label>
            <div class="layui-input-block" style="width: 150px;">
                <textarea id="description" class="layui-input" style="width: 300px;height:150px;padding: 10px;" maxlength="180"></textarea>
            </div>
        </div>
        <input type="hidden" id="dataset_id"/>
    </div>
    <div style="display:none;">
        <a id="btnok" class="layui-btn">保存</a>
    </div>
</div>
<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/system.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/function.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../plugin/datepicker/js/bootstrap-datepicker.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../plugin/datepicker/locales/bootstrap-datepicker.zh-CN.min.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../js/system/datasetedit.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>
