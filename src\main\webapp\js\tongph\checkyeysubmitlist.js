/*
 新增日期:
 作 者:
 內容摘要:体检报名名单
 */
var objdata = {
        sorType:{
            1:["标准排序(按性别+出生日期排序，先男后女，年龄从大到小排)","classno,sex desc, birthday,stuno"],
            3:["按年龄排序（从大到小）","classno,birthday,stuno"],
            4:["自定义排序（班级内自定义顺序）","classno,stusort,stuno"]
        }
    },
    form;
layui.config({
    base: './js/',
    waitSeconds: 0
}).extend({
    // system: '../../sys/system'
});
layui.use(['form', 'layer'], function () {
    form = layui.form;
    objdata.areacode = jQuery.getparent().getAreaInfo(jQuery.getparent().objdata.my.areacode);//地区编号
    objdata.areaname = jQuery.getparent().getAreaNamebyCode(objdata.areacode.areacode);//地区名称
    if (!jQuery.getparent().istuoyou()) {
        return jQuery.getparent().layer.msg("抱歉，您不是园所用户！");
    }
    objdata.checkid = Arg('checkid');
    // objdata.organid = jQuery.getparent().objdata.my.organid;
    objdata.organid = Arg("cur_organid");
    objdata.curdate = moment().format('YYYY-MM-DD');

    initEvent();
    initData();
    initclass();
});

/**
 * 初始化事件
 */
function initEvent() {
    $(".sec1").off('click', '#noticebtn').on('click', '#noticebtn', function () {
        jQuery.getparent().layer.msg('功能建设中..');
    }).off('click', '.notice').on('click', '.notice', function () {
        jQuery.getparent().layer.msg('功能建设中..');
    }).off('click', '#btnsearch').on('click', '#btnsearch', function () {
        var searchTxt = $("#searchtxt").val();
        var swhere = {};
        // if(searchTxt){
        //     swhere.search = [searchTxt];
        // }
        initData(swhere);
    }).off('click', '.delcheck').on('click', '.delcheck', function () {
        var stuname = $(this).data("stuname");
        var id = $(this).data("id");
        var stuno = $(this).data("stuno");
        var _this = $(this);
        jQuery.getparent().layer.confirm("是否确认删除" + stuname + "？", function (idx) {
            $.sm(function (re, err) {
                if (err) {
                    return jQuery.getparent().layer.msg("删除失败");
                }
                jQuery.getparent().layer.msg("删除成功");
                jQuery.getparent().layer.close(idx);

                //删除objdata.allstu 数据
                for (var i = 0; i < objdata.allstu.length; i++) {
                    var onestu = objdata.allstu[i];
                    if (onestu.stuno == stuno) {
                        objdata.allstu.splice(i, 1);
                        // i--;
                        _this.parents('tr').remove();
                        break;
                    }

                }

            }, ["checkyeyreserve.delcheckresult", id], Arg("cur_t"), Arg('cur_did'))
        })


    }).off('click', '.delstu').on('click', '.delstu', function () {
        var stuname = $(this).data("stuname");
        var stuno = $(this).data("stuno");
        var _this = $(this);
        jQuery.getparent().layer.confirm("是否确认删除" + stuname + "？", function (idx) {
            // 删除objdata.allstu 数据
            for (var i = 0; i < objdata.allstu.length; i++) {
                var onestu = objdata.allstu[i];
                if (onestu.stuno == stuno) {
                    objdata.allstu.splice(i, 1);
                    // i--;
                    jQuery.getparent().layer.close(idx);
                    jQuery.getparent().layer.msg("删除成功");
                    _this.parents('tr').remove();
                    break;
                }
            }
        })


    }).off('click', '#checkall').on('click', '#checkall', function () {
        debugger
        if ($(this).is(":checked")) {
            $("[name=stunos]").prop("checked", true)
        } else {
            $("[name=stunos]").prop("checked", false)
        }

    });
}


/**
 * 初始化数据
 */
function initData(swhere) {
    // 1.查询所有学生信息 2.查询家长报名名单 3.提交名单 把所有学生信息都提交过去
    jQuery.getparent().layer.load();
    objdata.yeyinfo = getyeyinfo();
    if (!objdata.yeyinfo.yeyname) {
        jQuery.getparent().layer.closeAll('loading');
        return jQuery.getparent().layer.msg('您的账户 暂无园所信息');
    }
    var sortway = objdata.yeyinfo.sortway || 1;
    //1.查询所有学生信息
    swhere = !swhere ? {} : swhere;
    swhere['orderule'] = [objdata.sorType[sortway][1]];
    objdata.allstu = getAllstu(swhere);
    //2.查询家长报名名单
    var allreshome = gethomeReserve();

    for (var j = 0; j < objdata.allstu.length; j++) {
        var onestu = objdata.allstu[j];
        for (var k = 0; k < allreshome.length; k++) {
            var onereserve = allreshome[k];
            if (onestu.stuno == onereserve.stuno) {
                onestu['reserve'] = onereserve;
                break;
            }
        }
    }

    //已报名名单、未报名名单
    var reserveHtml = '';
    var noreserveHtml = '';
    var reserveIndex = 0;
    var noreserveIndex = 0;
    objdata.nopushArr = [];//未提交的
    for (var i = 0; i < objdata.allstu.length; i++) {
        var onestu = objdata.allstu[i];
        var oneres = onestu['reserve'];

        //搜索 过滤条件
        var searchTxt = $("#searchtxt").val();
        if (onestu.claname.indexOf(searchTxt) < 0 && onestu.stuname.indexOf(searchTxt) < 0) {
            continue;
        }
        var classnoTxt = $("#sel_class").val();
        if(classnoTxt && onestu.classno!=classnoTxt){
            continue;
        }

        if (oneres && oneres.status == 1) {
            continue;
            // reserveIndex++;
            // var pkgnames = [];
            // if(oneres.packagearr){
            //    var pkgArr = JSON.parse(oneres.packagearr);
            //     for (var j = 0; j < pkgArr.length; j++) {
            //         var onepkg = pkgArr[j];
            //         pkgnames.push(onepkg.package_name);
            //     }
            // }
            //
            // reserveHtml+='<tr>\n' +
            //     '                <td style="text-align: center;"><div>'+(reserveIndex)+'</div></td>\n' +
            //     '                <td style="text-align: center;"><div>'+oneres.stuname+'</div></td>\n' +
            //     '                <td style="text-align: center;"><div>'+oneres.claname+'</div></td>\n' +
            //     '                <td style="text-align: center;"><div>'+oneres.sex+'</div></td>\n' +
            //     '                <td style="text-align: center;"><div>'+ getZhAgeByAge(oneres.age) +'</div></td>\n' +
            //     '                <td style="text-align: center;">基础套餐、'+pkgnames.join("、")+'</td>\n' +
            //     '            </tr>'
        } else if (oneres && oneres.status == 0) {
            continue;
            // noreserveIndex++;
            // noreserveHtml+='<tr>\n' +
            //     '                <td style="text-align: center;"><div>'+(noreserveIndex)+'</div></td>\n' +
            //     '                <td style="text-align: center;"><div>'+oneres.stuname+'</div></td>\n' +
            //     '                <td style="text-align: center;"><div>'+oneres.claname+'</div></td>\n' +
            //     '                <td style="text-align: center;"><div>'+oneres.sex+'</div></td>\n' +
            //     '                <td style="text-align: center;"><div>'+ getZhAgeByAge(oneres.age) +'</div></td>\n' +
            //      '                <td style="text-align: center;"><div>已提交</div></td>\n' +
            //     '                <td style="text-align: center;"><div class="blue-txt delcheck" style="cursor:pointer;display: '+((oneres.ecard_checkid||oneres.ecardresult)?"none":"")+'" data-id="' + oneres.id + '" data-stuno="' + oneres.stuno + '" data-stuname="'+oneres.stuname+'">删除</div></td>\n' +
            //     '            </tr>'
        } else {
            objdata.nopushArr.push(onestu);//未提交的
            noreserveIndex++;
            noreserveHtml += '<tr>\n' +
                '                <td style="text-align: center;"><input type="checkbox" name="stunos" data-stuno="' + onestu.stuno + '" title="勾选" lay-skin="primary"></td>\n' +
                '                <td style="text-align: center;"><div>' + (noreserveIndex) + '</div></td>\n' +
                '                <td style="text-align: center;"><div>' + onestu.stuname + '</div></td>\n' +
                '                <td style="text-align: center;"><div>' + onestu.claname + '</div></td>\n' +
                '                <td style="text-align: center;"><div>' + onestu.sex + '</div></td>\n' +
                '                <td style="text-align: center;"><div>' + GetAge(onestu.birthday, new Date(), 'zh') + '</div></td>\n' +
                '                <td style="text-align: center;color: #e19079"><div>未提交</div></td>\n' +
                '            </tr>'
        }

    }
    if (reserveHtml == '') {
        $("#reservedata").html('<div style="background: url(../plugin/flexigrid/images/kb.png) center center no-repeat;width: 100%;height: 100%"></div>').css({"height": "100%"});
    } else {
        $("#reservedata").html(reserveHtml);
    }
    if (noreserveHtml == '') {
        $("#noreservedata").html('<div style="background: url(../plugin/flexigrid/images/kb.png) center center no-repeat;width: 100%;height: 100%"></div>').css({"height": "100%"});
    } else {
        $("#noreservedata").html(noreserveHtml);
    }

    $("#totalstu").text(noreserveIndex);

    jQuery.getparent().layer.closeAll('loading');
}

/**
 * 查询所有学生信息
 */
function getAllstu(swhere) {
    var allstu = [];
    $.sm(function (re, err) {
        if (err) {
            jQuery.getparent().layer.msg('查询所有学生信息失败:' + err);
        }
        allstu = re || []
    }, ["checkyeyreserve.getallstu", $.msgwhere(swhere)], null, null, {async: false});
    return allstu;
}

/**
 * 查询家长报名名单
 */
function gethomeReserve(swhere) {
    swhere = swhere ? {...swhere, ...{status: [true]}} : {status: [true]};
    swhere.organid = [objdata.organid];
    var allstu = [];
    $.sm(function (re, err) {
        if (err) {
            jQuery.getparent().layer.msg('查询所有学生信息失败:' + err);
        }
        if (re) {
            for (var i = 0; i < re.length; i++) {
                if (re[i].packagearr) {
                    var arrpackage = JSON.parse(re[i].packagearr);
                    var arrname = [];
                    for (var j = 0; j < arrpackage.length; j++) {
                        arrname.push(arrpackage[j].package_name);
                    }
                    re[i].name = arrname.join(',');
                }
            }
        }
        allstu = re || []
    }, ["checkyeyreserve.getallhomeReserve", objdata.checkid, jQuery.getparent().objdata.my.yeyid, $.msgwhere(swhere), objdata.organid, jQuery.getparent().objdata.yeyinfo.fuyouguid,$.msgwhere()], Arg("cur_t"), Arg('cur_did'), {async: false});
    return allstu;
}

//提交名单
function publish(cb) {
    debugger
    //3.提交名单 把所有学生信息都提交过去
    if (!objdata.yeyinfo.yeyname) {
        return jQuery.getparent().layer.msg('您的账户 暂无园所信息');
    }

    if (Arg("endate") < objdata.curdate) {
        return jQuery.getparent().layer.msg('本次体检周期已过！不能提交了哦~');
    }

    var eleArr = $("[name=stunos]:checked");
    if (eleArr.length == 0) {
        return jQuery.getparent().layer.msg('请勾选需要提交的名单');
    }

    var msgarr = [];
    for (var i = 0; i < objdata.nopushArr.length; i++) {
        var onestu = objdata.nopushArr[i];//id,check_id,yeyid,yeyname,stuno,stuname,sex,birthday,classno,claname,age
        for (var j = 0; j < eleArr.length; j++) {
            var onele = eleArr[j];
            if (onestu.stuno == $(onele).data("stuno")) {
                onestu.age = GetAge(onestu.birthday, new Date());
                var reserveid = onestu['reserve'] ? onestu['reserve'].id : 0;
                msgarr.push(["checkyeyreserve.reservephyey", reserveid, objdata.checkid, objdata.yeyinfo.id, objdata.yeyinfo.yeyname, onestu.stuno, onestu.stuname, onestu.sex, onestu.birthday, onestu.classno, onestu.claname, onestu.age, onestu.credentialsnum, objdata.organid, onestu.stusort]);
            }
        }

    }
    jQuery.getparent().layer.confirm("您确定要提交名单到妇幼吗？", {
        btn: ['确定', '取消'], //按钮
        shade: false
    }, function (index) {
        jQuery.getparent().layer.load();
        // 同步最新人数到yey表
        // syncStunum2Yey(objdata.yeyinfo.id,objdata.allstu.length);
        $.sm(function (re, err) {
            if (err) {
                jQuery.getparent().layer.closeAll('loading');
                return jQuery.getparent().layer.msg('提交失败:' + err);
            }
            jQuery.getparent().layer.closeAll('loading');
            cb && cb();
        }, msgarr, Arg("cur_t"), Arg('cur_did'),{trans:1});
    });
}

function getyeyinfo() {
    var yeyinfo = {};
    $.sm(function (re, err, obj) {
        yeyinfo = obj || {};
    }, ["checkyeyreserve.selyey", jQuery.getparent().objdata.my.yeyid], null, null, {async: false});
    return yeyinfo;
}

function initclass() {
    $.sm(function (re) {
        if (re) {
            var arrclass = re;
            if (arrclass) {
                objdata.arrclass = arrclass;
                var arrhtml = ['<option value="">请选择班级</option>'];
                for (var i = 0; i < arrclass.length; i++) {
                    var item = arrclass[i];
                    arrhtml.push('<option value="' + item.classno + '">' + item.claname + '</option>');
                }
                $('#sel_class').html(arrhtml.join(""));
                form.render('select');
            }
        }
    }, ["checkfeestatistics.selclass", parent.objdata.strtoday]);
}