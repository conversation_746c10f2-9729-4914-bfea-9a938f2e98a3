<!DOCTYPE html>
<html>
	<head>
	    <title>选择群发</title>
	    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
		<link rel="stylesheet" href="../plugin/zTree/css/zTreeStyle/zTreeStyle.css" type="text/css">
	    <link rel="stylesheet" href="../css/reset.css"/>
	    <link rel="stylesheet" href="../css/style.css"/>
	    <script type="text/javascript">
	        document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
	    </script>
	    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
		<link rel="stylesheet" href="../plugin/formSelect/formSelects-v4.css"/>
		<link rel="stylesheet" href="../css/commonstyle-layui-btkj.css">
		<link rel="stylesheet" href="../css/medical.css">
	    <!--[if lt IE 9]>
	    <script src='sys/html5shiv.min.js'></script>
	    <![endif]-->
		<style>
			html, body {
				background: #fff;
				color: #333333;
				height: 100%;
			}
			.layui-tab-title {
				border-bottom: none;
			}

			.layui-tab-title .layui-this::after {
				height: 45px;
			}
			.zhiweitxt{display: inline-block; width:49%; float: left;}
		</style>
	</head>
	<body>
	<div class="content-medical" id="container" style="height: 100%;">
		<div class="layui-form layui-comselect" style="margin:0 25px 10px 25px;display: none;">
			<div class="layui-form-item" style="display: inline-block;vertical-align: top;">
				<label id="lbselreceivearea" class="layui-form-label" style="padding: 0px 0 10px 0; float: none;text-align: left; color: #333333;font-size: 14px;">选择区县：</label>
				<div class="layui-input-inline" style="min-height: 30px;width: 600px;">
					<select name="receivearea" lay-verify="required" id="receivearea"  lay-verify="required"  xm-select="receivearea" xm-select-search>
						<option value=""></option>
					</select>
				</div>
			</div>
		</div>
		<div id="divhospital" class="tbmargin" style="margin:0px 20px 10px 20px;">
			<h3 style=" color: #333333;font-size: 14px; margin: 10px 0;">基层卫生服务机构</h3>
			<table id="divseltotype_e" class="layui-table checkbox-blue" lay-skin="row" lay-even="" cellspacing="0" cellpadding="0" border="0" >
<!--				<tr>-->
<!--					<td style="background: #FAFAFA"><div id=""><input type="checkbox" name="chkfuyouroletype" value="e" title="区县妇幼保健院" lay-filter="chkfuyouroletype">幼儿园</div></td>-->
<!--					<td style="text-align: center"><div id="divfuyousole" class="zhiweitxt"><input type="checkbox" name="chkfuyouroletype" value="e" title="区县妇幼保健院" lay-filter="chkfuyouroletype">园长</div><div class="zhiweitxt"><input type="checkbox">保健医</div></td>-->
<!--				</tr>-->
<!--				<tr>-->
<!--					<td style="background: #FAFAFA"><div><input type="checkbox" name="chkogranroletype" value="ehos" title="基层卫生服务机构" lay-filter="chkogranroletype">&lt;!&ndash;<input type="checkbox">&ndash;&gt;托育机构</div></td>-->
<!--					<td style="text-align: center"><div id="divorgansole" class="zhiweitxt"><input type="checkbox">园长</div><div class="zhiweitxt"><input type="checkbox">保健医</div></td>-->
<!--				</tr>-->
			</table>
		</div>
		<div class="tbmargin" style="margin:0px 20px 10px 20px;">
			<h3 style=" color: #333333;font-size: 14px; margin: 10px 0;">托幼机构</h3>
			<table id="divseltotype_y" class="layui-table checkbox-blue" lay-skin="row" lay-even="" cellspacing="0" cellpadding="0" border="0" >
				<!--				<tr>-->
				<!--					<td style="background: #FAFAFA"><div id=""><input type="checkbox" name="chkfuyouroletype" value="e" title="区县妇幼保健院" lay-filter="chkfuyouroletype">幼儿园</div></td>-->
				<!--					<td style="text-align: center"><div id="divfuyousole" class="zhiweitxt"><input type="checkbox" name="chkfuyouroletype" value="e" title="区县妇幼保健院" lay-filter="chkfuyouroletype">园长</div><div class="zhiweitxt"><input type="checkbox">保健医</div></td>-->
				<!--				</tr>-->
				<!--				<tr>-->
				<!--					<td style="background: #FAFAFA"><div><input type="checkbox" name="chkogranroletype" value="ehos" title="基层卫生服务机构" lay-filter="chkogranroletype">&lt;!&ndash;<input type="checkbox">&ndash;&gt;托育机构</div></td>-->
				<!--					<td style="text-align: center"><div id="divorgansole" class="zhiweitxt"><input type="checkbox">园长</div><div class="zhiweitxt"><input type="checkbox">保健医</div></td>-->
				<!--				</tr>-->
			</table>
		</div>
	</div>

<!--	<section style="margin: 20px;" id="container">-->
<!--		<div class="layui-form-item">-->
<!--			<div class="layui-inline">-->
<!--				<label id="lbselreceivearea" class="layui-form-label">选择区县：</label>-->
<!--				<div class="layui-input-block" style="margin-left:125px;width: 190px;">-->
<!--					<select name="receivearea" lay-verify="required" id="receivearea"  lay-verify="required"  xm-select="receivearea" xm-select-search>-->
<!--						<option value=""></option>-->
<!--					</select>-->
<!--				</div>-->
<!--			</div>-->
<!--		</div>-->

<!--		<div class="layui-form-item">-->
<!--			<label id="lbseltotype" class="layui-form-label">选择医疗保健机构及接收对象：</label>-->
<!--			<div id="divseltotype" class="layui-input-block checkbox-blue">-->
<!--				<label class="form-display" style="margin-left: -10px">-->
<!--					区县妇幼保健院<input type="checkbox" name="chkfuyouroletype" value="e" title="区县妇幼保健院" lay-filter="chkfuyouroletype">-->
<!--				</label>-->
<!--				<div id="divfuyousole"></div>-->
<!--				<label class="form-display">-->
<!--					基层卫生服务机构<input type="checkbox" name="chkogranroletype" value="ehos" title="基层卫生服务机构" lay-filter="chkogranroletype">-->
<!--				</label>-->
<!--				<div id="divorgansole"></div>-->
<!--			</div>-->
<!--		</div>-->
<!--&lt;!&ndash;		<div class="layui-row layui-col-space30">&ndash;&gt;-->
<!--&lt;!&ndash;			<div class="layui-col-xs12 layui-col-sm12 layui-col-md12">&ndash;&gt;-->
<!--&lt;!&ndash;				<div class="grid-demo grid-demo-bg1" style="border: 1px solid #dddddd;">&ndash;&gt;-->
<!--&lt;!&ndash;					<div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief" style="margin: 0; position: relative;">&ndash;&gt;-->
<!--&lt;!&ndash;						<div class="layui-tab-title layui-label-check sel-set-check">&ndash;&gt;-->
<!--&lt;!&ndash;							<label><input class="qr-role" data-role="4" type="checkbox" title="园长" checked>园长</label>&ndash;&gt;-->
<!--&lt;!&ndash;							<label><input class="qr-role" data-role="3" type="checkbox" title="保健医">保健医</label>&ndash;&gt;-->
<!--&lt;!&ndash;							<label class="stat-num-right"><i>0</i>/<i></i></label>&ndash;&gt;-->
<!--&lt;!&ndash;						</div>&ndash;&gt;-->
<!--&lt;!&ndash;						<label class="qr-coll-num" style="display:none; position: absolute; right: 16px; top: 10px;"><i>0</i>/<i>8</i></label>&ndash;&gt;-->
<!--&lt;!&ndash;						<div id="js-sameUser" class="ztree">&ndash;&gt;-->

<!--&lt;!&ndash;						</div>&ndash;&gt;-->
<!--&lt;!&ndash;						<div id="js-yeyUser" class="ztree" style=" display: none;">&ndash;&gt;-->

<!--&lt;!&ndash;						</div>&ndash;&gt;-->
<!--&lt;!&ndash;					</div>&ndash;&gt;-->
<!--&lt;!&ndash;				</div>&ndash;&gt;-->
<!--&lt;!&ndash;			</div>&ndash;&gt;-->
<!--&lt;!&ndash;		</div>&ndash;&gt;-->
<!--	</section>-->
	<script>
		var v = top.version;
		document.write('<script type="text/javascript" src="../sys/jquery.js?v=' + v + '"><' + '/script>');
		document.write('<script type="text/javascript" src="../sys/arg.js?v=' + v + '"><' + '/script>');
		document.write('<script type="text/javascript" src="../layui-btkj/layui.js?v=' + v + '"><' + '/script>');
		document.write('<script type="text/javascript" src="../js/citycode.js?v=' + v + '"><' + '/script>');
		document.write('<script type="text/javascript" src="../plugin/formSelect/formSelects-v4.js?v=' + v + '"><' + '/script>');
		// document.write('<script type="text/javascript" src="../plugin/zTree/js/jquery.ztree.core-3.5.js?v=' + v + '"><' + '/script>');
		// document.write('<script type="text/javascript" src="../plugin/zTree/js/jquery.ztree.excheck-3.5.js?v=' + v + '"><' + '/script>');
		// document.write('<script type="text/javascript" src="../plugin/zTree/js/jquery.ztree.exedit-3.5.js?v=' + v + '"><' + '/script>');
		// document.write('<script type="text/javascript" src="../plugin/zTree/js/jquery.ztree.exhide-3.5.js?v=' + v + '"><' + '/script>');
		document.write('<script type="text/javascript" src="../js/noticeselper.js?v=' + v + '"><' + '/script>');
	</script>
	</body>
</html>