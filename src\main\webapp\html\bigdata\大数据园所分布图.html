<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>园所分布图</title>
    <link rel="stylesheet" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../css/bigdata.css">
    <script type="text/javascript" src="../../layui-btkj/layui.js"></script>
    <!--<script type="text/javascript">
        document.write("<link rel=\"stylesheet\" id=\"linktheme\" href=\"../css/" + parent.objdata.curtheme + ".css\" type=\"text/css\" media=\"screen\"/>");
    </script>-->
    <!--[if lt IE 9]>
    <script src="../sys/html5shiv.min.js"></script>
    <![endif]-->
    <style type="text/css">
        body{background:url(../../images/bigdata/data_bg.jpg) no-repeat; background-size: 100% auto;width: 100%; height: 100%; position: absolute;}
    </style>
</head>
<body>
<section class="bigdata-all">
    <div class="bigdata-sub">
		<div class="bigdata-top">
			<div class="bigdata-time">
				<span style="display: inline-block;vertical-align: top;">
					<label style="font-size: 30px;">11:36</label>
					<label>2021-06-15</label>
				</span>
			</div>
			<div style="display: inline-block;">
				<div class="bigdata-title"><label>园所分布图</label></div>
			</div>
			<div class="bigdata-topright">
				<img src="../../images/bigdata/zhankai.png" style="width: 21px;cursor: pointer;">
			</div>
		</div>
		<div class="layui-form" style="color: #ffffff;margin: 25px 10px;">
			<div>最新数据更新时间：2022年7月1日</div>
			<div style="float: right;margin-top: -20px;">
				<span>模式：</span>
				<div style="margin: 0 10px;display: inline-block;"><img src="../../images/bigdata/icon_tongji_HL.png" style="vertical-align: top;margin: 0 10px 0 0;">统计</div>
				<div style="margin: 0 10px;display: inline-block;"><img src="../../images/bigdata/icon_maodian.png" style="vertical-align: top;margin: 0 10px 0 0;">锚点</div>
				<div style="margin: 0 10px;display: inline-block;"><img src="../../images/bigdata/icon_mingcheng.png" style="vertical-align: top;margin: 0 10px 0 0;">名称</div>
				<div style="margin: 0 10px;display: inline-block;">
					<span>显示边界</span>
					<div style="display: inline-block;margin: -9px 0 0 5px;vertical-align: top;">
						<input type="checkbox" name="switch" lay-skin="switch">
					</div>
				</div>
			</div>
		</div>
    </div>
    <div class="bigdata-con" style="margin: 110px 10px 10px 10px;">
	    <div style="background: #f8f8f8;height: 100%;">地图</div>
	    <div style="position: absolute;top: 0;left: 0;right: 0;bottom: 0;">
	    	<div class="layui-row layui-col-space10" style="color: #ffffff;margin: 10px 15px;text-align: center;">
			    <div class="layui-col-xs2 layui-col-sm2 layui-col-md2">
			      	<div class="gradient-red" style="border-radius: 8px;">
			      		<div class="num-title">关联幼儿园数</div>
			      		<div style="height: 94px;">
			      			<img src="../../images/bigdata/icon_tuoyoujigou.png" style="width: 37px;height: 45px;position: absolute;left: 20px;top: 72px;">
			      			<div style="display: inline-block;margin-top: 22px;">
			      				<div style="font-size: 48px;">346</div>
			      			</div>
			      		</div>
			      	</div>
			    </div>
			    <div class="layui-col-xs2 layui-col-sm2 layui-col-md2">
			        <div class="gradient-blue" style="border-radius: 8px;">
			      		<div class="num-title">幼儿园负责人数</div>
			      		<div style="height: 94px;">
			      			<img src="../../images/bigdata/icon_jiaozhigong.png" style="width: 48px;height: 42px;position: absolute;left: 20px;top: 75px;">
			      			<div style="display: inline-block;margin-top: 22px;">
			      				<div style="font-size: 48px;">346</div>
			      			</div>
			      		</div>
			      	</div>
			    </div>			    			    
			    <div class="layui-col-xs2 layui-col-sm2 layui-col-md2">
			        <div class="gradient-orange" style="border-radius: 8px;">
			      		<div class="num-title">幼儿数</div>
			      		<div style="height: 94px;">
			      			<img src="../../images/bigdata/icon_youer.png" style="width: 37px;height: 44px;position: absolute;left: 20px;top: 75px;">
			      			<div style="display: inline-block;margin-top: 22px;">
			      				<div style="font-size: 48px;">346</div>
			      			</div>
			      		</div>
			      	</div>
			    </div>
			    <div class="layui-col-xs2 layui-col-sm2 layui-col-md2">
			        <div class="gradient-green" style="border-radius: 8px;">
			      		<div class="num-title">男童数</div>
			      		<div style="height: 94px;">
			      			<img src="../../images/bigdata/icon_imgboy.png" style="width: 36px;height: 40px;position: absolute;left: 20px;top: 77px;">
			      			<div style="display: inline-block;margin-top: 22px;">
			      				<div style="font-size: 48px;">346</div>
			      			</div>
			      		</div>
			      	</div>
			    </div>
			    <div class="layui-col-xs2 layui-col-sm2 layui-col-md2">
			        <div class="gradient-pink" style="border-radius: 8px;">
			      		<div class="num-title">女童数</div>
			      		<div style="height: 94px;">
			      			<img src="../../images/bigdata/icon_imggirl.png" style="width: 35px;height: 37px;position: absolute;left: 20px;top: 77px;">
			      			<div style="display: inline-block;margin-top: 22px;">
			      				<div style="font-size: 48px;">346</div>
			      			</div>
			      		</div>
			      	</div>
			    </div>
			    <div class="layui-col-xs2 layui-col-sm2 layui-col-md2">
			        <div class="gradient-purple" style="border-radius: 8px;">
			      		<div class="num-title">家长数</div>
			      		<div style="height: 94px;">
			      			<img src="../../images/bigdata/icon_feijiaozhigong.png" style="width: 37px;height: 43px;position: absolute;left: 20px;top: 75px;">
			      			<div style="display: inline-block;margin-top: 22px;">
			      				<div style="font-size: 48px;">346</div>
			      			</div>
			      		</div>
			      	</div>
			    </div>
			</div>
	    </div>
    </div>	
</section>
<script>
//Demo
layui.use('form', function(){
  var form = layui.form;
  
  //监听提交
  form.on('submit(formDemo)', function(data){
    layer.msg(JSON.stringify(data.field));
    return false;
  });
});
</script>
</body>
</html>
