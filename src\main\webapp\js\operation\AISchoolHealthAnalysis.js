require.config({
    paths: {
        jquery: '../../sys/jquery',
        system: '../../sys/system',
        echarts: '../../plugin/echart-5.1.2/dist/echarts.min',
        echartsgl: '../../plugin/echarts-gl/dist/echarts-gl',
        commom3dbt: 'common',
        layui: '../../layui-btkj/layui',
        mock: '../../plugin/mock/dist/mock-min'
    },
    shim: {
        "system": {
            deps: ["jquery"]
        },
        "echarts": {
            deps: ["jquery"]
        },
        "echartsgl": {
            deps: ["jquery"]
        },
        "commom3dbt": {
            deps: ["jquery", "echarts"],
            exports: "commom3dbt"
        },
        "layui": {
            deps: ["jquery"],
            exports: "layui"
        },
        "mock": {
            deps: ["jquery"],
            exports: "Mock"
        }
    },
    waitSeconds: 0
});


require(["jquery", "system", "echarts", "echartsgl", "commom3dbt", "layui", "mock"], function ($, system, echarts, echartsgl, commom3dbt, layui, Mock) {
    layui.use(['form', 'laydate'], function () {
        window.layuiForm = layui.form; // 全局保存 form 模块
        window.laydate = layui.laydate // 这里获取 laydate 引用
    });

    window.echarts = echarts;
    window.echartsgl = echartsgl;
    window.commom3dbt = commom3dbt;
    window.jQuery = window.$ = $;
    /*
   ***文件创建: 2025-06-19 16:53:22
   ***创建作者: wangzenghui
   ***最后修改: 2025-06-19 16:53:22
   ***修改人员: wangzenghui
   ***内容摘要: 存放数据定义全局chartData,currentFilters
   */
    var chartData = {};
    var currentFilters = {
        kindergarten: '',
        startDate: '',
        endDate: ''
    };

    // 辅助函数：解析URL参数mock
    function parseParams(url) {
        const params = {};
        const urlObj = new URL(url);
        urlObj.searchParams.forEach((value, key) => {
            params[key] = value;
        });
        return params;
    }

    // 辅助函数：计算日期范围天数mock
    function dateRangeDays(start, end) {
        if (!start || !end) return 30;
        const startDate = new Date(start);
        const endDate = new Date(end);
        return Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
    }

    /*
    ***文件创建: 2025-07-01 19:49:06
    ***创建作者: wangzenghui
    ***最后修改: 2025-07-01 19:49:06
    ***修改人员: wangzenghui
    ***内容摘要: mock返回模拟数据，六个图标和静态卡片的mock
    */
    // 1. 入园体检合格率mock - 动态Mock
    Mock.mock(new RegExp(location.origin.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '/api/AISchoolHealthAnalysis/examinationpassrate'), 'post', function (options) {
        const params = parseParams(options.url);
        const baseRate = 70 + (parseInt(params.kindergarten || 0) * 5); // 园所影响基准值(70-90%)
        const days = dateRangeDays(params.startDate, params.endDate);
        const fluctuation = Math.min(15, days / 3); // 最多±15%波动
        const qualifiedRate = Mock.Random.float(baseRate - fluctuation, baseRate + fluctuation);
        const deferredRate = 100 - qualifiedRate;

        return {
            "code": 200,
            "data": [
                {name: '暂缓入园率', value: deferredRate},
                {name: '体检合格率', value: qualifiedRate}
            ]
        };
    });

    // 2. 营养不良分布图mock - 动态Mock
    Mock.mock(new RegExp(location.origin.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '/api/AISchoolHealthAnalysis/Physicaldevelopmentratio'), 'post', function (options) {
        const params = parseParams(options.url);
        const baseNormal = params.kindergarten ? [80, 75, 70] : [85, 80, 75]; // 正常率基准
        const baseAbnormal = params.kindergarten ? [20, 25, 30] : [15, 20, 25]; // 异常率基准
        const daysFactor = dateRangeDays(params.startDate, params.endDate) / 60;

        // 保留两位小数
        const normalData = baseNormal.map(v =>
            Mock.Random.float(v * (0.9 - daysFactor), v * (1.1 - daysFactor)).toFixed(2)
        );
        const abnormalData = baseAbnormal.map(v =>
            Mock.Random.float(v * (0.9 + daysFactor), v * (1.1 + daysFactor)).toFixed(2)
        );

        return {
            "code": 200,
            "data": {
                categories: ['体重/年龄', '身高/年龄', '体重/身高'],
                firstData: normalData,
                secondData: abnormalData
            }
        };
    });

    //通用异常率生成函数 (五官/内科/实验室/特殊儿童)mock
    function generateDynamicAbnormalData(baseData, params) {
        const daysFactor = dateRangeDays(params.startDate, params.endDate) / 90;
        const kgFactor = (parseInt(params.kindergarten || 0) + 1) * 0.1;

        return baseData.map(item => ({
            ...item,
            count: Mock.Random.float(
                item.count * (0.8 + kgFactor - daysFactor),
                item.count * (1.2 + kgFactor + daysFactor)
            ).toFixed(1)
        }));
    }

    // 3. 五官检查异常率mock
    Mock.mock(new RegExp(location.origin.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '/api/AISchoolHealthAnalysis/facialAbnormalrate'), 'post', function (options) {
        const params = parseParams(options.url);
        const baseData = [
            {"relation": "眼外观异常", "count": 2},
            {"relation": "视力异常", "count": 1.5},
            {"relation": "耳异常", "count": 3},
            {"relation": "视力未通过", "count": 0.5}
        ];
        return {
            "code": 200,
            "data": generateDynamicAbnormalData(baseData, params)
        };
    });

    // 4. 内科检查异常mock
    Mock.mock(new RegExp(location.origin.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '/api/AISchoolHealthAnalysis/medicineAbnormalRate'), 'post', function (options) {
        const params = parseParams(options.url);
        const baseData = [
            {"relation": "头颅", "count": 2},
            {"relation": "胸腔", "count": 1.5},
            {"relation": "四肢", "count": 3},
            {"relation": "心肺", "count": 3},
            {"relation": "肝脾", "count": 3},
            {"relation": "外生殖器", "count": 3},
            {"relation": "咽部", "count": 0.5},
            {"relation": "其他", "count": 3}
        ];
        return {
            "code": 200,
            "data": generateDynamicAbnormalData(baseData, params)
        };
    });

    // 5. 贫血情况mock
    Mock.mock(new RegExp(location.origin.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '/api/AISchoolHealthAnalysis/anemia'), 'post', function (options) {
        const params = parseParams(options.url);
        const baseData = [
            {"relation": "轻度", "count": 2},
            {"relation": "中度", "count": 1.5},
            {"relation": "重度", "count": 3},
        ];
        return {
            "code": 200,
            "data": generateDynamicAbnormalData(baseData, params)
        };
    });

    // 6. 特殊儿童比率mock
    Mock.mock(new RegExp(location.origin.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '/api/AISchoolHealthAnalysis/spicalchildrate'), 'post', function (options) {
        const params = parseParams(options.url);
        const baseData = [
            {"relation": "低体重", "count": 1},
            {"relation": "贫血", "count": 3},
            {"relation": "肥胖", "count": 4},
            {"relation": "超重", "count": 5},
            {"relation": "生长迟缓", "count": 2}
        ];
        return {
            "code": 200,
            "data": generateDynamicAbnormalData(baseData, params)
        };
    });

    // 7. 统计卡片数据mock - 增加动态参数
    Mock.mock(new RegExp(location.origin.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '/api/AISchoolHealthAnalysis/statistics'), 'post', function (options) {
        const params = parseParams(options.url);
        const kgFactor = parseInt(params.kindergarten || 0) * 0.3;

        return {
            "code": 200,
            "data": {
                "rytjyyrs": Mock.Random.integer(1000, 2000) * (1 + kgFactor),
                "ytjrs": Mock.Random.integer(1000, 2000) * (1 + kgFactor),
                "wtjrs": Mock.Random.integer(1000, 2000) * (1 + kgFactor),
                "tjhgrs": Mock.Random.integer(1000, 2000) * (1 + kgFactor),
                "zhryrs": Mock.Random.integer(1000, 2000) * (1 + kgFactor),
                "rytjbgfss": Mock.Random.integer(1000, 2000) * (1 + kgFactor),
                "rytjbgwfss": Mock.Random.integer(1000, 2000) * (1 + kgFactor),
                "jzyds": Mock.Random.integer(1000, 2000) * (1 + kgFactor),
            }
        };
    });

    /*
    ***文件创建: 2025-06-24
    ***创建作者: wangzenghui
    ***内容摘要: 返回年份下拉框数据
    */
    Mock.mock(new RegExp(location.origin.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '/api/AISchoolHealthAnalysis/years'), 'post', function () {
        var data = [
            {value: "0", text: "2015"},
            {value: "1", text: "2016"},
            {value: "2", text: "2017"},
            {value: "3", text: "2018"},
            {value: "4", text: "2019"},
            {value: "5", text: "2020"},
            {value: "6", text: "2021"},
            {value: "7", text: "2022"},
            {value: "8", text: "2023"},
            {value: "9", text: "2024"}
        ];
        return {
            "code": 200,
            "msg": "success",
            "data": data
        };
    });

    /*
    ***文件创建: 2025-06-24
    ***创建作者: wangzenghui
    ***内容摘要: 返回园所下拉框数据
    */
    Mock.mock(new RegExp(location.origin.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '/api/' +
        ''), 'post', function () {
        var data = [
            {value: "0", text: "阳光幼儿园"},
            {value: "1", text: "蓝天幼儿园"},
            {value: "2", text: "彩虹幼儿园"},
            {value: "3", text: "星星幼儿园"},
            {value: "4", text: "月亮幼儿园"}
        ];
        return {
            "code": 200,
            "msg": "success",
            "data": data
        };
    });

    //smaction发送参数
    /**
     * 构建查询参数对象（增强版）
     * @param {Object} source - 源数据对象
     * @param {Array|string} [fields=['startDate', 'endDate', 'kindergarten']] - 需要处理的字段
     * @param {Object} [extraParams={}] - 额外参数
     * @returns {Object} 处理后的参数对象
     */
    /**
     * 构建查询参数对象
     * @param {Object} source - 源数据对象
     * @param {Array|string} [fields=['startDate', 'endDate', 'kindergarten']] - 需要处理的字段
     * @param {Object} [extraParams={}] - 额外参数
     * @returns {Object} 处理后的参数对象
     */
    function buildQueryParamsEnhanced(source, fields, extraParams = {}) {
        const pb = {};

        // 处理fields参数，确保它总是一个数组
        const fieldsToProcess = Array.isArray(fields) ? fields :
            (typeof fields === 'string' ? [fields] :
                ['startDate', 'endDate', 'kindergarten']);

        fieldsToProcess.forEach(field => {
            const value = source[field];

            // 特殊处理kindergarten字段
            if (field === 'kindergarten') {
                // 排除0、'0'、null、undefined、空字符串
                if (value != null && value !== "" && value !== 0 && value !== '0') {
                    pb[field] = [value];
                }
            }
            // 其他字段处理
            else if (value != null && value !== "") {
                pb[field] = [value];
            }
        });

        return {...pb, ...extraParams};
    }

    // 获取年份下拉框数据
    function getYearSelectData() {
        $.smaction(function (response) {
            if (response) {
                commom3dbt.updateSelectOptions('selarea', response);
            }
        }, {}, {route: "api", action: "AISchoolHealthAnalysis/years", datastring: true});
    }

    /*
    ***文件创建: 2025-07-02 09:52:50
    ***创建作者: wangzenghui
    ***最后修改: 2025-07-02 09:52:50
    ***修改人员: wangzenghui
    ***内容摘要: 初始化日期选择器
    */
    function initDatePicker() {
        layui.use(['laydate'], function () {
            // 设置默认日期范围（最近7天）

            const today = new Date();
            const lastWeek = new Date(today);
            lastWeek.setDate(today.getDate() - 6);

            currentFilters.startDate = lastWeek.toISOString().split('T')[0];
            currentFilters.endDate = today.toISOString().split('T')[0];

            // 开始日期选择器
            laydate.render({
                elem: '#startDate',
                type: 'date',
                value: currentFilters.startDate,
                max: currentFilters.endDate, // 开始日期不能超过结束日期
                done: function (value) {
                    if (value) {
                        currentFilters.startDate = value;
                        // 更新结束日期选择器的最小日期
                        endDatePicker.config.min = value;
                        // 如果结束日期早于新的开始日期，重置结束日期
                        if (new Date(currentFilters.endDate) < new Date(value)) {
                            currentFilters.endDate = value;
                            endDatePicker.config.value = value;
                            layui.laydate.render(endDatePicker.config);
                        }
                        loadProgressAnalysisWithFilters();
                    }
                }
            });

            // 结束日期选择器
            var endDatePicker = laydate.render({
                elem: '#endDate',
                type: 'date',
                value: currentFilters.endDate,
                min: currentFilters.startDate, // 结束日期不能早于开始日期
                done: function (value) {
                    if (value) {
                        // 确保结束日期不小于开始日期
                        if (new Date(value) < new Date(currentFilters.startDate)) {
                            layui.layer.msg('结束日期不能早于开始日期', {icon: 2});
                            return false;
                        }
                        currentFilters.endDate = value;
                        loadProgressAnalysisWithFilters();
                    }
                }
            });
        });
    }

    //根据当前的日期和园所加载卡片和图表的数据
    function loadProgressAnalysisWithFilters() {
        const params = {
            startDate: currentFilters.startDate,
            endDate: currentFilters.endDate,
            kindergarten: currentFilters.kindergarten,
            course: currentFilters.course
        };

        // 同时刷新所有数据
        refreshAllData(params);
    }

    function refreshAllData(params = {}) {
        // 使用传入参数或当前筛选条件
        const filterParams = params || {
            startDate: currentFilters.startDate,
            endDate: currentFilters.endDate,
            kindergarten: currentFilters.kindergarten,
            course: currentFilters.course
        };
        // 更新当前筛选条件
        currentFilters = Object.assign({}, currentFilters, filterParams);
        console.log("%c打印参数" + currentFilters.kindergarten, "color:red");
        // 加载所有数据
        loadStatistics(currentFilters);
        examinationpassrate(currentFilters);
        Physicaldevelopmentratio(currentFilters);
        loadfacialAbnormalrate(currentFilters);       //五官检查异常率
        loadmedicineAbnormalRate(currentFilters);     //内科检查异常
        loadanemia(currentFilters);   //贫血情况
        loadspicalchildrate(currentFilters);          //特殊儿童比率
    }

    /*
    ***文件创建: 2025-07-01 14:45:40
    ***创建作者: wangzenghui
    ***最后修改: 2025-07-01 14:45:40
    ***修改人员: wangzenghui
    ***内容摘要: 获取园所下拉框数据
    */
    function getKindergartenSelectData() {
        $.sm(function (re) {
            if (re) {
                var arrhtml = ['<option value="">请选择幼儿园</option>'];
                for (var i = 0; i < re.length; i++) {
                    var item = re[i];
                    arrhtml.push('<option value="' + item.id + '">' + item.yeyname + '</option>');
                }
                $('#kindergarten').html(arrhtml.join(""));
                layui.form.render('select');
                layui.form.on("select(kindergarten)", function (data) {
                    currentFilters.kindergarten = data.value;
                    loadProgressAnalysisWithFilters();

                });
            }
        }, ["AISchoolHealthAnalysis.getyey"]);


    }

    /**
     ***文件创建: 2025-06-24
     ***创建作者: wangzenghui
     ***最后修改: 2025-06-24
     ***修改人员: wangzenghui
     ***内容摘要: 初始化点击事件  AI入园体检分析-跳转
     */
    function initDataCardClick() {
        // 定义所有可点击元素及其点击处理函数
        const clickableElements = [
            {id: 'ytjrs', handler: ytjrsElementclick, label: '已体检人数'},
            {id: 'rytjyyrs', handler: rytjyyrsElementclick, label: '入园体检预约人数'},
            {id: 'wtjrs', handler: wtjrsElementclick, label: '未体检人数'},
            {id: 'tjhgrs', handler: tjhgrsElementclick, label: '体检合格人数'},
            {id: 'zhryrs', handler: zhryrsElementclick, label: '暂缓入园人数'},
            {id: 'rytjbgfss', handler: rytjbgfssElementclick, label: '入园体检报告发送数'},
            {id: 'rytjbgwfss', handler: rytjbgwfssElementclick, label: '入园体检报告未发送数'},
            {id: 'jzyds', handler: jzydsElementclick, label: '家长阅读数'}
        ];
        // 统一处理所有元素
        clickableElements.forEach(function (item) {
            var element = document.getElementById(item.id);
            if (element) {
                // 设置公共样式
                element.style.cursor = 'pointer';
                element.style.color = '#ffffff';
                // 添加点击事件
                element.addEventListener('click', item.handler);
            } else {
            }
        });
    }

    /**
     *** 辅助函数：日期格式化
     */
    function getFormattedDate(dateString) {
        if (!dateString) return '';
        try {
            const date = new Date(dateString);
            return isNaN(date.getTime()) ? dateString : date.toISOString().split('T')[0];
        } catch (e) {
            console.warn('日期格式化失败:', e);
            return dateString;
        }
    }

    /**
     * 公共跳转函数（目标页面URL可配置，不包含course参数）
     * @param {string} targetPage 目标页面URL
     * @param {string} elementId 获取数据的DOM元素ID
     * @param {string} pageTitle 页面标题
     */
    function handleStatisticsClick(targetPage, elementId, pageTitle) {
        // 获取并处理数值（移除逗号）
        const value = document.getElementById(elementId).innerText.replace(/,/g, '');
        // 构建URL参数（去掉course参数）
        const urlWithParams = `${targetPage}?totalcount=${encodeURIComponent(value)}` +
            `&startDate=${encodeURIComponent(getFormattedDate(currentFilters.startDate))}` +
            `&endDate=${encodeURIComponent(getFormattedDate(currentFilters.endDate))}` +
            `&kindergarten=${currentFilters.kindergarten ? encodeURIComponent(currentFilters.kindergarten) : ''}`;
        // 日志输出
        console.log(`%c${pageTitle}点击之后发送的URL：${urlWithParams}`, "color:red");
        commom3dbt.openLayerDialog(urlWithParams, pageTitle, function () {
            console.log(`${pageTitle}列表跳转成功`);
        });
    }

    /**
     * 文件创建: 2025-06-24
     * 创建作者: wangzenghui
     * 最后修改: 2025-06-24
     * 修改人员: wangzenghui
     * 内容摘要: 点击 AI入园体检分析-跳转-已体检人数 跳转事件
     */
    function ytjrsElementclick() {
        handleStatisticsClick(
            'w_AISchoolHealthAnalysis_statisticsDetail.html',
            'ytjrs',
            'AI入园体检分析-统计详情列表'
        );
    }

    /**
     * 文件创建: 2025-06-24
     * 创建作者: wangzenghui
     * 最后修改: 2025-06-24
     * 修改人员: wangzenghui
     * 内容摘要: 点击 AI入园体检分析-跳转-入园体检预约人数 跳转事件
     */
    function rytjyyrsElementclick() {
        handleStatisticsClick(
            'w_AISchoolHealthAnalysis_statisticsDetail.html',
            'rytjyyrs',
            'AI入园体检分析-统计详情列表'
        );
    }

    /**
     * 文件创建: 2025-06-24
     * 创建作者: wangzenghui
     * 最后修改: 2025-06-24
     * 修改人员: wangzenghui
     * 内容摘要: 点击 AI入园体检分析-跳转-未体检人数 跳转事件
     */
    function wtjrsElementclick() {
        handleStatisticsClick(
            'w_AISchoolHealthAnalysis_statisticsDetail.html',
            'wtjrs',
            'AI入园体检分析-统计详情列表'
        );
    }

    /**
     * 文件创建: 2025-06-24
     * 创建作者: wangzenghui
     * 最后修改: 2025-06-24
     * 修改人员: wangzenghui
     * 内容摘要: 点击 AI入园体检分析-跳转-体检合格人数 跳转事件
     */
    function tjhgrsElementclick() {
        handleStatisticsClick(
            'w_AISchoolHealthAnalysis_statisticsDetail.html',
            'tjhgrs',
            'AI入园体检分析-统计详情列表'
        );
    }

    /**
     * 文件创建: 2025-06-24
     * 创建作者: wangzenghui
     * 最后修改: 2025-06-24
     * 修改人员: wangzenghui
     * 内容摘要: 点击 AI入园体检分析-跳转-暂缓入园人数 跳转事件
     */
    function zhryrsElementclick() {
        handleStatisticsClick(
            'w_AISchoolHealthAnalysis_statisticsDetail.html',
            'zhryrs',
            'AI入园体检分析-统计详情列表'
        );
    }

    /**
     * 文件创建: 2025-06-24
     * 创建作者: wangzenghui
     * 最后修改: 2025-06-24
     * 修改人员: wangzenghui
     * 内容摘要: 点击 AI入园体检分析-跳转-入园体检报告发送数 跳转事件
     */
    function rytjbgfssElementclick() {
        handleStatisticsClick(
            'w_AISchoolHealthAnalysis_statisticsDetail.html',
            'rytjbgfss',
            'AI入园体检分析-统计详情列表'
        );
    }

    /**
     * 文件创建: 2025-06-24
     * 创建作者: wangzenghui
     * 最后修改: 2025-06-24
     * 修改人员: wangzenghui
     * 内容摘要: 点击 AI入园体检分析-跳转-入园体检报告未发送数 跳转事件
     */
    function rytjbgwfssElementclick() {
        handleStatisticsClick(
            'w_AISchoolHealthAnalysis_statisticsDetail.html',
            'rytjbgwfss',
            'AI入园体检分析-统计详情列表'
        );
    }

    /**
     * 文件创建: 2025-06-24
     * 创建作者: wangzenghui
     * 最后修改: 2025-06-24
     * 修改人员: wangzenghui
     * 内容摘要: 点击 AI入园体检分析-跳转-家长阅读数 跳转事件
     */
    function jzydsElementclick() {
        handleStatisticsClick(
            'w_AISchoolHealthAnalysis_statisticsDetail.html',
            'jzyds',
            'AI入园体检分析-统计详情列表'
        );
    }

    /*
***文件创建: 2025-07-01 14:45:40
***创建作者: wangzenghui
***最后修改: 2025-07-01 14:45:40
***修改人员: wangzenghui
***内容摘要: 初始化今日/昨日数据点击事件
*/
    function initTodayYesterdayClick() {
        // 定义所有可点击元素及其处理函数
        const clickableElements = [
            {id: 'rytjyyrsToday', handler: rytjyyrsTodayClick, label: '入园体检预约人数-今日'},
            {id: 'rytjyyrsYesoday', handler: rytjyyrsYesterdayClick, label: '入园体检预约人数-昨日'},
            {id: 'ytjrsToday', handler: ytjrsTodayClick, label: '已体检人数-今日'},
            {id: 'ytjrsYesoday', handler: ytjrsYesterdayClick, label: '已体检人数-昨日'},
            {id: 'wtjrsToday', handler: wtjrsTodayClick, label: '未体检人数-今日'},
            {id: 'wtjrsYesoday', handler: wtjrsYesterdayClick, label: '未体检人数-昨日'},
            {id: 'tjhgrsToday', handler: tjhgrsTodayClick, label: '体检合格人数-今日'},
            {id: 'tjhgrsYesoday', handler: tjhgrsYesterdayClick, label: '体检合格人数-昨日'},
            {id: 'zhryrsToday', handler: zhryrsTodayClick, label: '暂缓入园人数-今日'},
            {id: 'zhryrsYesoday', handler: zhryrsYesterdayClick, label: '暂缓入园人数-昨日'},
            {id: 'rytjbgfssToday', handler: rytjbgfssTodayClick, label: '入园体检报告发送数-今日'},
            {id: 'rytjbgfssYesoday', handler: rytjbgfssYesterdayClick, label: '入园体检报告发送数-昨日'},
            {id: 'rytjbgwfssToday', handler: rytjbgwfssTodayClick, label: '入园体检报告未发送数-今日'},
            {id: 'rytjbgwfssYesoday', handler: rytjbgwfssYesterdayClick, label: '入园体检报告未发送数-昨日'},
            {id: 'jzydsToday', handler: jzydsTodayClick, label: '家长阅读数-今日'},
            {id: 'jzydsYesoday', handler: jzydsYesterdayClick, label: '家长阅读数-昨日'}
        ];
        // 统一处理所有元素
        clickableElements.forEach(function (item) {
            var element = document.getElementById(item.id);
            if (element) {
                // 设置公共样式
                element.style.cursor = 'pointer';
                element.style.color = '#ffffff';
                // 添加点击事件
                element.addEventListener('click', item.handler);
            } else {
            }
        });
    }

    /**
     * 公共跳转函数（今日/昨日数据）
     * @param {string} elementId 元素ID
     * @param {string} dateType 日期类型（today/yesterday）
     * @param {string} label 页面标题标签
     */
    function handleTodayYesterdayClick(elementId, dateType, label) {
        // 获取数值（移除非数字字符）
        const value = document.getElementById(elementId).innerText.replace(/[^0-9]/g, '');

        // 获取当前日期或昨天日期
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(today.getDate() - 1);

        const date = dateType === 'today' ? today : yesterday;
        const dateStr = date.toISOString().split('T')[0];

        // 构建URL参数
        const urlWithParams = `w_AISchoolHealthAnalysis_statisticsDetail.html?totalcount=${encodeURIComponent(value)}` +
            `&startDate=${encodeURIComponent(dateStr)}` +
            `&endDate=${encodeURIComponent(dateStr)}` +
            `&kindergarten=${currentFilters.kindergarten ? encodeURIComponent(currentFilters.kindergarten) : ''}` +
            `&dateType=${dateType}`;

        // 日志输出
        console.log(`%c${label}点击之后发送的URL：${urlWithParams}`, "color:red");

        // 打开弹窗
        commom3dbt.openLayerDialog(urlWithParams, label, function () {
            console.log(`${label}列表跳转成功`);
        });
    }

// 入园体检预约人数今日点击
    function rytjyyrsTodayClick() {
        handleTodayYesterdayClick('rytjyyrsToday', 'today', 'AI入园体检分析-入园体检预约人数-今日');
    }

// 入园体检预约人数昨日点击
    function rytjyyrsYesterdayClick() {
        handleTodayYesterdayClick('rytjyyrsYesoday', 'yesterday', 'AI入园体检分析-入园体检预约人数-昨日');
    }

// 已体检人数今日点击
    function ytjrsTodayClick() {
        handleTodayYesterdayClick('ytjrsToday', 'today', 'AI入园体检分析-已体检人数-今日');
    }

// 已体检人数昨日点击
    function ytjrsYesterdayClick() {
        handleTodayYesterdayClick('ytjrsYesoday', 'yesterday', 'AI入园体检分析-已体检人数-昨日');
    }

// 未体检人数今日点击
    function wtjrsTodayClick() {
        handleTodayYesterdayClick('wtjrsToday', 'today', 'AI入园体检分析-未体检人数-今日');
    }

// 未体检人数昨日点击
    function wtjrsYesterdayClick() {
        handleTodayYesterdayClick('wtjrsYesoday', 'yesterday', 'AI入园体检分析-未体检人数-昨日');
    }

// 体检合格人数今日点击
    function tjhgrsTodayClick() {
        handleTodayYesterdayClick('tjhgrsToday', 'today', 'AI入园体检分析-体检合格人数-今日');
    }

// 体检合格人数昨日点击
    function tjhgrsYesterdayClick() {
        handleTodayYesterdayClick('tjhgrsYesoday', 'yesterday', 'AI入园体检分析-体检合格人数-昨日');
    }

// 暂缓入园人数今日点击
    function zhryrsTodayClick() {
        handleTodayYesterdayClick('zhryrsToday', 'today', 'AI入园体检分析-暂缓入园人数-今日');
    }

// 暂缓入园人数昨日点击
    function zhryrsYesterdayClick() {
        handleTodayYesterdayClick('zhryrsYesoday', 'yesterday', 'AI入园体检分析-暂缓入园人数-昨日');
    }

// 入园体检报告发送数今日点击
    function rytjbgfssTodayClick() {
        handleTodayYesterdayClick('rytjbgfssToday', 'today', 'AI入园体检分析-入园体检报告发送数-今日');
    }

// 入园体检报告发送数昨日点击
    function rytjbgfssYesterdayClick() {
        handleTodayYesterdayClick('rytjbgfssYesoday', 'yesterday', 'AI入园体检分析-入园体检报告发送数-昨日');
    }

// 入园体检报告未发送数今日点击
    function rytjbgwfssTodayClick() {
        handleTodayYesterdayClick('rytjbgwfssToday', 'today', 'AI入园体检分析-入园体检报告未发送数-今日');
    }

// 入园体检报告未发送数昨日点击
    function rytjbgwfssYesterdayClick() {
        handleTodayYesterdayClick('rytjbgwfssYesoday', 'yesterday', 'AI入园体检分析-入园体检报告未发送数-昨日');
    }

// 家长阅读数今日点击
    function jzydsTodayClick() {
        handleTodayYesterdayClick('jzydsToday', 'today', 'AI入园体检分析-家长阅读数-今日');
    }

// 家长阅读数昨日点击
    function jzydsYesterdayClick() {
        handleTodayYesterdayClick('jzydsYesoday', 'yesterday', 'AI入园体检分析-家长阅读数-昨日');
    }


    /*
     ***文件创建: 2025-06-23 17:04:46
     ***创建作者: wangzenghui
     ***最后修改: 2025-06-23 17:04:46
     ***修改人员: wangzenghui
     ***内容摘要: 发送参数函数，统计卡片获取数据
     */
    function loadStatistics(params = {}) {
        const pb = buildQueryParamsEnhanced(currentFilters);
         $.sm(function (re, err) {
            if (err) {
                console.log('获取统计数据失败:', err);
                return;
            }
            if (re) {
                // 获取数据结果对象，并合并后续数组元素
                const data = re? re.reduce((acc, item) => {
                        const itemData = (item && item[0]) || {};
                        return Object.assign(acc, itemData);
                    }, {}) : {};
                document.getElementById('rytjyyrs').innerText = (data.rytjyyrs || 0).toLocaleString();
                document.getElementById('ytjrs').innerText = (data.ytjrs || 0).toLocaleString();
                document.getElementById('wtjrs').innerText = (data.wtjrs || 0).toLocaleString();
                document.getElementById('tjhgrs').innerText = (data.tjhgrs || 0).toLocaleString();
                document.getElementById('zhryrs').innerText = (data.zhryrs || 0).toLocaleString();
                document.getElementById('rytjbgfss').innerText = (data.rytjbgfss || 0).toLocaleString();
                document.getElementById('rytjbgwfss').innerText = (data.rytjbgwfss || 0).toLocaleString();
                document.getElementById('jzyds').innerText = (data.jzyds || 0).toLocaleString();
                // 为数据卡片添加点击事件 AI入园体检分析-跳转
                initDataCardClick();
            }
        }, [
            ["AISchoolHealthAnalysis.getRytjyyrs",
                $.msgwhere(pb),
            ],
            ["AISchoolHealthAnalysis.getYtjrs",
                $.msgwhere(pb),
            ],
            ["AISchoolHealthAnalysis.getWtjrs",
                $.msgwhere(pb),
            ],
            ["AISchoolHealthAnalysis.getTjhgrs",
                $.msgwhere(pb),
            ],
            ["AISchoolHealthAnalysis.getZhryrs",
                $.msgwhere(pb),
            ],
            ["AISchoolHealthAnalysis.getRytjbgfss",
                $.msgwhere(pb),
            ],
            ["AISchoolHealthAnalysis.getRytjbgwfss",
                $.msgwhere(pb),
            ],
            ["AISchoolHealthAnalysis.getJzyds",
                $.msgwhere(pb),
            ]
        ], {msgid: "AISchoolHealthAnalysis.statistics"});
    }

    /*
    ***文件创建: 2025-06-19 16:44:02
    ***创建作者: wangzenghui
    ***最后修改: 2025-06-19 16:44:02
    ***修改人员: wangzenghui
    ***内容摘要: smaction发送参数，入园体检合格率3D饼图
    */
    function examinationpassrate(params = {}) {
        const pb = buildQueryParamsEnhanced(currentFilters);

        $.sm(function (re, err) {
            if (err) {
                console.error('获取入园体检合格率3D饼图数据失败:', err);
                return;
            }
            // 处理直接返回数组的情况
            if (Array.isArray(re) && re.length > 0) {
                console.log('获取入园体检合格率3D饼图数据成功:', re[0]);

                // 提取后端返回的数据
                const backendData = re[0];

                // 转换为与Mock数据相同的格式
                const formattedData = [
                    {
                        name: '体检合格率',
                        value: backendData.tjhgl || 0  // 使用tjhgl字段
                    },
                    {
                        name: '暂缓入园率',
                        value: backendData.zhryl || 0  // 使用zhryl字段
                    }
                ];

                chartData.examinationpassrate = formattedData;
                console.log('转换后的入园体检合格率数据:', chartData.examinationpassrate);
                initChartBT("chart-rytjhgl");  // 初始化图表
            } else {
                console.error('返回数据格式异常:', re);
                // 可以在这里设置默认数据或显示错误提示
                chartData.examinationpassrate = [
                    {name: '体检合格率', value: 0},
                    {name: '暂缓入园率', value: 0}
                ];
                initChartBT("chart-rytjhgl");
            }
        }, ["AISchoolHealthAnalysis.examinationpassrate", $.msgwhere(pb)]);
    }

    /*
    ***文件创建: 2025-06-19 18:26:55
    ***创建作者: wangzenghui
    ***最后修改: 2025-06-19 18:26:55
    ***修改人员: wangzenghui
    ***内容摘要: smaction发送参数，营养不良分布图3D双柱状图
    */
    function Physicaldevelopmentratio(params = {}) {
        const pb = buildQueryParamsEnhanced(currentFilters);

        $.sm(function (re, err) {
            console.log('wwwwwwwwwwww:', re);
            if (err) {
                console.error('获取营养不良分布图3D双柱状图数据失败:', err);
                return;
            }

            // 处理直接返回数组的情况
            if (Array.isArray(re) && re.length > 0) {
                console.log('获取营养不良分布图3D双柱状图数据成功:', re[0]);

                // 提取后端返回的数据
                const backendData = re[0];

                // 转换为与Mock数据相同的格式
                const formattedData = {
                    categories: ['低体重', '消瘦', '生长迟缓'],
                    firstData: [
                        100 - (backendData.low_weight_percent || 0),   // 正常率 = 100% - 异常率
                        100 - (backendData.wasting_percent || 0),
                        100 - (backendData.stunting_percent || 0)
                    ],
                    secondData: [
                        backendData.low_weight_percent || 0,
                        backendData.wasting_percent || 0,
                        backendData.stunting_percent || 0
                    ]
                };

                chartData.Physicaldevelopmentdata = formattedData;
                console.log('转换后的营养不良分布图数据:', chartData.Physicaldevelopmentdata);
                initChartDub("chart-tgfybl");  // 初始化图表
            } else {
                console.error('返回数据格式异常:', re);
                // 设置默认数据
                chartData.Physicaldevelopmentdata = {
                    categories: ['低体重', '消瘦', '生长迟缓'],
                    firstData: [100, 100, 100],  // 正常率
                    secondData: [0, 0, 0]        // 异常率
                };
                initChartDub("chart-tgfybl");
            }
        }, ["AISchoolHealthAnalysis.Physicaldevelopmentratio", $.msgwhere(pb)]);
    }

    /*
    ***文件创建: 2025-06-19 18:26:55
    ***创建作者: wangzenghui
    ***最后修改: 2025-06-19 18:26:55
    ***修改人员: wangzenghui
    ***内容摘要: smaction发送参数，五官检查异常率3D双柱状图
    */
    function loadfacialAbnormalrate(params = {}) {
        const pb = buildQueryParamsEnhanced(currentFilters);

        $.sm(function (re, err) {
            if (err) {
                console.error('获取五官检查异常率数据失败:', err);
                return;
            }
            // 处理返回数据
            if (Array.isArray(re) && re.length > 0) {
                const backendData = re[0];
                console.log('获取五官检查异常率数据成功:', backendData);

                // 转换为与Mock数据相同的格式
                const formattedData = [
                    {
                        relation: "眼外观异常",
                        count: parseFloat(backendData.eye_appearance_abnormal_percent || 0).toFixed(1)
                    },
                    {
                        relation: "视力异常",
                        count: parseFloat(backendData.vision_abnormal_percent || 0).toFixed(1)
                    },
                    {
                        relation: "耳异常",
                        count: parseFloat(backendData.ear_appearance_abnormal_percent || 0).toFixed(1)
                    },
                    {
                        relation: "听力未通过",
                        count: parseFloat(backendData.hearing_failed_percent || 0).toFixed(1)
                    },
                    {
                        relation: "龋齿",
                        count: parseFloat(backendData.dental_caries_percent || 0).toFixed(1)
                    }
                ];

                chartData.facialAbnormalrate = formattedData;
                console.log('转换后的五官检查异常率数据:', chartData.facialAbnormalrate);
                facialAbnormalrate();
            } else {
                console.error('返回数据格式异常:', re);
                // 设置默认数据
                chartData.facialAbnormalrate = [
                    {"relation": "眼外观异常", "count": "0.0"},
                    {"relation": "视力异常", "count": "0.0"},
                    {"relation": "耳异常", "count": "0.0"},
                    {"relation": "听力未通过", "count": "0.0"},
                    {"relation": "龋齿", "count": "0.0"}
                ];
                facialAbnormalrate();
            }
        }, ["AISchoolHealthAnalysis.facialAbnormalrate", $.msgwhere(pb)]);
    }

    /*
    ***文件创建: 2025-06-19 18:26:55
    ***创建作者: wangzenghui
    ***最后修改: 2025-06-19 18:26:55
    ***修改人员: wangzenghui
    ***内容摘要: smaction发送参数，贫血情况3D双柱状图
    */
    function loadanemia(params = {}) {
        const pb = buildQueryParamsEnhanced(currentFilters);
        $.sm(function (re, err) {
            if (err) {
                console.error('获取贫血情况3D双柱状图数据失败:', err);
                return;
            }
            // 处理返回数据
            console.log('获取贫血情况3D双柱状图数据成功:', re);
            if (Array.isArray(re) && re.length > 0) {
                const backendData = re[0];
                console.log('获取贫血情况3D双柱状图数据成功:', re[0]);

                // 转换为与Mock数据相同的格式
                const formattedData = [
                    {"relation": "轻度", "count": backendData.hematinqd || 0},
                    {"relation": "中度", "count": backendData.hematinzd || 0},
                    {"relation": "重度", "count": backendData.hematinzzd || 0}

                ];

                chartData.anemia = formattedData;
                console.log('格式化后的数据去去去去去去去去去去:', chartData.anemia);
                renderanemia();
            } else {
                console.error('返回数据格式异常:', re);
                // 设置默认数据
                chartData.anemia = [
                    {"relation": "轻度", "count": 0},
                    {"relation": "中度", "count": 0},
                    {"relation": "重度", "count": 0}

                ];
                renderanemia();
            
            }
        }, ["AISchoolHealthAnalysis.anemia", $.msgwhere(pb)]);
      
    }

    /*
    ***文件创建: 2025-06-19 18:26:55
    ***创建作者: wangzenghui
    ***最后修改: 2025-06-19 18:26:55
    ***修改人员: wangzenghui
    ***内容摘要: smaction发送参数，特殊儿童比率3D双柱状图
    */
    function loadspicalchildrate(params = {}) {
        const pb = buildQueryParamsEnhanced(currentFilters);
        console.log('特殊儿童比率3D双柱状图发送的参数:', pb);

        $.sm(function (re, err) {
            if (err) {
                console.error('获取特殊儿童比率数据失败:', err);
                return;
            }
            if (Array.isArray(re) && re.length > 0) {
                console.log('获取特殊儿童比率数据成功:', re[0]);
                const backendData = re[0];
                const formattedData = [
                    {"relation": "低体重", "count": backendData.low_weight_percent || 0},
                    {"relation": "消瘦", "count": backendData.wasting_percent || 0},
                    {"relation": "生长迟缓", "count": backendData.stunting_percent || 0},
                    {"relation": "贫血", "count": backendData.hematin_abnormal_percent || 0},
                    {"relation": "肥胖", "count": backendData.wufat_percent || 0},
                    {"relation": "超重", "count": backendData.wuow_percent || 0}
                ];
                chartData.spicalchildrate = formattedData;
                console.log('转换后的特殊儿童比率数据:', chartData.spicalchildrate);
                initChartLD('chart-tsetbl');
            } else {
                console.error('返回数据格式异常:', re);
                chartData.spicalchildrate = [
                    {"relation": "低体重", "count": 0},
                    {"relation": "消瘦", "count": 0},
                    {"relation": "生长迟缓", "count": 0},
                    {"relation": "贫血", "count": 0},
                    {"relation": "肥胖", "count": 0},
                    {"relation": "超重", "count": 0}
                ];
                initChartLD('chart-tsetbl');
            }
        }, ["AISchoolHealthAnalysis.spicalchildrate", $.msgwhere(pb)]);
    }

    /*
    ***文件创建: 2025-06-19 18:26:55
    ***创建作者: wangzenghui
    ***最后修改: 2025-06-19 18:26:55
    ***修改人员: wangzenghui
    ***内容摘要: smaction发送参数，内科检查异常3D双柱状图
    */
    function loadmedicineAbnormalRate(params = {}) {
        const pb = buildQueryParamsEnhanced(currentFilters);
        console.log('内科检查异常3D双柱状图发送的参数:', pb);

        $.sm(function (re, err) {
            if (err) {
                console.error('获取内科检查异常3D双柱状图数据失败:', err);
                return;
            }
            // 处理返回数据
            if (Array.isArray(re) && re.length > 0) {
                const backendData = re[0];
                console.log('获取内科检查异常3D双柱状图数据成功:', re[0]);

                // 转换为与Mock数据相同的格式
                const formattedData = [
                    {"relation": "头颅", "count": backendData.head_abnormal_percent || 0},
                    {"relation": "胸腔", "count": backendData.thoracic_abnormal_percent || 0},
                    {"relation": "脊柱四肢", "count": backendData.limbs_abnormal_percent || 0},
                    {"relation": "咽部", "count": backendData.tonsil_abnormal_percent || 0},
                    {"relation": "心", "count": backendData.heart_abnormal_percent || 0},
                    {"relation": "肺", "count": backendData.lung_abnormal_percent || 0},
                    {"relation": "肝脾", "count": backendData.liver_spleen_abnormal_percent || 0},
                    {"relation": "外生殖器", "count": backendData.genitalia_abnormal_percent || 0},
                    {"relation": "皮肤", "count": backendData.skin_abnormal_percent || 0}
                ];

                chartData.medicineAbnormalRate = formattedData;
                rendermedicineAbnormalRate();
            } else {
                console.error('返回数据格式异常:', re);
                // 设置默认数据
                chartData.medicineAbnormalRate = [
                    {"relation": "头颅", "count": 0},
                    {"relation": "胸腔", "count": 0},
                    {"relation": "脊柱四肢", "count": 0},
                    {"relation": "咽部", "count": 0},
                    {"relation": "心", "count": 0},
                    {"relation": "肺", "count": 0},
                    {"relation": "肝脾", "count": 0},
                    {"relation": "外生殖器", "count": 0},
                    {"relation": "皮肤", "count": 0}
                ];
                rendermedicineAbnormalRate();
            }
        }, ["AISchoolHealthAnalysis.medicineAbnormalRate", $.msgwhere(pb)]);
    }
    /*
    ***文件创建: 2025-06-19 16:55:51
    ***创建作者: wangzenghui
    ***最后修改: 2025-06-19 16:55:51
    ***修改人员: wangzenghui
    ***内容摘要: 绘制3D饼图-AI入园体检合格率数据3D饼图
    */
    function initChartBT(chartId) {
        // 获取DOM元素并初始化图表
        var chartDom = document.getElementById(chartId);
        if (!chartDom) return;
        var myChart = echarts.init(chartDom);
        // 配置选项
        var options = {};
        // 预处理数据 - 计算每个扇形的百分比
        var pieData = chartData.examinationpassrate.map(item => {
            console.log('valuevaluevaluevaluevaluevaluevaluevaluevalue', item.value)
            const percentValue = item.value || 0; // 如果value为null/undefined/0，使用0
            return {
                ...item,
                percent: percentValue.toFixed(2) + '%' // 始终保留两位小数
            };
        });
        // 生成3D饼图配置
        commom3dbt.create3DPie(chartId, pieData, options);
        // var option = commom3dbt.getPie3D(pieData, 0, options);
        // 设置图表配置
        myChart.off('click'); // 先移除之前的监听
        // 点击事件处理
        myChart.on('click', function (params) {
            var index = params.componentIndex;
            // 确保点击的是饼图扇形部分
            if (params.seriesType === 'surface') {
                // 跳转到详情页并携带参数
                const queryString = new URLSearchParams({
                    name: params.seriesName,
                    totalcount: chartData.examinationpassrate[index],
                    timestamp: Date.now(),
                    unit: "%"// 防止缓存
                }).toString();
                console.log('queryString', queryString)
                // 构造跳转URL
                const url = `w_AISchoolHealthAnalysis_bodycheckDetail.html?queryString=${encodeURIComponent(queryString)}`;
                commom3dbt.openLayerDialog(url, `AI入园体检分析-体检详细列表`, function () {
                    console.log('AI入园体检分析-体检详细列表');
                });
            }
        });

        // 响应式调整
        window.addEventListener('resize', function () {
            myChart.resize();
        });
    }

    /*
    ***文件创建: 2025-06-19 18:31:20
    ***创建作者: wangzenghui
    ***最后修改: 2025-06-19 18:31:20
    ***修改人员: wangzenghui
    ***内容摘要: 绘制3D-营养不良分布图3D双柱形图
    */
    function initChartDub(chartId) {
        console.log(chartData.Physicaldevelopmentdata.firstData);
        console.log(chartData.Physicaldevelopmentdata.secondData);
        // 获取图表DOM元素
        var chartDom = document.getElementById(chartId);
        if (!chartDom) return;
        // 渲染双柱状图
        var myChart = commom3dbt.renderDoubleBarChart3D(
            chartId,
            chartData.Physicaldevelopmentdata.categories,
            chartData.Physicaldevelopmentdata.firstData,
            chartData.Physicaldevelopmentdata.secondData,
            {
                seriesNames: ['正常', '异常'],
                colors: {
                    first: {
                        main: ['#FFD15D', '#FFDA7D', '#FFE39D'],
                        top: '#FFD15D',
                        side: ['#E6B800', '#CC9900'],
                        stroke: '#E6B800',
                        legendColor: '#FFD15D'
                    },
                    second: {
                        main: ['#ED635E', '#F07D7D', '#F59797'],
                        top: '#ED635E',
                        side: ['#D32F2F', '#B71C1C'],
                        stroke: '#D32F2F',
                        legendColor: '#ED635E'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {type: 'shadow'},
                    backgroundColor: 'rgba(255,255,255,0.9)',
                    borderColor: '#ddd',
                    borderWidth: 1,
                    padding: [8, 12],
                    textStyle: {color: '#333', fontSize: 12},
                    formatter: function (params) {
                        const colors = {
                            '正常': '#FFD15D',
                            '异常': '#ED635E'
                        };
                        let result = `<div style="font-weight:bold">${params[0].name}</div>`;
                        params.forEach(item => {
                            const color = item.seriesName.includes('正常') ? colors['正常'] : colors['异常'];
                            const value = parseFloat(item.value).toFixed(2); // 确保显示两位小数
                            result += `<div style="display:flex;align-items:center;margin-top:5px">
                                        <span style="display:inline-block;width:10px;height:10px;background:${color};border-radius:50%;margin-right:5px"></span>
                                        ${item.seriesName}: ${value}%
                                   </div>`;
                        });
                        return result;
                    }
                },
                xAxis: {
                    type: 'category',
                    data: chartData.Physicaldevelopmentdata.categories,
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: '#666'
                        }
                    },
                    axisTick: {
                        show: true,
                        alignWithLabel: true
                    },
                    axisLabel: {
                        color: '#666',
                        fontSize: 12
                    }
                },
                yAxis: {
                    type: 'value',
                    min: 0,
                    max: 100,
                    interval: 10,
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: '#666'
                        }
                    },
                    axisTick: {
                        show: true
                    },
                    axisLabel: {
                        formatter: function(value) {
                            return parseFloat(value).toFixed(2) + '%'; // y轴标签也显示两位小数
                        },
                        color: '#666',
                        fontSize: 12
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: '#f0f0f0'
                        }
                    }
                },
                onBarClick: function (params) {
                    // 点击事件处理
                    handleBarClick(params);
                }
            }
        );

        // 点击柱子处理函数
        function handleBarClick(params) {
            console.log('点击了柱子:', params);
            // 准备要传递的数据
            const clickData = {
                seriesName: params.seriesName,  // 系列名称（正常/异常）
                category: params.name,         // 分类名称
                value: params.value,           // 数值
                unit: '%',                     // 单位
                time: params.seriesName.includes('正常') ? '正常' : '异常', // 时间段
                // 可以添加更多需要传递的信息
                timestamp: new Date().getTime()
            };
            //跳转页面并携带参数
            const queryString = new URLSearchParams(clickData).toString();
            // 解析参数用于弹窗标题
            const paramsObj = Object.fromEntries(new URLSearchParams(queryString));
            // 构造跳转URL
            const url = `w_AISchoolHealthAnalysis_bodycheckDetail.html?queryString=${encodeURIComponent(queryString)}`;
            commom3dbt.openLayerDialog(url, `AI入园体检分析-体检详细列表`, function () {
                console.log('AI入园体检分析-营养不良分布图列表跳转成功');
            });
        }

        // 响应式调整
        window.addEventListener('resize', function () {
            myChart && myChart.resize();
        });
    }


    function initChartLD(chartId) {
        var chartDom = document.getElementById(chartId);
        if (!chartDom) return;

        var myChart = echarts.init(chartDom);
        var spicalchildrateData = (chartData && chartData.spicalchildrate) || [];

        if (!Array.isArray(spicalchildrateData)) {
            console.error("spicalchildrateData 必须是数组！", spicalchildrateData);
            return;
        }

        var indicatorData = [];
        var valueData = [];
        var valueMap = {};
        var allIndicators = {
            "低体重": 0,
            "贫血": 0,
            "肥胖": 0,
            "超重": 0,
            "生长迟缓": 0,
            "消瘦": 0
        };

        spicalchildrateData.forEach(function (item) {
            if (item && item.relation && allIndicators.hasOwnProperty(item.relation)) {
                allIndicators[item.relation] = item.count;
                valueMap[item.relation] = item.count;
            }
        });

        for (var key in allIndicators) {
            indicatorData.push({ name: key, max: Math.max(10, allIndicators[key] + 2) });
            valueData.push(allIndicators[key]);
        }
        var option = {
            tooltip: {},
            radar: {
                triggerEvent: true, // 关键！允许名称触发事件
                center: ['50%', '38%'],
                radius: '50%',
                splitNumber: 4,
                name: {
                    textStyle: {
                        color: '#000',
                        backgroundColor: '#fff',
                    }
                },
                axisLine: {
                    show: false
                },
                splitArea: {
                    show: false
                },
                indicator: indicatorData
            },
            series: [{
                name: '特殊儿童比率',
                type: 'radar',
                areaStyle: {
                    normal: {
                        width: 1,
                        opacity: 0.7,
                    },
                },
                data: [{
                    itemStyle: {
                        normal: {
                            color: '#67abff',
                            lineStyle: {
                                color: '#67abff',
                            },
                        },
                    },
                    value: valueData
                }]
            }]
        };
        myChart.setOption(option);
        myChart.off('click'); // 先移除之前的监听
        // 添加点击事件监听
        myChart.on('click', function (params) {
            // 判断点击的是雷达图的指标名称
            if (params.componentType === 'radar' && params.name) {
                var indicatorName = params.name;
                var indicatorValue = valueMap[indicatorName] || 0;

                // 构造跳转URL
                var urlWithParams = 'w_AISchoolHealthAnalysis_bodycheckDetail.html?name=' + encodeURIComponent(indicatorName) +
                    '&value=' + encodeURIComponent(indicatorValue);
                console.log("encodeURIComponent在URL后面拼接参数" + urlWithParams);
                commom3dbt.openLayerDialog(urlWithParams, `AI入园体检分析-体检详细列表`, function () {
                    console.log('AI入园体检分析-特殊儿童比率列表跳转成功');
                });
            }
        });
        // 响应式调整
        window.addEventListener('resize', function () {
            myChart.resize();
        });
    }

    /*
    ***文件创建: 2025-06-23 11:51:45
    ***创建作者: wangzenghui
    ***最后修改: 2025-06-23 11:51:45
    ***修改人员: wangzenghui
    ***内容摘要: 绘制3D图-五官检查异常率
    */
    function facialAbnormalrate() {
        const myChart = echarts.init(document.getElementById('chart-wgjcycl'));
        var xAxis = [];
        var data = [];
        // 存储原始数据用于点击事件
        var rawDataMap = {};
        chartData.facialAbnormalrate.forEach(function (item) {
            xAxis.push(item.relation);
            data.push(item.count);
            rawDataMap[item.relation] = item; // 保存完整数据项
        });

        var colorConfig = [
            ["#23E2A1"], // 顶面：青绿色
            ["#1ABF8A", "rgba(26,191,138,0.3)"], // 左侧面：深青绿渐变
            ["#4CE6B5", "rgba(76,230,181,0.3)"]  // 右侧面：浅青绿渐变
        ];
        var options = {
            labelRotate: 45,
            labelInterval: 0
        };
        var option = commom3dbt.getBar3Dnew(
            xAxis,
            data,
            'Relation',
            colorConfig,
            options
        );
        // 调整 xAxis 配置，设置标签旋转
        option.xAxis = {
            type: 'category',
            data: xAxis,
            axisLabel: {
                rotate: 45, // 标签旋转角度（正值为逆时针）
                interval: 0, // 强制显示所有标签
                margin: 15,  // 标签与坐标轴的间距（避免重叠）
                fontSize: 12 // 可选：调整字体大小
            }
        };
        // 调整底部边距以适应旋转的标签
        option.grid = {
            left: '8%',
            right: '5%',
            top: '15%',
            bottom: '18%',
            containLabel: true
        };
        // 添加点击事件支持
        option.series[0].emphasis = {
            itemStyle: {
                color: '#FFA500' // 点击时高亮颜色
            }
        };
        myChart.setOption(option);
        myChart.off('click'); // 先移除之前的监听
        // 添加点击事件监听
        myChart.on('click', function (params) {
            console.log('点击参数:', params);
            // 获取点击的柱子对应的数据
            var relationName = params.name;
            var dataItem = rawDataMap[relationName];

            if (dataItem) {
                // 构造跳转URL，携带relation和count参数
                var url = 'w_AISchoolHealthAnalysis_bodycheckDetail.html?relation=' + encodeURIComponent(relationName) +
                    '&count=' + encodeURIComponent(dataItem.count);
                commom3dbt.openLayerDialog(url, `AI入园体检分析-体检详细列表`, function () {
                        console.log('AI入园体检分析-五官检查异常率页面加载完成');
                    }
                );
            }
        });
        // 确保图表占满容器
        setTimeout(function () {
            myChart.resize();
        }, 100);
    }

    /*
    ***文件创建: 2025-06-23 11:51:45
    ***创建作者: wangzenghui
    ***最后修改: 2025-06-23 11:51:45
    ***修改人员: wangzenghui
    ***内容摘要: 绘制3D图-内科检查异常
    */
    function rendermedicineAbnormalRate() {
        const myChart = echarts.init(document.getElementById('chart-nkjcycl'));
        var xAxis = [];
        var data = [];
        // 存储原始数据用于点击事件
        var rawDataMap = {};
        chartData.medicineAbnormalRate.forEach(function (item) {
            xAxis.push(item.relation);
            data.push(item.count);
            rawDataMap[item.relation] = item; // 保存完整数据项
        });

        var colorConfig = [
            ["#42A5F5"], // 顶面：蓝色
            ["#1976D2", "rgba(25,118,210,0.3)"], // 左侧面：深蓝渐变
            ["#64B5F6", "rgba(100,181,246,0.3)"] // 右侧面：浅蓝渐变
        ];
        var options = {
            labelRotate: 45,
            labelInterval: 0
        };
        var option = commom3dbt.getBar3Dnew(
            xAxis,
            data,
            'Relation',
            colorConfig,
            options
        );
        // 调整 xAxis 配置，设置标签旋转
        option.xAxis = {
            type: 'category',
            data: xAxis,
            axisLabel: {
                rotate: 45, // 标签旋转角度（正值为逆时针）
                interval: 0, // 强制显示所有标签
                margin: 15,  // 标签与坐标轴的间距（避免重叠）
                fontSize: 12 // 可选：调整字体大小
            }
        };
        // 调整底部边距以适应旋转的标签
        option.grid = {
            left: '8%',
            right: '5%',
            top: '15%',
            bottom: '18%',
            containLabel: true
        };
        myChart.setOption(option);
        myChart.off('click'); // 先移除之前的监听
        // 添加点击事件监听
        myChart.on('click', function (params) {
            console.log('点击参数:', params);
            // 获取点击的柱子对应的数据
            var relationName = params.name;
            var dataItem = rawDataMap[relationName];

            if (dataItem) {
                // 构造跳转URL，携带relation和count参数
                var url = 'w_AISchoolHealthAnalysis_bodycheckDetail.html?relation=' + encodeURIComponent(relationName) +
                    '&count=' + encodeURIComponent(dataItem.count);
                commom3dbt.openLayerDialog(url, `AI入园体检分析-体检详细列表`, function () {
                        console.log('AI入园体检分析-内科检查异常率页面加载完成');
                    }
                );
            }
        });
        // 确保图表占满容器
        setTimeout(function () {
            myChart.resize();
        }, 100);
        window.addEventListener('resize', function () {
            myChart.resize();
        });
    }

    /*
    ***文件创建: 2025-06-23 11:51:45
    ***创建作者: wangzenghui
    ***最后修改: 2025-06-23 11:51:45
    ***修改人员: wangzenghui
    ***内容摘要: 绘制3D图-贫血情况
    */
    function renderanemia() {
        const myChart = echarts.init(document.getElementById('chart-sysjcycl'));
        var xAxis = [];
        var data = [];
        // 存储原始数据用于点击事件
        var rawDataMap = {};
        //确保正确填充rawDataMap
        chartData.anemia.forEach(function (item) {
            xAxis.push(item.relation);
            data.push(item.count);
            rawDataMap[item.relation] = item; // 将完整item对象存入map
        });
        var colorConfig = [
            ["#8890FF"], // 顶面：浅紫蓝纯色
            ["#6A72E5", "rgba(106,114,229,0.3)"], // 左侧面：深紫蓝渐变
            ["#A5ABFF", "rgba(165,171,255,0.3)"]  // 右侧面：浅紫蓝渐变
        ];
        var options = {
            labelRotate: 45,
            labelInterval: 0
        };
        var option = commom3dbt.getBar3Dnew(
            xAxis,
            data,
            'Relation',
            colorConfig,
            options
        );
        // 调整 xAxis 配置，设置标签旋转
        option.xAxis = {
            type: 'category',
            data: xAxis,
            axisLabel: {
                rotate: 45, // 标签旋转角度（正值为逆时针）
                interval: 0, // 强制显示所有标签
                margin: 15,  // 标签与坐标轴的间距（避免重叠）
                fontSize: 12 // 可选：调整字体大小
            }
        };
        // 调整底部边距以适应旋转的标签
        option.grid = {
            left: '8%',
            right: '5%',
            top: '15%',
            bottom: '18%',
            containLabel: true
        };
        myChart.setOption(option);
        myChart.off('click'); // 先移除之前的监听
        // 添加点击事件监听
        myChart.on('click', function (params) {
            console.log('点击参数:', params);
            // 获取点击的柱子对应的数据
            var relationName = params.name;
            var dataItem = rawDataMap[relationName];

            if (dataItem) {
                // 构造跳转URL，携带relation和count参数
                var url = 'w_AISchoolHealthAnalysis_bodycheckDetail.html?relation=' + encodeURIComponent(relationName) +
                    '&count=' + encodeURIComponent(dataItem.count);
                commom3dbt.openLayerDialog(url, `AI入园体检分析-体检详细列表`, function () {
                        console.log('AI入园体检分析-贫血情况页面加载完成');
                    }
                );
            }
        });
        // 确保图表占满容器
        setTimeout(function () {
            myChart.resize();
        }, 100);
    }

    function init() {
        initDatePicker();//初始化日期选择器，默认最近一周
        getYearSelectData();
        getKindergartenSelectData();
        loadStatistics();
        examinationpassrate();
        Physicaldevelopmentratio();
        loadfacialAbnormalrate();       //五官检查异常率
        loadmedicineAbnormalRate();     //内科检查异常
        loadanemia();   //贫血情况
        loadspicalchildrate();          //特殊儿童比率
        initTodayYesterdayClick(); // 新增初始化今日昨日点击事件

        // 添加查询按钮事件
        $("#select").click(function () {
            // 获取筛选条件
            var kindergarten = $("#kindergarten").val();
            if (kindergarten) {
                kindergarten = currentFilters.kindergarten;
            }
            var startDate = $("#startDate").val();
            if (startDate) {
                startDate = currentFilters.startDate;
            }
            var endDate = $("#endDate").val();
            if (endDate) {
                endDate = currentFilters.endDate;
            }
            // 更新筛选条件
            currentFilters = {
                kindergarten: kindergarten,
                startDate: startDate,
                endDate: endDate
            };

            // 显示加载中
            layer.load(1);

            // 刷新所有数据
            refreshAllData();

            // 关闭加载中
            setTimeout(function () {
                layer.closeAll('loading');
            }, 500);
        });

        window.addEventListener('resize', function () {
            setTimeout(function () {
                const chartIds = [
                    'chart-rytjhgl', 'chart-tgfybl', 'chart-tsetbl', 'chart-wgjcycl',
                    'chart-nkjcycl', 'chart-sysjcycl'
                ];
                chartIds.forEach(id => {
                    const chart = echarts.getInstanceByDom(document.getElementById(id));
                    if (chart) chart.resize();
                });
            }, 200);
        });
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        setTimeout(init, 300); // 确保DOM渲染完成
    }
});
