﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>专家讲座签到记录</title>
    <link rel="stylesheet" href="../css/reset.css">
    <link rel="stylesheet" href="../css/icon.css">
    <link rel="stylesheet" href="../css/style.css"/>
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../css/medical.css">
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <!--[if lt IE 9]>
    <script src="../sys/html5shiv.min.js"></script>
    <![endif]-->
    <style type="text/css">
        html, body {
            background: #EAEFF3;
            overflow: hidden;
        }

        .layui-form-label {
            line-height: 28px;
        }
    </style>
</head>
<body>
<div class="marmain" style="min-width: 950px;">
    <div class="content-medical">
        <div class="layui-form layui-comselect" style="position: relative; ">
            <div class="btn-nextfl">
<!--                <span id="spyeyname" class="layui-form-label" style="text-align: left;">园所名称：</span>-->
<!--                <div class="layui-input-inline layui-form" style="float:left;width: 205px;">-->
<!--                    <input id="yeyname" name="yeyname" type="text" autocomplete="off" placeholder="园所名称" class="layui-input">-->
<!--                </div>-->
                <div class="layui-input-inline organhide" style="float: left;margin-left: 20px;">
                    <input id="txtkeyword" name="txtkeyword" type="text" autocomplete="off" placeholder="请输入姓名或电话" class="layui-input">
                </div>
<!--                <span class="layui-form-label isshowenrollstatus" style="width: 115px;">报名状态：</span>-->
<!--                <div class="layui-input-inline layui-form isshowenrollstatus" style="float:left;width: 85px;" id="diverollstatus">-->
<!--                    <select id="isneroll" lay-filter="isneroll">-->
<!--                        <option value="" selected="">全部</option>-->
<!--                        <option value="2">未报名</option>-->
<!--                        <option value="1">已报名</option>-->
<!--                    </select>-->
<!--                </div>-->
<!--                <span class="layui-form-label isnhide" style="width: 115px;">是否已读：</span>-->
<!--                <div class="layui-input-inline layui-form isnhide" style="float:left;width: 85px;" id="didread">-->
<!--                    <select id="isnread" lay-filter="isnread">-->
<!--                        <option value="" selected="">全部</option>-->
<!--                        <option value="1">已读</option>-->
<!--                        <option value="0">未读</option>-->
<!--                    </select>-->
<!--                </div>-->
            </div>
            <div class="btn-nextlt">
                <button class="layui-btn blockcol  mgl-20" id="search" style="margin-left: 10px;">查询</button>
            </div>
        </div>
    </div>
    <div id="content" style="width: 100%;">
        <div class="marmain-cen">
            <div class="content-medical">
                <div class="tbmargin">
                    <table id="tbmain" lay-filter="tbmain">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../js/noticesignlist.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>
