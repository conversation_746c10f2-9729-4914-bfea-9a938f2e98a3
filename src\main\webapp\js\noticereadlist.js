/**
 *
 * */
layui.config({
    base: './js/'
}).extend({ //设定模块别名
    system: '../../sys/system'
});
var objdata = {
    where: {},
    objname: {e: "医疗保健机构名称", y: "托幼机构名称", ey: "医疗保健机构名称/托幼机构名称"}
    , getdatafromypt: 0
}, tableIns;
var parentobj = null;
layui.use(['system', 'table'], function () {
    layer = layui.layer;
    table = layui.table;
    form = layui.form;
    if((Arg("comefrom") && Arg("comefrom") == 'ypt' && Arg("type") == 1) || Arg("type") == 2){
        objdata.getdatafromypt = 1;
    }
    $(".isnhide").show();
    if(Arg("totype") == "p"){
    }else{
        $(".isnshoworgan").show();
        $("#sporganoryeyname").html(objdata.objname[Arg("totype")] + "：");
        if(Arg("readtype") == "isenroll"){
            $(".isshowenrollstatus").show();
        }else{//阅读隐藏报名
            $(".isshowenrollstatus").hide();
        }
    }
    $("#isnread").val(Arg("readstatus") + "" || "");
    form.render();
    initData();
    initEvent();
});

function initEvent(){
    $("#search").click(function () {
        objdata.where = {};
        objdata.where["noticeid"] = [Arg("noticeid")];
        var swhere = objdata.where;
        var organoryeyname = $("#organoryeyname").val();//园所名称
        if (organoryeyname) {
            if(Arg("totype") == "e"){
                // swhere.receiveorganname = [organoryeyname];
            }else if(Arg("totype") == "y"){
                swhere.yeyname = [organoryeyname];
            }
        }
        var mobile = $("#txtreceivemobile").val();//接收人电话
        if (mobile) {
            swhere.mobile = [mobile];
        }
        var isneroll = $("#isneroll").val();//是否已报名
        if(isneroll){
            swhere.isneroll = [isneroll];
        }
        var isnread = $("#isnread").val();//是否已读
        if (isnread == "1") {
            swhere.isnread = [];
        }else if (isnread == "0"){
            swhere.isnread2 = [];
        }
        tableIns.reload({
            where: { //设定异步数据接口的参数
                swhere: $.msgwhere(swhere),
                fields: 'receiveareaname',
                types: 'desc'
            }
            , page: {curr: 1}
        });
    });
}

function initData(){
    var ordercol = "receiveareaname,receivefuyouname,receiveorganname,yeyname";
    var arrcols = [];
    if(Arg("totype") == "e"){
        arrcols.push({field: 'receiveorganname', title: objdata.objname[Arg("totype")], width: 300, align: 'left'});
    }else if(Arg("totype") == "y"){
        arrcols.push({field: 'yeyname', title: objdata.objname[Arg("totype")], width: 200, align: 'left'});
    }else if(Arg("totype") == "ey"){
        arrcols.push({field: 'yeyname', title: objdata.objname[Arg("totype")], width: 200, align: 'left'});
    }
    arrcols.push({field: 'fromname', title: '接收人', width: 200, align: 'center'}
        , {field: 'mobile', title: '接收人电话', width: 200, align: 'center'}
        , {field: 'pctime', title: '阅读时间', align: 'center', templet: function (d){
            return d.pctime || d.wxtime;
            }});
    if(Arg("readtype") == "isenroll"){
        arrcols.push({field: 'enrollstatus', title: '报名状态', align: 'center', templet: function (d){
            if(d.enrollstatus == "2"){
                return '<span>不报名</span>';
            }else if(d.enrollstatus == "1"){
                return '<span style="color: #1ce01c;">已报名</span>';
            }else if(d.enrollstatus == "0"){
                return '<span>未报名</span>';
            }
            }});
    }
    if(Arg("totype") == "p"){//小程序微信家长通知
        if(Arg("expertype") == "1"){//专家讲座
            arrcols.push({field: 'status', title: '报名状态', width: 200, align: 'center',templet: function (d){
                    return d.wxenrollman ? "已报名" : "未报名";
                }}
                ,{field: 'wxenrollman', title: '报名人', width: 200, align: 'center'}
                ,{field: 'uenrolltime', title: '报名时间', width: 200, align: 'center'});
        }else if(Arg("expertype") == "2") {//就诊人
            arrcols.push({field: 'status', title: '预约情况', width: 200, align: 'center',templet: function (d){
                    return d.wxenrollman ? "已预约" : "未预约";
                }},{field: 'wxenrollman', title: '就诊人', align: 'center'});
        }
        ordercol = "yeyname,creatime";
    }
    objdata.where["noticeid"] = [Arg("noticeid")];
    var isnread = $("#isnread").val();
    if(isnread){
        if (isnread == "1") {
            objdata.where.isnread = [];
        }else if (isnread == "0"){
            objdata.where.isnread2 = [];
        }
    }
    var arrsm = ["noticedetail.getnoticeread"];
    if(objdata.getdatafromypt){
        arrsm = ["noticedetail.getnoticereadFromYpt"];
    }
    tableIns = table.render({
        elem: '#tbmain'
        , url: encodeURI($.getLayUrl() + "?" + $.getSmStr(arrsm))
        , height: 'full-' + parseInt($("#tbmain").offset().top + 5)
        , where: {
            swhere: $.msgwhere(objdata.where)
            , fields: ordercol,
            types: 'desc'
        }
        , cols: [arrcols]
        , done: function (res, curr, count) {
        }
        , countNumberBool: true
        , even: true
        , page: true //是否显示分页
        , limits: [30, 50, 100, 200]
        , limit: 30 //每页默认显示的数量
    });
}