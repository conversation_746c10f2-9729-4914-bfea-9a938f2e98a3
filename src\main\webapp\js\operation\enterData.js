/*新增日期: 2025.06.20
作 者: 朱辽原
修改时间：
修改人：
內容摘要: 详情数据来源（ mockjs ）
*/
require.config({
    paths: {
        system: '../../sys/system',
        jquery: '../../sys/jquery',
        mock: '../../plugin/mock/dist/mock'
    },
    shim: {
        system: {
            deps: ['jquery']
        }
    },
    waitSeconds: 0
})

require(['mock'], (Mock) => {

    /*
    功能：拦截LayEnter请求
    参数说明：无
    返回值说明：无
    */
    Mock.mock(/\/fuyou\/LayEnter/, 'get', (param) => {
        // 解析URL中的查询参数
        const queryString = param.url.split('?')[1];
        const queryParams = {};

        if (queryString) {
            const params = queryString.split('&');
            params.forEach(param => {
                const [key, value] = param.split('=');
                queryParams[key] = decodeURIComponent(value || '');
            });
            queryParams.swhere = JSON.parse(queryParams.swhere);
        }

        switch (queryParams.arr) {
            case 'bianminService.list':
                return bianminServiceInfo(queryParams);
            case 'expertLecture.list':
                return expertLectureInfo(queryParams);
            case 'parentReservation.list':
                return parentReservationInfo(queryParams);
            case 'expertLecture.list_person':
                return expertLectureInfo_person(queryParams);
            case 'parentAppointment.list':
                return getParentAppointmentInfo(queryParams);
            case 'departmentReservation.list':
                return departmentReservationInfo(queryParams);
            case 'packageReservation.list':
                return packageReservationInfo(queryParams);
        }

    })

    /*
    功能：拦截Enter请求
    参数说明：无
    返回值说明：无
    */
    Mock.mock(/\/fuyou\/Enter/, 'get', (param) => {
        // 解析URL中的查询参数
        const queryString = param.url.split('?')[1];
        const queryParams = {};

        if (queryString) {
            const params = queryString.split('&');
            params.forEach(param => {
                const [key, value] = param.split('=');
                queryParams[key] = decodeURIComponent(value || '');
            });
            queryParams.swhere = JSON.parse(queryParams.swhere);
        }

        switch (queryParams.arr) {
            case 'bianminService.list':
                return bianminServiceInfo(queryParams);
            case 'expertLecture.list':
                return expertLectureInfo(queryParams);
            case 'parentReservation.list':
                return parentReservationInfo(queryParams);
            case 'expertLecture.list_person':
                return expertLectureInfo_person(queryParams);
            case 'parentAppointment.list':
                return getParentAppointmentInfo(queryParams);
            case 'departmentReservation.list':
                return departmentReservationInfo(queryParams);
            case 'packageReservation.list':
                return packageReservationInfo(queryParams);
        }

    })


    function packageReservationInfo(queryParams = {}) {
        const queryParam = queryParams.swhere.msg_where
        console.log('请求参数', queryParam)
        // 解析查询参数
        let limit = parseInt(queryParams.limit) || 30;
        let totalCount = typeof queryParam.count !== 'undefined' ? queryParam.count : 100;
        let currentPage = parseInt(queryParams.page) || 1;
        if (totalCount < limit) {
            limit = totalCount;
        }
        let totalPage = Math.floor(totalCount / limit) + 1
        if (currentPage === totalPage) {
            limit = totalCount - (totalPage - 1) * limit
        }

        // 处理日期时间参数
        let startDateTime = queryParam.startDateTime && queryParam.startDateTime[0] ? queryParam.startDateTime[0] : undefined;
        let endDateTime = queryParam.endDateTime && queryParam.endDateTime[0] ? queryParam.endDateTime[0] : undefined;
        let startDate = queryParam.startDate && queryParam.startDate[0] ? queryParam.startDate[0] : undefined;
        let endDate = queryParam.endDate && queryParam.endDate[0] ? queryParam.endDate[0] : undefined;

        // 如果有精确到时间的参数，使用精确时间
        if (startDateTime && endDateTime) {
            startDateTime = startDateTime.replace('+', 'T')
            endDateTime = endDateTime.replace('+', 'T')
            startDate = new Date(startDateTime);
            endDate = new Date(endDateTime);
        } else if (startDate && endDate) {
            // 只有日期没有时间的情况
            startDate = new Date(startDate);
            endDate = new Date(endDate);
            // 设置默认时间范围
            startDate.setHours(0, 0, 0, 0);
            endDate.setHours(23, 59, 59, 999);
        } else {
            // 默认最近30天
            startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
            endDate = new Date();
        }

        // 其他查询参数
        let packageName = queryParam.packageName && queryParam.packageName[0] ? queryParam.packageName[0] : '';
        let keyword = typeof queryParam.keyword !== 'undefined' ? queryParam.keyword : '';
        let isSearch = typeof queryParam.isSearch !== 'undefined' ? queryParam.isSearch : '0';
        let packageData1 = typeof queryParam.packageData !== 'undefined' ? queryParam.packageData : '';

        // 计算总记录数
        const defaultTimeRange = 24 * 60 * 60 * 1000;
        const queryTimeRange = endDate.getTime() - startDate.getTime();

        // 根据时间范围和查询条件调整总记录数
        if (startDate > endDate || new Date().setTime('23:59') < endDate) {
            totalCount = 0;
        } else {
            // 按时间范围比例调整
            if (!queryParam.count && isSearch === '1') {
                const timeRatio = Math.min(1, queryTimeRange / defaultTimeRange);
                totalCount = Math.floor(totalCount * timeRatio);
            }

            // 根据筛选条件调整记录数
            if (packageName !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(2, 5));
            }

            if (keyword !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(3, 8));
            }

            // 确保总数不为负
            totalCount = Math.max(0, totalCount);
        }

        // 生成科室预约数据
        let packageList = [];
        // 定义套餐列表
        const packageData = [
            { name: '微量元素', count: Mock.Random.integer(80, 400) },
            { name: '过敏原检测', count: Mock.Random.integer(120, 600) },
            { name: '基础套餐', count: Mock.Random.integer(90, 350) },
            { name: '其他', count: Mock.Random.integer(60, 100) }
        ];

        // 如果指定了科室名称，过滤科室列表
        let filteredPackageData = packageData;
        if (packageName !== '') {
            filteredPackageData = packageData.filter(dept => dept.name === packageName);
        }

        // 如果有关键词，进一步过滤
        // if (keyword !== '') {
        //     filteredDepartments = filteredDepartments.filter(dept => dept.name.includes(keyword));
        // }

        // 为每个科室生成数据
        for (let i = 0; i < filteredPackageData.length; i++) {
            const pkg = filteredPackageData[i];
            // 计算各种统计数据
            const totalAppointments = pkg.count;

            let successCount = 0
            // 计算成功预约数（总预约数减去取消数）
            console.log(filteredPackageData[i].name, packageData1.packageName)
            if (filteredPackageData[i].name === packageData1.packageName) {
                successCount = packageData1.successCount
            } else {
                successCount = pkg.count;
            }

            // 计算取消数（5%-20%的取消率）
            const cancelCount = Math.floor(successCount / Mock.Random.integer(5, 10));
            // 计算套餐数量（每个成功预约平均1-2个套餐）
            const avgPackagesPerAppointment = Mock.Random.float(1, 2, 1, 1);
            const packageCount = Math.floor(successCount * avgPackagesPerAppointment);

            // 生成数据对象
            const data = {
                id: i + 1,
                packageName: pkg.name,
                successCount: successCount,
                packageCount: packageCount,
                cancelCount: cancelCount
            };

            packageList.push(data);
        }
        console.log(packageList)

        return {
            code: 0,
            msg: 'success',
            count: filteredPackageData.length,
            data: packageList
        };
    }

    /*
    功能：科室预约详情数据来源（ mockjs ）
    参数说明：
      queryParams - 包含以下可能的查询参数:
        - limit: 每页条数
        - startDate: 开始日期(yyyy-MM-dd)
        - endDate: 结束日期(yyyy-MM-dd)
        - departmentName: 科室名称
        - keyword: 关键词搜索
    返回值说明：
      - code: 状态码(0表示成功)
      - msg: 状态信息
      - count: 总记录数
      - data: 预约数据列表
    */
    function departmentReservationInfo(queryParams = {}) {
        const queryParam = queryParams.swhere.msg_where
        console.log('请求参数', queryParam)

        // 解析查询参数
        let limit = parseInt(queryParams.limit) || 30;
        let totalCount = typeof queryParam.count !== 'undefined' ? queryParam.count : 100;
        let currentPage = parseInt(queryParams.page) || 1;
        if (totalCount < limit) {
            limit = totalCount;
        }
        let totalPage = Math.floor(totalCount / limit) + 1
        if (currentPage === totalPage) {
            limit = totalCount - (totalPage - 1) * limit
        }

        // 处理日期时间参数
        let startDateTime = (queryParam.startDateTime != null) ? queryParam.startDateTime[0] : undefined;
        let endDateTime = (queryParam.endDateTime != null) ? queryParam.endDateTime[0] : undefined;
        let startDate = (queryParam.startDate != null) ? queryParam.startDate[0] : undefined;
        let endDate = (queryParam.endDate != null) ? queryParam.endDate[0] : undefined;

        // 如果有精确到时间的参数，使用精确时间
        if (startDateTime && endDateTime) {
            startDateTime = startDateTime.replace('+', 'T')
            endDateTime = endDateTime.replace('+', 'T')
            startDate = new Date(startDateTime);
            endDate = new Date(endDateTime);
        } else if (startDate && endDate) {
            // 只有日期没有时间的情况
            startDate = new Date(startDate);
            endDate = new Date(endDate);
            // 设置默认时间范围
            startDate.setHours(0, 0, 0, 0);
            endDate.setHours(23, 59, 59, 999);
        } else {
            // 默认最近30天
            startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
            endDate = new Date();
        }

        // 其他查询参数
        let departmentName = (queryParam.departmentName != null) ? queryParam.departmentName[0] : '';
        let keyword = (queryParam.keyword !== undefined && queryParam.keyword !== null) ? queryParam.keyword : '';
        let isSearch = (queryParam.isSearch !== undefined && queryParam.isSearch !== null) ? queryParam.isSearch : '0';
        let packageData = (queryParam.packageData !== undefined && queryParam.packageData !== null) ? queryParam.packageData : '';

        // 计算总记录数
        const defaultTimeRange = 24 * 60 * 60 * 1000;
        const queryTimeRange = endDate.getTime() - startDate.getTime();

        // 根据时间范围和查询条件调整总记录数
        if (startDate > endDate || new Date().setTime('23:59') < endDate) {
            totalCount = 0;
        } else {
            // 按时间范围比例调整
            if (!queryParam.count) {
                const timeRatio = Math.min(1, queryTimeRange / defaultTimeRange);
                totalCount = Math.floor(totalCount * timeRatio);
            }

            // 根据筛选条件调整记录数
            if (departmentName !== '') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(2, 5));
            }

            if (keyword !== '') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(3, 8));
            }

            // 确保总数不为负
            totalCount = Math.max(0, totalCount);
        }

        // 生成科室预约数据
        let departmentList = [];
        // 定义科室列表
        const departments = [
            { name: '儿科', count: Mock.Random.integer(100, 500) },
            { name: '口腔科', count: Mock.Random.integer(80, 400) },
            { name: '眼科', count: Mock.Random.integer(120, 600) },
            { name: '耳鼻喉科', count: Mock.Random.integer(90, 450) },
            { name: '皮肤科', count: Mock.Random.integer(70, 350) },
            { name: '内科', count: Mock.Random.integer(60, 300) },
            { name: '其他', count: Mock.Random.integer(60, 300) }
        ];

        // 如果指定了科室名称，过滤科室列表
        let filteredDepartments = departments;
        if (departmentName !== '') {
            filteredDepartments = departments.filter(dept => dept.name === departmentName);
        }

        // 如果有关键词，进一步过滤
        // if (keyword !== '') {
        //     filteredDepartments = filteredDepartments.filter(dept => dept.name.includes(keyword));
        // }

        console.log(packageData.packageName)
        if (packageData.packageName !== 'all') {
            // 为每个科室生成数据
            for (let i = 0; i < filteredDepartments.length; i++) {
                const dept = filteredDepartments[i];

                // 计算成功预约数（总预约数减去取消数）
                const successCount = dept.count;

                // 计算取消数（5%-20%的取消率）
                const cancelCount = Math.floor(successCount / Mock.Random.integer(8, 12));

                // 生成数据对象
                const data = {
                    id: i + 1,
                    departmentname: dept.name,
                    successcount: successCount,
                    cancelcount: cancelCount
                };

                departmentList.push(data);
            }
        } else {
            let successCount = packageData.successCount
            // 将successCount随机分配给科室列表，分配后总和为successCount
            let remain = successCount
            let success = 0
            for (let i = 0; i < departments.length; i++) {
                const dept = departments[i];
                if (i == departments.length - 1) {
                    success = remain;
                }
                if (remain > 0) {
                    // 计算成功预约数（总预约数减去取消数）
                    success = Mock.Random.integer(1, Math.floor(remain / 2));
                    remain -= success;
                } else {
                    success = 0;
                }
                // 计算取消数（5%-20%的取消率）
                const cancelCount = Math.floor(success / Mock.Random.integer(8, 12));
                // 计算套餐数量（每个成功预约平均1-2个套餐）
                const packageCount = Mock.Random.integer(1, cancelCount);
                // 生成数据对象
                const data = {
                    id: i + 1,
                    departmentName: dept.name,
                    successCount: success,
                    packageCount: packageCount,
                    cancelCount: cancelCount
                };

                departmentList.push(data);

            }
            if (departmentName !== '') {
                departmentList = departmentList.filter(dept => dept.departmentName === departmentName);
                filteredDepartments = departmentList
            }

        }

        console.log(departmentList)

        return {
            code: 0,
            msg: 'success',
            count: filteredDepartments.length,
            data: departmentList
        };
    }

    /*
    功能：家长预约详情数据来源（ mockjs ）
    参数说明：
      queryParams - 包含以下可能的查询参数:
        - limit: 每页条数
        - startDate: 开始日期(yyyy-MM-dd)
        - endDate: 结束日期(yyyy-MM-dd)
        - gardenId: 幼儿园ID
        - dept: 科室名称
        - isCancel: 是否取消(0/1)
        - grade: 年级(小班/中班/大班)
    返回值说明：
      - code: 状态码(0表示成功)
      - msg: 状态信息
      - count: 总记录数
      - data: 预约数据列表
    */
    function getParentAppointmentInfo(queryParams = {}) {
        console.log(queryParams)
        let queryParam = queryParams.swhere.msg_where
        // 解析查询参数
        let limit = queryParams.limit || 10;
        let totalCount = queryParam.count || 100;
        let currentPage = parseInt(queryParams.page) || 1;
        if (totalCount < limit) {
            limit = totalCount;
        }
        let totalPage = Math.floor(totalCount / limit) + 1
        if (currentPage === totalPage) {
            limit = totalCount - (totalPage - 1) * limit
        }
        // 处理日期时间参数 默认为当前时间
        let startDateTime = (queryParam.startDateTime != null) ? queryParam.startDateTime[0] : undefined;
        let endDateTime = (queryParam.endDateTime != null) ? queryParam.endDateTime[0] : undefined;
        let startDate = (queryParam.startDate != null) ? queryParam.startDate[0] : undefined;
        let endDate = (queryParam.endDate != null) ? queryParam.endDate[0] : undefined;

        let isSearch = (queryParam.isSearch !== undefined && queryParam.isSearch !== null) ? queryParam.isSearch : '0';
        let deviceType = (queryParam.deviceType != null) ? queryParam.deviceType[0] : '';
        let parentRelation = (queryParam.parentRelation != null) ? queryParam.parentRelation[0] : '';
        let parentAge = (queryParam.parentAge != null) ? queryParam.parentAge[0] : '';
        let parentGender = (queryParam.parentGender != null) ? queryParam.parentGender[0] : '';
        let className = (queryParam.className != null) ? queryParam.className[0] : '';
        let childAge = (queryParam.physicalAge != null) ? queryParam.physicalAge[0] : '';
        let childGender = (queryParam.childGender != null) ? queryParam.childGender[0] : '';
        let childName = (queryParam.childName !== undefined && queryParam.childName !== null) ? queryParam.childName : ''

        // let parentAge =
        if (startDateTime && endDateTime) {
            startDateTime = startDateTime.replace('+', 'T')
            endDateTime = endDateTime.replace('+', 'T')
        }
        // 如果有精确到时间的参数，使用精确时间
        if (startDateTime && endDateTime) {
            startDate = new Date(startDateTime);
            endDate = new Date(endDateTime);
        } else if (startDate && endDate) {
            // 只有日期没有时间的情况
            startDate = new Date(startDate);
            endDate = new Date(endDate);
            // 设置默认时间范围
            if (!startDateTime) {
                startDate.setHours(0, 0, 0, 0);
            }
            if (!endDateTime) {
                endDate.setHours(23, 59, 59, 999);
            }
        } else {
            // 默认最近30天
            startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
            endDate = new Date();
        }

        let gardenId = (queryParam.gardenId != null && queryParam.gardenId[0] != null) ? queryParam.gardenId[0] : '';
        let dept = (queryParam.dept != null && queryParam.dept[0] != null) ? queryParam.dept[0] : '';
        let isCancel = (queryParam.isCancel != null && queryParam.isCancel[0] != null) ? queryParam.isCancel[0] : '';
        let grade = (queryParam.grade != null && queryParam.grade[0] != null) ? queryParam.grade[0] : '';

        const defaultTimeRange = 24 * 60 * 60 * 1000;
        // 计算查询时间范围
        const queryTimeRange = endDate.getTime() - startDate.getTime();
        // 根据时间范围比例调整总记录数
        if (startDate > endDate || new Date().setTime('23:59') < endDate) {
            totalCount = 0;
        } else {
            // 按时间范围比例调整
            if (!queryParam.count && isSearch === '1') {
                const timeRatio = Math.min(1, queryTimeRange / defaultTimeRange);
                totalCount = Math.floor(totalCount * timeRatio);
            }
            // 根据筛选条件进一步调整
            if (gardenId !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(2, 5));
            }
            if (dept !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(2, 5));
            }
            if (isCancel === '1' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(10, 15));
            }
            if (isCancel === '0' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(2, 3));
            }
            if (grade !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(3, 5));
            }
            if (deviceType !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(2, 5));
            }
            if (childAge !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(6, 9));
            }
            if (parentRelation !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(2, 5));
                let genderRelation = getRelationship().filter(item => item.id == parentRelation)[0].gender
                if (parentGender !== '' && genderRelation !== parentGender) {
                    totalCount = 0;
                }
            }
            if (childGender !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(2, 3));
            }

        }

        // 生成预约数据
        let appointmentList = [];
        const gardenNameList = getGardenNameList();
        const timeRange = endDate.getTime() - startDate.getTime();

        for (let i = 0; i < limit; i++) {
            // 生成在时间范围内的随机预约时间
            let appointmentDate;
            if (startDateTime && endDateTime) {
                // 如果有精确时间参数，确保生成的时间在指定范围内
                const startTime = startDate.getTime();
                const endTime = endDate.getTime();
                if (startTime > endTime) {
                    // 如果开始时间大于结束时间，交换它们
                    [startDate, endDate] = [endDate, startDate];
                }
                // 确保开始日期小于结束日期
                if (startDate > endDate) {
                    [startDate, endDate] = [endDate, startDate];
                }

                // 计算两个日期之间的时间差（毫秒）
                const timeDiff = endDate.getTime() - startDate.getTime();

                // 生成随机时间点（在开始和结束日期之间）
                const randomTime = startDate.getTime() + Math.random() * timeDiff;
                const randomDate = new Date(randomTime);

                // 提取年、月、日（注意月份需要加1，因为getMonth返回0-11）
                const year = randomDate.getFullYear();
                const month = randomDate.getMonth() + 1; // 转为1-12
                const day = randomDate.getDate();

                // 生成随机时间（09:00:00到18:00:00之间）
                const hour = Mock.Random.integer(startDate.getHours(), endDate.getHours());
                const minute = Mock.Random.integer(0, 59);
                const second = Mock.Random.integer(0, 59);

                // 创建最终的日期时间对象
                appointmentDate = new Date(
                    year,
                    month - 1, // 注意：Date构造函数的月份参数是0-11
                    day,
                    hour,
                    minute,
                    second
                );

                // console.log('生成的日期时间:', appointmentDate);
            } else {
                // 没有精确时间参数，按原逻辑生成
                const randomTime = startDate.getTime() + Math.random() * timeRange;
                appointmentDate = new Date(randomTime);
            }

            const data = Mock.mock({
                'id|+1': 1,
                'gardenname': function () {
                    if (gardenId !== '') {
                        return gardenNameList[gardenId - 1].name;
                    }
                    return gardenNameList[Mock.Random.integer(0, gardenNameList.length - 1)].name;
                },
                childname: childName === '' ? Mock.Random.cname() : childName,
                childgender: childGender === '' ? Mock.Random.pick(['1', '2']) : childGender,
                birthdate: function () {
                    // 生成 2023-2025 年之间的随机日期（格式：YYYY-MM-DD）
                    const year = Mock.Random.integer(2019, 2023); // 随机年份
                    const date = Mock.Random.date('MM-dd'); // 随机月日
                    return `${year}-${date}`; // 组合为完整日期
                },
                parentrelation: function () {
                },
                grade: function () {
                    return grade == '' ? Mock.Random.pick(['小班', '中班', '大班']) : grade
                },
                classname: function () {
                    if (className === '') {
                        const gradeMap = {
                            小班: ['小一班', '小二班', '小三班'],
                            中班: ['中一班', '中二班', '中三班'],
                            大班: ['大一班', '大二班', '大三班']
                        };
                        const currentGrade = grade === '' ? this.grade : grade;
                        return currentGrade && gradeMap[currentGrade]
                            ? Mock.Random.pick(gradeMap[currentGrade])
                            : '';
                    } else {
                        console.log(className)
                        return this.grade[0] + className
                    }

                },
                parentidentity: function () {
                    let pickList = getRelationship()
                    if (parentGender !== '') {
                        pickList = pickList.filter(item => item.gender === parentGender)
                    }
                    if (parentRelation !== '') {
                        pickList = pickList.filter(item => item.id === parentRelation)
                    }
                    // console.log(pickList)
                    return Mock.Random.pick(pickList.map(item => item.name))
                },
                parentname: Mock.Random.cname(),
                parentgender: function () {
                    return getRelationship().filter(item => item.name === this.parentidentity)[0].gender
                },
                parentage: function () {
                    switch (parentAge) {
                        case '1':
                            return Mock.Random.integer(10, 19)
                        case '2':
                            return Mock.Random.integer(20, 29)
                        case '3':
                            return Mock.Random.integer(30, 39)
                        case '4':
                            return Mock.Random.integer(40, 49)
                        case '5':
                            return Mock.Random.integer(50, 59)
                        case '6':
                            return Mock.Random.integer(60, 69)
                        case '7':
                            return Mock.Random.integer(70, 79)
                        case '8':
                            return Mock.Random.integer(80, 100)
                        default:
                            return Mock.Random.integer(10, 100)
                    }
                },
                address: Mock.Random.county(true),
                phone: /^1[3-9]\d{9}$/,
                devicetype: function () {
                    if (deviceType !== '') return deviceType;
                    return Mock.Random.pick(['安卓', '苹果', '其他'])
                },
                deptname: function () {
                    const deptMap = {
                        安卓: ['耳鼻喉科', '儿科', '口腔科'],
                        苹果: ['眼科', '皮肤科', '内科'],
                        其他: ['眼科', '皮肤科', '内科']
                    };
                    if (dept !== '') return dept;
                    return deptMap[this['deviceType']]
                        ? Mock.Random.pick(deptMap[this['deviceType']])
                        : '';
                },
                rechecktime: function () {
                    appointmentDate = appointmentDate.toLocaleString().replace('/', '-')
                    return appointmentDate.toLocaleString().replace('/', '-').substring(0, 19);
                },
                appopentime: function () {
                    // 确保打开APP时间在预约时间之前1-3小时
                    const openDate = new Date(appointmentDate);
                    const hoursBefore = Mock.Random.integer(1, 3);
                    openDate.setHours(openDate.getHours() - hoursBefore);
                    // 确保不早于查询开始时间
                    if (openDate < startDate) {
                        openDate.setTime(startDate.getTime() + Mock.Random.integer(1, 60) * 60 * 1000); // 1-60分钟后
                    }
                    let date = openDate.toLocaleString().replace('/', '-').substring(0, 19);
                    date = date.toLocaleString().replace('/', '-')
                    return date;
                },
                // 取消相关字段
                iscancel: isCancel !== '' ? isCancel : Mock.Random.pick(['0', '1']),
                canceltime: function () {
                    if (this.iscancel === '1') {
                        const cancelDate = new Date(appointmentDate);
                        // 取消时间在预约时间之前1-24小时
                        const hoursBefore = Mock.Random.integer(1, 24);
                        cancelDate.setHours(cancelDate.getHours() - hoursBefore);
                        // 确保不早于查询开始时间
                        if (cancelDate < startDate) {
                            cancelDate.setTime(startDate.getTime() + Mock.Random.integer(1, 60) * 60 * 1000); // 1-60分钟后
                        }
                        let date = cancelDate.toLocaleString().replace('/', '-').substring(0, 19);
                        date = date.toLocaleString().replace('/', '-')
                        return date;
                    }
                    return '';
                },
                cancelreason: function () {
                    if (this.iscancel === '1') {
                        return Mock.Random.pick(['时间冲突', '改约其他医院', '孩子已康复', '其他原因']);
                    }
                    return '';
                },
                // 到诊状态
                arrivalstatus: function () {
                    if (this.iscancel === '1') return '0';
                    return Mock.Random.pick(['0', '1', '2']); // 0未到 1已到 2迟到
                },
                arrivaltime: function () {
                    if (this.arrivalstatus === '0') return '';
                    const arrivalDate = new Date(appointmentDate);
                    if (this.arrivalstatus === '1') {
                        arrivalDate.setMinutes(arrivalDate.getMinutes() - Mock.Random.integer(5, 30));
                    } else {
                        arrivalDate.setMinutes(arrivalDate.getMinutes() + Mock.Random.integer(5, 60));
                    }
                    // 确保在查询时间范围内
                    if (arrivalDate < startDate) {
                        arrivalDate.setTime(startDate.getTime() + Mock.Random.integer(1, 60) * 60 * 1000);
                    } else if (arrivalDate > endDate) {
                        arrivalDate.setTime(endDate.getTime() - Mock.Random.integer(1, 60) * 60 * 1000);
                    }
                    let date = arrivalDate.toLocaleString().replace('/', '-').substring(0, 19);
                    date = date.toLocaleString().replace('/', '-')
                    return date;
                },
                childage: function () {
                    let ageMouth = Mock.Random.integer(1, 11);
                    let ageYear = Mock.Random.integer(1, 10);
                    switch (childAge) {
                        case '0':
                            return `${ageMouth}月`
                        case '1':
                            return `1岁${ageMouth}月`
                        case '2':
                            return `2岁${ageMouth}月`
                        case '3':
                            return `3岁${ageMouth}月`
                        case '4':
                            return `4岁${ageMouth}月`
                        case '5':
                            return `5岁${ageMouth}月`
                        case '6':
                            return `6岁${ageMouth}月`
                        case '7':
                            return `7岁${ageMouth}月`
                        case '8':
                            ageYear = Mock.Random.integer(8, 10);
                            return `${ageYear}岁${ageMouth}月`
                        default:
                            ageYear = Mock.Random.integer(1, 10);
                            return `${ageYear}岁${ageMouth}月`
                    }
                },
                // 其他信息
                remark: Mock.Random.pick([
                    '常规体检', '疫苗接种', '视力检查', '牙齿检查',
                    '生长发育评估', '过敏检查', '其他'
                ]),
                packagecount: Mock.Random.integer(0, 1),
                parentcount: Mock.Random.integer(2, 5),
                recheckcount: Mock.Random.integer(1, 5),
                cancelcount: function () {
                    return Math.floor(this.recheckcount / (Mock.Random.integer(1, 3) * 10))
                }
            })
            appointmentList.push(data);
        }
        return {
            code: 0,
            msg: 'success',
            count: totalCount,
            data: appointmentList
        };
    }

    /*
    功能：便民服务牌详情数据来源（ mockjs ）
    参数说明：无
    返回值说明：表格数据
    */
    function bianminServiceInfo(queryParams = {}) {
        const queryParam = queryParams.swhere.msg_where
        console.log(queryParam)
        // 解析查询参数
        const startDate = (queryParam.startDate != null && queryParam.startDate[0] != null)
            ? new Date(queryParam.startDate[0])
            : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        const endDate = (queryParam.endDate != null && queryParam.endDate[0] != null)
            ? new Date(queryParam.endDate[0])
            : new Date();
        const regional = (queryParam.regional && queryParam.regional && queryParam.regional[0]) ? queryParam.regional[0] : '';
        const gardenId = (queryParam.kindergartenId !== undefined && queryParam.kindergartenId !== null) ? queryParam.kindergartenId : '';
        const kinderCount = (queryParam.kinderCount !== undefined && queryParam.kinderCount !== null) ? queryParam.kinderCount : '';
        const bianminCardCount = queryParam.bianminCardCount || queryParam.count;
        const page = parseInt(queryParams.page) || 1;
        let limit = parseInt(queryParams.limit) || 30;
        const timeRange = endDate.getTime() - startDate.getTime();
        let hasServiceCard = queryParam.hasServiceCard && queryParam.hasServiceCard[0] ? queryParam.hasServiceCard[0] : ''
        // 获取总数参数并转换为数字
        const totalParentCancelCount = parseInt(queryParam.parentCancelCount ? queryParam.parentCancelCount : Mock.Random.integer(100, 500));
        const totalParentScanCount = parseInt(queryParam.parentScanCount ? queryParam.parentScanCount : Mock.Random.integer(100, 500));
        const totalAvgScanCount = parseInt(queryParam.avgScanCount ? queryParam.avgScanCount : Mock.Random.integer(100, 500));

        console.log(regional, gardenId, bianminCardCount)
        console.log(getGardenNameList({ bianminCardCount, regional }))
        // 获取并过滤幼儿园列表
        let gardens = getGardenNameList({ bianminCardCount, regional }).filter(
            garden => gardenId === '' || +garden.id === +gardenId
        );
        // console.log(gardens)
        // console.log(gardens)
        if (startDate > endDate || new Date() < endDate) {
            gardens = [];
        }

        // 计算每个园所的权重（这里使用园所ID作为权重基础）
        const weights = gardens.map(garden => garden.id.charCodeAt(0));
        const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);

        // 生成模拟数据
        let remainingScanCount = totalParentScanCount;
        let remainingCancelCount = totalParentCancelCount;
        let gardens_bak = [...gardens]
        let allData = gardens_bak.map((garden, index) => {
            const randomTime = startDate.getTime() + Math.random() * timeRange;
            const recordDate = new Date(randomTime);

            // 按权重分配预约数和取消数
            const weight = weights[index];
            let appointmentCount = 0;
            let cancelCount = 0;

            if (totalWeight > 0) {
                // 最后一个园所分配剩余数量，避免四舍五入导致的误差
                if (index === gardens.length - 1) {
                    appointmentCount = remainingScanCount;
                    cancelCount = remainingCancelCount;
                    // console.log(remainingCancelCount)
                    console.log(cancelCount)
                } else {
                    appointmentCount = Math.round(totalParentScanCount * weight / totalWeight);
                    cancelCount = Math.round(totalParentCancelCount * weight / totalWeight);
                    remainingScanCount -= appointmentCount;
                    remainingCancelCount -= cancelCount;
                }
            }

            // 生成平均扫码次数，围绕totalAvgScanCount波动
            const avgScanCount = totalAvgScanCount > 0
                ? Mock.Random.integer(
                    Math.max(1, Math.floor(totalAvgScanCount * 0.8)),
                    Math.ceil(totalAvgScanCount * 1.2)
                )
                : Mock.Random.integer(1, 5);

            let dateStr = recordDate.toISOString().replace('T', ' ').substring(0, 19);
            dateStr = dateStr.replace(/-/g, '/'); // 统一日期分隔符
            console.log(garden)
            return Mock.mock({
                id: index + 1,
                gardenId: garden.id,
                gardenName: garden.name,
                dateStr,
                regional: garden.regional,
                serviceCardCount: garden.serviceCard,
                appointmentCount: appointmentCount,
                cancelCount: cancelCount,
                avgScanCount: avgScanCount,
                childCount: Mock.Random.integer(100, 500),
                parentCount: function () {
                    return Math.floor(this.childCount / Mock.Random.integer(3, 5))
                }
            });
        });
        if (hasServiceCard === '0') {
            allData = allData.filter(item => item.serviceCardCount === 0)
        } else if (hasServiceCard === '1') {
            allData = allData.filter(item => item.serviceCardCount > 0)
        }

        // 分页处理
        const total = allData.length;
        const start = (page - 1) * limit;
        const end = Math.min(start + limit, total);
        const pageData = allData.slice(start, end);

        return {
            code: 0,
            msg: 'success',
            count: total,
            data: pageData
        };
    }

    /*
    功能：专家讲座详情数据来源（ mockjs ）
    参数说明：
      queryParams - 包含以下可能的查询参数:
        - expertLectureCount: 讲座数量
        - successCount: 发送成功数
        - failCount: 发送失败数
        - viewCount: 查看人数
        - signUpCount: 报名人数
        - checkInCount: 签到人数
        - startDate/endDate: 日期范围
        - isSearch: 是否搜索模式
    返回值说明：
      - code: 状态码(0表示成功)
      - msg: 状态信息
      - data: 讲座数据列表
    */
    function expertLectureInfo(queryParams) {
        console.log('专家讲座查询参数:', queryParams.swhere.msg_where);
        const queryParam = queryParams.swhere.msg_where;

        // 获取查询参数
        let expertLectureCount = (queryParam.expertLectureCount !== undefined && queryParam.expertLectureCount !== null) ? queryParam.expertLectureCount : 10;
        let successCount = (queryParam.successCount !== undefined && queryParam.successCount !== null) ? queryParam.successCount : null;
        let failCount = (queryParam.failCount !== undefined && queryParam.failCount !== null) ? queryParam.failCount : null;
        let viewCount = (queryParam.viewCount !== undefined && queryParam.viewCount !== null) ? queryParam.viewCount : null;
        let signUpCount = (queryParam.signUpCount !== undefined && queryParam.signUpCount !== null) ? queryParam.signUpCount : null;
        let checkInCount = (queryParam.checkInCount !== undefined && queryParam.checkInCount !== null) ? queryParam.checkInCount : null;
        let startDate = (queryParam.startDate != null && queryParam.startDate[0] != null) ? queryParam.startDate[0] : undefined;
        let endDate = (queryParam.endDate != null && queryParam.endDate[0] != null) ? queryParam.endDate[0] : undefined;
        let lectureId = (queryParam.lectureId != null && queryParam.lectureId[0] != null) ? queryParam.lectureId[0] : '';
        const isSearch = (queryParam.isSearch != null && queryParam.isSearch[0] != null) ? queryParam.isSearch[0] : '0';
        console.log(startDate, endDate);
        

        // 处理日期范围
        if (startDate && endDate) {
            startDate = new Date(startDate);
            endDate = new Date(endDate);

            // 验证日期有效性
            if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
                startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
                endDate = new Date();
            }
        } else {
            // 默认最近30天
            startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
            endDate = new Date();
        }

        // 确保开始时间不晚于结束时间
        if (startDate > endDate) {
            [startDate, endDate] = [endDate, startDate];
        }
        console.log(startDate, endDate);
        

        let expertLecturesInfo = null
        // 获取讲座基础信息
        if (lectureId === '') {
            expertLecturesInfo = getExpertLecturesInfo({
                expertLectureCount
            });
        } else {
            expertLecturesInfo = getExpertLecturesInfo({
                expertLectureCount, expertLectureId: lectureId
            });
        }


        // 生成讲座数据
        const lectureData = [];
        for (let i = 0; i < expertLecturesInfo.length; i++) {
            // 生成随机日期（在开始和结束日期之间）
            
            const timeRange = endDate.getTime() - startDate.getTime();
            const randomTime = startDate.getTime() + Math.random() * timeRange;
            const lectureDate = new Date(randomTime);
            

            // 使用传入的固定值或生成随机值
            // 处理successCount和failCount的随机分配
            let lecSuccessCount, lecFailCount;
            if (successCount !== null && successCount !== undefined) {
                // 如果是最后一个讲座，使用剩余的成功数
                if (i === expertLecturesInfo.length - 1) {
                    lecSuccessCount = successCount;
                } else {
                    // 随机分配一部分成功数
                    lecSuccessCount = Mock.Random.integer(1, Math.floor(successCount / Mock.Random.pick([3, 4, 5])));
                    successCount -= lecSuccessCount;
                }
            } else {
                lecSuccessCount = Mock.Random.integer(500, 2000);
            }

            if (failCount !== null && failCount !== undefined) {
                // 如果是最后一个讲座，使用剩余的失败数
                if (i === expertLecturesInfo.length - 1) {
                    lecFailCount = failCount;
                } else {
                    // 随机分配一部分失败数
                    lecFailCount = Mock.Random.integer(1, Math.floor(failCount / Mock.Random.pick([3, 4, 5])));
                    failCount -= lecFailCount;
                }
            } else {
                // console.log(1)
                lecFailCount = Math.floor(lecSuccessCount * Mock.Random.pick([0.01, 0.02, 0.03, 0.04, 0.05, 0.06, 0.07, 0.08, 0.08]))
            }
            // console.log(lecSuccessCount, lecFailCount)

            const lecViewCount = viewCount || Mock.Random.integer(300, 1500);
            const lecSignUpCount = signUpCount || Mock.Random.integer(200, 1000);
            let lecCheckInCount = checkInCount || Mock.Random.integer(150, 800);

            // 确保签到人数不超过报名人数
            if (lecCheckInCount > lecSignUpCount) {
                lecCheckInCount = lecSignUpCount;
            }

            // 计算触达率（保留一位小数）
            const total = lecSuccessCount + lecFailCount;
            const reachRate = total > 0
                ? ((lecSuccessCount / total) * 100).toFixed(1) + '%'
                : '0.0%';

            let date = lectureDate.toLocaleString().replace('/', '-').substring(0, 19);
            date = date.toLocaleString().replace('/', '-')
            // 创建讲座数据对象
            const item = {
                id: i + 1,
                lectureid: i + 1,
                publishtime: date,
                lecturename: expertLecturesInfo[i].name,
                successcount: lecSuccessCount,
                failcount: lecFailCount,
                viewcount: lecViewCount,
                signupcount: lecSignUpCount,
                checkincount: lecCheckInCount,
                reachrate: reachRate
            };

            lectureData.push(item);
        }

        return {
            code: 0,
            msg: 'success',
            count: lectureData.length,
            data: lectureData
        };
    }

    /*
    功能：家长预约详情数据来源（ mockjs ）
    参数说明：
      queryParams - 包含以下可能的查询参数:
        - limit: 每页条数
        - startDateTime/endDateTime: 精确时间范围
        - startDate/endDate: 日期范围
        - gardenId: 幼儿园ID
        - dept: 科室名称
        - isCancel: 是否取消(0/1)
        - grade: 年级
        - className: 班级
        - childAge: 儿童年龄
        - childGender: 儿童性别
        - parentRelation: 家长关系
        - parentGender: 家长性别
        - parentAge: 家长年龄
        - deviceType: 设备类型
    返回值说明：
      - code: 状态码(0表示成功)
      - msg: 状态信息
      - count: 总记录数
      - data: 预约数据列表
    */
    function parentReservationInfo(queryParams) {
        console.log('queryParams', queryParams.swhere.msg_where)
        let queryParam = queryParams.swhere.msg_where

        // 解析查询参数
        let limit = parseInt(queryParams.limit) || 30;
        let totalCount = queryParam.count || 100;
        let currentPage = parseInt(queryParams.page) || 1;
        if (totalCount < limit) {
            limit = totalCount;
        }
        let totalPage = Math.floor(totalCount / limit) + 1
        if (currentPage === totalPage) {
            limit = totalCount - (totalPage - 1) * limit
        }
        // 处理日期时间参数
        let startDateTime = queryParam.startDateTime && queryParam.startDateTime[0] ? queryParam.startDateTime[0] : '';
        let endDateTime = queryParam.endDateTime && queryParam.endDateTime[0] ? queryParam.endDateTime[0] : '';
        let startDate = queryParam.startDate && queryParam.startDate[0] ? queryParam.startDate[0] : '';
        let endDate = queryParam.endDate && queryParam.endDate[0] ? queryParam.endDate[0] : '';

        // 如果有精确到时间的参数，使用精确时间
        if (startDateTime && endDateTime) {
            // 处理时间格式中的+符号
            startDateTime = startDateTime.includes('+') ?
                startDateTime.replace('+', 'T') :
                startDateTime;
            endDateTime = endDateTime.includes('+') ?
                endDateTime.replace('+', 'T') :
                endDateTime;

            startDate = new Date(startDateTime);
            endDate = new Date(endDateTime);

            // 验证时间有效性
            if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
                // 如果时间格式无效，使用默认时间范围
                startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
                endDate = new Date();
            }
        } else if (startDate && endDate) {
            // 只有日期没有时间的情况
            startDate = new Date(startDate);
            endDate = new Date(endDate);

            // 验证日期有效性
            if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
                startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
                endDate = new Date();
            }

            // 设置默认时间范围
            startDate.setHours(0, 0, 0, 0);
            endDate.setHours(23, 59, 59, 999);
        } else {
            // 默认最近30天
            startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
            endDate = new Date();
            startDate.setHours(0, 0, 0, 0);
            endDate.setHours(23, 59, 59, 999);
        }

        // 确保开始时间不晚于结束时间
        if (startDate > endDate) {
            [startDate, endDate] = [endDate, startDate];
        }

        // 其他查询参数
        let deptName = queryParam.deptId ? queryParam.deptId : '';
        let gardenId = queryParam.gardenId ? queryParam.gardenId : '';
        let childAge = queryParam.childAge && queryParam.childAge[0] ? queryParam.childAge[0] : '';
        let childGender = queryParam.childGender && queryParam.childGender[0] ? queryParam.childGender[0] : '';
        let parentRelation = queryParam.parentRelation && queryParam.parentRelation[0] ? queryParam.parentRelation[0] : '';
        let parentGender = queryParam.parentGender && queryParam.parentGender[0] ? queryParam.parentGender[0] : '';
        let grade = queryParam.grade && queryParam.grade[0] ? queryParam.grade[0] : '';
        let className = queryParam.className && queryParam.className[0] ? queryParam.className[0] : '';
        let deviceType = queryParam.deviceType && queryParam.deviceType[0] ? queryParam.deviceType[0] : '';
        let dept = queryParam.dept && queryParam.dept[0] ? queryParam.dept[0] : '';
        let isCancel = queryParam.isCancel && queryParam.isCancel[0] ? queryParam.isCancel[0] : '';
        let parentAge = queryParam.parentAge && queryParam.parentAge[0] ? queryParam.parentAge[0] : '';
        let isSearch = queryParam.isSearch || '0';
        let packageName = queryParam.packageName && queryParam.packageName[0] ? queryParam.packageName[0] : '';
        let childName = queryParam.childName || ''
        let gardenName = queryParam.gardenName || ''
        if (gardenName !== '') {
            gardenId = getGardenNameList().filter(item => item.name = gardenName)[0].id
        }

        // 计算总记录数
        const defaultTimeRange = 24 * 60 * 60 * 1000;
        const queryTimeRange = endDate.getTime() - startDate.getTime();

        // 根据时间范围和查询条件调整总记录数
        if (startDate > endDate || new Date().setTime('23:59') < endDate) {
            totalCount = 0;
        } else {
            // 按时间范围比例调整
            if (!queryParam.count && isSearch === '1') {
                const timeRatio = Math.min(1, queryTimeRange / defaultTimeRange);
                totalCount = Math.floor(totalCount * timeRatio);
            }

            // 基础筛选条件调整记录数
            if (gardenId !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(2, 5));
            }
            if (dept !== '' || deptName !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(2, 5));
            }
            if (grade !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(3, 5));
            }
            if (className !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(4, 7));
            }
            if (deviceType !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(2, 5));
            }

            // 儿童相关筛选条件
            if (childAge !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(6, 9));
            }
            if (childGender !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(2, 3));
            }

            // 家长相关筛选条件
            if (parentAge !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(4, 8));
            }
            if (parentGender !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(2, 3));
            }

            // 预约相关筛选条件
            if (packageName !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(3, 6));
            }
            if (isCancel === '1' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(10, 15));
            }
            if (isCancel === '0' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(2, 3));
            }

            // 处理家长关系和性别的关联
            if (parentRelation !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(2, 5));
                let genderRelation = getRelationship().filter(item => item.id === parentRelation)[0].gender;
                if (parentGender !== '' && genderRelation !== parentGender && genderRelation !== '0') {
                    // 如果指定了性别且与关系性别不匹配（且关系性别不是"不确定"），则无数据
                    totalCount = 0;
                }
            }

            // 组合条件处理
            // 多条件组合时，进一步减少数据量
            const filterCount = [gardenId, dept, deptName, grade, className, childAge, childGender,
                parentAge, parentGender, parentRelation, deviceType, packageName, isCancel]
                .filter(item => item !== '').length;

            if (filterCount >= 3 && isSearch === '1') {
                // 三个以上筛选条件，数据量显著减少
                totalCount = Math.floor(totalCount / Math.min(filterCount, 10));
            }

            // 确保总数不为负
            totalCount = Math.max(0, totalCount);
        }

        if (totalCount < limit) {
            limit = totalCount;
        }

        const packageNames = ['微量元素', '过敏原检测', '基础套餐', '其他']
        // 生成预约数据
        let appointmentList = [];
        const gardenNameList = getGardenNameList();
        const timeRange = endDate.getTime() - startDate.getTime();

        for (let i = 0; i < limit; i++) {
            // 生成在时间范围内的随机预约时间
            let appointmentDate;
            const startTime = startDate.getTime();
            const endTime = endDate.getTime();

            // 计算两个日期之间的时间差（毫秒）
            const timeDiff = endTime - startTime;

            // 生成随机时间点（在开始和结束日期之间）
            const randomTime = startTime + Math.random() * timeDiff;
            const randomDate = new Date(randomTime);

            // 根据是否有精确时间参数来设置时间范围
            if (startDateTime && endDateTime) {
                // 使用精确的小时范围
                const startHour = startDate.getHours();
                const endHour = endDate.getHours();
                const hour = Mock.Random.integer(startHour, endHour);
                const minute = Mock.Random.integer(0, 59);
                const second = Mock.Random.integer(0, 59);

                appointmentDate = new Date(
                    randomDate.getFullYear(),
                    randomDate.getMonth(),
                    randomDate.getDate(),
                    hour,
                    minute,
                    second
                );
            } else {
                // 使用默认的工作时间范围（9:00-18:00）
                const hour = Mock.Random.integer(9, 18);
                const minute = Mock.Random.integer(0, 59);
                const second = Mock.Random.integer(0, 59);

                appointmentDate = new Date(
                    randomDate.getFullYear(),
                    randomDate.getMonth(),
                    randomDate.getDate(),
                    hour,
                    minute,
                    second
                );
            }

            // 确保生成的时间在有效范围内
            if (appointmentDate < startDate) {
                appointmentDate = new Date(startDate);
            }
            if (appointmentDate > endDate) {
                appointmentDate = new Date(endDate);
            }
            const data = Mock.mock({
                'id|+1': 1,
                'childname': function () {
                    if (childName === '') {
                        const firstNames = '张王李赵刘陈杨黄周吴徐孙马朱胡郭何高林罗郑梁谢宋唐';
                        const lastNames = '芳伟娜敏杰强婷辉娟秀英丽燕鹏磊洋旭妍婷琪梓萱子轩';
                        return firstNames.charAt(Mock.Random.integer(0, firstNames.length - 1)) +
                            lastNames.charAt(Mock.Random.integer(0, lastNames.length - 1)) +
                            lastNames.charAt(Mock.Random.integer(0, lastNames.length - 1));
                    }
                    return childName
                },
                'childgender': childGender === '' ? Mock.Random.pick(['1', '2']) : childGender,
                'birthdate': function () {
                    // 生成 2019-2023 年之间的随机日期
                    const year = Mock.Random.integer(2019, 2023);
                    const date = Mock.Random.date('MM-dd');
                    return `${year}-${date}`;
                },
                'childage': function () {
                    let ageMouth = Mock.Random.integer(1, 12);
                    let ageYear = Mock.Random.integer(1, 10);
                    switch (childAge) {
                        case '0':
                            return `${ageMouth}月`
                        case '1':
                            return `1岁${ageMouth}月`
                        case '2':
                            return `2岁${ageMouth}月`
                        case '3':
                            return `3岁${ageMouth}月`
                        case '4':
                            return `4岁${ageMouth}月`
                        case '5':
                            return `5岁${ageMouth}月`
                        case '6':
                            return `6岁${ageMouth}月`
                        case '7':
                            return `7岁${ageMouth}月`
                        case '8':
                            ageYear = Mock.Random.integer(8, 10);
                            return `${ageYear}岁${ageMouth}月`
                        default:
                            ageYear = Mock.Random.integer(1, 10);
                            return `${ageYear}岁${ageMouth}月`
                    }
                },
                'gardenname': function () {
                    if (gardenId !== '') {
                        return gardenNameList[gardenId - 1].name;
                    }
                    return gardenNameList[Mock.Random.integer(0, gardenNameList.length - 1)].name;
                },
                grade: function () {
                    return grade === '' ? Mock.Random.pick(['小班', '中班', '大班']) : grade;
                },
                classname: function () {
                    if (className === '') {
                        const gradeMap = {
                            小班: ['小一班', '小二班', '小三班'],
                            中班: ['中一班', '中二班', '中三班'],
                            大班: ['大一班', '大二班', '大三班']
                        };
                        const currentGrade = grade === '' ? this.grade : grade;
                        return currentGrade && gradeMap[currentGrade]
                            ? Mock.Random.pick(gradeMap[currentGrade])
                            : '';
                    } else {
                        return this.grade[0] + className;
                    }
                },
                parentrelation: function () {
                    let relationData;
                    if (parentRelation === '') {
                        // 根据性别筛选关系
                        let pickList = getRelationship();
                        if (parentGender !== '') {
                            pickList = pickList.filter(item => item.gender === parentGender);
                        }
                        relationData = pickList[Mock.Random.integer(0, pickList.length - 1)];
                    } else {
                        console.log(2)
                        relationData = getRelationship().filter(item => item.id === parentRelation)[0];
                    }
                    this._relationGender = relationData.gender; // 保存性别信息供后续使用
                    return relationData.name;
                },
                parentname: function () {
                    const firstNames = '刘陈杨黄周吴徐孙马朱胡郭何高林罗郑梁谢宋王张';
                    const lastNames = '芳伟娜敏杰强婷辉娟秀英丽燕鹏磊洋旭妍梓萱子轩';
                    return firstNames.charAt(Mock.Random.integer(0, firstNames.length - 1)) +
                        lastNames.charAt(Mock.Random.integer(0, lastNames.length - 1));
                },
                parentgender: function () {
                    // 使用保存的关系性别
                    return this._relationGender;
                },
                parentage: function () {
                    switch (parentAge) {
                        case '1':
                            return Mock.Random.integer(10, 19);
                        case '2':
                            return Mock.Random.integer(20, 29);
                        case '3':
                            return Mock.Random.integer(30, 39);
                        case '4':
                            return Mock.Random.integer(40, 49);
                        case '5':
                            return Mock.Random.integer(50, 59);
                        case '6':
                            return Mock.Random.integer(60, 69);
                        case '7':
                            return Mock.Random.integer(70, 79);
                        case '8':
                            return Mock.Random.integer(80, 100);
                        default:
                            return Mock.Random.integer(10, 100);
                    }
                },
                'parenteducation|1': [1, 2, 3, 4, 5], // 1:小学及以下, 2:初中, 3:高中/中专, 4:大专, 5:本科及以上
                parentEducationText: function () {
                    const eduMap = { 1: '小学及以下', 2: '初中', 3: '高中/中专', 4: '大专', 5: '本科及以上' };
                    return eduMap[this.parenteducation];
                },
                address: Mock.Random.county(true),
                phone: /^1[3-9]\d{9}$/,
                devicetype: function () {
                    if (deviceType !== '') return deviceType;
                    return Mock.Random.pick(['安卓', '苹果', '其他']);
                },
                appointmenttime: function () {
                    appointmentDate = appointmentDate.toLocaleString().replace('/', '-').substring(0, 19);
                    return appointmentDate.toLocaleString().replace('/', '-').substring(0, 19);
                },
                appointmentweek: function () {
                    const date = new Date(this.appointmentTime);
                    const weekMap = { 0: '周日', 1: '周一', 2: '周二', 3: '周三', 4: '周四', 5: '周五', 6: '周六' };
                    return weekMap[date.getDay()];
                },
                deptname: function () {
                    const deptMap = {
                        安卓: ['耳鼻喉科', '儿科', '口腔科'],
                        苹果: ['眼科', '皮肤科', '内科'],
                        其他: ['眼科', '皮肤科', '内科']
                    };
                    if (deptName !== '') return deptName;
                    return deptMap[this.devicetype]
                        ? Mock.Random.pick(deptMap[this.devicetype])
                        : '';
                },
                packagename: function () {
                    return packageName === '' ? Mock.Random.pick(packageNames) : packageName;
                },
                iscancel: isCancel !== '' ? isCancel : Mock.Random.pick(['0', '1']),
                iscanceldisplay: function () {
                    return this.isCancel === '1' ? '是' : '否';
                },
                canceltime: function () {
                    if (this.iscancel === '1') {
                        const cancelDate = new Date(appointmentDate);
                        // 取消时间在预约时间之前1-24小时
                        const hoursBefore = Mock.Random.integer(1, 24);
                        cancelDate.setHours(cancelDate.getHours() - hoursBefore);
                        // 确保不早于查询开始时间
                        if (cancelDate < startDate) {
                            cancelDate.setTime(startDate.getTime() + Mock.Random.integer(1, 60) * 60 * 1000);
                        }
                        let data = cancelDate.toLocaleString().replace('/', '-').substring(0, 19);
                        data = data.toLocaleString().replace('/', '-')
                        return data
                    }
                    return '';
                },
                cancelreason: function () {
                    if (this.iscancel === '1') {
                        return Mock.Random.pick(['时间冲突', '改约其他医院', '孩子已康复', '其他原因']);
                    }
                    return '';
                },
                arrivalstatus: function () {
                    if (this.iscancel === '1') return '0';
                    return Mock.Random.pick(['0', '1', '2']); // 0未到 1已到 2迟到
                },
                arrivalstatustext: function () {
                    const statusMap = { 0: '未到诊', 1: '已到诊', 2: '迟到' };
                    return statusMap[this.arrivalstatus];
                },
                arrivaltime: function () {
                    if (this.arrivalstatus === '0') return '';
                    const arrivalDate = new Date(appointmentDate);
                    if (this.arrivalstatus === '1') {
                        arrivalDate.setMinutes(arrivalDate.getMinutes() - Mock.Random.integer(5, 30));
                    } else {
                        arrivalDate.setMinutes(arrivalDate.getMinutes() + Mock.Random.integer(5, 60));
                    }
                    // 确保在查询时间范围内
                    if (arrivalDate < startDate) {
                        arrivalDate.setTime(startDate.getTime() + Mock.Random.integer(1, 60) * 60 * 1000);
                    } else if (arrivalDate > endDate) {
                        arrivalDate.setTime(endDate.getTime() - Mock.Random.integer(1, 60) * 60 * 1000);
                    }
                    let data = arrivalDate.toLocaleString().replace('/', '-').substring(0, 19);
                    data = data.toLocaleString().replace('/', '-')
                    return data
                }
            });

            appointmentList.push(data);
        }

        return {
            code: 0,
            msg: 'success',
            count: totalCount,
            data: appointmentList
        };
    }

    /*
    功能：专家讲座详情数据来源（ mockjs ）
    参数说明：queryParams - 包含搜索条件和分页参数的对象
    返回值说明：包含表格数据的对象（code, msg, count, data）
    */
    function expertLectureInfo_person(queryParams) {
        console.log('queryParams', queryParams)
        let queryParam = queryParams.swhere.msg_where

        // 解析查询参数
        let limit = parseInt(queryParams.limit) || 30;
        let totalCount = queryParam.count || 100;
        let currentPage = parseInt(queryParams.page) || 1;
        if (totalCount < limit) {
            limit = totalCount;
        }
        let totalPage = Math.floor(totalCount / limit) + 1
        if (currentPage === totalPage) {
            limit = totalCount - (totalPage - 1) * limit
        }

        // 处理日期时间参数
        let startDateTime = queryParam.startDateTime && queryParam.startDateTime[0] ? queryParam.startDateTime[0] : '';
        let endDateTime = queryParam.endDateTime && queryParam.endDateTime[0] ? queryParam.endDateTime[0] : '';
        let startDate = queryParam.startDate && queryParam.startDate[0] ? queryParam.startDate[0] : '';
        let endDate = queryParam.endDate && queryParam.endDate[0] ? queryParam.endDate[0] : '';

        // 如果有精确到时间的参数，使用精确时间
        if (startDateTime && endDateTime) {
            startDateTime = startDateTime.replace('+', 'T')
            endDateTime = endDateTime.replace('+', 'T')
            startDate = new Date(startDateTime);
            endDate = new Date(endDateTime);
        } else if (startDate && endDate) {
            // 只有日期没有时间的情况
            startDate = new Date(startDate);
            endDate = new Date(endDate);
            // 设置默认时间范围
            startDate.setHours(0, 0, 0, 0);
            endDate.setHours(23, 59, 59, 999);
        } else {
            // 默认最近30天
            startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
            endDate = new Date();
        }

        // 确保开始时间不晚于结束时间
        if (startDate > endDate) {
            [startDate, endDate] = [endDate, startDate];
        }

        // 其他查询参数
        const lectureId = (queryParam.lectureId != null && queryParam.lectureId[0] != null) ? queryParam.lectureId[0] : '';
        const gardenId = (queryParam.gardenId != null && queryParam.gardenId[0] != null) ? queryParam.gardenId[0] : '';
        const areaDistribution = (queryParam.areaDistribution != null && queryParam.areaDistribution[0] != null) ? queryParam.areaDistribution[0] : '';
        const childGender = (queryParam.childGender != null && queryParam.childGender[0] != null) ? queryParam.childGender[0] : '';
        const className = (queryParam.className != null && queryParam.className[0] != null) ? queryParam.className[0] : '';
        const grade = (queryParam.grade != null && queryParam.grade[0] != null) ? queryParam.grade[0] : '';
        const deviceType = (queryParam.deviceType != null && queryParam.deviceType[0] != null) ? queryParam.deviceType[0] : '';
        const gardenName = (queryParam.gardenName != null && queryParam.gardenName[0] != null) ? queryParam.gardenName[0] : '';
        const isRegister = (queryParam.isRegister != null && queryParam.isRegister[0] != null) ? queryParam.isRegister[0] : '';
        const isSign = (queryParam.isSign != null && queryParam.isSign[0] != null) ? queryParam.isSign[0] : '';
        const isView = (queryParam.isView != null && queryParam.isView[0] != null) ? queryParam.isView[0] : '';
        const isSuccess = (queryParam.isSuccess != null && queryParam.isSuccess[0] != null) ? queryParam.isSuccess[0] : '';
        const keyword = (queryParam.keyword != null && queryParam.keyword[0] != null) ? queryParam.keyword[0] : '';
        const parentAge = (queryParam.parentAge != null && queryParam.parentAge[0] != null) ? queryParam.parentAge[0] : '';
        const parentGender = (queryParam.parentGender != null && queryParam.parentGender[0] != null) ? queryParam.parentGender[0] : '';
        const parentRelation = (queryParam.parentRelation != null && queryParam.parentRelation[0] != null) ? queryParam.parentRelation[0] : '';
        const x = (queryParam.physicalAge != null && queryParam.physicalAge[0] != null) ? queryParam.physicalAge[0] : '';
        const isSearch = (queryParam.isSearch !== undefined && queryParam.isSearch !== null) ? queryParam.isSearch : ''
        let expertLectureCount = queryParam.expertLectureCount !== undefined && queryParam.expertLectureCount !== null ? queryParam.expertLectureCount : 10;
        let lectureTitle = queryParam.title ? queryParam.title : ''
        console.log(expertLectureCount);
        
        // 获取讲座信息
        let lectures = getExpertLecturesInfo({ expertLectureCount });

        if (lectureId != '') {
            lectures = getExpertLecturesInfo({ expertLectureId: lectureId });
        }

        if (lectures.length === 0) {
            return {
                code: 0,
                msg: 'success',
                count: 0,
                data: []
            };
        }

        // 计算总记录数
        const defaultTimeRange = 24 * 60 * 60 * 1000;
        const queryTimeRange = endDate.getTime() - startDate.getTime();

        // 根据时间范围和查询条件调整总记录数
        if (startDate > endDate || new Date().setTime('23:59') < endDate) {
            totalCount = 0;
        } else {
            // 按时间范围比例调整
            if (!queryParam.count && isSearch === '1') {
                const timeRatio = Math.min(1, queryTimeRange / defaultTimeRange);
                totalCount = Math.floor(totalCount * timeRatio);
            }

            // 根据筛选条件调整记录数
            if (gardenName !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(2, 5));
            }
            if (childGender !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(2, 3));
            }
            if (grade !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(3, 5));
            }
            if (className !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(4, 7));
            }
            if (parentRelation !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(2, 5));
                if (parentRelation === '其他') {
                    totalCount = Math.floor(totalCount / Mock.Random.integer(20, 30));
                }
            }
            if (parentGender !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(2, 3));
            }
            if (isRegister !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(2, 3));
            }
            if (isSign !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(3, 5));
            }
            if (isView !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(2, 4));
            }
            if (isSuccess !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(2, 3));
            }
            if (lectureId !== '' && isSearch === '1') {
                totalCount = Math.floor(totalCount / Mock.Random.integer(1, 7));
            }
        }

        // 生成参与人员数据
        let personList = [];
        const gardenNameList = getGardenNameList();
        const timeRange = endDate.getTime() - startDate.getTime();

        console.log(limit)
        for (let i = 0; i < limit; i++) {
            const lecture = Mock.Random.pick(lectures);
            // 生成随机日期（在开始和结束日期之间）
            const randomTime = startDate.getTime() + Math.random() * timeRange;
            const recordDate = new Date(randomTime);

            // 生成家长信息
            let parentData = Mock.mock({
                'id|+1': 1,
                'childname': Mock.Random.cname(),
                'childgender': childGender === '' ? Mock.Random.pick(['1', '2']) : childGender,
                'birthdate': function () {
                    const year = Mock.Random.integer(2019, 2023);
                    const date = Mock.Random.date('MM-dd');
                    return `${year}-${date}`;
                },
                'gardenname': function () {
                    if (gardenName !== '') {
                        return gardenNameList[gardenName - 1].name;
                    }
                    return gardenNameList[Mock.Random.integer(0, gardenNameList.length - 1)].name;
                },
                'grade': grade === '' ? Mock.Random.pick(['小班', '中班', '大班']) : grade,
                'classname': function () {
                    if (className === '') {
                        const gradeMap = {
                            小班: ['小一班', '小二班', '小三班'],
                            中班: ['中一班', '中二班', '中三班'],
                            大班: ['大一班', '大二班', '大三班']
                        };
                        const currentGrade = grade === '' ? this.grade : grade;
                        return currentGrade && gradeMap[currentGrade]
                            ? Mock.Random.pick(gradeMap[currentGrade])
                            : '';
                    } else {
                        return this.grade[0] + className;
                    }
                },
                'parentrelation': function () {
                    let pickList = getRelationship();

                    if (parentGender !== '' && parentRelation !== '18') {
                        pickList = pickList.filter(item => item.gender === parentGender);
                    }
                    if (parentRelation !== '') {
                        pickList = pickList.filter(item => item.id === parentRelation);
                    }

                    return Mock.Random.pick(pickList.map(item => item.name));
                },
                'parentname': Mock.Random.cname(),
                'parentgender': function () {
                    return getRelationship().filter(item => item.name === this.parentrelation)[0].gender;
                },
                'parentage': function () {
                    switch (parentAge) {
                        case '1':
                            return Mock.Random.integer(10, 19);
                        case '2':
                            return Mock.Random.integer(20, 29);
                        case '3':
                            return Mock.Random.integer(30, 39);
                        case '4':
                            return Mock.Random.integer(40, 49);
                        case '5':
                            return Mock.Random.integer(50, 59);
                        case '6':
                            return Mock.Random.integer(60, 69);
                        case '7':
                            return Mock.Random.integer(70, 79);
                        case '8':
                            return Mock.Random.integer(80, 100);
                        default:
                            return Mock.Random.integer(20, 60);
                    }
                },
                'address': Mock.Random.county(true),
                'phone': /^1[3-9]\d{9}$/,
                'devicetype': deviceType === '' ? Mock.Random.pick(['安卓', '苹果', '其他']) : deviceType,
                'appopentime': function () {
                    // 确保在查询时间范围内
                    let openDate = new Date(recordDate);
                    if (openDate < startDate) {
                        openDate = new Date(startDate);
                    }
                    if (openDate > endDate) {
                        openDate = new Date(endDate);
                    }
                    let data = openDate.toLocaleString().replace('/', '-').substring(0, 19);
                    data = data.toLocaleString().replace('/', '-')
                    return data;
                },
                'viewtime': function () {
                    if (isView === '0') return '';
                    let viewDate = new Date(recordDate);
                    viewDate.setMinutes(viewDate.getMinutes() + Mock.Random.integer(1, 30));

                    // 确保在查询时间范围内
                    if (viewDate < startDate) {
                        viewDate = new Date(startDate);
                    }
                    if (viewDate > endDate) {
                        viewDate = new Date(endDate);
                    }

                    let data = viewDate.toLocaleString().replace('/', '-').substring(0, 19);
                    data = data.toLocaleString().replace('/', '-')
                    return data;
                },
                'registertime': function () {
                    if (isRegister === '0') return '';
                    let registerDate = new Date(recordDate);
                    registerDate.setMinutes(registerDate.getMinutes() + Mock.Random.integer(5, 60));

                    // 确保在查询时间范围内
                    if (registerDate < startDate) {
                        registerDate = new Date(startDate);
                    }
                    if (registerDate > endDate) {
                        registerDate = new Date(endDate);
                    }

                    let data = registerDate.toLocaleString().replace('/', '-').substring(0, 19);
                    data = data.toLocaleString().replace('/', '-')
                    return data;
                },
                'signtime': function () {
                    if (isSign === '0' || isRegister === '0') return '';
                    let signDate = new Date(recordDate);
                    signDate.setMinutes(signDate.getMinutes() + Mock.Random.integer(10, 120));

                    // 确保在查询时间范围内
                    if (signDate < startDate) {
                        signDate = new Date(startDate);
                    }
                    if (signDate > endDate) {
                        signDate = new Date(endDate);
                    }

                    let data = signDate.toLocaleString().replace('/', '-').substring(0, 19);
                    data = data.toLocaleString().replace('/', '-')
                    return data
                },
                'issuccess': isSuccess === '' ? Mock.Random.pick(['0', '1']) : isSuccess
            });
            // console.log(lecture)
            // 添加讲座信息
            if (lectureTitle) {
                lecture.name = lectureTitle
            }
            const data = {
                ...parentData,
                lectureid: lecture.id,
                lecturename: lecture.name
            };

            personList.push(data);
        }

        if (parentGender && parentRelation === '18') {
            personList = personList.filter(item => item.parentGender === parentGender)
            totalCount = personList.length
        }


        return {
            code: 0,
            msg: 'success',
            count: totalCount,
            data: personList
        };
    }

    /*
    功能：拦截Enter请求
    参数说明：无
    返回值说明：无
    */
    Mock.mock(/\/fuyou\/Enter/, 'post', (param) => {
        // console.log(param)
        // 解析URL中的查询参数
        const queryString = param && param.body ? param.body : '';
        let queryParams = {};
        if (queryString) {
            const params = queryString.split('&');
            params.forEach(param => {
                const [key, value] = param.split('=');
                queryParams[key] = decodeURIComponent(value || '');
            });
        }
        let path = {}
        let params = {}
        console.log(queryParams.arr)
        if (queryParams.arr.includes('%15')) {
            path = queryParams.arr.split('%15')[0]
            let arr = queryParams.arr.replace(/%22/g, "\"");
            arr = arr.replace(/%15/g, ":")
            arr = arr.replace(`${path}:`, '')
            params = JSON.parse(arr)
            // console.log('查询参数:', params);
        } else {
            path = queryParams.arr
        }


        switch (path) {
            case 'garden.list':
                return getGardenNameList(params);
            case 'garden.relationship':
                return getRelationship(params)
            case 'expertLecture.info':
                return getExpertLecturesInfo(params)
        }
    })

    function getRelationship() {
        let data = [
            {
                id: '1',
                name: '爸爸',
                count: 197,
                gender: '1' // 男性
            },
            {
                id: '2',
                name: '妈妈',
                count: 215,
                gender: '2' // 女性
            },
            {
                id: '3',
                name: '爷爷',
                count: 55,
                gender: '1' // 男性
            },
            {
                id: '4',
                name: '奶奶',
                count: 46,
                gender: '2' // 女性
            },
            {
                id: '5',
                name: '外公',
                count: 36,
                gender: '1' // 男性
            },
            {
                id: '6',
                name: '外婆',
                count: 42,
                gender: '2' // 女性
            },
            {
                id: '7',
                name: '叔叔',
                count: 10,
                gender: '1' // 男性
            },
            {
                id: '8',
                name: '阿姨',
                count: 11,
                gender: '2' // 女性
            },
            {
                id: '9',
                name: '婶婶',
                count: 6,
                gender: '2' // 女性
            },
            {
                id: '10',
                name: '姑妈',
                count: 3,
                gender: '2' // 女性
            },
            {
                id: '11',
                name: '姑父',
                count: 3,
                gender: '1' // 男性
            },
            {
                id: '12',
                name: '伯父',
                count: 4,
                gender: '1' // 男性
            },
            {
                id: '13',
                name: '伯母',
                count: 7,
                gender: '2' // 女性
            },
            {
                id: '14',
                name: '舅舅',
                count: 2,
                gender: '1' // 男性
            },
            {
                id: '15',
                name: '舅妈',
                count: 2,
                gender: '2' // 女性
            },
            {
                id: '16',
                name: '哥哥',
                count: 2,
                gender: '1' // 男性
            },
            {
                id: '17',
                name: '姐姐',
                count: 3,
                gender: '2' // 女性
            },
            {
                id: '18',
                name: '其他',
                count: 13,
                gender: Mock.Random.pick(['1', '2'])
            }
        ];
        return data;
    }

    /*
     功能：生成幼儿园名称列表
     参数说明：无
     返回值说明：幼儿园名称列表
     */
    function getGardenNameList(queryParams = {}) {
        // console.log(queryParams)
        let regional = queryParams.regional || ''
        let bianminCardCount = queryParams.bianminCardCount || 0
        let data = [
            {
                id: '1',
                name: '北京市第一幼儿园附属实验园',
                regional: '西城区',
                serviceCard: 1,
                geo: [116.368043, 39.921905],
            },
            {
                id: '2',
                name: '北京市第一幼儿园',
                regional: '东城区',
                serviceCard: 1,
                geo: [116.412403, 39.917531],
            },
            {
                id: '3',
                name: '丰台区第一幼儿园',
                regional: '丰台区',
                serviceCard: 1,
                geo: [116.338476, 39.851702],
            },
            {
                id: '4',
                name: '石景山区第三幼儿园',
                regional: '石景山区',
                serviceCard: 1,
                geo: [116.229604, 39.902406],
            },
            {
                id: '5',
                name: '通州区幼儿园',
                regional: '通州区',
                serviceCard: 1,
                geo: [116.676326, 39.934874],
            },
            {
                id: "6",
                name: "北京市海淀区幼儿园（万柳园区）",
                regional: "海淀区",
                serviceCard: 1,
                geo: [116.302145, 39.974523]
            },
            {
                id: "7",
                name: "海淀区中关村第一幼儿园",
                regional: "海淀区",
                serviceCard: 1,
                geo: [116.318927, 39.984216]
            },
            {
                id: "8",
                name: "海淀区世纪城幼儿园",
                regional: "海淀区",
                serviceCard: 1,
                geo: [116.287436, 39.953109]
            },
            {
                id: "9",
                name: "海淀区上地实验幼儿园",
                regional: "海淀区",
                serviceCard: 1,
                geo: [116.354218, 40.002367]
            },
            {
                id: "10",
                name: "海淀区四季青幼儿园",
                regional: "海淀区",
                serviceCard: 1,
                geo: [116.274589, 39.941276]
            },
            {
                id: "11",
                name: "海淀区北太平庄幼儿园",
                regional: "海淀区",
                serviceCard: 1,
                geo: [116.342765, 39.968432]
            }
        ]
        if (regional !== '') {
            data = data.filter(item => item.regional === regional)
        }
        if (bianminCardCount > 0) {
            for (let i = 0; i < data.length - 1; i++) {
                let itemCount = Mock.Random.integer(1, Math.floor(bianminCardCount / Mock.Random.pick([2, 3, 4])))
                data[i].serviceCard = itemCount
                bianminCardCount -= itemCount
            }
            data[data.length - 1].serviceCard = bianminCardCount
        }
        return data
    }

    function getExpertLecturesInfo(queryParams = {}) {

        console.log(queryParams)

        let data = [
            { id: '1', name: '蛀牙了怎么办?' },
            { id: '2', name: '孩子是假性近视 还是近视?' },
            { id: '3', name: '过敏性鼻炎生活 护理知识' },
            { id: '4', name: '儿童常见呼吸道感染的预防措施' },
            { id: '5', name: '如何培养孩子良好的刷牙习惯' },
            { id: '6', name: '儿童身高发育的关键影响因素' },
            { id: '7', name: '幼儿辅食添加的科学顺序与禁忌' },
            { id: '8', name: '儿童心理健康与情绪管理引导' },
            { id: '9', name: '学龄前儿童视力保护核心方法' },
            { id: '10', name: '儿童过敏体质的日常饮食调理策略' },
            { id: '11', name: '孩子挑食厌食的行为矫正技巧' },
            { id: '12', name: '儿童安全教育与家庭防护要点' },
            { id: '13', name: '婴幼儿常见皮疹的识别与护理' },
            { id: '14', name: '儿童运动能力发展与科学锻炼方案' },
            { id: '15', name: '学龄儿童注意力不集中的改善方法' },
            { id: '16', name: '儿童牙齿矫正的最佳时机与选择' },
            { id: '17', name: '幼儿入园适应期的心理调适指南' },
            { id: '18', name: '儿童意外伤害的紧急处理与急救措施' },
            { id: '19', name: '儿童阅读兴趣培养的科学方法' },
            { id: '20', name: '如何帮助孩子建立时间管理意识' },
            { id: '21', name: '亲子沟通中的倾听与表达技巧' },
            { id: '22', name: '儿童社交能力发展与同伴交往引导' },
            { id: '23', name: '幼儿情绪爆发时的应对与疏导策略' },
            { id: '24', name: '儿童创造力培养的家庭互动游戏设计' },
            { id: '25', name: '如何应对孩子的入园/入学焦虑' },
            { id: '26', name: '儿童财商启蒙的实践教育方法' },
            { id: '27', name: '家庭教育中的规则制定与执行技巧' },
            { id: '28', name: '儿童抗挫折能力培养的正确方式' },
            { id: '29', name: '亲子运动促进儿童感统发展的方案' },
            { id: '30', name: '如何引导孩子合理使用电子设备' },
            { id: '31', name: '儿童艺术启蒙与审美能力培养' },
            { id: '32', name: '二孩家庭的亲子公平相处之道' },
            { id: '33', name: '儿童注意力训练的游戏化方法' },
            { id: '34', name: '家庭教育中的正向激励策略' }
        ]


        const length = queryParams.expertLectureCount ? queryParams.expertLectureCount : data.length
        data = data.slice(0, length)
        const id = queryParams.expertLectureId ? queryParams.expertLectureId : ''
        if (id !== '') {
            data = data.filter(item => item.id === id)
        }
        return data
    }
})

/*
功能：家长预约详情数据来源（ mockjs ）
参数说明：date - 日期对象、日期字符串或时间戳
返回值说明：对应的中文周几（如：周一、周二）
*/
// function getChineseWeekday(date) {
//     // 如果未传入参数，默认为当前日期
//     if (!date) {
//         date = new Date();
//     }
//
//     // 如果传入的是字符串或数字，尝试转换为日期对象
//     if (typeof date === 'string' || typeof date === 'number') {
//         date = new Date(date);
//     }
//
//     // 检查是否为有效的日期对象
//     if (isNaN(date.getTime())) {
//         throw new Error('Invalid date');
//     }
//
//     // 定义周几的中文映射
//     const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
//
//     // 获取星期几（0-6）并返回对应的中文表示
//     return weekdays[date.getDay()];
// }