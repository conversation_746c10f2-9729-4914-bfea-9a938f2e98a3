/**
 * 专家讲座报名签到表
 * */
layui.config({
    base: './js/'
}).extend({ //设定模块别名
    system: '../../sys/system'
});
var objdata = {
    where: {}
}, tableIns;
var parentobj = null;
layui.use(['system', 'table'], function () {
    layer = layui.layer;
    table = layui.table;
    form = layui.form;
    form.render();
    initData();
    initEvent();
});

function initEvent(){
    $("#search").click(function () {
        objdata.where = {};
        objdata.where["noticeid"] = [Arg("noticeid")];
        var swhere = objdata.where;
        // var yeyname = $("#yeyname").val();//园所名称
        // if (yeyname) {
        //     swhere.yeyname = [yeyname];
        // }
        var txtkeyword = $("#txtkeyword").val();//姓名手机号
        if (txtkeyword) {
            swhere.keyword = [txtkeyword];
        }
        // var isneroll = $("#isneroll").val();//是否已报名
        // if(isneroll == 1){//已报名
        //     swhere.wxenrollstatus = [];
        // }else if(isneroll == 2){
        //     swhere.wxenrollstatus2 = [];
        // }
        // var isnread = $("#isnread").val();//是否已读
        // if (isnread == "1") {
        //     swhere.isnread = [];
        // }else if (isnread == "0"){
        //     swhere.isnread2 = [];
        // }
        tableIns.reload({
            where: { //设定异步数据接口的参数
                swhere: $.msgwhere(swhere),
                fields: 'creatime',
                types: ''
            }
            , page: {curr: 1}
        });
    });
}

function initData(){
    var arrcols = [];
    // if(Arg("totype") == "p"){
    //     arrcols.push({field: 'receiveorganname', title: objdata.objname[Arg("totype")], width: 300, align: 'left'});
    // }else if(Arg("totype") == "y"){
    //     arrcols.push({field: 'yeyname', title: objdata.objname[Arg("totype")], width: 200, align: 'left'});
    // }
    arrcols.push({field: 'mobile', title: '签到人手机号', width: 150, align: 'center'}
        , {field: 'truename', title: '签到人', width: 200, align: 'center'}
        , {field: 'signtime', title: '签到时间', align: 'center',templet: function (d){
            return d.signtime;
            }});
    objdata.where["noticeid"] = [Arg("noticeid")];
    tableIns = table.render({
        elem: '#tbmain'
        , url: encodeURI($.getLayUrl() + "?" + $.getSmStr(["noticesignlist.getnoticesign"]))
        , height: 'full-' + parseInt($("#tbmain").offset().top + 5)
        , where: {
            swhere: $.msgwhere(objdata.where)
            , fields: 'creatime',
            types: ''
        }
        , cols: [arrcols]
        // , skin: 'row' //表格风格
        , done: function (res, curr, count) {
            //res即为：{data: [], count: 99} data为当前页数据、count为数据总长度
            // var arrid = [];
            // for (var i = 0; i < res.data.length; i++) {
            //     arrid.push(res.data[i].id);
            //     var item = res.data[i];
            //     if (item.affiliate == "fuyou" && item.isopen == 0) {// 这里是判断需要禁用的条件
            //         // checkbox 根据条件设置不可选中;
            //     } else {
            //         var tr = $(".layui-table tr[data-index=" + i + "]");
            //         tr.find("input[type='checkbox']").remove();
            //         tr.find('.layui-form-checkbox').removeClass("layui-form-checked").remove();
            //     }
            // }
        }
        , countNumberBool: true
        , even: true
        , page: true //是否显示分页
        , limits: [30, 50, 100, 200]
        , limit: 30 //每页默认显示的数量
    });
}
