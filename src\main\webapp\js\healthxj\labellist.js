/**
 *标签管理（标签库）
 * */
layui.config({
    base: './js/'
}).extend({ //设定模块别名
    system: '../../../sys/system'
});
var objdata = {
    objtoken: {},
    areacode: {}
    , where: {}
}, tableIns;
var parentobj = null;
var table = null;
var layer = null;
layui.use(['system', 'table', 'form'], function () {
    layer = layui.layer;
    table = layui.table;
    initEvent();//初始化事件 

    initData();//初始化数据 

});


function initEvent() {
    $("#btnadd").click(function () {
        winopen("add");
    });

    $("#search").click(function () {
        var swhere = JSON.parse(JSON.stringify(objdata.where));
        if ($("input[name='labelname']").val()) {
            swhere.labelname = [$("input[name='labelname']").val()];
        }
        tableIns.reload({
            where: { //设定异步数据接口的参数
                swhere: $.msgwhere(swhere)
            }
            , page: {curr: 1}
        });
    });

    table.on('tool(tbmain)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
        var data = obj.data; //获得当前行数据
        var layEvent = obj.event; //获得 lay-event 对应的值
        var tr = obj.tr; //获得当前行 tr 的DOM对象
        if (layEvent === 'editaddyey') { //编辑
            winopen("edit", data);
        } else if (layEvent === 'btndel') {//删除
            parent.layer.confirm("确定要删除该标签吗？", {
                icon: 3,
                title: "删除确认",
                btn: ["确定", "取消"],
                btnAlign: "c"
            }, function (index2) {
                $.sm(function (re, err) {
                    if (re) {
                        tableIns.reload();
                        parent.layer.msg('删除成功');
                    } else {
                        parent.layer.msg('删除失败，请稍后再试！');
                    }
                    parent.layer.close(index2);
                }, ['labellist.del', data.id]);
            });
        }
    });
    $("#addxm").click(function () {
        winopen("add");
    });
}

function initData() {

    // 分类映射
    var categoryMap = {
        'hw': '身高体重',
        'eye': '眼科',
        'listen': '听力',
        'matin': '血红蛋白',
        'car': '口腔',
        'xf': '胸部腹部',
        'neike': '内科检查',
        'general': '通用'
    };

    var arrcols = [[ //标题栏
        {type: 'numbers', title: '序号', width: 60},
        {field: 'labelname', title: '标签名称', align: 'left', minWidth: 120},
        {field: 'category', title: '标签分类', align: 'center', width: 100, templet: function (d) {
            return categoryMap[d.category] || d.category || '通用';
        }},
        {field: 'algorithm_type', title: '算法类型', align: 'center', width: 100},
        {field: 'is_active', title: '是否活动', align: 'center', width: 80, templet: function (d) {
            return d.is_active == 1 ? '<span style="color: #16b777;">是</span>' : '<span style="color: #999;">否</span>';
        }},
        {field: 'algorithm_content', title: '算法代码', align: 'left', minWidth: 150, hide: true},
        {field: 'creatime', title: '创建时间', align: 'left', width: 160, hide: true},
        {field: 'altime', title: '修改时间', align: 'left', width: 160},
        {field: 'labelremark', title: '标签描述', align: 'left', minWidth: 150, hide: true}
        , {
            fixed: 'right', width: 120, title: '操作', align: 'center', templet: function (d) {
                var arrbtnhtml = [];
                arrbtnhtml.push('<a class="blue-txt layui-btn-xs" lay-event="editaddyey" style="margin-right: 8px;">编辑</a>');
                arrbtnhtml.push('<a class="blue-txt layui-btn-xs" lay-event="btndel" style="color: #e24242;">删除</a>');
                return arrbtnhtml.join('');
            }
        }
    ]];

    tableIns = table.render({
        elem: '#tbmain'
        , url: encodeURI($.getLayUrl() + "?" + $.getSmStr(["labellist.getdata"]))
        , height: 'full-' + parseInt($("#tbmain").offset().top + 5)
        , where: {
            swhere: $.msgwhere(objdata.where)
            , fields: 'id'
            , types: 'desc'
        }
        , cols: arrcols
        , done: function (res, curr, count) {
            console.log('表格渲染完成:', res, '当前页:', curr, '总数:', count);
            console.log('原始响应数据:', res);
            if(!res || !res.data || res.data.length === 0) {
                console.warn('表格数据为空');
                // 如果数据为空，检查是否有其他格式的数据
                if(res && res.re) {
                    console.log('发现re字段数据:', res.re);
                }
            }
        }
        , page: true //是否显示分页
        , limits: [30, 50, 100, 200]
        , limit: 30 //每页默认显示的数量
    });
}

/**
 * 添加编辑
 * @param type
 * @param id
 */
function winopen(type, data) {
    if (type == "add" || type == "edit") {
        var arrbtn = ["保存", "关闭"];
        parent.layer.open({
            type: 2,
            title: type == "add" ? "新增标签" : "编辑标签",
            area: ['800px', '600px'],
            btn: arrbtn,
            content: 'html/healthxj/labellist_edit.html?v=' + (Arg("v") || 1) + '&mid=' + (Arg("mid") || "") + (data && data.id ? '&id=' + data.id : ""),
            success: function (layero, index) {
            }, yes: function (index, layero) {//保存
                layero.find('iframe')[0].contentWindow.saveEvent(function () {
                    parent.layer.close(index);
                    tableIns.reload();
                });
                return false;
            }, btn2: function (index, layero) {
                return true;
            }
        });
    }
}