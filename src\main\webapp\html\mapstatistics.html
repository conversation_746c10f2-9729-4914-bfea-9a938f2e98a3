<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="../css/reset.css">
    <script type="text/javascript" src="../layui-btkj/layui.js"></script>
    <script type="text/javascript" src="../sys/jquery.js"></script>
    <script type="text/javascript" src="../sys/system.js"></script>
    <script src="../plugin/wdatepicker/wdatepicker.js"></script>
    <script src="../plugin/echarts/echarts.common.min.js"></script>
    <script src="../plugin/js/moment.js"></script>
    <script src="../js/mapstatistics.js"></script>
    <style>
        #container {width: 100%;background-color: #eeeeee;}
        .header {padding-top: 20px;margin: 0 auto;text-align: center;}
        .search-btn, .hot-select {height: 24px;margin: 0 20px;}
        .hot-input {text-align: center;}
        #model {display: none;margin-right: 0;}
    </style>
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../css/'+parent.parent.objdata.curtheme+'.css" type="text/css" media="screen"/>');
    </script>
    <title>mapstatistics</title>
</head>
<body>
    <section class="header">
        <select class="hot-select hot-select-md" id="model">
            <option value = "1" selected>角色模式</option>
            <!-- 前期简化版
            <option value = "2">类型模式</option>
             -->
        </select>
        <select class="hot-select hot-select-md" id="around">
            <option value = "7">最近7天</option>
            <option value = "15">最近15天</option>
            <option value = "30" selected>最近30天</option>
            <option value = "90">最近三个月</option>
            <option value = "180">最近半年</option>
            <option value = "365">最近一年</option>
        </select>
        <input type="text" class="hot-input hot-input-md Wdate" id="stime" />
        <span class="to">至</span>
        <input class="Wdate hot-input hot-input-md" type="text" id="etime"/>
        <button id="confirm" class="theme-bgc search-btn search-btn-md">确定</button>
    </section>
    <div id="container"></div>
</body>
</html>