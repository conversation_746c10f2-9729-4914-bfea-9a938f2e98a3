/*logo*/
.layui-logo img{    width:99px;height:27px;margin: 0 auto; margin-top: 19px;}
/*菜单左侧*/
.layui-main-tab ul{background: none;color: #D8D8D8;}
.layui-main-tab ul li{background: none;}
.layui-main-tab .layui-tab-title{top: 13px;}
.layui-main-tab ul.layui-tab-title li{background: none;}
.layui-main-tab ul.layui-tab-title li:hover{color: #28B394;}
.layui-main-tab ul.layui-tab-title .layui-this{background: none;color: #28B394;}
.layui-main-tab ul li .layui-tab-close:hover{background: none;}
.layui-main-tab .layui-tab-title .layui-this:after{border: none;}
.layui-main-tab .layui-tab-title{border: none;}
/*右侧菜单导航*/
.larry-right-menu .lay-pho{display: inline-block;width: 32px;height: 32px;line-height:32px;margin-right: 7px;}
.larry-right-menu .lay-pho img{width: 32px;border-radius: 50%;}
.larry-right-menu ul li i{padding: 0 2px !important;}
.headtimediv{ background:url(../images/time_bg.png) no-repeat; width:230px; height:42px; float:left;margin-left: -22px;margin-top: 13px;}
.titleright_rili{ cursor:default;font-size:22px;  color:#575757; text-align:center; width:110px; line-height:42px; display:inline-block; float:left; padding-left:30px; }
.rili{ color:#333333; font-size:12px; line-height:17px; margin:3px 0px 0 0; text-align:center;display:inline-block; width:75px; float:left}
.rili b{ color:#f86b4f; font-size:20px}
.titleright_rili b{color:#fcb322; font-size:16px}
/*填写上报报表*/
.tit-top-table{margin: 20px 15px 0 15px;}
.tit-top-table span{margin-right:20px;height: 62px;line-height: 62px;}
.rep-table{/*border: 1px solid #DFDFDF;*/margin: 0 15px 15px 15px;}
a.blue-txt{color: #57B3E8;text-decoration: underline;}
.rep-table-con{margin: 0 15px;}
.rep-table-con table{text-align: center;}
.report-no{ width:50px; height:42px; text-align:center; border:3px solid #e3e3e3; border-radius:30px; padding-top:8px; line-height:16px; font-size:12px; float:left; margin:0 10px  20px 0px }
.Orangesize{ color:#ff8518}
.redsize{ color:#d91e06}
.greensize{ color:#1aa094}
.rep-table{padding-bottom: 15px;}
/*切换*/
.tab-tit .layui-tab-title{ border-bottom:none!important;height: 47px!important;}
/*单选框*/
.layui-form-radio i:hover, .layui-form-radioed i{color: #3C99FC!important;}
.layui-btn{background: #4A90E2!important;}
.layui-btn-normal {
    background-color: #4A90E2!important;
}
.layui-form-checked i, .layui-form-checked:hover i {
    /*color: #3C99Ff!important;*/
}
/*.layui-form-checked span, .layui-form-checked:hover span {
    background-color: #3C99Ff!important;
}*/
.layui-form-checked, .layui-form-checked:hover {
    border-color: #3C99Ff!important;
}
/*内容垂直居中*/
.vertical-centre{display: table-cell;vertical-align: middle;}
/*修改layui蓝色背景复选框*/
.define-checkbox .layui-form-checkbox{padding:0;width:16px;height: 16px;line-height: 16px;margin-right: 5px;}
.define-checkbox .layui-form-checkbox i{width:16px;height: 16px;font-size: 14px;line-height: 16px;right:2px;}
.define-checkbox .layui-form-checked,.define-checkbox .layui-form-checked:hover{border-color:#50B2FC;padding: 0;width:16px;height: 16px;line-height: 16px;margin-right: 5px;}
.define-checkbox .layui-form-checked i,.define-checkbox .layui-form-checked:hover i{color: #50B2FC;width:16px;height: 16px;font-size: 14px;line-height: 16px;right:2px;}
.define-checkbox label{margin-right: 30px;}
/*搜索*/
.search-case{background: #EBEBEB;color:#9f9f9f;height: 30px;line-height: 30px;display: inline-block;border-radius: 30px;padding: 0 10px;}
.search-case .icon_search{vertical-align: top;}
.search-case input{height: 30px;line-height: 30px;border: none;margin: 0 5px;width: 230px;vertical-align: top;}
/*上传照片按钮*/
.add-pho-btn{width: 124px;height: 124px;border: 1px solid #999999;background: none;cursor: pointer;}
.add-pho-btn .icon_addbtn:after{font-size: 50px;}
/*分栏*/
.obj-left{width: 300px;background: #fff;float: left;margin: 0 8px 0 8px;height: 100%;}
.obj-right{overflow: hidden;background: #fff;margin: 0 15px 0 10px;height: 100%;}
/*文字溢出显示省略号*/
.text-elli{display:inline-block;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;}
.txt-elli{overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-box-orient: vertical;}
/*表格*/
table.tabstyle {
    border: 1.5pt solid black;
    margin: 0px auto;
    width: 100%;
    font-size: 13px;
    
    }
table.tabstyle th,table.tabstyle td {
    line-height: 22px;
    height: 35px;  
    border: #9F9F9F solid 1px;
    text-align: left;
    padding: 5px 10px;
    }
/* common */
.mtop10{margin-top: 10px;}
.mtop15{margin-top: 15px;}
.center{text-align: center;}
.right{text-align: right;}
.pd20{padding:20px;}
.pd10-20{padding: 10px 20px;}
.mg20{margin-left:15px;}
.mg15{margin-left:15px;}
.mgrg10{margin-right:10px;}
.my_id{text-align:center;}
.bg6{background: #fff;}/*白色*/
.mgbotm{ margin-bottom:20px}
.mtop20{margin-top: 20px;}
.mtop0{ margin:0}
.unline{text-decoration: underline}
.state-red{background: #FF5959;}
.state-grey{background: #dadada; color: #333333}
.state-green{background: #00c87f;}
/*btns*/
a:hover{-webkit-transition:200ms all;transition:200ms all;}
.btn-head{color:#484a4a;}
.btn-login{font-size: 20px;background: #1687d9;height: 44px;line-height: 44px;text-align: center; clear:both; width:329px; margin:0px auto; border-radius:5px}
.btn-login span{width:100%;height:100%;}
.btn-login .anime-text{z-index: 2;}
.btn-login .anime-back{z-index: 1; left:0;top: 0;
	-webkit-transform:scaleX(0) translateZ(0);
	-ms-transform:scaleX(0) translateZ(0);
	transform:scaleX(0) translateZ(0);
	-webkit-transition:800ms all;
	-ms-transition:800ms all;
	transition:800ms all;}
.btn-login:hover .anime-back{background: #54b0f2;
	-webkit-transform:scaleX(1) translateZ(0);
	transform:scaleX(1) translateZ(0);border-radius:5px}
.btn-big{height: 40px;line-height: 40px;color: #fff;display: inline-block;border-radius: 3px;}
.btn-black{display: inline-block; line-height: 28px;padding:0 25px;position: relative;color:#fff;}
.btn-control{border-radius: 8px; width:0.64rem;height: 32px; line-height: 32px;display:inline-block;text-align: center; background: #322e2b;border:2px solid #000;color:#fff;}/*遥控器*/
.btn-border{display: inline-block;padding:0 15px;line-height: 26px;margin-left: 10px;font-size: 14px;}
.btn-border:hover{color: #fff;}
.dateInput{background: url(../images/date.png) no-repeat center right;padding-right:28px;width: 123px;}
.layui-layer-btn a.layui-layer-btn1{background:#ddd;color:#47494a;border-color:#c3c3c3;}

/*btn-head*/
.btn-head{width: 24px;height: 24px; display: inline-block;border-radius: 3px;text-align: center;font-weight: bold;margin-left: 10px;}
.btns-foot{position:fixed;bottom:10px;left:0;width:100%;text-align: center;}

/*txt*/
.gray-txt{color: #454545;}
.pink-txt{color: #F58FED;}
.oran-txt{color: #FE8652;}
.green-txt{color: #1AA094;}
.red-txt{color: #ff5353;}
.dark-grey{ color:#333333;}
.gray-grey{ color:#666666;}
.yellow-txt{color:#FFCD00;}
/*字体大小*/
.font30{ font-size: 30px;}
/*tab*/
.tab li{float: left;color:#333; text-align: center;width: 100px;border:1px solid #000;margin-left: -1px; height: 30px;line-height: 30px; cursor: pointer;font-size: 14px;background: #fff;}
/*input*/
input,select{border:1px solid #47494a; background: none;border-radius: 2px; +vertical-align: middle;padding-left:3px;}
select{height: 26px;}
textarea{border:1px solid #47494a;border-radius: 2px;vertical-align: middle; padding:2px 3px;}
.set-checkbox-style input[type="checkbox"]{-webkit-appearance: none;width: 14px;height: 14px;border: 1px solid #ccc;outline: none;}
.set-checkbox-style input[type="checkbox"]:checked{background: url(../images/checked_bg.png) 50%;background-size:14px 14px;outline: none;border: 0;}
.set-checkbox-style input[type="checkbox"]{vertical-align: middle;margin-right: 7px;}		
.checkbox-style-blue input[type="checkbox"]{-webkit-appearance: none;width: 16px;height: 16px;border: 1px solid #ccc;outline: none;}
.checkbox-style-blue input[type="checkbox"]:checked{background: url(../images/physical/checkbox_bg.png) 50%;background-size:16px 16px;outline: none;border: 0;}
.checkbox-style-blue input[type="checkbox"]{vertical-align: middle;}
.def-input{height: 28px;width: 170px;border-radius: none;border:1px solid #BFBFBF;}
/*form*/
.formList{overflow: hidden;font-size: 14px;word-break:break-all;}
.formList label:first-child{display: inline-block; text-align: right;width: 215px;line-height: 26px;}
.formList div{margin-top: 8px;}
.formList input[type="text"]{width: 150px;}
.formList input[type="radio"]{height: auto; vertical-align: -1px;}
.formList .btn-black{margin-right: 6px;}
.formList label.error{color: #f00;}

/*tree*/
.ztree *{font-family: 'microsoft yahei';}
.treeWrap{width: 250px;height: 100%;overflow: auto;left: 0;top:0;}
.ztree li{line-height: 26px;}
.ztree li a{height: 26px;padding-left: 5px;}
.ztree li a.curSelectedNode{height: 26px; /*line-height: 26px;*/padding:1px 3px 0 5px; font-weight: normal;}
.ztree li span{line-height: 26px;}
.ztree li span.button.ico_open,.ztree li span.button.ico_docu,.ztree li span.button.ico_close,.ztree li span.button.bottom_open,.ztree li span.button.bottom_close,.ztree li span.button.roots_open,.ztree li span.button.roots_close,.ztree li span.button.root_open,.ztree li span.button.center_open,.ztree li span.button.center_close,.ztree li span.button.root_close,.ztree li span.button.add,.ztree li span.button.edit,.ztree li span.button.remove{background: url("../images/newzTree.png") no-repeat; vertical-align: middle; +vertical-align: -5px;}
.ztree li span.button.ico_open{background-position: 0 0; width: 25px;height: 20px;}
.ztree li span.button.ico_close{background-position: -25px 0; width: 25px;height: 22px;}
.ztree li span.button.ico_docu{background-position: -50px 0; width: 20px;height: 20px; +height: 22px;}
.ztree li span.button.bottom_open,.ztree li span.button.center_open,.ztree li span.button.root_open{background-position: -75px 0; width: 18px;height: 18px;}/*实心*/
.ztree li span.button.bottom_close,.ztree li span.button.center_close,.ztree li span.button.root_close{background-position: -100px 0; width: 18px;height: 18px;}/*空*/
.ztree li span.button.roots_open,.ztree li span.button.center_open,.ztree li span.button.root_open{background-position: -75px 0; width: 18px;height: 18px;}/*实心*/
.ztree li span.button.roots_close,.ztree li span.button.center_close,.ztree li span.button.root_close{background-position: -100px 0; width: 18px;height: 18px;}/*空*/
.ztree li span.button.add{background-position:-25px -25px;}
.ztree li span.button.edit{background-position:-50px -25px;}
.ztree li span.button.remove{background-position:0 -25px;}
.ztree li span.button.add,.ztree li span.button.edit,.ztree li span.button.remove{width: 20px;height: 20px;margin-left: 5px;}
.ztree li span.button.center_docu,.ztree li span.button.bottom_docu{visibility: hidden;}
.ztree li ul.line{background:none;}
.ztree li a input.rename{line-height: 16px; vertical-align: text-top;}
/*.ztree li span.button.chk.radio_false_full,.ztree li span.button.chk.radio_true_full,.ztree li span.button.chk.radio_false_full_focus,.ztree li span.button.chk.radio_true_full_focus,.ztree li span.button.chk.radio_false_part,.ztree li span.button.chk.radio_false_part_focus{+vertical-align: -3px;}*/
.ztree li span.button{+vertical-align: -3px;}

/*page*/
.dataTables_wrapper .dataTables_paginate .paginate_button{padding:0;margin-left: 5px;}
.dataTables_wrapper .dataTables_paginate{padding-top: 0.775em;}

/*icon*/
u{display:inline-block;background:url(../images/icon.png) no-repeat; vertical-align: middle; text-decoration: none;}

.key u{display:inline-block;background:url(../images/icon-gray.png) no-repeat; vertical-align: middle; text-decoration: none;}
/*.icon1{background-position:0 0;width: 32px;height: 32px;margin-right: 10px; cursor: pointer;}*/
/*.icon2{background-position:-50px 0;}*/
.icon3{background-position:-50px -50px;width: 20px;height: 20px;}
.icon4{background-position:-100px -50px;width: 20px;height: 20px;}
.icon5{background-position:-150px -50px;width: 25px;height: 25px;cursor: pointer;}
.icon6{background-position:-200px -50px;width: 14px;height: 14px;margin-right: 3px;}
.icon7,.icon8,.icon9,.icon10,.icon11{width: 34px;height: 34px;position: absolute;}
.icon7{background-position:-150px 0;}
.icon8{background-position:-200px 0;}
.icon9{background-position:-250px 0;}
.icon10{background-position:-300px 0;}
.icon11{background-position:-350px -150px;cursor: pointer;top:5px;right:5px;width: 12px;height: 12px;}/*delete*/
.icon12{background-position:-350px -50px;width: 18px;height: 24px;margin-right: 5px;}/*map*/
.icon13{background-position:-300px -50px;width: 20px;height: 20px;margin-right: 5px;}/*clock*/
.icon14{background-position:-400px -50px;width: 20px;height: 20px;margin-right: 5px;}/*color clock*/
.icon15{background-position:-100px -75px;width: 25px;height: 25px;margin-left: 10px;}/*read*/
.icon16,.icon17{width: 22px;height: 22px;margin-left:10px;cursor: pointer;}
.icon16{background-position:-100px -125px;}/*delete*/
.icon17{background-position:-400px -75px;}/*cancel*/
.icon18{background-position:-350px 0;width: 21px;height: 21px;}/*ok*/
.icon19{background-position:-400px 0;width: 33px;height: 33px;}
.icon20,.icon21{width: 20px;height: 20px; right: 28px;top: -1px; }
.icon20{background-position:-50px -50px;}/*删除*/
.icon21{background-position:-300px -75px;}/*增加*/
.icon22{background-position:-250px -50px; width: 22px;height: 22px;top:5px;right:5px;}/*search*/
.icon23{background-position:-100px -150px; width: 25px;height: 25px;top:9px;right:30px;}/*add gray*/
.icon24{background-position:-50px -50px; width: 22px;height: 22px;top:11px;right:0;}
.icon25{background-position:-300px -125px;}/*禁用*/
.icon26{background-position:-250px -125px;}/*恢复禁用*/
.icon27{background-position:0 -150px;}/*上移*/
.icon28{background-position:-50px -150px;}/*下移*/
.icon29{background-position:-250px -150px;}/*不能上移*/
.icon30{background-position:-300px -150px;}/*不能下移*/
.icon31{background-position:-400px -125px; height: 25px;
    width: 25px;-webkit-transition:all 1s;transition:all 1s; cursor: pointer;}/*折叠1*/
.current .icon31{background-position:-350px -125px; }/*折叠2*/
.icon32{background-position:-350px -75px;width: 25px;height: 35px;margin-left: 10px;}/*map*/
.icon33{background-position:0 -175px;width: 25px;height: 25px;}
.icon34{background-position:-50px -175px;width: 25px;height: 25px;}
.icon35{background-position:-100px -175px;width: 25px;height: 25px;top:8px;right:2px; cursor: pointer;}/*white close*/
.icon36{background-position:-400px -150px;width:34px;height:17px;}/*msg*/
.icon37{background-position:-350px -175px;}/*control up*/
.icon38{background-position:-300px -175px;}/*control down*/
.icon39{background-position:-150px -175px;}/*control home*/
.icon40{background-position:-200px -175px;}/*control return*/
.icon37,.icon38{+margin-top:8px;}
.icon39,.icon40{+margin-top:4px;}
.icon41{background-position:-250px -175px;right: 6px; top: 8px;}/*control delete*/
.icon42{background-position:-400px -175px;}/*control tel*/
.icon43{background-position:0 -200px;}/*control notel*/
.icon42,.icon43{+margin-top: 10px;}
.icon37,.icon38,.icon39,.icon40,.icon41,.icon42,.icon43{width: 25px;height: 25px;}
.icon44{background-position:-375px -250px;}/*sheng*/
.icon44-off{background-position:-125px -250px;}/*jiang*/
.icon45{background-position:-250px -250px;}/*on*/
.icon45-off{background-position:0 -250px;}/*off*/
/*hykz*/
.icon46{background-position:-150px -475px;}
.icon47{background-position:-200px -475px;}
.icon48{background-position:-250px -475px;}
.icon49{background-position:-300px -475px;}
.icon50{background-position:-50px -200px;}
.icon50-off{background-position:-450px 0;}
.icon51{background-position:-100px -200px;}
.icon51-off{background-position:-450px -50px;}
.icon52{background-position:-150px -200px;}
.icon52-off{background-position:-450px -100px;}
.icon53{background-position:-200px -200px;}
.icon53-off{background-position:-450px -150px;}
.icon54{background-position:-250px -200px;}
.icon54-off{background-position:-300px -200px;}
.icon55{background-position:0 -375px;}
.icon55-m {background-position:-50px -375px;}
.icon55-off{background-position:-100px -375px;}
.icon56{background-position:-150px -375px;}
.icon56-m {background-position:-200px -375px;}
.icon56-off{background-position:-250px -375px;}
.icon57{background-position:-300px -375px;}
.icon57-m {background-position:-350px -375px;}
.icon57-off{background-position:-400px -375px;}
.icon58{background-position:-450px -375px;}
.icon58-m {background-position:0 -425px;}
.icon58-off{background-position:-50px -425px;}
.icon59 {background-position:-100px -425px;}
.icon59-m {background-position:-150px -425px;}
.icon59-off{background-position:-198px -425px;}
.icon60{background-position:-250px -425px;}
.icon61{background-position:-300px -425px;}/*字幕*/
.icon62{background-position:-350px -425px;}/*分屏*/
.icon63{background-position:-400px -425px;}/*退出 */
.icon64{background-position:-450px -425px;}
.icon64-off{background-position:-50px -475px;}
.icon65{background-position:0 -475px;}
.icon65-off{background-position:-100px -475px;}
.icon66{background-position:-350px -200px;}
.icon67{background-position:-400px -200px;}
.icon68{background-position:-475px -500px;width: 25px;height: 25px;margin-left: 10px;}/*未读*/
.icon69{background-position:-482px 0;width: 20px;height: 20px;left: 0;top:0;}
.icon70{background-position:-150px -100px;}
.icon70-off{background-position:-100px -100px;}
.icon71{background-position:-200px -525px;}
.icon71-sel{background-position:-250px -525px;}
.icon72{background-position:-350px -475px;}
.icon73{background-position:0 -575px;}
.icon74{background-position:-75px -575px;}
.icon75{background-position:-150px -575px;}
.icon76{background-position:-225px -575px;}
.icon77{background-position:-300px -575px;}
.icon78{background-position:-375px -575px;}
.icon79{background-position:0 -650px;}
.icon80{background-position:-75px -650px;}
.icon81{background-position:-150px -650px;left: 15px;}
.icon82{background-position:-200px -650px;right: 15px;}
.icon83{background-position:-375px -525px;width: 44px;height: 34px;right: 0;bottom:-40px; cursor: pointer;}


.iconM22{background-position:0 -723px;}
.iconM222{background-position:-70px -723px;}
.iconZ222{background-position:-197px -723px;}
.iconM3{background-position:-80px -799px;}
.iconZ3{background-position:-159px -799px;}
.iconM4{background-position:-80px -884px;}
.iconZ4{background-position:-160px -884px;}
.iconX4{background-position:-239px -884px}
.iconM5{background-position:-78px -960px;}
.iconZ5{background-position:-156px -960px}
.iconX5{background-position:-239px -960px}
.iconM6{background-position:-4px -960px}
.iconM8{background-position:-319px -959px}

.iconM9{background-position:-76px -1032px;}
.iconZ9{background-position:-156px -1032px}
.iconX9{background-position:-239px -1032px}
.iconS9{background-position:-319px -960px}
.iconM10{background-position:-239px -1108px;}
.iconZ10{background-position:-317px -1108px}
.iconM13{background-position:-156px -1108px;}
.iconM16{background-position:-76px -1108px}
.iconM20{background-position:-3px -1108px;}



.nMainico1{background-position:-417px -961px;}
.nMainico2{background-position:-417px -1035px}
.nMainico3{background-position:-417px -1108px;}

.skinIcon1,.skinIcon2{width: 25px;height: 25px;margin-left: 20px;cursor: pointer;}
.skinIcon1{background-position:-100px -100px;}
.skinIcon2{background-position:-200px -100px;}
.skinIcon1:hover{background-position:-150px -100px;}
.skinIcon2:hover{background-position:-300px -50px;}
.skinIcon3{background-position:-250px -100px;width: 22px;height: 22px;}
.skinIcon3:hover{background-position:-300px -100px;}
.skinIcon4{background-position:-450px 0;width: 24px;height: 20px;}
.skinIcon4:hover{background-position:-450px -50px;}
.skinIcon5{background-position:-250px 0;width:25px;height:25px;}/*保存*/
.skinIcon5:hover,.skinIcon6:hover{background-position:-250px -50px;}
.skinIcon6{background-position:-400px -100px;width: 25px;height: 25px; cursor: pointer; margin-left: 20px;}/*black保存*/
.skinIcon7{background-position:-250px -150px;}
.skinIcon7-sel{background-position:-200px -150px;}
.skinIcon8{background-position:-325px -150px;}
.skinIcon8-sel{background-position:-375px -150px;}
.skinIcon7,.skinIcon7-sel,.skinIcon8,.skinIcon8-sel{width: 33px;height: 29px; cursor: pointer;position: absolute;top:7px;right:5px;}
.skinIcon9,.skinIcon10{width: 32px;height: 32px;right:-45px;}
.skinIcon9{background-position:0 -350px;top:0;}
.skinIcon9:hover{background-position:-50px -350px;}
.skinIcon10{background-position:-100px -350px;bottom: 0;}
.skinIcon10:hover{background-position:-150px -350px;}
.skinIcon-up,.skinIcon-down{width: 110px;height: 112px;display: block; margin: 45px auto 0;}
.skinIcon-up{background-position:0 -400px;}
.skinIcon-down{background-position:-125px -400px;}
.skinIcon-camera,.skinIcon-camera-on{width: 81px;height: 37px;cursor: pointer;}
.skinIcon-camera{background-position:-400px -325px;}
.skinIcon-camera-on{background-position:-400px -375px;}
.skinIcon-middle-on,.skinIcon-middle{width: 225px;height: 171px;}
.skinIcon-middle{background-position:0 -550px;}
.skinIcon-middle-on{background-position:-250px -550px;}
.skinIcon-shadow-on,.skinIcon-shadow{width: 125px;height: 92px;display: block; margin: 50px auto 0;}
.skinIcon-shadow{background-position:-250px -350px;}
.skinIcon-shadow-on{background-position:-250px -450px;}
.rankList .icon51,.rankList .icon52,.rankList .icon51-off,.rankList .icon52-off{width: 27px;height: 25px;right: -43px;top:44px;}
.key .skinIcon-top,.skinIcon-bottom{width: 84px;height: 30px;left:30px;}
.key .skinIcon-left,.skinIcon-right{width: 30px;height: 84px;top:30px;}
.key .skinIcon-top{background-position:0 -250px;top:0;}
.key .skinIcon-top:hover{background-position:0 -300px;}
.key .skinIcon-bottom{background-position:-100px -250px;bottom: 0;}
.key .skinIcon-bottom:hover{background-position:-100px -300px;}
.key .skinIcon-left{background-position:-200px -250px;left: 0;}
.key .skinIcon-left:hover{background-position:-250px -250px;}
.key .skinIcon-right{background-position:-300px -250px;right: 0;}
.key .skinIcon-right:hover{background-position:-350px -250px;}
.key .skinIcon-center{background-position:-400px -250px;width: 60px;height: 60px;top:42px;left: 42px;}
.key.skinIcon-center:hover{background-position:-425px -125px;}
.arrowPre{background-position:0 -75px;}
.arrowNext{background-position:-50px -75px;}

/*contentRight*/
.contentRight{padding:4px 4px 0 4px; width: auto;margin-left: 280px;}
iframe{width:100%;border:0;background-color:#fff;}
.popRight{margin-left: 250px;}

/*timezone*/
.timezone{top:0;left:0;z-index:9999;border:1px solid #ccc;box-shadow: 0 0 8px #aaa;}
.timezone li{position: relative; line-height: 30px;padding:0 3px; cursor: pointer;}
.timezone li:hover{background: #ccc;}
.timezone li .timestr{margin-right: 5px;}

/*login*/
/*.loginContent{height: 390px; width: 100%;background:url(../images/alpha3.png);top:50%;margin-top: -195px;}
.loginTitle{color: #fff;font-size: 36px; line-height:3;text-align: center;}
.loginArea{margin:0 auto; width:380px; background:url(../images/denglubg.png) no-repeat; height:355px;}*/
/*.loginArea input{display: block;background: #eee;border:0;width:475px; height: 60px;line-height: normal;line-height: 60px\9;margin-bottom: 20px; font-size: 16px; padding-left:25px;}
*/.changeBg{right: 50px;bottom: 50px;}
.changeBg u{width: 40px;height: 62px;cursor: pointer;margin:0 50px;}

/*字母列表*/
.innerTitle{font-size: 16px;line-height: 44px;border-top: 1px solid #fff;padding:0 20px;}
.letterList{margin-top: 10px;}
.letterList li{float: left;cursor: pointer;padding:2px 7px;line-height: 26px;}

/*数据字典*/
.otx-search div{margin-right: 20px; display: inline-block; +display: inline;zoom:1;font-size: 14px;}
.otx-search input,.otx-search select{border: 1px solid #d8d8d8;
box-sizing: border-box;
border-radius: 2px;
background: #ffffff;line-height: 26px;
height: 28px;}
.dic-head{padding:20px 20px 10px;}
.dic-list dl{padding: 10px;}
.dic-list dl:nth-of-type(2n){background: #f7f8fa;}
.dic-list dt{float: left;margin-right: 25px;overflow: hidden;}
.dic-list dd{overflow: hidden;}
.dic-list u{cursor: pointer;}
.dic-list .icon4{margin:0 15px;}
.dic-list input{width: 100px;margin-right: 5px;}
.dic-list dd input:first-child{width: 50px;}
.dic-list dd label{margin-right: 15px;margin-bottom:5px;display: inline-block;}


/*工作台日历弹框*/
.popCalendar{border:1px solid #ccc;border-top:4px solid #48494b; line-height: 26px;}
.popCalendar label{width: 55px;}
.calendarBtn{padding:10px;}
/*日历*/
.fc-toolbar{/*margin-bottom:5px;*/}
.fc-toolbar .fc-center{margin-top: 6px; +margin-top: 3px;}
.fc .fc-toolbar > .titlehtml{display: inline;float: none;}
.fc-center span{float: none;}
.fc-state-default{background: #fff;border-color:#ccc;text-shadow:none;}
.fc-state-down, .fc-state-active{box-shadow: none;}
.fc-next-button{background:#fff url("../images/icon.png") no-repeat scroll -150px -525px;}
.fc-prev-button{background:#fff url("../images/icon.png") no-repeat scroll -100px -525px;}
.fc-prev-button span,.fc-next-button span{visibility: hidden;}
#restwindow{background: #FFFFFF;}
#restwindow p:hover{background: #F5F7FA;}
.h_calendar_alm{border-color:#ccc;}
.alm_content{background: #fff;border-top:1px solid #ccc;padding:0 0 5px;}
.alm_content .alm_lunar_date{background: none; width: 100%;}
.fc-view-container{background: #fff;}
.fc-day-header{line-height: 40px;color: #8a8b8c;}
.today_date{font-size: 50px;line-height: 60px;}
.alm_date{overflow: hidden;}
.calTable th, .calTable td{position: static; line-height: 29px; background-clip: padding-box; cursor: pointer;}
.fc-more-popover .fc-event-container{font-size: 14px;padding: 2px;}
.fc-event{line-height: 20px;}
.opt th, .opt td{font-size: 14px;vertical-align: middle;}
.titlehtml .titleword{font-size: 16px;font-weight: normal;}
.haveMeeting{width: 12px;height: 12px;background-color: red;position:absolute;top:0;right:0;border-radius: 50%;line-height:12px; color:#fff; font-size: 10px; font-family: 'microsoft yahei'; -webkit-text-size-adjust:none; font-weight: normal;}
.monthHead{font-size: 20px;text-align: center;color:#fff;line-height: 40px;position: relative;}
.monthNone{background: #47494a;}
.workTotal{font-size: 12px;position: absolute;top: 0;right: 5px;color:#fff;}
.calendar {width: 84%;float: left;}
.calendarWrapper {margin: 5px;}
.sidePanel {width: 15%;}/*日期宽度*/
.onemouth, .fc .onemouth{+display: inline;zoom:1;margin:0 5px 7px 0;}
.onemouth tbody:first-child{display: table;width: 100%;}
.calColor{border: 1px solid #ccc; margin-top: -1px;padding:5px;}
.calColor div{+margin-bottom: 10px;}
.triangle{width:0;height:0;border-width:0 6px 11px;border-top-color:transparent;border-right-color:transparent;border-left-color:transparent;border-style:solid;overflow:hidden;display: inline-block;margin-right: 5px;}
.meaboutme{width: 12px;height: 12px;display: inline-block;margin-right: 6px;}
.calColor u,u[class^=star]{width: 14px;height: 14px;margin-right: 4px;}
.calColor img{vertical-align: top;}
.star1{background-position:-350px -475px;}
.star2{background-position:-375px -475px;}
.star3{background-position:-400px -475px;}
.star4{background-position:-425px -475px;}
.star5{background-position:-450px -475px;}
.star6{background-position:-475px -475px;}
.star7{background-position:-350px -500px;}
.star8{background-position:-375px -500px;}
.fc u{width: 30px;height: 30px; margin-top: -1px;display: none;}
.selDown{padding:10px;margin:1px 0 2px; border:1px solid #ccc;}
.selDown select{width:100%;}

/*个人信息*/
.personalImg{width: 200px; margin-left: 100px;}
.otx-head{width: 96px;height: 96px;border-radius: 3px;border:1px solid #ccc;display: block; margin-bottom: 15px;}
.cmxform{margin:0;}

/*用户管理*/
.personList li {border: 1px solid #ccc;float: left;margin:10px 1%;position: relative;width: 31%; line-height: 24px;min-width: 350px;}
.personImg {
    border-radius:50%;
    height: 96px;
    margin: 10px;
    width: 96px;}
.personInfo{margin-left: 115px;}
.personInfo span:first-child {
    color: #999;
    display: inline-block;
    text-align: right;
    width: 70px;}
.personList .btn-edit{color:#fff; font-size: 12px; border-radius: 5px; top: 5px; right: 5px;padding: 0 5px;}
.divrole {border: 1px solid; left: 160px; top: 17px;z-index: 9;}
.ulselect li{padding: 0 5px;cursor: pointer;}


/*栏目管理*/
.columnList{width: 30%;padding:10px;}
.meauRight{width: 60%;}
.meauRight div{padding-top: 10px;margin: 0}
.formList .funLabel label{width: auto;margin:0 10px 10px 0;display: inline-block;}
.formList .funLabel input[type="text"]{width: 70px;}
.formList .funLabel{width: 60%;padding: 0;}
.meauSearch{margin:15px 0;}
.meauSearch .skinIcon3{position: absolute;top: 3px;right: 0;}
.meauList .inner img,.selImg img{border-radius: 50%;vertical-align: middle; height: 40px;margin: 0 auto;width: 40px;background: url(../images/gray.png) no-repeat center center;}
.meauList li{margin-bottom: 2px;overflow: hidden;}
.meauList u{cursor: pointer;width: 25px;height: 25px;margin:7px 0 0 5px;}
.meauList span{display: block; line-height: 36px; color:#616161;cursor: pointer;background: url(../images/list.png) no-repeat left 8px; text-indent: 23px;}
.meauList span .icon24{margin-top: -10px;}
.meauList .inner{margin: 2px 0;}
.meauList .inner a{float: left; width:32%;position: relative;text-align: center;font-size: 12px;color:#fff;padding:10px 0 5px;}
.meauList .inner p{height: 28px; line-height: 26px; overflow: hidden;}
.meauList .icon25,.meauList .icon26{position: absolute;}
.meauList .icon29,.meauList .icon30{cursor: not-allowed;}
.meauList .inner .icon24{margin: 0; top:0; right: 16px;position: absolute;}
.meauList .inner .icon25,.meauList .inner .icon26{bottom: 20px; left: 50%; margin-left: -9px;}
.inner a.selected{border-color:#fdaa2e;}
.selImg img{cursor:pointer;margin:4px;}


/*统计分析*/
.analysis{width: 49%;margin-right: 1%;margin-bottom: 15px;}
.analysis .charts{height: 500px;}
.analysis .noticeTitle{font-size:16px;color:#fff;text-align: center;}
.analysisHead{padding:10px; background: #f8f8f8;border-bottom: 1px solid #ccc;font-size: 14px;}
.analysis .tab{margin: 8px;}

/*table demo*/
.otx-table{text-align: center; line-height: 2;}
.otx-table tr:nth-of-type(2n){background: #f7f8fa;}
.otx-table tr:hover{background:#feecd4;}
.otx-table td,.otx-table th{border:1px solid #ccc;}

/*角色管理*/
.roleTitle th{border:1px solid #ccc;color:#fff;font-size: 16px; line-height: 54px;}
.innerTable th,.innerTable td{border:1px solid #ccc; line-height: 40px;}
.innerTable th{text-align: left; background-color: #ddd;}
.innerTable td:last-child label{margin-right: 15px;display: inline-block;}
.roleTable .icon31{margin: 0 10px;}

/*上传图片btn*/
.webuploader-pick{vertical-align: middle;display:inline-block;}
.webuploader-container{
	margin-top:15px;
	height:40px;
	display:inline-block;
	+vertical-align: middle;
}


/*加入设置*/
.hnave{ background:#f8f8f8; height:42px;  color:#fff; font-size:14px; line-height:42px; border-bottom:1px solid #1da89e ;}
.hnave font{ background:#1da89e; line-height:30px; height:30px; display:inline-block;  padding:0 10px; margin-top:12px }
.jr_div{ width:498px; margin:10px auto; overflow:hidden}
.jt_div{ width:309px; margin:10px auto; overflow:hidden}
.jr_div p,.sj_div p,.sr_div p,.jt_div p{ margin-top:15px; clear:both; font-size:14px}
.jr_div span{ text-align:right; width:150px; display:inline-block; font-size:14px;}
.jr_div  input{
    border: 1px solid #BFBFBF;
    height:30px;
    line-height: 30px;
    padding-left: 3px;
	width:342px;
	border-radius:0;
	font-size:14px
}

.sj_div input,.sr_div input{
    border: 1px solid #BFBFBF;
    height: 25px;
    line-height: 25px;
    padding-left: 3px;
	width:342px;
	border-radius:0;
	font-size:14px
}
.btn-gree{
	background:#1da89e ;
    height: 32px;
    line-height: 32px;
    padding-left: 3px;
	width:342px;
	border-radius:3px; float:right; text-align:center; color:#FFF }


.jr_div input[type="radio"]{ width:13px;}
.jr_div p.csdiv{ color:#323232; font-size:12px; width:342px; float:right; margin:5px 0 20px 0}
.sr_div{ width:590px; margin:10px auto 0 auto; overflow:hidden; }
.jt_div p.csdiv1{ color:#f4a26c; font-size:12px; float:right; margin:5px 0 20px 0; font-weight:bold}
.jt_div input{ border:1px solid #bfbfbf; border-radius:0;}
.tjbtn-gree{
	background:#1da89e ;
    height: 30px;
    line-height: 30px;
    margin-right:10px;
	width:80px;
	border-radius:3px;  text-align:center; color:#FFF; font-size:14px; display:inline-block }

.tjbtn-gree1{
	border:1px solid #1da89e ;
	border-radius:3px;
    height: 28px;
    line-height: 28px;
	width:80px;
    text-align:center; color:#1da89e;font-size:14px; display:inline-block }

.quexiaobtn{
	background:#d2d2d2 ;
    height: 30px;
    line-height: 30px;
    margin-right:10px;
	width:80px;
	border-radius:3px;  text-align:center; color:#0d0d0d; font-size:14px; display:inline-block }
.edpossword{
	background:#f19149 ;
    height: 30px;
    line-height: 30px;
    margin-right:28px;
	width:80px;
	border-radius:3px;  text-align:center; color:#fff; font-size:14px; display:inline-block; margin-left:20px }
.deletebtn{
	background:#da4a4c ;
    height: 30px;
    line-height: 30px;
    margin-right:28px;
	width:80px;
	border-radius:3px;  text-align:center; color:#fff; font-size:14px; display:inline-block }


/*邀请加入通知*/
.yqtz_rldiv{ width:90%; margin:10px  auto 0 auto; overflow:hidden }
.yqtz_rl1{background: #fff; color:#323232; height: 33px; line-height:33px; cursor: pointer; text-align:center;border:1px solid #1da89e ; overflow: visible; width:180px;display:block;font-size:14px; float:left; position:relative}
.cjhdsyys_zbg{ background:#1da89e ; line-height:20px; height:28px; color:#FFF;}
.yqtz_rl2{background:#1da89e ; color:#fff; height: 33px; line-height:33px; cursor: pointer; text-align:center;border:1px solid #1da89e ; overflow: visible; width:180px; display:block;font-size:16px;float:left; border-right-style:none; position:relative }
.ssjh_rl1.hover{background:#1da89e ;color:#fff; height: 33px; line-height:33px; cursor: pointer; text-align:center;border:1px solid #1da89e ; overflow: visible; width:180px; display:block;font-size:16px;float:left }
.yqtz_botdiv{ border:1px solid #BFBFBF; overflow:hidden; margin:0 auto 10px auto; width:90%; padding-bottom:20px;  }
.yqtzmaindiv.znone{ border-bottom:none}
.yqtzmaindiv img{float: left;
    height:75px;
    margin:10px 0 5px 0;
    vertical-align: middle;
    width: 75px;
}
.yqtzmaindiv h3{ display:block; float:left; font-size:18px;  width:70%; margin-top:15px; height:30px;}
.yqtzmaindiv p{display:block; float:left; font-size:14px; width:80%}
.yqtzmaindiv p span{ width:30%; display:inline-block}

.yqtzmaindiv1{ margin:0 auto; border-bottom:1px solid  #d6d6d6; overflow:hidden; width:100%; color:#323232;line-height:30px; padding:20px 0 20px  40px; }
.yqtzmaindiv1 p{display:block; float:left; font-size:14px; width:100%}
.yqtzmaindiv1 p span{ width:33%; display:inline-block; float:left}



.yqtz_zleftDiv{ width:100%; margin:10px  auto 0 auto; overflow:hidden  }
.yqtz_zleftDiv span.jgd_rl{background:#fff; height:34px; line-height:34px; cursor: pointer; text-align:center;border:1px solid #1da89e; overflow: visible;  display:inline-block;font-size:16px;float:left; width:107px;
 }
 .jt_zleftDiv span.jgd_rl{background:#fff; height:34px; line-height:34px; cursor: pointer; text-align:center;border:1px solid #1da89e; overflow: visible;  display:inline-block;font-size:16px;float:left; width:172px;
 }

 .jt_zleftDiv{ width:350px; margin:10px 0; overflow:hidden; float:right}

.yqtz_zleftDiv span:first-child,.jt_zleftDiv span:first-child{
	border-left-color: #1da89e;
	border-radius:5px 0 0 5px;
}

.yqtz_zleftDiv span:last-child,.jt_zleftDiv span:last-child {
	border-radius: 0 5px 5px 0;
}

.yqtz_zleftDiv span.jgd_rl.currebg,.jt_zleftDiv span.jgd_rl.currebg,.yqtz_zleftDiv span.jgd_rl1.currebg,.yqtz_zleftDiv span.jgd_rl2.currebg{background:#1da89e ; color:#fff;border:1px solid #1da89e ;}


/*******STYLE 1*******/
.chk_1 + label {

	border: 1px solid #999;
	box-shadow: 0 2px 2px rgba(0, 0, 0, 0.05), inset 0px -15px 10px -12px rgba(0, 0, 0, 0.05);
	padding: 9px;
	border-radius:5px;
	display: inline-block;
	position: relative;
	margin: 0 5px 0 10px; top:6px;
	background:#eeeeee
}
.chk_1 + label:active {
	box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px 2px 3px rgba(0,0,0,0.1);
}

.chk_1:checked + label {

	border: 1px solid #d2d2d2;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05), inset 0px -15px 10px -12px rgba(0, 0, 0, 0.05), inset 15px 10px -12px rgba(255, 255, 255, 0.1);
	color: #243441;
}

.chk_1:checked + label:after {
	content: '\2714';
	position: absolute;
	top: -10px;
	left: 0px;
	color: #7E7C7D;
	width: 100%;
	text-align: center;
	font-size: 1.2em;
	padding: 1px 0 0 0;
	vertical-align: text-top;
}


.yspf_bgwt  input.yspf_xhx{line-height:23px;  width:40px; text-align:center; height:23px; border:0;border-bottom:1px solid #c3c3c3; border-radius:0}
.tjzagl_input{line-height:23px;  width:95%; text-align:center; height:23px;  border:0; border-bottom:1px solid #c3c3c3;}
.FancyInput__bar___1P3wW:after,.FancyInput__hasError___332t5.FancyInput__bar___1P3wW:before{background:#eb5815}
.FancyInput__bar___1P3wW{position:relative;display:block}
.FancyInput__bar___1P3wW:after,.FancyInput__bar___1P3wW:before{content:'';height:2px;width:0;bottom:0;position:absolute;background:#22b8eb;transition:all .2s ease}
.FancyInput__bar___1P3wW:before{left:50%}.FancyInput__bar___1P3wW:after{right:50%}
.ssjh_xhx:focus~.FancyInput__bar___1P3wW:after,.ssjh_xhx:focus~.FancyInput__bar___1P3wW:before,.tjzagl_input:focus~.FancyInput__bar___1P3wW:after,.tjzagl_input:focus~.FancyInput__bar___1P3wW:before{width:50%}
.tjjc:focus~.FancyInput__bar___1P3wW:after,.tjjc:focus~.FancyInput__bar___1P3wW:before{width:50%}


/*申请加入*/
.sj_zdiv{ width:95%; margin:20px auto; padding:10px 0 30px 0 ; overflow:hidden; border:1px solid #1da89e }
.sj_div{ width:559px; margin:10px auto; overflow:hidden}
.sj_div span{ text-align:right; width:140px; display:inline-block; font-size:16px;}
.sj_div u,.yqtzmaindiv u,.yqtz_zleftDiv u,.yqtzmaindiv1 u,.yqtz_tyq u,.jgd_rl3 u,.fxsp_bgwt u,.fxsp_zhlb u{ background:url(../images/zicon.png) no-repeat; }
.sj_div u.ico01{ background-position:-4px -5px; display:inline-block; width:20px; height:20px}
.sj_div u.ico02{ background-position:-38px -5px; display:inline-block; width:20px; height:20px}
.sj_div u.ico03{ background-position:-66px -5px; display:inline-block; width:20px; height:20px}
.yqtzmaindiv u.ico04{ background-position:-2px -31px; display:inline-block; width:50px; height:39px; cursor:pointer}
.yqtzmaindiv u.ico05{ background-position:-55px -31px; display:inline-block; width:50px; height:39px; cursor:pointer}
.yqtzmaindiv u.ico06{ background-position:-110px -31px; display:inline-block; width:47px; height:39px; cursor:pointer}
.yqtzmaindiv u.ico061{ background-position:-157px -31px; display:inline-block; width:47px; height:39px; cursor:pointer}
.yqtz_zleftDiv u.ico07{ background-position:-4px -94px; display:inline-block; width:25px; height:39px; cursor:pointer}
.yqtzmaindiv1 u.ico08{ background-position:-34px -94px; display:inline-block; width:25px; height:39px; cursor:pointer}
.yqtzmaindiv1 u.ico09{ background-position:-164px -100px; display:inline-block; width:25px; height:39px; cursor:pointer}
.yqtzmaindiv1 u.ico10{ background-position:-58px -95px; display:inline-block; width:25px; height:39px; cursor:pointer}
.yqtz_zleftDiv u.ico11,.jgd_rl3 u.ico11{ background-position:-118px 3px; display:inline-block; width:25px; height:31px; cursor:pointer}
.yqtz_tyq u.ico12{ background-position:-127px -176px; display:inline-block; width:25px; height:31px; cursor:pointer}
.yqtz_tyq u.ico13{ background-position:-147px -176px; display:inline-block; width:25px; height:31px; cursor:pointer}
.yqtz_tyq u.ico121{ background-position:-192px -176px; display:inline-block; width:25px; height:31px; cursor:pointer}
.yqtz_tyq u.ico14{ background-position:-166px -176px; display:inline-block; width:25px; height:31px; cursor:pointer}
.yqtz_tyq u.upico15{ background-position:-37px -127px; display:inline-block; width:35px; height:31px; cursor:pointer}
.yqtz_tyq u.downico15{ background-position:-2px -131px; display:inline-block; width:35px; height:21px; cursor:pointer}
.yqtz_tyq u.upico16{ background-position:-104px -182px; display:inline-block; width:30px; height:31px; cursor:pointer}
.yqtzmaindiv1 u.upico17{ background-position:-72px -135px; display:inline-block; width:65px; height:31px; cursor:pointer}
.jgd_rl3 u.ico18{ background-position:-140px -136px; display:inline-block; width:25px; height:31px; cursor:pointer}
.fxsp_bgwt u.upico19{ background-position:0px -70px; display:inline-block; width:26px; height:31px; cursor:pointer}
.fxsp_bgwt u.upico20{ background-position:-83px -72px; display:inline-block; width:26px; height:31px; cursor:pointer}
.fxsp_bgwt u.upico21{ background-position:-112px -72px; display:inline-block; width:26px; height:31px; cursor:pointer}
.fxsp_zhlb u.upico22{ background-position:-173px -72px; display:inline-block; width:26px; height:31px; cursor:pointer; float:right}


.edit{ color:#6b6b6b}
.sj_div label{ display:inline-block; width:56px; height:30px; line-height:30px;  font-size:14px; color:#FFF; border-radius:3px; margin-left:6px; padding-left:8px; cursor:pointer}
.sj_div label.yellow{background:#ABD46C;}
.sj_div label.org{background:#F2914A;}
.yqtzmaindiv label{ display:inline-block; line-height:20px; margin-bottom:10px;}

.yaoqing{ color:#dddddd; padding:10px 0px 10px 30px; overflow:hidden; }
.yaoqing_right{ padding-left:126px; }




/*邀请加入通知*/
.yqtz_rldiv{ width:97%; margin:10px  auto 0 auto; overflow:hidden }
.yqtz_rl1{background: #fff; color:#1ca89f; height: 33px; line-height:33px; cursor: pointer; text-align:center;border:1px solid #1da89e ; overflow: visible; width:180px;display:block;font-size:14px; float:left }
.cjhdsyys_zbg{ background:#1da89e ; line-height:20px; height:28px; color:#FFF;}

.yqtz_rl2{background:#1da89e ; color:#fff; height: 33px; line-height:33px; cursor: pointer; text-align:center;border:1px solid #1da89e ; overflow: visible; width:180px; display:block;font-size:16px;float:left; border-right-style:none }
.ssjh_rl1.hover{background:#1da89e ;color:#fff; height: 33px; line-height:33px; cursor: pointer; text-align:center;border:1px solid #1da89e ; overflow: visible; width:180px; display:block;font-size:16px;float:left }
.yqtz_botdiv{ border:1px solid #BFBFBF; overflow:hidden; margin:0 auto 10px auto; width:97%; padding-bottom:0px;  }
.yqtzmaindiv{ margin:0 auto;  overflow:hidden; color:#323232;line-height:30px; padding:0px 0 0 100px;}
.yqtzmaindiv.znone{ border-bottom:none}
.yqtzmaindiv img{float: left;
    height:75px;
    margin:10px 0 5px 0;
    vertical-align: middle;
    width: 75px;
}
.rsbg{ background:#f2914a; display:block; text-align:center; color:#fff; font-size:12px; position:absolute; border-radius:4px; right:10px; top:0; width:30px; line-height:22px; height:22px; cursor:pointer}
.yqtzmaindiv h3{ display:block; float:left; font-size:18px;  width:70%; margin-top:15px; height:30px;}
.yqtzmaindiv p{display:block; float:left; font-size:14px; width:80%}
.yqtzmaindiv p span{ width:30%; display:inline-block}

.yqtz_zleftDiv{ width:100%; margin:0px  auto 0 auto; overflow:hidden; padding-top:10px;  }
.yqtz_zleftDiv span.jgd_rl{background:#1ca89f; height:34px; line-height:34px; color:#fff; cursor: pointer; text-align:center; overflow: visible;  display:inline-block;font-size:16px;float:left; width:107px; border-radius:5px}

.yqtz_zleftDiv span.jgd_rl1{background:#da4a4c; height:34px; line-height:34px; color:#fff; cursor: pointer; text-align:center; overflow: visible;  display:inline-block;font-size:16px;float:left; width:107px; border-radius:5px; margin-left:20px;}
.yqtz_zleftDiv span.jgd_rl2{background:#1ca89f; height:31px; line-height:31px; color:#fff; cursor: pointer; text-align:center; overflow: visible;  display:inline-block;font-size:14px;float:left; width:98px; border-radius:5px}
.yqtz_zleftDiv span.bjgd_rl2{background:#1ca89f; height:31px; line-height:31px; color:#fff; cursor: pointer; text-align:center; overflow: visible;  display:inline-block;font-size:14px;float:left; width:98px; border-radius:5px}

.yqtz_zleftDiv span.greyjgd_rl{background:#d9d9d9; height:34px; line-height:34px; color:#fff; cursor: pointer; text-align:center; overflow: visible;  display:inline-block;font-size:16px;float:left; width:107px; border-radius:5px}

.yqtz_zleftDiv span.greyjgd_rl1{background:#d9d9d9; height:34px; line-height:34px; color:#fff; cursor: pointer; text-align:center; overflow: visible;  display:inline-block;font-size:16px;float:left; width:107px; border-radius:5px; margin-left:20px;}

.jgd_rl3{background:#1ca89f; height:31px; line-height:31px; color:#fff; cursor: pointer; text-align:center; overflow: visible;  display:inline-block;font-size:14px;width:98px; border-radius:5px}
.jgd_rl4{background:#da494c; height:35px; line-height:35px; color:#fff; cursor: pointer; text-align:center; overflow: visible;  display:inline-block;font-size:16px;width:173px; border-radius:5px; margin:0 auto}

.jt_zleftDiv span.jgd_rl{background:#fff; height:34px; line-height:34px; cursor: pointer; text-align:center;border:1px solid #1da89e; overflow: visible;  display:inline-block;font-size:16px;float:left; width:172px;
 }
.jt_zleftDiv{ width:350px; margin:10px 0; overflow:hidden; float:right}
.bottombr{    border-bottom: 1px solid #d6d6d6; padding-left:40px; padding-bottom:10px}
.bottomxx{    border-bottom: 1px dashed #d6d6d6; width:95%; margin:20px auto 20px auto; padding-bottom:20px}

/*.yqtz_zleftDiv span:first-child,.jt_zleftDiv span:first-child{
	border-left-color: #1da89e;
	border-radius:5px 0 0 5px;
}
*/
/*.yqtz_zleftDiv span:last-child,*/
.jt_zleftDiv span:last-child {
	border-radius: 0 5px 5px 0;
}

.yqtz_zleftDiv span.jgd_rl.currebg,.jt_zleftDiv span.jgd_rl.currebg,.yqtz_zleftDiv span.jgd_rl1.currebg,.yqtz_zleftDiv span.jgd_rl2.currebg{background:#1da89e ; color:#fff;border:1px solid #1da89e ;}
.yqtz_tyq{ overflow:hidden; height:31px; line-height:31px; font-size:14px; color:#323232; padding:10px 20px 10px 40px; }
.yqtz_tyq span{ display:block; float:left; }
.yqtz_tyq span.lm1{ width:60%;}
.yqtz_tyq span.lm2{ width:20%;}
.yqtz_tyq img{ cursor:pointer}
.yqtz_tytyq{ overflow:hidden; width:100%;border-bottom: 1px solid #d6d6d6;}
.yqtzred{ color:#da4a4c}
.yqtzgreen{ color:#abd46e}
.yqtzzred{ color:#ff0000}
.yqtzblue{color:#0492e3; font-size:12px; padding-left:145px; display:block; margin-top:5px}

/*修改首页*/
.layui-layout-admin #larry-body{overflow-y:hidden ;}
.header-menu .layui-main   .layui-nav .layui-nav-item a{line-height: 68px;}
.header-menu .layui-main ul.layui-nav li.layui-nav-item{vertical-align: top; }

/*加入设置*/
.addset{width: 850px;}
.addset .layui-form-item .layui-input-inline{line-height: 20px ;padding: 9px 0;width: 250px;}
.addset .layui-form-item{margin-bottom: 0;}
.addset .layui-input-inline .layui-input{border: none;border-bottom: 1px solid #dddddd;height: 25px;line-height: 25px;border-radius: 0;padding-left: 0;}
.addset .layui-form-label{width: 200px;}
.addset .layui-input-block{margin-left: 170px;}
.addset .appr-txt{display: inline-block;font-size: 12px;position: absolute;left: 280px;top: -37px;}
.appr-txt .appr-con{width: 300px;height: 162px;background: url(../images/tishi_bg.png)no-repeat;background-size: 300px 162px;}
.appr-txt .appr-con p{width: 220px;height: 120px;margin: 25px 25px 25px 48px;color: #fe4e00;}
.appr-txt img{vertical-align: top;margin-top: 40px;}

/*行间距*/
.heightnum{line-height: 35px;padding: 5px 15px;}
.heightnum2{line-height: 25px;padding: 0 13px;}
.layui-btn-own img{vertical-align: top;margin-top: 9px;}
.layui-color-green{color: #1aa094;}
.layui-color-red{color: #da4a4c;}
.layui-color-gary1{color: #666666;}
.layui-color-gary2{color: #9B9B9B;}
.layui-sel-btn{background: #383c48;line-height: 40px; }
.layui-sel-btn:hover{background: #009688;}
.curr-btn-color{background: #009688;color: #fff;}
/*通知公告*/
.layui-mark-red{color: #ff2626;}
.notice-txt{margin:0 auto;background:url(../images/notice_bg.png)no-repeat;width: 278px;height: 564px;background-size: 278px 564px;}
.notice-list{padding-top: 110px;}
.notice-con{background: #fff;text-align: left;margin: 20px 30px 10px 30px;padding: 10px 0 5px 0;    width: 217px;}
.addtxt-con{background: url(../images/addtxt_bg.png)no-repeat;background-size: 177px 39px;width: 177px;height: 39px;line-height:40px;color: #fff;font-size: 12px;padding-left: 17px;position: absolute;left: 100px;top: 10px}
.layui-txt-add{font-size: 12px;margin: 10px 0;}
.layui-txt-add dt{float: left}
.layui-txt-add dd{margin-top: 1px;margin-left: 33px;}
.layui-txt-add dd p.layui-txt-tit{width: 200px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;}
/*修改 更新幼儿数与家长数弹框*/
.popup-container {padding: 20px 20px 0 20px;font-size: 14px;}
.width-setting1 {width: 500px;}
.width-setting2 {width: 680px;}
.inline-label {text-align: right;display: inline-block;width: 120px;}
.table-row {padding: 10px 0;position: relative;}
.ul-input {width: 65%;border:0;border-bottom: 1px solid #ccc;}


.layui-tab-ul{height: 34px;line-height: 34px;background: #fff;font-size: 0;width: 188px;}
.layui-tab-ul li{display: inline-block;width:50%;cursor: pointer;text-align: center;font-size: 14px;}
.layui-tab-ul li.curr-btn{background: #25B294;color: #fff;}
/*通知公告*/
.noti-content{margin: 20px;color: #363636; background: #fff; min-width: 850px;}
.noti-content h3{height: 40px;line-height: 40px;font-size: 14px;background: #F2F2F2;border-bottom: 1px solid #ddd;}
.noti-content h3 .stat-mark{vertical-align:top;background: url(../images/read.png)no-repeat;background-size: 38px 38px;display: inline-block;width: 38px;height: 38px;}
.noti-content h3 .unread-mark{background: url(../images/unread.png)no-repeat;}
.noti-content p{height: 50px;line-height: 40px;font-size: 14px;}
/*.noti-content p label{cursor: pointer;}*/
.noti-content dl{font-size: 0;}
.noti-content dl dt,.noti-content dl dd{display: inline-block;vertical-align: top;font-size: 14px;}
.noti-content dl dt{width: 205px;}
.noti-content dl dt img{margin: 15px;width: 180px;}
.noti-content dl dd{width: 86%;padding: 15px 0 0px 5px; line-height: 23px;}
.noti-content {
    border: 1px solid #fff;
}
.noti-content:hover {
    border: 1px solid #45B0A5;
}
@media only screen and (max-width: 1700px) {
	.noti-content dl dd{width: 85%;}
}
@media only screen and (max-width: 1600px) {
	.noti-content dl dd{width:85%;}
}
@media only screen and (max-width: 1500px) {
	.noti-content dl dd{width: 82%;}
}
@media only screen and (max-width: 1400px) {
	.noti-content dl dd{width: 82%;}
}
@media only screen and (max-width: 1230px) {
	.noti-content dl dd{width: 78%;}
}
@media only screen and (max-width: 1000px){
	.noti-content dl dd{width: 70%;}
}

.detail-tit{line-height: 26px;padding: 10px 0;border-top: 1px dashed #E4E4E4;border-bottom: 1px dashed #E4E4E4;color: #9B9B9B;}
.detail-txt{color: #383838;line-height: 22px;padding: 5px 15px;}
.detail-txt p{margin-top: 5px;}
.pro-list{display: inline-block;text-align: center;margin: 15px;}
.pro-list p{margin-top: 5px;}
.notice-label{position:relative;border-radius:2px;padding:0 5px;font-size:12px;color:#fff;background: #FC8458;display: inline-block;min-width: 58px;height: 24px;line-height:24px;text-align: center;margin-top: 8px;margin-right: 8px;}
.notice-label img{position: absolute;bottom: -5px;left: 30%;}
/*生病地图展示*/
.out-container{background: url(../images/data_bg.jpg)no-repeat;background-size:100% 880px;height: 880px;min-width: 800px;}
.ill-tit{background: #052E50;font-size: 16px;height: 40px;line-height: 40px;color: #2CFFFE;text-align: center;border: 2px solid #024466;display: inline-block;}
.data-set{text-align: right;color: #fff;padding-right: 20px;}
.data-set img{margin-right: 5px;vertical-align: top;}
.data-set span{cursor: pointer;}
.sick-per{width: 200px;position: absolute;bottom: 20px;right: 0;}
.sick-per img{margin:0 10px 15px 0;}
.trend-con{border: 2px solid #0069b4;height: 295px;background: #012653;}
.trend-con h3{background:#013562;height: 40px;line-height: 40px;font-size: 16px;color: #2BFEFD;text-align: center;border: 2px solid #004b78;margin: 2px 3px}
ul.chart-txt {color: #fff;padding: 15px;}
ul.chart-txt li{margin-bottom: 9px;overflow:hidden;text-overflow: ellipsis;white-space: nowrap;}
ul.chart-txt li img{vertical-align: top;margin-right: 7px;}
ul.chart-txt li span{float: right;}
.color-blue{color: #26AEFB;}
.color-blue2{color: #4AFFFF;}
.data-num{width: 248px;height: 47px;border: 1px solid #01367a;font-size: 0;padding: 0 2px;}
.data-num span{display: inline-block;margin-left: 0.7px;margin-top: 3px;font-size: 25px;vertical-align: top;color: #87baf9;text-align: center;line-height: 40px; float:left }
.data-num span.data-num-middle{width: 33px;height: 40px;background: url(../images/num_bg2.png)no-repeat;background-size:33px 40px ;}
.data-num span.data-num-middle label{margin-left: 3px;}
.data-num span.data-num-first{width: 30px;height:40px;background: url(../images/num_bg2.png)no-repeat;background-size: 33px 40px;}
.data-num span.data-num-end{width: 45px;height: 40px;background: url(../images/num_bg3.png)no-repeat;background-size: 45px 40px;}
.data-num span.data-num-end label{margin-left: 2px;}
.data-num2{width: 248px;height: 43px;line-height:43px;background: url(../images/num_bg2.png)no-repeat;background-size: 247px 50px;font-size: 24px;color: #89BAF6;text-align: center;}
ul.data-per{position:absolute;font-size: 14px;color: #fff;}
ul.data-per li{margin-top: 18px;}
ul.data-per li img{margin-right: 14px;}
@media only screen and (max-width: 1350px){
    .data-num{width: 94%;}
    .data-num span.data-num-middle{width: 13%;}
    .data-num span.data-num-first{width: 13%}
    .data-num span.data-num-end{width: 19%}
    .data-num2{width:100%;background-size:100% 50px }
}
@media only screen and (max-height: 750px){
    ul.data-per li{margin-top: 12%}
    .div-zhzs .heightnum{line-height: 28px;padding:3px 15px;}
}
.color-blue-txt{color: #4AFFFF;font-size: 24px;}
.color-blue2-txt{color: #1CA49D;font-size: 24px;}
.color-white-txt{color: #fff;font-size: 16px;}
.color-green-txt{color: #20C72E;font-size: 30px;}
.color-green2-txt{color: #A8DE66;font-size: 24px;}
.color-yellow-txt{color: #F6F74A;font-size: 45px;}
.color-purple-txt{color: #3850E3;font-size: 24px;}
.color-purple2-txt{color: #AE1ECC;font-size: 30px;}
.color-brown-txt{color: #C27635;font-size: 24px;}

.txt-label{color:#B1BAC1;border: 1px solid #045B88;display: inline-block;border-left: 2px solid #045B88;padding: 0 7px;}
.curr-label{color: #09E3EC;border-left: 2px solid #09E3EC;}
.btn-txt{text-align: center}
.btn-txt li{}
ul.chart-label li{white-space: normal;}
ul.chart-label li i{width: 70%;display: inline-block;text-overflow: ellipsis;overflow: hidden;}/*white-space: nowrap;*/
/*.chart-table tr{height: 59px}*/
.chart-table th{width: 16.6%;padding: 0;text-align: center;}
.chart-table tr td{position: relative;text-align: center;}

.data-num-set{width: 250px;margin: 0 auto;}
.data-num-set li{height: 36px;line-height: 36px;margin-top: 8px;}
.data-num-set li .num-set-left{width: 120px;display: inline-block;}
.data-num-set li .num-set-left img{vertical-align: top;margin-top: 6px;margin-right: 8px;}
.data-num-set li .num-set-right .layui-unselect{vertical-align: top;margin-top: 7px;margin-right: 10px;}
.data-num-set li .num-set-right .layui-form-onswitch{border-color: #1CA89D;background-color: #1CA89D;}

  /*复选框样式*/
.check-equilibrium{ position: absolute; left:0px; bottom:-5px; width:130px; color: #D0D0D0;  }
.regular-checkbox {
            display: none;
        }
.regular-checkbox + label {
                position: relative;
                display: inline-block;
                padding:5px;   
               /* background-color: #5CB878;*/
	            border-radius: 3px;
                border: 1px solid #D0D0D0;
                box-shadow: 0 1px 3px rgba(0,0,0,0.5); /*vertical-align: text-top*/
          
               
            }
                
.regular-checkbox + label:active, .regular-checkbox + label:checked + label:active {
                    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
                }
           
 .regular-checkbox:checked + label {
                background-color: #5CB878;
                box-shadow: 0 1px 2px rgba(0,0,0,0.05); border: 1px solid #5CB878;
            }
                /*选中后的效果*/
 .regular-checkbox:checked + label:after {
                    content: '\2714';
                    position: absolute;
                    font-size: 11px;
                    top: -3px;
                    left: 1px; 
                    color: #fff; }

.col-red{width:16px;height:24px;display: inline-block;font-size:26px;line-height:16px;text-align:center;color:#E83418;text-decoration:none; margin: 0 8px;vertical-align: middle} 
.col-green{width:16px;height:24px;display: inline-block;font-size:26px;line-height:16px;text-align:center;color:#63F074;text-decoration:none; margin: 0 8px;vertical-align: middle}   
.col-yellow{width:16px;height:24px;display: inline-block;font-size:26px;line-height:16px;text-align:center;color:#DBD632;text-decoration:none; margin:0 8px;vertical-align: middle}   
.col-gray{width:16px;height:24px;display: inline-block;font-size:26px;line-height:16px;text-align:center;color:#C0C2C2;text-decoration:none; margin: 0 8px;vertical-align: middle}   
.sp_junheng{line-height: 24px; display: inline-block; cursor: pointer}
/*按钮*/
.opr-btn{border-radius:3px;background: #1CA89D;width: 105px;height: 30px;line-height:30px;text-align:center;display: inline-block;color: #fff;}
.pay-btn{text-align: center;margin-top: 20px}
.pay-btn a{margin-left: 5px;margin-right: 5px;}
.pay-btn a:hover{color: #fff;}

.noticeTitle{color: #fff;  padding: 7px;  font-size: 14px;}
/*大数据 新*/
.statistics{margin: auto;font-size: 0;background: url(../images/data_bg.jpg)no-repeat;background-size: 100% 880px;}
.statistics-list{display: inline-block;font-size: 14px;vertical-align: top;}

.curr-pro{background: #014067;position: relative; }
.curr-pro .bor-one{width: 12px;height: 12px;display: inline-block;border-left: 2px solid #2CFFFE;position: absolute;left: 0;top: 0;border-top: 2px solid #2CFFFE;}
.curr-pro .bor-two{width: 12px;height: 12px;display: inline-block;border-right: 2px solid #2CFFFE;position: absolute;right: 0;top: 0;border-top: 2px solid #2CFFFE;}
.curr-pro .bor-three{width: 12px;height: 12px;display: inline-block;border-left: 2px solid #2CFFFE;position: absolute;left: 0;bottom: 0;border-bottom: 2px solid #2CFFFE;}
.curr-pro .bor-four{width: 12px;height: 12px;display: inline-block;border-right: 2px solid #2CFFFE;position: absolute;right: 0;bottom: 0;border-bottom: 2px solid #2CFFFE;}
/*修改通知公告*/
.notice-sub{background: #fff;margin: 0 22px 0 21px;min-height: 349px;text-align: left;padding: 15px;font-size: 12px;height: 349px;overflow-y: auto;overflow-x: hidden;}
.notice-sub-p{line-height: 20px;}
.notice-sub .layui-txt-add dd p.layui-txt-tit{width: 170px;}
.notice-title{min-height: 379px;height: 379px;overflow-y:auto ;overflow-x: hidden;}

.notice-pages {float:right;margin-right:20px;line-height: 25px; margin-top: 10px;}
.notice-pages a {display: inline-block;padding: 5px 14px 5px 14px;margin-left:5px;color: #666;border: 1px solid #dedede; font-size:14px;}
.notice-pages em {margin-left: 10px;height:35px;line-height:30px;color:#666;display:inline-block;}

.notice-pages .pages-skip{margin-left: 20px;}
.notice-pages .pages-skip input[type="text"]{border: 1px solid #dedede;border-radius: 0;margin:0 5px;width:60px;height: 35px;vertical-align: top; text-align: center;}
.notice-del-label{margin-left: 10px;cursor: pointer;color: #D8474F;}
.notice-edit-label{cursor: pointer;}
.notice-del-label img{vertical-align: top;width: 16px;margin: 11px 3px 0 0}
.notice-pages .selected {font-weight: bold;color: #fff;}
.notice-pages a:hover{color: #fff;}
.notice-pages .pages-skip .skip-sure{width: 45px;text-align: center;color: #fff;}



.noti-tit-right{float: right;}
.time-con{display: inline-block;line-height: 16px;vertical-align: middle;margin-top: -3px;font-size: 12px;margin-right: 20px;}
.time-con .time-txt{display: block;color: #FE3939;}
.time-con .time-num i{background: #000;color: #fff;padding: 0 4px;border-radius: 3px;}
.enroll-label{font-size: 12px;}
.enroll-label img{margin-right: 3px;vertical-align: top;margin-top: 11px;}
.end-label{margin-right: 20px;color: #999;font-size: 12px;}
/*修改选择群发*/
.layui-label-check{height: 40px;}
.sel-set-check label{margin-top: 10px;display: inline-block;margin-left: 30px;font-size: 14px;}
.stat-num-right{float: right;margin: 10px 20px 0 0;}
.sel-set-check label input[type="checkbox"]{-webkit-appearance:none;vertical-align: top;margin-top:3px;width: 14px;height: 14px;border: 1px solid #ccc;margin-right: 5px;}
.sel-set-check label input[type="checkbox"]:checked{background: url(../images/checked_bg.png) center center;background-size: 14px 14px;border: none;}

/*新增个人基本信息*/
.personal-center-left .webuploader-pick{background: #1AA095;}
.personal-center-left .webuploader-pick a{color: #fff;}
.personal-type{margin-top: 10px;display: inline-block;}
.set-txt-btn{color: #fff;float: right;margin-top: 8px;}
.set-txt-btn img{margin-right:5px;vertical-align: top;}
.personal-tit{background: #F2F2F2;height: 38px;line-height: 38px;border-bottom: 1px solid #E4E4E4;padding-left: 20px;}
/*修改大数据营养素统计*/
.data-num-set2{width: 400px;}
.data-num-set2 .num-set-left{width: inherit !important}
.data-num-set2 .num-set-right input[type="text"]{text-align:center;width: 50px;border: none;border-bottom: 1px solid #E2E2E2;border-radius: 0;}
.data-num-set2 .bala-set-num{width: 50px;display: inline-block;text-align: center;color: #676767;}

/*首页左侧栏搜索框*/
.search-con{border: 1px solid #96979B;  width: 237px;  margin-left: 5px;  margin-top: 10px;  height: 26px;  line-height: 26px;  border-radius: 0;}
.search-con .iconfont{color:#96979B;font-size:14px;margin-left: 6px;}
.search-con input{width: 83%;  border: 0;  color: #96979B;}

/*欢迎页*/
.wel-con{text-align: center;margin-top: 9%;}
.wel-con .wel-pic{width: 700px;}
.wel-con h1{font-size: 58px;color: #eb5312;cursor: pointer;}
.wel-con p{font-size: 28px;color: #96C3CB;margin-top: 10px;}
@media only screen and (max-width: 1280px) {
	.wel-con .wel-pic{width: 600px;}
}
/*填写报名信息*/
.layui-repair-input input{border: 0;border-bottom: 1px solid #E4E4E4;border-radius: 0}
.enroll-mess .layui-form-item{margin: 5px 60px;}

/*食谱公示详情*/
.map-box{width: 230px;background: rgba(49,50,56,0.78);position: relative;}
.map-box h3{font-size: 14px;color: #efc631;text-align: center;padding-top:0px;margin-top: 5px;}
.map-box p{font-size: 12px;color: #fff;text-align: center;}
.map-box p a{cursor:pointer;margin-left:5px;color:#fff;width: 45px;height: 17px;display: inline-block;background: #ff9900;text-align: center;line-height: 18px;}
.map-box p a:hover{text-decoration: none;}
.map-box ul{color:#fff;list-style: none;font-size: 12px;padding-bottom: 0px;}
.map-box ul i{font-style: normal;}
.map-box ul li {margin:0 20px 10px 20px;height: 18px;width: 85%;position: relative;}
.map-box ul li .xuhao{position:absolute;left:-11px;float:left;width: 22px;margin-top:-3px;height: 22px;display: inline-block;border-radius: 50%;text-align: center;line-height: 22px;}
.map-box ul li b{overflow:hidden;white-space:nowrap;text-overflow:ellipsis;margin-left:0px;padding-left:12px;font-weight: normal;width: 100px;background: #16adff;display: inline-block;}
.map-box ul li i{float: right;color: #ea3518;}
.map-box ul li.span1 .xuhao{background: #16adff;}
.map-box ul li.span1 b{background: #16adff;}
.map-box ul li.span2 .xuhao{background: #00d3d3;}
.map-box ul li.span2 b{background: #00d3d3;}
.map-box ul li.span3 .xuhao{background: #d58365;}
.map-box ul li.span3 b{background: #d58365;}
.map-box ul li.span4 .xuhao{background: #7f9f33;}
.map-box ul li.span4 b{background: #7f9f33;}
.map-box ul li.span5 .xuhao{background: #c14bfc;}
.map-box ul li.span5 b{background: #c14bfc;}

/*查看园所申请的详情信息*/
.person-tab td{border: 1px solid #DEE7E6;text-align: center;}
.person-tab{margin: 10px;}
.person-tab img{margin: 0 3px;margin-top: 5px;}
.person-tab .check-label{margin:auto 10px;color: #A5A5A5;}
.person-tab table tr td{height: 34px;line-height: 24px;}
.person-tab .gove-tit{height: 40px;line-height: 30px}
.person-tab .gove-tit a{float: right;}
.person-tab table .curr-tab{color:#3aa6ff;}
.person-tab table a{margin: 0 8px;}
/*未发起上报（无数据）*/
.nodata-img{position: absolute;top: 50%;left: 50%;margin-top: -108px;margin-left: -203px;}
/*上报*/
.mark-label{border: 1px solid #FE7B43;display: inline-block;height: 20px;line-height:20px;padding:0 10px 0 13px;font-size: 12px;color:#FE7B43;text-align: center;position: relative;}
.mark-label:before{content: "";width: 3px;height: 100%;display: inline-block;background: #FE7B43;position:absolute;left: 0;top: 0;}

.report-list h5{height: 44px;line-height: 44px;background: #47494A;margin:15px 20px;color: #fff;padding-left: 20px;}
.report-row{margin: 0 10px 0 10px !important;}
.rep-list{vertical-align: top;padding: 0 5px!important;}
.times-con{border: 1px solid #EEEEEE;border-radius: 4px;color: #4A4A4A;}
.times-con li{min-height: 40px;line-height: 40px;border-bottom: 1px dashed #E7E7E7;padding: 0 20px;}
.report-list a{color: #63B3E8;text-decoration: underline;}
.times-con li:last-child{border-bottom: none;}
.cir-div,.cir-div2{position:relative;margin:15px 5%;text-align:center;width: 76px;height: 76px;line-height:26px;border-radius:50% ;display: inline-block;}
.cir-div{border: 4px solid #00d1ec;}
.cir-div2{border: 4px solid #eee;}
.cir-div div,.cir-div2 div{margin-top:14px;}
.cir-div span,.cir-div2 div{font-size: 12px;}
.cir-div span,.cir-div2 span{display: block;margin-top:3px;}
.report-num .cir-div span.pur-span{color: #8693ef;border-bottom: 1px solid #EFEFEF;margin: 0 5px;}
.report-num .cir-div span.ora-span{color: #FA7E4A;font-size: 18px;}
.report-num{text-align: center;}
.cir-div .report-label{position: absolute;top: -4px;right:-84px;font-size: 12px;color: #fff;margin-top: 0;}
.cir-div .report-label label{height:22px;line-height:22px;display: block;padding: 0 3px;border-radius: 3px;margin-bottom: 6px}
.green-label{color: #1FA296;}
.pink-label{color: #EC76E3;}
.report-month-quarter{background: #F5F5F5;margin: 0 20px 0 20px;padding: 20px 10px 10px 0;margin-bottom: 20px;}
.report-month-quarter .report-row{margin-right: 0!important;}
.report-month-quarter .rep-top{position: relative;}
.report-month-quarter .rep-top p{margin-bottom: 10px;font-size: 14px;}
.report-month-quarter .rep-top .iconfont{color: #1AA094;font-size: 20px;vertical-align: middle;margin: 0 5px;cursor: pointer;}
.report-month-quarter .rep-top .mark-left{background: url(../images/markle_bg.png)no-repeat;width: 33px;height: 33px;position: absolute;left: -10px;top: -20px;color: #fff;font-size: 12px;padding: 3px 0 0 3px;}
.month-quart-list{background: #fff;text-align: center;height: 235px;}
.month-quart-list h6{height: 40px;line-height: 40px;text-align: center;} 
.green-bg{background: #1AA094;color: #fff;}
.gray-bg{background: #E6E6E6;}
.gray-bg2{background: #F4F4F4;}
.orange-bg{background: #FE8652;color: #fff;}
.red-bg{background: #DA4A4C;color: #fff;}
.month-quart-list .no-rep{margin-top: 50px;}
.month-quart-list .cir-div,.cir-div2{margin:15px 3px;position: relative;}
.month-quart-list .report-num{margin-top: 20px;}
.month-quart-list .report-label{bottom: 65px !important;right: -20px;top: inherit;}
.month-quart-list .cir-div .report-label label{margin-bottom: 2px;}
.num-div{text-align: left;margin-left: 10px;font-size: 13px;}
.num-div .mark-label{margin-top: 3px;}
.report-month-quarter .month-quart-con{margin-bottom: 20px !important;}
.no-rep-txt{color: #6F6F6F;font-size: 18px;margin-top: 82px;}
.tab-tit{border-bottom: 1px solid #e2e2e2;}
.tab-tit .layui-tab-title{display: inline-block;border-bottom: 0;}
.sel-enr-btn{display: inline-block;}
.sel-enr-btn button{width: 105px;height: 30px;line-height: 30px;display: block;}
/*查看上报详情*/
.rep-list-con{padding: 9px 15px 9px 0;}
.report-con .layui-form-item{margin-bottom: 0;}
.report-con .layui-form-item .layui-input-block{min-height: 20px;}
/*查看未上报*/
.rep-table{padding: 20px;}
.rep-table-top{height: 42px;line-height: 42px}
.cir-btn{background: #FE8652;height:30px;margin-top:6px;line-height:30px;color: #fff;padding:0 15px;border-radius: 20px 0 0 20px;float: right;margin-right: -20px;}
.cir-btn-gray{background: #DADADA;height:30px;margin-top:6px;line-height:30px;color: #fff;padding:0 15px;border-radius: 20px 0 0 20px;float: right;margin-right: -20px;}
.rep-table table,.rep-table table th{text-align: center;}
.rep-table table input[type="checkbox"]{margin-right: 10px;}
/*查看已上报*/
.rep-txt-list{border: 1px solid #E5E5E5;margin: 20px;padding-bottom: 40px;}
.rep-txt-list .p-tit{height: 38px;line-height: 38px;background: #F2F2F2;border-bottom: 1px solid #E5E5E5;padding: 0 15px;}
.rep-txt-list .p-tit input{margin-right: 8px;}
.rep-txt-list .p-tit span{float: right;color: #797979;}
.rep-txt-list .rep-list-congtent .div-tit{margin: 20px 0 40px 0;}
.rep-txt-list .rep-list-congtent .div-tit span{margin-left: 70px;color: #1AA094;}
.rep-txt-list .rep-progress{text-align: center;width: 920px;margin: 0 auto;}
.rep-txt-list .rep-img-pro:after{content: "";width: 120px;border-top:1px dashed #E5E5E5;position: absolute;top: 15px;right: -60px;}
.rep-txt-list .rep-img-pro:last-child:after{content: none;}
.rep-txt-list .rep-progress .rep-img-pro{display: inline-block;width: 180px;position: relative;font-size: 12px;}
.rep-txt-list .rep-progress .rep-img-pro img{margin-bottom: 10px;}
.rep-txt-list .rep-progress .rep-img-pro span{margin-top: 5px;display: inline-block;color: #999;}


.rep-textarea{margin: 20px 40px;border: 1px solid #E7E7E7;padding: 10px;}
.rep-textarea textarea{border: none;width: 100%;height: 100px;}
.rep-textarea p{text-align: right;color: #A5A5A5;}
/*出勤登记统计*/
.count-top{height: 68px;line-height: 68px;border: 1px solid #ECECEC;}
.count-top .layui-form-item{margin-top: 15px;display: inline-block;vertical-align: top;margin-right: 30px;}
.stat-top-con h6{background: #47494A;color: #fff;height: 44px;line-height: 44px;text-align: center;}
.stat-list-con{border: 1px solid #e8e8e8;border-top: none;height: 283px;position: relative;}
.stat-list-con .right-mark{position: absolute;right: 20px;}
.stat-list-con .right-mark .red{color: #F6766C;}
.stat-list-con .right-mark .blue{color: #60B9E7;}
.stat-list-con .right-mark p{margin-top: 20px;}
.stat-list-con .right-mark img{margin-right: 10px;}
.stat-list-num{margin: 0 10px !important;}
.cir-div-stat{width: 100px;height: 100px;border: 7px solid #00D1EC;}
.cir-div-stat div{margin-top: 23px;}
.cir-div-stat span.ora-span{font-size: 34px !important;padding-bottom: 5px;}
.cir-div-stat span.pur-span{font-size: 16px !important;padding-top:2px;border-bottom: none!important;border-top: 1px solid #EFEFEF;}
/*区域园所招生数据分析*/
.stat-num-top{font-size: 12px;color: #fff;height: 140px;padding-top: 10px;}
.stat-tit{background: #013562;height: 40px;line-height: 40px;font-size: 16px;color: #2BFEFD;text-align: center;border: 2px solid #004b78;margin: 0 auto;width: 22%;}
.stat-tit2{text-align: center;height: 40px;line-height: 40px;font-size: 16px;color: #fff;position: relative;}
.stat-tit2:after{
		content: "";
	    width: 80%;
	    height: 2px;
	    position: absolute;
	    bottom: 0;
	    left: 10%;
	    background: -moz-linear-gradient(left, rgba(1,66,100,0) 0%,rgba(15,107,152,1) 50%,rgba(1,66,100,0) 100%);		
		background: -webkit-gradient(linear, left, right, color-stop(0%,rgba(1,66,100,0)), color-stop(50%,rgba(15,107,152,1)), color-stop(100%,rgba(1,66,100,0)));		
		background: -webkit-linear-gradient(left, rgba(1,66,100,0) 0%,rgba(15,107,152,1) 50%,rgba(1,66,100,0) 100%);		
		background: -o-linear-gradient(left, rgba(1,66,100,0) 0%,rgba(15,107,152,1) 50%,rgba(1,66,100,0) 100%);		
		background: -ms-linear-gradient(left, rgba(1,66,100,0) 0%,rgba(15,107,152,1) 50%,rgba(1,66,100,0) 100%);		
		background: linear-gradient(to right, rgba(1,66,100,0) 0%,rgba(15,107,152,1) 50%,rgba(1,66,100,0) 100%);		
		filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='rgba(15,107,152,1)', endColorstr='rgba(1,66,100,0)',GradientType=1 );
		}
		:root .gradient{filter:none;}		
.stat-border-list{height: 100%;margin: 0 10px 10px 10px;background: url(../images/stat_border_bg.png)no-repeat 100% 100%;background-size: 100% 116px;}
.stat-border-list .grid-demo{text-align: center;width: 270px;margin: 12px auto 0 auto;}
.stat-border-list .grid-demo .white-txt{color: #fefefe;font-size: 16px;margin: 3px 0;}
.stat-border-list .grid-demo .blue-txt{color: #02FEFF;font-size: 30px;}
.stat-border-list .grid-demo .orange-txt{color: #EDAD01;font-size: 30px;}
.stat-border-list .grid-demo .green-txt{color: #15E403;font-size: 24px;}
.stat-border-list .grid-demo .yellow-txt{color: #D2E500;font-size: 24px;}
.stat-border-list .grid-demo .blue-small-txt{color: #008CC2;font-size: 12px;}
.gradual-bg{
		position: relative;
		display: table-cell;
		vertical-align: middle;
		width: 270px;
		height: 58px;		
		background: -moz-linear-gradient(left, rgba(1,66,100,0) 0%,rgba(1,66,100,1) 50%,rgba(1,66,100,0) 100%);		
		background: -webkit-gradient(linear, left, right, color-stop(0%,rgba(1,66,100,0)), color-stop(50%,rgba(1,66,100,1)), color-stop(100%,rgba(1,66,100,0)));		
		background: -webkit-linear-gradient(left, rgba(1,66,100,0) 0%,rgba(1,66,100,1) 50%,rgba(1,66,100,0) 100%);		
		background: -o-linear-gradient(left, rgba(1,66,100,0) 0%,rgba(1,66,100,1) 50%,rgba(1,66,100,0) 100%);		
		background: -ms-linear-gradient(left, rgba(1,66,100,0) 0%,rgba(1,66,100,1) 50%,rgba(1,66,100,0) 100%);		
		background: linear-gradient(to right, rgba(1,66,100,0) 0%,rgba(1,66,100,1) 50%,rgba(1,66,100,0) 100%);		
		filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='rgba(1,66,100,1)', endColorstr='rgba(1,66,100,0)',GradientType=1 );
		}
		:root .gradient{filter:none;}
.gradual-bg:before{
	    content: "";
	    width: 120px;
	    height: 2px;
	    position: absolute;
	    top: 0;
	    left: 50%;
	    margin-left: -60px;
	    background: -moz-linear-gradient(left, rgba(1,66,100,0) 0%,rgba(26,167,224,1) 50%,rgba(1,66,100,0) 100%);		
		background: -webkit-gradient(linear, left, right, color-stop(0%,rgba(1,66,100,0)), color-stop(50%,rgba(26,167,224,1)), color-stop(100%,rgba(1,66,100,0)));		
		background: -webkit-linear-gradient(left, rgba(1,66,100,0) 0%,rgba(26,167,224,1) 50%,rgba(1,66,100,0) 100%);		
		background: -o-linear-gradient(left, rgba(1,66,100,0) 0%,rgba(26,167,224,1) 50%,rgba(1,66,100,0) 100%);		
		background: -ms-linear-gradient(left, rgba(1,66,100,0) 0%,rgba(26,167,224,1) 50%,rgba(1,66,100,0) 100%);		
		background: linear-gradient(to right, rgba(1,66,100,0) 0%,rgba(26,167,224,1) 50%,rgba(1,66,100,0) 100%);		
		filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='rgba(26,167,224,1)', endColorstr='rgba(1,66,100,0)',GradientType=1 );
		}
		:root .gradient{filter:none;}		
.progress-cir-num{width: 82px;height: 82px;display: table-cell;vertical-align: middle;color: #fff;font-size: 26px;}
.porgress-list{width: 48%;height:100%;display: inline-block;vertical-align: top;}
.progress-con{display: inline-block;vertical-align: top;width: 100px;height: 100px;}	
.progress-txt{display: inline-block;vertical-align: top;text-align: left;margin-top: 26px;margin-left: 15px;}	
.purple-pro .bar{background: #5B69BC;}
.purple-pro .progress-num{color: #A5B2FE;}
.pink-pro .bar{background: #FF8ACC;}
.pink-pro .progress-num{color: #FF8ACC;}
.rank-order-list{margin: 24px 15px;}
.rank-order-list li{margin: 10px 0;}
.rank-order-list li .order-num,.rank-order-list li .order-name,.rank-order-list li .order-progress{display: inline-block;color: #fff;vertical-align: middle;}
.rank-order-list li .order-num{background: #004561;width: 56px;height: 26px;border-radius: 2px;line-height: 26px;text-align: center;}
.rank-order-list li .order-name{width: 120px;margin: 0 5px;}
.rank-order-list li .order-num.first-num{background: #ED4901;}
.rank-order-list li .order-num.second-num{background: #EDAD01;}
.rank-order-list li .order-num.third-num{background: #0186ED;}
.rank-order-list li .order-progress{width:48%;}
.seq-progress li p,.seq-progress li .progressbar_1,.seq-progress li span{display: inline-block;font-size: 12px;color: #fff;vertical-align: middle;}
.seq-progress li .progressbar_1{width: 50%;margin: 10px 5px;}
.seq-progress li span{color: #A6B3FF;}
.seq-progress li p{width: 60px;text-align: right;}

/*专家建议*/
.inp-panel{border: 1px solid #E0E0E0;margin: 0 15px;}
.inp-txt{padding: 10px;}
.inp-txt textarea{width: 100%;height: 260px;border: none;}
.inp-exp{border-top: 1px solid #E0E0E0;padding: 8px 15px;}
/*专家建议模板*/
.panel-list{margin: 0 15px;}
.panel-list .current{border: 2px solid #FC7140;}
.panel-txt,.panel-opr{display: inline-block;}
.panel-txt{border: 1px solid #E8E8E8;line-height: 22px;padding: 15px;width:75%;min-height: 40px;}
.panel-opr{vertical-align: top;margin-top: 10px;}
.panel-opr img{margin:0 8px;cursor: pointer;}
.panel-txt textarea{width: 100%;height: 100%;min-height: 38px;border: none;}

/*特殊儿童干预*/
.frame-page{border: 1px solid #3DAFA5;margin: 20px;}
.def-table{border:1px solid #B5B5B5;}
.def-table td{border: 1px solid #B5B5B5;height: 34px;line-height: 24px;color: #161221;text-align: center;}
.special-tit{margin: 20px 0;font-size: 16px;color: #009688;}
.special-tit img{margin-right: 5px;}
.inscribe-info{line-height: 30px;margin: 20px 0;position: relative;}
.inscribe-info>h5{margin-left:30px;font-size: 16px;color: #009688;}
.inscribe-info>div{margin-left: 100px;}
.special-list{border-bottom: 1px solid #E2E2E2;padding:0 30px;}
.inscribe-info input[type="text"]{width:255px;height:22px;border: 1px solid #CECECE;padding: 0 8px;}
.inscribe-info .save-btn{position: absolute;top: 50%;margin-top: -15px;left: 520px;}
.tem-con{margin: 0 30px;position: relative;}
.tem-list{line-height:32px;border-bottom: 1px solid #DFDFDF;padding: 0 22px;color: #999999;}
.tem-list .icon_arrow_right,.icon_arrow_down{display: inline-block;vertical-align: middle;float: right;}
.tem-list .icon_arrow_right:after,.tem-list .icon_arrow_down:after{vertical-align: middle;cursor: pointer;color: #009688;}
.tem-list textarea{width: 90%;height: 120px;padding: 5px 10px;margin-left: 20px;border: 1px solid #E5E5E5;}
.tem-edit{position: relative;margin-bottom: 15px;}
.tem-edit .layui-btn{margin-left: 30px;}
.stat-div{border: 1px solid #EEEEEE;box-shadow: 3px 3px 8px #E5E5E5;}
.stat-div h5{margin: 10px;}
.stat-div h5 span{padding:0 15px;cursor: pointer;border-right: 2px solid #3AB1E3;}
.stat-div h5 span:last-child{border-right: none;}
.stat-div h5 span.current{color: #3AB1E3;}
.send-state{display: inline-block;vertical-align: top;text-align: center;margin: 0 10px;font-size: 16px;color: #333333;}
.sex-list{text-align: center;display: inline-block;}
.sex-list>img{margin: 10px 15px;}
.send-type{display: inline-block;width:30%;position: relative;margin-top: 40px;}
.send-type:after{content: "";height: 50%;position: absolute;right:0;top:25%;border-right: 1px solid #E3E3E3;}
.send-type:last-child:after{content: none;}








/*新增园所用户*/
.incr-chat{margin: 20px; color: #34495E}
.incr-chat .layui-form-label{width:120px;text-align: left; padding: 9px 0px;}
.incr-chat .layui-input-block{margin-left: 95px;}
.incr-chat .layui-form-item{margin-bottom: 10px; position: relative;}
.asdf {background: red;}
.incr-chat .noticeTitle{text-align: center;padding: 14px; background:#21232a;}
.icon_locationdiv{position:absolute; right:0px; top:1px; }
/*查看套餐*/
.setmeal-cen{ margin: 20px 0px 0 0px; border: 1px solid #d5d5d5; padding: 30px; overflow: hidden; background: #fff;position: relative;}
.setmeal-cen.select-bor{border: 1px solid #ff5353;}
.setmeal_img{ width:130px; height: 130px; float: left; margin-right:14px;}
.setmeal_tit{ color:#003cff; font-size: 16px;margin-bottom: 10px;}
.setmeal_titbot{ color:#999999; font-size: 14px;line-height: 20px;}
.setmeal_red{color:#ff5353; font-size: 30px; margin-top: 20px;width: 500px;}
.setmeal-cen span{display: block; text-align: center;color: #65A1E7; font-size: 14px; width: 170px; line-height:35px; border: 1px solid #65A1E7; margin-top: 10px; border-radius: 20px;}
.setmeal-cen span.redbg{ background:#ff4949; color: #fff; margin-top: 0; border: 1px solid #ff4949;}
.setmeal-cen.current,.setmeal-cen:hover{background: #FFFEE4;border: 1px solid #EB655D;cursor: pointer;}
/*查看套餐*/
.notes-cen{margin:40px;color: #34495E}
.notes-cen h3{text-align: center; line-height:30px; font-size: 16px;margin-bottom: 10px; }
.notes-cen p{line-height:20px;font-size: 14px; margin-top: 15px;}
/*查看团体订单*/
.order-cen{ margin:30px auto;}
.order-cen p{ font-size: 14px; line-height: 30px; color: #34495E}
.order-bot{margin:10px 0; border: 1px solid #d5d5d5; padding:20px; overflow: hidden;font-size: 14px;}
.order-tc{display:inline-block; text-align: center;color: #fff; font-size: 14px;  line-height:30px;   border-radius:5px; background:#4bb1ff; margin-left: 10px; padding: 0 20px }
.order-price{color: #999999}
.order-peo{border: 1px solid #d5d5d5;text-align: center;display:inline-block; padding: 2px 10px; margin: 0px 10px 0 0px;height: 33px;line-height: 33px;float: left}
.order-alt{ text-align: right; color:#333333}
/*增加团体订单*/
.order-suspend{ width: 85px; position: relative; display: inline-block;}
.input-txt{ border: none; color: #4bb1ff; line-height:20px; padding:0 10px; width: 323px;border-bottom:1px solid #4bb1ff;border-radius:0; font-size: 14px;margin-right:5px;}
.input-pricetxt{ border: none; color: #333333; line-height: 20px; padding:0 5px; width:30px;border-bottom:1px solid #4bb1ff;border-radius:0; font-size: 14px;margin-right:5px;}
.order-suspend label{ display: inline-block}
.order-suspend p{background:url(../images/physical/pricebg.png) no-repeat; width: 46px; height: 24px;  text-align: center; color: #fff; font-size: 10px; line-height: 28px; position: absolute; top:23px;left: 5px;}
.order-list{display:block;font-size: 14px;margin-top: 40px; overflow: hidden;}
.order-div{ margin-top: 10px; line-height: 40px;margin-left: 70px;}
.order-click{ display: inline-block; width: 15px; margin: 0px 20px 0 -5px;height: 33px;float: left; line-height: 15px; padding-top: 3px
 }
.input-peotxt{border: 1px solid #d5d5d5;text-align: center;display:inline-block;  margin: 10px 10px 0 10px;height: 25px;line-height: 25px; width:89px }
/*查看详情*/
.physical-detail{background:#fff; margin:20px 10px;line-height:25px; overflow: hidden; position: relative}
.physical-detail h3{ font-size: 16px; font-weight: bold; line-height: 40px}
.physical-list{ border-bottom: 1px dashed #E2E2E2; margin: 20px 50px;  overflow: hidden; padding-bottom: 20px;}
.physical-list:last-child{ border-bottom: none;}
.input-time{border: none; color: #4bb1ff; line-height: 20px; padding:0 5px; width:80px;border-bottom:1px solid #4bb1ff;border-radius:0; font-size: 14px;margin-right:5px;}
.state-phy{position: absolute;top: 15px;right:0px;border-radius: 20px 0 0 20px;height: 34px;line-height:34px;display: inline-block;color: #fff;padding: 0 15px;}
.state-exam{position: absolute;top:55px;right:0px;border-radius: 20px 0 0 20px;height: 34px;line-height:34px;display: inline-block;color: #fff;padding: 0 15px;width: 83px; text-align: center}
.phy-infor p{ float: left; margin-right: 25px;}
.phy-infor p:last-child{margin-right: 0;}
/*预览体检项目*/
.physical-tab td{border: 1px solid #DEE7E6;text-align: center;}
.physical-tab{margin: 10px;}
.physical-tab img{margin: 0 3px;margin-top: 5px;}
.physical-tab .check-label{margin:auto 10px;color: #A5A5A5;}
.physical-tab table tr td{height: 34px;line-height: 24px;}
.physical-tab .gove-tit{height: 40px;line-height: 30px}
.physical-tab .gove-tit a{float: right;}
.physical-tab table .curr-tab{color: #1da89e;}
.physical-tab table a{margin: 0 8px;}

/*团体订单缴费*/
.group-list{border: 1px solid #82B7EA;/*margin: 0 25px;*/margin-bottom: 10px; color: #34495E;height: 238px;}
.group-list-tit{background: #F1FAFF;height: 48px;line-height: 48px;border-bottom: 1px solid #E1E1E1;padding-left: 20px;}
.num-mark{background: #EAEFF3;letter-spacing:0;border-radius: 50%;color: #ec3713;width: 25px;height: 25px;line-height:25px;display: inline-block;font-size: 14px;margin-left: 8px;text-align: center;}
.phy-list{border-right: 1px solid #E4E4E4;height: 160px;}
.phy-list-tit{font-size: 15px;color: #34495E;font-weight: bold;margin-bottom: 10px;}
.group-buy{display: inline-block;min-width: 170px;vertical-align: top;line-height: 28px;margin-left: 5px;}
.cost-detial{display: inline-block;position: relative;} 
.cost-detial .discount{position: absolute;background: url(../images/physical/discount_bg.png)no-repeat;width:59px;height: 28px;color: #fff;text-align: center;top: 25px;right: 0;}
.cost-detial .discount i{
	transform:rotate(-10deg);
	-ms-transform:rotate(-10deg); 	/* IE 9 */
	-moz-transform:rotate(-10deg); 	/* Firefox */
	-webkit-transform:rotate(-10deg); /* Safari 和 Chrome */
	-o-transform:rotate(-10deg); 	/* Opera */
	display: inline-block;
}
.phy-label{border: 1px solid #DFDFDF;min-width: 104px;margin: 6px 0;padding: 3px 8px;text-align: center;}
.yellow-btn{border: 1px solid #FFBE21;color: #FFBE21;background: none;width: 110px;height: 38px;border-radius: 20px;cursor: pointer;}
.blue-btn{border: 1px solid #40AAFC;color: #40AAFC;background: none;width: 110px;height: 38px;border-radius: 20px;cursor: pointer;}
.state-label{border-radius: 20px 0 0 20px;height: 34px;line-height:34px;display: inline-block;color: #fff;padding: 0 15px;}
.state-pink{background: #FF98C6;}
.state-green{background: #1EC881;}
.sub-list{margin: 0 auto;margin: 12px 25px;padding-bottom: 12px;padding: 1px 0 12px 0;position: relative;}
.sub-list .state-label{position: absolute;top: 15px;right:-25px;}
.sub-list li{margin: 10px auto;width: 350px;letter-spacing: 1px;}			
/*团体预案管理*/
.institute-con{background: #fff;padding: 0 28px;width: 785px;margin: 0 auto;}
.institute-list{border-bottom: 1px solid #E5E5E5;padding: 26px 0 16px 0;}
.institute-list:last-child{border-bottom: none;}
.institute-txt{display: inline-block;width: 500px;vertical-align: top;margin-left:8px;line-height: 26px;}
/*体检套餐管理*/
.sheet-list{background: #fff;margin: 15px;padding: 20px 0;}    	    	
.sheet-list .sheet-left{display: inline-block;vertical-align: top;min-width: 186px;height:28px;line-height:28px;text-align: left;color: #999999;margin: 5px 0 5px 27px;}
.sheet-list .sheet-right{display: inline-block;margin: 5px 0;line-height: 30px;position: relative;}
.sheet-list .sheet-right .icon_time{position: absolute;top: -2px;right:-30px;cursor: pointer;}
.sheet-list input{height: 28px;line-height: 28px;}
.sheet-list textarea{border: 1px solid #CFCFCF;padding: 5px;vertical-align: bottom;}
.sheet-list .layui-form-radio{margin: 0;}
.sheet-list .layui-form-radio i:hover,.sheet-list .layui-form-radioed i{color: #4BB1FF;}
input.blue-bot{border:none;border-radius:0;border-bottom: 1px solid #76C4FF;}
input.gray-bor{border: 1px solid #CFCFCF;}
.pho-describe{display: inline-block;vertical-align: top;margin-left: 10px;line-height: 24px;}
.institute-label{background: #4CC4BB;display: inline-block;color: #fff;height: 30px;line-height: 30px;padding: 0 8px;margin-right: 20px;margin-bottom:5px;margin-top:5px;position: relative;border-radius: 3px;}
.institute-label.blue-label{background: #4A90E2;border-radius: 3px;cursor: pointer;}
.institute-label .icon_cancel{position: absolute;top: -22px;right:-10px;cursor: pointer;}
.institute-sel{width: 845px;padding: 0;background: none;}
.institute-sel .institute-list{padding: 26px 30px 16px 30px;background:#fff;margin-bottom: 10px;border: none;position: relative;}
.institute-sel .institute-list input{position: absolute;top: 10px;right: 12px;}
.institute-sel .institute-list.current{border: 1px solid #DCDCDB;background: #FFFBE2;}

/*分院样式*/
/*到捡操作*/
.phy-infor p.phy-left{width:33%; margin-right:0;}
.sheet-notes span{margin-left:180px;}
.sheet-notes p{margin-left:180px;}
/*系统设置*/
.date-setting{border: 1px solid #eee;padding:20px; width:97%;overflow: hidden}
.date-setting li{border: 1px solid #00b3ff;height: 40px; line-height: 40px; width: 90px; text-align: center; color: #00b3ff; float: left; margin:0 20px 10px  0;}
.date-setting li:hover,.date-setting li.cohover{ background:#00b3ff; color: #fff; }
.location-txt { color:#459dff; padding: 13px 0;display:inline-block; float:left}
/*今日检查*/
.phy-info{border: 1px solid #E3E3E3;position: absolute;top: 50px;bottom: 10px;left: 10px;right: 10px;overflow: auto;}	
.phy-info-con{position: absolute;width: 100%;bottom: 80px;top: 0;overflow: auto;}    	
.phy-info-list:nth-child(2n+1){background: #EFEFEF;}
.phy-info-list:nth-child(2n){background: #FFFFFF;}
.phy-info-list{height: 50px;border-bottom: 1px solid #E6E6E6;padding-left: 60px;position: relative;cursor: pointer;}
.phy-info-list .vertical-centre{height: 50px;}
.phy-info-list .queue-mark{position: absolute;top:0;left:0;display:inline-block;width:48px;height:51px;font-size:11px;color:#fff;padding:3px 0 0 4px;background: url(../images/physical/queue_bg.png)no-repeat;background-size: 48px 51px;}
.phy-info-list.current{background: #00E08E;}
.phy-info-list.current .queue-num,.phy-info-list.current .queue-name,.phy-info-list.current .queue-info{color: #fff;}
.queue-mark.unqueue{background: url(../images/physical/queue_bg2.png)no-repeat;color: #333333;}
.queue-num{color: #4bb1ff;vertical-align: middle;}
.queue-name{color: #4bb1ff;font-size: 18px;font-weight: bold;vertical-align: middle;margin: 0 20px;width: 55px; display: inline-block;}
.queue-info{display: inline-block;vertical-align: middle;color: #666666;}
.phy-target .sheet-list{}
.phy-target .sheet-list .sheet-left{min-width: 250px;margin-right: 10px;color: #333333;}
.target-mark{background: #00C87F;color: #fff;padding: 10px;border-radius: 5px;display: inline-block;margin: 10px 0 10px 260px;position: relative;font-size: 12px;}
.target-mark .triangle-top{width: 0;height: 0;border-left: 7px solid transparent;border-bottom: 12px solid #1EC981;border-right: 7px solid transparent;position: absolute;left:40px;top:-10px;}
.obj-list{background: #fff;padding: 20px 24px;border-radius: 8px;position: relative;}
.obj-list .sel-img{position: absolute;top: -15px;right: -15px;cursor: pointer;}
.obj-list .table-tit{background: #4BB1FF;color: #fff;text-align: center;}
.obj-list .local-txt{margin-top: 15px;color: #333333;}
.obj-list .local-txt select{width: 160px;height: 26px;line-height: 26px;background: #F8F8F8;border: none;}
.obj-list.current{background: #4BB1FF;}
.obj-list.current .table-tit{background: #0071C8;}
.obj-list.current tr:nth-child(2n){color: #fff;}
.obj-list.current .local-txt{color: #fff;}
.obj-list .layui-table td{border: none;}
.obj-list table td:last-child{border-left:1px solid #E6E6E6;}
.obj-list table{margin: 0;}
.obj-list table td,.obj-list table th{padding: 5px 15px;}
.obj-list table tr{cursor: pointer;}
.obj-list table tr:nth-child(2n+1){background: #F2F2F2;}
.sheet-pic{display: inline-block;width: 124px;height: 124px;position: relative;margin-right: 10px;}   
.sheet-pic i.close-icon{cursor: pointer;position: absolute;top: -8px;right: -8px;color: #FF4949;font-size: 30px;width: 18px;height: 20px;line-height: 20px;text-align: center;} 
/*日历增加图标*/
.cal-img{cursor:pointer;display: inline-block;width: 32px;position: relative;}
.cal-img span{border-radius:5px;position: absolute;right: -4px;bottom: 0;min-width: 15px;height: 15px;line-height:15px;text-align:center;font-size: 12px;color: #fff;padding:0 1px;}
.cal-img span.red-span{background:#E7614B;}
.cal-img span.oran-span{background:#FC8458;}
.cal-img i{position:absolute;top:0;left:0;}
.mark-span{background:#FF6C6C;top:-3px;right:-3px;  color: #fff;
  width: 20px;
  display: inline-block;
  height: 20px;
  line-height: 20px;
  border-radius: 20px;
}
.label-section{cursor:pointer;width:43%;height:24px;position:relative;display:inline-block;margin-right:4px;}
.askleave-note,.medicine-note,.birth-note ,.overdue-note1,.overdue-note2,.overdue-note3{width:100%;height: 24px;line-height: 24px;font-size: 12px;text-align: center;float: left;margin-left: 1px;border:1px solid #ccc;}
.askleave-note span,.medicine-note span,.birth-note span,.overdue-note1 span,.overdue-note2 span,.overdue-note3 span{display:inline-block;width:50%;font-size:12px;vertical-align:top;}
.askleave-note label{color:#E31E0B;}
.medicine-note label{color:#00ACD2;}
.birth-note label{color:#FF1BBA;}
.overdue-note1,.overdue-note2,.overdue-note3,.askleave-note,.medicine-note,.birth-note{font-size:0;overflow:hidden;cursor:pointer;}
.overdue-note1 .span-left,.askleave-note .span-left{background:#F596BE;color:#fff;}
.overdue-note2 .span-left,.medicine-note .span-left{background:#00ACD2;color:#fff;}
.overdue-note3 .span-left,.birth-note .span-left{background:#FFB957;color:#fff;}
.overdue-note1 .span-right,.overdue-note2 .span-right,.overdue-note3 .span-right{vertical-align:top;background:#dddddd;color:#666;}
.divremind>div{margin-left:2px;overflow:hidden;cursor:pointer;position:relative;}
.divremind .label-btn{position:absolute;right:-6px;top:-6px;}
.lbnum{font-weight:bold;}
/*查看报告详情*/
.phyresult-txt{border: 1px solid #CACACA;box-shadow: 9px 10px 0 #D7D7D7;padding:5px 20px 20px 20px;}
.phyresult-txt h6{margin-top: 15px;}
.phyresult-tit{font-size: 16px;color: #333333;font-weight: bold;text-align: center;margin-bottom: 20px;}
.phy-tab-img img{margin: 0 5px;}
.phyresult .obj-list{padding: 0;margin-bottom: 15px;}
.phyresult .obj-list .table-tit{background: #A8DAFD;}
.phyresult .obj-list table tbody tr{border-bottom: 1px solid #ECECEC;}
.phyresult .obj-list table tbody tr:first-child{border-bottom: 1px solid #A0A0A0;color: #333333;}
.phyresult .obj-list table tbody{color: #999999;}
.phyresult .obj-list table th{}
.phyresult .obj-list table td,.phyresult .obj-list table th{padding: 15px 30px;text-align: center;border: none;word-break: break-all;min-width: 30px;}
.phyresult .obj-list table td:last-child{}
.phyresult .obj-list table tr:nth-child(2n+1){background: #F7F7F7;}
.phyresult .obj-list .local-txt{background: #F7F7F7;margin-top: 0;font-size: 16px;padding: 24px 30px;}
.phyresult .obj-list .local-txt .phy-place{display: inline-block;width: 50%;}
.phyresult .obj-list .local-txt .phy-name{display: inline-block;font-weight: bold;text-align: right;width: 50%;vertical-align: top;}
.phyresult-txt.phyresult-usual{padding: 0;text-align: center;padding-top: 20px;}
/*日历页*/
.total-con{text-align: center;}
.total-attend{width: 132px;height: 132px;border: 2px solid #ffffff;display: inline-block;margin: 16px auto;border-radius: 50%;background: #34c97d;position: relative;}
.total-attend:before{content: "";border: 9px solid #dbeae2;display: inline-block;width: 136px;height: 136px;position: absolute;top: -11px;left: -11px;border-radius: 50%;}
.total-attend .total-title{font-size: 14px;color: #ffffff;min-width: 54px;border: 1px solid #ffffff;border-radius: 20px;margin-top: 18px;display: inline-block;padding: 2px 8px;}
.total-attend .total-per{font-size: 36px;color: #ffffff;font-weight: bold;border-bottom: 1px solid #ffffff;width: 75%;margin: 0 auto;}
.total-attend .total-num{font-size: 16px;color: #ffffff;margin-top: 3px;}
.legend-label{font-size: 12px;color: #666666;text-align: right;margin: 5px 0;}
.legend-label i{display: inline-block;width: 8px;height: 8px;border-radius: 2px;margin-right: 5px;}
.total-txt{color: #ffffff;padding: 6px 8px;line-height: 24px;}
table.appo-table{border: 1px solid #e7e6d4;text-align: center;background: #f9f8ea;}
table.appo-table th{color: #6a5f5b;height: 35px;text-align: left;}
table.appo-table td{height: 30px;}
table.appo-table th,table td{padding: 0 8px;}
table.appo-table tr:nth-child(2){background: #eeebbc;}
table.appo-table tr.orangetxt{color: #f2914a;}
table.appo-table tr.bluetxt{color: #00acd2;}

/*设置*/
.setColor u{top:0;right:0;display:none;}
.setColor li{float: left;width: 70px;height: 70px;margin-right:15px;position: relative; cursor: pointer;}
.setColor .bg_gray{background: #363f46;}
.setColor .bg_blue{background: #2c99de;}
.setColor .bg_green{background: #05a9a0;}
.setColor .bg_orange{background: #ff761b;}
.setColor .bg_purple{background: #6777d7;}
.setColor .bg_red{background: #de575d;}
.setColor .bg_lightGreen{background: #6eb92b;}
.setColor .bg_pink{background: #e79bb9;}

/*采购统计*/
.count-list{height: 68px;color: #ffffff;}
.count-list h5{font-size: 16px;color: #ffffff;margin-bottom: 10px;border-bottom: 1px solid #ddd; text-align: center; line-height: 40px;font-weight: bold;}
.count-numcell{display: inline-block;vertical-align: middle;padding:0 15px;font-size: 16px;}
.count-numcell i{font-size: 36px;}
.model-cell{padding: 6px;cursor: pointer;position: relative;}
.model-cell.current{background: #f4f6f8;}
.model-cell img{vertical-align: top;display: inline-block;margin: 1px 3px;}
.model-cell:nth-last-child(1){margin-left: 6px;}
.model-cell:nth-last-child(1):before{content: "";border-left: 1px dashed #dee1e5;width: 1px;height: 16px;position: absolute;left: -5px;top: 7px;}
.type-cell{background: #eaedf1;color: #7c8196;font-size: 12px;padding: 2px 8px;cursor: pointer;}
.type-cell.current{background: #279bff;color: #ffffff;}
.count-list .icon_arrow_goup:after,.count-list .icon_arrow_decline:after{color: #ffffff;}
.park-cell{background: #ffffff;width: 256px;position: absolute;top: 0;bottom: 0;right: 0;color: #7c8196;box-shadow: 10px 17px 12px 10px #dddddd;}
.park-cell h5{background: #edf7ff;border: 1px solid #d9ebfa;padding: 14px 10px;margin: 8px 6px;border-radius: 5px;font-size: 16px;}
.park-cell h5 img{margin: 2px 2px 0 0;display: inline-block;vertical-align: top;}
.park-cell label{color: #279bff;margin-right: 8px;}
.park-cell p{display: inline-block;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;width: 140px;vertical-align: middle;}
.park-cell ul{margin: 0 10px 0 25px;position: absolute;top: 60px;bottom: 0;left: 0;right: 0;overflow-y: auto;}
.park-cell ul li{margin: 7px 0;}
.point-cell{position: absolute;width: 550px;background: #ffffff;padding: 11px 15px;border: 1px solid #bbbbbb;border-radius: 5px;}
.point-cell .count-list h5{margin-bottom: 8px;}
.point-cell .count-list h5 img{display: inline-block;vertical-align: top;margin: 1px 5px 0 0;}
.point-cell .count-list{height: 55px;padding: 12px 15px 8px 15px;}
.point-cell .count-numcell i{font-size: 24px;}
.point-cell .triangle-mark{z-index: 9;border-right: 6px solid red;left: 50%;margin-left: -6px;
    transform: rotate(-90deg);
    -ms-transform: rotate(-90deg);
    -moz-transform: rotate(-90deg);
    -webkit-transform: rotate(-90deg);
    -o-transform: rotate(-90deg);
}
.point-text{margin-top: 10px;}
.point-text .iconfont{display: inline-block;vertical-align: middle;margin-right: 5px;}
.point-text .iconfont:after{color: #b1bbc4;}
.point-text p{margin: 3px 0;}
.point-text{color: #7c8196;font-size: 12px;}

/*添加教职工信息*/
.staffme{min-width: 780px;}
.staffme .addbill{margin: 0 30px 20px 30px;padding: 20px 0 30px 0;}
.staffme .addbill ul{margin: 0;width: auto;color: #444;}
.staffme .addbill input[type="text"],.staffme .addbill select{border: 1px solid #e8e8e8;height: 30px;line-height: 30px;vertical-align: middle;}
.staffme .addbill select{width: 285px;height: 30px;line-height: 30px;border-radius: 0;}
.staffme .addbill textarea{border: 1px solid #e8e8e8;min-height: 54px;vertical-align: top;width: 277px;border-radius: 0;}
.staffme .addbill label img{vertical-align: top;margin-top: 5px;margin-left: -25px}
.line-cut{border-top:1px dashed #ddd;height: 1px;text-align: center;margin: 20px 0 30px 0;}
.line-cut span{background: #fff;margin-top: -10px;display: inline-block;padding: 0 10px;color: #999;}
.staffme .addbill ul li{height: inherit;padding: 7px 0;font-size: 0; min-height: auto;width: 45%;display: inline-block;}
.staffme .addbill ul li em{font-style: normal;}
.staffme .addbill ul li span{width: auto;font-size: 14px;text-align: left;color: #999999;}
.staffme .addbill ul li label{display: inline-block;font-size: 14px;}
.staffme .addbill ul li .idcard{margin-right:50px;vertical-align:top;display: inline-block;position: relative;width: 214px;height: 128px;}
.staffme .addbill ul li .idcard img{width: 214px;height: 128px;}
.staffme .addbill ul li i{font-style: normal;line-height: normal;}
.staffme .layui-form-item .layui-input-block{min-height: auto;margin-left: 138px;}
.staffme .layui-form-item .layui-form-label{width: 120px;color: #999999;}
.staffme .layui-form-item{margin-bottom: 9px}
.img-addcard{position: absolute;top: 50%;left: 50%;margin-top: -33px;margin-left: -33px;cursor: pointer;}
.idcard p{text-align: center;color: #49a6f4;margin-top: 10px;font-size: 14px;}
.opr-btn{cursor: pointer;}
.close-btn{cursor: pointer;background: rgba(6,6,6,0.5);top: 0;right: 0;font-size: 25px;position: absolute;top: 0;right: 0;width: 30px;height: 30px;line-height: 30px;color: #fff;text-align: center;display: inline-block;}
.select-inp+.select-inp{margin-left: 15px}
.select-inp input{margin-right: 3px}
.upload-sub{position: absolute!important;left: 50%;top: 50%;width: 50px;height: 50px;margin: -25px;}
.upload-sub .webuploader-pick{background: url(../image/addcard.png)no-repeat;width: 50px;height: 50px;padding: 0;background-size: 100%;position: absolute;top: 0;left: 0;margin: 0;}
/*查看对象*/
.daycare-txt{
height: 20px;
font-size: 14px;
color: #999999;
line-height: 20px; margin: 20px 0 20px 0;}
/*编辑老师*/
.ediper{margin: 20px;min-width: 960px;}
.ediper .staff-sear input{border-radius:0 ;border: 1px solid #C3C3C3;}
.ediper .edi-btn{float: right;margin-top: 15px;}
/*过敏管理*/
.alle-tab td{border: 1px solid #DEE7E6;text-align: center;}
.alle-tab{margin: 10px;}
.alle1{color: #FED144;}
.alle2{color: #FE980F}
.alle3{color: #FF0302;}
.alle-tab img{margin: 0 3px;margin-top: 5px;}
.alle-tab .check-label{margin:auto 10px;color: #A5A5A5;}
.check-state {margin:0 10px ;}
.check-state .tab-btn{color: #454545;}
.check-state .sel-label{float: right;}
.check-state .sel-label i{color: #1CA89D;}
.check-state .sel-label a{margin-left:15px;}
.pass-txt{color: #4CB0A8 !important;}
.unpass-txt{color: #EC563E !important;}


/*通知公告查看详情新版*/
.view-notifications{background: rgba(74,144,226,0.1); padding:10px 20px;}
.view-notifications h3{font-size: 14px;color: #333333;line-height: 30px; font-weight: bold;}
.view-notifictxt{color: #666666; display:block; margin-right: 10px;line-height: 30px;font-size: 12px; }
.view-notifictxt span{margin-right: 20px;}
.view-notifications-list{padding:0 20px;}
.view-notifications-list p{font-size: 14px; text-indent: 2em;color: #333333;line-height: 25px; margin-top: 10px;}
.unit-txt{display: inline-block;font-size: 12px;color: #999999;line-height: 30px; border-top: 1px solid #E1E3E5;  margin: 20px 0;}
.annex-txt{font-size: 14px;color: #333333;}
/*查看报名情况*/
.registration-div{ background: url(../images/verify/check-ico21.png);
    background-size: auto;
  background-size: 100% 100%;
  height: 115px;}
/*教职工管理*/
.bring_class {
    background: #ffffff none repeat scroll 0 0;
    border: 1px solid #EBEBEB;
    box-shadow: 0 0 15px #EBEBEB;
    right: 20px;
    position: absolute;
    top: 109px;
    z-index: 1000;
	padding: 20px;
	width: 295px;
}

.bring_class label {
    float: left;
    padding: 10px 0 0 0;
    width: 50%;
}
.duihao{position: absolute;bottom: 0px;bottom: -8px;
left: 1px; }
.duihaotxt{position: relative; }
.n-msg{color: red;}
.noti-content p.sourcehg{height: 111px;}
.hegit{ margin-top: 60px;}