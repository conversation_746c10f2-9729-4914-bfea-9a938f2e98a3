﻿<!DOCTYPE html>
<html>
<head>
    <title>查看上报活动详情</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <link rel="stylesheet" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../css/style.css"/>
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <!--[if lt IE 9]>
    <script src='../../sys/html5shiv.min.js'></script>
    <![endif]-->
    <style>
        .div_btn {
            position: fixed;
            bottom: 25px;
        }
        .incr-chat{margin: 20px;}
        .incr-chat .layui-form-label{width: 176px}
        .incr-chat .layui-form-item{margin-bottom: 10px;}
        .rep-list-con{padding: 9px 15px 9px 0;}
        .report-con .layui-form-item{margin-bottom: 0;}
        .report-con .layui-form-item .layui-input-block{min-height: 20px;}
        .layui-mark-red{color:red;}
    </style>
</head>
<body>
<!--按月-->
<section class="incr-chat">
    <script id="tplJoin" type="text/html">
        <div class="site-text site-block report-con">
            <div class="layui-form">
                <div class="layui-form-item">
                    <label class="layui-form-label"><i class="layui-mark-red">*</i>选择上报类型：</label>
                    <div class="layui-input-inline rep-list-con" style="width: 200px;">
                        <p>{{d.reporttypename}}</p>
                    </div>
                    <span class="layui-form-mid layui-word-aux">（共需上报<i class="red">{{d.reportTablecount}}</i>个表）</span>
                </div>
                <div class="layui-form-item" style="display:none;">
                    <label class="layui-form-label"><i class="layui-mark-red">*</i>选择上报频次：</label>
                    <div class="layui-input-block rep-list-con" >
                        <p>按{{d.ratename}}</p>
                    </div>
                    {{# if(d.rate !='second'){ }}
                        <div class="layui-input-block" style="margin-left: 206px;padding-bottom: 10px;">
                            <div class="layui-input-inline" style="width: 200px;padding-right: 15px;">
                                <p style="">{{d.endtypename}}{{d.enddays}}天</p>
                            </div>
                            <span class="layui-form-mid layui-word-aux" style="padding: 0!important;">（每到{{d.endtypename}}<i class="red">{{d.enddays}}</i>天为上报截止日期）</span>
                        </div>
                    {{# } }}
                </div>
                <div class="layui-form-item" style="display:none;">
                    <label class="layui-form-label"><i class="layui-mark-red">*</i>上报是否有截止时间：</label>
                    <div class="layui-input-inline rep-list-con" style="width: 200px;">
                        {{# if(d.isend==1){ }}
                            <p>是</p>
                        {{# } else { }}
                            <p>否</p>
                        {{# } }}
                    </div>
                    {{# if(d.isend==1 && d.rate!="second"){ }}
                        <span class="layui-form-mid layui-word-aux">（共需上报{{d.reportcount}}次，上报截止时间为{{d.endtime}}）</span>
                    {{# } else { }}
                    {{# } }}
                </div>
                {{# if(d.isend==1 && d.rate!="second"){ }}
                    <div class="layui-form-item">
                        <label class="layui-form-label"><i class="layui-mark-red">*</i>选择上报截止日期：</label>
                        <div class="layui-input-inline rep-list-con" style="width: 200px;">
                            <p>{{d.endtime}}</p>
                        </div>
                    </div>
                {{# } else { }}
                {{# } }}
                <!-- 上报按次显示截止时间-->
                {{# if(d.rate=="second"){ }}
                    <div class="layui-form-item">
                        <label class="layui-form-label"><i class="layui-mark-red">*</i>选择上报截止时间：</label>
                        <div class="layui-input-inline rep-list-con" style="width: 200px;">
                            <p>{{d.endtime}}</p>
                        </div>
                    </div>
                {{# } else { }}
                {{# } }}
                <div class="layui-form-item">
                    <label class="layui-form-label"><i class="layui-mark-red">*</i>上报名称：</label>
                    <div class="layui-input-inline rep-list-con" style="width: 200px;">
                        <p>{{d.reportname}}</p>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><i class="layui-mark-red">*</i>选择上报的园所范围：</label>
                    <div class="layui-input-inline rep-list-con" style="width: 200px;">
                        <p>{{d.yeytypename}}、{{d.classificationname}}</p>
                    </div>
                    <!--<span class="layui-form-mid layui-word-aux">（已关联此类别的园所<i class="red">{{d.yeycount}}</i>园）</span>-->
                </div>
            </div>
        </div>
    </script>
</section>
<div class="div_btn" style="text-align: center;width:100%;">
    <button id="btn_close" class="layui-btn">关闭</button>
<!--    <button id="btn_recovery" class="layui-btn">提前终止上报（回收站）</button>-->
</div>
<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/system.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../js/citysb/reportsbdetail.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>
