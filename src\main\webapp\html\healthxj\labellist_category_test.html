<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>标签管理 - Category字段功能测试</title>
    <link rel="stylesheet" href="../../css/reset.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <style type="text/css">
        html, body {
            background: #fff;
            overflow: auto;
        }
        .layui-form-label {
            width: 120px;
        }
        .layui-input-block {
            margin-left: 150px;
        }
		.layui-form .layui-form-item{ margin-bottom: 15px;}
		.form-display .layui-form-checkbox i {margin: 2px 0;}
		.layui-form-item .layui-form-checkbox[lay-skin="primary"] {height: 37px !important; padding-top: 10px; margin-top: 0;}
        
        .test-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            z-index: 9999;
            max-width: 350px;
        }
        
        .test-panel h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 14px;
        }
        
        .test-panel button {
            display: block;
            width: 100%;
            margin: 5px 0;
            padding: 8px;
            background: #16b777;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .test-panel button:hover {
            background: #14a06b;
        }
        
        .test-panel .status {
            font-size: 12px;
            color: #666;
            margin: 5px 0;
        }
        
        .test-panel .category-test {
            background: #1E9FFF;
        }
        
        .test-panel .category-test:hover {
            background: #1890ff;
        }
    </style>
</head>
<body>
    <div class="test-panel">
        <h3>🧪 Category字段功能测试</h3>
        <button onclick="fillCategoryTestData('hw')">测试身高体重分类</button>
        <button onclick="fillCategoryTestData('eye')">测试眼科分类</button>
        <button onclick="fillCategoryTestData('matin')">测试血红蛋白分类</button>
        <button onclick="fillCategoryTestData('general')">测试通用分类</button>
        <button onclick="validateCategoryField()">验证Category字段</button>
        <button onclick="showFormData()">显示完整表单数据</button>
        <button onclick="clearForm()">清空表单</button>
        <div class="status" id="testStatus">等待测试...</div>
    </div>

    <div class="marmain" style="min-width: 800px; padding-right: 370px;">
        <div class="content-medical">
            <h2 style="padding: 20px; margin: 0; border-bottom: 1px solid #eee;">标签管理 - Category字段功能测试</h2>
            <form class="layui-form form-display" lay-filter="formMain" style="padding: 20px;">
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>标签名称：</label>
                    <div class="layui-input-block">
                        <input type="text" name="labelname" required lay-verify="required" placeholder="请输入" autocomplete="off" class="layui-input" maxlength="50">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标签描述：</label>
                    <div class="layui-input-block">
                        <input type="text" name="labelremark" required lay-verify="required" placeholder="请输入" autocomplete="off" class="layui-input" maxlength="150">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>标签分类：</label>
                    <div class="layui-input-block">
                        <select name="category" lay-verify="required">
                            <option value="">请选择标签分类</option>
                            <option value="hw">身高体重</option>
                            <option value="eye">眼科</option>
                            <option value="listen">听力</option>
                            <option value="matin">血红蛋白</option>
                            <option value="car">口腔</option>
                            <option value="xf">胸部腹部</option>
                            <option value="neike">内科检查</option>
                            <option value="general">通用</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>算法类型：</label>
                    <div class="layui-input-block">
                        <select name="algorithm_type" lay-verify="required">
                            <option value="">请选择算法类型</option>
                            <option value="EXPRESSION">EXPRESSION</option>
                            <option value="SCRIPT">SCRIPT</option>
                            <option value="RULE">RULE</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">算法代码：</label>
                    <div class="layui-input-block">
                        <textarea name="algorithm_content" placeholder="请输入算法代码" class="layui-textarea" style="min-height: 120px;"></textarea>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">是否活动：</label>
                    <div class="layui-input-block">
                        <input type="checkbox" name="is_active" value="1" title="活动" lay-skin="primary">
                    </div>
                </div>
                
                <div class="layui-form-item" style="margin-top: 30px;">
                    <div class="layui-input-block">
                        <button type="button" class="layui-btn" onclick="testSave()">测试保存</button>
                        <button type="button" class="layui-btn layui-btn-primary" onclick="resetForm()">重置表单</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <script type="text/javascript">
        var v = top.version || '1.0';
        document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../plugin/xm-select/xm-select.js?v=" + v + "'><" + "/script>");
    </script>
    
    <script type="text/javascript">
        var form, layer;
        
        // 分类测试数据模板
        var categoryTestData = {
            'hw': {
                labelname: 'BMI超重标签',
                labelremark: '用于检测BMI超重的标签',
                algorithm_type: 'EXPRESSION',
                algorithm_content: '${bmi} > 24',
                is_active: true
            },
            'eye': {
                labelname: '视力低常标签',
                labelremark: '用于检测视力低常的标签',
                algorithm_type: 'EXPRESSION',
                algorithm_content: '${vision_left} < 0.8 || ${vision_right} < 0.8',
                is_active: true
            },
            'matin': {
                labelname: '贫血检测标签',
                labelremark: '用于检测血红蛋白偏低的标签',
                algorithm_type: 'EXPRESSION',
                algorithm_content: '${hemoglobin} < 110',
                is_active: true
            },
            'general': {
                labelname: '通用健康标签',
                labelremark: '通用的健康评估标签',
                algorithm_type: 'RULE',
                algorithm_content: 'if (健康状况 == "良好") return true;',
                is_active: false
            }
        };
        
        layui.use(['form', 'layer'], function(){
            form = layui.form;
            layer = layui.layer;
            
            updateStatus('✅ Layui 加载完成，Category字段测试就绪');
        });
        
        function updateStatus(message) {
            document.getElementById('testStatus').innerHTML = message;
        }
        
        function fillCategoryTestData(category) {
            var testData = categoryTestData[category];
            if (testData) {
                testData.category = category;
                form.val('formMain', testData);
                updateStatus('✅ 已填充 ' + getCategoryName(category) + ' 测试数据');
            } else {
                updateStatus('❌ 未找到分类测试数据: ' + category);
            }
        }
        
        function getCategoryName(category) {
            var categoryMap = {
                'hw': '身高体重',
                'eye': '眼科',
                'listen': '听力',
                'matin': '血红蛋白',
                'car': '口腔',
                'xf': '胸部腹部',
                'neike': '内科检查',
                'general': '通用'
            };
            return categoryMap[category] || category;
        }
        
        function validateCategoryField() {
            var data = form.val('formMain');
            var errors = [];
            
            if (!data.labelname) errors.push('标签名称不能为空');
            if (!data.category) errors.push('标签分类不能为空');
            if (!data.algorithm_type) errors.push('算法类型不能为空');
            
            if (errors.length > 0) {
                updateStatus('❌ 验证失败: ' + errors.join(', '));
                layer.msg('验证失败: ' + errors.join(', '));
            } else {
                updateStatus('✅ Category字段验证通过');
                layer.msg('Category字段验证通过！分类: ' + getCategoryName(data.category));
            }
        }
        
        function showFormData() {
            var data = form.val('formMain');
            // 处理复选框值
            data.is_active = data.is_active ? 1 : 0;
            
            console.log('=== Category字段测试数据 ===');
            console.log('原始数据:', form.val('formMain'));
            console.log('处理后数据:', data);
            console.log('Category字段值:', data.category);
            console.log('Category中文名:', getCategoryName(data.category));
            console.log('JSON字符串:', JSON.stringify(data, null, 2));
            
            var displayData = Object.assign({}, data);
            displayData.category_display = getCategoryName(data.category);
            
            layer.open({
                type: 1,
                title: 'Category字段测试数据',
                area: ['700px', '500px'],
                content: '<div style="padding:20px;"><h3>表单数据:</h3><pre>' + JSON.stringify(displayData, null, 2) + '</pre></div>'
            });
            
            updateStatus('📊 Category数据已显示 - 分类: ' + getCategoryName(data.category));
        }
        
        function testSave() {
            var data = form.val('formMain');
            
            if (!data.labelname) {
                layer.msg('请输入标签名称');
                return;
            }
            if (!data.category) {
                layer.msg('请选择标签分类');
                return;
            }
            if (!data.algorithm_type) {
                layer.msg('请选择算法类型');
                return;
            }
            
            // 处理复选框值
            data.is_active = data.is_active ? 1 : 0;
            
            console.log('=== Category字段保存测试 ===');
            console.log('提交数据:', data);
            console.log('Category字段:', data.category, '(' + getCategoryName(data.category) + ')');
            console.log('JSON字符串:', JSON.stringify(data));
            
            layer.msg('Category字段保存测试成功！分类: ' + getCategoryName(data.category));
            updateStatus('💾 Category保存测试完成 - ' + getCategoryName(data.category));
        }
        
        function clearForm() {
            form.val('formMain', {
                labelname: '',
                labelremark: '',
                category: '',
                algorithm_type: '',
                algorithm_content: '',
                is_active: false
            });
            updateStatus('🗑️ 表单已清空');
        }
        
        function resetForm() {
            document.querySelector('form').reset();
            form.render();
            updateStatus('🔄 表单已重置');
        }
    </script>
</body>
</html>
