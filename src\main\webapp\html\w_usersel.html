﻿<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8" />
	<title>选择人员</title>
	<link rel="stylesheet" href="../css/reset.css" />
	<link rel="stylesheet" href="../css/style.css" />
	<link rel="stylesheet" href="../plugin/DataTables/media/css/jquery.dataTables.css" />
	<script type="text/javascript">
		document.write('<link rel="stylesheet" id="linktheme" href="../css/'+parent.objdata.curtheme+'.css" type="text/css" media="screen"/>');
	</script>
	<!--[if lt IE 9]>
	    <script src="../sys/html5shiv.min.js"></script>     
	<![endif]-->
	<style type="text/css">
		.select{
			background:#ddd;
		}
		.dataTables_wrapper .dataTables_paginate .paginate_button {
		  /* min-width: 1.5em; */
		   padding: 1px;
		}
	</style>
</head>
<body>
	<section class="font14 bg6">
		<div class="clearfix pd10-20">
			<div class="fl placeChoose">
				<div>
					<input type="text" placeholder="请输入搜索内容" id="txtsearch">&emsp;<u id="btnsearch" class="skinIcon3 pointer"></u>
				</div>
				<div class="mtop15 ztree" id="divtree" style="overflow: auto;height:370px;width:465px;">
					<div class="rangeTable">
					<table cellpadding="0" cellspacing="0" border="0" class="display" id="tbusers" style="height:100%;">
						<thead>
							<tr>
								<th style="width: 15px"><input type="checkbox" id='checkAll' /></th>
								<th>姓名</th>
								<th>单位</th>
								<th>手机</th>
							</tr>
						</thead>
						<tbody>
							<tr>
								<td colspan="3" style="text-align:center;height:120px;"></td>
							</tr>
						</tbody>
					</table>
				</div>
				</div>
			</div>
			<div class="fl placeBtns">
				<a id="btnadd" class="btn-black" style="visibility: hidden;">添加&gt;&gt;</a>
				<a id="btndel" class="btn-black">&lt;&lt;删除</a>
			</div>
			<ul class="fl placeResult" id="userresult">
				
			</ul>
		</div>
		<div class="center" style="display:none;">
			<a id="btnok" class="btn-black">确定</a>
			<a id="btnclose" class="btn-black">取消</a>
		</div>
	</section>
	<script data-main="../js/w_usersel.js" src='../sys/require.min.js'></script>
</body>
</html>
