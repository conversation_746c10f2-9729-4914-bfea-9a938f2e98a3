layui.config({
    base: './js/'
}).extend({ //设定模块别名
    promise: '../../plugin/promise/promise'
});
var objdata = {
    myid : parent.objdata.my.id,
    myname : parent.objdata.my.username,
    zIcon : {
        area : "../images/noti_area.png",
        yey : "../images/noti_yey.png",
        doc : "../images/noti_doc.png",
        director : "../images/noti_director.png",
        colleague : "../images/noti_director.png"
    }
    , objroletypename: {"e": "区县妇幼保健院", "ehos": "基层卫生服务机构", "ehosin": "入托体检机构", "ehosother": "其他体检机构", "eyey": "幼儿园", "echild": "托育机构", "echyey": "托幼一体园"}
    , getdatafromypt: 0//读取存储在市级的通知
};
var outCon = $("#container"),
    objSelectUser = {}, //选择的发送人信息
    noticeObjdata = parent.$("#win_" + Arg("mid")).find("iframe")[0].contentWindow.objdata,
    ossBaseUrl = ossPrefix,
    noticeList = Arg("noticelist"),
    attacheIcon = {
        baseUrl : "../images/",
        png : "txt_img1.png",
        jpg : "txt_img1.png",
        doc : "txt_img2.png",
        docx : "txt_img2.png",
        zip : "txt_img3.png",
        rar: "txt_img3.png",
        txt : "txt_img5.png",
        xlsx : "txt_img6.png",
        xls : "txt_img6.png",
        pdf : "txt_img7.png",
        ppt : "txt_img10.png"
    };
//定义模板
var templete = {
//    详情头部
//     headTem :   '<h3 class="heightnum" style="text-align: center;">{{title}}</h3>' +
//                 '<div class="layui-row detail-tit">' +
//                     '<div class="layui-col-xs6 layui-col-sm6 layui-col-md6" style="padding-left: 30px;">' +
//                         '<p>发布人：<span>{{userName}}</span></p>' +
//                         '<p>发布日期：<span>{{publishtime}}</span></p>' +
//                         '<p style="display: {{enrollNumShow}}">允许报名</p>' +
//                     '</div>' +
//                     '<div class="layui-col-xs6 layui-col-sm6 layui-col-md6">' +
//                         '<p>群发人群：<span class="js-totype">（{{tousernum}}）</span></p>' +
//                         '<p>通知类型：<span>{{typename}}</span></p>' +
//                     '</div>' +
//                 '</div>',
    headTem :   '<div class="view-notifications">' +
        '<h3>{{title}}</h3>' +
        '<div class="view-notifictxt">' +
        '<div style="margin-right: 225px;line-height: 22px;"><span>发布人：{{fromname}}</span> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span>通知类型：{{typename}}</span> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{strisnprocess}}' +
        '{{strreceiveobj}}' +
        '{{noticeneedinfos}}</div>' +
        '<div style="position: absolute;right: 20px;top: 35px;"><span style="float: right; margin-right: 0;">发布日期：{{publishtime}}</span></div>' +
        '</div></div>',
    bodytem: '<div class="view-notifications-list">{{content}}<div class="unit-txt">发文单位：{{fromdepart}}</div></div>',
    //附件
    // attacheTem :'<dl class="layui-txt-add">' +
    //                 '<dt><img src="{{icon}}"></dt>' +
    //                 '<dd>' +
    //                     '<p class="layui-txt-tit">{{name}}<label>（{{size}}）</label></p>' +
    //                     '<a class="js-down-file" href="{{path}}" data-path="{{path}}" style="color: #1ca89f !important;">下载</a>' +
    //                 '</dd>' +
    //             '</dl>'
    attacheTem :'<div class="js-attache-list" style="padding: 0 20px;">\
                    <dl class="layui-txt-add">\
                        <dt><img src="../images/newicon/wenjian.png" style="width: 32px; margin-right: 5px;"></dt>\
                        <dd><p class="layui-txt-tit" style="line-height: 20px;">{{name}}<label>（{{size}}）</label></p>\
                        <a class="js-down-file" href="{{path}}" data-path="{{path}}" style="color: #4A90E2; !important;">下载</a></dd>\
                </dl></div>'
},
    /**
     * 替换模板数据 模板与数据中的变量名完全一致
     * @param template {String} 模板字符串
     * @param model {Object} 模板数据
     * @returns {*} {strHtml} 数据替换后的模板字符串
     */
    renderTemp = function (template, model) {
        var reg = /\{\{\w+\}\}/g;
        template = template.replace(reg,function (regStr) {
            var reg2 = /\{\{(\w+)\}\}/g,
                key = reg2.exec(regStr)[1];
            return model[key];
        });
        return template;
    };
layui.use(['promise','form'], function () {
    // noticeObjdata.curNoticePro.then(function (noticeList) {
    //     var noticeId = Arg("noticeId");
    //     for(var i = 0; i < noticeList.length; i++){
    //         var curNotice = noticeList[i];
    //         if(curNotice.id !== noticeId){
    //             continue;
    //         }
    //         curNotice.userName = objdata.myname;
    //         curNotice.unreadNum = curNotice.tousernum - curNotice.readernum;
    //         curNotice.typename = curNotice.typename;
    //         // curNotice.curTypeShow  = Arg("noticetype") === "sent" ? "block" : "none";
    //         outCon.prepend(renderTemp(templete.headTem, curNotice));
    //         showContent(noticeId);
    //         break;
    //     }
    // });
    if((Arg("comefrom") && Arg("comefrom") == 'ypt' && Arg("type") == 1) || Arg("type") == 2){
        objdata.getdatafromypt = 1;
    }
    showContent();
    initEvent();
});
function initEvent() {
//    下载附件
    outCon.off("click",".js-down-file").on("click",".js-down-file",function (ev) {
        var fileObj = {};
        fileObj.fileName = $(this).prev().get(0).childNodes[0].nodeValue;
        fileObj.ossKey = $(this).attr("data-path");
        downAttache(fileObj);
    });
}

/**
 * 获取通告内容
 */
function showContent() {
    new Promise(function (resolve, reject) {
        var arrsm = [["notice.selContent", Arg("noticeId")],["notice.selAttache", Arg("noticeId")]];
        if(objdata.getdatafromypt){//查看转发的原通知如果是市妇幼
            arrsm = [["notice.selContentFromYpt", Arg("noticeId")],["notice.selAttacheFromYpt", Arg("noticeId")]];
        }
        $.sm(function(re, err){
            if(!err && re){
                resolve(re);
            }else{
                parent.layer.msg("获取通知内容失败！")
            }
        }, arrsm);
    }).then(function (data) {
        var content = data[0][0],
            attaches = data[1];
        objdata.totype = content.totype;
        objdata.isnneedinfo = content.isnneedinfo;
        content.content = $.DecodeSpChar(content.content);
        content.unreadNum = content.tousernum - content.readernum;
        if(content.isnneedinfo == 1 && content.needinfos){
            var arrneed = [];
            var needinfos = JSON.parse(content.needinfos);//需要提交的资料
            for (var j in needinfos) {
                arrneed.push(needinfos[j]);
            }
            if(arrneed.length > 0){
                content.noticeneedinfos = '<br/><span>报名需要提交的资料：' + arrneed.join('，') + '</span>';
            }else{
                content.noticeneedinfos = '';
            }
        }else{
            content.noticeneedinfos = '';
        }
        if(Arg("noticetype") != "receive"){//接受的没有
            content.strisnprocess = '<span>是否自己审核：' + (content.isnprocess == 1 ? "是" : "否") + '</span>';
            var objroletype = objdata.objroletypename;
            var strreceiveobj = "";
            if(content.receiveobj){
                var receiveobj = JSON.parse(content.receiveobj || '{}');
                var arrsole = [];
                for (var k in receiveobj) {
                    var arrrolename = [];
                    var items = receiveobj[k];
                    strreceiveobj += objroletype[k];
                    for (var i = 0; i < items.length; i++) {
                        arrsole.push(items[i][0]);
                        arrrolename.push(items[i][1])
                    }
                    if (arrrolename.length > 0) {
                        strreceiveobj += "：(" + arrrolename.join(',') + ")，";
                    }
                }

            }
            content.strreceiveobj = strreceiveobj.length > 0 ? '<br/><span style="">群发对象：' + strreceiveobj.substring(0, strreceiveobj.length - 1) + '</span>' : "";
        }else{
            content.strisnprocess = "";
            content.strreceiveobj = "";
        }
        outCon.prepend(renderTemp(templete.headTem, content))
        outCon.append(renderTemp(templete.bodytem,content));
        if(attaches.length === 0){
            return;
        }
        var arrHtml = [];
        for(var i = 0; i < attaches.length; i++){
            var curAttache = attaches[i],
                ext = curAttache.name ? /\.\w+/.exec(curAttache.name)[0].replace(".","") : "";
            curAttache.icon = attacheIcon.baseUrl + (attacheIcon[ext] || attacheIcon.txt);
            if(!curAttache.path){
                continue;
            }
            curAttache.path = ossPrefix + curAttache.path;
            arrHtml.push(renderTemp(templete.attacheTem,attaches[i]));
        }
        if(arrHtml.length > 0){
            outCon.append('<div class="view-notifications"><p class="annex-txt"><img src="../images/newicon/fujian.png" style="width: 16px; margin-right: 5px;">附件' + arrHtml.length + '个</p></div>');
            outCon.append(arrHtml.join(""));
        }
    });
}
//获取接收通知人员列表
function getReadCount(noticeId) {
    new Promise(function (resolve, reject) {
       $.sm(function(re, err){
           if(!err && re){
               resolve(re[0]);
           }else{
               parent.layer.msg("发送消息失败")
           }
       },["notice.readCount",noticeId]);
    }).then(function (data) {
    //    已读园数
        var param1 = {} , arrHtml = [];
        param1.readNum = data.readyeynum;
        param1.totalNum = data.toorganoryeynum;
        param1.readUnit = "园";
        param1.text = "已读";
        param1.showOrHide = "inline-block";
        if(false){//totype === "1"
            param1.showOrHide = "none";
        }
        arrHtml.push(renderTemp(templete.circleTem,param1));
    //    已读人数
        param1.readNum = data.readernum;
        param1.totalNum = data.tousernum;
        param1.readUnit = "人";
        param1.showOrHide = "inline-block";
        arrHtml.push(renderTemp(templete.circleTem,param1));
    //    微信已读
        param1.text = "微信端已读";
        param1.readNum = data.wxreadnum;
        param1.totalNum = data.tousernum;
        arrHtml.push(renderTemp(templete.circleTem,param1));
    //    pc已读
        param1.text = "PC端已读";
        param1.readNum = data.pcreadnum;
        arrHtml.push(renderTemp(templete.circleTem,param1));
        var circleList = $("#read-detail").children().children();
        circleList.eq(0).html(arrHtml.slice(0,2));
        circleList.eq(1).html(arrHtml.slice(2));
        var $pars = $("#read-detail").find(".wrap");
        $pars.each(function (index,_this) {
            var num = Number($(_this).attr("data-num")),
                totalNum = Number($(_this).attr("data-total"));
            initStatisticsChart(num,totalNum,$(_this));
        })
    });
}
//报名人数、园数圆圈
function enrollCircle(readUsers) {
    new Promise(function (resolve, reject) {
        $.sm(function(re, err){
            if(!err && re){
                resolve(re[0]);
            }else{
                parent.layer.msg("发送消息失败")
            }
        },["notice.readCount",Arg("noticeId")]);
    }).then(function (data) {
        function getYeyNum(readUsers) {
            if( !readUsers.length) return 0;
            var tmp = {}, yeyNum = 0;
            for (var i = 0; i < readUsers.length; i++){
                var curYeyId = readUsers[i].yeyid;
                if(!tmp[curYeyId] && tmp[curYeyId] !== curYeyId && readUsers[i].enrollstatus === 1){
                    tmp[curYeyId] = curYeyId;
                    yeyNum ++;
                }
            }
            return yeyNum;
        }
        function getEnrollNum(readUsers) {
            if( !readUsers.length) return 0;
            var enrollNum = 0;
            for (var i = 0; i < readUsers.length; i++){
                if(readUsers[i].enrollstatus === 1){
                    enrollNum += readUsers[i].enrollnum;
                }
            }
            return enrollNum;
        }
        //    已报名园数
        var param1 = {} , arrHtml = [];
        param1.totalNum = data.toorganoryeynum;
        param1.readUnit = "园";
        param1.text = "已报";
        param1.showOrHide = "inline-block";
        if(false){//objdata.totype === "1"
            param1.showOrHide = "none";
        }else{
            param1.readNum = getYeyNum(readUsers);
        }
        arrHtml.push(renderTemp(templete.circleTem,param1));
        //    已报名人数
        param1.readNum = getEnrollNum(readUsers);
        param1.totalNum = param1.readNum;
        param1.readUnit = "人";
        param1.showOrHide = "inline-block";
        arrHtml.push(renderTemp(templete.circleTem,param1));
        $("#read-detail").find(".qr-enroll").eq(0).html(arrHtml.join(""));
        var $pars = $("#read-detail").find(".wrap");
        $pars.each(function (index,_this) {
            var num = Number($(_this).attr("data-num")),
                totalNum = Number($(_this).attr("data-total"));
            initStatisticsChart(num,totalNum,$(_this));
        })
    });
}
function initStatisticsChart(num, totalNum, $par) {
    var stop = totalNum === 0 ? 0 : (num / totalNum * 100).toFixed(2);
    if(stop > 50) {
        $par.find('.circle').addClass('clip-auto');
        $par.find('.right').removeClass('wth0');
    }
    $par.find('.left').css("-webkit-transform", "rotate(" + (18 / 5) * stop + "deg)");
    $par.find('.num>b').text(num);
}
//阅读通告的人员信息和接收通告的人员信息
function readUser() {
    return new Promise(function (resolve, reject) {
        $.sm(function(re, err){
            if(!err && re){
                resolve(re);
            }else{
                parent.layer.msg("获取阅读人员信息失败！")
            }
        },[["notice.getReadUser",Arg("noticeId")],["notice.getSentUser",Arg("noticeId")]]);
    }).then(function (data) {
        objdata.readUsers = data[0];
        objdata.sentUsers = data[1];
        enrollCircle(objdata.readUsers);
        if(objdata.readUsers.length === 1){
        //    只有一个阅读记录
            showFirstUser(objdata.readUsers[0]);
        }else if(objdata.readUsers.length > 1){
            var user0 = objdata.readUsers[0],
                ptime0 = (user0.pctime && + new Date(user0.pctime)) || +new Date(),
                wtime0 = (user0.wxtime && + new Date(user0.wxtime)) || +new Date(),
                etime0 = (user0.enrollstatus===1 && + new Date(user0.uenrolltime)) || +new Date(),
                hasEnroll = user0.enrollstatus===1,
                minTime1 = etime0,
                minTime0 = ptime0 > wtime0 ? wtime0 : ptime0,
                index = 0;
            for(var i = 1; i < objdata.readUsers.length; i++){
                var cur = objdata.readUsers[i],
                    ptime = (cur.pctime && + new Date(cur.pctime)) || +new Date(),
                    wtime = (cur.wxtime && + new Date(cur.wxtime)) || +new Date(),
                    etime = (cur.enrollstatus===1 && + new Date(cur.uenrolltime)) || +new Date(),
                    minTime = ptime > wtime ? wtime : ptime;
                if(objdata.showType === "readDetail"){
                    //阅读情况第一条记录
                    if(minTime < minTime0){
                        minTime0 = minTime;
                        index = i;
                    }
                }else{
                //    报名情况第一条记录
                    if(etime < minTime1){
                        hasEnroll = cur.enrollstatus===1,
                        minTime1 = etime;
                        index = i;
                    }
                }
            }
            objdata.showType === "readDetail" ? showFirstUser(objdata.readUsers[index]) : (hasEnroll ? showFirstUser(objdata.readUsers[index]) : null);
        }
    });
}
//展现第一条阅读记录
function showFirstUser(objUser) {
    var fromName = objUser.fromname;
    if(fromName.length > 11){
        fromName = fromName.substring(0,10) + "...";
    }
    if(objdata.showType === "readDetail"){
        var readTable = $("#read-detail").find(".qr-read"),
            pct = objUser.pctime ? (+new Date(objUser.pctime)) : +new Date(),
            wxt = objUser.wxtime ? (+new Date(objUser.wxtime)) : +new Date(),
            readTime = (pct < wxt ? objUser.pctime : objUser.wxtime),
            readPlam = (pct < wxt ? "../images/pc.png" : "../images/weixin.png");
        readTable.find("tr").eq(0).find("img").prop("src",readPlam).show();
        readTable.find("tr").eq(1).find("td").html(fromName + "<span style='float: right'>" + readTime + "</span>");
    }else if(objUser.enrollstatus === 1){
        var enrollTable = $("#read-detail").find(".qr-enroll").eq(1);
        enrollTable.find("tr").eq(0).find("span").text(fromName);
        enrollTable.find("tr").eq(1).find("span").text(objUser.uenrolltime);
    }
    //阅读人的单位信息
    objdata.firstUserM = objUser.mobile;
}
//发送人列表
function sendUserList() {
    var pro = new Promise(function (resolve, reject) {
        $.sm(function (re, err, obj) {
            if (!err && obj) {
                objdata.lingInfo = obj;
                resolve(obj);
            } else {
                parent.layer.msg("发送消息失败")
            }
        }, ["noticedetail.getnoticeread"]);//["index.getLinkMan"]
    }).then(function (obj) {
        var objYeyUser = groupData(obj.arryeyuser, "areacode"),
            objSameUser = groupData(obj.arruser, "areacode");
        if(false){//objdata.totype === "1"
            showSameUserTree(objSameUser);
        }else{
            showYeyUserZtree(objYeyUser);
        }
    });
}
//第一个阅读或报名的人展现情况
function firstReadOrEnroll(pNode, cNode) {
    var len = cNode.name.indexOf("（") > -1 ? cNode.name.indexOf("（") : cNode.name.length,
        fromName = cNode.name.substring(0,len);
    if(fromName.length > 11){
        fromName = fromName.substring(0,10) + "...";
    }
    if(objdata.showType === "readDetail"){
        $("#read-detail").find("tr").eq(2).show().find("td").text(pNode.name);
        var readTime = $("#read-detail").find("tr").eq(1).find("td").find("span");

        readTime.parent().html(fromName + '<span style="float: right">' + readTime.text() + '</span>');
    }else if(/已报\d+人/.test(cNode.name)){
        //第一个报名的人
        var enrollTable = $("#read-detail").find(".qr-enroll").eq(1);
        var trname = enrollTable.find("tr").eq(0).find("span");
        trname.text(fromName);
        var fromName = pNode.name;
        if(fromName.length > 11){
            fromName = fromName.substring(0,10) + "...";
        }
        enrollTable.find("tr").eq(2).find("span").text(fromName);
    }
}
//展现幼儿园用户树
function showYeyUserZtree(objYeyUser) {
//    获取幼儿园id去查询幼儿园名字
    var arrAreaCode = objYeyUser.arrKey,
        objData = objYeyUser.objData,
        arrYeyid = [], strWhere = {};
    //将按areacode分组后的数据按幼儿园id再次进行细分
    var finalYeyObj = {};
    for (var key in objData) {
        var tmpArr = objData[key];
        finalYeyObj[key] = groupData(tmpArr, "yeyid").objData;
    }
    for (var i = 0; i < arrAreaCode.length; i++) {
        var tmpCode = arrAreaCode[i], objUsers = objData[tmpCode] , yeyid, preYeyid;
        for(var j = 0; j < objUsers.length; j++){
            if(j === 0){
                yeyid = preYeyid = objUsers[0].yeyid;
                arrYeyid.push(yeyid);
            }else if(objUsers[j].yeyid !== preYeyid){
                yeyid = preYeyid = objUsers[j].yeyid;
                arrYeyid.push(yeyid);
            }
        }
    }
    if (arrYeyid.length === 1) {
        strWhere.id = [arrYeyid[0]];
    } else {
        strWhere.ids = $.msgpJoin(arrYeyid);
    }
    new Promise(function (resolve, reject) {
        $.sm(function (re, err) {
            if (!err && re) {
                resolve(re);
            } else {
                parent.layer.msg("发送消息失败")
            }
        }, [["notice.getYeyName", $.msgwhere(strWhere)],["notice.get2UserPhone",Arg("noticeId")]]);

    }).then(function (data) {
        var nodes = [],
            root = {open : true, icon : objdata.zIcon.area},
            yeyData = data[0],
            userPhonesData = data[1].join(",");
        for (var i = 0; i < arrAreaCode.length; i++) {
            if(!root.name){
                root.name = objCityCode[arrAreaCode[0].substring(0,6)];
                root.self_areaCode = arrAreaCode[0].substring(0,6);
                root.children = [];
            }
            var curAreaCode = arrAreaCode[i],
                curAreaUser = finalYeyObj[curAreaCode],
                name = getAreaName(curAreaCode),
                tmpNode = {
                    name: name,
                    open: true,
                    children: [],
                    self_areaCode : curAreaCode,
                    icon :objdata.zIcon.area
                };
            //循环幼儿园用户
            var yeyCount = 0;
            for (var key1 in curAreaUser) {
                var curYeyUsers = curAreaUser[key1],
                    tmpNode2 = {
                        name: getYeyName(yeyData, key1),
                        open: true,
                        self_yeyid: key1,
                        children: [],
                        icon : objdata.zIcon.yey
                    };
                var userCount = 0;
                for (var j = 0; j < curYeyUsers.length; j++) {
                    if(!isSentUser(curYeyUsers[j].mobile, curYeyUsers[j].yeyid)){
                        continue;
                    }
                    userCount ++;
                    var tmp = curYeyUsers[j],
                        uInfo  = isReadUser(tmp.mobile, tmp.yeyid),
                        name = objdata.showType === "readDetail" ? (uInfo.platform ? tmp.username + uInfo.platform  : tmp.username) : (tmp.username + uInfo.enrollInfo),
                        tmpNode3 = {
                        name: name,
                        self_phone: tmp.mobile,
                        icon : objdata.zIcon[tmp.role === 3 ? "doc" : "director"]
                    };
                    tmpNode2.children.push(tmpNode3);
                    //    是否是第一个阅读者
                    if(tmp.mobile === objdata.firstUserM){
                        firstReadOrEnroll(tmpNode2, tmpNode3);
                    }
                }
                if(userCount === 0){
                    tmpNode2.isHidden = true;
                }else{
                    yeyCount ++;
                }
                tmpNode.children.push(tmpNode2);
            }
            if(yeyCount === 0){
                tmpNode.isHidden = true;
            }
            root.children.push(tmpNode);
        }
        nodes.push(root);
        var zTreeObj;
        // zTree 的参数配置，深入使用请参考 API 文档（setting 配置详解）
        var setting = {
            view: {
                addDiyDom: addDiyDom,
                showTitle: false
            }
        };
        // zTree 的数据属性，深入使用请参考 API 文档（zTreeNode 节点数据详解）
        var zNodes = nodes;
        zTreeObj = $.fn.zTree.init($("#js-user-list"), setting, zNodes);
        if(objdata.showType === "enrollDetail"){
            $("#read-detail").find(".qr-tit-info").html("发送通知人群列表（<span style='color: #FC8458;'>报名人数/</span><span style='color: #00a0e9;'>报名园所/总园所</span>）");
        }else{
            $("#read-detail").find(".qr-tit-info").html("发送通知人群列表（<span style='color: #FC8458;'>阅读人数/</span><span style='color: #00a0e9;'>阅读园所/总园所</span>）");
        }
        showNum2( zTreeObj.getNodes()[0],zTreeObj,2);
    });
}
//后期处理节点（报名和阅读情况）
function addDiyDom(treeId, treeNode) {
    if(!treeNode.self_phone) return false;
    var aObj = $("#" + treeNode.tId + "_a"),
        reg = /(\S+)（(\S+)）/,
        arrInfo = reg.exec(treeNode.name),
        uname = arrInfo[1],
        enrollStr = arrInfo[2],
        txtClass = "",
        imgSrc = "",
        str = "";
    if(objdata.showType === "enrollDetail"){
    //    报名情况  "彭浩（未报名）"
        enrollStr === "未报名" ? txtClass = "qr-unenroll-txt" : (enrollStr === "不报名" ? txtClass = "qr-noenroll-txt" : txtClass = "qr-enroll-txt");
        str = uname + '<i class="' + txtClass + '">' + enrollStr + '</i>';
        aObj.find("span").eq(1).html(str);
    }else if(objdata.showType === "readDetail"){
    //    阅读情况
        if(enrollStr === "PC"){
            txtClass = "qr-enroll-txt";
            imgSrc = "../images/pc.png";
            enrollStr = "已读"
        } else if(enrollStr === "微信"){
            txtClass = "qr-enroll-txt";
            imgSrc = "../images/weixin.png"
            enrollStr = "已读"
        } else if(enrollStr === "PC,微信"){
            txtClass = "qr-enroll-txt";
            imgSrc = "both";
            enrollStr = "已读"
        } else{
            txtClass = "qr-noenroll-txt";
        }
        if(imgSrc === "both"){
            str = uname + '<i class="' + txtClass + '">' + enrollStr + '</i>' + ('<img style="margin-left:5px; " src="../images/pc.png"><img style="margin-left:5px; " src="../images/weixin.png">');
        }else{
            str = uname + '<i class="' + txtClass + '">' + enrollStr + '</i>' + (imgSrc && '<img style="margin-left:5px; " src="' + imgSrc + '">');
        }
        aObj.find("span").eq(1).html(str);
    }
}
//    非片区 （非直辖市管辖的区或县）
function makeDistinctNodes(nodes, curKey, objUsers, index, cityFlag) {
    var myId = parent.objdata.my.id;
    nodes[curKey] = {
        name : cityFlag ? "本部" : index ? getAreaName(curKey) : "本部",
        open : true,
        self_areaCode : curKey,
        icon : objdata.zIcon.area,
        children : cityFlag ? [] : index ? [{name : "本部", icon : objdata.zIcon.area, open : true, self_areaCode : curKey, children : []}] : []
    };
    //    获取本部用户
    var localNode = cityFlag ? nodes[curKey] : index ? nodes[curKey].children[0] : nodes[curKey],
        userCount = 0,
        curUses = objUsers[curKey];
    if(!curUses){
        localNode.isHidden = true;
        return;
    }
    for (var j = 0; j < curUses.length; j++){
        var tmp = curUses[j],
            uInfo  = isReadUser(tmp.mobile, tmp.yeyid),
            name = objdata.showType === "readDetail" ? (uInfo.platform ? tmp.username + uInfo.platform  : tmp.username) : (tmp.username + uInfo.enrollInfo),
            tmpNode2 = {
            name: name,
            self_phone: tmp.mobile,
            icon : objdata.zIcon.colleague
        },
            isSent = isSentUser(tmp.mobile, tmp.yeyid);
        tmpNode2.isHidden = !isSent;
        isSent && userCount++;
        //    是否是第一个阅读者
        if(tmp.mobile === objdata.firstUserM){
            firstReadOrEnroll(localNode, tmpNode2);
        }
        localNode.children.push(tmpNode2);
    }
    if(localNode.children.length === 0 || !userCount){
        localNode.isHidden = true;
        nodes[curKey].isHidden = true;
    }else{
        nodes[curKey].isHidden = false;
    }
}
//片区 生成节点
function makeSliceNodes(pNodes, sliceCode, objUsers, cityFlag) {
    var myId = parent.objdata.my.id;
    if(cityFlag || pNodes.self_areaCode.length + 2 === sliceCode.length){
        //    片区直属于指定的父节点
        var tmpNode = {
            name : getAreaName(sliceCode),
            open : true,
            self_areaCode :sliceCode,
            icon : objdata.zIcon.area,
            children : []
        };
        var userCount = 0,
            curUses = objUsers[sliceCode];
        for (var j = 0; j < curUses.length; j++){
            var tmp = curUses[j],
                uInfo  = isReadUser(tmp.mobile, tmp.yeyid),
                name = objdata.showType === "readDetail" ? (uInfo.platform ? tmp.username + uInfo.platform  : tmp.username) : (tmp.username + uInfo.enrollInfo),
                tmpNode2 = {
                    name: name,
                    self_phone: tmp.mobile,
                    icon : objdata.zIcon.colleague
                };
            tmpNode2.isHidden = !isSentUser(tmp.mobile, tmp.yeyid);
            !tmpNode2.isHidden && userCount++;
            //    是否是第一个阅读者
            if(tmp.mobile === objdata.firstUserM){
                firstReadOrEnroll(tmpNode, tmpNode2);
            }
            tmpNode.children.push(tmpNode2);
        }
        if(tmpNode.children.length === 0 || !userCount){
            tmpNode.isHidden = true;
        }
        if(cityFlag){
            pNodes[sliceCode] = tmpNode;
        }else{
            pNodes.children.push(tmpNode);
        }
    }else{

    }
}
//是否是4个直辖市所属
function is4City(areaCode) {
    return /^(110|120|310|500)/.test(areaCode);
}
function makeNodes(arrKey,objUsers) {
    var nodes = {};
    for (var i = 0; i < arrKey.length; i ++){
        var curKey = arrKey[i];
        if(curKey.length === 6){
            //    非片区 （非直辖市管辖的区或县）
            nodes[curKey] = [];
            makeDistinctNodes(nodes,curKey,objUsers, i);
        }else{
            //    自定义片区
            var pNode = curKey.substring(0,6); //片区所属区或县的六位编码
            if(typeof nodes[pNode] === "undefined"){
                //    非片区 （非直辖市管辖的区或县）
                nodes[pNode] = [];
                makeDistinctNodes(nodes,pNode,objUsers, i);
            }
            makeSliceNodes(nodes[pNode], curKey, objUsers);
        }
    }
    return nodes;
}
//直辖市生成用户节点树
function make4CityNodes(arrKey, objUsers) {
    var nodes = {};
    for (var i = 0; i < arrKey.length; i ++){
        var curKey = arrKey[i];
        if(curKey.length === 6){
            //    海淀区本部用户
            makeDistinctNodes(nodes,curKey,objUsers, i, true);
        }else{
            //    自定义片区
            makeSliceNodes(nodes, curKey, objUsers, true);
        }
    }
    return nodes;
}
//展现统计数据
function showNum2(pNode, zTreeObj, toType) {
    var pNum = 0,
        allNum = 0,
        enYeyNum = 0,
        yeyNum  = 1,
        childNodes = pNode.children,
        len = childNodes.length;
    if(typeof childNodes[0].self_areaCode === "undefined" && typeof childNodes[0].self_phone !== "undefined"){
        for(var j = 0; j < len; j++){
            var cur = childNodes[j];
            if(cur.isHidden){
                continue;
            }
            allNum++;
            if(objdata.showType === "readDetail"){
                //    阅读详情
                if(/（PC|微信）/.test(cur.name)){
                    pNum ++;
                    enYeyNum = 1;
                }
            }else if(objdata.showType === "enrollDetail"){
                var arr  = /（已报(\d+)人）/.exec(cur.name);
                if(!arr) continue;
                enYeyNum = 1;
                pNum += Number(arr[1]);
            }
        }
        var tmpName = pNode.name;
        pNode.name = tmpName + (toType === 2 ? ("（" + pNum + "人/" + enYeyNum + "园/1园）") : ("（" + pNum + "人/" + enYeyNum + "单位/1单位）"));
        zTreeObj.updateNode(pNode);
        var ppNode = pNode.getParentNode();
        //更新父节点统计数据
        while (ppNode){
            var ppName = ppNode.name,
                ppName0 = ppName,
                ppNum = 0,
                ppYeyNum = 0,
                ppAllNum = 0,
                ppNumText = toType === 2 ? (/（(\d+人\/\d+园\/\d+园)）/.exec(ppName)) : (/（(\d+人\/\d+单位\/\d+单位)）/.exec(ppName));
            if(ppNumText){
                var arrNum = ppNumText[1].split("/");
                ppNum = Number(arrNum[0].substring(0,arrNum[0].length - 1));
                ppYeyNum = Number(arrNum[1].substring(0,(toType === 2 ? (arrNum[1].length-1) : (arrNum[1].length-2))));
                ppAllNum = Number(arrNum[2].substring(0,(toType === 2 ? (arrNum[2].length-1) : (arrNum[2].length-2))));
                ppName0 = ppName.substring(0,ppName.indexOf("（"));
            }
            ppNum += pNum;
            ppAllNum += yeyNum;
            ppYeyNum += enYeyNum;
            if(objdata.showType === "enrollDetail"){
                ppNode.name = ppName0 + (toType === 2 ? ("（" + ppNum +"人/" + ppYeyNum + "园/" + ppAllNum + "园）") : ("（" + ppNum +"人/" + ppYeyNum + "单位/" + ppAllNum + "单位）")) ;
            }else{
                ppNode.name = ppName0 + (toType === 2 ? ("（" + ppNum +"人/" + ppYeyNum + "园/" + ppAllNum + "园）") : (("（" + ppNum +"人/" + ppYeyNum + "单位/" + ppAllNum + "单位）"))) ;
            }
            zTreeObj.updateNode(ppNode);
            ppNode = ppNode.getParentNode();
        //    清除幼儿园节点上带的统计信息
            if(toType === 2 && objdata.showType === "readDetail" && pNode.name.indexOf("（") > -1){
                var ppName1 = pNode.name.substring(0,pNode.name.indexOf("（"));
                pNode.name = ppName1;
                zTreeObj.updateNode(pNode);
            }
        }
    }else{
        for(var i = 0; i < len; i++) {
            var cur = childNodes[i];
            if (cur.isHidden) {
                continue;
            }
            arguments.callee(cur,zTreeObj,toType);
        }
    }
}
//展现同事用户树
function showSameUserTree(objSameUser) {
    var arrAreaCode = objSameUser.arrKey,
        objData = objSameUser.objData,
        myId = parent.objdata.my.id,
        nodes = [];
    var root = {open : true, icon : objdata.zIcon.area};
    for (var key in objData) {
        root.name = objCityCode[key.substring(0,6)];
        root.self_areaCode = key.substring(0,6);
        root.children = [];
        var tmp = is4City(arrAreaCode[0]) ? make4CityNodes(arrAreaCode,objData) : makeNodes(arrAreaCode,objData);
        for (var key1 in tmp ){
            root.children.push(tmp[key1]);
        }
        break;
    }
    nodes.push(root);
    var zTreeObj;
    // zTree 的参数配置，深入使用请参考 API 文档（setting 配置详解）
    var setting = {
        view: {
            addDiyDom: addDiyDom,
            showTitle: false
        }
    };
    // zTree 的数据属性，深入使用请参考 API 文档（zTreeNode 节点数据详解）
    var zNodes = nodes;
    zTreeObj = $.fn.zTree.init($("#js-user-list"), setting, zNodes);
    if(objdata.showType === "enrollDetail"){
        $("#read-detail").find(".qr-tit-info").html("发送通知人群列表（<span style='color: #FC8458;'>报名人数/</span><span style='color: #00a0e9;'>报名单位/总单位</span>）");
    }else{
        $("#read-detail").find(".qr-tit-info").html("发送通知人群列表（<span style='color: #FC8458;'>阅读人数/</span><span style='color: #00a0e9;'>阅读单位/总单位</span>）");
    }
    showNum2( zTreeObj.getNodes()[0],zTreeObj,1);
}
//检索区域名字
function getAreaName(areaCode) {
    for(var i = 0; i < objdata.lingInfo.arrarea.length; i++){
        var cur = objdata.lingInfo.arrarea[i];
        if(cur.areacode === areaCode){
            return cur.areaname;
        }
    }
    if(is4City(areaCode)){
        return areaCode.length === 6 ? "本部" : "未定义";
    }else{
        return areaCode.length === 6 ? objCityCode[areaCode] : "未定义";
    }
}

//查看用户是不是已经阅读及阅读终端
function isReadUser(mobile, yeyid) {
    var obj = {
        isRead : false,
        platform : "（未读）",
        enrollInfo : "（未报名）"
    };
    for(var i = 0; i < objdata.readUsers.length; i++){
        if((objdata.readUsers[i].mobile === mobile && objdata.readUsers[i].yeyid === yeyid) || (yeyid === undefined && objdata.readUsers[i].mobile === mobile)){
            var s = objdata.readUsers[i].enrollstatus;
            obj.isRead = true;
            obj.platform = '（' + objdata.readUsers[i].platform + '）';
            obj.enrollInfo = '（' + (s ? ( s === 1 ? "已报" + objdata.readUsers[i].enrollnum + "人" : "不报名") : "未报名") + '）';
            return obj;
        }
    }
    return obj;
}
//查看用户是不是本条通知的接收人
function isSentUser(mobile, yeyid) {
    for(var i = 0; i < objdata.sentUsers.length; i++){
        var cur = objdata.sentUsers[i];
        if((cur.mobile === mobile && cur.yeyid === yeyid) || (yeyid === undefined && cur.mobile === mobile)){
            return true;
        }
    }
    return false;
}
//获取返回数据汇总的幼儿园名称
function getYeyName(arrYey, id) {
    for (var i = 0; i < arrYey.length; i++) {
        var curid = arrYey[i].id;
        if (curid == id) {
            return arrYey[i].yeyname;
        }
    }
}
function groupData(arr, key) {
    var objData = {},
        arrKey = [];
    for (var i = 0; i < arr.length; i++) {
        var tmpObj = arr[i],
            keyVal = tmpObj[key];
        if (objData[keyVal]) {
            objData[keyVal].push(tmpObj);
        } else {
            arrKey.push(keyVal);
            objData[keyVal] = [tmpObj];
        }
    }
    arrKey.sort(function (arr1, arr2) {
        return Number(arr1) - Number(arr2);
    });
    return {arrKey: arrKey, objData: objData};
}
//以附件形式下载文件
function downAttache(fileOption) {
    var form = document.createElement("form");
    form.style.display = "none";
    form.method = "post";
    form.target = "";
    form.action = "http://" + location.host + $.projectpath + "/download?filename=" + fileOption.ossKey + "&toname=" + fileOption.fileName + "&downtype=notice";
    document.body.appendChild(form);
    form.submit();
}