<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>入园体检套餐设置</title>
    <link rel="stylesheet" href="../css/style.css"/>
    <link rel="stylesheet" href="../css/reset.css"/>
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
	<link rel="stylesheet" href="../css/commonstyle-layui-btkj.css">
	<link rel="stylesheet" href="../css/medical.css">
    <link rel="stylesheet" href="../css/icon.css">
    <link rel='stylesheet' href='../plugin/calendar/fullcalendar.css'/>
    <link rel='stylesheet' href='../plugin/calendar/fullcalendar.print.css' media='print'/>
    <link type="text/css" rel="stylesheet" href="../plugin/jquery-ui/jquery-ui.min.css">
    <link rel="stylesheet" href="../css/medical.css">
    <script src="../sys/html5shiv.min.js"></script>
        <script type="text/javascript">
             document.write('<link rel="stylesheet" id="linktheme" href="../css/' + top.objdata.curtheme + '.css" type="text/css" media="screen"/>');
        </script>
    <style>
       ul {
           line-height: 40px;
       }
      html, body {
	     background: #EAEFF3;
      }
    </style>
</head>
<body>
<section>
	
		<div  style="margin: 10px 15px;">
        <div class="content-medical">
            <div class="comtitletop"> <span class="manufacturer-txt">套餐设置</span>
                <div style="float: right">
                    <div style="float: left;margin-right: 10px;line-height: 30px;margin-top: 0px;"><input id="isopenbtn" type="checkbox" style="margin-top: 0px;" lay-skin="switch" value="1" checked><label style="margin-left:5px;margin-right: 10px;"> 是否开启 </label></div>
                    <button id="addbtn" class="layui-btn addbtn" style="float: right">选择体检项目</button>
                </div>
            </div>
              <div  class="examination-number">
						<div class="layui-form-item" style="display: inline-block;vertical-align: top;">
						    <label class="layui-form-label" style="padding: 8px 0px 5px 0px; text-align: left;">每日体检预约最多：</label>
							<div class="def-search" style="display: inline-block;vertical-align: top;width: 215px;margin: 0px 10px 0 0; height: 28px;">
								<label><input type="text" placeholder="请输入" class="layui-input" style="width:180px;float: left;margin-right: 10px;"><i style="display: inline-block;line-height: 35px;">人</i></label>
							</div>
						</div>
				       <div style="width: 545px;display: inline-block;">
						   <label style="float: left;line-height: 38px;">设置体检日期：</label>
						   <div class="checkbox-blue rilibg" style="float: left;">
							   <label><input type="checkbox" checked="checked">周一</label>
						       <label><input type="checkbox" checked="checked">周二</label>
							    <label><input type="checkbox">周三</label>
						       <label><input type="checkbox">周四</label>
							    <label><input type="checkbox">周五</label>
						       <label><input type="checkbox">周六</label>
							   <label><input type="checkbox">周日</label>
				           </div>
						  </div>
				           <div class="layui-form" style="width:230px;display: inline-block;vertical-align: top;">
						   <div class="layui-input-block operation-label">
								 <div class="layui-input-inline" style="width: 230px;">
									  <span class="triangle-left"></span><span class="triangle-left2"></span>
									  <input type="radio" name="autoreview" value="1" title="上午" lay-filter="approval">
									  <input type="radio" name="autoreview" value="0" title="下午" checked="" lay-filter="notApproval">
									  <input type="radio" name="autoreview" value="0" title="全天" checked="" lay-filter="notApproval">	
								</div>
						   </div>
					  </div>
				   
				       <!--  <label style="margin: 5px 10px 5px 10px; float: left; border-left: 1px solid #D8D8D8; padding: 0 0px 0 20px"><input type="checkbox" checked="checked">采购老师</label>
					  <div class="layui-form" style="width: 150px;clear: none;">
						   <div class="layui-input-block operation-label" style="margin:0 0px 0 0px;width: 140px;">
								 <div class="layui-input-inline" style="width: 140px;">
									  <span class="triangle-left"></span><span class="triangle-left2"></span>
									  <input type="radio" name="autoreview" value="1" title="编辑" lay-filter="approval">
									  <input type="radio" name="autoreview" value="0" title="查看" checked="" lay-filter="notApproval">	
								</div>
						   </div>
					  </div>-->
			</div>
        </div>
		
    <div class="datemain">
        <ul>
            <!--<li>每日体检预约最多:<input id="maxpeople" type="text" value="1" maxlength="6" style="width: 80px"> 人</li>
            <li>设置体检日期:<span>仅选择:

                <span><input type="checkbox" name="day" value="1"> 周一</span>
                <span><input type="checkbox" name="day" value="2"> 周二</span>
                <span><input type="checkbox" name="day" value="3"> 周三</span>
                <span><input type="checkbox" name="day" value="4"> 周四</span>
                <span><input type="checkbox" name="day" value="5"> 周五</span>
                <span><input type="checkbox" name="day" value="6"> 周六</span>
                <span><input type="checkbox" name="day" value="7"> 周日</span>
                </span>
                <p><span>仅选择:
                <span><input type="radio" name="timequantum" value="1"> 上午</span>
                <span><input type="radio" name="timequantum" value="2"> 下午</span>
                <span><input type="radio" name="timequantum" value="3"> 全天</span>
        </span></p>
            </li>-->
            <li>
                <div id="main" style="width:100%">
                    <div id='calendar'></div>
                    <div id="window" style="height: 1px;"></div>
                </div>
            </li>
            <li style="display: flex;background: #fff; padding: 15px 0;">
                <span style="display: inline-block; width: 80px;padding-left: 10px;">体检须知 ：</span>
                <textarea id="physical" rows="3" cols="20" style="width: 100%;height: 150px"></textarea>
            </li>
        </ul>
    </div>
    <div>
        <button  id="btnok" class="layui-btn" >保存</button>
    </div>
</div>
</section>


<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../sys/system.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../plugin/js/moment.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../plugin/My97DatePicker/WdatePicker.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../plugin/calendar/fullcalendar.js?v=" + v + "'><" + "/script>");
     //document.write("<script type='text/javascript' src='../plugin/calendar/lang/zh-cn.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../js/tongph/check_enterschool.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>