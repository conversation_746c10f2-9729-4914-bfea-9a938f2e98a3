<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <link rel="stylesheet" href="../css/reset.css"/>
    <link rel="stylesheet" href="../css/gray.css"/>
    <link rel="stylesheet" href="../css/style.css"/>
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
</head>
<body>
<section class="bg4 pd10-20">
    <h3 class="noticeTitle bg1" style="margin-top: 10px;"><span class="lanflag">页面ID设置</span></h3>
    <div class=" bg6 pd20 formList">
        <!--<h4><b><span class="lanflag" id="lansettime">推荐红包配置</span></b></h4>-->
        <form class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label">工作台：</label>
                <div class="layui-input-block">
                    <input id="workspace" type="text" name="workspace" setname="工作台" required lay-verify="required"
                           placeholder="请输入栏目ID"
                           autocomplete="off" class="layui-input" style="width: 240px;display: inline">
                    <a lay-submit lay-filter="workspace" class="btn-black" style="margin-left: 7px"><span
                            class="lanflag">应用</span></a>
                </div>
            </div>
        </form>
        <form class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label">日历：</label>
                <div class="layui-input-block">
                    <input id="calendar" type="text" name="calendar" setname="日历" required lay-verify="required"
                           placeholder="请输入栏目ID"
                           autocomplete="off" class="layui-input" style="width: 240px;display: inline">
                    <a lay-submit lay-filter="calendar" class="btn-black" style="margin-left: 7px"><span
                            class="lanflag">应用</span></a>
                </div>
            </div>
        </form>
        <form class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label">数据展示：</label>
                <div class="layui-input-block">
                    <input id="datashow" type="text" name="datashow" setname="数据展示" required lay-verify="required"
                           placeholder="请输入栏目ID"
                           autocomplete="off" class="layui-input" style="width: 240px;display: inline">
                    <a lay-submit lay-filter="datashow" class="btn-black" style="margin-left: 7px"><span
                            class="lanflag">应用</span></a>
                </div>
            </div>
        </form>
        <form class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label">通知公告：</label>
                <div class="layui-input-block">
                    <input id="notice" type="text" name="notice" setname="通知公告" required lay-verify="required"
                           placeholder="请输入栏目ID"
                           autocomplete="off" class="layui-input" style="width: 240px;display: inline">
                    <a lay-submit lay-filter="notice" class="btn-black" style="margin-left: 7px"><span
                            class="lanflag">应用</span></a>
                </div>
            </div>
        </form>
        <form class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label">消息提醒：</label>
                <div class="layui-input-block">
                    <input id="message" type="text" name="message" setname="消息提醒" required lay-verify="required"
                           placeholder="请输入栏目ID"
                           autocomplete="off" class="layui-input" style="width: 240px;display: inline">
                    <a lay-submit lay-filter="message" class="btn-black" style="margin-left: 7px"><span
                            class="lanflag">应用</span></a>
                </div>
            </div>
        </form>
    </div>
</section>
<script type="text/javascript" src="../layui-btkj/layui.js"></script>
<script type="text/javascript" src="../sys/jquery.js"></script>
<script type="text/javascript" src="../js/w_chdcset.js"></script>
</body>
</html>