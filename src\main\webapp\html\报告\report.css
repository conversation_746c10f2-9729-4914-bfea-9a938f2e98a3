/*复选框*/
.set-checkbox-blue input[type="checkbox"]{-webkit-appearance: none;width: 14px;height: 14px;border: 1px solid #cccccc;outline: none;border-radius: 2px;vertical-align: top;margin: 3px 5px;}
.set-checkbox-blue input[type="checkbox"]:checked{background: url(../../images/report/icon_checkbox_HL.png) 50%;background-size:14px 14px;outline: none;border: none;border-radius: 1px;}
/*报告*/
.layui-col-xs5{width: 19.9999992%;}
.report-sub .mark-div{background: #4a90e2;font-size: 15px;color: #ffffff;padding: 7px 15px;min-width: 84px;max-width: 154px;display: inline-block;text-align: center;border-radius: 0 30px 30px 0;position: absolute;top: 20px;left: -8px;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;}
.report-sub{height: 272px;margin: 25px 14px 35px 14px;background: url(../../images/report/booklet_bg.png) no-repeat center bottom;background-size: 100% 46px;padding: 0 20px;}
.report-cell{width: 184px;height: 236px;margin: 0 auto;position: relative;}
.report-cell .report-img{width: 184px;height: 236px;}
.report-cell .add-div{text-align: center;position: absolute;width: 100%;margin-top: 75px;cursor: pointer;}
.report-cell .add-div p{margin-top: 15px;font-size: 17px;color: #4a90e2;}
/*创建报告弹框*/
.caleninput{background: url(../../images/report/icon_calen.png)no-repeat right 8px center;background-size: 18px;}
.report-form{border: 1px solid #dddddd;border-top: 2px solid #eeeeee;border-bottom: 2px solid #eeeeee;margin: 13px 16px;}
.report-form h5{background: #f8f8f8;color: #3a3a3a;font-size: 14px;padding: 8px 7px;border-bottom: 2px solid #ebebeb;font-weight: bold;}
.mark-num{display: inline-block;background: #4a90e2;color: #ffffff;width: 24px;height: 24px;line-height: 24px;text-align: center;border-radius: 50%;margin-right: 8px;font-weight: normal;}
.checkbox-sel{padding: 10px 15px;}
.checkbox-sel label{margin: 3px 30px 3px 0;display: inline-block;}
