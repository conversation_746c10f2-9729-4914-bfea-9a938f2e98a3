<!DOCTYPE html>
<html>
<head>
    <title>添加体检结果</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <link rel="stylesheet" href="../../css/reset.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../styles/tbstyles.css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <style>
        .layui-mark-red {
            color: #ff2626;
        }

        /*--------------体检-------------------------*/
        .tabletjzmain {
            width: 93%;
            margin: 0px auto 20px auto;
            border-bottom: 0;
            border-left: 0;
            border-right: 1px solid #c5a49d;
            text-align: center;
            table-layout: fixed;
            border-collapse: collapse
        }

        .tabletjzmain tr td span {
            padding-right: 5px;
            display: block;
            text-align: right;
            color: #666;
        }

        .tabletjzmain tr th {
            background-color: #f3fbe3;
            text-align: center;
        }

        .tabletjzmain tr.firstline, .tabletjzmain tr.firstline td {
            border-top: 1px solid #c5a49d;
            border-bottom: 1px solid #c5a49d;
        }

        .tabletjzmain tr td {
            border-left: 1px #c5a49d solid;
            border-top: 0;
            border-bottom: 1px solid #c5a49d;
            padding-left: 5px;
            height: 48px;
        }

        .tabletjzmain tr td.first { /*border-left: 0;*/
            font-size: 14px;
        }

        /*2016修改*/
        .tabletjz-bot input[type="text"] {
            height: 24px;
            line-height: 24px;
        }

        .jc_content {
            width: 1280px;
            margin: 0px auto;
            background: url(../../images/physical/ticen.png) repeat-y;
            color: rgba(1, 1, 1, 1);
            border-top: none;
            padding: 10px 0 1px 0;
        }

        .tabletj tr td {
            border-left: 1px #C5A49D dotted;
            border-top: 1px #C5A49D dotted;
            padding-left: 1px;
            height: 48px;
        }

        .tabletj tr.firstline, .tabletj tr.firstline td {
            padding-left: 20px;
            border-top: 1px solid #C5A49D;
            border-bottom: 1px solid #C5A49D;
        }

        .tabletj tr td {
            border-left: 1px #C5A49D solid;
            border-top: 0;
            padding-left: 1px;
            border-bottom: 1px solid #C5A49D;
        }

        .tjgrade tr td {
            border: 1px solid #C5A49D;
            color: #ea9103;
            cursor: pointer;
            font-size: 12px;
            height: 24px;
            line-height: 24px;
            text-align: center;
            width: 25%;
        }

        .tabs-nav li {
            height: 30px;
            float: left;
            margin-left: 5px;
            min-width: 66px; /* be nice to Opera *//* background: #8bab00; */
            background: #C5A49D;
            line-height: 30px;
            border-style: solid;
            border-color: #C5A49D;
            border-width: 3px 1px 0 1px;
        }

        .tabs-nav {
            list-style: none;
            margin: 0 auto;
            padding: 0 0 0 1px;
            display: inline-block;
            border-bottom: 1px solid #C5A49D;
            width: 99%;
        }

        .first {
            background: #FFFDFD;
        }

        .first.leftline {
            width: 300px;
        }

        .addpic-btn {
            margin-left: 10px;
        }

        .tabletj label {
            margin: 0 10px 0 0;
        }

        .tabletj input[type="checkbox"] {
            margin-right: 3px;
        }

        .layui-input-block {
            min-height: 30px;
            margin-left: 0px;
            float: left;
            margin-right: 10px;
            width: 150px;
        }

        .marpadd10 {
            padding-top: 10px;
        }

        .fl {
            float: left
        }

        .tjzagl_inputcl, .tjzagl_wt {
            width: 193px;
        }

        .layui-form-item {
            display: inline-block;
            vertical-align: top;
        }

        .layui-form-radio {
            margin-right: 15px;
            margin: 10px 0 5px 0;
        }

        .checkbox-box {
            width: 280px;
            display: inline-block;
            margin: 10px 0 5px 0;
        }

        .msg-wrap.n-error {
            display: none;
        }

        .layui-form-radio {
            margin-right: 10px;
        }
    </style>
</head>
<body>
<div style="width:1280px; margin: 0 auto" id="form" action="" class="layui-form" lay-filter="formOk">
    <div style=" margin:10px auto 0  auto"><img src="../../images/physical/titop.png" width="1280" height="70"/>
    </div>
    <div class="jc_content">
        <table cellpadding="0" id="divtableclass" cellspacing="0" class="tabletjzmain" style="text-align: center;">
            <tr class="firstline">
                <td class="first leftline">
<!--                    <i class="layui-mark-red">*</i>-->
                    编号
                </td>
                <td>
                    <div class="tjzagl_wt">
                        <input id="premcheck_id" type="text" maxlength="20" placeholder="编号" class="tjzagl_inputcl">
                        <span class="FancyInput__bar___1P3wW"></span>
                    </div>
                </td>
                <td>
<!--                    <button class="layui-btn form-search" onclick="OcrRecognition()">开启服务</button>-->
                    <button class="layui-btn form-search" style="float: left" onclick="ReadIDCard()">读身份证</button>
                    <label id="message" style="float: left;margin: 10px;"></label>
                </td>
            </tr>
            <tr>
                <td class="first leftline">
<!--                    <i class="layui-mark-red">*</i> -->
                    医院地址
                </td>
                <td>
                    <div class="layui-form" style="display:block; float: left;">
                        <div class="layui-form-item" style=" margin: 10px;display: inline;float: left;width: 800px" id="selareacode">
                            <div class="layui-input-block">
                                <select id="selprovince" name="province" class="province-selector" lay-filter="province-1"></select>
                            </div>
                            <div class="layui-input-block">
                                <select id="selcity" name="city" class="city-selector" lay-filter="city-1"></select>
                            </div>
                            <div class="layui-input-block">
                                <select id="selcounty" name="county" class="county-selector" lay-filter="county-1">
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="fl marpadd10 tjzagl_wt">
                        <input id="hospital_fulladdress" type="text" placeholder="详细地址" maxlength="200" class="tjzagl_inputcl">
                        <span class="FancyInput__bar___1P3wW"></span></div>
                </td>
            </tr>
        </table>
        <table cellpadding="0" cellspacing="0" class="tabletjzmain" style="text-align: center;">
            <tr class="firstline">
                <td class="first leftline"><i class="layui-mark-red">*</i>姓名</td>
                <td class="first"><i class="layui-mark-red">*</i>性别</td>
                <td class="first"><i class="layui-mark-red">*</i>出生日期</td>
                <td class="first"><i class="layui-mark-red">*</i>民族</td>
            </tr>
            <tr>
                <td>
                    <div class="tjzagl_wt" style="margin: 0 auto;">
                        <input type="text" id="name" maxlength="20" lay-verify="required" lay-reqtext="姓名不能为空"
                               value=""
                               class="tjzagl_inputcl">
                        <span class="FancyInput__bar___1P3wW"></span></div>
                </td>
                <td>
                    <div class="layui-form" style="display:block;">
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <select id="sex" lay-verify="required" lay-reqtext="性别不能为空">
                                    <option value="">请选择</option>
                                    <option value="0">男</option>
                                    <option value="1">女</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="layui-form" style="display:block;">
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <input type="text" class="layui-input" id="birthday"
                                       placeholder="请选择日期" lay-verify="required" lay-reqtext="出生日期不能为空">
                            </div>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="layui-form" style="display:block;">
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <select lay-search="" id="ethnic" lay-verify="required" lay-reqtext="民族信息不能为空">
                                </select>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
        </table>
        <table cellpadding="0" cellspacing="0" class="tabletjzmain" style="text-align: center;">
            <tr class="firstline">
                <td class="first leftline"><i class="layui-mark-red">*</i>身份证号</td>
                <td class="first"><i class="layui-mark-red">*</i>单位或职业</td>
                <td class="first"><i class="layui-mark-red">*</i>现住址</td>
                <td class="first"><i class="layui-mark-red">*</i>对方姓名</td>
            </tr>
            <tr>
                <td>
                    <div class="tjzagl_wt" style="margin: 0 auto;">
                        <input type="text" id="idcard" class="tjzagl_inputcl"
                               maxlength="18" lay-verify="required|identity" lay-reqtext="身份证号不能为空"/>
                        <span class="FancyInput__bar___1P3wW"></span></div>
                </td>
                <td>
                    <div class="tjzagl_wt" style="margin: 0 auto;">
                        <input type="text" id="occupation" class="tjzagl_inputcl"
                               maxlength="200" lay-verify="required" lay-reqtext="单位或职业不能为空"/>
                        <span class="FancyInput__bar___1P3wW"></span></div>
                </td>
                <td>
                    <div class="tjzagl_wt" style="margin: 0 auto;">
                        <input type="text" id="current_address" class="tjzagl_inputcl"
                               maxlength="200" lay-verify="required" lay-reqtext="现住址不能为空"/>
                        <span class="FancyInput__bar___1P3wW"></span></div>
                </td>
                <td>
                    <div class="tjzagl_wt" style="margin: 0 auto;">
                        <input type="text" id="other_name" class="tjzagl_inputcl"
                               maxlength="20" lay-verify="required" lay-reqtext="对方姓名不能为空"/>
                        <span class="FancyInput__bar___1P3wW"></span></div>
                </td>
            </tr>
            <tr>
                <td class="first"><i class="layui-mark-red">*</i> 直系、三代内旁系血亲关系</td>
                <td colspan="3" style="text-align: left;">
                    <div class=" layui-form" style="padding-left: 10px;">
                        <div class="layui-input-inline">
                            <input type="radio" name="blrelations" value="0" title="无"
                                   lay-verify="requiredBlrelations">
                            <div class="layui-unselect layui-form-radio"><i class="layui-anim layui-icon"></i>
                                <div>无</div>
                            </div>
                            <input type="radio" name="blrelations" value="1" title="有"
                                   lay-verify="requiredBlrelations">
                            <div class="layui-unselect layui-form-radio"><i
                                    class="layui-anim layui-icon"></i>
                                <div>有</div>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
        </table>
        <table cellpadding="0" cellspacing="0" class="tabletjzmain" style="text-align: center;">


            <tr class="firstline">
                <td class="first leftline"><em class="layui-mark-red">*</em>婚前医学检查结果</td>
                <td style="text-align: left;">
                    <div class=" layui-form" style="padding-left: 10px; padding-bottom: 5px;" id="premcheck_stu">
                        <div class="layui-input-inline">
                            <div style="display: flex; flex-direction: row">
                                <input id="radio1" type="radio" name="chkprem" lay-filter="myRadioFilter" value="0"
                                       title="未见异常" lay-verify="requiredChkprem">
                                <div style="margin-top: 13px"><label
                                        style="margin-left: 20px;">未发现医学上不宜结婚的异常情况和疾病 </label><label
                                        style="margin-left: 20px;">常规检查未发现法律规定的不宜生育的疾病 </label>
                                </div>
                            </div>
                            <div style="display: flex; flex-direction: row">
                                <input id="radio2" type="radio" name="chkprem" lay-filter="myRadioFilter" value="1"
                                       title="异常" lay-verify="requiredChkprem"
                                       style="margin-left:30px;">
                                <div style="margin-top:8px">
                                    <input type="text" placeholder="请输入异常结果" class="layui-input"
                                           id="premcheck_result"
                                           name="premcheck_result" maxlength="200"
                                           style="width:630px;height: 32px;line-height: 32px; margin-left: 45px;"
                                           lay-verify="requiredChkpremInput">
                                </div>
                            </div>
                        </div>
                    </div>
                </td>

            </tr>
            <tr>
                <td class="first leftline"><em class="layui-mark-red">*</em> 医学建议</td>
                <td style="text-align: left; padding-left: 16px; padding-bottom: 5px;" class="checkbox-blue"
                    id="prem_check">
                    <!--                    <label><input id="txtguidance1" type="checkbox" name="premcheckbox" value="1"-->
                    <!--                                  lay-skin="primary" lay-verify="requiredCheckbox"/>1.建议不宜结婚</label>-->
                    <!--                    <label><input id="txtguidance2" type="checkbox" name="premcheckbox" value="2"-->
                    <!--                                  lay-skin="primary" lay-verify="requiredCheckbox"/>2.建议不宜生育</label>-->
                    <!--                    <label><input id="txtguidance3" type="checkbox" name="premcheckbox" value="4"-->
                    <!--                                  lay-skin="primary" lay-verify="requiredCheckbox"/>3.建议暂缓结婚</label>-->
                    <!--                    <br>-->
                    <!--                    <label><input id="txtguidance4" type="checkbox" name="premcheckbox" value="8"-->
                    <!--                                  lay-skin="primary" lay-verify="requiredCheckbox"/>4.未发现医学上不宜结婚的情形</label>-->
                    <!--                    <label><input id="txtguidance5" type="checkbox" name="premcheckbox" value="16"-->
                    <!--                                  lay-skin="primary" lay-verify="requiredCheckbox"/>5.建议采取医学措施，尊重受检者意愿</label>-->

                    <div class="checkbox-box">
                        <input id="txtguidance1" type="checkbox" name="premcheckbox" value="1"
                               lay-skin="primary" lay-verify="requiredCheckbox">
                        <label for="txtguidance1">①建议不宜结婚</label>
                    </div>
                    <div class="checkbox-box">
                        <input id="txtguidance2" type="checkbox" name="premcheckbox" value="2"
                               lay-skin="primary" lay-verify="requiredCheckbox">
                        <label for="txtguidance2">②建议不宜生育</label>
                    </div>
                    <div class="checkbox-box">
                        <input id="txtguidance3" type="checkbox" name="premcheckbox" value="4"
                               lay-skin="primary" lay-verify="requiredCheckbox">
                        <label for="txtguidance3">③建议暂缓结婚</label>
                    </div>
                    <div class="checkbox-box">
                        <input id="txtguidance4" type="checkbox" name="premcheckbox" value="8"
                               lay-skin="primary" lay-verify="requiredCheckbox">
                        <label for="txtguidance4">④未发现医学上不宜结婚的情形</label>
                    </div>
                    <div class="checkbox-box">
                        <input id="txtguidance5" type="checkbox" name="premcheckbox" value="16"
                               lay-skin="primary" lay-verify="requiredCheckbox">
                        <label for="txtguidance5">⑤建议采取医学措施，尊重受检者意愿</label>
                    </div>


                </td>
            </tr>
        </table>
        <table cellpadding="0" cellspacing="0" class="tabletjzmain" style="text-align: center;margin-bottom: 10px;">
            <tr class="firstline">
                <td class="first leftline">主检医师签字</td>
                <td class="first">检查单位专用章</td>
                <td class="first"><i class="layui-mark-red">*</i>日期</td>
            </tr>
            <tr>
                <td>
                    <div class="tjzagl_wt" style="margin: 0 auto;">
                        <input id="doctor_name" type="text" maxlength="20" class="tjzagl_inputcl">
                    </div>
                </td>
                <td>
                    <div class="tjzagl_wt" style="margin: 0 auto;">
                        <input id="hospital_seal" type="text" maxlength="20" class="tjzagl_inputcl">
                    </div>
                </td>
                <td>
                    <div class="layui-form" style="display:block;">
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <input type="text" class="layui-input" id="time"
                                       placeholder="请选择日期" lay-verify="required" lay-reqtext="日期不能为空">
                            </div>
                        </div>
                    </div>
                </td>

            </tr>

        </table>
        <div style="display: none">
            <div class="layui-input-block">
                <a id="addbtnok" class="layui-btn" lay-submit="lay-submit">新增按钮</a>
            </div>
        </div>
        <div style="display: none">
            <div class="layui-input-block">
                <a id="savebtnok" class="layui-btn" lay-submit="lay-submit">修改按钮</a>
            </div>
        </div>
    </div>
    <div style=" margin:0px auto 0  auto"><img src="../../images/physical/tibot.png" width="1280" height="52"/>
    </div>
</div>
</body>

<script type="text/javascript">
    let websockets;
    let isconncet = false;
    let parameterAll = "timeout=" + 10000;
    //当前的连接的状态
    let statusArr = [
        {state: 0, value: '正在连接'},
        {state: 1, value: '已建立连接'},
        {state: 2, value: '正在关闭连接'},
        {state: 3, value: '已关闭连接'}
    ]

    /**
     * 建立连接
     */
    function OcrRecognition() {
        if (!isconncet) {
            // 1. 创建websockets对象，参数为服务器websockets地址
            // let hostname = location.hostname;
            var url = "ws:127.0.0.1:9001"
            websockets = new WebSocket(url);

            // 2.监听websocket的状态变化，接收的信息，关闭时的状态

            //监听连接状态的变化
            websockets.onopen = (event) => socketChange();

            //监听接收消息的情况
            websockets.onmessage = (res) => {
                console.log('身份证 res-> ', res);
                if (res.data == "卡认证失败") {
                    $("#message").css({"color":"red"}).html("请拿起身份证再放下重试!");
                    return;
                }
                var alldata = res.data.split("|");
                //返回值 卡类型|姓名|性别|民族|出生日期|地址|证件号 码|签发机关|起始有期限|结束有效期|新地址|指纹|签发次数|通行证号码|中文姓名|证件版本|国籍代码|照片base64|身份证正面base64|身份证反面base64
                if (alldata.length >= 17) {
                    console.log('身份证 -> ', alldata);
                    $("#name").val(alldata[3]);
                    var sex = alldata[4];
                    if(sex=='男'){
                        var select = 'dd[lay-value=0]';
                        $('#sex').siblings("div.layui-form-select").find('dl').find(select).click();
                    }
                    if(sex=='女'){
                        var select = 'dd[lay-value=1]';
                        $('#sex').siblings("div.layui-form-select").find('dl').find(select).click();
                    }

                    var birthday = alldata[6].substring(0, 4) + "-" + alldata[6].substring(4, 6) + "-" + alldata[6].substring(6, 8);
                    $("#birthday").val(birthday);
                    //民族
                    setTimeout(() => {
                        console.log('民族 -> ', alldata[5]);
                        var ethnic = 'dd[lay-value="'+alldata[5]+'族"]';
                        $('#ethnic').siblings("div.layui-form-select").find('dl').find(ethnic).click();
                    },1000);
                    //身份证
                    $("#idcard").val(alldata[8]);
                    //地址
                    $("#current_address").val(alldata[7]);
                    $("#message").css({"color":"green"}).html("读卡完成");
                }
            }
            //监听关闭时的状态变化
            websockets.onclose = (event) => socketChange();
        } else {
            closeConnect();
            $("#message").css({"color":"green"}).html("连接成功")
        }
    }

    function ReadIDCard() {
        if (!isconncet) {
            //连接ws
            OcrRecognition();
            setTimeout(() => {
                if(isconncet){
                    //第一次建立连接之后立即读卡
                    let val = "03?" + parameterAll;
                    websockets.send(val);
                    $("#message").css({"color":"green"}).html("读卡完成");
                }
            },1500);
            console.log('ReadIDCard - 未连接服务');
            $("#message").css({"color":"red"}).html("请拿起身份证再放下重试!");
        } else {
            console.log('开始读卡');
            $("#message").css({"color":"black"}).html("开始读卡");
            //固定用法
            let val = "03?" + parameterAll;
            websockets.send(val);
        }
    }

    /**
     * socket状态变化
     */
    function socketChange() {
        let state = websockets.readyState;
        let val = statusArr.map((item) => {
            if (item.state == state) {
                return item.value
            }
        });

        //读身份证
        if (1 == state) {
            isconncet = true;
            $("#message").css({"color":"green"}).html("连接成功");
        }
        if (3 == state) {
            isconncet = false;
        }
    }

    /**
     * 关闭连接
     */
    function closeConnect() {
        websockets.close();
    }
</script>
<script type="text/javascript" data-main="../../js/prem/premedicalexamdetail" src="../../sys/require.min.js"></script>
</html>
