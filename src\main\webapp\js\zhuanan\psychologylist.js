﻿/***
 *
 * 心理行为专案
 */
require.config({
    paths: {
        jquery: '../../sys/jquery',
        system: '../../sys/system',
        func: '../../sys/function',
        listcommon: '../common/listcommon',
        sendMsg: "../common/sendMsg",
        layui: "../../layui-btkj/layui",
        "xm-select": "../../plugin/xm-select/xm-select"
    },
    shim: {
        "system": {
            deps: ["jquery"]
        },
        "layui": {
            deps: ["jquery", "system"]
        }
    },
    waitSeconds: 0
});
var objdata = {
    objselresult: {},
    objrecord: {}
};
require(["jquery", "system", "xm-select", 'layui', 'func', 'listcommon', 'sendMsg'], function () {
    layui.use(['table', "laydate", 'form'], function () {
        objdata.istuoyou = jQuery.getparent().istuoyou();
        initEvent();
        initarea();
        initTable();
    });
});

/**
 * 初始化事件
 */
function initEvent() {
    $("#btnadd").click(function () {
        btnadd();
    });
    $("#btnsearch").click(function () {
        btnsearch();
    });

    $('#iconstartdate').click(function (e) {
        var show = true;
        if (document.activeElement.id == "txtstartdate") {
            show = false;
        }
        var objdate = {
            elem: '#txtstartdate',
            show: show, //直接显示
            needrender: true,
            isInitValue: true,
            showBottom: true,
            done: function () {
                $('#txtstartdate').blur();
            }
        }
        layui.laydate.render(objdate);
        return false;
    });
    $('#txtstartdate').focus(function (e) {
        setTimeout(function () {
            $('#iconstartdate').trigger("click");
        }, 100);
    });
    $('#iconenddate').click(function (e) {
        var show = true;
        if (document.activeElement.id == "txtenddate") {
            show = false;
        }
        var objdate = {
            elem: '#txtenddate',
            show: show, //直接显示
            needrender: true,
            isInitValue: true,
            showBottom: true,
            done: function () {
                $('#txtenddate').blur();
            }
        }
        layui.laydate.render(objdate);
        return false;
    });
    $('#txtenddate').focus(function (e) {
        setTimeout(function () {
            $('#iconenddate').trigger("click");
        }, 100);
    });
    $("#btnReviewRemind").click(function (){//批量提醒
        if (isEmpty(objdata.objselresult)) {
            return jQuery.getparent().layer.msg("请先选择要批量提醒的幼儿！");
        }
        var arrid = [];
        for (var id in objdata.objselresult) {
            arrid.push(parseInt(id));
        }
        reviewremind(arrid);
        // parent.layer.confirm("请确认是否批量提醒心里行为发育异常幼儿复查？", { btn: ["批量提醒", "取消"], btnAlign: 'c' }, function (index) {
        //     var objreviewcontent = {
        //         detailtype: 2,//消息详情源分类 1.通知类 2.预约挂号 3.预约入园体检 4预约定期体检 5付款
        //         receivetype: 1,//接收类型（1家长）
        //         areacode: jQuery.getparent().objdata.my.areacode.substring(0, 6),
        //         msgtype: 8,//消息类型 1:''专家讲座'',2:''专家治疗'',3:''消息通知'',4:''已报名'',5:''预约成功'',6:''取消预约'',7:''付款提醒''',8专案提醒
        //         tabletype: 'psychtest',//专案类型，判断哪个专案
        //         noticetitle: '心里行为发育异常',//提醒标题
        //         arrid: []
        //     };
        //     $.sm(function (re, err) {//获取营养不良专案幼儿
        //         if (re) {
        //             parent.layer.msg('提醒成功！');
        //             parent.layer.close(index);
        //         } else {
        //             parent.layer.msg(err);
        //         }
        //     }, ['w_slice.getreviewstu', JSON.stringify(objreviewcontent)]);
        // });
    });
    layui.form.render();
}

function reviewremind(arrid){
    var arrname = [];
    for (var i = 0; i < arrid.length; i++) {
        var item = objdata.objrecord[arrid[i]];
        arrname.push(item.stuname);
    }
    if(arrname.length > 0){
        jQuery.getparent().layer.confirm("请确认是否提醒“" + arrname.join("、") + "”复查？", { btn: ["提醒", "取消"], btnAlign: 'c' }, function (index) {
            var msgtime = jQuery.getparent().objdata.moment().format("YYYY-MM-DD HH:mm:ss");
            var objpushmsg = getmsgobj("xcxcpmch", 1, "", {
                "time3": {
                    "value": msgtime
                },
                "thing2": {
                    "value": "营养不良复查提醒"
                }
            });
            var objreviewcontent = {
                detailtype: "",//消息详情源分类 1.通知类 2.预约挂号 3.预约入园体检 4预约定期体检 5付款
                receivetype: 1,//接收类型（1家长）
                areacode: jQuery.getparent().objdata.my.areacode.substring(0, 6),
                msgtype: 8,//消息类型 1:''专家讲座'',2:''专家治疗'',3:''消息通知'',4:''已报名'',5:''预约成功'',6:''取消预约'',7:''付款提醒''',8专案提醒
                tabletype: 'psychtest',//专案类型，判断哪个专案
                noticetitle: '心里行为发育异常',//提醒标题
                arrid: arrid,
                objpushmsg: objpushmsg
            };
            $.sm(function (re, err) {//获取营养不良专案幼儿
                if (re) {
                    jQuery.getparent().layer.msg('提醒成功！');
                    jQuery.getparent().layer.close(index);
                    layui.data('checked', null);
                    objdata.objselresult = {};
                } else {
                    jQuery.getparent().layer.msg(err);
                }
            }, ['w_slice.getreviewstu', JSON.stringify(objreviewcontent)]);
        });
    } else {
        return jQuery.getparent().layer.msg("请先选择要提醒的幼儿！");
    }
}
/*
功能：初始化数据
*/
function initTable() {
    var pageData = [];
    var table = layui.table;
    var arrcols = [ //标题栏
        {field: 'id', type: 'checkbox', width: 60, fixed: 'left'},
        {type: 'numbers', width: 50},
        {field: 'yeyname', title: '托幼机构名称', align: 'left', width: 200, sort: true, hide: objdata.istuoyou},
        {field: 'stuname', title: '姓名', align: 'left', width: 100, sort: true},
        {field: 'sex', title: '性别', align: 'center', width: 80, sort: true},
        {field: 'birthday', title: '出生日期', align: 'left', width: 110, sort: true},
        {
            field: 'age', title: '年龄', align: 'left', width: 80, templet: function (d) {
                return GetAge(d.birthday, jQuery.getparent().objdata.strtoday, "zh");
            }
        },
        {field: 'credentialsnum', title: '身份证号', align: 'center', width: 180, sort: true},
        {field: 'localtion', title: '户籍', align: 'left', width: 110, hide:true},
        {field: 'managetime', title: '筛查日期', align: 'left', width: 120, sort: true},
        {
            title: '操作', minWidth: 250, align: 'left', fixed: 'right',
            templet: function (d) {
                var arrbtnhtml = [];
                if (objdata.istuoyou) {
                    arrbtnhtml.push('<a class="blue-txt layui-btn-xs" lay-event="detail">查看</a>');
                    arrbtnhtml.push('<a class="blue-txt layui-btn-xs" lay-event="edit">编辑</a>');
                    arrbtnhtml.push('<a class="blue-txt layui-btn-xs" lay-event="manage">管理</a>');
                }
                arrbtnhtml.push('<a class="blue-txt layui-btn-xs" lay-event="card">管理卡</a>');
                // if (jQuery.getparent().iserbao()) {
                arrbtnhtml.push('<a class="blue-txt layui-btn-xs" lay-event="reviewrecheck">提醒复查</a>');
                // }
                if (objdata.istuoyou) {
                    arrbtnhtml.push('<a class="blue-txt layui-btn-xs" lay-event="del">删除</a>');
                }
                return arrbtnhtml.join('');
            }
        }
    ];
    var option = {
        elem: '#laytable',
        where: {
            swhere: $.msgwhere(getswhere()),
            fields: 'managetime',
            types: ''
        },
        height: 'full-' + ($("#laytable").offset().top),
        autoSort: false,
        cols: [arrcols],
        skin: 'row', //表格风格
        jump: function (obj1, obj2) {
        },
        done: function (o1, o2, count) {
            if (count === 0) {
                var backurl = "../../images/searchwhere.png";
                if (o1.hasOwnProperty("code")) {//后端返回值0条
                    backurl = "../../plugin/flexigrid/images/kb.png";
                }
                $(".layui-none").html('<div style="background: url(' + backurl + ') center center no-repeat;width: 100%;height: 100%"></div>').css({"height": "100%"});
            }

            //.记下当前页数据，Ajax 请求的数据集，对应你后端返回的数据字段
            pageData = o1.data;
            //.遍历当前页数据，对比已选中项中的 id
            for (var i = 0; i < pageData.length; i++) {
                objdata.objrecord[pageData[i]['id']] = pageData[i];
                if (layui.data('checked', pageData[i]['id'])) {
                    //.选中它，目前版本没有任何与数据或表格 id 相关的标识，不太好搞，土办法选择它吧
                    var tr = $(".layui-table tr[data-index=" + i + "]");
                    tr.find("input[type='checkbox']").prop('checked', true);
                    // tr.find('.layui-form-checkbox').removeClass("layui-form-checked").remove();
                    // tbl.find('table>tbody>tr').eq(i).find('td').eq(0).find('input[type=checkbox]').prop('checked', true);
                }
            }
            //.PS：table 中点击选择后会记录到 table.cache，没暴露出来，也不能 mytbl.renderForm('checkbox');
            layui.form.render('checkbox');
        },
        countNumberBool: true,
        even: true,
        page: true, //是否显示分页
        limits: [10, 20, 30, 50, 100],
        limit: 20 //每页默认显示的数量
    };
    if (objdata.istuoyou) {
        option.url = encodeURI($.getLayUrl() + "?" + $.getSmStr(["psychologylist.table"]));
    } else {
        option.data = [];
    }
    table.render(option);
    layui.data('checked', null);
    table.on('checkbox(laytable)', function (obj) {//.监听选择，记录已选择项
        //.全选或单选数据集不一样
        var data = obj.type == 'one' ? [obj.data] : pageData;
        //.遍历数据
        $.each(data, function (k, v) {
            //.假设你数据中 id 是唯一关键字
            if (obj.checked) {
                //.增加已选中项
                layui.data('checked', {
                    key: v.id, value: v
                });
                objdata.objselresult[v.id] = v;
            } else {
                //.删除
                layui.data('checked', {
                    key: v.id, remove: true
                });
                delete objdata.objselresult[v.id];
            }
        });
    });
    table.on('sort(laytable)', function (obj) {
        table.reload('laytable', {
            where: {
                swhere: $.msgwhere(getswhere()),
                fields: obj.field, //排序字段
                types: obj.type, //排序方式
                isPage: 0
            }
        });
    });
    table.on('tool(laytable)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
        var data = obj.data; //获得当前行数据
        var layEvent = obj.event; //获得 lay-event 对应的值（也可以是表头的 event 参数对应的值）
        var title = data.stuname;
        if (layEvent === "detail") {
            jQuery.getparent().layer.open({
                type: 2,
                area: ["850px", "100%"],
                title: "查看儿童心理行为的筛查分析报告",
                shadeClose: false, //点击遮罩关闭
                content: ["html/zhuanan/psychologydetail2.html?v=" + Arg('v') + "&mid=" + Arg("mid") + "&stuno=" + data.stuno + "&isviwe=1"],
                btn: ["打印预览", "导出Excel", "关闭"],
                success: function (layero, index) {
                }, yes: function (index, layero) {
                    layero.find('iframe')[0].contentWindow.$('#btprint').trigger('click');
                }, btn2: function (index, layero) {
                    layero.find('iframe')[0].contentWindow.$('#btexcel').trigger('click');
                    return false;
                }
            });
        } else if (layEvent === "edit") {
            jQuery.getparent().layer.open({
                type: 2,
                area: ["850px", "100%"],
                title: "编辑儿童心理行为的筛查分析",
                shadeClose: false, //点击遮罩关闭
                content: ["html/zhuanan/psychologydetail2.html?v=" + Arg('v') + "&mid=" + Arg("mid") + "&stuno=" + data.stuno + "&isedit=1"],
                btn: ["关闭"],
                success: function (layero, index) {
                }, yes: function (index, layero) {
                    // var w = layero.find('iframe')[0].contentWindow;
                    // w.$("#btnsave").trigger('click', function () {
                    //     btnsearch();
                    //     jQuery.getparent().layer.close(index);
                    // });
                    jQuery.getparent().layer.close(index);
                }
            });
        } else if (layEvent === "manage") {
            jQuery.getparent().layer.open({
                type: 2,
                title: "管理" + title,
                shadeClose: false,
                area: ["100%", "100%"],
                content: "html/zhuanan/psychologycard.html?v=" + (Arg("v") || 1) + "&mid=" + Arg("mid") + "&stuno=" + data.stuno + ($("#txtstartdate").val() == "" ? "" : "&sdate=" + $("#txtstartdate").val()) + ($("#txtenddate").val() == "" ? "" : "&edate=" + $("#txtenddate").val()),
                btn: ["确定", "取消"],
                yes: function (index, layero) { //或者使用btn1
                    var w = layero.find('iframe')[0].contentWindow;
                    w.$("#btnsave").trigger("click", function (obj) {
                        btnsearch();
                        jQuery.getparent().layer.close(index);
                    });
                }
            });
        } else if (layEvent === "card") {
            jQuery.getparent().objdata.malnubindex = jQuery.getparent().layer.open({
                type: 2,
                title: "心理专案管理卡",
                shadeClose: false,
                area: ['100%', '100%'],
                content: "html/zhuanan/psychologyreport.html?v=" + (Arg("v") || 1) + "&mid=" + Arg("mid") + "&stuno=" + data.stuno + ($("#txtstartdate").val() == "" ? "&sdate=" + $("#txtstartdate").val() : "") + ($("#txtenddate").val() == "" ? "&edate=" + $("#txtenddate").val() : "") + "&yeyid=" + data.yeyid + "&yeyname=" + data.yeyname,
                btn: ["打印预览", "导出Excel", "关闭"],
                success: function (layero, index) {
                }, yes: function (index, layero) {
                    layero.find('iframe')[0].contentWindow.$('#btprint').trigger('click');
                }, btn2: function (index, layero) {
                    layero.find('iframe')[0].contentWindow.$('#btexcel').trigger('click');
                    return false;
                }
            });
        } else if (layEvent === "del") {
            jQuery.getparent().layer.confirm('请确认是否删除"' + data.stuname + '"？', function (r) {
                var arrsm = [["tb_psychtest.del", data.stuno], ["tb_psychtestmanage.del", data.stuno], ["tb_psychtestreslut.del", data.stuno]];
                $.sm(function (re, err) {
                    if (err) {
                        jQuery.getparent().layer.msg(err, {icon: 5});
                    } else {
                        btnsearch();
                    }
                    jQuery.getparent().layer.close(r);
                }, arrsm, null, null, null, null, 1);
            });
        } else if (layEvent === "reviewrecheck") {//提醒复查
            reviewremind([data.id]);
        }
    });
}

function getswhere() {
    var strstud = $("#txtstudent").val();
    var stridend = $("#selidend").val();
    var startdate = $("#txtstartdate").val();
    var enddate = $("#txtenddate").val();
    var objwhere = {};
    var yeytype = $("#selyeytype").val();
    var age = $("#selage").val(); //年龄
    if (yeytype) {
        objwhere.yeytype = [yeytype];
    }
    var yeyid = objdata.selyey.getValue('valueStr');
    if (yeyid) {
        objwhere.yeyid = [yeyid];
    }
    if (strstud) {
        objwhere.keyword = [strstud];
    }
    if (stridend != "") {
        objwhere.idend = [stridend];
    }
    if (startdate) {
        objwhere.managetime1 = [startdate];
    }
    if (enddate) {
        objwhere.managetime2 = [enddate];
    }
    if (age) {
        var sage = 0, eage = 0;
        switch (age + "") {
            case "0.03":
                sage = 0.03;
                eage = 0.06;
                break;
            case "0.06":
                sage = 0.06;
                eage = 0.08;
                break;
            case "0.08":
                sage = 0.08;
                eage = 1;
                break;
            case "1":
                sage = 1;
                eage = 1.06;
                break;
            case "1.06":
                sage = 1.06;
                eage = 1.11;
                break;
            case "2":
                sage = 2;
                eage = 2.06;
                break;
            case "2.5":
                sage = 2.06;
                eage = 2.11;
                break;
            case "3":
                sage = 3;
                eage = 3.11;
                break;
            case "4":
                sage = 4;
                eage = 4.11;
                break;
            case "5":
                sage = 5;
                eage = 5.11;
                break;
            case "6":
                sage = 6;
                eage = 9.11;
                break;
        }
        objwhere.age = [sage, eage];
    }
    if (jQuery.getparent().ishospital()) {
        objwhere.fuyouorganid = [jQuery.getparent().objdata.my.organid || 0];
    }
    var status = $("#selstatus").val();
    if (status) {
        objwhere.status = [status];
    }
    return objwhere;
}

/*
功能：查询
*/
function btnsearch() {
    var option = {url: '', data: []};
    if (isHavaSearch() || objdata.istuoyou) {
        option = {
            url: encodeURI($.getLayUrl() + "?" + $.getSmStr(["psychologylist.table"])),
            page: {curr: 1}, where: {
                swhere: $.msgwhere(getswhere()),
                fields: 'managetime',
                types: '',
            }
        };
    }
    layui.table.reload('laytable', option);
}

/*
功能：添加
*/
function btnadd() {
    jQuery.getparent().layer.open({
        type: 2,
        title: "添加",
        shadeClose: false,
        area: ["800px", "80%"],
        content: 'html/zhuanan/psychologyadd2.html?v=' + Arg("v") + '&mid=' + Arg("mid"),
        btn: ["确定", "取消"],
        yes: function (index, layero) { //或者使用btn1
            var w = layero.find('iframe')[0].contentWindow;
            w.btsave(function (obj) {
                btnsearch();
                jQuery.getparent().layer.close(index);
            });
        }
    });
}