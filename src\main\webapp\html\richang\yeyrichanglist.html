﻿<!DOCTYPE html>
<html style="overflow:hidden;">
<head>
    <meta charset="utf-8" />
    <title>报表管理</title>
    <link rel="stylesheet" href="../../css/reset.css" />
    <link rel="stylesheet" href="../../plugin/zTree/css/zTreeStyle/zTreeStyle.css" type="text/css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" type="text/css" href="../../css/icon.css" />
    <link rel="stylesheet" type="text/css" href="../../css/style.css" />
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css" />
    <style>
        .agent-left { background: #eee; width: 250px; display: inline-block; font-size: 16px; position: absolute; height: 100%; overflow-y: auto; overflow-x: hidden; }
        .agent-right { vertical-align: top; font-size: 16px; margin-left: 250px; }
        .ztree li a { padding: 0px 20px 0 0; color: #333; width: 210px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
        .ico_docu { background-size: 20px 20px !important; }
		.ztree li a.curSelectedNode {height: 30px;}
		.ztree li {line-height: 30px;}
		.ztree li span { line-height: 30px;}
		.ztree {width: 215px;}
		.ztree li span.button.noline_docu{display: none;}
		.ztree li a.curSelectedNode{padding:0px 20px 0 10px;}
		.ztree li a{padding: 0px 20px 0 10px;}
		.ztree li span.button {margin-top: -3px;}
    </style>
</head>
<body>
    <div class="agent-left">
        <div id="divtree" style="overflow:auto;margin-top:10px;">
            <div id="treeDemo" class="ztree"></div>
        </div>
    </div>
    <div class="agent-right">
        <i style="position: relative; left: 10px; top: 0px; line-height: 25px; display: block;float: left;" id="icon_showhide" isnshow="1"><img src="../../images/image1/tbdown.png" style=" height: 15px;width: 13px; position: absolute; left: -2px;top: 2px; cursor: pointer;"></i>
        <div style=" background: #EAEFF3;">
            <p id="labtablename" style="margin:0 8px 0;background-color:#fff;padding: 10px 20px 0;font-size:18px;"></p>
        </div>
        <iframe id="iframe" src=""></iframe>
    </div>
    <script data-main="../../js/richang/yeyrichanglist" src="../../sys/require.min.js"></script>
</body>
</html>
