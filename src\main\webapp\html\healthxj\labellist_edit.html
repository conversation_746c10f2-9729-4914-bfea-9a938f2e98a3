<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>标签管理</title>
    <link rel="stylesheet" href="../../css/reset.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <style type="text/css">
        html, body {
            background: #fff;
            overflow: hidden;
        }
        .layui-form-label {
            width: 120px;
        }
        .layui-input-block {
            margin-left: 150px;
        }
		.layui-form .layui-form-item{ margin-bottom: 15px;}
		.form-display .layui-form-checkbox i {margin: 2px 0;}
		.layui-form-item .layui-form-checkbox[lay-skin="primary"] {height: 37px !important; padding-top: 10px; margin-top: 0;}
    </style>
</head>
<body>
    <div class="marmain" style="min-width: 800px;">
        <div class="content-medical">
            <form class="layui-form form-display" lay-filter="formMain" style="padding: 20px;">
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>标签名称：</label>
                    <div class="layui-input-block">
                        <input type="text" name="labelname" required lay-verify="required" placeholder="请输入" autocomplete="off" class="layui-input" maxlength="50">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>标签分类：</label>
                    <div class="layui-input-block">
                        <select name="category" lay-verify="required">
                            <option value="">请选择标签分类</option>
                            <option value="hw">身高体重</option>
                            <option value="eye">眼科</option>
                            <option value="listen">听力</option>
                            <option value="matin">血红蛋白</option>
                            <option value="car">口腔</option>
                            <option value="xf">胸部腹部</option>
                            <option value="neike">内科检查</option>
                            <option value="general">通用</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>算法类型：</label>
                    <div class="layui-input-block">
                        <select name="algorithm_type" lay-verify="required">
                            <option value="">请选择算法类型</option>
                            <option value="EXPRESSION">EXPRESSION</option>
                            <option value="SCRIPT">SCRIPT</option>
                            <option value="RULE">RULE</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">算法代码：</label>
                    <div class="layui-input-block">
                        <textarea name="algorithm_content" placeholder="请输入算法代码" class="layui-textarea" style="min-height: 120px;"></textarea>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">是否活动：</label>
                    <div class="layui-input-block">
                        <input type="checkbox" name="is_active" value="1" title="活动" lay-skin="primary">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">标签描述：</label>
                    <div class="layui-input-block">
                        <input type="text" name="labelremark" required lay-verify="required" placeholder="请输入" autocomplete="off" class="layui-input" maxlength="150">
                    </div>
                </div>
            </form>
        </div>
    </div>
    <script type="text/javascript">
        var v = top.version;
        document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../plugin/xm-select/xm-select.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../js/healthxj/labellist_edit.js?v=" + v + "'><" + "/script>");
    </script>
</body>
</html>