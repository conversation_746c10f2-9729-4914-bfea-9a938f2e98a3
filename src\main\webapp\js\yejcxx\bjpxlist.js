﻿/*
日期： 
作者： 
功能：班级排序
*/
require.config({
    paths: {
        system: "../../sys/system",
        jquery: "../../sys/jquery",
        jqueryui: "../../plugin/jquery-ui/jquery-ui.min"
    }, shim: {
        // contextmenu: ["jquery"]
    },
    waitSeconds: 0
});
var parentobj = null;
require(['jquery', 'system', 'jqueryui'],function() {
	parentobj = jQuery.getparent().$("#win_" + Arg('mid')).children('iframe')[0].contentWindow;
    initclassdata();
    $('#btnup').click(function () { oup() });
    $('#btndown').click(function () { odown() });
    $('#btnsave').click(save);
    $("#selcla").change(function (){
        initstudata();
    });
});
function oup() {
    var so = $("#selorderobj option:selected");
    if (so.get(0).index != 0) {
        so.each(function () {
            $(this).prev().before($(this));
        });
    }
};
function odown() {
    var alloptions = $("#selorderobj option");
    var so = $("#selorderobj option:selected");
    if (so.get(so.length - 1).index != alloptions.length - 1) {
        for (var i = so.length - 1; i >= 0; i--) {
            var item = $(so.get(i));
            item.insertAfter(item.next());
        }
    }
};

/*
功能：加载班级数据
*/
function initclassdata() {
    var objcla = parentobj.tongsetting.objcurclainfo;
    var t = 0;
    var arrstr = [];
    for (var i in objcla) {
        arrstr.push('<option value="' + i + '" title="' + objcla[i][0] + '" ' + (t == 0 ? 'selected="true"' : '') + '>' + objcla[i][0] + '</option>');
        t += 1;
        parentobj.tongsetting.objcurclainfo[i][2] = t;
    }
    $("#selcla").html(arrstr.join(''));
    initstudata();
}

/*
功能：加载幼儿数据
*/
function initstudata() {
    // if (Arg("type") == 2 || Arg("type") == 1) {//体检体测用
    //     var arrclass = []
    //     if (Arg("type") == "1") {
    //         arrclass = parentobj.getcurfrm()[0].contentWindow.tjsetting.arrclass;
    //     } else {
    //         arrclass = parentobj.getcurfrm()[0].contentWindow.tzsetting.arrclass;
    //     }
    //     var arrhtml = [];
    //     for (var i = 0; i < arrclass.length; i++) {
    //         arrhtml.push("<option  value='" + arrclass[i][1] + "' title='" + arrclass[i][0] + "'>" + arrclass[i][0] + "</option>");
    //     }
    //     $("#selorderobj").html(arrhtml.join(''));
    if(Arg("type") == "yesort"){//幼儿排序
        $("#selorderobj").css("width","270px");
        var classno = $("#selcla").val();
        var selyear = parseInt(parentobj.$("#sel_year").val());
        var intime = (Arg("gouptime") ? parentobj.getpreday(Arg("gouptime"), 1) : parentobj.tongsetting.cursysdate);
        var objorder = parentobj.getOrderWhere();
        $.sm(function (re, err) {
            if (err) {
                jQuery.getparent().layer.msg(err, { icon: 5 });
                jQuery.getparent().layer.closeAll('loading');
            } else if (re) {
                if(re.length > 0){
                    var arrhtml = [];
                    for (var i = 0; i < re.length; i++) {
                        arrhtml.push("<li id='" + re[i][0] + "' value='" + re[i][0] + "' title='" + re[i][1] + "'>" + re[i][1] + " (" + re[i][3] + "," + re[i][6] + ")</li>");
                    }
                    $("#selorderobj").html(arrhtml.join(''));
                }else{
                    $("#selorderobj").html("");
                }
                $("#selorderobj").sortable({
                    stop: function (event, ui) {//拖动结束后的事件
                        var curid = ui.item.attr("id"),//拖动元素的id
                            arr = $("#selorderobj").sortable('toArray');//拖动结束后的id数组
                        var idx = arr.indexOf(curid);
                        var curnextid = arr[idx + 1], curpreid = arr[idx - 1];//排序后拖拽元素后面的食物
                    }
                });
            }
        }, ["bjcylist.stugrid", $.msgwhere({classno:[classno]}), intime, $.msgwhere(objorder), (parentobj.getcurfrm()[0].contentWindow.bjcysetting.ny ? parentobj.tongsetting.cursysdate : parentobj.getpreday(parentobj.tongsetting.objgouptime[selyear + 1], 1)),$.msgwhere()]);
    } else {
        $("#selclass").hide();
        var objcla = parentobj.tongsetting.objcurclainfo;
        var t = 0;
        var arrstr = [];
        for (var i in objcla) {
            arrstr.push('<li classno="' + i + '" title="' + objcla[i][0] + '" ' + (t == 0 ? 'selected="true"' : '') + '>' + objcla[i][0] + '</li>');
            t += 1;
            parentobj.tongsetting.objcurclainfo[i][2] = t;
        }
        $("#selorderobj").html(arrstr.join(''));
        $("#selorderobj").sortable({
            stop: function (event, ui) {//拖动结束后的事件
                var curid = ui.item.attr("id"),//拖动元素的id
                    arr = $("#selorderobj").sortable('toArray');//拖动结束后的id数组
                var idx = arr.indexOf(curid);
                var curnextid = arr[idx + 1], curpreid = arr[idx - 1];//排序后拖拽元素后面的食物
            }
        });
    }
};
function save() {
	jQuery.getparent().layer.load();
    var arrpm = [];
    if(Arg("type") == "yesort"){//幼儿排序
        $('#selorderobj li').each(function (){
            var i = $(this).index();
            var t = i + 1;
            arrpm.push(["bjpxlist.stuname", t, $(this).attr("id")]);
        });
        // var arrye = $('#selorderobj')[0].options;
        // if (arrye) {
        //     for (var i = 0; i < arrye.length; i++) {
        //         var t = i + 1;
        //         arrpm.push(["bjpxlist.stuname", t, arrye[i].value]);
        //     }
        if(arrpm.length > 0){
            $.sm(function (re, err) {
                if (err) {
                    jQuery.getparent().layer.msg(err, {icon:5});
                } else if (re) {
                    // delete $.getparent().adminSetting.objtree['ek_class'];
                    var year = parseInt(parentobj.$("#sel_year").val());
                    if(year) parentobj.resetclatree(year, 'clatree');
                    jQuery.getparent().layer.msg("自定义排序设置成功！");
                    jQuery.getparent().layer.close(jQuery.getparent().objdata.bjpxindex);
                }
                jQuery.getparent().layer.closeAll('loading');
            }, arrpm);
        }else{
            jQuery.getparent().layer.msg("该班级没有需要排序的学生");
            jQuery.getparent().layer.closeAll('loading');
        }
        // }
    }else{//班级排序
        $('#selorderobj li').each(function (){
            var i = $(this).index();
            var classno = $(this).attr("classno");
            var t = i + 1;
            arrpm.push(["bjpxlist.class", t, classno, parentobj.tongsetting.objcurclainfo[classno][4]]);
            parentobj.tongsetting.objcurclainfo[classno][2] = t;
        });
        if (arrpm.length > 0) {
            $.sm(function (re, err) {
                if (err) {
                    jQuery.getparent().layer.msg(err, {icon:5});
                    jQuery.getparent().layer.closeAll('loading');
                } else if (re) {
                    delete $.getparent().adminSetting.objtree['ek_class'];
                    var year = parseInt(parentobj.$("#sel_year").val());
                    if(year) parentobj.resetclatree(year, 'clatree');
//                parentobj.openclatree(1);
                    jQuery.getparent().layer.msg("保存成功！");
                    jQuery.getparent().layer.closeAll('loading');
                    jQuery.getparent().layer.close(jQuery.getparent().objdata.bjpxindex);
                }
            }, arrpm);
        }else{
            jQuery.getparent().layer.msg("没有需要排序的班级");
            jQuery.getparent().layer.closeAll('loading');
        }
    }
};

