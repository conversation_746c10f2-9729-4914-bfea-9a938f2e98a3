body, html {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden; /* 防止滚动条出现，如果图片比视口小 */
}
.jc_content {
    width: 60%;
    height: 95%;
    margin: 0 auto;
    /*background-image: url(../../images/prem/marriedcheck.png/);*/
    color: rgba(1, 1, 1, 1);
    border-top: none;
    padding: 10px 0 1px 0;
    background-repeat: no-repeat;
    background-size: cover;
}
.report1{
    position:absolute;
}

.report1 .birthday{
    position:absolute; /** 相对父级的位置来定位**/
    top: 120px;
    left: 130px;
    width: 200px;
}

.report1 .occupation{
    position:absolute; /** 相对父级的位置来定位**/
    top: 190px;
    left: 95px;
    width: 200px;
}

.report1 .doctor_name{
    position:absolute; /** 相对父级的位置来定位**/
    top: 570px;
    left: 105px;
    width: 200px;
}

.report1 .time{
    position:absolute; /** 相对父级的位置来定位**/
    top: 595px;
    left: 280px;
    width: 200px;
}
.report1 .hospital_address{
    position:absolute; /** 相对父级的位置来定位**/
    top: 230px;
    left: 105px;
    width: 200px;
}
.report1 .hospital_fulladdress{
    position:absolute; /** 相对父级的位置来定位**/
    top: 230px;
    left: 255px;
    width: 100px;
}

.report1 .ethnic{
    position:absolute; /** 相对父级的位置来定位**/
    top: 115px;
    left: 270px;
    width: 200px;
}
.report1 .premcheck_result{
    position:absolute; /** 相对父级的位置来定位**/
    top: 390px;
    left: 45px;
    width: 300px;
}

.report1 .other_name{
    position:absolute; /** 相对父级的位置来定位**/
    top: 265px;
    left: 100px;
    width: 200px;
}
.report1 .idcard{
    position:absolute; /** 相对父级的位置来定位**/
    top: 156px;
    left: 103px;
    width: 200px;
}

.report1 .name{
    position:absolute; /** 相对父级的位置来定位**/
    top: 80px;
    left: 103px;
    width: 200px;
}
.report1 .sex{
    position:absolute; /** 相对父级的位置来定位**/
    top: 80px;
    left: 270px;
    width: 100px;
}
.report1 .premcheck_id{
    position:absolute; /** 相对父级的位置来定位**/
    top: 50px;
    left: 260px;
    width: 200px;
}
.report2{
    position:absolute;
    left: 765px;
}

.report2 .birthday{
    position:absolute; /** 相对父级的位置来定位**/
    top: 120px;
    left: 130px;
    width: 200px;
}

.report2 .occupation{
    position:absolute; /** 相对父级的位置来定位**/
    top: 190px;
    left: 95px;
    width: 200px;
}

.report2 .doctor_name{
    position:absolute; /** 相对父级的位置来定位**/
    top: 570px;
    left: 105px;
    width: 200px;
}

.report2 .time{
    position:absolute; /** 相对父级的位置来定位**/
    top: 595px;
    left: 280px;
    width: 200px;
}
.report2 .hospital_address{
    position:absolute; /** 相对父级的位置来定位**/
    top: 230px;
    left: 105px;
    width: 200px;
}
.report2 .hospital_fulladdress{
    position:absolute; /** 相对父级的位置来定位**/
    top: 230px;
    left: 255px;
    width: 100px;
}

.report2 .ethnic{
    position:absolute; /** 相对父级的位置来定位**/
    top: 115px;
    left: 270px;
    width: 200px;
}
.report2 .premcheck_result{
    position:absolute; /** 相对父级的位置来定位**/
    top: 390px;
    left: 45px;
    width: 300px;
}

.report2 .other_name{
    position:absolute; /** 相对父级的位置来定位**/
    top: 265px;
    left: 100px;
    width: 200px;
}
.report2 .idcard{
    position:absolute; /** 相对父级的位置来定位**/
    top: 156px;
    left: 103px;
    width: 200px;
}

.report2 .name{
    position:absolute; /** 相对父级的位置来定位**/
    top: 80px;
    left: 103px;
    width: 200px;
}
.report2 .sex{
    position:absolute; /** 相对父级的位置来定位**/
    top: 80px;
    left: 270px;
    width: 100px;
}
.report2 .premcheck_id{
    position:absolute; /** 相对父级的位置来定位**/
    top: 50px;
    left: 260px;
    width: 200px;
}

.report3{
    position:absolute;
    left: 1155px;
}

.report3 .birthday{
    position:absolute; /** 相对父级的位置来定位**/
    top: 120px;
    left: 130px;
    width: 200px;
}

.report3 .occupation{
    position:absolute; /** 相对父级的位置来定位**/
    top: 190px;
    left: 95px;
    width: 200px;
}

.report3 .doctor_name{
    position:absolute; /** 相对父级的位置来定位**/
    top: 570px;
    left: 105px;
    width: 200px;
}

.report3 .time{
    position:absolute; /** 相对父级的位置来定位**/
    top: 595px;
    left: 280px;
    width: 200px;
}
.report3 .hospital_address{
    position:absolute; /** 相对父级的位置来定位**/
    top: 230px;
    left: 105px;
    width: 200px;
}
.report3 .hospital_fulladdress{
    position:absolute; /** 相对父级的位置来定位**/
    top: 230px;
    left: 255px;
    width: 100px;
}

.report3 .ethnic{
    position:absolute; /** 相对父级的位置来定位**/
    top: 115px;
    left: 270px;
    width: 200px;
}
.report3 .premcheck_result{
    position:absolute; /** 相对父级的位置来定位**/
    top: 390px;
    left: 45px;
    width: 300px;
}

.report3 .other_name{
    position:absolute; /** 相对父级的位置来定位**/
    top: 265px;
    left: 100px;
    width: 200px;
}
.report3 .idcard{
    position:absolute; /** 相对父级的位置来定位**/
    top: 156px;
    left: 103px;
    width: 200px;
}

.report3 .name{
    position:absolute; /** 相对父级的位置来定位**/
    top: 80px;
    left: 103px;
    width: 200px;
}
.report3 .sex{
    position:absolute; /** 相对父级的位置来定位**/
    top: 80px;
    left: 270px;
    width: 100px;
}
.report3 .premcheck_id{
    position:absolute; /** 相对父级的位置来定位**/
    top: 50px;
    left: 260px;
    width: 200px;
}