<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>家长分享数据详情</title>
    <link rel="stylesheet" type="text/css" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" type="text/css" href="../../css/icon.css"/>
    <link rel="stylesheet" type="text/css" href="../../styles/tbstyles.css"/>
    <link href="../../plugin/flexigrid/css/tbflexigrid.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css"/>
    <script type="text/javascript">
        // 检查 top.objdata 是否存在，如果不存在则设置默认主题
        var curtheme = (top && top.objdata && top.objdata.curtheme) ? top.objdata.curtheme : 'backstage';
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + curtheme + '.css" type="text/css" media="screen"/>');
    </script>

    <style type="text/css">
        html, body {
            background: #EAEFF3;
            overflow: hidden;
        }
        .layui-form-item {
            display: inline-block;
            vertical-align: top;
            margin: 5px 5px 0px 0;
        }
        .layui-form-label {
            line-height: 28px;
        }
    </style>
</head>
<body>
<div class="marmain">
    <div class="content-medical">
        <div class="layui-form layui-comselect" style="padding:10px;">
            <!-- 查询条件区域 -->
            <div class="layui-form-item" style="margin-bottom: 10px;">
                <div class="layui-input-inline" style="width:120px; margin-right: 5px;">
                    <input id="datestart" type="text" autocomplete="off" placeholder="开始日期" class="layui-input">
                </div>
                <div class="layui-form-mid">至</div>
                <div class="layui-input-inline" style="width:120px; margin-right: 20px;">
                    <input id="dateend" type="text" autocomplete="off" placeholder="结束日期" class="layui-input">
                </div>

                <label class="layui-form-label" style="width: 80px;">预约科室：</label>
                <div class="layui-input-inline" style="width:120px; margin-right: 15px;">
                    <select id="department" lay-filter="department">
                        <option value="">请选择</option>
                    </select>
                </div>

                <label class="layui-form-label" style="width: 80px;">预约套餐：</label>
                <div class="layui-input-inline" style="width:120px; margin-right: 15px;">
                    <select id="package" lay-filter="package">
                        <option value="">请选择</option>
                    </select>
                </div>

                <label class="layui-form-label" style="width: 100px;">是否取消预约：</label>
                <div class="layui-input-inline" style="width:120px; margin-right: 15px;">
                    <select id="isCancelled" lay-filter="isCancelled">
                        <option value="">请选择</option>
                        <option value="1">是</option>
                        <option value="0">否</option>
                    </select>
                </div>
            </div>

            <!-- 第二行查询条件 -->
            <div class="layui-form-item" style="margin-bottom: 10px;">
                <label class="layui-form-label" style="width: 80px;">家长性别：</label>
                <div class="layui-input-inline" style="width:120px; margin-right: 15px;">
                    <select id="parentSex" lay-filter="parentSex">
                        <option value="">请选择</option>
                        <option value="男">男</option>
                        <option value="女">女</option>
                    </select>
                </div>

                <label class="layui-form-label" style="width: 80px;">家长年龄：</label>
                <div class="layui-input-inline" style="width:120px; margin-right: 15px;">
                    <select id="parentAge" lay-filter="parentAge">
                        <option value="">请选择</option>
                        <option value="20-30">20-30岁</option>
                        <option value="31-40">31-40岁</option>
                        <option value="41-50">41-50岁</option>
                        <option value="51-60">51-60岁</option>
                    </select>
                </div>

                <label class="layui-form-label" style="width: 80px;">访问设备：</label>
                <div class="layui-input-inline" style="width:120px; margin-right: 15px;">
                    <select id="device" lay-filter="device">
                        <option value="">请选择</option>
                        <option value="安卓">安卓</option>
                        <option value="苹果">苹果</option>
                    </select>
                </div>

                <label class="layui-form-label" style="width: 80px;">区域分布：</label>
                <div class="layui-input-inline" style="width:120px; margin-right: 15px;">
                    <select id="region" lay-filter="region">
                        <option value="">请选择</option>
                    </select>
                </div>

                <label class="layui-form-label" style="width: 120px;">孩子与家长关系：</label>
                <div class="layui-input-inline" style="width:120px; margin-right: 15px;">
                    <select id="relationship" lay-filter="relationship">
                        <option value="">请选择</option>
                        <option value="爸爸">爸爸</option>
                        <option value="妈妈">妈妈</option>
                        <option value="爷爷">爷爷</option>
                        <option value="奶奶">奶奶</option>
                    </select>
                </div>
            </div>

            <!-- 第三行 -->
            <div class="layui-form-item" style="margin-bottom: 10px;">
                <label class="layui-form-label" style="width: 60px;">关键字：</label>
                <div class="layui-input-inline" style="width:200px; margin-right: 20px;">
                    <input id="keyword" type="text" autocomplete="off" placeholder="请输入幼儿姓名或家长姓名" class="layui-input">
                </div>
                <button class="layui-btn form-search" style="margin-left: 10px;" id="btnsearch">查询</button>
            </div>
        </div>
    </div>
    <div id="content" style="width: 100%;">
        <div class="marmain-cen">
            <div class="content-medical" id="divtable">
                <table id="laytable" lay-filter="laytable"></table>
            </div>
        </div>
    </div>
</div>

<script data-main="../../js/operation/w_parentShare" src='../../sys/require.min.js'></script>
</body>
</html> 