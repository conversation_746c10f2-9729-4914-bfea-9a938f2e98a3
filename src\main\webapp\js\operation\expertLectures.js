/*新增日期: 2025.06.16
作 者: 朱辽原
修改时间：
修改人：
內容摘要: 专家讲座大数据功能
*/
require.config({
    paths: {
        system: '../../sys/system',
        jquery: '../../sys/jquery',
        echarts: '../../plugin/echart-5.1.2/dist/echarts.min',
        echartsgl: '../../plugin/echarts-gl/dist/echarts-gl',
        layui: '../../layui-btkj/layui',
        expertLecturesData: './expertLecturesData',
        common: './common',
        commonUtils: './commonUtils'
    },
    shim: {
        system: { deps: ['jquery'] },
        layui: { deps: ['jquery', 'system'] },
        echarts: { deps: ['jquery', 'system'] },
        echartsgl: { deps: ['echarts'] }
    },
    waitSeconds: 0
})

const openMock = false

const baseModule = ['jquery', 'echarts', 'common', "commonUtils", 'layui', 'system', 'echartsgl']

if (openMock) {
    baseModule.push('expertLecturesData')
}

require(baseModule, ($, echarts, common, utils) => {

    const openLog = false

    /**
     * 当前页面展示数据的时间段
     */
    let pageDate = () => {
        const endDate = utils.getFormattedDate()
        const startDate = utils.formatDate(new Date((new Date(endDate)).getTime() - 7 * 24 * 60 * 60 * 1000));
        return { startDate, endDate }
    }
    pageDate = pageDate()

    const drawSpaceMap = {
        noticeReach: $('#noticeReach')[0], // 通知触达情况
        userOpen: $('#userOpen')[0], // 用户打开趋势
        registrationTime: $('#registrationTime')[0], // 报名时段分布
        appointmentTime: $('#appointmentTime')[0], // 预约时间分布
        sceneListenConvter: $('#appointmentTime')[0], // 现场听讲转化
        parentGender: $('#parentGender')[0], // 家长性别
        parentDevice: $('#parentDevice')[0], // 家长设备
        parentAge: $('#parentAge')[0], // 家长年龄
        parentRegion: $('#parentRegion')[0], // 区域分布
        parentChildRelation: $('#parentChildRelation')[0], // 家长与孩子关系
        childrenGender: $('#childrenGender')[0], // 儿童性别分布
        childrenAge: $('#childrenAge')[0] // 儿童年龄分布
    }

    const numPeoMap = {
        lecturesNumber: $('#lecturesNumber'), // 专家讲座数
        touchRate: $('#touchRate'), // 触达率
        viewNumber: $('#viewNumber'), // 查看人数
        registrationNumber: $('#registrationNumber'), // 报名人数
        signNumber: $('#signNumber') // 签到人数
    }

    /**
     * 绑定事件
     */
    numPeoMap.lecturesNumber.on('click', () => {
        var lecturesNumber = numPeoMap.lecturesNumber.text()
        utils.pageJump('expertLecturesInfo.html', {
            startDate: pageDate.startDate,
            endDate: pageDate.endDate,
            expertLectureCount: lecturesNumber
        }, '专家讲座数据详细')
    })

    numPeoMap.touchRate.on('click', () => {
        var lecturesNumber = numPeoMap.lecturesNumber.text()
        utils.pageJump('expertLecturesInfo.html', {
            startDate: pageDate.startDate,
            endDate: pageDate.endDate,
            expertLectureCount: lecturesNumber
        }, '专家讲座数据详细')
    })

    numPeoMap.viewNumber.on('click', () => {
        var lecturesNumber = numPeoMap.lecturesNumber.text()
        utils.pageJump('expertLecturesInfo_person.html', {
            startDate: pageDate.startDate,
            endDate: pageDate.endDate,
            expertLectureCount: lecturesNumber,
            count: numPeoMap.viewNumber.text(),
            isSuccess: '1'
        }, '专家讲座数据详细')
    })

    numPeoMap.registrationNumber.on('click', () => {
        var lecturesNumber = numPeoMap.lecturesNumber.text()
        utils.pageJump('expertLecturesInfo_person.html', {
            startDate: pageDate.startDate,
            endDate: pageDate.endDate,
            expertLectureCount: lecturesNumber,
            count: numPeoMap.registrationNumber.text(),
            isRegister: '1'
        }, '专家讲座数据详细')
    })

    numPeoMap.signNumber.on('click', () => {
        var lecturesNumber = numPeoMap.lecturesNumber.text()
        utils.pageJump('expertLecturesInfo_person.html', {
            startDate: pageDate.startDate,
            endDate: pageDate.endDate,
            expertLectureCount: lecturesNumber,
            count: numPeoMap.signNumber.text(),
            isSign: '1'
        }, '专家讲座数据详细')
    })

    /*
    功能：渲染统计卡片
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderStats(data) {
        // 渲染统计项
        for (const key in numPeoMap) {
            numPeoMap[key].text(data[key])
        }
    }

    /*
    功能：渲染通知触达情况饼图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderNoticeReach(data) {
        utils.logWithCondition(openLog, new Error().stack, data);

        const itemStyleMap = {
            成功触达: {
                color: '#1fe6a3'
            },
            未触达: {
                color: '#ffd15d'
            }
        }
        // 使用common模块的get3dPie函数生成3D饼图配置
        const options = {
            showLegend: false,
            distance: 200,
            boxHeight: 0.02,
        }

        data.map(item => (item.itemStyle = itemStyleMap[item.name]))

        var myChart = common.create3DPie(drawSpaceMap['noticeReach'].id, data, options)
        myChart.off('click');
        myChart.on('click', e => {
            utils.logWithCondition(openLog, new Error().stack, e);
            var lecturesNumber = numPeoMap.lecturesNumber.text()
            var params = {
                startDate: pageDate.startDate,
                endDate: pageDate.endDate,
                expertLectureCount: lecturesNumber,
            }
            if (e.seriesName === '未触达') {
                params.failCount = data[e.componentIndex].value
            } else if (e.seriesName === '成功触达') {
                params.successCount = data[e.componentIndex].value
            }
            utils.pageJump('expertLecturesInfo.html', params, '专家讲座数据详细')
        })
    }

    /*
    功能：渲染用户打开趋势折线图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderUserOpen(data) {
        var myChart = echarts.init(drawSpaceMap['userOpen'], null, { renderer: 'div' });
        utils.logWithCondition(openLog, new Error().stack, data)
        const days = data.xAxis
        const trendData = data.yAxis

        const option = {
            xAxis: {
                type: 'category',
                data: days,
                boundaryGap: false, // 使折线从坐标轴起点开始
                name: '日期'
            },
            yAxis: {
                type: 'value',
                // min: 0,
                // max: 220, // 可根据数据调整最大值，让趋势更清晰
                // interval: 50, // 坐标轴间隔
                name: '人数'
            },
            dataZoom: [
                {
                    type: 'inside',
                    start: 0,
                    end: days.length > 7 ? 100 * 7 / days.length : 100, // 默认显示7天
                    zoomLock: false
                },
                {
                    type: 'slider',
                    start: 0,
                    end: days.length > 7 ? 100 * 7 / days.length : 100,
                    handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
                    handleSize: '80%',
                    handleStyle: {
                        color: '#fff',
                        shadowBlur: 3,
                        shadowColor: 'rgba(0, 0, 0, 0.6)',
                        shadowOffsetX: 2,
                        shadowOffsetY: 2
                    }
                }
            ],
            series: [
                {
                    name: '用户打开数量',
                    type: 'line',
                    data: trendData,
                    smooth: true, // 开启平滑曲线
                    label: {
                        show: true, // 显示数据点数值
                        position: 'top'
                    },
                    lineStyle: {
                        color: '#2ea875' // 折线颜色
                    },
                    itemStyle: {
                        color: '#2ea875' // 数据点颜色
                    },
                    areaStyle: {
                        // 区域阴影配置
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [
                                {
                                    offset: 0,
                                    color: '#abffdc' // 起始透明度
                                },
                                {
                                    offset: 1,
                                    color: '#fff' // 结束透明度
                                }
                            ]
                        }
                    },
                    emphasis: {
                        lineStyle: {
                            width: 4
                        },
                        symbolSize: 8
                    }
                }
            ]
        }
        myChart.setOption(option)
        myChart.off('click');
        myChart.on('click', e => {
            var lecturesNumber = numPeoMap.lecturesNumber.text()
            utils.pageJump('expertLecturesInfo_person.html', {
                startDate: e.name,
                endDate: e.name,
                isSuccess: '1',
                expertLectureCount: lecturesNumber,
                count: e.value
            }, '专家讲座数据详细')
        })
    }

    /*
    功能：渲染报名时段分布折线图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderRegistrationTimeTime(data) {
        var myChart = echarts.init(drawSpaceMap['registrationTime'], null, { renderer: 'div' });
        utils.logWithCondition(openLog, new Error().stack, data);

        const option = {
            xAxis: {
                type: 'category',
                data: data.xAxis,
                boundaryGap: false, // 使折线从坐标轴起点开始
                name: '时间'
            },
            yAxis: {
                type: 'value',
                min: 0,
                max: 55, // 可根据数据调整最大值，适配峰值展示
                interval: 10, // 坐标轴间隔
                name: '人数'
            },
            dataZoom: [
                {
                    type: 'inside',
                    start: 0,
                    end: data.xAxis.length > 7 ? 100 * 7 / data.xAxis.length : 100,
                    zoomLock: false
                },
                {
                    type: 'slider',
                    start: 0,
                    end: data.xAxis.length > 7 ? 100 * 7 / data.xAxis.length : 100,
                    handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
                    handleSize: '80%',
                    handleStyle: {
                        color: '#fff',
                        shadowBlur: 3,
                        shadowColor: 'rgba(0, 0, 0, 0.6)',
                        shadowOffsetX: 2,
                        shadowOffsetY: 2
                    }
                }
            ],
            series: [
                {
                    name: '报名数量',
                    type: 'line',
                    data: data.yAxis,
                    smooth: true, // 开启平滑曲线
                    label: {
                        show: true, // 显示数据点数值
                        position: 'top'
                    },
                    lineStyle: {
                        color: '#2ea875' // 折线颜色
                    },
                    itemStyle: {
                        color: '#2ea875' // 数据点颜色
                    },
                    areaStyle: {
                        // 区域阴影配置
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [
                                {
                                    offset: 0,
                                    color: '#abffdc' // 起始透明度
                                },
                                {
                                    offset: 1,
                                    color: '#fff' // 结束透明度
                                }
                            ]
                        }
                    },
                    emphasis: {
                        lineStyle: {
                            width: 4
                        },
                        symbolSize: 8
                    }
                }
            ]
        }
        myChart.setOption(option)
        myChart.off('click');
        myChart.on('click', e => {
            let startTime = e.name.split('-')[0]
            let endTime = e.name.split('-')[1]
            utils.pageJump('expertLecturesInfo_person.html', {
                startDate: pageDate.startDate,
                endDate: pageDate.endDate,
                startTime: `${startTime}:00`,
                endTime: `${endTime}:00`,
                count: e.value
            }, '专家讲座数据详细')
        })
    }

    /*
    功能：渲染现场听讲转化漏斗图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderSceneListenConvter(data) {
        // 配置Highcharts图表
        Highcharts.chart(drawSpaceMap['sceneListenConvter'], {
            accessibility: {
                enabled: false  // 关闭无障碍功能及警告
            },
            credits: {
                enabled: false
            },
            chart: {
                type: 'funnel3d',
                options3d: {
                    enabled: true,
                    alpha: 10,
                    depth: 100
                }
            },
            title: {
                text: ''
            },
            plotOptions: {
                series: {
                    // 数据标签配置
                    dataLabels: {
                        connectorWidth: 1,
                        distance: 10,
                        enabled: true,
                        format: '<b>{point.name}</b>',
                        color: 'green'
                    },
                    // 漏斗形状配置
                    neckWidth: '10%',
                    neckHeight: '0%',
                    width: '50%',
                    height: '80%',
                    point: {
                        events: {
                            click: function (event) {
                                // 这里处理点击事件
                                utils.logWithCondition(openLog, new Error().stack, '点击了:', this.name, '值:', this.y);
                                jumpParam = {
                                    startDate: pageDate.startDate,
                                    endDate: pageDate.endDate,
                                    count: this.y
                                }
                                switch (this.name) {
                                    case '打开通知':
                                        jumpParam.isSuccess = '1'
                                        break
                                    case '报名讲座':
                                        jumpParam.isRegister = '1'
                                        break
                                    case '现场签到':
                                        jumpParam.isSign = '1'
                                        break
                                }
                                utils.pageJump('expertLecturesInfo_person.html', jumpParam, '专家讲座数据详细')
                            }
                        }
                    }
                }
            },
            series: [
                {
                    borderColor: '#fff',
                    data: data.map(item => ({
                        name: item.name,
                        y: item.value,
                        opacity: 0.2
                    }))
                }
            ]
        })
    }

    /*
    功能：渲染家长性别分布饼图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderGenderChart(data) {
        const itemStyleMap = {
            男性: {
                color: '#fea131'
            },
            女性: {
                color: '#f54d48'
            }
        }
        // 使用common模块的get3dPie函数生成3D饼图配置
        const options = {
            title: '性别',
        }
        data.map(item => (item.itemStyle = itemStyleMap[item.name]))
        var myChart = common.create3DPie(drawSpaceMap['parentGender'].id, data, options)
        myChart.off('click');
        myChart.on('click', e => {
            let gender = '1'
            if (e.seriesName === '女性') {
                gender = '2'
            }
            utils.pageJump('expertLecturesInfo_person.html', {
                startDate: pageDate.startDate,
                endDate: pageDate.endDate,
                parentGender: gender,
                count: data[e.componentIndex].value
            }, '专家讲座数据详细')
        })
    }

    /*
    功能：渲染访问设备分布饼图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderDeviceChart(data) {
        const itemStyleMap = {
            安卓: {
                color: '#fea033'
            },
            iOS: {
                color: '#8485fe'
            },
            未知: {
                color: '#f54d48'
            }
        }
        // 使用common模块的get3dPie函数生成3D饼图配置
        const options = {
            title: '访问设备',
        }
        data.map(item => (item.itemStyle = itemStyleMap[item.name]))
        var deviceChart = common.create3DPie(drawSpaceMap['parentDevice'].id, data, options)
        deviceChart.off('click');
        deviceChart.on('click', e => {
            utils.pageJump('expertLecturesInfo_person.html', {
                startDate: pageDate.startDate,
                endDate: pageDate.endDate,
                deviceType: e.seriesName === 'iOS' ? '苹果' : e.seriesName,
                count: data[e.componentIndex].value
            }, '专家讲座数据详细')
        })
    }

    /*
    功能：渲染家长年龄分布饼图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderAgeChart(data) {
        const itemStyleMap = {
            '18岁到25岁': {
                color: '#ffd15d'
            },
            '26岁到35岁': {
                color: '#08d0fd'
            },
            '36岁到45岁': {
                color: '#20e8a4'
            },
            '46岁到60岁': {
                color: '#0883fe'
            },
            '60岁以上': {
                color: '#f54e46'
            },
            未知: {
                color: '#8583fe'
            }
        }
        // 使用common模块的get3dPie函数生成3D饼图配置
        const options = {
            title: '年龄分布',
            showLegend: false,
            distance: 200,
            boxHeight: 0.02,
        }
        data.map(item => (item.itemStyle = itemStyleMap[item.name]))
        const ageChart = common.create3DPie(drawSpaceMap['parentAge'].id, data, options)
        ageChart.off('click')
        ageChart.on('click', e => {
            // utils.logWithCondition(openLog, new Error().stack, e.name, e.value)
            utils.pageJump('expertLecturesInfo_person.html', {
                startDate: pageDate.startDate,
                endDate: pageDate.endDate,
                parentAge: e.seriesName[0],
                count: data[e.componentIndex].value
            }, '专家讲座数据详细')
        })
    }

    /*
    功能：渲染家长区域分布雷达图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderRegionChart(data) {
        const regionChart = echarts.init(drawSpaceMap['parentRegion'], null, { renderer: 'div' })
        try {
            const indicator = []
            for (var i = 0; i < 18; i++) {
                indicator.push({
                    name: '',
                    max: 100
                })
            }

            const option = {
                // backgroundColor: "rgba(114, 184, 255, 0.5)",
                title: {
                    text: '区域分布',
                    left: '5px',
                    top: '5px',
                    textStyle: {
                        color: '#333',
                        fontSize: 14
                    }
                },
                grid: {
                    top: '10%'
                },
                tooltip: {
                    show: true,
                    trigger: 'item',
                    // 自定义tooltip内容
                    formatter: function (params) {
                        // utils.logWithCondition(openLog, new Error().stack, data.areas[params.componentIndex])
                        // utils.logWithCondition(openLog, new Error().stack, params.componentIndex)

                        return `家长数：${data.areas[params.componentIndex].parentCount}<br>
								幼儿数：${data.areas[params.componentIndex].childCount}`
                    }
                },
                radar: {
                    // 雷达图起始角度（关键配置）
                    startAngle: 180, // 从0度开始（默认是90度，即从正上方开始）
                    splitNumber: 3,
                    center: ['50%', '50%'],
                    radius: '70%',
                    shape: 'circle',
                    splitArea: {
                        show: true,
                        areaStyle: {
                            // 渐变背景色，从中心向外扩散
                            color: [
                                'rgba(114, 184, 255, 0.8)', // 最内部区域
                                'rgba(114, 184, 255, 0.6)', // 第二层
                                'rgba(114, 184, 255, 0.4)' // 第三层
                            ]
                        }
                    },
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(114, 184, 255)'
                        }
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: [
                                'rgba(114, 184, 255, 0.5)', // 最内部区域
                                'rgba(114, 184, 255, 0.5)', // 第二层
                                'rgba(114, 184, 255, 0.4)' // 第三层
                            ]
                        }
                    },
                    indicator
                },
                series: [
                    {
                        type: 'radar',
                        symbol: 'circle',
                        symbolSize: 6,
                        itemStyle: {
                            normal: {}
                        },
                        areaStyle: {
                            normal: {
                                color: 'rgba(114, 184, 255, 0.5)',
                                opacity: 0.2
                            }
                        },
                        lineStyle: {
                            normal: {}
                        },
                        label: {
                            normal: {
                                show: true,
                                color: '#a4a4a4',
                                position: 'top',
                                fontSize: 12,
                                fontWeight: 200,
                                formatter: params => {
                                    return '1KM'
                                }
                            }
                        },
                        data: [33]
                    },
                    {
                        type: 'radar',
                        symbol: 'circle',
                        symbolSize: 6,
                        itemStyle: {
                            normal: {}
                        },
                        areaStyle: {
                            normal: {
                                color: 'rgba(114, 184, 255, 0.5)'
                            }
                        },
                        lineStyle: {},
                        label: {
                            normal: {
                                show: true,
                                color: '#a4a4a4',
                                position: 'top',
                                fontSize: 13,
                                fontWeight: 200,
                                formatter: params => {
                                    return '3KM'
                                }
                            }
                        },
                        data: [66]
                    },
                    {
                        type: 'radar',
                        symbol: 'circle',
                        symbolSize: 6,
                        itemStyle: {
                            normal: {}
                        },
                        areaStyle: {
                            normal: {
                                color: 'rgba(114, 184, 255, 0.5)'
                            }
                        },
                        lineStyle: {
                            normal: {}
                        },
                        label: {
                            normal: {
                                show: true,
                                color: '#a4a4a4',
                                position: 'top',
                                fontSize: 15,
                                fontWeight: 200,
                                formatter: params => {
                                    return '5KM'
                                }
                            }
                        },
                        data: [99]
                    }
                ]
            }

            // 设置配置项并渲染图表
            regionChart.setOption(option)
            regionChart.on('click', e => {
                utils.logWithCondition(openLog, new Error().stack, e)
                // utils.logWithCondition(openLog, new Error().stack, e.componentIndex)
                utils.pageJump('expertLecturesInfo_person.html', {
                    startDate: pageDate.startDate,
                    endDate: pageDate.endDate,
                    areaDistribution: e.componentIndex + 1,
                    count: +data.areas[e.componentIndex].parentCount + +data.areas[e.componentIndex].childCount
                }, '专家讲座数据详细')
            })
        } catch (e) {
            console.error('初始化图表失败:', e)
        }
    }

    /*
    功能：渲染孩子与家长关系柱状图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderChildParentRelationChart(data) {
        // 初始化 ECharts 实例
        const myChart = echarts.init(drawSpaceMap['parentChildRelation'], null, { renderer: 'div' })

        var colorConfig = [
            ['#42A5F5'], // 顶面：蓝色
            ['#1976D2', 'rgba(25,118,210,0.3)'], // 左侧面：深蓝渐变
            ['#64B5F6', 'rgba(100,181,246,0.3)'] // 右侧面：浅蓝渐变
        ]

        var options = {
            labelRotate: 45,
            labelInterval: 0
        }
        var option = common.getBar3D(data.xAxis, data.data, 'Relation', colorConfig, options)
        myChart.setOption(option)
        myChart.off('click')
        myChart.on('click', e => {
            // utils.logWithCondition(openLog, new Error().stack, e.name, e.value)
            utils.logWithCondition(openLog, new Error().stack, e)
            utils.pageJump('expertLecturesInfo_person.html', {
                startDate: pageDate.startDate,
                endDate: pageDate.endDate,
                parentRelation: e.dataIndex + 1,
                count: e.value
            }, '专家讲座数据详细')
        })
    }

    /*
    功能：渲染孩子性别分布饼图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderChildGenderChart(data) {
        const itemStyleMap = {
            男性: {
                color: '#fea131'
            },
            女性: {
                color: '#f54d48'
            }
        }
        // 使用common模块的get3dPie函数生成3D饼图配置
        const options = {
            title: '性别',
        }
        data.map(item => (item.itemStyle = itemStyleMap[item.name]))
        const myChart = common.create3DPie(drawSpaceMap['childrenGender'].id, data, options)
        myChart.off('click')
        myChart.on('click', e => {
            let childGender = '1'
            if (e.seriesName == '女性') {
                childGender = '2'
            }
            utils.pageJump('expertLecturesInfo_person.html', {
                startDate: pageDate.startDate,
                endDate: pageDate.endDate,
                childGender,
                count: Math.floor(data[e.componentIndex].value)
            }, '专家讲座数据详细')

        })
    }

    /*
    功能：渲染孩子年龄分布饼图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderChildAgeChart(data) {
        const itemStyleMap = {
            '3岁': {
                color: '#ffd15d'
            },
            '4岁': {
                color: '#08d0fd'
            },
            '5岁': {
                color: '#20e8a4'
            },
            '6岁': {
                color: '#0883fe'
            },
            '7岁': {
                color: '#f54e46'
            }
        }
        // 使用common模块的get3dPie函数生成3D饼图配置
        const options = {
            title: '年龄分布',
        }
        data.map(item => (item.itemStyle = itemStyleMap[item.name]))
        const ageChart = common.create3DPie(drawSpaceMap['childrenAge'].id, data, options)
        ageChart.on('click', e => {
            utils.pageJump('expertLecturesInfo_person.html', {
                startDate: pageDate.startDate,
                endDate: pageDate.endDate,
                physicalAge: e.seriesName[0],
                count: data[e.componentIndex].value
            }, '专家讲座数据详细')
        })
    }

    // 页面初始化
    layui.use(['form', 'laydate'], function () {
        laydate = layui.laydate
        const param = {
            startDate: pageDate.startDate,
            endDate: pageDate.endDate
        }

        const tasks = [
            () => utils.smactionMockData('expertLectures/stats', param, renderStats), // 统计卡
            () => utils.smactionMockData('expertLectures/reachData', param, renderNoticeReach), // 科室预约量
            () => utils.smactionMockData('expertLectures/openTrendData', param, renderUserOpen), // 用户打开趋势
            () => utils.smactionMockData('expertLectures/registerTimeData', param, renderRegistrationTimeTime), // 报名时段分布
            () => utils.smactionMockData('expertLectures/funnelData', param, renderSceneListenConvter), // 现场听讲转化
            // () => utils.smactionMockData('expertLectures/gender', param, renderGenderChart), // 家长性别分布
            // () => utils.smactionMockData('expertLectures/age', param, renderAgeChart), // 年龄分布
            // () => utils.smactionMockData('expertLectures/device', param, renderDeviceChart), // 设备分布
            // () => utils.smactionMockData('expertLectures/region', param, renderRegionChart), // 区域分布
            // () => utils.smactionMockData('expertLectures/relationship', param, renderChildParentRelationChart), // 孩子与家长关系
            // () => utils.smactionMockData('expertLectures/gender', param, renderChildGenderChart), // 孩子性别分布
            // () => utils.smactionMockData('expertLectures/childAge', param, renderChildAgeChart),
        ]

        // 加载初始数据
        function loadData() {
            const sheduler = runChunk => {
                setTimeout(() => {
                    let count = 0
                    const startTime = Date.now();
                    runChunk(() => count++ < 1)
                    const endTime = Date.now();
                    const executionTime = endTime - startTime;
                    utils.logWithCondition(openLog, new Error().stack, `任务执行时间：${executionTime} 毫秒`);
                }, 100)
            }
            utils.performTask(tasks, sheduler)
        }

        // 初始加载
        loadData()

        // 初始化日期选择器
        initDatePicker();

        /*
         * 初始化日期选择器
         */
        function initDatePicker() {
            // 开始日期
            laydate.render({
                elem: '#startDate',
                type: 'date',
                value: param.startDate,
                done: function (data) {
                    param.startDate = data;
                    pageDate.startDate = data
                }
            });

            // 结束日期
            laydate.render({
                elem: '#endDate',
                type: 'date',
                value: param.endDate,
                done: function (data) {
                    param.endDate = data;
                    pageDate.endDate = data
                }
            });
        }

        /**
         * 绑定事件
         */
        function initEvent() {
            $("#btnSearch").click(function () {
                loadData()
            });
        }
        initEvent()

    })

    /*
    功能：图表窗口自适应
    参数说明：无
    返回值说明：无
    */
    function resizeCharts() {
        for (var key in drawSpaceMap) {
            const dom = drawSpaceMap[key]
            const echartsDom = echarts.getInstanceByDom(dom)
            if (echartsDom) {
                echartsDom.resize()
            }
        }
    }

    // 监听窗口大小变化，自适应图表
    window.addEventListener('resize', resizeCharts)
})