<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>AI年体检分析-辖区内园所</title>
    <link rel="stylesheet" type="text/css" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" type="text/css" href="../../css/icon.css"/>
    <link rel="stylesheet" type="text/css" href="../../styles/tbstyles.css"/>
    <link href="../../plugin/flexigrid/css/tbflexigrid.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css"/>
    <script type="text/javascript">
        // 检查 top.objdata 是否存在，如果不存在则设置默认主题
        var curtheme = (top && top.objdata && top.objdata.curtheme) ? top.objdata.curtheme : 'backstage';
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + curtheme + '.css" type="text/css" media="screen"/>');
    </script>

    <style type="text/css">
        .layui-form-item {
            display: inline-block;
            vertical-align: top;
            margin: 5px 5px 0px 0;
        }


        .layui-form-item .layui-input-inline,
        .layui-form-item .layui-form-label,
        .layui-form-item .layui-form-mid {
            display: inline-block;
            vertical-align: top;
        }

        .layui-form-label {
            line-height: 28px;
        }

        /* 强制分页显示在右边 */
        .layui-table-page .layui-laypage {
            float: right !important;
            margin: 0 !important;
        }

        .layui-table-page .layui-laypage span,
        .layui-table-page .layui-laypage a,
        .layui-table-page .layui-laypage input,
        .layui-table-page .layui-laypage button {
            float: left !important;
        }

        .layui-table-page .layui-laypage .layui-laypage-count {
            float: right !important;
            margin-right: 10px !important;
        }

        .layui-table-page .layui-laypage .layui-laypage-limits {
            float: right !important;
            margin-right: 10px !important;
        }

    </style>
</head>
<body>
<div class="marmain">
    <div class="content-medical">
        <div class="layui-form layui-comselect" style="padding:10px;">
            <!-- 第一行 -->
            <div class="layui-form-item" style="margin-bottom: 10px;">
                <div class="layui-form-item" style="margin-bottom: 10px;">
                    <label class="layui-form-label" style="width: 80px;">体检安排：</label>
                    <div class="layui-input-inline" style="width:150px; margin-right: 15px;">
                        <select id="physicalexaminationarrangement" lay-filter="physicalexaminationarrangement">
                            <option value="">请选择</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item" style="margin-bottom: 10px;">
                    <label class="layui-form-label" style="width: 80px;">预约状态：</label>
                    <div class="layui-input-inline" style="width:100px;">
                        <select id="reservationstatus" lay-filter="reservationstatus">
                            <option value="">请选择</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item" style="margin-bottom: 10px;">
                    <label class="layui-form-label" style="width: 100px;">是否提交幼儿：</label>
                    <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                        <select id="issubmitchild" lay-filter="issubmitchild">
                            <option value="">请选择</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item" style="margin-bottom: 10px;">
                    <div class="layui-input-inline" style="width:100px; margin-right: 15px;display: none">
                        <select id="kindergarten" lay-filter="kindergarten">
                            <option value="">请选择</option>
                        </select>
                    </div>
                </div>
                <button class="layui-btn form-search" style="margin-left: 10px;" id="btnsearch">搜索</button>
            </div>
        </div>
    </div>
    <div id="content" style="width: 100%;height: 694px;">
        <div class="marmain-cen">
            <div class="content-medical" id="divtable">
                <table id="laytable" lay-filter="laytable"></table>
            </div>
        </div>
    </div>
</div>

<script data-main="../../js/operation/w_AIYearHealthDataCompare_kindergartenofjurisdiction"
        src='../../sys/require.min.js'></script>
</body>
</html> 