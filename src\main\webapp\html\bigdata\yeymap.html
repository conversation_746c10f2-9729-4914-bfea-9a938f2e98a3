﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../css/reset.css" />
    <link rel="stylesheet" href="../../css/style.css" />
    <link rel="stylesheet" href="../../styles/tbstyles.css" />
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <style>
        html, body { height: 100%; overflow: hidden; }
        body { background: #eaedf1; }

        .bottom-frame { padding: 0 1px; }
            .bottom-frame .map-con { float: left; width: 100%; min-height: 500px; }
        .count-list { height: 95px; color: #ffffff; }
        .mybtn:hover { cursor: pointer; text-decoration: underline; }
    </style>
</head>
<body>
    <div>
        <div class="layui-row layui-col-space5" style="margin: -2.5px 0;">
            <div class="layui-col-xs2 layui-col-sm2 layui-col-md3">
                <div class="count-list" style="background: #ffffff;color: #7c8196;">
                    <div style="margin-left:15px; padding-top: 20px;">
                        <label>模式：</label>
                        <span class="btntype model-cell current" data-type="1"><img src="../../images/tongbuy/icon_count.png">统计</span>
                        <span class="btntype model-cell" data-type="2"><img src="../../images/tongbuy/icon_anchor.png">锚点</span>
                        <span class="btntype model-cell" data-type="3"><img src="../../images/tongbuy/icon_parkname.png" style="margin: 0 3px;">园名</span>
                    </div>
                    <div style="margin-left:15px;margin-top: 10px;">
                        <form class="layui-form">
                            <!--<span class="theme-fgc-static" style="float: left;margin-top: 5px;">显示边界：</span>
                            <div class="layui-input-block" style="float: left;margin-left: 4px;margin-top: -3px;">
                                <input type="checkbox" lay-filter="switchBoundary"  lay-skin="switch" lay-text="开启|关闭">
                            </div>-->
                            <span class="theme-fgc-static" style="float: left;margin-top: 8px;">深色主题：</span>
                            <div class="layui-input-block" style="float: left;margin-left: 4px;margin-top: -3px;">
                                <input type="checkbox" checked="checked" lay-filter="switchthem"  lay-skin="switch" lay-text="开启|关闭">
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="layui-col-xs10 layui-col-sm10 layui-col-md9">
                <div class="layui-row layui-col-space5">
                    <div class="layui-col-xs2 layui-col-sm2 layui-col-md2" style="width: 230px;">
                        <div class="count-list" style="background: #f49478;">
                            <h5><span>幼儿园数量</span></h5>
                            <div style="text-align: center;">
                                <span class="count-numcell"><i id="btnyeycount" class="mybtn" title="切换园所数据">0</i></span>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-xs2 layui-col-sm2 layui-col-md2" style="width: 230px;">
                        <div class="count-list" style="background: #69bedd;">
                            <h5>
                                <span>发布食谱数</span>
                            </h5>
                            <div>
                                <span class="count-numcell"><i id="btnispubliccount" class="mybtn" title="切换发布食谱数据">0</i></span>
                                <span class="count-numcell">本周新增<i id="btnnewpublic" class="mybtn" style="margin-left: 10px;">0</i></span>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-xs2 layui-col-sm2 layui-col-md2" style="width: 230px;display:none;">
                        <div class="count-list" style="background: #53c79f;">
                            <h5>
                                <span>分享食谱数</span>
                            </h5>
                            <div>
                                <span class="count-numcell"><i id="btnissharecount" class="mybtn" title="切换分享食谱数据">0</i></span>
                                <span class="count-numcell">本周新增<i id="btnnewshare" class="mybtn" style="margin-left: 10px;">0</i></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bottom-frame">
            <div id="allmap" class="map-con"></div>
        </div>
    </div>
    <script type="text/javascript" src="../../sys/jquery.js"></script>
    <script type="text/javascript" src="../../sys/arg.js"></script>
    <script type="text/javascript" src="../../sys/function.js"></script>
    <script type="text/javascript" src="../../layui-btkj/layui.js"></script>
    <script type="text/javascript" src="https://api.map.baidu.com/api?v=1.0&type=webgl&ak=MfX8ZrC7i89wF1UcPFnorhsWFebQLtBv&services=true"></script>
    <script type="text/javascript" src="../../plugin/js/selarea.js"></script>
    <script type="text/javascript" src="//mapv.baidu.com/build/mapv.min.js"></script>
    <script type="text/javascript" src="https://code.bdstatic.com/npm/mapvgl@1.0.0-beta.136/dist/mapvgl.min.js"></script>
    <script type="text/javascript" src="../../js/citycode.js"></script>
    <script type="text/javascript" src="../../js/bigdata/yeymap.js"></script>
</body>
</html>
