<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>检查项目设置</title>
    <link rel="stylesheet" href="../../css/reset.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <style type="text/css">
        html, body {
            background: #fff;
          
        }
        .layui-form-label {
            width: 120px;
        }
        .layui-input-block {
            margin-left: 150px;
        }
		.layui-form .layui-form-item{ margin-bottom: 15px;}
    </style>
</head>
<body>
    <div style="min-width: 800px;">
        <div class="content-medical">
            <form class="layui-form" lay-filter="formMain" style="padding: 20px;">
<!--                <input type="hidden" name="id">-->
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>检查项目名称：</label>
                    <div class="layui-input-block">
                        <input type="text" name="packagename" required lay-verify="required" placeholder="请输入检查项目名称" autocomplete="off" class="layui-input" maxlength="50">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>检查门诊：</label>
                    <div class="layui-input-block">
                        <select name="mzid" lay-verify="required">
                            <option value="">请选择门诊</option>
                        </select>
                    </div>
                </div>
                 
                <div class="layui-form-item">
                    <label class="layui-form-label">双方信息：</label>
                    <div class="layui-input-block">
                        <input type="radio" name="ujtype" value="3" title="全部" checked>
                        <input type="radio" name="ujtype" value="1" title="男方">
                        <input type="radio" name="ujtype" value="2" title="女方">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">携带证件：</label>
                    <div class="layui-input-block">
                        <div id="pids-select"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">免费就诊地址：</label>
                    <div class="layui-input-block">
                        <input type="text" name="address" required lay-verify="required" placeholder="请输入就诊地址" autocomplete="off" class="layui-input" maxlength="150">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">收费就诊地址：</label>
                    <div class="layui-input-block">
                        <input type="text" name="addressvip" placeholder="请输入就诊地址" autocomplete="off" class="layui-input" maxlength="150">
                    </div>
                </div>
            </form>
        </div>
    </div>
    <script type="text/javascript">
        var v = top.version;
        document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../plugin/xm-select/xm-select.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../js/depart/check_itemspackage_edit.js?v=" + v + "'><" + "/script>");
    </script>
</body>
</html>