﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>报表管理</title>
    <link rel="stylesheet" href="../css/reset.css"/>
    <link rel="stylesheet" href="../css/style.css"/>
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <!--[if lt IE 9]>
    <script src="../sys/html5shiv.min.js"></script>
    <![endif]-->
    <style>
        input[type="password"] {
            width: 221px;
        }
        .divrole {
            display: none;
        }
        .layui-form-label {
            width: 160px
        }
    </style>
</head>
<body>
<section class="personalInfo font14 cl1 ">
	<div id = "report" style="display:none;">
    	<form id="form1" class="layui-form">
	        <section class="clearfix pd20 bg6 ">
	            <section class=" fl">
	                <div class="layui-form-item">
	                    <label class="layui-form-label"><em>*</em>报表标识：</label>
	                    <div class="layui-input-inline">
	                        <input id="reportcode" type="text" name="reportcodes" required lay-verify="required"
	                               placeholder="请输入报表标识" maxlength="30" autocomplete="off"
	                               class="layui-input">
	                    </div>
	                </div>
	                <div class="layui-form-item">
	                    <label class="layui-form-label"><em>*</em>报表名称：</label>
	                    <div class="layui-input-inline">
	                        <input id="reportname" type="text" name="reportnames" required lay-verify="required"
	                               placeholder="请输入报表名称" maxlength="30" autocomplete="off"
	                               class="layui-input">
	                    </div>
	                </div>
	            </section>
	        </section>
	    <div class="btns-foot">
            <input type="hidden" id="reportestid"/>
            <a id="btnsave" class="layui-btn" lay-submit="" lay-filter="btnsave">保存</a>
            <a id="btncancel" class="layui-btn btncancel">取消</a>
        </div>
     	</form>
     </div>
	 <div id = "reports" style="display:none;">
	 	<form id="form2" class="layui-form">
	        <section class="clearfix pd20 bg6 ">
	            <section class=" fl">
	                <div class="layui-form-item">
	                    <label class="layui-form-label"><em>*</em>报表类型标识：</label>
	                    <div class="layui-input-inline">
	                        <input id="reportsetcode" type="text" name="reportsetcode" required lay-verify="required"
	                               placeholder="请输入上报类型标识" maxlength="30" autocomplete="off"
	                               class="layui-input">
	                    </div>
	                </div>
	                <div class="layui-form-item">
	                    <label class="layui-form-label"><em>*</em>报表类型名称：</label>
	                    <div class="layui-input-inline">
	                        <input id="reportsetname" type="text" name="reportsetname" required lay-verify="required"
	                               placeholder="请输入上报类型名称" maxlength="30" autocomplete="off"
	                               class="layui-input">
	                    </div>
	                </div>
	                <div class="layui-form-item">
	                    <label class="layui-form-label"><em>*</em>包含报表：</label>
	                    <div class="layui-input-inline include">
	                        <input id="reportcodes" type="text" name="reportcodes" required lay-verify="required"
	                               placeholder="请输入包含报表" maxlength="30" autocomplete="off"
	                               class="layui-input" readonly="readonly">
	                    </div>
	                </div>
	                <div class="layui-form-item">
	                    <label class="layui-form-label"><em>*</em>包含报表名称：</label>
	                    <div class="layui-input-inline include">
	                        <input id="reportnames" type="text" name="reportnames" required lay-verify="required"
	                               placeholder="请输入包含报表名称" maxlength="30" autocomplete="off"
	                               class="layui-input" readonly="readonly">
	                    </div>
	                </div>
	                <div class="layui-form-item">
	                    <label class="layui-form-label">负责地区编号：</label>
	                    <div class="layui-input-inline">
	                        <input id="areacode" type="text" name="areacode"
	                               placeholder="请输入负责地区编号" maxlength="30" autocomplete="off"
	                               class="layui-input">
	                    </div>
	                </div>
	                <div class="layui-form-item">
	                    <label class="layui-form-label">负责地区名称：</label>
	                    <div class="layui-input-inline">
	                        <input id="areaname" type="text" name="areaname"
	                               placeholder="请输入负责地区名称" maxlength="30" autocomplete="off"
	                               class="layui-input">
	                    </div>
	                </div>
	                <div class="layui-form-item">
	                    <label class="layui-form-label">上报频次：</label>
	                    <div class="layui-input-inline">
		                    <select name="rate" lay-verify="" id="rate">
							  <option value="">请选上报频次</option>
							  <option value="quarter">季</option>
							  <option value="month">月</option>
							  <option value="second">次</option>
							</select> 
	                    </div>
	                </div>
	            </section>
	        </section>
        <div class="btns-foot">
            <input type="hidden" id="reportestid"/>
            <a id="btnsave" class="layui-btn" lay-submit="" lay-filter="btnsave2">保存</a>
            <a id="btncancel" class="layui-btn btncancel">取消</a>
        </div>
    	</form>
    </div>
</section>
<script type="text/javascript" src="../layui-btkj/layui.js"></script>
<script type="text/javascript" src="../sys/jquery.js"></script>
<script type="text/javascript" src="../sys/arg.js"></script>
<script type="text/javascript" src="../js/reportsetedit.js"></script>
</body>
</html>
