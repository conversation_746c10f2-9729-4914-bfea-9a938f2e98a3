﻿<!DOCTYPE html>
<html style="overflow: hidden;">
<head>
    <meta charset="utf-8"/>
    <title>幼儿园管理</title>
    <link rel="stylesheet" href="../css/reset.css"/>
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../plugin/zTree/css/zTreeStyle/zTreeStyle.css" type="text/css">
	<link rel="stylesheet" href="../styles/xbcomstyle.css">
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <!--[if lt IE 9]>
    <script src="../sys/html5shiv.min.js"></script>
    <![endif]-->
	 <style>
		body {
            background: #F4F5F7;
        }
    </style>
</head>
<body>
<div style="background-color: white; margin:0 10px;">
    <div class="layui-form" style="width:100%;float:left;margin-top: 10px;">
        <span>
            <span class="layui-form-label" style="width: 60px;">选择年：</span>
            <div class="layui-input-inline" style="float: left;width:100px;">
                <select name="year" lay-verify="">
                </select>
            </div>
            <span class="layui-form-label" style="width: 60px;margin-left: 8px;">选择月：</span>
            <div class="layui-input-inline" style="float: left;width:80px;">
                <select name="month" lay-verify="">
                      <option value="">请选择</option>
                      <option value="1">1月</option>
                      <option value="2">2月</option>
                      <option value="3">3月</option>
                      <option value="4">4月</option>
                      <option value="5">5月</option>
                      <option value="6">6月</option>
                      <option value="7">7月</option>
                      <option value="8">8月</option>
                      <option value="9">9月</option>
                      <option value="10">10月</option>
                      <option value="11">11月</option>
                      <option value="12">12月</option>
                </select>
            </div>
            <span class="layui-form-label" style="width: 60px;margin-left: 8px;">选择周：</span>
            <div class="layui-input-inline" style="float: left;width:80px;">
                <select name="week" lay-verify="">
                      <option value="">请选择</option>
                      <option value="1">第1周</option>
                      <option value="2">第2周</option>
                      <option value="3">第3周</option>
                      <option value="4">第4周</option>
                      <option value="5">第5周</option>
                </select>
            </div>
            <span class="layui-form-label" style="width: 100px;margin-left: 8px;">食谱均衡级别：</span>
            <div class="layui-input-inline" style="width:100px;float: left;">
                <select name="junheng" lay-verify="">
                      <option value="">请选择</option>
                      <option value="1">均衡</option>
                      <option value="2">略微均衡</option>
                      <option value="3">不均衡</option>
                      <option value="4">未上报</option>
                </select>
            </div>
			  <span class="layui-form-label" style="width: 60px;margin-left: 8px;">幼儿园：</span>
            <div class="layui-input-inline" style="float: left;width:160px;">
                <input type="text" name="yeyname" class="layui-input" style="width:100%;" placeholder="请输入幼儿园名称"/>
            </div>
            <span class="layui-form-label" style="width: 90px;margin-left: 8px;display: none;">营养素达标：</span>
            <div class="layui-input-inline" style="float: left;width:100px;display: none;">
                <select name="nutri" lay-verify="">
                    <option value="">请选择</option>
                    <option value="cal">能量</option>
                    <option value="pro">蛋白质</option>
                    <option value="va">维生素A</option>
                    <option value="vb1">维生素B1</option>
                    <option value="vb2">维生素B2</option>
                    <option value="vc">维生素C</option>
                    <option value="ca">钙</option>
                    <option value="zn">锌</option>
                    <option value="fe">铁</option>
                    <option value="na2">钠</option>
                </select>
            </div>
            <div class="layui-input-inline" style="float: left;width:80px;margin-left:5px;display: none;">
                <select name="dabiao" lay-verify="">
                    <option value="">请选择</option>
                    <option value="1">达标</option>
                    <option value="2">超标</option>
                    <option value="3">不达标</option>
                </select>
            </div>
			 <span class="layui-form-label" style="width: 80px;margin-left: 8px;">区域选择：</span>
            <div class="layui-input-inline" style="width:100px;">
                <input type="text" id="citySel" name="selarea" class="layui-input" style="width:100px;" readonly/>
                <div id="menuContent" class="menuContent" style="display:none; position: absolute;z-index:10;background: #fff;border:1px solid #ccc;height: 330px;overflow: auto;">
                    <ul id="treeDemo" class="ztree" style="margin-top:0; width:100px;"></ul>
                </div>
            </div>
            <button class="layui-btn mgl-20 bluebg" id="search" style="margin-left: 10px;">查询</button>
            <button class="layui-btn mgl-20 purplebg" id="refresh">刷新</button>
            <button class="layui-btn mgl-20 greenbg" id="export">导出Excel</button>
        </span>
    </div>
    <div id="content" style="width: 100%;">
        <table id="tbmain" lay-filter="tbmain"></table>
    </div>
</div>
<script type="text/html" id="optTpl">
    <a class="layui-table-link" lay-event="detail">{{d.yeyname}}</a>
</script>

<script type="text/html" id="junhengTpl">
    {{# if(d.score >= objdata.nutriSet.balancestart){ }}
        均衡
    {{# } else if(d.score >= objdata.nutriSet.slight_balancestart){ }}
        略微均衡
    {{# } else if(d.score >= objdata.nutriSet.not_balancestart) { }}
        不均衡
    {{# } else { }}
    {{# } }}
</script>
<script type="text/html" id="avgintakeTpl">
    {{# if(d.avgintake > 0){ }}
        扣{{d.avgintake}}分
    {{# } else if(d.avgintake == 0) { }}
        ok
    {{# } else { }}
    {{# } }}
</script>
<script type="text/html" id="nutriintakeTpl">
    {{# if(d.nutriintake > 0){ }}
        扣{{d.nutriintake}}分
    {{# } else if(d.nutriintake == 0) { }}
        ok
    {{# } else { }}
    {{# } }}
</script>
<script type="text/html" id="calsourceTpl">
    {{# if(d.calsource > 0){ }}
        扣{{d.calsource}}分
    {{# } else if(d.calsource == 0) { }}
        ok
    {{# } else { }}
    {{# } }}
</script>
<script type="text/html" id="prosourceTpl">
    {{# if(d.prosource > 0){ }}
        扣{{d.prosource}}分
    {{# } else if(d.prosource == 0) { }}
        ok
    {{# } else { }}
    {{# } }}
</script>
<script type="text/html" id="calstructureTpl">
    {{# if(d.calstructure > 0){ }}
        扣{{d.calstructure}}分
    {{# } else if(d.calstructure == 0) { }}
        ok
    {{# } else { }}
    {{# } }}
</script>
<script type="text/javascript" src="../sys/jquery.js"></script>
<script type="text/javascript" src="../sys/arg.js"></script>
<script type="text/javascript" src="../layui-btkj/layui.js"></script>
<script type="text/javascript" src="../sys/function.js"></script>
<script type="text/javascript" src="../js/citycode.js"></script>
<script type="text/javascript" src="../plugin/zTree/js/jquery.ztree.core-3.5.js"></script>
<script type="text/javascript" src="../plugin/zTree/js/jquery.ztree.excheck-3.5.js"></script>
<script type="text/javascript" src="../js/recipelist.js"></script>
</body>
</html>
