﻿body{-webkit-tap-highlight-color:rgba(0,0,0,0);font-size:16px;/* font-family:"microsoft yahei";*/ background: #fff; font-family:"冬青黑体简体中文 W3"; }
*{
    -webkit-tap-highlight-color: rgba(0,0,0,0);
    -webkit-tap-highlight-color: transparent;
}
/* 通用样式 */
.out-container {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 100%;
    overflow: scroll;
    -webkit-overflow-scrolling: touch;
}
/*取消before,after*/
.nobefore:before{content: none!important;}
.noafter:after{content: none!important;}
/*自定义flex*/
.weui-flex_center{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;}
/*通用按钮、标签*/
.fullbtn{font-size: 12px;background: #e57c68;border-radius: 20px;padding: 2px 8px;}
.circle-btn-left{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;z-index: 9;position: absolute;height: 25px;box-shadow: 0 0 8px 2px #6edcb7;margin-top: 14px;padding: 0 10px;height: 25px;background: #ffffff;color: #02d196;font-size: 13px;border-radius: 0 20px 20px 0;}					
.borderbtn{font-size: 12px;color: #666666;border: 1px solid #dddddd;padding: 4px 12px;border-radius: 20px;}
.def-weui-btn{background: #f55358;border-radius: 30px;font-size: 12px;line-height: 24px;margin: 0 3px;border: 1px solid #f55358;color: #ffffff;}
/*底部按钮*/
.btmopr-btn{padding: 15px 0;position: fixed;bottom: 0;width: 100%;z-index: 9; background: #fff;box-shadow: 0px 0px 5px 1px #ececec;}
.btmopr-btn a{margin: 0 25px;background: #00cd91;height: 44px;line-height: 44px;font-size: 16px;border-radius: 30px;}
.btmmore-btn{justify-content: center;padding: 15px 0;position: fixed;bottom: 0;width: 100%;z-index: 9; background: #fff;box-shadow: 0px 0px 5px 1px #ececec;}
.btmmore-btn a:first-child{margin: 0 2px;height: 44px;line-height: 44px;font-size: 16px;border-radius: 30px 0 0 30px;background: #dddddd;color: #666666;}
.btmmore-btn a:last-child{margin: 0 2px;height: 44px;line-height: 44px;font-size: 16px;border-radius: 0 30px 30px 0;background: #00cd91;}
.nospace-btn{padding: 0;position: fixed;bottom: 0;width: 100%;z-index: 9;background: #fff;box-shadow: 0px 0px 5px 1px #ececec;}
.nospace-btn .weui-btn,.btmopr-btn .weui-btn+.weui-btn{margin: 0;font-size: 15px;border-radius: 0;padding: 5px 0;}
.fixed-btm{z-index: 9;height: 60px;font-size: 12px;align-items: center;justify-content: space-between;background: #ffffff;position: fixed;bottom: 0;width: 100%;box-shadow: 0 0 10px 3px #eeeeee;}
/*背景色*/
.whitebg{background: #ffffff;}
.greenbg{background: #52DFB7;}
.bluebg{background: #57beff;}
.orgBtn{ background:#ffa922;}
.emerald-bg{ background: #19bb72}
.gray-bg{ background: #dddddd}
.red-bg{ background: #ff7878}
/*字体颜色*/
.red{ color:#f44336;}
.green{ color:#42c567;}
.gray{ color:#666666;}
.gray-txt{color: #999999}
.blue{ color:#4fb4fc;}
.liblue{color: #48a0dc;}
.orange{color: #fe9759;}
.violet{ color:#bd8cff;}
.pink{color: #ff6b6b;}
.lightgreen{ color:#00cd91}
.emeraldgreen{color: #76d571;}
.orange{color:#ff805a}
.check_update p.red,.check_update p.red a,.eatstuname.red{ color:#F00;}
.orange-yellow{color: #f7a502}
.purple{color:#9e7ffe;}
.Purple-txt{color: #645ef7;}
/*间距*/
.martop10{margin-top: 10px;}
.pd15{ padding:15px;}
.pd10{ padding:10px;}
.mg15{ margin:15px;}
.mgtop10{ margin-top:10px;}
.mgtop12{ margin-top:12px;}
.mgtop15{ margin-top:15px;}
.mgtop20{ margin-top:20px;}
.mgleft15{ margin:0px 15px 10px 15px; }
.mg10{ margin:10px;}
.mgtopnone{ margin-top:0px;}
.pdbtom15{ padding-bottom:15px;}
.pdtop10{ padding-top:10px;}
.bg1{background: #fff;}
.mgleft10{ margin-left:10px;}
/*按钮*/
.btn:active{opacity:0.8;}
/*自定义搜索框*/
.def-search .weui-search-bar{background-color: #ffffff;padding: 8px 15px;}	
.def-search .weui-search-bar__label{background: #F5F5F5;border-radius: 3px;}
.def-search .weui-search-bar__box .weui-search-bar__input{padding: 5px 0;}
.def-search .weui-search-bar__form:after{content: none;}
.def-search .weui-search-bar__form{border-radius:20px;background: #F5F5F5;}
.def-search .weui-search-bar__cancel-btn{color: #19bb72;font-size: 14px;}
.def-search .weui-search-bar__box .weui-icon-search{top: 1px;}
.def-search .weui-search-bar:before{content: none;}
/*图片遮罩*/
.shadowdiv{position: absolute;top: 0;bottom: 0;left: 0;right: 0;background: rgba(0,0,0,0.3);}
/*遮罩层/弹框*/
.shadow-con{position: fixed;top: 0;bottom: 0;left: 0;right: 0;background: rgba(0,0,0,0.5);z-index: 9;display:-webkit-box;display:-webkit-flex;display:flex;align-items: center;justify-content: center;}
.shadow-box{position: relative;background: #ffffff;margin: 0 15px;border-radius: 6px;font-size: 13px;color: #333333;padding:20px 0;width: 100%;}
/*遮罩层底部/弹框*/
.shadow-botcon{position: fixed;top: 0;bottom: 0;left: 0;right: 0;z-index: 9;display:-webkit-box;display:-webkit-flex;display:flex;}
.shadow-botbox{position: relative;background: #ffffff;border-radius:10px;font-size: 13px;color: #333333;padding:0 15px 20px 15px;width: 100%;  box-shadow: inset 0 -1px 14px #dfdfdf; top:100px; }
/*更改 遮罩层/弹框*/
.popup-con{position: fixed;top: 0;bottom: 0;left: 0;right: 0;z-index: 9;display:-webkit-box;display:-webkit-flex;display:flex;align-items: center;justify-content: center;}
.popup-shadow{position: fixed;top: 0;bottom: 0;left: 0;right: 0;background: rgba(0,0,0,0.5);}
.popup-box{position: relative;background: #ffffff;margin: 0 15px;border-radius: 6px;font-size: 13px;color: #333333;padding:20px 0;width: 100%;}
.popup-box h5{margin: 0 20px;font-weight: bold;font-size: 17px;color: #333333;border-bottom: 1px dashed #dddddd;height: 50px;}
.popup-box h5.title-blue{background: #0663ca;color: #ffffff;border-bottom: 0;margin: 0;border-radius: 6px 6px 0 0;}
/*弹框底部按钮*/
.box-oprbtn{border-top: 1px solid #dddddd;}
.box-oprbtn a{border-right: 1px solid #dddddd;color: #666666;height: 45px;font-size: 15px;}
.box-oprbtn a.current{color: #399bf7;}
.box-oprbtn a:last-child{border-right: none;}
/*竖线*/
.report-con{ display: inline-block; width:4px;height: 18px;position: absolute;font-size: 16px; color: #333333; border-top-left-radius:10px;border-top-right-radius:10px;border-bottom-left-radius: 10px;border-bottom-right-radius:10px;}
/*省略号*/
.text-elli{overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}
.ellipsis-clamp{overflow:hidden;text-overflow:ellipsis;display:-webkit-box; -webkit-box-orient:vertical;-webkit-line-clamp:2;}
/*切换*/
.commontab{display: flex;display: -webkit-flex;align-items:center;border: 1px solid #ffffff;border-radius: 4px;}
.commontab li{height: 100%;padding: 4px 10px 3px 10px;color: #ffffff;display: flex;display: -webkit-flex;align-items: center;justify-content:center;font-size: 12px;border-right: 1px solid #ffffff;}
.commontab li:last-child{border-right: none;}
.commontab li.current{background: #ffffff;color: #00d1c9;}	
.stat-tab{display: flex;display: -webkit-flex;align-items: center;width:100%;height: 27px;border-radius: 6px;}
.stat-tab li{font-size:14px;color:#7E7E7E;display: flex;display: -webkit-flex;align-items: center;justify-content:center;height: 27px;line-height: 27px;position: relative;flex: 1;-webkit-flex: 1;}
.stat-tab li:after{content: "";height: 27px;position: absolute;right: 0;top: 0;}
.stat-tab li:first-child{border-radius: 5px 0 0 5px;}
.stat-tab li:last-child{border-radius: 0 5px 5px 0;}
.stat-tab li:last-child:after{content: none;}
.stat-tab li.curr-stat{color: #fff;background: #FAC132;}
.tab-common{font-size: 15px;color: #666666;height:28px;background: #ffffff;align-items: center;justify-content: space-around;}
.tab-common li{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;height: 100%; margin-right: 10px;}
.tab-common li.current{color: #fff;  background: #00cd91; padding: 0 10px; border-radius: 20px; }
.tab-common2{font-size: 15px;color: #666666;border-bottom: 1px solid #eeeeee;height: 40px;background: #ffffff;align-items: center;justify-content: space-around;}
.tab-common2 li{position: relative;display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;height: 100%;}
.tab-common2 li.current{color: #19bb72;}
.tab-common2 li.current:after{content: "";position: absolute;bottom: 0;left: 0;width: 100%;border-bottom: 2px solid #19bb72;color: #19bb72;}
.tab-common2.tab-yellow li.current{color: #fac132;}
.tab-common2.tab-yellow li.current:after{border-bottom: 2px solid #fac132;color: #fac132;}
.tab-common3{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;background: #ffffff;height: 36px;line-height: 36px;font-size: 0;box-shadow: 0 4px 4px -3px #eeeeee;}
.tab-common3 li{display: -webkit-box;display: -webkit-flex;display: flex;-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;align-items: center;justify-content: center;font-size: 14px;color: #666666;position: relative;}
.tab-common3 li.current{color: #fcb303;}
.tab-common3 li.current:before{content: "";position: absolute;bottom: 0;left: 50%;margin-left: -16px;background: #fbc134;width: 32px;height: 3px;border-radius: 10px 10px 0 0;}
.tab-common3 li:after{content: "";width: 1px;height: 50%;position: absolute;right: 0;top: 25%;background: #eeeeee;}
.tab-common3 li:last-child:after{content: none;}
.tab-common4{padding: 0 10px;border-top: 1px solid #dddddd;border-bottom: 1px solid #dddddd;margin-top: 10px;align-items: center;justify-content: space-around;font-size: 14px;color: #666666;height: 39px;background: #ffffff;}
.tab-common4 li{position: relative;-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;text-align: center;}
.tab-common4 li.current a{background: #fac134;color: #ffffff;padding: 2px 6px;display: inline-block;min-width: 64px;border-radius: 20px;}
@media only screen and (max-width : 62px ){
	.tab-common4{padding: 0;}
}
.tab-common5{border: 1px solid #64b0f9;height: 38px;border-radius: 50px;}
.tab-common5 li{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;font-size: 14px;color: #666666;}
.tab-common5 li{border-right: 1px solid #64b0f9;}
.tab-common5 li.current{background: #64b0f9;height: 38px;color: #ffffff;}	
.tab-common5 li:first-child{border-radius: 50px 0 0 50px;}
.tab-common5 li:last-child{border-right: none;border-radius: 0 50px 50px 0;}
.tab-common6{display: -webkit-box;display: -webkit-flex;display: flex;justify-content: space-around;align-items:center;background: #ffffff;border-bottom: 1px solid #eeeeee;color: #666666;font-size: 13px;height: 35px;}
.tab-common6 li{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;width: 100%;height: 100%;position: relative;}
.tab-common6 li.current{color: #02c6ac;}
.tab-common6 li.current:after{content:"";height: 2px;width: 34px;position: absolute;bottom: 0;left: 50%;margin-left: -17px;background: #02c6ac;border-radius: 3px;}	
.tab-common7{border: 1px solid #00cd91;margin: 10px 15px;border-radius: 3px;}
.tab-common7 li{position: relative;display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;height: 36px;font-size: 14px;color: #666666;}
.tab-common7 li.current{background: #00cd91;color: #ffffff;}
.tab-common8{border: 1px solid #00cd91;height: 30px;border-radius: 3px 3px 0 0;}
.tab-common8 li{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;font-size: 13px;color: #666666;border-right: 1px solid #00cd91;}
.tab-common8 li:first-child{border-radius: 3px 0 0 0;}
.tab-common8 li:last-child{border-right: none;border-radius: 0 3px 0 0;}
.tab-common8 li.current{background: #e1f8f1;color: #00cd91;font-weight: bold;}
.tab-common9{margin: 0px 0px; background: #fff;}
.tab-common9 li{ border-right: 1px solid #F3F3F3;position: relative;display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;height: 40px;font-size: 15px;color: #666666;  }
.tab-common9 li.current{background: #47decb;color: #ffffff;border-right:none;}
.tab-common9 li:last-child{border-right: 0;}
.tab-common10{justify-content: space-around;font-size: 13px;color: #c6c6c6;padding: 15px;font-weight: bold;align-items: center;margin: 0 -10px;}
.tab-common10 li{position: relative;margin: 0 10px;}
.tab-common10 li.current{color: #2188db;font-size: 15px;margin-top: -5px;}
.tab-common10 li.current:after{content: "";width: 24px;height: 4px;background: #2188db;position: absolute;left: 50%;margin-left: -12px;bottom: -5px;border-radius: 10px;}
/*通用上传照片*/
.upload-pit .weui-uploader__file{width: 62px;height: 62px;margin-right: 8px;margin-bottom: 8px;position: relative;}
.upload-pit .weui-uploader__input-box{width: 62px;height: 62px;border: 1px dashed #d9d9d9;}
.upload-pit .icon_close{position: absolute;right: 0;width: 18px;height: 18px;line-height: 18px;text-align: center;background: rgba(0,0,0,0.5);border-radius: 0 0 0 6px;}
.upload-pit .icon_close:before{color: #ffffff;font-size: 14px;}
.uploader-tit{width: 100%;color: #999999;font-size: 12px; margin-top: 4px;}
/*单选框*/
.set-radio-style input[type="radio"]{-webkit-appearance: none;width: 17px;height: 17px;border: 1px solid #ccc;border-radius: 50%;outline: none;vertical-align: top;margin-top: 2px;}
.set-radio-style input[type="radio"]:checked{background: url(../images/attendance/radio_bg3.png) 50%;background-size:17px 17px;outline: none;border: 0;}
.set-radio-style input[type="radio"][type="radio"]{margin-right: 5px;}
.set-radio-style .sel-radio{margin-left: 15px;}
.set-radio-style1 input[type="radio"]{-webkit-appearance: none;width: 17px;height: 17px;border: 1px solid #ccc;border-radius: 50%;outline: none;vertical-align: top;margin-top: 2px;}
.set-radio-style1 input[type="radio"]:checked{background: url(../images/sel-green3.png) 50%;background-size:17px 17px;outline: none;border: 0;}
.set-radio-style1 input[type="radio"][type="radio"]{margin-right: 5px;}
.set-radio-style1 .sel-radio{margin-left: 15px;}
.set-radio-style3 input[type="radio"]{-webkit-appearance: none;position: relative;margin-right: 5px;width: 12px;height: 12px;background: #ffffff;border-radius: 50%;display: inline-block;border: 1px solid #d2d2d2;box-sizing: content-box;outline: none;}
.set-radio-style3 input[type="radio"]:checked{-webkit-appearance: none;border-radius: 50%;display: inline-block;border: 1px solid #03c7ad;box-sizing: content-box;}        
.set-radio-style3 input[type="radio"]:checked:before{content:"";display: inline-block;width: 6px;height: 6px;border-radius: 50%;background: #03c7ad;position: absolute;left: 3px;top: 3px;}
.set-radio-style4 input[type="radio"]{-webkit-appearance: none;position: relative;margin-right: 5px;width: 12px;height: 12px;background: #ffffff;border-radius: 50%;display: inline-block;border: 1px solid #d2d2d2;box-sizing: content-box;outline: none;margin-top: -2px;}
.set-radio-style4 input[type="radio"]:checked{-webkit-appearance: none;border-radius: 50%;display: inline-block;border: 1px solid #0268d4;box-sizing: content-box;}
.set-radio-style4 input[type="radio"]:checked:before{content:"";display: inline-block;width: 6px;height: 6px;border-radius: 50%;background: #0268d4;position: absolute;left: 3px;top: 3px;}
.set-radio-style5 input[type="radio"]{-webkit-appearance: none;position: relative;margin-right: 5px;width: 12px;height: 12px;background: #ffffff;border-radius: 50%;display: inline-block;border: 1px solid #d2d2d2;box-sizing: content-box;outline: none;margin-top: -2px;}
.set-radio-style5 input[type="radio"]:checked{-webkit-appearance: none;border-radius: 50%;display: inline-block;border: 1px solid #f55358;box-sizing: content-box;}
.set-radio-style5 input[type="radio"]:checked:before{content:"";display: inline-block;width: 6px;height: 6px;border-radius: 50%;background: #f55358;position: absolute;left: 3px;top: 3px;}
/*复选框*/
.set-checkbox-style input[type="checkbox"]{-webkit-appearance: none;width: 14px;height: 14px;border: 1px solid #ccc;outline: none;}
.set-checkbox-style input[type="checkbox"]:checked{background: url(../images/check_bg.png) 50%;background-size:14px 14px;outline: none;border: 1px solid #53CAC3;}
.set-checkbox-style input[type="checkbox"]{vertical-align: middle;margin-right: 7px;}
.set-checkbox-style2 input[type="checkbox"]{-webkit-appearance: none;width: 15px;height: 15px;border-radius: 50%;border: 1px solid #ccc;outline: none;}
.set-checkbox-style2 input[type="checkbox"]:checked{background: url(../images/icon_checkbox_blue.png) 50%;background-size:15px 15px;border-radius: 50%;outline: none;border: 1px solid #0663ca;}
.set-checkbox-style2 input[type="checkbox"]{vertical-align: middle;margin-right: 7px;}
.set-check-style2 input[type="checkbox"]:checked{background: url(../images/sel-green3.png) 50%;background-size:17px 17px;outline: none;border: 1px solid #53CAC3;}
.set-check-style2 input[type="checkbox"]{margin-right: 5px;}	
.set-check-style2 .sel-check{margin-left: 15px;}
.set-check-style2 input[type="checkbox"]{-webkit-appearance: none;width: 17px;height: 17px;border: 1px solid #ccc;outline: none;border-radius: 50%;outline: none;vertical-align: top;margin-top: 2px;}
.blue-checkbox-style input[type="checkbox"]{-webkit-appearance: none;width: 12px;height: 12px;border-radius: 50%;border: 1px solid #ccc;outline: none;}
.blue-checkbox-style input[type="checkbox"]:checked{background: url(../images/dingding/ico_single_up.png) 50%;background-size:12px 12px;outline: none;border: 1px solid #53CAC3;}
.blue-checkbox-style input[type="checkbox"]{vertical-align: middle;margin-right: 7px;}
.set-checkbox-style3 input[type="checkbox"]{-webkit-appearance: none;width: 15px;height: 15px;border-radius: 50%;border: 1px solid #ccc;outline: none;}
.set-checkbox-style3 input[type="checkbox"]:checked{background: url(../images/icon_checkbox_red.png) 50%;background-size:15px 15px;border-radius: 50%;outline: none;border: 1px solid #f55358;}
.set-checkbox-style3 input[type="checkbox"]{vertical-align: middle;margin-right: 7px;}
.set-checkbox-style4 input[type="checkbox"]{-webkit-appearance: none;width: 13px;height: 13px;border-radius: 2px;border: 1px solid #ccc;outline: none;}
.set-checkbox-style4 input[type="checkbox"]:checked{background: url(../images/icon_checkbox_red2.png) 50%;background-size:13px 13px;border-radius: 2px;border: none;outline: none;}
.set-checkbox-style4 input[type="checkbox"]{vertical-align: middle;margin-right: 7px;}
.set-checkbox-style5 input[type="checkbox"]{-webkit-appearance: none;width: 15px;height: 15px;border-radius: 50%;border: 1px solid #ccc;outline: none;}
.set-checkbox-style5 input[type="checkbox"]:checked{background: url(../images/yello-sel.png) 50%;background-size:15px 15px;border-radius: 50%;outline: none;border: 1px solid #ff9c2c;}
.set-checkbox-style5 input[type="checkbox"]{vertical-align: middle;margin-right: 7px;}
.set-checkbox-style6 input[type="checkbox"]{-webkit-appearance: none;width: 15px;height: 15px;border-radius: 50%;border: 1px solid #ccc;outline: none;}
.set-checkbox-style6 input[type="checkbox"]:checked{background: url(../images/icon_checkbox_green.png) 50%;background-size:16px 16px;border-radius: 50%;outline: none;border: 1px solid #60cabb;}
.set-checkbox-style6 input[type="checkbox"]{vertical-align: middle;margin-right: 7px;}
.set-checkbox-style7 input[type="checkbox"]{-webkit-appearance: none;width: 15px;height: 15px;border-radius: 2px;border: 1px solid #ccc;outline: none;}
.set-checkbox-style7 input[type="checkbox"]:checked{background: url(../images/icon_checkbox_green.png) 50%;background-size:16px 16px;border-radius: 2px;outline: none;border: 1px solid #60cabb;}
.set-checkbox-style7 input[type="checkbox"]{vertical-align: middle;margin-right: 7px;}

/*选择订单底部样式*/
.footer-pur{z-index: 999; justify-content: space-between;height: 50px; background: #fff; position: absolute; bottom: 0; width: 100%; color:#999999; font-size: 12px; }
.footer-pur label{margin: 0 15px;}
.footer-pur span{ background:#19bb72;  text-align: center; color: #fff; display: inline-block; border-radius: 20px; font-size: 12px; padding: 5px 15px; margin-right: 15px;}
/*对话框箭头*/
.triangle-out-bottom{
	position: absolute;
  	border: 6px solid transparent;
  	border-right-color: #00bb87;
  	bottom: -13px;
  	left: 50%;
  	margin-left: -6px;
  	border-left:6px solid transparent;
  	transform:rotate(-90deg);
	-ms-transform:rotate(-90deg); 	/* IE 9 */
	-moz-transform:rotate(-90deg); 	/* Firefox */
	-webkit-transform:rotate(-90deg); /* Safari å’Œ Chrome */
	-o-transform:rotate(-90deg); 	/* Opera */
}
.triangle-in-bottom{
	position: absolute;
  	border: 6px solid transparent;
  	border-right-color: #ffffff;
 	bottom: -11px;
 	left: 50%;
 	margin-left: -6px;
  	border-left:6px solid transparent;
  	transform:rotate(-90deg);
	-ms-transform:rotate(-90deg); 	/* IE 9 */
	-moz-transform:rotate(-90deg); 	/* Firefox */
	-webkit-transform:rotate(-90deg); /* Safari å’Œ Chrome */
	-o-transform:rotate(-90deg); 	/* Opera */
}
/*条形进度条*/
.progressbar_1{ 
	background-color:#eeeeee; 
    height: 5px; 
    width:55%;    
    color:#222222; 
	border-radius:10px;
	position: relative;
} 
.progressbar_1 .bar { 
    background-color:#0fd69c; 
    height:5px; 
	border-radius:10px;
	position: relative;
} 
.progressbar_1 .val-num{
	position: absolute;
	right: 0;
	top: -18px;
	font-size: 11px;
	color:#999999;			
}
/*上传照片通用*/
.default-uploader .weui-uploader__file{width: 62px;height: 62px;margin-right: 8px;margin-bottom: 8px;position: relative;}
.default-uploader .weui-uploader__input-box{width: 62px;height: 62px;border: none;}			
.default-uploader .icon_close{position: absolute;right: 0;width: 18px;height: 18px;line-height: 18px;text-align: center;background: rgba(0,0,0,0.5);border-radius: 0 0 0 6px;}
.default-uploader .icon_close:before{color: #ffffff;font-size: 14px;}									
.default-uploader .weui-uploader__input-box{width: 58px;height: 58px;border: 1px dashed #d9d9d9;}
.default-uploader .weui-uploader__input-box:after{width: 25.5px;}
.default-uploader .weui-uploader__input-box:before{height: 25.5px;}
.library .img-tit{ width: 100%; color: #999999; font-size: 12px; margin-top: 4px;}
/*placeholder*/
input::-webkit-input-placeholder{
    color: #cccccc;
}
input::-moz-placeholder{ 
    color: #cccccc;
}
input:-moz-placeholder{
    color: #cccccc;
}
input:-ms-input-placeholder{ 
    color: #cccccc;
} 
textarea::-webkit-input-placeholder{
    color: #cccccc;
}
textarea::-moz-placeholder{ 
    color: #cccccc;
}
textarea:-moz-placeholder{
    color: #cccccc;
}
textarea:-ms-input-placeholder{ 
    color: #cccccc;
}
/*群组消息头像*/
.list-title{position: relative;font-size: 16px;color: #181818;}
.list-title:before{content: "";width: 5px;height: 20px;background: #00cd91;border-radius: 0 2px 2px 0;position: absolute;left: 0;top: 50%;margin-top: -10px;} 
/*群组消息头像*/
.group-pic{margin-right: 10px;background: #e4e4e4;width: 41px;height: 41px;flex-wrap: wrap;border-radius: 3px;}
.group-pic img{margin: 0 1px!important;width: 17px!important;display: block;}
/*竖线*/
.line-title{position: relative;font-size: 16px;color: #181818;border-top: 1px solid #dddddd;padding:10px 15px 10px 20px; display: flex; justify-content: space-between;align-items: center;}
.line-title:before{content: "";width: 3px;height: 20px;background: #00cd91;border-radius: 0 2px 2px 0;position: absolute;left: 10px;top: 50%;margin-top: -10px;}
/*扫码查看*/
.code-div{width: 177px;height: 177px;position: relative;}
.code-div span{width: 25px;height: 25px;border: 4px solid #ff9f08;position: absolute;}
.code-div .lefttop{border-right: none;border-bottom: none;left: -15px;top: -15px;border-radius: 12px 0 0 0;}
.code-div .leftbottom{border-right: none;border-top: none;left: -15px;bottom: -15px;border-radius: 0 0 0 12px;}
.code-div .righttop{border-left: none;border-bottom: none;right: -15px;top: -15px;border-radius: 0 12px 0 0;}
.code-div .rightbottom{border-left: none;border-top: none;right: -15px;bottom: -15px;border-radius: 0 0 12px 0;}
.code-p{border-top: 1px dashed #fac25e;margin: 0 15px;height: 55px;font-size: 16px;color: #666666;margin-top: 10px;}
/*自定义下拉框*/
.defselect{position: relative;}
.defselect select{background: none;border: none;color: #ffffff;font-size: 16px;appearance: none;-moz-appearance: none;-webkit-appearance: none;padding-right: 20px;z-index: 999;outline: none;}
.defselect .icon_triangledown{position: absolute;right: 0;}
.defselect .icon_triangledown:before{vertical-align: top;margin: 3px 0 0 3px;color: #ffffff;}
/*自定义weui-cell*/
.default-cells{margin: 0 0 10px 0;}
.default-cells .weui-cell__bd{font-size: 14px;color: #333333;}
.default-cells .weui-cell__ft{font-size: 12px;color: #999999;}
.weui-cells.default-cells{border-bottom: 1px solid #eeeeee;}
.weui-cells.default-cells{border-bottom: none;}
.default-cells .weui-cell{padding: 13px;}
.default-cells .weui-cell:nth-child(2):before{border-top: 1px dashed #eeeeee;}
.default-cells .weui-cell:before {border-top: 1px solid #eeeeee;left: 0;}
.panel-view{background: #ffffff;margin: 12px;border-radius: 5px;position: relative;box-shadow: 0px 0px 10px 2px #eeeeee;}
/*textarea*/
.default-textarea{background: #f8f8f8;margin: 10px 18px;padding: 10px 15px;}
.default-textarea textarea{width: 100%;height: 55px;font-size: 13px;background: transparent;border: none;}
/*文本*/
.font12gray{font-size: 12px;color: #a7a8a9;}

/*头部通用*/
.common-top{color: #333333;padding: 5px 15px;
    background: #fff;    border-top: 1px solid #F6F6F6;
}
.common-num{height: 44px;color: #666666;line-height: 17px;padding: 0 10px 0 15px;border-radius: 30px 0 0 30px;margin-right: -15px;}

.common-top.weui-cell{padding: 5px 15px;}   
.calendar-con .toolbar{ font-size: 10px;}
.calendar-con .toolbar .toolbar-inner{height: auto;}
.calendar-con{background: #ffffff;box-shadow: 0 3px 15px 1px #ebebeb; margin: 0px 0;overflow: hidden;}	
.calendar-top{border-bottom: 1px solid #dddddd;display: -webkit-box;display: -webkit-flex;display: flex;justify-content: center;height:45px;font-size: 10px;color: #292621;/*margin: 0 5px; */align-items: center;}					
.calendar-week{font-size: 13px;text-align: center;padding: 10px 0;}
.calendar-label{position: relative;height:43px;margin-left: 1px;margin-bottom:1px;display: -webkit-box;display: -webkit-flex;display: flex;align-items:center;justify-content:center;flex-direction: column;color: #333333;font-size: 13px;}
.calendar-date .weui-flex__item:first-child .calendar-label{margin-left: 0;}
.card-mark{display: -webkit-box;display: -webkit-flex;display: flex;width: 100%;align-items: center;justify-content: center;margin-top: 5px;}
.linetxt{ color:  #EEEEEE; display: inline-block; margin: 0px 10px;}
