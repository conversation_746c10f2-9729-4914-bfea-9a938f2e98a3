<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>总览大数据</title>
    <link rel="stylesheet" href="../../css/reset.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../css/style.css"/>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/physical.css">
    <link rel="stylesheet" href="../../css/operation.css">
    <script type="text/javascript">
        // 修复parent.objdata问题
        try {
            if (parent && parent.objdata && parent.objdata.curtheme) {
                document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
            } else {
                // 如果没有父页面主题，使用默认主题
                document.write('<link rel="stylesheet" id="linktheme" href="../../css/blue.css" type="text/css" media="screen"/>');
            }
        } catch (e) {
            // 如果出错，使用默认主题
            document.write('<link rel="stylesheet" id="linktheme" href="../../css/blue.css" type="text/css" media="screen"/>');
        }
    </script>
    <!--[if lt IE 9]>
    <script src="../sys/html5shiv.min.js"></script>
    <![endif]-->
    <style type="text/css">
        html, body {
            background: #EAEFF3;
        }

        .layui-form-label {
            padding: 8px 0px 5px 10px;
            width: auto;
        }
    </style>
</head>
<body>
<div class="marmain" style="min-width: 950px;">
    <div class="content-medical">
        <div class="layui-form layui-comselect" style="position: relative">
            <div class="btn-nextfl">
                <span class="layui-form-label" style="text-align: left; padding-left: 0">时间范围：</span>
                <div class="layui-input-inline" style="float: left; width: 150px">
                    <input type="text" class="layui-input" id="startDate" placeholder="开始日期" readonly>
                </div>
                <div class="layui-form-mid">-</div>
                <div class="layui-input-inline layui-form" style="float: left; width: 150px">
                    <input type="text" class="layui-input" id="endDate" placeholder="结束日期" readonly>
                </div>
            </div>
        </div>
    </div>
    <div id="content" style="width: 100%;">
        <div class="marmain-cen">
            <div class="cenmartop">
                <div class="conference-sta">
                    <div class="total-div" style="margin-left: 0;"><img src="../../images/physical/nkindergarten01.png" class="kindergartenimg">
                        <div class="total-panel">
                            <div class="total-left"><img src="../../images/physical/zkindergarten01img.png"
                                                         class="kinder-ltimg">
                                <div class="total-numbg">
                                    <div class="num-label" id="totalViews">0</div>
                                    <div class="num-peo">预约挂号数</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="total-div"><img src="../../images/physical/zkindergarten02.png" class="kindergartenimg">
                        <div class="total-panel">
                            <div class="total-left"><img src="../../images/physical/zkindergarten02img.png"
                                                         class="kinder-ltimg">
                                <div class="total-numbg">
                                    <div class="num-label" id="avgReadTime">0</div>
                                    <div class="num-peo">AI健康问答</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="total-div"><img src="../../images/physical/zkindergarten03.png" class="kindergartenimg">
                        <div class="total-panel">
                            <div class="total-left"><img src="../../images/physical/zkindergarten03img.png"
                                                         class="kinder-ltimg">
                                <div class="total-numbg">
                                    <div class="num-label" id="totalArticles">0</div>
                                    <div class="num-peo">入园报告数</div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <div class="conference-sta">
                    <div class="total-div" style="margin-left: 0;"><img src="../../images/physical/nkindergarten04.png" class="kindergartenimg">
                        <div class="total-panel">
                            <div class="total-left"><img src="../../images/physical/zkindergarten04img.png"
                                                         class="kinder-ltimg">
                                <div class="total-numbg">
                                    <div class="num-label" id="totalShares">0</div>
                                    <div class="num-peo">健康宣教数</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="total-div"><img src="../../images/physical/zkindergarten05.png" class="kindergartenimg">
                        <div class="total-panel">
                            <div class="total-left"><img src="../../images/physical/zkindergarten05img.png"
                                                         class="kinder-ltimg">
                                <div class="total-numbg">
                                    <div class="num-label" id="totalLikes">0</div>
                                    <div class="num-peo">便民服务牌</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="total-div"><img src="../../images/physical/zkindergarten06.png" class="kindergartenimg">
                        <div class="total-panel">
                            <div class="total-left"><img src="../../images/physical/zkindergarten06img.png"
                                                         class="kinder-ltimg">
                                <div class="total-numbg">
                                    <div class="num-label" id="totalComments">0</div>
                                    <div class="num-peo">专家讲座</div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>


                <div class="conference-sta">
                    <div class="total-div" style="margin-left: 0;"><img src="../../images/physical/nkindergarten07.png" class="kindergartenimg">
                        <div class="total-panel">
                            <div class="total-left"><img src="../../images/physical/dockindergarten01img.png"
                                                         class="kinder-ltimg">
                                <div class="total-numbg">
                                    <div class="num-label" id="totalUsers">0</div>
                                    <div class="num-peo">专家坐诊</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="total-div"><img src="../../images/physical/zkindergarten08.png" class="kindergartenimg">
                        <div class="total-panel">
                            <div class="total-left"><img src="../../images/physical/zkindergarten08img.png"
                                                         class="kinder-ltimg">
                                <div class="total-numbg">
                                    <div class="num-label" id="totalQuestions">0</div>
                                    <div class="num-peo">推送通知</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="total-div"><img src="../../images/physical/zkindergarten09.png" class="kindergartenimg">
                        <div class="total-panel">
                            <div class="total-left"><img src="../../images/physical/zkindergarten09img.png"
                                                         class="kinder-ltimg">
                                <div class="total-numbg">
                                    <div class="num-label" id="totalAnswers">0</div>
                                    <div class="num-peo">定期报告数</div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <div class="visiting-statis">
                    <div class="safestat-cen">
                        <div class="safestat-tit"><i class="left-tit"></i><span>家长预约来源</span></div>
                        <div class="colunwit" id="notificationChart" style="height: 420px;"></div>
                    </div>
                    <div class="safestat-cen">
                        <div class="safestat-tit"><i class="left-tit"></i><span>渠道预约数据对比</span></div>
                        <div class="colunwit" id="answerNatureChart" style="height: 380px;"></div>
                    </div>
                </div>
                <div class="visiting-statis">
                    <div class="safestat-cen">
                        <div class="safestat-tit"><i class="left-tit greenbg"></i><span>预约挂号分析</span></div>
                        <div class="colunwit" id="parentActivityChart" style="height: 360px;"></div>
                    </div>
                    <div class="safestat-cen">
                        <div class="safestat-tit"><i class="left-tit"></i><span>预约时间分布</span></div>
                        <div class="colunwit" id="timeComparisonChart" style="height: 300px;"></div>
                    </div>

                </div>
                <div class="visiting-statis">
                    <div class="safestat-cen">
                        <div class="safestat-tit"><i class="left-tit"></i><span>各渠道预约人性别分布</span></div>
                        <div class="colunwit" id="healthEducationHeatChart" style="height: 350px;"></div>
                    </div>
                    <div class="safestat-cen">
                        <div class="safestat-tit"><i class="left-tit"></i><span>各渠道预约人年龄分布</span></div>
                        <div class="colunwit" id="departmentDistributionChart" style="height: 400px;"></div>
                    </div>
                </div>
                <div class="visiting-statis">
                    <div class="safestat-cen">
                        <div class="safestat-tit"><i class="left-tit"></i><span>各类活动预约统计</span></div>
                        <div class="colunwit" id="regionConsultationChart" style="height: 300px;"></div>
                    </div>
                    <div class="safestat-cen">
                        <div class="safestat-tit"><i class="left-tit"></i><span>健康宣教类别阅读热度</span></div>
                        <div class="colunwit" id="regionAgeDistributionChart" style="height: 360px;"></div>
                    </div>
                    <div class="safestat-cen">
                        <div class="safestat-tit"><i class="left-tit"></i><span>各渠道就诊人地域分布</span></div>
                        <div class="colunwit" id="regionLocationChart" style="height: 300px;"></div>
                    </div>
                </div>


                <div class="visiting-statis">
                    <div class="safestat-cen heighrow">
                        <div class="safestat-tit"><i class="left-tit"></i><span>家长画像</span></div>
                        <div class="colunwit ">
                            <div class=" weui-flex  hit50">
                                <div class="columnlist">
                                    <h3>性别</h3>
                                    <div id="parentGenderPie" style="height: 300px;"></div>
                                </div>
                                <div class="columnlist">
                                    <h3>访问设备</h3>
                                    <div id="devicePie" style="height: 300px;"></div>
                                </div>
                            </div>
                            <div class="weui-flex hit50">
                                <div class="columnlist">
                                    <h3>年龄分布</h3>
                                    <div id="parentAgePie" style="height: 300px;"></div>
                                </div>
                                <div class="columnlist">
                                    <h3>区域分布</h3>
                                    <div id="regionRadar" style="height: 300px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="visiting-statis">
                    <div class="safestat-cen">
                        <div class="safestat-tit"><i class="left-tit"></i><span>孩子与家长关系</span></div>
                        <div class="colunwit" id="relationBar" style="height: 400px;"></div>
                    </div>
                </div>
                <div class="visiting-statis">
                    <div class="safestat-cen">
                        <div class="safestat-tit"><i class="left-tit"></i><span>幼儿画像</span></div>
                        <div class="colunwit weui-flex ">
                            <div class="columnlist">
                                <h3>性别</h3>
                                <div id="childGenderPie" style="height: 300px;"></div>
                            </div>
                            <div class="columnlist">
                                <h3>年龄分布</h3>
                                <div id="childAgePie" style="height: 300px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script data-main="../../js/operation/Overview.js" src='../../sys/require.min.js'></script>
</body>
</html>
