﻿<!DOCTYPE html>
<html>
<head>
    <title>儿童死亡登记</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <link rel="stylesheet" href="../../css/reset.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <!--<script type="text/javascript">-->
    <!--document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');-->
    <!--</script>-->
    <style type="text/css">
        html, body {
            background: #EAEFF3;
        }

        .btn-nextlt {
            margin-top: 10px;
        }

        .layui-form-label {
            width: 70px;
        }

        .layui-form .layui-form-item {
            margin-bottom: 5px;
        }
        .layui-form .layui-form-item, .layui-btn {
            margin-bottom: 5px;
        }
        .tabhead{
            height: 20px;
        }
        /*.layui-table tr {*/
        /*    height: 100px;*/
        /*}*/
        hr{
            border: 1px dotted #999;
            margin: 2px;
        }
        .layui-table-cell {
            height: 55px;
            line-height: 45px;
        }
        .layui-table-cell>.tworow {
            line-height: 20px;
        }
        .layui-table-cell>span {
            line-height: 30px;
        }
    </style>
</head>
<body>
<div class="marmain">
    <div class="content-medical">
        <div class="layui-form" style="padding: 10px 10px 5px 10px;">
            <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                <label class="layui-form-label" style="padding: 8px 0px 5px 10px; text-align: left;">关键字：</label>
                <div class="layui-input-inline" style="min-height: 30px;width:300px;">
                    <input type="text" placeholder="请输入幼儿姓名/父母姓名/联系电话" class="layui-input" id="keywords">
                </div>
            </div>
            <button id="btnsearch" class="layui-btn form-search" style="vertical-align: top;">查询</button>
            <button id="btnadd" class="layui-btn form-search" style="vertical-align: top;">添加</button>
        </div>
    </div>
    <div class="layui-row marmain-cen">
        <!--    layui-col-xs5 layui-col-sm5 layui-col-md5-->
        <div class="" style="height: 100%;background: #ffffff;">
            <table id="tabelList" lay-filter="tabelList"></table>
        </div>
    </div>
</div>
<script type="text/html" id="zizeng">
    {{d.LAY_TABLE_INDEX+1}}
</script>
<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../../sys/jquery.js?v='" + v + "><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/arg.js?v='" + v + "><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/system.js?v='" + v + "><" + "/script>");
    document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v='" + v + "><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/function.js?v='" + v + "><" + "/script>");
    document.write("<script type='text/javascript' src='../../js/child/child_dielist.js?v='" + v + "><" + "/script>");
</script>
</body>
</html>
