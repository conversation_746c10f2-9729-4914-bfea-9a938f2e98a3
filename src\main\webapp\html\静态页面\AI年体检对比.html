﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>AI体检对比</title>
    <link rel="stylesheet" href="../../css/reset.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../css/style.css"/>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/physical.css">
    <script type="text/javascript" src="../../layui-btkj/layui.js"></script>
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <!--[if lt IE 9]>
    <script src="../sys/html5shiv.min.js"></script>
    <![endif]-->
    <style type="text/css">
        html, body {
            background: #EAEFF3;
        }
       .layui-form-label {padding: 8px 0px 5px 10px; width: auto;}   
    </style>
</head>
<body>
<div class="marmain" style="min-width: 950px;">
    <div class="content-medical">
        <div class="layui-form layui-comselect" style="position: relative; ">
            <div class="btn-nextfl">
                <span class="layui-form-label" style="text-align: left;padding-left: 0;">年份：</span>
                <div class="layui-input-inline" style="float: left; width: 120px;">
                    <select id="selarea" lay-filter="selarea">
						   <option value="" selected="">2014</option>
                        <option value="0">2015</option>
                    </select>
                </div>
                <span class="layui-form-label isshowenrollstatus">园所：</span>
                <div class="layui-input-inline layui-form isshowenrollstatus" style="float:left;width:120px;" id="diverollstatus">
                    <select id="isneroll" lay-filter="isneroll">
                        <option value="" selected="">全部</option>
                        <option value="0"></option>
                      
                    </select>
                </div>
          
            </div>
       
        </div>
    </div>
    <div id="content" style="width: 100%;">
        <div class="marmain-cen">

		<div  class="cenmartop">

			 <div class="conference-sta" >
				<div class="total-div" style="margin-left: 0;">
					  <img src="../../images/physical/nkindergarten01.png" class="kindergartenimg" >
						<div class="total-panel">
							<div  class="total-left">
							 <img src="../../images/physical/kindergarten01img.png" class="kinder-ltimg" >
							<div class="total-numbg">
									<div class="num-label">298</div>
									<div  class="num-peo">辖区内园所数</div>
								</div>
							</div>
							<div  class="total-right">
								<span>+2%</span>
								<i class="iconfont icon_arrow_goup"></i>

							</div>
			              </div>

				 </div>
				 	<div class="total-div">
					  <img src="../../images/physical/nkindergarten02.png" class="kindergartenimg" >
						<div class="total-panel">
							<div class="total-left">
							 <img src="../../images/physical/kindergarten02img.png" class="kinder-ltimg" >
							<div class="total-numbg">
									<div class="num-label">296</div>
									<div  class="num-peo">定期体检已预约园所数</div>
								</div>
							</div>
							<div class="total-right">
								<span>+5%</span>
								<i class="iconfont icon_arrow_goup"></i>

							</div>
			              </div>

				 </div>
				 	<div class="total-div">
					  <img src="../../images/physical/nkindergarten03.png" class="kindergartenimg" >
						<div class="total-panel">
							<div class="total-left">
							 <img src="../../images/physical/kindergarten03img.png" class="kinder-ltimg" >
							<div class="total-numbg">
									<div class="num-label">2</div>
									<div  class="num-peo">定期体检未预约园所数</div>
								</div>
							</div>
							<div class="total-right">
								<span>-10%</span>
								<i class="iconfont icon_arrow_goup"></i>

							</div>
			              </div>

				 </div>
				 	<div class="total-div">
					  <img src="../../images/physical/nkindergarten02.png" class="kindergartenimg" >
						<div class="total-panel">
							<div class="total-left">
							 <img src="../../images/physical/kindergarten04img.png" class="kinder-ltimg" >
							<div class="total-numbg">
									<div class="num-label">58900</div>
									<div  class="num-peo">辖区内幼儿总数</div>
								</div>
							</div>
							<div class="total-right">
								<span>+2%</span>
								<i class="iconfont icon_arrow_goup"></i>

							</div>
			              </div>

				 </div>
			
			
			 </div>
				 <div class="conference-sta" >
				<div class="total-div" style="margin-left: 0;">
					  <img src="../../images/physical/nkindergarten05.png" class="kindergartenimg" >
						<div class="total-panel">
							<div  class="total-left">
							 <img src="../../images/physical/kindergarten05img.png" class="kinder-ltimg" >
							<div class="total-numbg">
									<div class="num-label">58540</div>
									<div  class="num-peo">定期体检已提交幼儿总数</div>
								</div>
							</div>
							<div  class="total-right">
								<span>+8%</span>
								<i class="iconfont icon_arrow_goup"></i>

							</div>
			              </div>

				 </div>
				 	<div class="total-div">
					  <img src="../../images/physical/nkindergarten06.png" class="kindergartenimg" >
						<div class="total-panel">
							<div class="total-left">
							 <img src="../../images/physical/kindergarten06img.png" class="kinder-ltimg" >
							<div class="total-numbg">
									<div class="num-label">58540</div>
									<div  class="num-peo">预约收费套餐人数</div>
								</div>
							</div>
							<div class="total-right">
								<span>+4%</span>
								<i class="iconfont icon_arrow_goup"></i>

							</div>
			              </div>

				 </div>
				 	<div class="total-div">
					  <img src="../../images/physical/nkindergarten07.png" class="kindergartenimg" >
						<div class="total-panel">
							<div class="total-left">
							 <img src="../../images/physical/kindergarten07img.png" class="kinder-ltimg" >
							<div class="total-numbg">
									<div class="num-label">58540</div>
									<div  class="num-peo">定期体检报告发布数</div>
								</div>
							</div>
							<div class="total-right">
								<span>-10%</span>
								<i class="iconfont icon_arrow_decline"></i>

							</div>
			              </div>

				 </div>
				 	<div class="total-div">
					  <img src="../../images/physical/nkindergarten08.png" class="kindergartenimg" >
						<div class="total-panel">
							<div class="total-left">
							 <img src="../../images/physical/kindergarten08img.png" class="kinder-ltimg" >
							<div class="total-numbg">
									<div class="num-label">58900</div>
									<div  class="num-peo">定期体检报告未发布数</div>
								</div>
							</div>
							<div class="total-right">
								<span>+4%</span>
								<i class="iconfont icon_arrow_goup"></i>

							</div>
			              </div>

				 </div>
			
			 </div>
	        <div class="visiting-statis">
					<!--收费套餐预约占比-->
					 <div class="safestat-cen" >
						 <div class="safestat-tit">
							<i class="left-tit" ></i><span >收费套餐预约比率</span>
						  </div>
					 </div>
					 <div class="safestat-cen">
						  <div class="safestat-tit">
									<i class="left-tit"></i><span >体格发育异常率</span>
						  </div>

					 </div>
				 <div class="safestat-cen" >
						  <div class="safestat-tit">
									<i class="left-tit"></i><span >高危儿儿童比率</span>
						  </div>

					 </div>
				</div>
			  <div class="visiting-statis">
					<!--体格检查异常率-->
					 <div class="safestat-cen" >
						 <div class="safestat-tit">
							<i class="left-tit" ></i><span>体格检查异常率</span>
						  </div>
					 </div>
					 <div class="safestat-cen">
						  <div class="safestat-tit">
									<i class="left-tit"></i><span>体格发育异常率</span>
						  </div>

					 </div>
				 <div class="safestat-cen" >
						  <div class="safestat-tit">
									<i class="left-tit"></i><span>两次随访间患病率</span>
						  </div>

					 </div>
				</div>
		
                
            </div>
        </div>
    </div>
</div>
<script>
		//Demo
layui.use('form', function(){
var form = layui.form;

//监听提交
form.on('submit(formDemo)', function(data){
layer.msg(JSON.stringify(data.field));
return false;
});
});
</script>
</body>
</html>
