/*
內容摘要:角色管理
测定分析
修改日期：2024.8.6
*/
require.config({
    paths: {
        jquery: '../../sys/jquery',
        system: '../../sys/system',
        layui: '../../layui-btkj/layui'
    },
    shim: {
        'system': {
            deps: ['jquery']
        },
        'layui': {
            deps: ['jquery', 'system']
        }
    },
    waitSeconds: 0
});
var objdata = {
    apptype: 'area', //apptype
    objRolePid: {}, //角色对象
    arrRoleTree: [], //角色树数组
    objframe: {
        '0': ['是', '#1e9fff'],
        '1': ['否', '#ff5722']
    }
};
require(['jquery', 'system', 'layui'], function () {
    layui.use(['table', 'form', 'treeTable'], function () {
        if (Arg('apptype')) {
            objdata.apptype = Arg("apptype");
        }
        var layer = layui.layer;
        var table = layui.table;
        var form = layui.form;
        var treetable = layui.treeTable;

        window.getData = function (cb) {
            parent.layer.load();
            var keyword = $('#keywords').val();
            var objwhere = {};
            if (keyword) {
                objwhere.keyword = keyword;
            }
            $.sm(function (re, err) {
                if (err) {
                    parent.layer.msg(err);
                    parent.layer.closeAll("loading");
                } else {
                    objdata.objRolePid = {};

                    for (var i = 0; i < re[1].length; i++) {
                        var item = re[1][i];
                        if (!objdata.objRolePid[item.roletype]) {
                            objdata.objRolePid[item.roletype] = [];
                        }
                        objdata.objRolePid[item.roletype].push(item);
                    }

                    // 收集所有顶层菜单的pid，即那些没有对应id的pid
                    objdata.objRoleType = {};
                    var topPids = new Set();
                    for (var i = 0; i < re[0].length; i++) {
                        var item = re[0][i];
                        topPids.add(item.roletype);
                        objdata.objRoleType[item.roletype] = item;
                    }

                    // 将所有顶层菜单的子菜单递归展开并合并
                    var allMenus = Array.from(topPids).reduce(function (acc, pid) {
                        return acc.concat(window.getTypeAll(pid));
                    }, []);

                    cb && cb(allMenus);
                }
            }, ["w_role.list", JSON.stringify(objwhere)]);
        };

        //递归获取数据
        window.getTypeAll = function (pid) {
            var arrnew = [];
            var item = objdata.objRoleType[pid];
            var childrenList = getRoleAll(item.roletype, item.num_coding);
            item.isParent = true;
            item.children = childrenList || [];
            arrnew.push(item);
            return arrnew;
        };

        //递归获取数据
        window.getRoleAll = function (pid, num_coding) {
            var arrnew = [];
            var arr = objdata.objRolePid[pid] || [];
            for (var i = 0; i < arr.length; i++) {
                var item = JSON.parse(JSON.stringify(arr[i]));
                item.num_coding = num_coding;
                var childrenList = [];
                if (childrenList && childrenList.length) {
                    item.isParent = true;
                } else {
                    item.isParent = false;
                }
                item.children = childrenList;
                arrnew.push(item);
            }
            return arrnew;
        };

        window.render = function (arr) {
            treetable.render({
                elem: '#tableList',
                id: 'tableList', // 自定义 id 索引
                data: arr,
                tree: {
                    customName: {
                        pid: "roletype",
                        name: "rname"
                    },
                    view: {
                        expandAllDefault: true
                    },
                    data: {
                        rootPid: "0"
                    }
                },
                page: false,
                height: 'full-' + ($(".toolbar").height() + 42),
                cols: [
                    [
                        // {type: 'checkbox', fixed: 'left'},
                        // {field: 'id', title: 'ID', width: 80, fixed: 'left', align: 'center'},
                        {type: 'numbers', title: '序号', width: 60},
                        {field: 'rname', width: 200, title: '角色名称', align: 'left'},
                        {field: 'rolesort', title: '排序', width: 80, align: 'center'},
                        {field: 'rolekey', title: '权限标识', width: 150, align: 'left'},
                        {
                            field: 'status', title: '是否可用', align: 'center', width: 100, templet: function (d) {
                                var arrtype = objdata.objframe[d.status];
                                var typeName = arrtype[0];
                                var cls = arrtype[1];
                                return '<span class="layui-btn-sm" style="color:white;background-color: ' + cls + '">' + typeName + '</span>';
                            }
                        },
                        {field: 'remark', title: '备注', width: 180},
                        {
                            title: '操作', width: 250, align: 'center', fixed: 'right', templet: function (d) {
                                var arrhtml = [];
                                if (!d.coding) {
                                    arrhtml.push('<a class="layui-btn layui-btn-xs btnEdit" lay-event="edit" title="编辑" lay-perm="sys:role:edit"><i class="layui-icon">&#xe630;</i>编辑</a>');
                                    arrhtml.push('<a class="layui-btn layui-btn-danger layui-btn-xs btnDel" lay-event="del" title="删除" lay-perm="sys:role:del"><i class="layui-icon">&#xe630;</i>删除</a>');
                                    arrhtml.push('<a class="layui-btn layui-btn-xs " lay-event="keyrole" title="权限分配" lay-perm="sys:role:perm">权限分配</a>');
                                } else {
                                    arrhtml.push('<a class="layui-btn layui-btn-xs layui-btn-normal btnAdd2" lay-event="add" lay-perm="sys:role:add"><i class="layui-icon">&#xe654;</i>添加</a>');
                                }
                                return arrhtml.join('');
                            }
                        }
                    ]
                ],
                done: function (o1, o2, count) {
                    //获取权限
                    $.buttonPermShow();
                    parent.layer.closeAll("loading");
                }
            });
        };
        table.on('tool(tableList)', function (obj) {
            var data = obj.data; //获得当前行数据
            if (obj.event === 'del') {
                window.remove(data.id);
            } else if (obj.event === 'edit') {
                openwin("edit", data.id, data.rname, data.roletype, data.num_coding);
            } else if (obj.event === 'add') {
                openwin("add", "", data.rname, data.roletype, data.num_coding);
            }else if (obj.event === "keyrole") {
                openwin("area", data.id, data.rname, data.roletype, data.num_coding);
            }
        });

        form.on('switch(status)', function (obj) {
            jQuery.getparent().layer.closeAll('loading');
            $.sm(function (re, err) {
                if (err) {
                    parent.layer.msg(err, {shift: 6});
                } else {
                    layer.msg('修改成功');
                }
            }, ["role.update", JSON.stringify({
                status: this.checked ? 0 : 1
            }), $.msgwhere({id: [obj.value]})])
        });


        window.init = function () {
            getData(function (arr) {
                render(arr);
            });
        };

        init();

        //查询
        $("#search").click(function () {
            init();
        });
        //添加
        $("#add").click(function () {
            openwin("add")
        });
        //全部展开
        $("#expand").click(function () {
            treetable.expandAll("tableList", true);
            $.buttonPermShow();
        });
        //全部折叠
        $("#collapse").click(function () {
            treetable.expandAll("tableList", false);
            $.buttonPermShow();
        });

        window.remove = function (id) {
            layer.confirm('确定要删除该角色', {icon: 3, title: '提示'}, function (index) {
                $.sm(function (re, err) {                //sm是一个ajax请求吗?
                    jQuery.getparent().layer.closeAll('loading');
                    if (err) {
                        parent.layer.msg(err, {shift: 6});
                    } else {
                        init();
                        jQuery.getparent().layer.close(index);
                        jQuery.getparent().layer.msg('删除成功！', {icon: 1});
                    }
                }, ["role.update", JSON.stringify({
                    isdel: 1
                }), $.msgwhere({id: [id]})]);
                layer.close(index);
            });
        }
    })
});

function openwin(type, id, rname, roletype, num_coding) {
    switch (type) {
        case "add":
        case "edit":
            jQuery.getparent().layer.open({
                type: 2,
                title: (type == "add" ? "添加" : "编辑" + rname) + '角色',
                shadeClose: false,
                area: ['450px', '700px'],
                content: 'html/sysapp/roleedit.html?v=' + Arg("v") + '&type=' + type + '&mid=' + Arg("mid") + "&id=" + (id ? id : 0) + "&roletype=" + roletype,
                success: function (layero, index) {
                },
                btn: ["保存", "取消"],
                yes: function (index, layero) {     //或者使用btn1
                    var w = layero.find('iframe')[0].contentWindow
                    w.$("#saveOK").trigger("click", function () {            //提交按钮
                        jQuery.getparent().layer.close(index);        //"btnok" 被点击后，关闭当前的模态窗口。
                        window.init();
                    });
                },
                no: function (index, layero) {
                    jQuery.getparent().layer.close(index);        //"editOK" 被点击后，关闭当前的模态窗口。
                    window.init();
                }
            });
            break;
        case "area":
            // case "school":
            jQuery.getparent().layer.open({
                type: 2,
                title: "编辑" + rname + '权限分配',
                shadeClose: false,
                shade: 0.8,
                area: ['350px', '700px'],
                content: 'html/sysapp/rolemenutree.html?v=' + Arg("v") + '&num_coding=' + num_coding + '&mid=' + Arg("mid") + "&id=" + (id ? id : 0) + "&apptype=" + type,
                success: function (layero, index) {
                },
                btn: ["保存", "取消"],
                yes: function (index, layero) {        //或者使用btn1
                    var w = layero.find('iframe')[0].contentWindow;
                    w.saveEvent(function () {            //提交按钮
                        jQuery.getparent().layer.msg("分配成功！");
                        jQuery.getparent().layer.close(index);        //"addOK" 被点击后，关闭当前的模态窗口。
                        $("#search").trigger("click");
                    });
                },
                no: function (index, layero) {
                    jQuery.getparent().layer.close(index);        //"addOK" 被点击后，关闭当前的模态窗口。
                }
            });
            break;
        default:
            break;
    }
}