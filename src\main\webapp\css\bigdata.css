/*大数据页面头部时间*/
.bigdata-time{position: absolute; left:35px;top:20px; color: #D5D6E2;}
.bigdata-time label{display: block;line-height: 28px; font-size: 14px;}
/*大数据总览*/
.bigdata-title{vertical-align: top;padding: 0 130px;height: 71px;line-height: 60px;display: inline-block;font-size: 30px;color: #ffffff;}
.bigdata-top{ text-align: center;width: 100%;height: 71px;background:url(../images/xbimages/titlebg.png) no-repeat 100% 100%;background-size: 100% 100%;}
.bigdata-leftright{font-size: 16px;color: #52ffff;position: absolute;top: 35px;left: 20px;}
.bigdata-topright{display: inline-block;font-size: 16px;color: #52ffff;position: absolute;top: 30px;right: 20px;}
.bigdata-con{background: #030C15;background-size: 100% auto;position: absolute;top: 0px;bottom: 0;width: 100%;}
.bigdata-con .bigdata-cell{display: inline-block;font-size: 14px;vertical-align: top;}
.bigdata-list.whole-border:after{bottom: -15px;right: 0;}
.btm-mark{width: 55px;height: 4px;display: inline-block;background: #52ffff;position: absolute;left: -14px;bottom: -8px;z-index: 9;}
.bigdata-detail{height: 100%;/*margin: 6px 9px 0 -18px;*/font-size: 14px;color: #ffffff;}
.bigdata-detail{/*position: relative;display: inline-block;width: 100%;padding-right: 10px;*/}
.bigdata-time{position: absolute; left: 10px;top: 22px;}
/*新增*/
.allergy-tit{background:#08172A; margin: 10px auto; text-align: center; padding: 10px 0; width: 90%}
/*标题*/
.title-cell{/*position: absolute;top: -31px;width: 100%;*/height: 31px;line-height: 36px;font-size: 16px;}
.data-name{display: inline-block;border-top: 1px solid #395eb7;min-width: 190px;color: #65c6e7;}
.datacell-title{font-size: 15px;color: #ffffff;margin: 10px 0; position: relative; padding-left: 20px;}
/*各专案类型中男孩女孩情况分析*/
.project-anal{background:#fafafa; overflow: hidden; border: 1px  solid  #eeeeee; padding:15px 0;margin: 20px;min-width: 1200px;}
.analysis-compar{ margin:0 auto; float:left; width:581px;}
.analysis-left{ position:relative; width:120px; float:left; margin-left:80px;}
/*环形进度条*/
.progress-cell{display: inline-block;margin: 2% 6%;}
.progress-cell .procell-con{display: inline-block;margin: 0 auto;width: 72px;height: 72px; float:left;}
.wrapouter,.circle,.percent{
	position: absolute;
	width: 72px;
	height: 72px;
	border-radius: 50%;
}
.wrapouter{
	background-color: #eee;
}
.circle{
	box-sizing: border-box;
	border:4px solid #eee;
	clip:rect(0,72px,72px,36px);
}
.clip-auto{
	clip:rect(auto, auto, auto, auto);
}
.percent{
	box-sizing: border-box;
	top:-4px;
	left:-4px;
}
.perleft{
	transition:transform ease;
	border:4px solid #fc784f;
	clip: rect(0,36px,72px,0);
}
.perleft.red{
	border:4px solid red;
}
.perright{
	border:4px solid #fc784f;
	clip: rect(0,72px,72px,36px);
}
.wth0{
	width:0;
}
.centercontent{
	position: absolute;
	box-sizing: border-box;
	width: 64px;
	height: 64px;
	text-align: center;
	left: 4px;
	top: 4px;
	border-radius: 50%;
	background-color: #fff;
	z-index: 1;
	display: flex;
	display: -webkit-flex;
	align-items: center;

	justify-content: center;
	flex-direction: column;
	color: #52ffff;
	background: #03162d;
}
/*!*食谱均衡指数*!*/
.reportrate .perleft{border-color:#E4B23F;}
.reportrate .perright{border-color:#E4B23F;}
.reportrate .centercontent{color:#E4B23F;}
.goodrate .perleft{border-color:#56C964;}
.goodrate .perright{border-color:#56C964;}
.goodrate .centercontent{color:#56C964;}

.energy-tab{text-align: center;margin: 0;}
.energy-tab span{line-height: 26px;margin: 0 5px;position: relative;padding: 10px 0;cursor: pointer;color: #516592;}
.energy-tab span.current{color: #52ffff;}
.energy-tab span.current:after{content: "";position: absolute;bottom: 0;left: 0;width: 100%;height: 4px;background: #52ffff;}
.energy-tab .iconfont{vertical-align: top;margin-right: 5px;}
.energy-tab span.current .iconfont:after{color: #52ffff;}
.school-cell{color: #65c6e7;margin: 5px 30px;display: inline-block;line-height: 25px;width: -webkit-fill-available;}
.school-cell label{float: right;}
.food-label{position: relative;}
.food-label label{background: rgba(0,255,255,0.3);display: inline-block;border-radius: 20px;padding: 0px 8px;color: #03d3d3;position: absolute;}
.food-label label.current{font-size: 16px;background: rgba(3,211,211,0.5);border: 1px solid #52fefe;}
.food-label a{background: rgba(0,255,255,0.3);display: inline-block;border-radius: 20px;padding: 0px 8px;color: #03d3d3;position: absolute;}
.food-label a.current{font-size: 16px;background: rgba(3,211,211,0.5);border: 1px solid #52fefe;}
.radar-map{position: relative;}
.radar-map .radar-txt{position: absolute;font-size: 12px;color: #52ffff;}
.datatxt-cell{margin: 24px 28px 0 0;float: right;position: absolute;top:44px;right: 0;}
.datatxt-cell li{cursor: pointer;width: 112px;height: 30px;line-height: 30px;margin-bottom: 2px;text-align: center;font-size: 16px;color: #BBB;background: #08172A;}
.datatxt-cell li.current{color: #FFFFFF;background: #0E2038;}
.circle-con{display: inline-block;background: rgba(0,0,0,0.6);width: 232px;height: 232px;position: absolute;top: 0;z-index: 9;border-radius: 0 0 100% 0;overflow: hidden;}
.circle-cell{cursor: pointer;display: inline-block;width: 237px;height: 62px;background: url(../images/circle_cellbg.png);line-height: 68px;color: #22a0af;position: absolute;}
.circle-cell span{width: 70px;display: inline-block;margin-left: 92px;}
.circle-cell.current{color: #52ffff;background: url(../images/circle_cellbg_HL.png);}
.circle-cell img{margin-top: -6px;}
.circle-cell.current img{border: 1px solid #52ffff;}
.opr-icon{width: 78px;height: 78px;background: #1dd3d4;border-radius: 0 0 100% 0;cursor: pointer;z-index: 99;position: absolute;}
/*大数据-传染病患病监控*/
.data-state{position: relative;margin: 3px auto;text-align: right;margin-right:40px;font-size: 14px;color: #e4b23d;font-weight: bold;/*background: url(../images/data_numbg3.png)no-repeat;background-size: 100% 100%;*/height:30px;line-height: 30px;width: 80%;}
.data-state.current{/*background: url(../images/data_numbg4.png)no-repeat;background-size: 100% 100%*/;color: #fe8d97;}
.progress-list{font-size: 12px;color: #BBB;display: inline-block;width: 100%;padding: 2% 2%;}
.progress-list li{margin: 9px 0;}
.pro-content{display: inline-block;height: 8px;border-radius: 10px;background: none;width: 60%;margin: 0 5px;vertical-align: middle;}
.pro-bar{height: 8px;border-radius: 10px;vertical-align: top;display: inline-block;}
.pro-name{display: inline-block;width: 120px;text-align: right;}
.pro-txt{display: inline-block;margin: -4px 0 0 10px;vertical-align: top;line-height: 15px;}
/*大数据-膳食营养数据展示*/
.tower-con{display: inline-block;position: relative;height: 100%;}
.tower-cell{background-size: 100% 100%;display: table;color: #52ffff;text-align: left;float: right;margin-left: -12px;}
.tower-txt{display: table-cell;vertical-align: middle;}
.cirbefore{position: relative;}
.cirbefore:before{content: "";width: 8px;height: 8px;display: inline-block;border-radius: 50%;background: red;position: absolute;left: -15px;top: 8px;}
.cirbefore.cirred:before{background: #ef8678;}
.cirbefore.cirpink:before{background: #e39b6a;}
.cirbefore.ciryellow:before{background: #ebc66c;}
.cirbefore.cirgreen:before{background: #7bc497;}
.cirbefore.cirblue:before{background: #5db9e9;}
.nutrient-cell{font-size: 0;}
.nutrient-cell .iconfont{display: inline-block;height: 20px;vertical-align: top;}
.nutrient-cell p{height: 20px;line-height: 20px;}
.nutrient-cell li{cursor: pointer;display: inline-block;text-align: center;font-size: 12px;width: 10%;vertical-align: top;color: #506490;position: relative;}
.nutrient-cell li.current{color: #52ffff;}
.nutrient-cell li.current .iconfont{color: #52ffff;}
/*.nutrient-cell li.current:after{content: "";height: 4px;width: 100%;position: absolute;bottom: -4px;left: 0;background: #eee;}*/
.progress-list.nutrient-pro{margin: 0 10px;padding-bottom: 7px;}
.progress-list.nutrient-pro .iconfont{vertical-align: middle;height: 22px;display: inline-block;}
.progress-list.nutrient-pro .iconfont:after{color: #ffd800;}
.progress-list.nutrient-pro li{margin: 4px 0;}
.progress-list.nutrient-pro li .pro-content{background: #010d19;width: 64%;}
.progress-list.nutrient-pro li .pro-content .pro-bar{background: #52ffff;}
.nutrient-name{width: 53px;display: inline-block;text-align: center;}
table.data-table{background: rgba(80,100,144,0.2);color: #ffffff;font-weight: normal;}
table.data-table tbody tr{height: 45px;}
table.data-table thead{background: #081526;height: 45px;color: #ffffff;}
table.data-table tr th,table.data-table tr td{padding: 1px;text-align: center;line-height: 18px;font-weight: normal;}
table.data-table thead tr th:first-child{width: 65px;}
table.data-table tbody tr td{background: #0B1828;}
.tower-sub{position: absolute;height: 94%;left: 50%;top: 0;margin-top: 6%;}
/*身体素质指标监控*/
.analyse-txt{font-size: 14px;position: relative;margin: 10px auto;width:100%;text-align: center; display: inline-block;}
/*.analyse-txt.girltxt{color: #fe8d97;}  */
/*.analyse-txt.boytxt{color: #6799ff;} */
/*.analyse-txt img{position: absolute;!*top: -20px;left: -24px;*!}*/
.analyse-txt .analyse-label{width: 190px;height: 31px;line-height: 31px;display: inline-block;text-align: center;}
.analyse-txt.girltxt .analyse-label{border-radius: 10px 0 10px 0;background: #F75457;}
.analyse-txt.boytxt .analyse-label{border-radius: 10px 0 10px 0;background: #517CFF;}
.result-cell{margin: 3px 0;}
.result-cell .result-img{/*border: 2px solid #52ffff;*/width: 40px;height: 40px;display: inline-block;vertical-align: middle;border-radius: 50%;}
.result-cell .result-txt{display: inline-block;vertical-align: middle;text-align: left;}
.result-txt {width: 26%;}
.result-txt p{/*color: #52ffff;*/font-size: 16px;}
.result-txt i{display: inline-block;vertical-align: top;height: 14px;margin-right: 5px;}
.result-txt span{color: #f0af22;font-size: 12px;width: 170px;display: inline-block;}
.result-label{display: inline-block;vertical-align: middle;width: 65%;}
.result-label span{display: inline-block;position: relative;vertical-align: middle;background: red;width: 23%;height: 14px;text-align: center;font-size: 16px;color: #ffffff;}
.result-label span i{position: absolute;top: -20px;left: 0;width: 100%;}
.result-label span:nth-child(1){background: rgba(255,70,40,0.2);border-radius: 3px 0 0 3px;}
.result-label span:nth-child(1).current{background: rgb(255,70,40);height: 20px;line-height: 20px;}
.result-label span:nth-child(1).current i{color: rgb(255,70,40);}
.result-label span:nth-child(2){background: rgba(255,156,0,0.2);}
.result-label span:nth-child(2).current{background: rgb(255,156,0);height: 20px;line-height: 20px;}
.result-label span:nth-child(2).current i{color: rgb(255,156,0);}
.result-label span:nth-child(3){background: rgba(29,173,255,0.2);}
.result-label span:nth-child(3).current{background: rgb(29,173,255);height: 20px;line-height: 20px;}
.result-label span:nth-child(3).current i{color: rgb(29,173,255);}
.result-label span:nth-child(4){background: rgba(12,211,125,0.2);border-radius: 0 3px 3px 0;}
.result-label span:nth-child(4).current{background: rgb(12,211,125);height: 20px;line-height: 20px;}
.result-label span:nth-child(4).current i{color: rgb(12,211,125);}
/*大数据疾病数据*/
.diseasedata{margin: auto;}
.diseasedata-left{width:70%;}
.diseasedata-right{width:29.5%;}
.diseasedata-list{display: inline-block;font-size: 14px;vertical-align: top;}
.diseasedata-top{height:100%;position: relative;margin-bottom: 15px;background:#050F1B;margin: 10px 0;border:1px solid #0E1621;}
.diseasedata-top h3{position:relative; line-height:50px; height: 50px; width: 100%; background:#0E1726;}
.diseasedata-top .report-rtit{display: inline-block; background: #ffffff;width:4px;height: 20px;position: absolute;top: 15px;left: 20px; }
.diseasedata-top h3 span{color: #ffffff;font-size: 18px; padding-left: 34px;}
.datacell-title .report-rtit{display: inline-block; background: #ffffff;width:4px;height: 18px;position: absolute;top: 0px;left:10px; }
.diseasedata-top h3 span{color: #ffffff;font-size: 18px; padding-left: 34px;}
.img_map{margin: 0 auto; text-align: center; height: 82%;}
.img_map img{height: 100%;}
ul.chart-list{color: #fff;padding:0 15px 0px 15px;}
ul.chart-list li{margin-bottom: 9px;overflow:hidden;text-overflow: ellipsis;white-spa.datacell-titlece: nowrap; border-bottom: 2px solid #0A121F;line-height: 40px; height: 40px;}
ul.chart-list li img{margin-right: 7px;}
ul.chart-list li span{float: right;}
.body-num{
	text-align: center;
	display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-ms-flex-align: center;
	align-items: center;
	cursor: pointer;}
.body-num p{text-align: center;}
.data-per-num{display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-ms-flex-align: center;
	align-items: center;
	cursor: pointer;}
.data-per-num span{margin:0 30px 0 8px;font-size: 20px;vertical-align: middle;}
.data-per-num label{margin-right: 5px; font-size: 24px;}
.data-zhzs{display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-ms-flex-align: center;
	align-items: center;flex-wrap:wrap;  height: 70%;}
.font-txt{font-size: 48px;}



@media screen and (min-width:1000px) and (max-width:1460px){
	.diseasedata-left{width:65%;}
	.diseasedata-right{width:34.5%;}
	.font-txt{font-size: 40px;}
	.data-per-num label{font-size: 20px;}
}
@media screen and (max-height: 880px){
	.result-cell .result-img{width: 35px;height: 35px;}
	.result-cell .result-txt{line-height: 14px;}
	.result-txt p{font-size: 13px;}
	.result-label span{font-size: 12px;}
}
/*食物过敏监控*/
.progress-list li{}
.school-list{text-align: center;}
.school-list li{font-size: 0;margin: 10px 0;}
.school-name{color: #BBB;display: inline-table;width: 23%;vertical-align: sub;text-align: center;line-height: 14px;font-size: 14px;height: 28px;padding: 0 8px;}
.school-name span{display: table-cell;vertical-align: middle;margin: 0 10px;}
.school-txt{color: #BBB;display: inline-block;vertical-align: top;background: #023a4d;height: 28px;line-height: 28px;border-radius: 4px;width: 70%;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;font-size: 14px;}
.school-txt p{width: 96%;margin: 0 10px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;}
.analyse-cell{margin: 8px 0;}
.analyse-cell .pro-text{display: inline-block;width: 45%;text-align: center;}
.analyse-cell .pro-content{height: 26px;border-radius: 3px;width: 50%;margin: 0;font-size: 12px;}
.analyse-cell .pro-content .pro-bar{height: 26px;border-radius: 3px;}
.analyse-cell .pro-content .pro-txt{line-height: 26px;margin:  0;float: right;}
.analyse-cell .pro-num{display: inline-block;width: 50px;height: 50px;line-height: 44px;text-align: center;border: 4px solid #e15d68;border-radius: 50%;box-sizing: border-box;}
/*幼儿体检指标监控*/
.phy-label{width: 180px;height: 60px;margin: 2px 0;text-align: center;background: #081729;vertical-align: middle;}
.phy-label i{width: 39px;height: 36px;line-height: 36px;display: inline-block;vertical-align: middle;text-align: center;background-size: 100% 100%!important;}
.phy-name{display: inline-block;margin: 0px 0 4px 0;position: relative;}
.check-eye{color: #06C4EA;font-size: 26px;}
.check-lis{color: #E9AE39;font-size: 26px;}
.check-hematin{color: #FA5467;font-size: 26px;}
.check-car{color: #5D87F0;font-size: 26px;}
.bodycheck_stxc{color:#BBB;}
.phy-num p{color: #BBB;}
.phy-num i{font-weight: bold;font-size: 36px;vertical-align: text-top;}
.phy-name i{position: absolute;/*top: -8px;*/left: -45px;}
.phy-cell{background: #08172A;border-radius: 7px;padding: 2px 0;margin: 6px 15px;line-height: 26px;}
.phy-num{display: inline-block;font-size: 14px;color: #52ffff;vertical-align: middle;width: 22%;height:100%;text-align: center;}

/**
营养素设置
 */
.data-num-set{width: 250px;margin: 0 auto;}
.data-num-set li{height: 36px;line-height: 36px;margin-top: 8px;}
.data-num-set li .num-set-left{width: 120px;display: inline-block;}
.data-num-set li .num-set-left img{vertical-align: top;margin-top: 6px;margin-right: 8px;}
.data-num-set li .num-set-right .layui-unselect{vertical-align: top;margin-top: 7px;margin-right: 10px;}
.data-num-set li .num-set-right .layui-form-onswitch{border-color: #1CA89D;background-color: #1CA89D;}
.layui-tab-brief .layui-tab-title li{ color:#666666;}
/*修改大数据营养素统计*/
.data-num-set2{width: 400px;}
.data-num-set2 .num-set-left{width: inherit !important}
.data-num-set2 .num-set-right input[type="text"]{text-align:center;width: 50px;border: none;border-bottom: 1px solid #E2E2E2;border-radius: 0;}
.data-num-set2 .bala-set-num{width: 50px;display: inline-block;text-align: center;color: #676767;}



.data-zhzs{display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-ms-flex-align: center;
	align-items: center;flex-wrap:wrap;}
.data-zhzs1{display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: center;
	-webkit-align-items: center;
	-ms-flex-align: center;
	align-items: center;flex-wrap:wrap; justify-content: center;}
.header-menu .layui-main .side-menu-switch{height: 50px;}

/*幼儿大数据总览*/
.bigdata-all .bigdata-con{top: 0;position: absolute;bottom: 0;left: 0;right: 0;background: none;width: auto;}
.bigdata-all .bigdata-cen{font-size: 0;position: absolute;left: 10px;right: 0; height: 100%}
.bigdata-all .bigdata-sub{background: url(../images/bigdata/data_topbg.png)no-repeat 100% 100%;background-size: 100% 69px;height:69px;position: absolute;width: 100%;z-index: 99;}
.bigdata-all .bigdata-title{text-align: center;font-weight: bold;height: inherit;line-height: inherit;line-height: 69px;display: inline-block;font-size: 30px;color: #ffffff;}
.bigdata-all .bigdata-topright{top: 13px;text-align: right;}
.bigdata-all .bigdata-topleft{font-size: 17px;color: #8ee6ff;position: absolute;left: 22px;top: 11px;text-align: left;}
.bigdata-all .bigdata-top{width: 100%;height: 55px;text-align: center;background: none;}
.bigdata-all .bigdata-leftright{font-size: 16px;color: #52ffff;position: absolute;top: 35px;left: 20px;}
.bigdata-all .bigdata-topright{display: inline-block;font-size: 16px;color: #52ffff;position: absolute;top: 25px;right: 20px;}
.bigdata-all .bigdata-time{position: absolute; left:2%;top:10px; color: #14b3bc;}
.bigdata-all .bigdata-time label{display: block;line-height: 28px; font-size: 14px;}
.bigdata-all .bigdata-con .bigdata-cell{display: inline-block;font-size: 14px;vertical-align: top; }
.bigdata-all .bigdata-list{ height: 100%;position: relative; margin-right: 10px;text-align: center;}
.bigdata-all .mark-trian{width:28px;height:28px;position: absolute;}
.bigdata-all .bigdata-cell{height: 100%;position: relative;}
.bigdata-all .bigdata-list1{ height: 100%;background: url(../images/bigdata/listbg01.png)no-repeat; background-size: 100% 100%; position: relative; margin-right: 10px;font-size: 14px;}
.bigdata-all .bigdata-list2{ height: 100%;background: url(../images/bigdata/listbg02.png)no-repeat; background-size: 100% 100%; position: relative; margin-right: 10px;font-size: 14px;}
.bigdata-all .bigdata-list3{ height: 100%;background: url(../images/bigdata/listbg03.png)no-repeat; background-size: 100% 100%; position: relative; margin-right: 10px;font-size: 14px;}
.bigdata-all .bigdata-list4{ height: 100%;background: url(../images/bigdata/listbg04.png)no-repeat; background-size: 100% 100%; position: relative; margin-right: 10px;font-size: 14px;}
.bigdata-all .bigdata-list5{ height: 100%;background: url(../images/bigdata/listbg05.png)no-repeat; background-size: 100% 100%; position: relative; margin-right: 10px;font-size: 14px;}
.bigdata-all .bigdata-list6{ height: 100%;background: url(../images/bigdata/listbg06.png)no-repeat; background-size: 100% 100%; position: relative; margin-right: 10px;font-size: 14px;}
.bigdata-all .bigdata-list7{ height: 100%;background: url(../images/bigdata/listbg07.png)no-repeat; background-size: 100% 100%; position: relative; margin-right: 10px;font-size: 14px;}
.bigdata-all .bigdata-list8{ height: 100%;background: url(../images/bigdata/listbg08.png)no-repeat; background-size: 100% 100%; position: relative; margin-right: 10px;font-size: 14px;}
.bigdata-all .bigdata-list9{ height: 100%;background: url(../images/bigdata/listbg09.png)no-repeat; background-size: 100% 100%; position: relative; margin-right: 10px;font-size: 14px;}
.table-list{font-size: 13px;text-align: center;}
.table-list tr{height: 38px;}
.table-list tr:nth-child(2n+1){background: rgba(13,151,189,0.2);}
.table-list tr:hover{background: #0d97bd;}
.table-list tr:first-child{background: rgba(13,151,189,0.3);color: #14b3bc;text-align: left;}
.school-sub{color: #ffffff;padding: 10px;width: 26%;height: 60%;position: absolute;top: 190px;right: 20px;background: url(../images/bigdata/bg_school.png);background-size: 100% 100%;}
.num-title{text-align: center;font-size: 16px;padding: 10px 0;border-bottom: 1px solid rgba(255,255,255,0.3);}
.area-text{font-size: 20px;margin: 10px 0;border: 1px solid #2b8291;font-size: 16px;padding: 10px;box-shadow: 0 0 15px 7px inset #2b627d;background: #2d3c50;margin: 10px 30px 10px 0;}
/*标题*/
.title-sign{
	color: #ffffff;
	padding: 5px 10px;
	position: relative;
	margin: 4px 0 0px 12px;
	display: inline-block;
}
.title-sign:before{content: "";width: 3px;height: 14px;background: #14b3bc;position: absolute;left: -1px;top: 8px;}
.progressball-list{display: inline-block;vertical-align: top;color: #ffffff;margin: 0 5px;}
.progressball-sub{background: rgba(20,44,73,0.8);border: 1px solid #153757;font-size: 12px;}
/*水球*/
.wave-sub,.wave-con{
	width: 80px;
	height: 80px;
	border-radius: 50%;
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%,-50%);
}
.wave-sub{
	box-shadow: 0 0 10px 5px inset rgba(245,119,122,1);
}
.wave-sub.wave-red{
	box-shadow: 0 0 10px 5px inset rgba(245,119,122,1);
}
.wave-sub.wave-blue{
	box-shadow: 0 0 10px 5px inset rgba(72,209,240,1);
}
.wave-sub.wave-yellow{
	box-shadow: 0 0 10px 5px inset rgba(241,185,80,1);
}
.wave-sub.wave-green{
	box-shadow: 0 0 10px 5px inset rgba(85,250,177,1);
}
.wave-con{
	background: rgba(255,75,84,0.5);
	overflow: hidden;
}
.wave-sub.wave-red .wave-con{background: rgba(255,75,84,0.5);}
.wave-sub.wave-blue .wave-con{background: rgba(72,209,240,0.5);}
.wave-sub.wave-yellow .wave-con{background: rgba(241,185,80,0.5);}
.wave-sub.wave-green .wave-con{background: rgba(85,250,177,0.5);}
.wave-con:after{
	content: "";
	width: 120px;
	height: 120px;
	background: rgba(1, 9, 48, 0.7);
	position: absolute;
	left: 50%;
	top: 0;
	transform: translate(-50%,-60%);
	border-radius: 40%;
	animation: wave-con 5s linear infinite;
}
@keyframes wave-con{
	100%{
		transform: translate(-50%,-60%) rotate(360deg);
	}
}
.wave-txt{position: absolute;z-index: 9;font-size: 12px;text-align: center;width: 100%;top: 17px;color: #ffffff;}
.type-img{
	width: 50%;
	margin: 5px -20px -30px -20px;
	position:relative;
	animation:moveanimation 2s infinite;
	-webkit-animation:moveanimation 2s infinite; /* Safari and Chrome */
}
.arrow-img{
	position:relative;
	animation:moveanimation2 1s infinite;
	-webkit-animation:moveanimation2 1s infinite; /* Safari and Chrome */
}
/*上下移动动画2*/
@keyframes moveanimation2 {
	0%{
		top: -2px;
	}
	50%{
		top: 3px;
	}
	100%{
		top: -2px;
	}
}
@-webkit-keyframes moveanimation2 {
	0%{
		top: -2px;
	}
	50%{
		top: 3px;
	}
	100%{
		top: -2px;
	}
}
.type-ul li{font-size: 0;text-align: center;margin: 2% 0;color: #d0deee;}
.type-ul li>div{display: inline-block;font-size: 12px;}
.progressbar_1{
    background-color:#e2e2e2;
    height: 10px;
    width:200px;
    color:#222222;
    border-radius:10px;
    width: 70%;
    display: inline-block;
    vertical-align: top;
    margin-top: 13px;
}
.progressbar_1 .bar {
    background-color:#f5906b;
    height:10px;
    border-radius:10px;
    position: relative;
}
.progressbar_txt{
    width: 30%;
    display: inline-block;
    vertical-align: middle;
    text-align: center;
}
.progressbar_1 .val-num{
    position: absolute;
    right: 4px;
    font-size: 12px;
    color:#ffffff;
    height: 10px;
    min-width: 20px;
}
.progressbar_1{margin-top: 2px;border-radius: 0;width: 100%;background-color: #6c8097;}
.progressbar_1 .bar{border-radius: 0;background-color: #0cdbbe;}
.progressbar_1.progressbar-red .bar{background-color: #e95e73;}
.progressbar_1.progressbar-orange .bar{background-color: #e59665;}
.label-text{padding: 1% 8%;position: absolute;border-radius: 40px;color: #ffffff;}
.grad-red{
	background: -webkit-linear-gradient(left, rgba(254,88,109,0.3),rgba(254,88,109,1)); /* Safari 5.1-6.0 */
	background: -o-linear-gradient(left, rgba(254,88,109,0.3),rgba(254,88,109,1));  /* Opera 11.1-12.0 */
	background: -moz-linear-gradient(left, rgba(254,88,109,0.3),rgba(254,88,109,1)); /* Firefox 3.6-15 */
	background: linear-gradient(left, rgba(254,88,109,0.3),rgba(254,88,109,1)); /* æ ‡å‡†è¯­æ³•*/
}
.grad-green{
	background: -webkit-linear-gradient(left, rgba(1,220,205,0.3),rgba(1,220,205,1)); /* Safari 5.1-6.0 */
	background: -o-linear-gradient(left, rgba(1,220,205,0.3),rgba(1,220,205,1));  /* Opera 11.1-12.0 */
	background: -moz-linear-gradient(left, rgba(1,220,205,0.3),rgba(1,220,205,1)); /* Firefox 3.6-15 */
	background: linear-gradient(left, rgba(1,220,205,0.3),rgba(1,220,205,1)); /* æ ‡å‡†è¯­æ³•*/
}
.grad-blue{
	background: -webkit-linear-gradient(left, rgba(16,115,197,0.3),rgba(16,115,197,1)); /* Safari 5.1-6.0 */
	background: -o-linear-gradient(left, rgba(16,115,197,0.3),rgba(16,115,197,1));  /* Opera 11.1-12.0 */
	background: -moz-linear-gradient(left, rgba(16,115,197,0.3),rgba(16,115,197,1)); /* Firefox 3.6-15 */
	background: linear-gradient(left, rgba(16,115,197,0.3),rgba(16,115,197,1)); /* æ ‡å‡†è¯­æ³•*/
}
.grad-purple{
	background: -webkit-linear-gradient(left, rgba(143,18,252,0.3),rgba(143,18,252,0.5)); /* Safari 5.1-6.0 */
	background: -o-linear-gradient(left, rgba(143,18,252,0.3),rgba(143,18,252,0.5));  /* Opera 11.1-12.0 */
	background: -moz-linear-gradient(left, rgba(143,18,252,0.3),rgba(143,18,252,0.5)); /* Firefox 3.6-15 */
	background: linear-gradient(left, rgba(143,18,252,0.3),rgba(143,18,252,0.5)); /* æ ‡å‡†è¯­æ³•*/
}
.grad-orange{
	background: -webkit-linear-gradient(left, rgba(252,155,91,0.3),rgba(252,155,91,0.5)); /* Safari 5.1-6.0 */
	background: -o-linear-gradient(left, rgba(252,155,91,0.3),rgba(252,155,91,0.5));  /* Opera 11.1-12.0 */
	background: -moz-linear-gradient(left, rgba(252,155,91,0.3),rgba(252,155,91,0.5)); /* Firefox 3.6-15 */
	background: linear-gradient(left, rgba(252,155,91,0.3),rgba(252,155,91,0.5)); /* æ ‡å‡†è¯­æ³•*/
}
.grad-yellow{
	background: -webkit-linear-gradient(left, rgba(176,145,18,0.3),rgba(176,145,18,0.5)); /* Safari 5.1-6.0 */
	background: -o-linear-gradient(left, rgba(176,145,18,0.3),rgba(176,145,18,0.5));  /* Opera 11.1-12.0 */
	background: -moz-linear-gradient(left, rgba(176,145,18,0.3),rgba(176,145,18,0.5)); /* Firefox 3.6-15 */
	background: linear-gradient(left, rgba(176,145,18,0.3),rgba(176,145,18,0.5)); /* æ ‡å‡†è¯­æ³•*/
}
.rule-mark{color: #17edff;position: absolute;font-size: 18px;}
.rule-mark:before{content: "";width: 55%;height: 2px;background: #17edff;position: absolute;left: -64%;top: 42%;}


/*切换*/
.tab-common6{color: #ffffff;font-size: 13px;height: 28px;line-height: 28px;margin: 5px 0 15px 2px;}
.tab-common6 li{height: 100%;position: relative;display: inline-block;margin-right: 20px;font-size: 16px;cursor: pointer;}
.tab-common6 li.current{color: #14b3bc;}
.tab-common6 li.current:after{content:"";height: 2px;width: 34px;position: absolute;bottom: 0;left: 50%;margin-left: -17px;background: #14b3bc;border-radius: 3px;}	
/*渐变*/
.gradient-red{
	background: -webkit-linear-gradient(#ff9c82, #ff6268); /* Safari 5.1 - 6.0 */
 	background: -o-linear-gradient(#ff9c82, #ff6268); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(#ff9c82, #ff6268); /* Firefox 3.6 - 15 */
    background: linear-gradient(#ff9c82, #ff6268); /* 标准的语法 */
}
.gradient-green{
	background: -webkit-linear-gradient(#33ea86, #04cc5e); /* Safari 5.1 - 6.0 */
 	background: -o-linear-gradient(#33ea86, #04cc5e); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(#33ea86, #04cc5e); /* Firefox 3.6 - 15 */
    background: linear-gradient(#33ea86, #04cc5e); /* 标准的语法 */
}
.gradient-blue{
	background: -webkit-linear-gradient(#52c4ff, #119ff4); /* Safari 5.1 - 6.0 */
 	background: -o-linear-gradient(#52c4ff, #119ff4); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(#52c4ff, #119ff4); /* Firefox 3.6 - 15 */
    background: linear-gradient(#52c4ff, #119ff4); /* 标准的语法 */
}
.gradient-orange{
	background: -webkit-linear-gradient(#ffc852, #f2a001); /* Safari 5.1 - 6.0 */
 	background: -o-linear-gradient(#ffc852, #f2a001); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(#ffc852, #f2a001); /* Firefox 3.6 - 15 */
    background: linear-gradient(#ffc852, #f2a001); /* 标准的语法 */
}
.gradient-purple{
	background: -webkit-linear-gradient(#896eff, #4c25f6); /* Safari 5.1 - 6.0 */
 	background: -o-linear-gradient(#896eff, #4c25f6); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(#896eff, #4c25f6); /* Firefox 3.6 - 15 */
    background: linear-gradient(#896eff, #4c25f6); /* 标准的语法 */
}
.gradient-pink{
	background: -webkit-linear-gradient(#ff7895, #f84269); /* Safari 5.1 - 6.0 */
 	background: -o-linear-gradient(#ff7895, #f84269); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(#ff7895, #f84269); /* Firefox 3.6 - 15 */
    background: linear-gradient(#ff7895, #f84269); /* 标准的语法 */
}