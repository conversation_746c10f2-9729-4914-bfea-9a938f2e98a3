<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加号源</title>
    <link rel="stylesheet" href="../../css/reset.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <script type="text/javascript" src="../../layui-btkj/layui.js"></script>

    <style>
        .view p {
            color: #34495E;
            font-size: 14px;
        }

        .layui-form-label {
            text-align: left;
            width: 105px;
            color: #666666;
        }

        .datemain input, .datemain textarea {
            border: 1px solid #CBD6E1;
            color: #34495E;
        }

        .pre-css {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 90px;
            height: 60px;
            margin-right: 10px;
            background-color: #F5F5F5;
            border-radius: 5px;
            flex: 0 0 auto;
            margin-bottom: 10px;
        }


    </style>
</head>

<body>
<section>
    <div style="margin: 20px 30px;">
        <div class="form-list layui-form" id="form" lay-filter="formOk">
            <div class="layui-form-item " style="margin: 0;">
                <label class="layui-form-label" style="float: left;"><em>*</em>号源类型：</label>
                <div class="layui-input-block" style="min-height: 30px;float: left;width: 80%; margin-left:0px;">
                    <select type="text" id="hc_select" lay-filter="hc_select" autocomplete="off"
                            style="width: 250px" lay-verify="required" class="layui-select">
                    </select>
                </div>
            </div>

            <div class="layui-form-item " style="margin: 10px 0 0 0;">
                <label class="layui-form-label" style="float: left;"><em>*</em>医事服务费：</label>
                <div class="layui-input-block"
                     style="float: left;width: 80%;margin-left:0px; border: #cbd6e1 solid 1px; display: flex; flex-direction: row;">
                    <input type="text" name="service_charge" id="service_charge" placeholder="请输入" class="layui-input"
                           lay-verify="required|number" maxlength="10" style="border: 0px">
                    <label style="padding: 0px 10px; line-height: 37px;">元</label>
                </div>
            </div>

            <div class="layui-form-item haoyuan-show" style="margin: 10px 0 0 0;">
                <label class="layui-form-label" style="float: left;"><em>*</em>姓名：</label>
                <div class="layui-input-block" style="float: left;width: 80%;margin-left:0px;">
                    <input type="text" name="name" id="name" placeholder="请输入" class="layui-input" maxlength="20"
                           lay-verify="required">
                </div>
            </div>

            <div class="layui-form-item datemain haoyuan-show" style="margin: 10px 0 0 0;">
                <label class="layui-form-label" style="float: left;">介绍：</label>
                <div class="layui-input-block" style="min-height: 30px;margin-left:0px;float: left;width: 80%;">
                <textarea id="introduce" name="introduce" rows="3" cols="20" style="width: 100%;height: 150px"
                          maxlength="500"></textarea>
                </div>
            </div>

            <div class="layui-form-item" style="margin: 10px 0 0 0;">
                <label class="layui-form-label" style="float: left;">手机号：</label>
                <div class="layui-input-block" style="float: left;width: 80%;margin-left:0px;">
                    <input type="text" name="phoneNumber" id="phoneNumber" lay-verify="phoneNotNull"
                           placeholder="请输入手机号"
                           maxlength="20"
                           class="layui-input">
                </div>
            </div>

            <div class="layui-form-item " style="margin: 10px 0 0 0;">
                <label class="layui-form-label" style="float: left;">工作时间：</label>
                <div class="layui-input-block"
                     style="float: left;width: 80%;margin-left:0px;display: flex;height: 30px;line-height: 19px;">
                    <label style="line-height: 36px;display: inline-block;margin-right: 5px;">上午</label>
                    <span>
					<input id="morning_start_time" type="text" autocomplete="off" class="layui-input"
                           style="width: 60px; border: 0px;" lay-ignore readonly>
					</span> <span class="linebg">-</span> <span>
					<input id="morning_end_time" type="text" autocomplete="off" class="layui-input"
                           style="width: 60px; border: 0px;" lay-ignore readonly>
					</span>
                    <label style="line-height: 36px;display: inline-block; margin-left: 30px;margin-right: 5px;">下午</label>
                    <span>
					<input id="afternoon_start_time" type="text" autocomplete="off"
                           class="layui-input"
                           style="width: 60px; border: 0px;" lay-ignore readonly>
					</span> <span class="linebg">-</span> <span>
					<input id="afternoon_end_time" type="text" autocomplete="off" class="layui-input"
                           style="width: 60px; border: 0px;" lay-ignore readonly>
					</span>
                </div>
            </div>

            <div class="layui-form-item " style="margin: 10px 0 0 0;">
                <label class="layui-form-label" style="float: left;"><em>*</em>模式设置：</label>
                <div class="layui-input-block" style="float: left;width: 80%;margin-left:0px;">
                    <div>
                        <input name="model" type="radio" value="0" title="数量模式" lay-filter="demo-radio-filter"
                               checked="checked" style="margin-right: 5px;">
                        <input name="model" type="radio" value="1" title="编号模式" lay-filter="demo-radio-filter"
                               style="margin-right: 5px;margin-left: 30px;">
                    </div>
                    <div id="show-model">
                        <div id="model_num1_box">
                            <div>
                                <span style="margin-left: 50px">上午工作时长：<span class="mor_min"></span>(分钟)</span>
                                <span style="margin-left: 100px">下午工作时长：<span class="aft_min"></span>(分钟)</span>
                            </div>
                            <div style="display: flex;margin-top: 10px;">
                                <label style="line-height: 36px;display: inline-block;margin-right: 5px;"><em>*</em>上午号源数量：</label>
                                <span>
                                    <input id="morning_num_1" name="morning_quantity" type="number" autocomplete="off" placeholder="请输入数字"
                                            lay-verify="numrequired|num" class="layui-input" style="width: 130px;" maxlength="32" min="0">
                                </span>
                                <label style="line-height: 36px;display: inline-block; margin-left: 30px;margin-right: 5px;">上午号源分</label>
                                <span>
                                    <input id="morning_para" name="morning_split" type="number" autocomplete="off" placeholder="请输入数字"
                                           lay-verify="numrequired|num|numAmVerify" class="layui-input" style="width: 130px;" maxlength="32" min="0">
                                </span>
                                <label style="line-height: 36px;display: inline-block; margin-left: 5px;">段</label>
                                <label style="line-height: 36px;display: inline-block; margin-left: 30px;margin-right: 5px;">每</label>
                                <span>
                                    <input id="morning_minperpara" type="number" autocomplete="off" placeholder="请输入数字" class="layui-input"
                                           lay-verify="numrequired|num|numAmIsInt" style="width: 130px;" maxlength="32" min="0">
                                </span>
                                <label style="line-height: 36px;display: inline-block; margin-left: 5px;">分钟1段</label>
                            </div>
                            <div style="display: flex; margin-top: 10px;">
                                <label style="line-height: 36px;display: inline-block;margin-right: 5px;"><em>*</em>下午号源数量：</label>
                                <span>
                                    <input id="afternoon_num_1" name="afternoon_quantity" type="number" autocomplete="off" placeholder="请输入数字"
                                           class="layui-input" lay-verify="numrequired|num" style="width: 130px;" maxlength="32" min="0">
                                </span>
                                <label style="line-height: 36px;display: inline-block; margin-left: 30px;margin-right: 5px;">下午号源分</label>
                                <span>
                                    <input id="afternoon_para" name="afternoon_split" type="number" autocomplete="off" placeholder="请输入数字"
                                           class="layui-input" lay-verify="numrequired|num|numPmVerify" style="width: 130px;" maxlength="32" min="0">
                                </span>
                                <label style="line-height: 36px;display: inline-block; margin-left: 5px;">段</label>
                                <label style="line-height: 36px;display: inline-block; margin-left: 30px;margin-right: 5px;">每</label>
                                <span>
                                    <input id="afternoon_minperpara" type="number" autocomplete="off" placeholder="请输入数字" class="layui-input"
                                           lay-verify="numrequired|num|numPmIsInt" style="width: 130px;" maxlength="32" min="0">
                                </span>
                                <label style="line-height: 36px;display: inline-block; margin-left: 5px;">分钟1段</label>
                            </div>
                        </div>


                        <div id="model_num2_box" style="display: none">
                            <div style="display: flex;margin-top: 10px;">
                                <label style="line-height: 36px;display: inline-block;margin-right: 5px;"><em>*</em>上午工作时间为<span class="mor_min"></span>分钟，每</label>
                                <span>
                                    <input id="morning_idpermin" type="number" autocomplete="off" placeholder="请输入数字" class="layui-input"
                                           lay-verify="coderequired|num|codeAmtotal" style="width: 130px;" min="0">
                                </span>
                                <label style="line-height: 36px;display: inline-block; margin-left:5px;">分钟一个编号</label>
                                <label style="line-height: 36px;display: inline-block; margin-left: 30px;margin-right: 5px;">号源数量：</label>
                                <span>
                                    <input id="morning_num_2" name="morning_quantity" type="number" autocomplete="off" placeholder="请输入数字"
                                           class="layui-input" lay-verify="coderequired|num" style="width: 130px;" min="0" disabled>
                                </span>
                            </div>
                            <div style="display: flex;margin-top: 10px;">
                                <label style="line-height: 36px;display: inline-block;margin-right: 5px;"><em>*</em>下午工作时间为<span class="aft_min"></span>分钟，每</label>
                                <span>
                                    <input id="afternoon_idpermin" type="number" autocomplete="off" placeholder="请输入数字" class="layui-input"
                                           lay-verify="coderequired|numcodePmtotal" style="width: 130px;" min="0">
                                </span>
                                <label style="line-height: 36px;display: inline-block; margin-left:5px;">分钟一个编号</label>
                                <label style="line-height: 36px;display: inline-block; margin-left: 30px;margin-right: 5px;">号源数量：</label>
                                <span>
                                    <input id="afternoon_num_2" name="afternoon_quantity" type="number" autocomplete="off" placeholder="请输入数字"
                                           class="layui-input" lay-verify="coderequired|num" style="width: 130px;" min="0" disabled>
                                </span>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            <div class="layui-form-item " style="margin: 10px 0 0 0;">
                <label class="layui-form-label" style="float: left;">预约样式：</label>
                <div class="layui-input-block" style="float: left;width: 80%;margin-left:0px;">
                    <div class="layui-col-md6">
                        <div class="bluelt" style="line-height: 36px;">上午预约时间段</div>
                        <ul class="reservation-list" id="mor-per-show-num"></ul>
                        <ul class="reservation-list" id="mor-per-show-code" style="display: none"></ul>
                    </div>
                    <div class="layui-col-md6">
                        <div class="bluelt" style="line-height: 36px; margin-left: 10px;">下午预约时间段</div>
                        <ul class="reservation-list" style="margin-left: 10px;" id="aft-per-show-num"></ul>
                        <ul class="reservation-list" style="margin-left: 10px;" id="aft-per-show-code" style="display: none"></ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div style="padding: 10px 0;text-align: center;background: #F8F8F8;position: fixed;bottom: 0;width: 100%; display: none">
        <div style="margin-right: 15px;">
            <a class="layui-btn gray-bg" id="btnClose" type="button">取消</a>
            <a class="layui-btn" id="btnSave">确认</a>
        </div>
    </div>
</section>
</body>

<script type="text/javascript" data-main="../../js/depart/departform" src="../../sys/require.min.js"></script>
</html>