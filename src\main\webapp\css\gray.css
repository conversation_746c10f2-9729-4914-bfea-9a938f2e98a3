/*灰色皮肤*/
/*color*/
.bg1{background: #717171;}
.bg3{background: #22282b;}
.bg4{background: #e9e9e9;}/*浅灰*/
.bg5{background: #d1d1d1;}/*深灰*/
.cl1{color: #717171;}
.cl2{color: #fdaa2e;}

/*icon*/
u[class^=skinIcon],.layim_chateme .layim_chatsay .layim_zero,skinIcondenglu u,skinIconbq u{background-image:url(../images/icon-gray.png);}

/*btn*/
.btn-big,.personalImg .webuploader-pick,.echarts-dataview button{background: #fdaa2e;}
.btn-big:hover{background: #dc7700;}
.btn-black,.monthHead{background: #717171;}
.btn-black:hover{background: #fc9b28;}
.btn-border{border:1px solid #717171;}
.btn-border:hover{background: #fc9b28;color:#fff;}
.layui-layer-btn a.layui-layer-btn0{background:#fdaa2e;border-color:#fc9b28;}

/*tab*/
.tab li:hover, .tab li.current{color: #fff; background:#717171!important;border: 1px solid #717171;}
.tab li.pulldown.current{color: #fff; background:#717171 url(../images/icon_pulldown_white.png) no-repeat 75px center!important;background-size: 12px !important;border: 1px solid #717171;}
.tab li.pulldown:hover{color: #fff; background:#717171 url(../images/icon_pulldown_white.png) no-repeat 75px center!important; background-size: 12px !important;border: 1px solid #717171;}

/*page*/
.dataTables_wrapper .dataTables_paginate .paginate_button.current, .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover,.dataTables_wrapper .dataTables_paginate .paginate_button:hover{
	background: -webkit-linear-gradient(#585858 0%, #111 100%);background: linear-gradient(#585858 0%, #111111 100%);color: #fff !important;border-color:#111111;filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#585858', endColorstr='#111111', GradientType=0);}

/*聊天窗口*/
.layim_chatmore{border-right:1px solid #000; background-color:#717171;}
.layim_chatlist li:hover,.layim_chatlist .layim_chatnow,.layim_chatlist .layim_chatnow:hover{background-color:#a3a4a4;}
.layim_chatlist li:hover span,.layim_chatlist .layim_chatnow span{color:#000;}
.layim_sendbtn{background-color:#717171;}
.layim_enter{border-left-color:#999;}
.layim_sendbtn:hover,.layim_enter:hover{background-color:#757575;}
.layim_sendtype span:hover,.layim_sendtype span:hover i{background-color:#717171;color:#fff;}
.layim_send,.layim_tool,.layim_chatbox h6{background: #e2e2e2;}
.layim_tool i{border:1px solid #e2e2e2;}
.layim_chateme .layim_chatsay{background: #9a9a9a;}
.btnTop a:hover,.btn-control:hover,.operateBtn a:hover{background:#f29527;border-color:#da7600;}
.popTitle,.btnTop a,.fc-day-header{background: #717171;}

/*左侧面板*/
.usualFun a:hover,.inner a:hover,footer a.current,footer a:hover,.curList .current,.curList a:hover,.letterList li:hover,.letterList li.current,.btn-head:hover,.noticeTitle a:hover,.articleGray a:hover,.loadMore:hover,.innerHistory li:hover,.today_date,.alm_content p,.list .current,.ztree li a:hover,.foldList li:hover,.foldList li p,.dataTables_wrapper a,.otx-table a,.foldList li{color: #fdaa2e;}/*橘色*/
/*.usualFun a:hover img,.inner a:hover img,.foldList li:hover img{background: url(../images/hover.png) no-repeat center center;}
*/.list li{border-bottom: 1px solid #747474;}
.leftTitle{border-bottom:1px solid #717171;}
.letterIndex{background: #9d9d9e;}
.btn-lang:hover,.current.btn-lang{border-color:#fdaa2e;color:#fdaa2e;}
.list .face{border-color:#ccc;}
.foldList li,.funList li{color: #717171;}

.btn-far:hover,.btn-near:hover{background:url(../images/distance-gray.png);}
.fast:hover,.normal:hover,.slow:hover,.arrow-up:hover,.arrow-down:hover{background: url(../images/hover.png);}
.numberList span:hover{background: url(../images/gray-big.png);}

/*日历*/
.fc-state-down,.fc-state-active,.fc-button:hover{border-color:#fc9b28;}
.fc-planmeeting-button{border:0;border-radius: 0;}
.fc-year-button:hover,.fc-month-button:hover,.fc-agendaWeek-button:hover,.fc-agendaDay-button:hover,.fc-state-active{background:#fc9b28;color:#fff;}

/*会控*/
.meetingIndex,.fc-planmeeting-button{color:#fff; background: #fdaa2e;}
.rankList span.current{background: #fe9b02;}

/*栏目管理*/
.meauList span{border-top: 2px solid #717171; border-bottom: 2px solid #717171;}
.meauList .inner{background: #717171;}
.meauList .inner a.selected{background: #6f6f6f;}

/*单位管理*/
.jOrgChart .node,.formList .webuploader-pick{background:#fc9b28;}

/*tree*/
.ztree li a.curSelectedNode{background-color: #fde5c5;border: 1px solid #b9b9b9;color: #fc9b28;}

/*会议控制*/
.meetMain{border-top: 4px solid #717171;}
.submeetList{border-top: 4px solid #a8a9a9;}

/*下拉多选*/
.ui-widget-header{background: #ff9c03; border-color:#ff9c03;}
.ui-state-hover, .ui-widget-content .ui-state-hover, .ui-widget-header .ui-state-hover, .ui-state-focus, .ui-widget-content .ui-state-focus, .ui-widget-header .ui-state-focus{color: #ff9c03;}
.ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active{background: none; border-color:#ff9c03;color: #ff9c03;}
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default{ border-color:#ff9c03;color: #ff9c03;}


/*新增加样式*/

.usualFun{ background:#FFFFFF; padding-left:3px; padding-bottom:3px}
.usualFun a{float: left; width: 32%; +width: 32%; margin-top:3px;position: relative;text-align: center;font-size: 12px;line-height: 28px;color:#ffffff; background:#717171; margin-right:3px; font-size:14px;}
.usualFun img{width: 35px;height: 35px; display: block; margin:0 auto;/*border-radius: 50%;*/ /*background: url(../images/gray.png) no-repeat center center;*/ padding-top:10px}
.usualFun u{position: absolute;display: none;}

/*.inner a:hover{width: 32%; +width: 32%; margin-top:3px;position: relative;text-align: center;font-size: 12px;line-height: 28px;color:#ffffff; background:#53C2BC; margin-right:3px; font-size:14px; display:block}
*/


.inner a:hover{ color:#FFF; background:#717171; display:inline-block}

/*幼儿园名称下拉背景色*/
.selectColor{background-color:#717171 }

/*智慧数字幼儿园*/
/*桌面头部菜单*/
.ultopnums li.current,.ultopnums li:hover,.topultool li:hover,.topultool li.current {
	color: #ffffff;
	background: -webkit-linear-gradient(#808080, #606060); /* Safari 5.1 - 6.0 */
	background: -o-linear-gradient(#808080, #606060); /* Opera 11.1 - 12.0 */
	background: -moz-linear-gradient(#808080, #606060); /* Firefox 3.6 - 15 */
	background: linear-gradient(#808080, #606060); /* 标准的语法 */
}
/*菜单切换*/
.menutab ul{color: #34495E!important;}
.menutab ul li{color: #F3F4F4;border-bottom:3px solid #D5D6D6; 
	background: #F3F4F4;		
	border-image: -webkit-linear-gradient(#F2F3F4,#D5D6D6) 40 20;
    border-image: -moz-linear-gradient(#F2F3F4,#D5D6D6) 40 20;		
	border-image: -o-linear-gradient(#F2F3F4,#D5D6D6) 40 20;
	border-image: linear-gradient(#F2F3F4,#D5D6D6) 40 20;}
.menutab ul li.current{color: #34495E!important; background: #fff;}
#divdesktop .scrollArea{background: none;}
#divmenulist .funList li:hover{background: #F2F2F2;color: #666!important;}
#divdeskleft:after{content: "";width: 20px;position: absolute;top: 0px;bottom: 0;right: -20px;box-shadow: 5px 0px 5px -4px inset #DCE0E4;
  
}
.funtop .desktop-logo .tabout-div{box-shadow: 0px 0px 5px 2px #757474;}
.funtop .desktop-logo .tab-cell{box-shadow: 0px -8px 4px -5px inset #b0b0b0;}

/*保教*/
.term-tab.current{background: #686f74;color: #ffffff;}
.term-tab.current .calendar-div{border: 1px solid #363f46;}
.term-list.redbg{background: #686f74;}
.calen-top h5{background: #686f74;}
.calen-txt p{color: #686f74;}
.div-right .layui-table tbody.current{border: 2px solid #686f74;}
.div-right .layui-table tbody.current tr:nth-child(1) td:nth-child(1){background-color: #686f74;}
.div-right .layui-table td.pinkcurrent{background-color: #686f74;}
.workrest-left h5{background: #616a71!important;}
.workrest-right h5{background: #686f74!important;}
.time-work li.time-worksel{ background:#686f74;}
.week-tab.current{background: #686f74;}
.icon-list .layui-btn{background: #52575b!important;}
.protect-educate .plan-txt.current{background: #686f74;color: #ffffff;border: 1px solid #686f74;}
.play-sub{border: 2px solid #686f74!important;}
.def-icon .icon_calen:before{color: #686f74!important;}
.opreate-icon .icon-txt .iconfont{color: #686f74!important;}
.protect-educate .plan-left-icon>div .iconfont:before{color: #686f74;}
.protect-educate .plan-left-icon>div .icon_ratio:before{border: 2px solid #686f74;}
.def-icon .icon_tableft:before,.def-icon .icon_tabright:before{color: #686f74;}
#a_unselect{color: #686f74!important;}
.upload-btn .opr_select{background: #686f74!important;}
.opr-btn.pay-save{background: #686f74!important;}
#pgriddivshipuarea.def-layui-table tr.current td:not(.tr-firsttd){background: #e7e9ea;}
#pgridplantable.def-layui-table tr.current td:not(.tr-firsttd){background: #e7e9ea;}
/*作息弹框按钮颜色*/
.opr-btn{background: #717171;}
.cho-main .layui-btn{background: #717171;}
.protect-educate .plan-left-icon>div.current .iconfont:before{color: #686f74;}
.protect-educate .plan-left-icon>div.current .iconfont:before{color: #686f74;}
.plan-con{border: 4px solid #686f74;}
.plan-leftspan.currentpink, .plan-rightspan.currentpink{background: #686f74;}
.plan-leftspan.currentred, .plan-rightspan.currentred{background: #5b5d5f;}
.left-contain .common-tab3 li.current a{color: #686f74;font-weight: bold;border-bottom: 2px solid #686f74;}
.left-contain .icon_search2:before{color: #686f74;}
.type-plan .iconfont.redicon2:before{color: #686f74;}
#divdt .layui-table tbody.current{border: 2px solid #686f74;}
#divdt .layui-table tbody.current tr:nth-child(1) td:nth-child(1){background-color: #686f74;}
#divdt .layui-table td.pinkcurrent{background-color: #686f74;}
#divdt .tdexist{background-color: #4a5359;}
.timetable-list .circle-label{background: #686f74;}
.teaching-list .teachadd-btn .teachlayui-btn{background: #686f74;}
.upload-list .layui-btn-normal, .teaching-list-bom .upload-right .layui-btn, .cover-list .layui-btn{background: #686f74;}
#add_pic .webuploader-pick{background: #686f74;}
.teaching-list .layui-btn{color: #686f74;}
.upload-btn .opr_save{background: #686f74!important;}
.upload-btn .opr_saveadd{background: #686f74!important;}
.course-opr .layui-tab-brief>.layui-tab-title .layui-this{color: #686f74;}
.course-opr .layui-tab-brief>.layui-tab-more li.layui-this:after,.course-opr .layui-tab-brief>.layui-tab-title .layui-this:after{border-bottom: 4px solid #686f74;}
#add_pic .webuploader-pick{background:#686f74;}
#add_pic .webuploader-pick, #add_video .webuploader-pick{background:#686f74;}
#add_audio .webuploader-pick{background:#686f74;}
#add_files .webuploader-pick{background:#686f74;}
