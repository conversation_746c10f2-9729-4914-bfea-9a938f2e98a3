// 2016.04.11
// 郭玉峰
require.config({
    paths: {
        system: '../sys/system',
        jquery: '../sys/jquery'
    },
    waitSeconds: 0

});
var objdata = {};
require(['jquery', 'system'], function () {
    $(".set-tab").children().click(function () {
        var pindex = $(this).index();
        $(this).removeClass('layui-btn-primary').siblings().addClass('layui-btn-primary');
        $("#divpages").children().eq(pindex).show().siblings().hide()
        var index = parent.layer.getFrameIndex(window.name);
        var isfrist = false;
        if (!$(this).data('hasload')) {
            isfrist = true;
            $(this).data('hasload', true)
        }
        if (pindex == 0) {
            initPage0(isfrist);
        } else if (pindex == 1) {
            initPage1(isfrist);
        } else if (pindex == 2) {
            initPage2(isfrist);
        } else if (pindex == 3) {
            initPage3(isfrist);
        }
        parent.layer.iframeAuto(index);
    }).eq(0).trigger('click')
    function initPage0(isfrist) {
        if (!isfrist) return;
        var curback = parent.objdata.curback;
        var bgs = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"];
        var names = ["背景1", "背景2", "背景3", "背景4", "背景5", "背景6", "背景7", "背景8", "背景9", "背景10", "背景11", "背景12"];
        var html = [];
        for (var i = 0; i < bgs.length; i++) {
            html.push('<li class="onebg"><img src="../images/back/' + bgs[i] + '.jpg" class="bgimg" alt="' + names[i] + '" />' + (curback == bgs[i] ? '<img src="../images/desktop/checkbox_greenbg.png" class="sel-checkbox">' : '') + '</li>');
        }
        if (curback.indexOf("#") > -1) {
            $("#ulbgcolors").children("[color=" + curback + "]").append('<img src="../images/desktop/checkbox_greenbg.png" class="sel-checkbox">')
        }
        $("#ulbgs").html(html.join('')).children('li').click(function () {
            var _this = $(this);
            if (_this.children('.sel-checkbox').length) {
                return;
            }
            _this.append('<img src="../images/desktop/checkbox_greenbg.png" class="sel-checkbox">')
            _this.siblings().each(function () {
                $(this).children('.sel-checkbox').remove();
            });
            var index = _this.index();
            parent.$("body").css("background", "url(images/back/" + bgs[index] + ".jpg) center");
            parent.objdata.curback = bgs[index];
            $.sm(function (re, err) {
                if (err) {
                    parent.layer.msg('保存失败！');
                }
            }, ['w_theme.update', parent.objdata.my.id, $.msgwhere({curbackimg:[parent.objdata.curback]})]);
        });
        $("#ulbgcolors").children().click(function () {
            var _this = $(this);
            if (_this.children('.sel-checkbox').length) {
                return;
            }
            _this.append('<img src="../images/desktop/checkbox_greenbg.png" class="sel-checkbox">')
            _this.siblings().each(function () {
                $(this).children('.sel-checkbox').remove();
            });
            var color = $(this).attr('color');
            var index = _this.index();
            parent.$("body").css("background", color);
            parent.objdata.curback = color;
            $.sm(function (re, err) {
                if (err) {
                    parent.layer.msg('保存失败！');
                }
            }, ['w_theme.update', parent.objdata.my.id, $.msgwhere({curbackimg:[parent.objdata.curback]})]);
        })
    }

    function initPage1(isfrist) {
        if (!isfrist) return;
        var colors = ["gray", "blue", "green", "orange", "purple", "red", "lightGreen"], html = [];
        for (var i = 0; i < colors.length; i++) {
            html.push('<li class="bg_' + colors[i] + '" type="' + colors[i] + '">' + (parent.objdata.curtheme == colors[i] ? '<img src="../images/desktop/checkbox_greenbg.png" class="sel-checkbox">' : '') + '</li>');
        }
        $("#ultheme").html(html.join('')).children('li').click(function () {
            var _this = $(this);
            var type = _this.attr('type');
            if (type == parent.objdata.curtheme) {
                return;
            }
            parent.desktop.onthemechange(type);

            /*//iframe内
            parent.objdata._divframes.children().each(function() {
                var iframe = $(this).find('iframe');
                if (iframe.length > 0) {
                    var _link = iframe[0].contentWindow.$("#linktheme");
                    if (_link.length)
                        _link.attr('href', '../css/' + parent.objdata.curtheme + ".css");
                }
            });*/
            _this.append('<img src="../images/desktop/checkbox_greenbg.png" class="sel-checkbox">')
            _this.siblings().each(function () {
                $(this).children('.sel-checkbox').remove();
            });
            //parent.$.cookie('curtheme', type);
            $.sm(function (re, err) {
                if (err) {
                    parent.layer.msg('保存失败！');
                }
            }, ['w_theme.update', parent.objdata.my.id,$.msgwhere({curtheme:[parent.objdata.curtheme]})]);
        });
    }

    function initPage2(isfrist) {
        if (!isfrist) return;
        function makeonehtml(objonedata) {
            var str =
                '<li' + (objonedata.menukey ? ' data-menukey="' + objonedata.menukey + '"' : '') + (objonedata.menuid ? ' data-menuid="' + objonedata.menuid + '"' : '') + '>\
					<span class="order-num" style="display: none">' + (i + 1) + '</span>\
					<i class="iconfont icon_close_white"></i>\
					<div class="desktop-cell">\
						<img src="' + (objonedata.image.indexOf('../') > -1 ? '' : '../') + objonedata.image + '">\
					</div>\
					<div class="edit-txt">\
						<input type="text" placeholder="' + objonedata.name + '" value="' + objonedata.name + '">\
					</div>\
				</li>';
            return str;
        }

        var arrdeskdata = parent.objdata.objmy.arrdeskdata;
        var strtoptab = '';
        for (var i = 0; i < arrdeskdata.length; i++) {
            strtoptab += makeonehtml(arrdeskdata[i]);
        }
        $("#uldesktab").html(strtoptab);
        $("#uldesktab").on('click', '.icon_close_white', function () {
            $(this).parent().remove();
        })
        $("#btnadddesk").click(function () {
            parent.layer.open({
                type: 2,
                title: ["选择栏目", 'background-color:' + parent.objtheme[parent.objdata.curtheme] + ';text-align:center;color:#ffffff'],
                content: 'desktop/desktopmenuzu.html?v=' + Arg("v") + "&frompagename=" + window.name,
                area: ['350px', '600px'],//
                btn: ["确定", "取消"],
                yes: function (index, layero) {
                    var win = layero.find('iframe')[0].contentWindow;
                    win.btnok && win.btnok(function (arrkey, arrid) {
                        if (!arrkey.length && !arrid.length) {
                            return parent.layer.msg("至少选择一个哦");
                        }
                        if ($("#uldesktab").children().length + arrkey.length + arrid.length > 5) {
                            return parent.layer.msg("最多有5个桌面哦");
                        }
                        parent.layer.close(index);
                        var strhtml = '';
                        for (var i = 0; i < arrkey.length; i++) {
                            strhtml += makeonehtml(arrkey[i])
                        }
                        for (var i = 0; i < arrid.length; i++) {
                            strhtml += makeonehtml(arrid[i])
                        }
                        $("#uldesktab").append(strhtml);
                    });
                }
            })
        })
        $("#btnsavedesk").click(function () {
            parent.layer.confirm("确定保存桌面设置吗？", function (index) {
                //debugger
                var arrolddeskdata = parent.objdata.objmy.arrdeskdata;
                var arrnewdata = [];
                $("#uldesktab").children().each(function () {
                    var menukey = $(this).data('menukey');
                    if (menukey) {
                        var obj = {
                            menukey: menukey,
                            image: $(this).find('img').attr('src'),
                            name: $(this).find('input').val()
                        };
                        //查看之前是否有数据
                        var has = false;
                        for (var i = 0; i < arrolddeskdata.length; i++) {
                            if (menukey == arrolddeskdata[i].menukey) {
                                obj = arrolddeskdata[i];
                                has = true;
                                break;
                            }
                        }
                        //之前没有 去取默认的
                        if (!has) {
                            var arrdefaultdeskdata = parent.getdefaultdeskdata;
                            for (var i = 0; i < arrdefaultdeskdata.length; i++) {
                                if (arrdefaultdeskdata[i].menukey == menukey) {
                                    obj = arrdefaultdeskdata[i];
                                }
                            }
                        }
                        obj.name = $(this).find('input').val();
                        arrnewdata.push(obj)
                    } else {
                        var menuid = $(this).data('menuid');
                        if (!menuid) {
                            return;
                        }
                        var obj = {
                            menuid: menuid,
                            image: $(this).find('img').attr('src'),
                            name: $(this).find('input').val()
                        };
                        //查看之前是否有数据
                        var has = false;
                        for (var i = 0; i < arrolddeskdata.length; i++) {
                            if (menuid == arrolddeskdata[i].menuid) {
                                obj = arrolddeskdata[i];
                                has = true;
                                break;
                            }
                        }
                        if(!has){
                            obj.arrmenuid = parent.objdata.objMenu[menuid][4].split(",");
                        }
                        obj.name = $(this).find('input').val();
                        arrnewdata.push(obj)
                    }
                })
                $.sm(function (re) {
                    if (re) {
                        parent.layer.close(index);
                        parent.layer.confirm("刷新后生效，现在刷新吗？", function (index) {
                            parent.location.reload();
                        });
                    } else {
                        parent.layer.msg('重置失败，请稍后再试');
                    }
                }, ['user.updatestrdeskdata', JSON.stringify(arrnewdata)])
            })
        })
        $("#btnresetdesk").click(function () {
            parent.layer.confirm("确定重置桌面设置吗？", function (index) {
                $.sm(function (re) {
                    if (re) {
                        parent.layer.close(index);
                        parent.layer.confirm("刷新后生效，现在刷新吗？", function (index) {
                            parent.location.reload();
                        });
                    } else {
                        parent.layer.msg('重置失败，请稍后再试');
                    }
                }, ['user.updatestrdeskdata', ''])
            })
        })
    }
});