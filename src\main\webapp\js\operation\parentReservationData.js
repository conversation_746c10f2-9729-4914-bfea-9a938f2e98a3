/*新增日期: 2025.06.18
作 者: 朱辽原
修改时间：
修改人：
內容摘要: 家长预约大数据数据来源（ mockjs ）
*/
require.config({
    paths: {
        mock: '../../plugin/mock/dist/mock'
    },
    waitSeconds: 0
})

// 配置常量
const CHART_CONFIG = {
    deptTitle: '科室预约量对比',
    packageTitle: '体检套餐使用率',
    timeTitle: '预约时段分布',
    cancelTitle: '预约取消情况',
    colorScheme: ['#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE', '#3BA272', '#FC8452', '#9A60B4', '#EA7CCC']
}

require(['mock'], () => {
    const Mock = require('mock')
    // 启用请求拦截
    Mock.setup({
        timeout: '200-600' // 模拟网络延迟
    })

    /**
     * 统计卡片
     */
    Mock.mock(/\/parentReservation\/stats/, 'post', function (option) {
        console.log('拦截到 POST 请求:', option) // 可选：打印请求信息
        return Mock.mock({
            code: 0,
            msg: 'success',
            data: {
                appointmentSuccess: Mock.Random.natural(1500, 2500), // 预约成功数
                appointmentCancellation: function () {
                    return Math.round(this.appointmentSuccess * 0.15);
                }, // 预约取消
                parentCount: Mock.Random.natural(800, 1200), // 父母预约量
                childCount: function () {
                    return Math.round(this.parentCount * 0.8); // 儿童数约为父母数的1.2倍
                }, // 儿童预约量
                avgAppointmentsPerParent: Mock.Random.float(1.5, 2.5, 1, 2), // 平均每个父母预约量
                appointmentsDepartment: 7 // 预约科室数
            }
        })
    })

    /**
     * 科室预约量
     */
    Mock.mock(/\/parentReservation\/dept/, 'post', function (option) {
        const departments = ['耳鼻喉科', '眼科', '外科', '内科', '儿科']
        return {
            code: 0,
            msg: 'success',
            data: {
                labels: departments,
                values: [
                    Mock.Random.natural(80, 120),  // 儿科 - 最高
                    Mock.Random.natural(50, 80),   // 耳鼻喉科
                    Mock.Random.natural(40, 70),   // 眼科
                    Mock.Random.natural(30, 60),   // 外科
                    Mock.Random.natural(20, 50)    // 内科
                ]
            }
        }
    })

    /**
     * 套餐使用率
     */
    Mock.mock(/\/parentReservation\/package/, 'post', function (option) {
        const base = Mock.Random.float(500, 700, 1, 1);
        const micro = Mock.Random.float(150, 250, 1, 1);
        const allergy = Mock.Random.float(100, 200, 1, 1);
        const other = Math.floor(Math.min(base, micro, allergy) / 5);
        return {
            code: 0,
            msg: 'success',
            data: [
                {name: '基础套餐', value: Math.floor(base)},
                {name: '微量元素', value: Math.floor(micro)},
                {name: '过敏原检测', value: Math.floor(allergy)},
                {name: '其他', value: other > 0 ? Math.floor(other) : 5} // 确保不为负
            ]
        }
    })

    /**
     * 预约时段分布
     */
    Mock.mock(/\/parentReservation\/time/, 'post', function (option) {
        // 生成24小时的时间序列数据
        const timeData = []
        // 设置基准值并添加早晚高峰模式
        const baseValues = [
            {time: '08:00', min: 80, max: 120},  // 早高峰
            {time: '10:00', min: 60, max: 100},
            {time: '12:00', min: 30, max: 60},   // 午间低谷
            {time: '14:00', min: 50, max: 90},
            {time: '16:00', min: 70, max: 110}, // 晚高峰
            {time: '18:00', min: 40, max: 80},
            {time: '20:00', min: 20, max: 50},
            {time: '22:00', min: 10, max: 30},   // 夜间最低
            {time: '00:00', min: 5, max: 20},
            {time: '02:00', min: 5, max: 15},
            {time: '04:00', min: 5, max: 15},
            {time: '06:00', min: 20, max: 40}
        ];

        baseValues.forEach(item => {
            timeData.push({
                time: item.time,
                count: Mock.Random.natural(item.min, item.max)
            });
        });
        return {
            code: 0,
            msg: 'success',
            data: timeData
        }
    })

    /**
     * 预约取消情况
     */
    Mock.mock(/\/parentReservation\/cancel/, 'post', function (option) {
        const appointment = Mock.Random.natural(800, 1200);
        const cancelRate = Mock.Random.float(0.1, 0.2, 2, 2); // 10-20%取消率
        const cancel = Math.round(appointment * cancelRate);
        return {
            code: 0,
            msg: 'success',
            data: [
                {name: '已预约', value: appointment},
                {name: '已取消', value: cancel}
            ]
        }
    })

    // 拦截性别数据请求
    Mock.mock(/\/parentReservation\/childGender/, 'post', () => {
        const total = Mock.Random.natural(200, 1000)
        const male = total / 2
        const female = total - male
        return Mock.mock({
            code: 0,
            message: 'success',
            data: [
                {
                    name: '男性',
                    value: Math.floor(male)
                },
                {
                    name: '女性',
                    value: Math.floor(female)
                }
            ]
        })
    })

    // 拦截设备数据请求
    Mock.mock(/\/parentReservation\/device/, 'post', () => {
        return Mock.mock({
            code: 0,
            message: 'success',
            data: [
                {
                    name: '安卓',
                    value: '@integer(100, 150)'
                },
                {
                    name: 'iOS',
                    value: '@integer(100, 150)'
                },
                {
                    name: '未知',
                    value: '@integer(100, 150)'
                }
            ]
        })
    })

    // 拦截年龄数据请求
    Mock.mock(/\/parentReservation\/age/, 'post', () => {
        return Mock.mock({
            code: 0,
            message: 'success',
            data: [
                {
                    name: '36到45岁',
                    value: Mock.Random.natural(100, 400)
                },
                {
                    name: '26到35岁',
                    value: Mock.Random.natural(100, 400)
                },
                {
                    name: '46到60岁',
                    value: Mock.Random.natural(100, 400)
                },
                {
                    name: '18岁到25岁',
                    value: Mock.Random.natural(100, 400)
                },
                {
                    name: '60岁以上',
                    value: Mock.Random.natural(100, 400)
                },
                {
                    name: '未知',
                    value: Mock.Random.natural(100, 400)
                }
            ]
        })
    })

    // 拦截区域数据请求
    Mock.mock(/\/parentReservation\/region/, 'post', () => {
        return Mock.mock({
            code: 0,
            message: 'success',
            data: {
                // 模拟不同距离范围的家长数、幼儿数
                areas: [
                    {
                        distance: '1km',
                        parentCount: '@integer(10, 50)',
                        childCount: '@integer(10, 50)'
                    },
                    {
                        distance: '3km',
                        parentCount: '@integer(30, 80)',
                        childCount: '@integer(30, 80)'
                    },
                    {
                        distance: '5km',
                        parentCount: '@integer(50, 100)',
                        childCount: '@integer(50, 100)'
                    },
                    {
                        distance: '未知',
                        parentCount: '@integer(50, 100)',
                        childCount: '@integer(50, 100)'
                    }
                ]
            }
        })
    })

    // 获取关系数据
    Mock.mock(/\/parentReservation\/relationship/, 'post', () => {
        const categories = [
            '爸爸',
            '妈妈',
            '爷爷',
            '奶奶',
            '外公',
            '外婆',
            '叔叔',
            '阿姨',
            '婶婶',
            '姑妈',
            '姑父',
            '伯父',
            '伯母',
            '舅舅',
            '舅妈',
            '哥哥',
            '姐姐',
            '其他'
        ];

        const values = [
            Mock.Random.natural(180, 220),  // 爸爸
            Mock.Random.natural(200, 250),  // 妈妈
            Mock.Random.natural(30, 60),    // 爷爷
            Mock.Random.natural(40, 70),    // 奶奶
            Mock.Random.natural(25, 50),    // 外公
            Mock.Random.natural(35, 60),    // 外婆
            Mock.Random.natural(5, 15),     // 叔叔
            Mock.Random.natural(5, 15),     // 阿姨
            Mock.Random.natural(3, 10),     // 婶婶
            Mock.Random.natural(3, 10),     // 姑妈
            Mock.Random.natural(2, 8),      // 姑父
            Mock.Random.natural(2, 8),      // 伯父
            Mock.Random.natural(2, 8),      // 伯母
            Mock.Random.natural(2, 8),      // 舅舅
            Mock.Random.natural(2, 8),      // 舅妈
            Mock.Random.natural(1, 5),      // 哥哥
            Mock.Random.natural(1, 5),      // 姐姐
            Mock.Random.natural(5, 15)      // 其他
        ];
        return Mock.mock({
            code: 0,
            message: 'success',
            data: {
                categories: categories,
                values: values
            }

        })
    })

    // 拦截年龄数据请求
    Mock.mock(/\/parentReservation\/childAge/, 'post', () => {
        const base = Mock.Random.natural(800, 1200);
        return Mock.mock({
            code: 0,
            message: 'success',
            data: [
                {
                    name: '3岁',
                    value: Math.round(base * 0.35)  // 35%
                },
                {
                    name: '4岁',
                    value: Math.round(base * 0.35)  // 35%
                },
                {
                    name: '5岁',
                    value: Math.round(base * 0.2)   // 20%
                },
                {
                    name: '6岁',
                    value: Math.round(base * 0.08)  // 8%
                },
                {
                    name: '7岁',
                    value: Math.round(base * 0.02)  // 2%
                }
            ]

        })
    })
})