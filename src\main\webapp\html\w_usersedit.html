﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>用户管理</title>
    <link rel="stylesheet" href="../css/reset.css"/>
    <link rel="stylesheet" href="../plugin/jQueryValidationEngine/css/validationEngine.jquery.css" type="text/css"/>
    <link rel="stylesheet" href="../css/style.css"/>
	<link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../css/medical.css">
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <!--[if lt IE 9]>
    <script src="../sys/html5shiv.min.js"></script>
    <![endif]-->
    <style>
        .u-edit {
            display: none;
        }
        
        .divrole {
            display: none;
        }
        
        .layui-form-label {
            width: 160px
        }
        .location{
            cursor: pointer;
            padding: 4px 4px 4px 20px;
            margin-left: 4px;
            background: url(../images/locate.png) 0 2px no-repeat;
            background-size: 20px;
        }
        #bmapicon{
            display: inline-block;
            background: url(../images/bmapicon.png) center no-repeat;
            background-size: 20px;
            width: 30px;
            height: 20px;
            float: right;
        }
        .theme-fgc{
            color: #1da89e !important;
        }
		.cl2.lanflag{color: #ff5a5a;}
		#lanlocalup{color: #fff;}
        .editrv{
            display: none;
        }
    </style>
</head>
<body>
<section class="personalInfo font14 cl1 ">
    <form id="form" class="layui-form" style="height: 100%;width: 100%;position: absolute">
        <section id="contant" style="overflow-y: scroll;overflow-x: hidden" class="clearfix pd20">
            <section id="divicon" class="fl personalImg" style="margin-top:100px">
                <img src="../images/default.png" onerror="this.src='../images/default.png'" alt="" class="otx-head"/>
                <p>尺寸：96*96</p>
                <p>支持jpg、png、gif、bmp</p>
                <a class="cl2 lanflag editrv" id="btndelimg">删除</a>
                <span id="btnup" class="editrv">
                        <a class="lanflag" id="lanlocalup">本地上传</a>
                    </span>
            </section>
            <section class=" fl">
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>真实姓名：</label>
                    <div class="layui-input-inline">
                        <input id="txttruename" type="text" name="truename" required lay-verify="required"
                               placeholder="请输入真实姓名" maxlength="30" autocomplete="off"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>性别：</label>
                    <input type="radio" name="sex" value="男" title="男" checked>
                    <input type="radio" name="sex" value="女" title="女">
                    <input type="hidden" id="txtsex"/>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>手机：</label>
                    <div style="display:none;">
                        <input type="text" name="mobile"/>
                    </div>
                    <div class="layui-input-inline">
                        <input id="txtmobile" type="text" name="mobile" maxlength="13" readonly="readonly" required lay-verify="required" placeholder="手机" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>用户名：</label>
                    <div class="layui-input-inline">
                    	<div style="display:none;">
                    		<input type="text" name="uname"/>
                    	</div>
                        <input id='txtuname' type="text" readonly="readonly" name="uname" required lay-verify="required" placeholder="请输入用户名" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item u-edit">
                    <label class="layui-form-label"><em>*</em>密码：</label>
                    <div class="layui-input-inline">
                        <input id="txtpwd" type="text" onfocus="this.type='password';" name="pwd" required placeholder="请输入密码" readonly="readonly" autocomplete="off" class="layui-input">
                        <table border="1" cellspacing="0" cellpadding="1" bordercolor="#eeeeee" height="22" style="width: 200px;margin-top: 2px;color:#fff;">
                            <tbody><tr align="center" bgcolor="#f5f5f5">
                                <td width="25%" id="strength_L">弱</td>
                                <td width="25%" id="strength_M">中</td>
                                <td width="25%" id="strength_H">强</td>
                                <td width="25%" id="strength_G">极佳</td>
                            </tr></tbody>
                        </table>
                    </div>
                </div>
                <div class="layui-form-item u-edit">
                    <label class="layui-form-label"><em>*</em>确认密码：</label>
                    <div class="layui-input-inline">
                        <input id="txtpwdagin" type="text" onfocus="this.type='password'" name="pwdagin" required
                               placeholder="确认密码" maxlength="60" autocomplete="off"
                               readonly class="layui-input">
                    </div>
                </div>
                <div id="divrolesel" class="layui-form-item pr" style="display: none;">
                    <label class="layui-form-label"><em>*</em>角色：</label>
                    <div class="layui-input-inline">
                        <select id="roleSel" lay-filter="roles" lay-verify="required">
                        </select>
                    </div>
                    <input type="hidden" id="rolesid"/>
                    <div id="divrole" class="divrole pa bg4 font14">
                        <ul id="ulselect" class="ulselect"></ul>
                    </div>
                </div>
                <div class="layui-form-item pr" id="divselpart" style="display:none;">

                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">邮箱：</label>
                    <div class="layui-input-inline">
                        <input id="txtemail" type="text" name="email" placeholder="邮箱" maxlength="30" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">单位名称：</label>
                    <div class="layui-input-inline">
                        <input id="txtdepartname" type="text" name="departname" placeholder="20字以内" maxlength="30" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">单位地址：</label>
                    <div class="layui-input-inline">
                        <input id="txtdepartaddress" type="text" name="departaddress" autocomplete="off" class="layui-input">
                    </div>
<!--                    <span class="location" id="mapmsg"><a class="theme-fgc">点击图标定位</a></span><a id="bmapicon" title="点击查看百度地址"></a>-->
                </div>
                <div class="layui-form-item" id="divCanSlice" style="display: none;">
<!--                    <label class="layui-form-label"><em>*</em>是否允许创建下级分片：</label>-->
<!--                    <input type="radio" name="radisadmin" value="1" title="是">-->
<!--                    <input type="radio" name="radisadmin" value="0" title="否" checked>-->
                    <input type="hidden" id="txtareacode"/>
                </div>
<!--                <div class="layui-form-item" id="duanxin">-->
<!--                    <input type="checkbox" name="sendmessage" value="1" lay-skin="primary" title="发送短信通知成功创建账户">-->
<!--                </div>-->
            </section>
        </section>
        <div class="btns-foot" >
            <input type="hidden" id="txtroletype"/>
            <input type="hidden" id="txtdepartid"/>
            <a id="btnsave" class="layui-btn addbtn editrv" lay-submit="" lay-filter="btnsave">保存</a>
            <a id="btnisforbid" class="layui-btn addbtn" lay-filter="isforbid" style="display: none;">禁用</a>
            <a id="btnnotforbid" class="layui-btn addbtn" lay-filter="noforbid" style="display: none;">取消禁用</a>
            <a style="display:none;" id="btnresetpwd" class="layui-btn addbtn">密码重置</a>
            <a style="display:none;" id="btnunbindwx" class="layui-btn addbtn">解绑微信</a>
            <a style="display:none;" id="btndelete" class="layui-btn addbtn">删除</a>
            <!--                 <a style="display:none;" id="btncheck" class=" layui-btn">检测</a> -->
            <a id="btncancel" class="layui-btn addbtn">取消</a>
        </div>
    </form>
</section>
<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../sys/uploadutil.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../plugin/ueditor/third-party/webuploader/webuploader.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../plugin/validator/js/pwdvalidator.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../js/w_usersedit.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>
