﻿<!DOCTYPE html>
<html>

<head>
	<meta charset="utf-8" />
	<title>专家讲座大数据</title>
	<link rel="stylesheet" href="../../css/reset.css" />
	<link rel="stylesheet" href="../../css/icon.css" />
	<link rel="stylesheet" href="../../css/style.css" />
	<link rel="stylesheet" href="../../layui-btkj/css/layui.css" />
	<link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css" />
	<link rel="stylesheet" href="../../css/physical.css" />
	<script src="../../plugin/highcharts/highcharts.js"></script>
	<script src="../../plugin/highcharts/highcharts-3d.js"></script>
	<script src="../../plugin/highcharts/cylinder.js"></script>
	<script src="../../plugin/highcharts/funnel3d.js"></script>
	<!--[if lt IE 9]>
			<script src="../sys/html5shiv.min.js"></script>
		<![endif]-->
	<style type="text/css">
		html,
		body {
			background: #eaeff3;
		}

		.layui-form-label {
			padding: 8px 0px 5px 10px;
			width: auto;
		}

		#parentChildRelation {
			width: 100%;
			height: 90%;
		}

		.safestat-tit,
		.num-label {
			cursor: pointer;
			user-select: none;
			-webkit-user-select: none;
			/* Chrome, Safari, Opera */
			-moz-user-select: none;
			/* Firefox */
			-ms-user-select: none;
			/* Internet Explorer/Edge */
		}
	</style>
</head>

<body>
	<div class="marmain" style="min-width: 950px">
		<div class="content-medical">
			<div class="layui-form layui-comselect" style="position: relative">
				<div class="btn-nextfl">
					<span class="layui-form-label" style="text-align: left; padding-left: 0">发布时间：</span>
					<div class="layui-input-inline" style="float: left; width: 120px">
						<input id="startDate" type="text" autocomplete="off" placeholder="选择开始日期" class="layui-input">
					</div>
					<div class="layui-form-mid">-</div>
					<div class="layui-input-inline layui-form isshowenrollstatus" style="float: left; width: 120px"
						id="diverollstatus">
						<input id="endDate" type="text" autocomplete="off" placeholder="选择结束日期" class="layui-input">
					</div>
					<div class="layui-input-inline layui-form isshowenrollstatus" style="float: left; width: 120px">
						<button class="layui-btn form-search" style="margin-left: 5px; vertical-align: top;"
							id="btnSearch">查询</button>
					</div>
				</div>
			</div>
		</div>
		<div id="content" style="width: 100%">
			<div class="marmain-cen">
				<div class="cenmartop">
					<div class="conference-sta">
						<div class="total-div" style="margin-left: 0">
							<img src="../../images/physical/jzkindergarten01.png" class="kindergartenimg" />
							<div class="total-panel">
								<div class="total-left">
									<img src="../../images/physical/zkindergarten06img.png" class="kinder-ltimg" />
									<div class="total-numbg">
										<div class="num-label" id="lecturesNumber">0</div>
										<div class="num-peo">专家讲座数</div>
									</div>
								</div>
							</div>
						</div>
						<div class="total-div">
							<img src="../../images/physical/jzkindergarten02.png" class="kindergartenimg" />
							<div class="total-panel">
								<div class="total-left">
									<img src="../../images/physical/dockindergarten02img.png" class="kinder-ltimg" />
									<div class="total-numbg">
										<div class="num-label" id="touchRate">0</div>
										<div class="num-peo">触达率</div>
									</div>
								</div>
							</div>
						</div>
						<div class="total-div">
							<img src="../../images/physical/jzkindergarten03.png" class="kindergartenimg" />
							<div class="total-panel">
								<div class="total-left">
									<img src="../../images/physical/jzkindergarten03img.png" class="kinder-ltimg" />
									<div class="total-numbg">
										<div class="num-label" id="viewNumber">0</div>
										<div class="num-peo">查看人数</div>
									</div>
								</div>
							</div>
						</div>
						<div class="total-div">
							<img src="../../images/physical/jzkindergarten04.png" class="kindergartenimg" />
							<div class="total-panel">
								<div class="total-left">
									<img src="../../images/physical/jzkindergarten04img.png" class="kinder-ltimg" />
									<div class="total-numbg">
										<div class="num-label" id="registrationNumber">0</div>
										<div class="num-peo">报名人数</div>
									</div>
								</div>
							</div>
						</div>
						<div class="total-div">
							<img src="../../images/physical/jzkindergarten05.png" class="kindergartenimg" />
							<div class="total-panel">
								<div class="total-left">
									<img src="../../images/physical/jzkindergarten05img.png" class="kinder-ltimg" />
									<div class="total-numbg">
										<div class="num-label" id="signNumber">0</div>
										<div class="num-peo">签到人数</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="visiting-statis">
						<div class="safestat-cen">
							<div class="safestat-tit"><i class="left-tit"></i><span>通知触达情况</span></div>
							<div class="colunwit" id="noticeReach"></div>
						</div>
						<div class="safestat-cen">
							<div class="safestat-tit"><i class="left-tit greenbg"></i><span>用户打开趋势</span></div>
							<div class="colunwit" id="userOpen"></div>
						</div>
					</div>
					<div class="visiting-statis">
						<div class="safestat-cen">
							<div class="safestat-tit"><i class="left-tit greenbg"></i><span>报名时段分布</span></div>
							<div class="colunwit" id="registrationTime"></div>
						</div>
						<div class="safestat-cen">
							<div class="safestat-tit"><i class="left-tit"></i><span>现场听讲转化漏斗</span></div>
							<div class="colunwit" id="appointmentTime"></div>
						</div>
					</div>
					<div class="visiting-statis">
						<div class="safestat-cen heighrow">
							<div class="safestat-tit"><i class="left-tit"></i><span>家长画像</span></div>
							<div class="colunwit">
								<div class="weui-flex hit50">
									<div class="columnlist" id="parentGender">
										<h3>性别</h3>
										<div></div>
									</div>
									<div class="columnlist" id="parentDevice">
										<h3>访问设备</h3>
										<div></div>
									</div>
								</div>
								<div class="weui-flex hit50">
									<div class="columnlist" id="parentAge">
										<h3>年龄分布</h3>
										<div></div>
									</div>
									<div class="columnlist" id="parentRegion">
										<h3>区域分布</h3>
										<div></div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="visiting-statis">
						<div class="safestat-cen">
							<div class="safestat-tit"><i class="left-tit"></i><span>孩子与家长关系</span></div>
							<div id="parentChildRelation"></div>
						</div>
					</div>
					<div class="visiting-statis">
						<div class="safestat-cen">
							<div class="safestat-tit"><i class="left-tit"></i><span>幼儿画像</span></div>
							<div class="colunwit weui-flex">
								<div class="columnlist" id="childrenGender">
									<h3>性别</h3>
									<div>11</div>
								</div>
								<div class="columnlist" id="childrenAge">
									<h3>年龄分布</h3>
									<div>11</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- <script type="text/javascript" data-main="../../js/operation/expertLectures.js" src="../../sys/require.min.js"></script> -->
	<script type="text/javascript" data-main="../../js/operation/expertLectures.js" src="../../sys/require.min.js"></script>
</body>

</html>