<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>团购订单缴费</title>
	<meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
	<link rel="stylesheet" href="../css/reset.css"/>
	<link rel="stylesheet" href="../css/style.css"/>
	<link rel="stylesheet" href="../css/icon.css"/>
	<link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../css/medical.css">
	<style>
		body{
			background: #F7F7F7;
			overflow: hidden;
		}
		.group-list{border:none;min-width: 1500px;}
	</style>
</head>
	<body>
		<div style="">
			 <div class="content-medical"  style="margin: 0px;border-radius:0px;">
				<div class="layui-tab-brief tab-tit" style="margin-bottom: 0;padding: 0px 10px 0 0;">
					<ul id="ultop-tab" class="common-tab layui-tab-title">
						<li class="layui-this">园所录入预约</li>
<!--						<li>员工扫码预约</li>-->
					</ul>
					<span class="fr" style="display: none;">
						 <a class="layui-btn btn-add btn-default" id="btn-add" style="margin-top: 5px;">添加园所录入预约</a>
					</span>
				</div>
			</div>
			 <div id = "commondiv" style="background-color: white; margin: 8px 0 8px 8px;height: auto;">
				<div class="layui-tab-brief tab-tit" style="margin-bottom: 0;padding: 0px 10px 0 0;">
					<ul id="ulcommon-tab" class="common-tab layui-tab-title"  style="display:none;">
						<li class="layui-this">全部订单</li>
						<li>待提交订单</li>
						<li>已提交订单</li>
					</ul>
				</div>
			<!--<div style="width:100%;float:left;margin-top: 10px;">-->
				<div style="float:right;margin-top: 6px;">
					<!--<span class="fl">-->
						<!--<span class="layui-form-label" style="width: 80px;">团购单号：</span>-->
						<!--<div class="layui-input-inline" style="float: left;">-->
							<!--<input id="oddnumber" type="text" autocomplete="off" placeholder="团购单号" class="layui-input" style="width: 160px;">-->
						<!--</div>-->
						<!--<button class="layui-btn mgl-20" id="search" style="margin-left: 10px;">查询</button>-->
					<!--</span>-->
<!--					<span class="fr"  style="position: absolute;right: 10px;top: 62px;">-->
<!--						 <a class="layui-btn btn-add btn-default" id="btn-download">导入模板下载</a>-->
<!--					</span>-->
				</div>

				<div style="margin-top: 10px;">
					<div class="group-list-container" style="width:100%;">
						<!--<h5 class="group-list-tit">
							<span>北京童话邦教育科技有限公司</span>
							<span style="margin-left: 16%;text-decoration: underline;color: #2855FC;cursor: pointer;">团购单号：84840838048038403</span>
							<span style="margin-left: 16%;">团购人/手机号：李四（妇幼）/13900020001</span>
							<span class="state-label state-pink" style="float: right;margin-top: 7px;">部分已体检</span>
						</h5>
						<div class="layui-row">
							<div class="phy-list layui-col-xs4 layui-col-sm4 layui-col-md4">
								<div style="margin: 10px 20px;">
									<h5 class="phy-list-tit">团购预订的套餐</h5>
									<div>
										<img src="../images/physical/institute_img.jpg" style="width: 130px;height: 130px;">
										<div class="group-buy">
											<h6 style="font-size: 16px;color: #003cff;text-decoration: underline;">感恩尊享体检套餐</h6>
											<div style="color: #999999;">截止日期：2018年7月28日</div>
											<div style="color: #999999;">适用人群：男性、女性</div>
											<div style="color: #999999;">
												<div class="cost-detial">男：<s style="margin-right: 5px;">￥120</s><span style="font-size: 18px;color: #000000;">￥96</span><span class="discount"><i>8.5折</i></span></div>
												<div class="cost-detial" style="margin-left: 10px;">女：<s style="margin-right: 5px;">￥150</s><span style="font-size: 18px;color: #000000;">￥120</span><span class="discount"><i>8.5折</i></span></div>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div class="layui-col-xs6 layui-col-sm6 layui-col-md6">
								<div class="phy-list layui-col-xs6 layui-col-sm6 layui-col-md6">
									<div style="margin: 15px auto;width: 270px;">
										<h5 class="phy-list-tit">团购人数</h5>
										<div style="display: inline-block;">
											<div style="display: table-cell;vertical-align: middle;height: 100px;">
												<div class="phy-label"><img src="../images/physical/male_ico.png"><span style="margin: 0 15px;">男</span><span>1人</span></div>
												<div class="phy-label"><img src="../images/physical/female_ico.png"><span style="margin: 0 15px;">女</span><span>1人</span></div>
											</div>
										</div>
										<button class="layui-btn layui-btn-normal" style="vertical-align: top;margin-top: 30px;float: right;">查看名单</button>
										<div style="padding-top: 10px;clear: both;">预订共计：<span style="color: #333333;font-weight: bold;">￥920</span></div>
									</div>
								</div>
								<div class="phy-list layui-col-xs6 layui-col-sm6 layui-col-md6">
									<div style="margin: 15px auto;width: 270px;">
										<h5 class="phy-list-tit">已完成体检情况</h5>
										<div style="display: inline-block;">
											<div style="display: table-cell;vertical-align: middle;height: 100px;">
												<div class="phy-label"><img src="../images/physical/male_ico.png"><span style="margin: 0 15px;">男</span><span>1人</span></div>
												<div class="phy-label"><img src="../images/physical/female_ico.png"><span style="margin: 0 15px;">女</span><span>1人</span></div>
											</div>
										</div>
										<button class="layui-btn layui-btn-normal" style="vertical-align: top;margin-top: 30px;float: right;">查看名单</button>
									</div>
								</div>
							</div>
							<div class="layui-col-xs2 layui-col-sm2 layui-col-md2" style="text-align: center;">
								<div style="height: 26px;line-height: 26px;margin: 54px 0 26px 0;"><img src="../images/physical/untime_ico.png"><span style="font-size: 16px;color: #999999;margin-left: 8px;">还剩58天</span></div>
								<button class="yellow-btn">确认缴费</button>
							</div>
						</div>-->
					</div>
				</div>
			</div>
	   </div>
		<script>
			var v = top.version;
			document.write("<script type='text/javascript' src='../sys/jquery.js?v=" + v + "'><" + "/script>");
			document.write("<script type='text/javascript' src='../sys/arg.js?v=" + v + "'><" + "/script>");
            document.write("<script type='text/javascript' src='../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
            document.write("<script type='text/javascript' src='../sys/function.js?v=" + v + "'><" + "/script>");
			document.write("<script type='text/javascript' src='../js/custom/checkorderlist.js?v=" + v + "'><" + "/script>");
		</script>
	</body>
</html>
