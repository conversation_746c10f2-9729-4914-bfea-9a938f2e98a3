﻿<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8"/>
	<title>上报报表设置</title>
	<link rel="stylesheet" href="../css/reset.css" />
	<link rel="stylesheet" href="../css/style.css" />
	<link rel="stylesheet" href="../layui-btkj/css/layui.css" media="all">
	<script type="text/javascript">
		document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
	</script>
	<!--[if lt IE 9]>
	    <script src="../sys/html5shiv.min.js"></script>     
    <![endif]-->
    <style>
    </style>
	<script src="../plugin/js/selarea.js"></script>
</head>
<body>
 	<section class="popRight" style="margin-left: 20px;display: inline;">
 		<!-- search -->
 		<section class="bg4 dic-head" style="padding-bottom: 0;">
 		    <section class="otx-search" style="padding: 20px 40px;border: 1px solid #ccc;">
 		    	<!-- <ul class="selbtn-ul">
            		<li class="click-btn">报告列表</li>
            		<li class="">上报报表</li>
        		</ul> -->
        		<div  id="radio">
		 		    <form class="layui-form layui-form-pane">
		 		    	<input type="radio" name="report" value="0" title="报告列表" checked>
		      			<input type="radio" name="report" value="1" title="上报报表">
		 		    </form>
		 		</div>
 		    </section>
 		</section>
 		<!-- 报表信息 -->
 		<section id="reportlist" style="display:none;">
 			<h2 class="noticeTitle bg1"><span class="lanflag">报告列表</span><a class="fr cl2"><span class="lanflag addreport" id="addreport">添加报表</span></a></h2>
	 		<!-- <h1 class="cl3">报表管理</h1> -->
	 		<div id = "dianji2">
			<table id="tblist" lay-filter="test"></table>
			</div>
			<script type="text/html" id="bianji">
				<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
			</script>
			<script type="text/html" id="shanchu">
				<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
			</script>
 		</section>
 		<!-- 上报报表 --> 		
 		<section id="reportslist" style="display:inline;">
 			<h2 class="noticeTitle bg1"><span class="lanflag">上报报表</span><a class="fr cl2"><span class="lanflag addreport" id="addescalation">添加上报信息</span></a></h2>
	 		<!-- <h1 class="cl3">报表管理</h1> -->
	 		<div id = "dianji">
			<table id="tbscalation" lay-filter="test"></table>
			</div>
			<script type="text/html" id="bianjiscalation">
				<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
			</script>
			<script type="text/html" id="tingyong">
				{{#  if(d.isforbid == 1){ }}
				<a class="layui-btn layui-btn-xs" lay-event="use">启用</a>
				{{#  } else { }}
				<a class="layui-btn layui-btn-xs" lay-event="unuse">禁用</a>
				{{#  } }}
			</script>
			<script type="text/html" id="shanchuscalation">
				<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
			</script>
 		</section> 		
 	</section>
	<script type="text/javascript" src="../layui-btkj/layui.js"></script>
	<script type="text/javascript" src="../sys/jquery.js"></script>
	<script type="text/javascript" src="../sys/arg.js"></script>
	<script type="text/javascript" src="../js/reportset.js"></script>
</body>
</html>
