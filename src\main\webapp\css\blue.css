/*蓝色皮肤*/
/*color*/
.bg1{background: #2c99de;}
.bg2{background: #d4d5d9;}
.bg3{background: #2c99de;}
.bg4{background: #f3f3f3;}/*浅灰*/
.bg5{background: #e9e9e9;}/*深灰*/
.cl1{color: #47494a;}
.cl2{color: #3398dc;}/*蓝色*/

/*scroll*/
.scrollArea,.foldList{/*background:#eee;*/ background:#fff;}

/*icon*/
u[class^=skinIcon],.layim_chateme .layim_chatsay .layim_zero,skinIcondenglu u,skinIconbq u{background-image:url(../images/icon-blue.png);}

/*btn*/
.btn-big,.personalImg .webuploader-pick,.btn-black,.monthHead,.echarts-dataview button{background: #4A90E2;}
.letterIndex{background: #ccc;}
.popTitle,.fc-day-header{background: #2c99de;}
.btn-border{border:1px solid #3398dc;}
.btn-border:hover{background: #3a99d9;color: #fff;}
.btn-black:hover,.btn-big:hover{background: #0069b0;}
.layui-layer-btn a.layui-layer-btn0,.btnTop a:hover,.btn-control:hover,.operateBtn a:hover{background:#2c99de;border-color:#2c99de;}
.layui-layer-btn a{font-size: 14px;}

/*tab*/
.tab li:hover, .tab li.current{color: #fff; background:#3a99d9!important;border: 1px solid #3a99d9;}
.tab li.pulldown.current{color: #fff; background:#3a99d9 url(../images/icon_pulldown_white.png) no-repeat 75px center!important;background-size: 12px !important;border: 1px solid #3a99d9;}
.tab li.pulldown:hover{color: #fff; background:#3a99d9 url(../images/icon_pulldown_white.png) no-repeat 75px center!important; background-size: 12px !important;border: 1px solid #3a99d9;}

/*letter*/
.letter{background:#eee;}

/*page*/
.dataTables_wrapper .dataTables_paginate .paginate_button.current, .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover,.dataTables_wrapper .dataTables_paginate .paginate_button:hover{ background: -webkit-linear-gradient(#3a99d9 0%, #007ed2 100%);background: linear-gradient(#3a99d9 0%, #007ed2 100%);color: #fff !important;border-color:#007ed2;filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#3a99d9', endColorstr='#007ed2', GradientType=0);}

/*聊天窗口*/
.layim_chatmore{border-right:1px solid #0089bb; background-color:#2c99de;}
.layim_chatlist li:hover,.layim_chatlist .layim_chatnow,.layim_chatlist .layim_chatnow:hover{background-color:#9bd8ee;}
.layim_sendbtn{ background-color:#2c99de;}
.layim_enter{border-left-color:#8dd3eb;}
.layim_sendbtn:hover,.layim_enter:hover{background-color:#0089bb;}
.layim_sendtype span:hover,.layim_sendtype span:hover i{background-color:#2c99de;color:#fff;}
.layim_chateme .layim_chatsay{background: #87bbde;}

/*左侧面板*/
.noticeTitle a:hover{color:#0069b0;}
.funList li{color: #30a2eb;}
.usualFun a:hover,.inner a:hover,.letterList li:hover,.letterList li.current,.btn-head:hover,.articleGray a:hover,.loadMore:hover,.innerHistory li:hover,.today_date,.alm_content p,.list .current,.ztree li a:hover,.foldList li p,.foldList li:hover,.dataTables_wrapper a,.otx-table a,.foldList li,.fc-sat .fc-day-num, .fc-sun .fc-day-num{color: #59b3f0;}/*蓝色*/
footer a.current,footer a:hover{color: #2c99de;}
.curList a,.workTotal{color: #fff;}
.curList .current,.curList a:hover{color: #0c2e46;}
/*.usualFun a:hover img,.inner a:hover img,.foldList li:hover img{background: url(../images/hover-blue.png) no-repeat center center;}
*/.list li{border-bottom: 1px solid #3497db;}
.leftTitle{border-bottom: 1px solid #2d323e; background: #3398dc;}
.btn-lang:hover,.current.btn-lang{border-color:#3499da;color:#3499da;}
.list .face{border-color:#b8b8b8;}

/*智能中控*/
.btn-far:hover,.btn-near:hover{background:url(../images/distance-blue.png);}
.fast:hover,.normal:hover,.slow:hover,.arrow-up:hover,.arrow-down:hover{background: url(../images/hover-blue.png);}
.numberList span:hover{background: url(../images/blue-big.png);}

/*日历*/
.fc-state-down,.fc-state-active,.fc-button:hover{border-color:#2c99de;}
.fc-planmeeting-button{border:0;border-radius: 0;}
.fc-year-button:hover,.fc-month-button:hover,.fc-agendaWeek-button:hover,.fc-agendaDay-button:hover,.fc-state-active{background: #2c99de;color:#fff;}

/*会控*/
.meetingIndex,.fc-planmeeting-button{color:#fff; background: #3499da;}
.rankList span.current{background: #3497db;}

/*栏目管理*/
.meauList span{border-top: 2px solid #2c99de; border-bottom: 2px solid #2c99de;}
.meauList .inner a{color: #2c99de;}
.inner a.selected{background: #e3f2fd;}

/*单位管理*/
.jOrgChart .node,.formList .webuploader-pick{background:#2c99de;}

/*tree*/
.ztree li a.curSelectedNode{background-color: #e3f2fd;border: 1px solid #2c99de;}

/*会议控制*/
.meetMain{border-top: 4px solid #2c99de;}
.submeetList{border-top: 4px solid #cfe8ff;}

/*下拉多选*/
.ui-widget-header{background: #3398dc; border-color:#3398dc;}
.ui-state-hover, .ui-widget-content .ui-state-hover, .ui-widget-header .ui-state-hover, .ui-state-focus, .ui-widget-content .ui-state-focus, .ui-widget-header .ui-state-focus{color: #3398dc;}
.ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active{background: none; border-color:#3398dc;color: #3398dc;}
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default{ border-color:#3398dc;color: #3398dc;}



/*新增加样式*/

.usualFun{ background:#FFFFFF; padding-left:3px; padding-bottom:3px; margin-top: -3px;}
.usualFun a{float: left; width: 32%; +width: 32%; margin-top:3px;position: relative;text-align: center;font-size: 12px;line-height: 28px;color:#ffffff; background:#30a2eb; margin-right:3px; font-size:14px;}
.usualFun img{width: 35px;height: 35px; display: block; margin:0 auto;/*border-radius: 50%;*/ /*background: url(../images/gray.png) no-repeat center center;*/ padding-top:10px}
.usualFun u{position: absolute;display: none;}

/*.inner a:hover{width: 32%; +width: 32%; margin-top:3px;position: relative;text-align: center;font-size: 12px;line-height: 28px;color:#ffffff; background:#53C2BC; margin-right:3px; font-size:14px; display:block}
*/


.inner a:hover{ color:#FFF; /*background:#30a2eb;*/ display:inline-block}

/*幼儿园名称下拉背景色*/
.selectColor{background-color:#2c99de }

/*智慧数字幼儿园*/
/*桌面头部菜单*/
.ultopnums li.current,.ultopnums li:hover,.topultool li:hover,.topultool li.current {
    color: #F3F4F4;border-bottom:4px solid #4A90E2;
}
/*菜单切换*/
.menutab ul{color: #34495E!important;}
.menutab ul li{color: #F3F4F4;border-bottom:3px solid #D5D6D6; 
	background: #fff;		
	border-image: -webkit-linear-gradient(#F2F3F4,#D5D6D6) 40 20;
    border-image: -moz-linear-gradient(#F2F3F4,#D5D6D6) 40 20;		
	border-image: -o-linear-gradient(#F2F3F4,#D5D6D6) 40 20;
	border-image: linear-gradient(#F2F3F4,#D5D6D6) 40 20;}
.menutab ul li.current{color: #34495E!important; background: #fff;}
#divdesktop .scrollArea{background: none;}
#divmenulist .funList li:hover{background: #F2F2F2;color: #666!important;}
#divdeskleft:after{content: "";width:6px;position: absolute;top: 0px;bottom: 0;right: -6px;box-shadow: 5px 0px 5px -4px inset #DCE0E4;
  
}
/*#divdeskleft{box-shadow: 5px 0px 5px -4px inset #DCE0E4;
  
}*/
.funtop .desktop-logo .tabout-div{box-shadow: 0px 0px 5px 2px #1e8acf;}
.funtop .desktop-logo .tab-cell{box-shadow: 0px -8px 4px -5px inset #76abcc;}

/*保教*/
.term-tab.current{background: #70b8e5;color: #ffffff;}
.term-tab.current .calendar-div{border: 1px solid #3b8dc0;}
.term-list.redbg{background: #70b8e5;}
.calen-top h5{background: #70b8e5;}
.calen-txt p{color: #70b8e5;}
.div-right .layui-table tbody.current{border: 2px solid #70b8e5;}
.div-right .layui-table tbody.current tr:nth-child(1) td:nth-child(1){background-color: #70b8e5;}
.div-right .layui-table td.pinkcurrent{background-color: #70b8e5;}
.workrest-left h5{background: #62afe0!important;}
.workrest-right h5{background: #70b8e5!important;}
.time-work li.time-worksel{ background:#70b8e5;}
.week-tab.current{background: #70b8e5;}
.icon-list .layui-btn{background: #399cda!important;}
.protect-educate .plan-txt.current{background: #70b8e5;color: #ffffff;border: 1px solid #70b8e5;}
.play-sub{border: 2px solid #70b8e5!important;}
.def-icon .icon_calen:before{color: #70b8e5!important;}
.opreate-icon .icon-txt .iconfont{color: #70b8e5!important;}
.protect-educate .plan-left-icon>div .iconfont:before{color: #70b8e5;}
.protect-educate .plan-left-icon>div .icon_ratio:before{border: 2px solid #70b8e5;}
.def-icon .icon_tableft:before,.def-icon .icon_tabright:before{color: #70b8e5;}
#a_unselect{color: #70b8e5!important;}
.upload-btn .opr_select{background: #70b8e5!important;}
.opr-btn.pay-save{background: #70b8e5!important;}
#pgriddivshipuarea.def-layui-table tr.current td:not(.tr-firsttd){background: #dbeffb;}
#pgridplantable.def-layui-table tr.current td:not(.tr-firsttd){background: #dbeffb;}
/*作息弹框按钮颜色*/
.opr-btn{background: #70b8e5;}
.cho-main .layui-btn{background: #70b8e5;}
.plan-con{border: 4px solid #70b8e5;}
.plan-leftspan.currentpink, .plan-rightspan.currentpink{background: #70b8e5;}
.plan-leftspan.currentred, .plan-rightspan.currentred{background: #1c8bd0;}
.left-contain .common-tab3 li.current a{color: #70b8e5;font-weight: bold;border-bottom: 2px solid #70b8e5;}
.left-contain .icon_search2:before{color: #70b8e5;}
.type-plan .iconfont.redicon2:before{color: #70b8e5;}
#divdt .layui-table tbody.current{border: 2px solid #70b8e5;}
#divdt .layui-table tbody.current tr:nth-child(1) td:nth-child(1){background-color: #70b8e5;}
#divdt .layui-table td.pinkcurrent{background-color: #70b8e5;}
#divdt .tdexist{background-color: #3ea0dd;}
.timetable-list .circle-label{background: #70b8e5;}
.teaching-list .teachadd-btn .teachlayui-btn{background: #70b8e5;}
.upload-list .layui-btn-normal, .teaching-list-bom .upload-right .layui-btn, .cover-list .layui-btn{background: #70b8e5;}
#add_pic .webuploader-pick{background: #70b8e5;}
.teaching-list .layui-btn{color: #70b8e5;}
.upload-btn .opr_save{background: #70b8e5!important;}
.upload-btn .opr_saveadd{background: #70b8e5!important;}
.course-opr .layui-tab-brief>.layui-tab-title .layui-this{color: #70b8e5;}
.course-opr .layui-tab-brief>.layui-tab-more li.layui-this:after,.course-opr .layui-tab-brief>.layui-tab-title .layui-this:after{border-bottom: 4px solid #70b8e5;}
#add_pic .webuploader-pick{background:#70b8e5;}
#add_pic .webuploader-pick, #add_video .webuploader-pick{background:#70b8e5;}
#add_audio .webuploader-pick{background:#70b8e5;}
#add_files .webuploader-pick{background:#70b8e5;}
/*日历颜色*/
.layui-laydate .layui-this {
  background: #4A90E2 !important;
}


.notice-pages .selected {background: #00ACEB;}
.notice-pages a:hover{background:#00ACEB;border:1px solid #00ACEB}
.notice-pages .pages-skip .skip-sure{background: #00ACEB;}