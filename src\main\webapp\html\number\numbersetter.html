<!DOCTYPE html>
<html>
<head>
    <title>挂号设置</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <link rel="stylesheet" href="../../css/reset.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <script type="text/javascript" src="../../layui-btkj/layui.js"></script>
    <style>
        .view p {
            color: #34495E;
            font-size: 14px;
        }

        .layui-form-label {
            text-align: left;
            width: 80px;
            color: #666666;
        }

        .datemain input, .datemain textarea {
            border: 1px solid #CBD6E1;
            color: #34495E;
        }

        .layui-form-radio {
            margin-top: 7px;
        }
    </style>
</head>
<body>
<section>
    <div style="margin: 20px 30px;">
        <div class="form-list layui-form" lay-filter="numbersetter">
            <div class="layui-form-item " style="margin: 0;">
                <label class="layui-form-label" style="float: left;"><em>*</em>支付设置：</label>
                <div class="layui-input-block" style="min-height: 30px;float: left;width: 80%; margin-left:0px;">
                    <select name="pay_type" id="pay_type" lay-verify="required" disabled>
                        <option>请选择</option>
                        <option value="1" selected>线下支付</option>
                        <option value="2">线上支付</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item " style="margin: 10px 0 0 0;">
                <label class="layui-form-label" style="float: left;">放号设置：</label>
                <div class="layui-input-block"
                     style="float: left;width: 80%;margin-left:0px;display: flex;height: 30px;line-height: 19px;">
                    <div style="display: flex;">
                        <label style="line-height: 36px;display: inline-block;min-width:50px;">每日：</label>
                        <span>
						<input id="start_time" type="text" autocomplete="off" placeholder="7：00"
                               class="layui-input settime WdateFmtErr" style="width: 130px;">
						<img for="settime" class="settimetu" src="../../images/newicon/ico_calendar.png"
                             style="cursor: pointer;position: relative;top: -29px;width: 20px;left: 103px;"> </span>
                        <span class="linebg">-</span> <span>
						<input id="end_time" type="text" autocomplete="off" placeholder="14:00"
                               class="layui-input settime" style="width: 130px;">
						<img for="settime" class="settimetu" src="../../images/newicon/ico_calendar.png"
                             style="cursor: pointer;position: relative;top: -29px;width: 20px;left: 103px;"> </span>
                        <label style="line-height: 36px;display: inline-block; margin: 0 5px;min-width: 120px">，可预定当天至第</label>
                        <span>
						<input id="days" type="text" autocomplete="off" placeholder="请输入数字" class="layui-input"
                               style="width: 100px;">
						</span>
                        <label style="line-height: 36px;display: inline-block;margin-left: 5px;min-width: 130px">天（包含）内的号源</label>
                    </div>
                </div>
            </div>
            <div class="layui-form-item " style="margin: 10px 0 0 0;">
                <label class="layui-form-label" style="float: left;">取号时间：</label>
                <div class="layui-input-block"
                     style="float: left;width: 80%;margin-left:0px;display: flex;height: 30px;line-height: 19px;">
                    <div style="display: flex;">
                        <label style="line-height: 36px;display: inline-block;min-width: 100px">上午号截止至：</label>
                        <span>
						<input id="morning_stop_time" type="text" autocomplete="off" placeholder="7：00"
                               class="layui-input settime WdateFmtErr" style="width: 130px;">
						<img for="settime" class="settimetu" src="../../images/newicon/ico_calendar.png"
                             style="cursor: pointer;position: relative;top: -29px;width: 20px;left: 103px;"> </span>
                        <label style="line-height: 36px;display: inline-block; margin: 0 5px 0  30px;min-width: 100px">下午号截止至</label>
                        <span>
						<input id="afternoon_stop_time" type="text" autocomplete="off" placeholder="14:00"
                               class="layui-input settime" style="width: 130px;">
						<img for="settime" class="settimetu" src="../../images/newicon/ico_calendar.png"
                             style="cursor: pointer;position: relative;top: -29px;width: 20px;left: 103px;"> </span>
                    </div>
                </div>
            </div>
            <div class="layui-form-item " style="margin: 10px 0 0 0;">
                <label class="layui-form-label" style="float: left;"><em>*</em>取号地点：</label>
                <div class="layui-input-block" style="float: left;width: 80%;margin-left:0px;">
                    <input type="text" placeholder="请输入" class="layui-input" id="address">
                </div>
            </div>
            <div class="layui-form-item " style="margin: 10px 0 0 0;">
                <label class="layui-form-label" style="float: left;"><em>*</em>退号时间：</label>
                <div class="layui-input-block" style="float: left;width: 80%;margin-left:0px;line-height: normal;">
                    <p>
                        <input id="refund_type1" name="refund" type="radio" value="1" title="随时退"
                               style="margin-right: 5px;">
                    </p>
                    <p style="display: flex;">
                        <input id="refund_type2" name="refund" type="radio" value="2" title="就诊前"
                               style="margin-right: 5px;margin-left: 30px;">
                        <span>
						<input id="refund_days" type="text" autocomplete="off" placeholder="请输入数字" class="layui-input"
                               style="width: 100px;margin-left: 5px;">
						</span>
                        <label style="line-height: 36px;display: inline-block; margin:0 5px;">个工作日</label>
                        <span>
						<input id="refund_times" type="text" autocomplete="off" placeholder="7：00"
                               class="layui-input settime WdateFmtErr" style="width: 130px;">
						<img for="settime" class="settimetu" src="../../images/newicon/ico_calendar.png"
                             style="cursor: pointer;position: relative;top: -29px;width: 20px;left: 103px;"> </span>
                        <label style="line-height: 36px;display: inline-block; margin:0 5px;">前取消</label>
                    </p>
                </div>
            </div>
            <div class="layui-form-item " style="margin: 10px 0 0 0;">
                <label class="layui-form-label" style="float: left;"><em>*</em>挂号限制：</label>
                <div class="layui-input-block" style="float: left;width: 80%;margin-left:0px;display: flex;">
                    <label style="line-height: 36px;display: inline-block;margin-right: 5px;min-width: 120px">就诊人每天限挂号</label>
                    <span>
					<input id="limit_number" type="number" autocomplete="off" placeholder="请输入数字" class="layui-input"
                           style="width: 100px;">
					</span>
                    <label style="display: inline-block; margin: 5px;">次，每个科室同一就诊单元（上午、下午）限挂号</label>
                    <span>
					<input id="same_dept_limit" value="1" disabled type="number" autocomplete="off" placeholder="请输入数字" class="layui-input"
                           style="width: 100px;background-color: #cacdd1;">
					</span>
                    <label style="display: inline-block; margin: 5px;">次</label>
                </div>
            </div>
        </div>
        <div style="margin: 20px 0 0 90px;">
            <button class="layui-btn bluebtn btnoperate" lay-submit="" lay-filter="formDemo"
                    style="background:#4A90E2 !important; color: #fff !important;width: 140px; height: 48px; line-height: 48px;"
                    onclick="save()" id="save">保存
            </button>
<!--            <button id="btncancel" onclick="btncancel()" class="layui-btn" style="color: #34495E;width: 140px; height: 48px;background: #FFFFFF;border: 1px solid #DEDEDE; line-height: 48px;">测试</button>-->
        </div>
    </div>

</section>
<script data-main="../../js/number/numbersetter" src="../../sys/require.min.js"></script>
<script src="../../sys/jquery.js"></script>
<script>
    //Demo
    layui.use('form', function () {
        var form = layui.form;

        //监听提交
        form.on('submit(formDemo)', function (data) {
            layer.msg(JSON.stringify(data.field));
            return false;
        });
    });
</script>
<script>
    layui.use(['form',  'laydate'], function () {
    });
</script>
</body>
</html>
