﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>角色管理</title>
    <link rel="stylesheet" href="../css/reset.css"/>
    <link rel="stylesheet" href="../plugin/zTree/css/zTreeStyle/zTreeStyle.css" type="text/css">
    <link rel="stylesheet" href="../css/style.css"/>
	 <link rel="stylesheet" href="../../styles/xbcomstyle.css">
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <!--[if lt IE 9]>
    <script src="../sys/html5shiv.min.js"></script>
    <![endif]-->
    <style>
        body {
            background: #EAEFF3;
        }

        table.appo-table th, table td {
            padding: 0px;
        }

        .popRight {
            margin-left: 260px;
            background: #fff;
        }
		.otx-table{line-height: 38px; }
    </style>
</head>
<body>
<div class="marmain">
    <section style="padding: 10px 10px;background: #fff;" >
        <table class="otx-table roleTable font14" cellpadding="0" cellspacing="0">
            <thead>
            <tr>
                <th style="width:30%;">录入项目</th>
                <th style="width:50%;">人员</th>
                <th style="width:20%;">操作</th>
            </tr>
            </thead>
            <tbody id="tblist">
            <tr>
                <td>身高体重</td>
                <td id="hw" class="person">

                </td>
                <td>
                    <a id="btnaddhw" class="btnadd" data-item="hw" style="display: none;">添加</a>
                </td>
            </tr>
            <tr>
                <td>眼科</td>
                <td id="eye" class="person">

                </td>
                <td>
                    <a id="btnaddeye" class="btnadd" data-item="eye" style="display: none;">添加</a>
                </td>
            </tr>
            <tr>
                <td>听力</td>
                <td id="listen" class="person">

                </td>
                <td>
                    <a id="btnaddlisten" class="btnadd" data-item="listen" style="display: none;">添加</a>
                </td>
            </tr>
            <tr>
                <td>口腔</td>
                <td id="car" class="person">

                </td>
                <td>
                    <a id="btnaddcar" class="btnadd" data-item="car" style="display: none;">添加</a>
                </td>
            </tr>
            <tr>
                <td>内科检查</td>
                <td id="nk" class="person">

                </td>
                <td>
                    <a id="btnaddnk" class="btnadd" data-item="nk" style="display: none;">添加</a>
                </td>
            </tr>
            <tr>
                <td>辅助检查</td>
                <td id="fz" class="person">

                </td>
                <td>
                    <a id="btnaddfz" class="btnadd" data-item="fz" style="display: none;">添加</a>
                </td>
            </tr>
            <tr>
                <td>胸部腹部</td>
                <td id="xf" class="person">

                </td>
                <td>
                    <a id="btnaddxf" class="btnadd" data-item="xf" style="display: none;">添加</a>
                </td>
            </tr>
            <tr>
                <td>血红蛋白</td>
                <td id="matin" class="person">

                </td>
                <td>
                    <a id="btnaddmatin" class="btnadd" data-item="matin" style="display: none;">添加</a>
                </td>
            </tr>
            <!--<tr>
                <td>口腔</td>
                <td id="kouqiang" class="person">

                </td>
                <td>
                    <a id="btnaddkouqiang" class="btnadd" data-item="kouqiang" style="display: none;">添加</a>
                </td>
            </tr>-->
            <!--<tr>
                <td>眼科</td>
                <td id="yanke" class="person">

                </td>
                <td>
                    <a id="btnaddyanke" class="btnadd" data-item="yanke" style="display: none;">添加</a>
                </td>
            </tr>-->
            </tbody>
        </table>
    </section>
</div>
<script type="text/javascript" src="../layui-btkj/layui.js"></script>
<script type="text/javascript" src="../sys/jquery.js"></script>
<script type="text/javascript" src="../sys/arg.js"></script>
<script type="text/javascript" src="../plugin/zTree/js/jquery.ztree.core-3.5.js"></script>
<script type="text/javascript" src="../plugin/zTree/js/jquery.ztree.excheck-3.5.js"></script>
<script type="text/javascript" src="../plugin/zTree/js/jquery.ztree.exedit-3.5.js"></script>
<script type="text/javascript" src="../js/w_user_item.js"></script>
</body>
</html>
