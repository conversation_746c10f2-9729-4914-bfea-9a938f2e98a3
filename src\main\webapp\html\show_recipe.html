﻿<!DOCTYPE html>
<html style="overflow: hidden;">
    <head>
    <meta charset="utf-8"/>
    <title>膳食健康大数据</title>
    <link rel="stylesheet" href="../css/reset.css"/>
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../plugin/jqcloud/jqcloud.css">
    <link rel="stylesheet" href="../css/bigdata.css">
    <script type="text/javascript">
        document.write("<link rel=\"stylesheet\" id=\"linktheme\" href=\"../css/" + parent.objdata.curtheme + ".css\" type=\"text/css\" media=\"screen\"/>");
    </script>
    <!--[if lt IE 9]>
    <script src="../sys/html5shiv.min.js"></script>
    <![endif]-->
    <style>
        /*处理日期选择*/
        .wrap{
            left: 50% !important;
            margin-left: -30px;
            background-color: #ccc;
            margin-top: -30px;
        }
        .wrap,.circle,.percent{
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
        }
        .circle{
            box-sizing: border-box;
            border:10px solid #ccc;
            clip:rect(0,60px,60px,30px);
        }
        .clip-auto{
            clip:rect(auto, auto, auto, auto);
        }
        .percent{
            box-sizing: border-box;
            top:-10px;
            left:-10px;
        }
        .left{
            transition:transform ease;
            border:10px solid blue;
            clip: rect(0,30px,60px,0);
        }
        .right{
            border:10px solid blue;
            clip: rect(0,60px,60px,30px);
        }
        .wth0{
            width:0;
        }
        .num{
            position: absolute;
            box-sizing: border-box;
            width: 40px;
            height: 40px;
            line-height: 40px;
            text-align: center;
            font-size: 12px;
            left: 10px;
            top: 10px;
            border-radius: 50%;
            background-color: #fff;
            z-index: 1;
            color: #52bfef;
        }
        .sick .left,  .sick .right {
            border-color: #52bfef;
        }
        @media screen and (min-width:700px) and (max-width:1300px) {
            .wrap{
              
                left: 50% !important;
                margin-left: -25px;
                margin-top:-25px; 
                background-color: #ccc;
            }
            .wrap, .circle, .percent {
                width: 50px;
                height: 50px;
            }
            .num {
                position: absolute;
                width: 30px;
                height: 30px;
                line-height: 30px;
                font-size: 9px;
            }
            .wth0{
                width:0;
            }
        }
        /*end*/
        .yingyang {
             margin-left: 25px;
             display: none;
		}
	     .statistics{background:#030C15;}
		 body { background: #000f1d; }
</style>
    </head>
    <body>
	    <div style="height: 95px;position: absolute;width: 100%;z-index: 99;">
            <div style="width: 100%;height: 10px;background: #041425;"></div>
            <div style="margin-left: 10px;"> <div class="bigdata-top">
                <div class="bigdata-time">
                    <span style="display: inline-block;vertical-align: top;" id="spcurdatetime"><label id="headtime" style="font-size: 30px;font-weight: bold;"></label><label id="headdate"></label></span>
                </div>
                <div style="display: inline-block;">
                    <div class="bigdata-title"><label id="labcityname"></label>膳食健康大数据总览</div>>
                </div>
                <div class="bigdata-topright">
<!--                    <i class="iconfont icon_time"></i>-->
<!--                    <span style="display: inline-block;vertical-align: top;margin: 6px 25px 0 5px;" id="spcurdatetime"></span>-->
<!--                    <i id="fullScreen" class="iconfont icon_fullscreen" style="cursor: pointer;"></i>-->
<!--                    <i id="exitFullScreen" class="iconfont icon_reducescreen" style="cursor: pointer;display:none;"></i>-->
                    <img src="../images/bigdata/fullscreen.png" style="width: 21px;cursor: pointer;"  id="fullScreen">
                    <img src="../images/bigdata/exitfullscreen.png" style="width: 21px;cursor: pointer;display:none;" id="exitFullScreen">
                </div>
            </div></div>
        </div>
        <div class="bigdata-con" style="width:100%;">
          <div class="diseasedata-list" style="width: 30%;height: 100%; float: left">
            <div style="height: 44%;margin: 10px;" class="diseasedata-top">
              <h3 class="diseasedata-tit"><p><i class="report-rtit"></i><span>食谱公示指数</span></p></h3>
              <div id="divweek" style="text-align: center;">
                    <div style="display: inline-block;width: 100%;height:100%;vertical-align: top;">
                          <table style="height:100%;" class="chart-table">
                            <thead>
                                  <tr style="height:35px;">
                                    <th style="vertical-align: bottom;"><img id="btup" src="../images/arrow_up.png"></th>
                                    <th><span class="txt-label">第1周</span></th>
                                    <th><span class="txt-label">第2周</span></th>
                                    <th><span class="txt-label">第3周</span></th>
                                    <th><span class="txt-label">第4周</span></th>
                                    <th><span class="txt-label">第5周</span></th>
                                  </tr>
                            </thead>
                            <!--切换月份显示-->
                            <tbody id="pseldate">
                            </tbody>
                              <tbody>
                                  <tr>
                                      <td style="vertical-align: top;"><img id="btdown" src="../images/arrow_down.png"></td>
                                      <td></td>
                                      <td></td>
                                      <td></td>
                                      <td></td>
                                      <td></td>
                                  </tr>
                              </tbody>
                      </table>
                        </div>
                  </div>
            </div>
            <div class="diseasedata-top" style="height: 51%;margin: 10px;">
                <h3 class="diseasedata-tit"><p><i class="report-rtit"></i><span>区域幼儿进食量【ACFI值】</span></p></h3>
                <div style="clear: both;" id="radar">
                <!--  雷达图 -->
                </div>
            </div>
      </div>
          <div class="diseasedata-list" style="width: 50%;height: 100%;float: left">
        <div style="height: 53.5%;margin: 10px 0;" class="diseasedata-top">
              <div>

                <!--  <div class="ill-tit" style="width:100%;margin-right: 10px;">公示幼儿园地图展示</div>-->
		 <h3 style="position: relative;"><p><i class="report-rtit"></i><span>公示幼儿园地图展示</span> <span id="mapSeting"  style="position: absolute;right: 15px;cursor: pointer;"><img src="../images/data_set.png" style="vertical-align: middle;"><i style="color: #fff;margin-left: 5px;vertical-align: middle;">设置</i></span> </h3>


           <!-- <div style="width: 12%;display: inline-block;vertical-align: top;text-align: center;margin-top: 26px;"> <img src="../images/data_img1.png" style="width: 67px;vertical-align: bottom;"> </div>
            <div style="width: 58%;display: inline-block;vertical-align: top;float: right;"> <span class="set-txt-btn" id="mapSeting" style="cursor: pointer;"><img src="../images/data_set.png">设置</span>
                  <hr class="layui-bg-blue" style="margin-top: 41px;background-color: #014567 !important;">
                </div>-->
          </div>
              <div class="data-set"></div>
              <div id="scoremapouter" style="height: 85%;position: relative;">
            <div class="data-num" style="width: 215px;position: absolute;z-index: 10"> <span class="data-num-first">
              <label>0</label>
              </span> <span class="data-num-middle">
              <label>0</label>
              </span> <span class="data-num-middle">
              <label>·</label>
              </span> <span class="data-num-middle">
              <label>0</label>
              </span> <span class="data-num-middle">
              <label>0</label>
              </span> <span class="data-num-end">
              <label>分</label>
              </span> </div>
            <!--复选框-->
              <div class="check-equilibrium" style="z-index: 10;">
                  <p><input type="checkbox" name="junheng" value="1" class="regular-checkbox" id="check" checked="checked"/><label for="check"></label><span class="col-green">●</span><label class="sp_junheng">均衡</label></p>
                  <p><input type="checkbox" name="junheng" value="2" class="regular-checkbox"  id="check2" checked="checked"/><label for="check2"></label><span class="col-yellow">●</span><label class="sp_junheng">略微均衡</label><p>
                  <p><input type="checkbox" name="junheng" value="3" class="regular-checkbox"  id="check3" checked="checked"/><label for="check3"></label><span class="col-red">●</span><label class="sp_junheng">不均衡</label><p>
                  <p><input type="checkbox" name="junheng" value="4" class="regular-checkbox"  id="check4" checked="checked"/><label for="check4"></label><span class="col-gray">●</span><label class="sp_junheng">未上报</label><p></div>
            <div style="height: 100%" id="divmap">
            </div>
            <!--地图弹框：蓝天国际幼儿园-->
            <div class="map-box" style="position: absolute;top:0;left:0;z-index: 2;display:none;">
                  <h3>蓝天国际幼儿园</h3>
                  <p>第27周食谱营养分析报告<a>85.32</a></p>
                  <ul>
                <li class="one-span"><span>1</span><b>营养素摄入量</b><i>-4.45</i></li>
                <li class="two-span"><span>2</span><b>平均每人进食量</b><i>-4.45</i></li>
                <li class="thr-span"><span>3</span><b>配餐能量结构</b><i>-4.45</i></li>
                <li class="fou-span"><span>4</span><b>营养素摄入量</b><i>-4.45</i></li>
                <li class="fiv-span"><span>5</span><b>营养素摄入量</b><i>-4.45</i></li>
              </ul>
                  <div class="triangle-bot"></div>
                </div>
          </div>
            </div>
        <div class="diseasedata-top" style="height: 42%;margin: 10px 0;">
              <h3 style="position: relative;"><p><i class="report-rtit"></i><span>各营养素在食谱中的比例分析</span> <span id="sandainSeting" style="position: absolute;right: 15px;cursor: pointer;"><img src="../images/data_set.png" style="vertical-align: middle;"><i style="color: #fff;margin-left: 5px;">设置</i></span> </h3>
              <div class="data-set" style="margin-top: 10px;text-align: center;"> <span class="yingyang" id="is_score"><img src="../images/data_icon.png">分数</span> <span class="yingyang" id="is_cal"><img src="../images/energy.png">能量</span> <span class="yingyang" id="is_pro"><img src="../images/danbaizhi.png">蛋白质</span> <span class="yingyang" id="is_va"><img src="../images/VA.png">维生素A</span> <span class="yingyang" id="is_vb1"><img src="../images/VB1.png">维生素B1</span> <span class="yingyang" id="is_vb2"><img src="../images/V1.png">维生素B2</span> <span class="yingyang" id="is_vc"><img src="../images/VC.png">维生素C</span> <span class="yingyang" id="is_ca"><img src="../images/Ca.png">钙</span> <span class="yingyang" id="is_zn"><img src="../images/Zn.png">锌</span> <span class="yingyang" id="is_fe"><img src="../images/Fe.png">铁</span> <span class="yingyang" id="is_na2"><img src="../images/Na.png">钠</span> </div>
              <div style="clear: both;" id="sandain">
            <!-- 散点图 -->
          </div>
            </div>
      </div>
          <div class="diseasedata-list" style="width: 20%;height: 100%; float: left">
          <div class="diseasedata-top" style="margin-bottom: 15px;height: 53%;margin: 10px;">
               <h3 class="diseasedata-tit"><p><i class="report-rtit"></i><span>实时公示幼儿园</span></p></h3>
              <ul class="chart-txt chart-label" id="ulShowYey" style="height:80%;"></ul>
            </div>
          <div class="diseasedata-top" style="margin-bottom: 15px;height: 42%;margin: 10px;" id="divalleOuter">
              <h3 class="diseasedata-tit"><p><i class="report-rtit"></i><span>过敏食物标签云</span></p></h3>
              <div id="divalle" style="height: 300px"></div>
            </div>
      </div>
    </div>
    <script type="text/javascript" src="../sys/jquery.js"></script>
    <script type="text/javascript" src="../sys/function.js"></script>
    <script type="text/javascript" src="../sys/arg.js"></script>
    <script type="text/javascript" src="../layui-btkj/layui.js"></script>
    <script type="text/javascript" src="../plugin/promise/promise.js"></script>
    <script type="text/javascript" src="http://api.map.baidu.com/api?v=2.0&ak=bkpk4sh4cFcEGqgqjUG79DfRGhqNS7W8"></script>
    <script type="text/javascript" src="../plugin/echarts/echarts.min.js"></script>
    <script type="text/javascript" src="../plugin/echarts/extension/bmap.min.js"></script>
    <script type="text/javascript" src="../plugin/echarts/map/js/china.js"></script>
    <script type="text/javascript" src="../plugin/nicescroll/jquery.nicescroll.min.js"></script>
    <script type="text/javascript" src="../plugin/jqcloud/jqcloud-1.0.4.js"></script>
    <script type="text/javascript" src="../js/seldate.js"></script>
    <script type="text/javascript" src="../js/bigdata_common.js"></script>
    <script type="text/javascript" src="../js/show_recipe.js"></script>
</body>
</html>
