/* common */
.mtop15{margin-top: 15px;}
.mbottom20{margin: 20px 0 20px 20px;}
.center{text-align: center;}
.right{text-align: right;}
.pdop10{padding-top: 10px;}
.pd20{padding:20px;}
.pd10-20{padding: 10px 20px;}
.mg20{margin-left:15px;}
.my_id{text-align:center;}
.mtop15{margin-top: 15px;}
.mbot10{margin-bottom: 10px;}
.marginlt50{margin-left: 50px;}
.margin20{margin-right:20px;}
.margin5{margin-right:5px;}
.bg6{background: #fff;}/*白色*/
.greebg{background:#1da89e;}
.redbg{background:#FF0000;}
.marginleft{left: 585px;}	
/*弹框样式表头样式*/
.layui-layer-title{padding:0!important;}
/*下拉颜色*/
.layui-form-select dl dd.layui-this {background-color: #1da89e;}
/*单选框颜色*/
.layui-form-radio i:hover, .layui-form-radioed i{color:#1da89e; }
/*form*/
.custom-select select{border: 1px solid #e6e6e6;height: 34px;line-height: 34px;outline: none;}
.stat-btn{cursor:pointer;background: #F1F2F7;display: inline-block;padding: 10px 20px;margin-right: 20px;border-radius: 20px;}
/*btn*/
.opr-btn2{background: #1DA89E;color: #fff;padding: 0 25px;height: 35px;line-height: 35px;display: inline-block;border-radius: 3px;}
/*label*/
.gre-label{border: 1px solid #41CDA8;color: #41CDA8;font-size: 12px;padding: 0 2px;}
.ora-label{font-size: 12px;border:1px solid #FEAD77;color: #FEAD77;padding: 0 2px;}
.oran-label{background: #FF8C40;color: #fff;display: inline-block;width: 16px;height: 16px;line-height: 16px;text-align: center;font-size: 12px;margin-left: 20px;}
/*txt*/
.gray-txt{color: #454545;}
.pink-txt{color: #F58FED;}
.oran-txt{color: #FE8652;}
.green-txt{color: #1AA094;}
.green-txt1{ color: #1dc499;}
.green-txt2{color: #5FB878;}  
.green-txt3{color: #19bb72;}
.green-txt4{color: #76d571;}
.green-txt5{color:#A1DF5E}
.green-txt6{color:#00ac9f}
.gray-txt{color: #999999}
.gray-txt2{color:#666;}
.pink-txt{ color: #f97fb3;}
.pink-tit{color:#f49090; }
.red-txt{color: #ea3b3b;}
.yellow-txt{color:#ff7800}
.blue-txt,.layui-table td.blue-txt{color: #49c1ff;}
.bluelt-txt{color:#34495e;}
.redbtn{
	background:#ff0d0d;
    height: 30px;
    line-height: 30px;
	border-radius:3px; 
	text-align:center; color:#fff; 
	font-size:14px; display:inline-block; 
	padding: 0 10px; }
.bluebtn{
	background:#19a6ff;
    height: 30px;
    line-height: 30px;
	border-radius:3px; 
	text-align:center; color:#fff; 
	font-size:14px; display:inline-block; 
	padding: 0 10px; }
.legreenbtn{
	background:#1fc872;
    height: 30px;
    line-height: 30px;
	border-radius:3px; 
	text-align:center; color:#fff; 
	font-size:14px; display:inline-block; 
	padding: 0 10px; }
/*flex布局写法*/
.drink-weui-flex{align-items:center;
	-webkit-align-items:center;
	box-align:center;
	-moz-box-align:center;
	-webkit-box-align:center;
	display:-webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;}
/*提示框三角*/
.triangle-mark{
    width:0;
    height:0px;
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
    border-left:16px solid transparent;
    border-right: 6px solid #ffffff;
    position:absolute;			    
    transform:rotate(90deg);
    -ms-transform:rotate(90deg); 	/* IE 9 */
    -moz-transform:rotate(90deg); 	/* Firefox */
    -webkit-transform:rotate(90deg); /* Safari 和 Chrome */
    -o-transform:rotate(90deg); 	/* Opera */
}
/*公用的流程记录*/
.layui-record-con{margin: 20px 15px 0 45px;padding: 0 10px 20px 0;height: 100%;}
.layui-record-list{position: relative;padding: 0px 0 14px 35px;min-height: 30px;}
.layui-record-icon{width: 36px;height: 36px;position: absolute;left: -20px;top: 0;border-radius: 50%; text-align: center;line-height: 36px; color: #ffffff;}    
.layui-record-list h5{font-size: 16px;color: #333333; margin: 0; font-weight: normal;}
.layui-record-list p{font-size: 14px;color: #999999;line-height: 22px;}
/*用layui的页面通用样式*/
.layui-page{ margin: 0 auto;}
.layui-page .layui-page-hearder{border-bottom: 1px solid #eeeeee;padding: 0 15px; background: #ffffff;border-top: 1px solid #eeeeee;}
.layui-page .layui-page-hearder .layui-form-item{display: inline-block;margin: 10px 15px 8px 0;vertical-align: middle;}
.layui-page .layui-page-hearder .layui-form-label{width: auto;padding: 7px 5px;}
.layui-page .layui-page-hearder .layui-input-inline input{height: 35px;display: inline-block;}
.layui-page .layui-page-hearder .layui-form-item .layui-input-inline{width: auto;}
.layui-page .layui-page-hearder select{height: 35px;border-color: #e6e6e6!important;width: inherit;}
.layui-page .layui-page-body{position: relative;line-height: 24px;background: #ffffff; margin-top: 8px;}
.layui-page .layui-page-body .layui-page-body-title{height: 48px;border-bottom: 1px solid #eeeeee;padding: 0px 15px;font-size: 16px;color: #333333;}
.layui-page .layui-page-body .layui-page-title-left{float: left;line-height: 48px;}
.layui-page .layui-page-body .layui-page-btn{float: right;margin-top:5px; padding-bottom: 5px;}
.layui-page .layui-page-body-table{margin-top: 10px;}
.layui-page .layui-page-body-table .layui-table a{padding: 0 10px;}
/*用layui的添加页面通用样式*/
.layui-page-form .layui-form-item .layui-input-inline{min-width: 310px;width: auto;}	    	
.layui-page-form .layui-form-label{width: 120px;padding: 9px 5px 0 20px;}
.layui-page-form .layui-form-item{margin-bottom: 10px;}
.layui-page-form .layui-input-block{margin-left: 145px;}
.layui-page-form .opr-btn{padding: 0 10px;}
.layui-page .layui-tab-brief{background: #ffffff;}
/*不用用layui的页面通用样式*/
.tb-page{position: relative; margin: 0 auto;}
.tb-page .tb-page-hearder{border-bottom: 1px solid #f6f6f6;padding: 0 15px; background: #ffffff;}
.tb-page .tb-page-body{position: relative;line-height: 24px;background: #ffffff; margin-top: 8px;}
/*新版日历*/
.layui-comselect .layui-alendar{width: auto;height: auto;right: 10px;position: absolute; top:5px;cursor: pointer;}
/*自定义搜索框*/
.def-search{margin: 12px;height: 36px;border: 1px solid #d8d8d8;box-sizing: border-box;border-radius: 2px;background: #ffffff;}
.def-search i{float: left;margin: 0 5px 0 8px;height: 34px;line-height: 34px;}
.def-search label{overflow: hidden;display: block;}
.def-search label input{height: 34px;box-sizing: border-box;border: none;width: 96%;}	
/*复选框*/
.contact-checkbox input[type="checkbox"]{-webkit-appearance: none;width: 24px;height: 24px;background: url(../images/drinking/sel_checkimg.png) 50%;background-size:24px 24px;outline: none;}
.contact-checkbox input[type="checkbox"]:checked{width: 24px;height: 24px;background: url(../images/drinking/sel_checkimg_HL.png) 50%;background-size:24px 24px;outline: none;} 	
/*树形结构*/
.line-dashed{padding: 0!important;margin-left: 13px!important;height: 25px;line-height: 25px;}
.line-dashed .ri-line{vertical-align: top;margin-top: 3px;margin-right: 5px;}
.line-dashed .gre-label{margin-left: 7px;}
/*文字溢出显示省略号*/
.text-elli{display:inline-block;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;}
.txt-elli{overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-box-orient: vertical;}
/*flex布局*/
.layout-flex{display: -webkit-box;display: -webkit-flex;display: flex;}  
.layout-flex__item{-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;}
/*btns*/
a:hover{-webkit-transition:200ms all;transition:200ms all;}
.btn-head{color:#484a4a;}
.btn-login{font-size: 30px;background: #fdaa2e;height: 60px;line-height: 60px;text-align: center;}
.btn-login span{width:100%;height:100%;}
.btn-login .anime-text{z-index: 2;}
.btn-login .anime-back{z-index: 1; left:0;top: 0;
	-webkit-transform:scaleX(0) translateZ(0); 
	-ms-transform:scaleX(0) translateZ(0); 
	transform:scaleX(0) translateZ(0); 
	-webkit-transition:800ms all;
	-ms-transition:800ms all;
	transition:800ms all;}
.btn-login:hover .anime-back{background: #fec36c; 
	-webkit-transform:scaleX(1) translateZ(0);
	transform:scaleX(1) translateZ(0);}
.btn-big{height: 40px;line-height: 40px;color: #fff;display: inline-block;border-radius: 3px;}
.btn-black{display: inline-block; line-height: 28px;padding:0 25px;position: relative;color:#fff;}
.btn-control{border-radius: 8px; width: 64px;height: 32px; line-height: 32px;display:inline-block;text-align: center; background: #322e2b;border:2px solid #000;color:#fff;}/*遥控器*/
.btn-border{display: inline-block;padding:0 15px;line-height: 26px;margin-left: 10px;font-size: 14px;}
.btn-border:hover{color: #fff;}
.dateInput{background: url(../images/date.png) no-repeat center right;padding-right:28px;width: 123px;}
.layui-layer-btn a.layui-layer-btn1{background:#ddd;color:#363f46;border-color:#c3c3c3;}
.fullbtn{background: #00c461;color: #ffffff;display: inline-block;font-size: 12px;height: 18px;line-height: 18px;border-radius: 20px;padding: 0 10px;}
/*btn-head*/
.btn-head{width: 24px;height: 24px; display: inline-block;border-radius: 3px;text-align: center;font-weight: bold;margin-left: 10px;}
.btns-foot{position:fixed;bottom:10px;left:0;width:100%;text-align: center;}
.cir-label-left{background: #DEE3EE;color:#879AAE;position: absolute;padding: 5px 12px;border-radius:20px;line-height: normal; right: 10px}
.cir-label-left.overtxt{background: #EFEFEF;color:#ADADAD;position: absolute;padding: 5px 12px;border-radius:20px;line-height: normal; right: 10px}
.cir-label-right{background: #FF5D5E;color: #fff;position: absolute;padding: 5px 12px;border-radius:20px 0 0 20px;right: 0;top: 0;line-height: normal;}
/*vip登陆页面*/
.contain_cen{ width: 1220px; margin: 100px auto 0 auto; overflow:hidden;}
.logo-left{width:615px;} 
.logo-right{width:600px;} 
.logo-tit{position: relative; padding: 20px 10px;}
.logo-tit label{ display: inllogo-btitine-block; position: absolute; top: 8px;left: 3px;}
.logo-tit .logoimg{ border: 1px solid #FFD55D; border-radius: 50px; }
.logo-left p{font-size: 22px; color:#FFD55D ; font-family: "微软雅黑";  margin-top: 25px; position: relative;}
.logo-left p::after{position: absolute; bottom: 0; background:#FFD55D; height:3px; border-bottom-left-radius: 3px; border-bottom-right-radius: 3px;}
.logo-btit{clear: both; padding-top: 10px;color: #ffd55d; margin-left: 10px;}
.logo-btit p.logo-top{  font-size:30px; border-bottom: 1px solid #d5884c; padding-bottom: 5px}
.logo-btit p.logo-bot{margin-top: 5px; font-size:24px;}
.logo-left u,.line-left u,.open-btn u,.contain_cen u{display:inline-block;background:url(../images/login/icon.png) no-repeat; vertical-align: middle; text-decoration: none;}
.logo-left span{ width: 55px; display: block; margin-top: 20px;}
.logo-left u.login_icon1{background-position:0px 0px;width: 55px;height: 43px;}
.logo-left u.login_icon2{background-position:0px -50px;width: 55px;height: 43px;}
.logo-left u.login_icon3{background-position:0px -100px;width: 55px;height: 43px;}
.logo-left u.login_icon4{background-position:0px -150px;width: 55px;height: 43px;}
.logo-left u.login_icon5{background-position:0px -200px;width: 55px;height: 43px;}
.logo-left u.login_icon6{background-position:0px -250px;width: 55px;height: 43px;}
.logo-left u.login_icon7{background-position:0px -300px;width: 55px;height: 43px;}
.logo-left u.login_icon8{background-position:0px -350px;width: 55px;height: 43px;}
.line-left u.login_icon9{background-position:-137px 0px;width: 18px;height: 230px;}
.open-btn u.login_icon10{background-position:-68px -299px;width: 58px;height: 43px;}
.contain_cen u.login_icon11{background-position: -90px -42px;width:15px;height: 20px;}
.contain_cen u.login_icon12{background-position: -83px -14px;width:22px;height: 22px;}
.open-btn{ background:#2f2c26; width:248px; height: 70px; border: 1px solid #f4d080; border-radius: 40px; line-height: 70px; text-align: center; font-size: 24px; color: #ffd55d; float: right;}
.login-rightbg{background:url(../images/login/logincen_bg.png) no-repeat; width: 600px; height: 378px; margin-top: 226px;}
.loginright-tit{ width: 600px; color: #2f2c26;font-size:18px; border-bottom: 1px solid #d9b766;line-height:50px; text-align:center; height:50px; margin-bottom:10px;}
.loginright-tit img{vertical-align: middle; margin-right: 10px;}
.input-tit{ clear:both; margin:0px 0px 20px 0px; border:1px solid #D5C474; border-radius:4px; height:45px; line-height:45px; vertical-align:middle;}
.input-tit input{width:100%; height:44px; line-height:44px; background: #fff; /*color:#cccccc;*/ font-size:18px}
.btn-login{  margin:20px 0; width:311px; height:55px; cursor:pointer; display:block; background:url(../images/login/login-btn.png) no-repeat}
.code-main{padding: 25px 0 20px 0;position: relative; margin: 30px 0px 0 10px;}
.code-cen{ padding:0 10px;}
.code-cen .bor-span1{position:absolute;top:10px;left:8px;border-top:3px solid rgba(223,193,142,0.9);border-left:3px solid rgba(223,193,142,0.9);width: 22px;height: 22px;display: inline-block;}
.code-cen .bor-span4{position:absolute;top:10px;right:8px;border-top:3px solid rgba(212,179,117,0.9);border-right:3px solid rgba(212,179,117,0.9);width: 22px;height: 22px;display: inline-block;}
.code-cen .bor-span2{position:absolute;bottom:10px;right:8px;border-bottom:3px solid rgba(212,179,117,0.9);border-right:3px solid rgba(212,179,117,0.9);width: 22px;height: 22px;display: inline-block;}
.code-cen .bor-span3{position:absolute;bottom:200px;left:8px;border-left:3px solid rgba(212,179,117,0.9);border-bottom:3px solid rgba(212,179,117,0.9);width: 22px;height: 22px;display: inline-block;}
.scan-tit{position: absolute;bottom: -3px;text-align: center; width: 100%;font-size: 12px;color: #2f2c26;}
.vip-mian{ width: 500px;width: 800px;margin:0px auto;}
.vip-tit{ text-align: center; font-size:60px; color: #ffffff; line-height: 60px; font-weight: bold;}
.vip-mian i{ background:#FFD55D;border-radius: 10px; height: 5px; width:60px; margin: 20px auto; display: block;}
.vip-left{display: block;}
.vip-left u{ margin-top: 10px; display: block;}
.vip-bottom{ margin-top: 20px; border-bottom: 1px dotted #696242; overflow: hidden;}
.vip-bottom p{ font-size: 30px; font-weight: bold; line-height: 40px; color: #fff;}
.vip-bottom p:before {background: #FFD55D;content: "";display: inline-block;width: 5px;height: 24px;margin: 10px 10px -2px 0px;border-bottom-left-radius: 3px; border-bottom-right-radius: 3px; border-top-left-radius: 3px;border-top-right-radius: 3px;}
.vip-bottom li{ float: left; font-size:18px; color: #ffffff; margin:20px 10px 20px 10px;}
.vip-btn{margin: 30px auto; text-align: center; width:240px; position: relative;}
.vip-btn img{position: absolute; top:-8px; left: 0px;}
.vip-btn p{background: -webkit-linear-gradient(left, #f8db86 , #D8AB55);
background: -o-linear-gradient(right, #f8db86, #D8AB55);
background: -moz-linear-gradient(right, #f8db86, #D8AB55); /* Firefox 3.6 - 15 */
background: linear-gradient(to right, #f8db86 , #D8AB55); width: 100%; border-radius: 43px; display: block; height:83px; text-align: center; color:#2f2c26; font-size: 24px; line-height: 83px; }
/*tab*/
.tab li{float: left;color:#333; text-align: center;width: 100px;border:1px solid #000;margin-left: -1px; height: 30px;line-height: 30px; cursor: pointer;font-size: 14px;background: #fff;}
/*通用切换*/
.common-tab{height: 50px;display: inline-block;vertical-align: top;font-size: 0;}
.common-tab li:first-child{border-left: 1px solid #eeeeee;}
.common-tab li{cursor: pointer;display: inline-block;height: 50px;line-height: 50px;min-width: 130px;text-align: center;border-right: 1px solid #eeeeee;position: relative;font-size: 14px;color: #666666;padding: 0 10px;}
.common-tab li.current{background: #f2f2f2;}
.common-tab li.current:before{content: "";position: absolute;top: -1px;left: 0;width: 100%;border-top: 4px solid #01a39a;}
.common-tab li img{margin-right: 5px;vertical-align: top;margin-top: 13px;}	    	
.common-tab.tabheight{height: 42px;line-height: 42px;}
.common-tab.tabheight li{height: 42px;line-height: 42px;}
.common-tab.tabheight li.current{color: #01a39a;}
/*自定义多选框*/
.com-checkbox input[type="checkbox"]{-webkit-appearance: none;width: 14px;height: 14px;background: #ffffff;border-radius: 3px;display: inline-block;border: 1px solid #d2d2d2;outline: none;}
.com-checkbox input[type="checkbox"]:checked{-webkit-appearance: none;width: 14px;height: 14px;background: #ffffff url(../images/education/checkbox_bluebg.png) no-repeat;background-size: 14px 14px;border: none;border-radius: 3px;display: inline-block;position: relative;} 

/*通用切换2*/
.common-tab2.layui-tab-title li{position: relative;width: 100px;height: 32px;margin-top: 4px;line-height: 34px;}	    	
.common-tab2.layui-tab-title li:before{content: "";position: absolute;left: 0;border-left: 1px solid #e2e2e2;height: 100%;}
.common-tab2.layui-tab-title li:first-child:before{content: none;}
.common-tab2.layui-tab-title .layui-this{background: #f2f2f2;height: 37px;vertical-align: top;}
.common-tab2.layui-tab-title .layui-this:after {
    position: absolute;
    left: 0;
    top: 0;
    content: '';
    width: 131px;
    height: 37px;
    border: 1px solid #e2e2e2;
    border-bottom-color: #f2f2f2;
    border-radius: 2px 2px 0 0;
    box-sizing: border-box;
    pointer-events: none;
    border-top: 4px solid #01a39a;
}
.common-tab3{font-size: 0;}
.common-tab3 li{display: inline-block;font-size: 14px;width: 25%;}	
.common-tab3 li a{padding: 2px 0;}
.common-tab3 li.pinktxt a{color: #fd9d9d;}
.common-tab4{border: 1px solid #53c2bc;display: inline-block;border-radius: 2px;color: #53c2bc;font-size: 0;}
.common-tab4 li{height: 30px;line-height: 30px;min-width: 130px;width: auto;font-size: 14px;display: inline-block;padding: 0 10px;text-align: center;border-right: 1px solid #53c2bc;} 
.common-tab4 li:last-child{border-right: none;}
.common-tab4 li.current{color: #ffffff;background: #53c2bc;} 
.common-tab5{border-bottom: 1px solid #dddddd;padding: 0 22px;}
.common-tab5 li{display: inline-block;min-width: 98px;cursor: pointer;height: 36px;line-height: 36px;text-align: center;background: #ededed;color: #666666;font-size: 16px;border-radius: 8px 8px 0 0;padding: 0 20px;margin: 0 1px;}
.common-tab5 li.current{background: #1da89e;color: #ffffff;font-weight: bold;}	 
/*按钮*/
.del-txt{border: 1px solid #DEDEDE; color: #818181; font-size: 12px; background: #fff; padding: 0 5px;margin-right: 10px; cursor: pointer;}
.edit-txt{border: 1px solid #5F9DE6; color: #5F9DE6; font-size: 12px;background:#fff; padding: 0 5px;cursor: pointer;}
/*遮罩*/
.shadow-box{background: rgba(0,0,0,0.3);width: 100%;height: 100%;position: absolute;top: 0;bottom: 0;left: 0;right: 0;z-index: 9;}
/*选择类型li*/
.type-label li{display: inline-block;height: 26px;line-height: 26px;background: #ffffff;padding: 0 10px;cursor: pointer;}
.type-label li.current{background: #4cd2c9;color: #ffffff;}
/*搜索框*/
.search-div{border: 1px solid #d8d8d8;height: 26px;border-radius: 3px;background: #ffffff;text-align: left;}		
.search-div input[type="text"]{vertical-align: top;border: none;height: 26px;margin-left: -8px;}
.search-div i.iconfont{vertical-align: top;width: 30px;height: 26px;line-height: 26px;display: inline-block;text-align: center;}
/*加减按钮*/
.opr-inp{font-size: 0;border: 1px solid #1fc872;display: inline-block;vertical-align: middle;width: 77px;}
.opr-inp input[type="button"]{-webkit-appearance:none;outline:none;width: 20px;height: 22px;border-radius: 0;background: #1fc872;border: none;color: #ffffff;font-size: 20px;line-height: 22px;cursor: pointer;}
.opr-inp input[type="text"]{-webkit-appearance:none;outline:none;width: 34px;height: 22px;border-radius: 0;border: none;color: #999999;font-size: 13px;text-align: center;}
/*input*/
input,select{height: 24px;line-height: 24px;border:1px solid #363f46; background: none;border-radius: 2px; +vertical-align: middle;padding-left:3px;}
select{height: 26px;}
textarea{border:1px solid #363f46;border-radius: 2px;vertical-align: middle; padding:2px 3px;}
i{font-style: normal;}
/*form*/
.formList{overflow: hidden;font-size: 14px;word-break:break-all;}
.formList label:first-child{display: inline-block; text-align: right;width: 160px;line-height: 26px;}
.formList div{margin-top: 8px;}
.formList input[type="text"]{width: 150px;}
.formList input[type="radio"]{height: auto; vertical-align: -1px;}
.formList .btn-black{margin-right: 6px;}
.formList label.error{color: #f00;}
/*用户头像信息*/
.userinfo{padding: 16px 0;}
.userinfo dt{display: inline-block;vertical-align: top;}
.userinfo dd{display: inline-block;padding-top: 5px;}
/*数字加减输入框*/
.opr-num{background: #fff;border: 1px solid #ddd;display: inline-block;margin: 0 5px}
.opr-num input[type="button"]{width: 22px;height: 22px;line-height:22px;background: #fff;border: none;padding: 0;cursor: pointer;}
.opr-num input[type="text"]{width: 34px;height: 22px;border: none;border-left:1px solid #ddd;border-right: 1px solid #ddd;text-align: center}
/*!*tree*!
.ztree *{font-family: 'microsoft yahei';}
.treeWrap{width: 240px;height: 100%;overflow: auto;left: 0;top:0;}
.ztree li{line-height: 26px;}
.ztree li a{height: 26px;padding-left: 5px;}
.ztree li a.curSelectedNode{height: 26px; !*line-height: 26px;*!padding:1px 3px 0 5px; font-weight: normal;}
.ztree li span{line-height: 26px;}
.ztree li span.button.ico_open,.ztree li span.button.ico_docu,.ztree li span.button.ico_close,.ztree li span.button.bottom_open,.ztree li span.button.bottom_close,.ztree li span.button.root_open,.ztree li span.button.center_open,.ztree li span.button.center_close,.ztree li span.button.root_close,.ztree li span.button.add,.ztree li span.button.edit,.ztree li span.button.remove{background: url("../images/newzTree.png") no-repeat; vertical-align: middle; +vertical-align: -5px;}
.ztree li span.button.ico_open{background-position: 0 0; width: 25px;height: 20px;}
.ztree li span.button.ico_close{background-position: -25px 0; width: 25px;height: 22px;}
.ztree li span.button.ico_docu{background-position: -50px 0; width: 20px;height: 20px; +height: 22px;}
.ztree li span.button.bottom_open,.ztree li span.button.center_open{background-position: -75px 0; width: 18px;height: 18px;}!*实心*!
.ztree li span.button.bottom_close,.ztree li span.button.center_close{background-position: -100px 0; width: 18px;height: 18px;}!*空*!
.ztree li span.button.root_open{background-position: -75px 0; width: 18px;height: 18px;}
.ztree li span.button.root_close{background-position: -100px 0; width: 18px;height: 18px;}
.ztree li span.button.add{background-position:-25px -25px;}
.ztree li span.button.edit{background-position:-50px -25px;}
.ztree li span.button.remove{background-position:0 -25px;}
.ztree li span.button.add,.ztree li span.button.edit,.ztree li span.button.remove{width: 20px;height: 20px;margin-left: 5px;}
.ztree li span.button.center_docu,.ztree li span.button.bottom_docu{visibility: hidden;}
.ztree li ul.line{background:none;}
.ztree li a input.rename{line-height: 16px; vertical-align: text-top;}
!*.ztree li span.button.chk.radio_false_full,.ztree li span.button.chk.radio_true_full,.ztree li span.button.chk.radio_false_full_focus,.ztree li span.button.chk.radio_true_full_focus,.ztree li span.button.chk.radio_false_part,.ztree li span.button.chk.radio_false_part_focus{+vertical-align: -3px;}*!
.ztree li span.button{+vertical-align: -3px;}*/
/*page*/
.dataTables_wrapper .dataTables_paginate .paginate_button{padding:0;margin-left: 5px;}
.dataTables_wrapper .dataTables_paginate{padding-top: 0.775em;}
/*表格*/
.def-layui-table{margin: 0;background: #faf8f8;width: 100%;}	
.def-layui-table td, .def-layui-table th{padding: 9px 15px;min-height: 20px;line-height: 20px;font-size: 14px;}
.def-layui-table td, .def-layui-table th{border: 1px solid #e2e2e2;}
/*iconfont*/
.def-icon .iconfont{display: inline-block;vertical-align: middle;}
.def-icon .icon-text{display: inline-block;vertical-align: top;}
/*icon*/
u{display:inline-block;background:url(../images/icon.png) no-repeat; vertical-align: middle; text-decoration: none;}
/*.icon1{background-position:0 0;width: 32px;height: 32px;margin-right: 10px; cursor: pointer;}*/
/*.icon2{background-position:-50px 0;}*/
.icon3{background-position:-50px -50px;width: 20px;height: 20px;}
.icon4{background-position:-100px -50px;width: 20px;height: 20px;}
.icon5{background-position:-150px -50px;width: 25px;height: 25px;cursor: pointer;}
.icon6{background-position:-200px -50px;width: 14px;height: 14px;margin-right: 3px;}
.icon7,.icon8,.icon9,.icon10,.icon11{width: 34px;height: 34px;position: absolute;}
.icon7{background-position:-150px 0;}
.icon8{background-position:-200px 0;}
.icon9{background-position:-250px 0;}
.icon10{background-position:-300px 0;}
.icon11{background-position:-350px -150px;cursor: pointer;top:5px;right:5px;width: 12px;height: 12px;}/*delete*/
.icon12{background-position:-350px -50px;width: 18px;height: 24px;margin-right: 5px;}/*map*/
.icon13{background-position:-300px -50px;width: 20px;height: 20px;margin-right: 5px;}/*clock*/
.icon14{background-position:-400px -50px;width: 20px;height: 20px;margin-right: 5px;}/*color clock*/
.icon15{background-position:-100px -75px;width: 25px;height: 25px;margin-left: 10px;}/*read*/
.icon16,.icon17{width: 22px;height: 22px;margin-left:10px;cursor: pointer;}
.icon16{background-position:-100px -125px;}/*delete*/
.icon17{background-position:-400px -75px;}/*cancel*/
.icon18{background-position:-350px 0;width: 21px;height: 21px;}/*ok*/
.icon19{background-position:-400px 0;width: 33px;height: 33px;}
.icon20,.icon21{width: 20px;height: 20px; right: 28px;top: -1px; }
.icon20{background-position:-50px -50px;}/*删除*/
.icon21{background-position:-300px -75px;}/*增加*/
.icon22{background-position:-250px -50px; width: 22px;height: 22px;top:5px;right:5px;}/*search*/
.icon23{background-position:-100px -150px; width: 25px;height: 25px;top:9px;right:30px;}/*add gray*/
.icon24{background-position:-50px -50px; width: 22px;height: 22px;top:11px;right:0;}
.icon25{background-position:-300px -125px;}/*禁用*/
.icon26{background-position:-250px -125px;}/*恢复禁用*/
.icon27{background-position:0 -150px;}/*上移*/
.icon28{background-position:-50px -150px;}/*下移*/
.icon29{background-position:-250px -150px;}/*不能上移*/
.icon30{background-position:-300px -150px;}/*不能下移*/
.icon31{background-position:-400px -125px; height: 25px;
    width: 25px;-webkit-transition:all 1s;transition:all 1s; cursor: pointer;}/*折叠1*/
.current .icon31{background-position:-350px -125px; }/*折叠2*/
.icon32{background-position:-350px -75px;width: 25px;height: 35px;margin-left: 10px;}/*map*/
.icon33{background-position:0 -175px;width: 25px;height: 25px;}
.icon34{background-position:-50px -175px;width: 25px;height: 25px;}
.icon35{background-position:-100px -175px;width: 25px;height: 25px;top:8px;right:2px; cursor: pointer;}/*white close*/
.icon36{background-position:-400px -150px;width:34px;height:17px;}/*msg*/
.icon37{background-position:-350px -175px;}/*control up*/
.icon38{background-position:-300px -175px;}/*control down*/
.icon39{background-position:-150px -175px;}/*control home*/
.icon40{background-position:-200px -175px;}/*control return*/
.icon37,.icon38{+margin-top:8px;}
.icon39,.icon40{+margin-top:4px;}
.icon41{background-position:-250px -175px;right: 6px; top: 8px;}/*control delete*/
.icon42{background-position:-400px -175px;}/*control tel*/
.icon43{background-position:0 -200px;}/*control notel*/
.icon42,.icon43{+margin-top: 10px;}
.icon37,.icon38,.icon39,.icon40,.icon41,.icon42,.icon43{width: 25px;height: 25px;}
.icon44{background-position:-375px -250px;}/*sheng*/
.icon44-off{background-position:-125px -250px;}/*jiang*/
.icon45{background-position:-250px -250px;}/*on*/
.icon45-off{background-position:0 -250px;}/*off*/
/*hykz*/
.icon46{background-position:-150px -475px;}
.icon47{background-position:-200px -475px;}
.icon48{background-position:-250px -475px;}
.icon49{background-position:-300px -475px;}
.icon50{background-position:-50px -200px;}
.icon50-off{background-position:-450px 0;}
.icon51{background-position:-100px -200px;}
.icon51-off{background-position:-450px -50px;}
.icon52{background-position:-150px -200px;}
.icon52-off{background-position:-450px -100px;}
.icon53{background-position:-200px -200px;}
.icon53-off{background-position:-450px -150px;}
.icon54{background-position:-250px -200px;}
.icon54-off{background-position:-300px -200px;}
.icon55{background-position:0 -375px;}
.icon55-m {background-position:-50px -375px;}
.icon55-off{background-position:-100px -375px;}
.icon56{background-position:-150px -375px;}
.icon56-m {background-position:-200px -375px;}
.icon56-off{background-position:-250px -375px;}
.icon57{background-position:-300px -375px;}
.icon57-m {background-position:-350px -375px;}
.icon57-off{background-position:-400px -375px;}
.icon58{background-position:-450px -375px;}
.icon58-m {background-position:0 -425px;}
.icon58-off{background-position:-50px -425px;}
.icon59 {background-position:-100px -425px;}
.icon59-m {background-position:-150px -425px;}
.icon59-off{background-position:-198px -425px;}
.icon60{background-position:-250px -425px;}
.icon61{background-position:-300px -425px;}/*字幕*/
.icon62{background-position:-350px -425px;}/*分屏*/
.icon63{background-position:-400px -425px;}/*退出 */
.icon64{background-position:-450px -425px;}
.icon64-off{background-position:-50px -475px;}
.icon65{background-position:0 -475px;}
.icon65-off{background-position:-100px -475px;}
.icon66{background-position:-350px -200px;}
.icon67{background-position:-400px -200px;}
.icon68{background-position:-475px -500px;width: 25px;height: 25px;margin-left: 10px;}/*未读*/
.icon69{background-position:-482px 0;width: 20px;height: 20px;left: 0;top:0;}
.icon70{background-position:-150px -100px;}
.icon70-off{background-position:-100px -100px;}
.icon71{background-position:-200px -525px;}
.icon71-sel{background-position:-250px -525px;}
.icon72{background-position:-350px -475px;}
.icon73{background-position:0 -575px;}
.icon74{background-position:-75px -575px;}
.icon75{background-position:-150px -575px;}
.icon76{background-position:-225px -575px;}
.icon77{background-position:-300px -575px;}
.icon78{background-position:-375px -575px;}
.icon79{background-position:0 -650px;}
.icon80{background-position:-75px -650px;}
.icon81{background-position:-150px -650px;left: 15px;}
.icon82{background-position:-200px -650px;right: 15px;}
.icon83{background-position:-375px -525px;width: 44px;height: 34px;right: 0;bottom:-40px; cursor: pointer;}
.skinIcon1,.skinIcon2{width: 25px;height: 25px;margin-left: 20px;cursor: pointer;}
.skinIcon1{background-position:-100px -100px;}
.skinIcon2{background-position:-200px -100px;}
.skinIcon1:hover{background-position:-150px -100px;}
.skinIcon2:hover{background-position:-300px -50px;}
.skinIcon3{background-position:-250px -100px;width: 22px;height: 22px;}
.skinIcon3:hover{background-position:-300px -100px;}
.skinIcon4{background-position:-450px 0;width: 24px;height: 20px;}
.skinIcon4:hover{background-position:-450px -50px;}
.skinIcon5{background-position:-250px 0;width:25px;height:25px;}/*保存*/
.skinIcon5:hover,.skinIcon6:hover{background-position:-250px -50px;}
.skinIcon6{background-position:-400px -100px;width: 25px;height: 25px; cursor: pointer; margin-left: 20px;}/*black保存*/
.skinIcon7{background-position:-250px -150px;}
.skinIcon7-sel{background-position:-200px -150px;}
.skinIcon8{background-position:-325px -150px;}
.skinIcon8-sel{background-position:-375px -150px;}
.skinIcon7,.skinIcon7-sel,.skinIcon8,.skinIcon8-sel{width: 33px;height: 29px; cursor: pointer;position: absolute;top:7px;right:5px;}
.skinIcon9,.skinIcon10{width: 32px;height: 32px;right:-45px;}
.skinIcon9{background-position:0 -350px;top:0;}
.skinIcon9:hover{background-position:-50px -350px;}
.skinIcon10{background-position:-100px -350px;bottom: 0;}
.skinIcon10:hover{background-position:-150px -350px;}
.skinIcon-up,.skinIcon-down{width: 110px;height: 112px;display: block; margin: 45px auto 0;}
.skinIcon-up{background-position:0 -400px;}
.skinIcon-down{background-position:-125px -400px;}
.skinIcon-camera,.skinIcon-camera-on{width: 81px;height: 37px;cursor: pointer;}
.skinIcon-camera{background-position:-400px -325px;}
.skinIcon-camera-on{background-position:-400px -375px;}
.skinIcon-middle-on,.skinIcon-middle{width: 225px;height: 171px;}
.skinIcon-middle{background-position:0 -550px;}
.skinIcon-middle-on{background-position:-250px -550px;}
.skinIcon-shadow-on,.skinIcon-shadow{width: 125px;height: 92px;display: block; margin: 50px auto 0;}
.skinIcon-shadow{background-position:-250px -350px;}
.skinIcon-shadow-on{background-position:-250px -450px;}
.rankList .icon51,.rankList .icon52,.rankList .icon51-off,.rankList .icon52-off{width: 27px;height: 25px;right: -43px;top:44px;}
.skinIcon-top,.skinIcon-bottom{width: 84px;height: 30px;left:30px;}
.skinIcon-left,.skinIcon-right{width: 30px;height: 84px;top:30px;}
.skinIcon-top{background-position:0 -250px;top:0;}
.skinIcon-top:hover{background-position:0 -300px;}
.skinIcon-bottom{background-position:-100px -250px;bottom: 0;}
.skinIcon-bottom:hover{background-position:-100px -300px;}
.skinIcon-left{background-position:-200px -250px;left: 0;}
.skinIcon-left:hover{background-position:-250px -250px;}
.skinIcon-right{background-position:-300px -250px;right: 0;}
.skinIcon-right:hover{background-position:-350px -250px;}
.skinIcon-center{background-position:-400px -250px;width: 60px;height: 60px;top:42px;left: 42px;}
.skinIcon-center:hover{background-position:-425px -125px;}
.arrowPre{background-position:0 -75px;}
.arrowNext{background-position:-50px -75px;}

/*contentRight*/
.contentRight{padding:4px 4px 0 4px; width: auto;margin-left: 280px;}
iframe{width:100%;border:0;background-color:#fff;}
.popRight{margin-left: 250px;}

/*timezone*/
.timezone{top:0;left:0;z-index:9999;border:1px solid #ccc;box-shadow: 0 0 8px #aaa;}
.timezone li{position: relative; line-height: 30px;padding:0 3px; cursor: pointer;}
.timezone li:hover{background: #ccc;}
.timezone li .timestr{margin-right: 5px;}


/*字母列表*/
.innerTitle{font-size: 16px;line-height: 44px;border-top: 1px solid #fff;padding:0 20px;}
.letterList{margin-top: 10px;}
.letterList li{float: left;cursor: pointer;padding:2px 7px;line-height: 26px;}

/*数据字典*/
.otx-search div{margin-right: 20px; display: inline-block; +display: inline;zoom:1;font-size: 14px;}
.dic-head{padding:20px 20px 10px;}
.dic-list dl{padding: 10px;}
.dic-list dl:nth-of-type(2n){background: #f7f8fa;}
.dic-list dt{float: left;margin-right: 25px;overflow: hidden;}
.dic-list dd{overflow: hidden;}
.dic-list u{cursor: pointer;}
.dic-list .icon4{margin:0 15px;}
.dic-list input{width: 100px;margin-right: 5px;}
.dic-list dd input:first-child{width: 50px;}
.dic-list dd label{margin-right: 15px;margin-bottom:5px;display: inline-block;}

/*通知公告*/
.notice{padding: 10px 20px;}
.notice .icon5{top: 3px; right: 3px;}
.noticeTitle{height: 40px;line-height: 40px;padding:0 15px; font-size: 14px;}
.noticeTitle,.noticeTitle a{color: #fff;}
.articleContent{border:1px solid #d6d6d6;border-bottom:0;margin-bottom: 10px;}
.articleContent img{max-width: 100%;}
.articleGray{height: 42px;line-height: 42px;border-bottom:1px solid #d6d6d6;background: #f8f8f8;padding: 0 10px;font-size: 14px;position: relative;}
.articleGray a{margin-left: 10px;}
.articleBar{right:10px;top:0;}
.articleBar time{margin:0 10px;}
.loadMore{line-height: 30px;}
/*新建公告*/
.noticeAdd label:first-child{width: 100px;text-align: right;float: left; line-height: 26px;}
.noticeAdd li{margin-bottom:10px;}
.noticeEdit{width: 85%;}
.rangeBtn{width: 100%; left: 0; bottom: 10px;position: fixed;}
.rangeList {margin:10px;height: 455px;overflow: hidden;}

/*指派会议*/
.meetTimeline{width:500px;margin:0 auto;border-left:5px solid #e2e2e2;padding:0 0 20px 20px;}
/*.meetFlag{margin-right: 25px;}*/
.meetFlag img{display: block;margin-bottom: 5px;}
.lineContent{margin-bottom: 10px;}
.lineContent .btn-border{margin:0 0 3px 0; font-size: 14px; padding: 0 8px;display: block;}
.lineContent p{height:22px;overflow: hidden;margin-bottom:2px;}
.meetMap{top:32px;right:0;}
.lineContent u:first-child{left: -19px; top: 0;}
.meetState{width: 160px;top:8px;left:-185px;text-align: right;color:#8b8b8b; font-size: 12px;}
.lineContent .time{color:#8b8b8b;}
.meetRight{margin-left: 95px;}

/*会议提醒*/
.meetings{overflow: hidden;padding-top: 20px;}
.meetCheck{border:1px solid #ccc;margin: 10px 20px;}
.meetCheck li{line-height: 28px;clear:both;}
.meetCheck input[type='radio']{vertical-align: -1px; height: auto;}
.meetInfo{width: 50%;}
.meetName,.meetCheck label+span{margin-left: 110px;display: block;+float: left; +margin-left: 0;}
.meetName .blockspan{display: block;}
.meetOperate{width: 49%;}
.meetInfo li{clear:both;}
.meetInfo label:first-child,.meetOperate label:first-child{float: left; width: 110px;text-align: right;}
.meetOperate .btn-black{margin-right:10px;}
.openMeet{border-right: 1px solid #ccc;}
.meetOperate .last{margin:5px 0 0 110px;}
.meetCheck .first{text-align: center;font-size: 16px;}
.meetDetail{margin:10px 0 0 180px;}
.meetDetail .meetTimeline{width: auto;}

/*会场地图*/
.mapSearch div.pa{right: 0; top: 17px;}
.mapSearch input{margin-right: 10px;}
.mapSearch .clock{border-width:2px;width: 130px; top:-2px;margin-right: 6px;vertical-align: middle;display: inline-block;+display: inline;zoom:1;}
.mapSearch .clock .timestr,.mapSearch .clock .fix{font-size: 16px;}
.mapSearch .clock .fix{left: 15px;bottom: -10px;}
.markerdetail span:first-child{width: 90px;display: inline-block;text-align: right;}
.markerdetail div{border-bottom:1px solid #aaa;line-height: 26px;}
.markerdetail div:last-child{border:0;}
.spmeet{right: 20px; top: 22px;}

/*工作台日历弹框*/
.popCalendar{border:1px solid #ccc;border-top:4px solid #48494b; line-height: 26px;}
.popCalendar label{width: 55px;}
.calendarBtn{padding:10px;}
/*日历*/
.fc-toolbar{margin-bottom:1px;}
.fc-toolbar .fc-center{margin-top: 6px; +margin-top: 3px;}
.fc .fc-toolbar > .titlehtml{display: inline;float: none;}
.fc-center span{float: none;}
.fc-state-default{background: #fff;border-color:#ccc;text-shadow:none;}
.fc-state-down, .fc-state-active{box-shadow: none;}
.fc-next-button{background:#fff url("../images/icon.png") no-repeat scroll -150px -525px;}
.fc-prev-button{background:#fff url("../images/icon.png") no-repeat scroll -100px -525px;}
.fc-prev-button span,.fc-next-button span{visibility: hidden;}
.h_calendar_alm{border-color:#ccc;}
.alm_content{background: #fff;border-top:1px solid #ccc;padding:0 0 5px;}
.alm_content .alm_lunar_date{background: none; width: 100%;}
.fc-view-container{background: #fff;}
.fc-day-header{line-height: 40px;color: #fff;}
.today_date{font-size: 50px;line-height: 60px; position:relative}
.alm_date{overflow: hidden;}
.calTable th, .calTable td{position: static; line-height: 29px; background-clip: padding-box; cursor: pointer;}
.fc-more-popover .fc-event-container{font-size: 14px;padding: 2px;}
.fc-event{line-height: 20px;}
.opt th, .opt td{font-size: 14px;vertical-align: middle;}
.titlehtml .titleword{font-size: 16px;font-weight: normal;}                
.haveMeeting{width: 12px;height: 12px;background-color: red;position:absolute;top:0;right:0;border-radius: 50%;line-height:12px; color:#fff; font-size: 10px; font-family: 'microsoft yahei'; -webkit-text-size-adjust:none; font-weight: normal;}
.monthHead{font-size: 20px;text-align: center;color:#fff;line-height: 40px;position: relative;}
.monthNone{background: #363f46;}
.workTotal{font-size: 12px;position: absolute;top: 0;right: 5px;color:#fff;}
.calendar {width: 84%;float: left;}
.calendarWrapper {margin: 5px;}
.sidePanel {width: 15%;}/*日期宽度*/
/*.onemouth, .fc .onemouth{+display: inline;zoom:1;margin:0 1% 10px 0;}*/
.onemouth, .fc .onemouth{+display: inline;zoom:1;margin:0 1% 0px 2px;}
.onemouth tbody:first-child{display: table;width: 100%;}
.calColor{border: 1px solid #ccc; margin-top: -1px;padding:5px;}
.calColor div{+margin-bottom: 10px;}
.triangle{width:0;height:0;border-width:0 6px 11px;border-top-color:transparent;border-right-color:transparent;border-left-color:transparent;border-style:solid;overflow:hidden;display: inline-block;margin-right: 5px;}
.meaboutme{width: 12px;height: 12px;display: inline-block;margin-right: 6px;}
.calColor u,u[class^=star]{width: 14px;height: 14px;margin-right: 4px;}
.calColor img{vertical-align: top;}
.star1{background-position:-350px -475px;}
.star2{background-position:-375px -475px;}
.star3{background-position:-400px -475px;}
.star4{background-position:-425px -475px;}
.star5{background-position:-450px -475px;}
.star6{background-position:-475px -475px;}
.star7{background-position:-350px -500px;}
.star8{background-position:-375px -500px;}
.fc u{width: 30px;height: 30px; margin-top: -1px;display: none;}
.selDown{padding:10px;margin:1px 0 2px; border:1px solid #ccc;}
.selDown select{width:100%;}
/*状态*/
.back4{background:#c4e5ff;}
.back5{background:#faf3cb;}
.back6{background:#ffd2c8;}
.back7{background:#d5f3db;}

/*所属关系*/

.meetLog {border-bottom:1px solid #d6d6d6}
.meetLog li{border:1px solid #d6d6d6; padding:5px;/* margin-bottom: 10px; */overflow: hidden; border-bottom:none}
.meetLog h5{height: 20px; line-height: 20px;}
.logTitle{display: inline-block;height: 14px;line-height: 16px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;width:80%;font-weight:bold;}
.meetTotal{line-height: 36px;}
.meetLog time{font-size: 12px;}

/*会议室管理*/
.meetManage{border-bottom: 1px solid #ccc;}
.meetManage li{float: left;width: 180px;margin:10px 10px 10px 0;text-align: center;}
.meetManage img{margin:0 auto 5px;display: block;height: 50px;}
.meetManage time{margin-left: 10px;}
.meetManage .addr{height:40px;overflow: hidden;}

/*个人信息*/
.personalImg{width: 200px; margin-left: 100px;}
.personalInfo input[type="text"]{width: 220px;}
.otx-head{width:96px;height:96px;border-radius: 3px;border:1px solid #ccc;display: block; margin-bottom: 15px; text-align:center}
.cmxform{margin:0;}
.otx-head img{ vertical-align:middle}
/*增加设备*/
/*有浮动的form*/
.floatForm div{float: left; width: 50%; +width: 49%;}
.floatForm input[type="text"]{width:220px;}

/*用户管理*/
.personList li {border: 1px solid #ccc;float: left;margin:10px 1%;position: relative;width: 31%; line-height: 24px;min-width: 350px;}
.personImg {
    border-radius:50%;
    height: 96px;
    margin: 10px;
    width: 96px;}
.personInfo{margin-left: 115px;}
.personInfo span:first-child {
    color: #999;
    display: inline-block;
    text-align: right;
    width: 70px;}
.personList .btn-edit{color:#fff; font-size: 12px; border-radius: 5px; top: 5px; right: 5px;padding: 0 5px;}
.divrole {border: 1px solid; left: 160px; top: 17px;z-index: 9;}
.ulselect li{padding: 0 5px;cursor: pointer;}

/*设置*/
.setColor u{top:0;right:0;display:none;}
.setColor li{float: left;width: 70px;height: 70px;margin-right:15px;position: relative; cursor: pointer;}
.setColor .bg_gray{background: #363f46;}
.setColor .bg_blue{background: #2c99de;}
.setColor .bg_green{background: #05a9a0;}
.setColor .bg_orange{background: #ff761b;}
.setColor .bg_purple{background: #6777d7;}
.setColor .bg_red{background: #de575d;}
.setColor .bg_lightGreen{background: #6eb92b;}
.setColor .bg_pink{background: #e79bb9;}

/*活跃度分析弹框*/
.active-con{background:#fff;width:415px;margin: auto;padding-bottom: 30px;}
.tit-top{height: 58px;line-height: 58px;font-size: 17px;margin-left: 40px;}
.active-text {font-size: 0;margin:0 ;}
.active-text .active-list{width: 50%;display: inline-block;font-size: 14px;}
.active-list:nth-child(1) .area-con::after{content: "";height: 100%;position: absolute;right:0;}
.area-con{font-size: 0;position: relative;}
.area-con span{margin-right:30px;display: inline-block;font-size: 17px;vertical-align: top;margin-top: 22px;margin-left: 40px;}
.area-con div{text-align:center;width: 70px;height: 70px;display: inline-block;font-size: 17px;border-radius: 50%;}
.area1 span{color:#04a3e3;}
.area1 div {border:9px solid #04a3e3;}
.area2 span{color:#f08ea6;}
.area2 div {border:9px solid #f08ea6;}
.area3{width: 30%;text-align: right;padding-right: 20px;float: left;}
.area3 div{border:9px solid #f6766c;}
.area-con div p:nth-child(1){margin-top:10px;}
.area-txt{color:#666;font-size:17px;margin-top:15px;padding-bottom: 15px;border-bottom: 1px solid #eeeeee;padding-left: 40px;}
.area-txt p{text-align: left;line-height: 30px;}
.btn-con{text-align: center;clear: both;}
.btn-con a{color:#333;margin-top:40px;width: 60px;height: 30px;display: inline-block;text-align: center;line-height: 30px;background: #f1f1f1;border: 1px solid #dedede;}

.lines {
	width: 5px;
	height: 88px;
	border-right: 1px solid;
	border-image: -webkit-linear-gradient( rgba(255,255,255,0), rgba(6,6,6,1),rgba(255,255,255,0)) 10 30;
	border-image: -moz-linear-gradient( rgba(255,255,255,0), rgba(6,6,6,1),rgba(255,255,255,0)) 10 30;
	border-image: linear-gradient( rgba(255,255,255,0), rgba(6,6,6,1),rgba(255,255,255,0)) 0 30;
	position: absolute;
	top:0;
	left:98%;
}

.txt-list{color:#666;float: left;margin-left: 20px;margin-top:-7px;}
.txt-list p{font-size: 17px;line-height: 35px;}
.txt-list p img{vertical-align: middle;margin-right: 20px;margin:auto;}
.txt-list p i{width: 60px;display: inline-block;text-align: center;}
.txt-list p:nth-child(2) i img{margin-top:-7px;}
.txt-list p:nth-child(3) i img{margin-top:-5px;}
.txt-list p span.wish-span{color: #ff242f;}
.txt-list p span.like-span{color: #ff6f30;}
.txt-list p span.peo-span{color: #1297db;}

.setBack li{width: 190px;height: 120px;border:1px solid #f3f3f3;margin-bottom: 10px;}
.setBack img{width: 100%;height: 100%;}
.setBack u.icon19{right: 50%; margin-right: -16px; top: 50%; margin-top: -16px;}

/*会议详情会议信息查看*/
.meetMsg .meetRight{margin-left: 0;}
.meetMsg .meetTimeline{padding:0 0 10px 20px;}
.meetMsg .meetInfo{line-height: 28px;}

/*栏目管理*/
.columnList{width: 20%;padding:10px;}
.meauRight{width: 70%;}
.meauRight div{padding-top: 10px;margin: 0}
.formList .funLabel label{width: auto;margin:0 10px 10px 0;display: inline-block;}
.formList .funLabel input[type="text"]{width: 70px;}
.formList .funLabel{width: 60%;padding: 0;}
.meauSearch{margin:15px 0;}
.meauSearch .skinIcon3{position: absolute;top: 3px;right: 0;}
.meauList .inner img,.selImg img{border-radius: 50%;vertical-align: middle; height: 40px;margin: 0 auto;width: 40px;background: url(../images/gray.png) no-repeat center center;}
.meauList li{margin-bottom: 2px;overflow: hidden;}
.meauList u{cursor: pointer;width: 25px;height: 25px;margin:7px 0 0 5px;}
.meauList span{display: block; line-height: 36px; color:#616161;cursor: pointer;background: url(../images/list.png) no-repeat left 8px; text-indent: 23px;}
.meauList span .icon24{margin-top: -10px;}
.meauList .inner{margin: 2px 0;}
.meauList .inner a{float: left; width:32%;position: relative;text-align: center;font-size: 12px;color:#fff;padding:10px 0 5px;}
.meauList .inner p{height: 28px; line-height: 26px; overflow: hidden;}
.meauList .icon25,.meauList .icon26{position: absolute;}
.meauList .icon29,.meauList .icon30{cursor: not-allowed;}
.meauList .inner .icon24{margin: 0; top:0; right: 16px;position: absolute;}
.meauList .inner .icon25,.meauList .inner .icon26{bottom: 20px; left: 50%; margin-left: -9px;}
.inner a.selected{border-color:#fdaa2e;}
.selImg img{cursor:pointer;margin:4px;}

/*单位管理*/
.secedit{margin-left: 250px; width:80%;}
#chart table{margin:0 auto;}
.jOrgChart .node{
    height: 40px;
    border-radius: 8px;
    color:white;
    font-size: 14px;
    line-height: 40px;
    white-space: nowrap;
}
.htitle{text-align: center; font-size: 20pt; font-weight: bold;margin-bottom: 25px;}
.formList .webuploader-pick{margin-top: 0;padding: 4px 10px;vertical-align: middle;}
.webuploader-pick a{color: #fff;}

/*系统配置*/
.sysLabel{display: inline-block;width: 70px;}

/*统计分析*/
.analysis{width: 49%;margin-right: 1%;margin-bottom: 15px;}
.analysis .charts{height: 500px;}
.analysis .noticeTitle{font-size:16px;color:#fff;text-align: center;}
.analysisHead{padding:10px; background: #f8f8f8;border-bottom: 1px solid #ccc;font-size: 14px;}
.analysis .tab{margin: 8px;}

/*table demo*/
.otx-table{text-align: center; line-height: 2;}
.otx-table tr:nth-of-type(2n){background: #f7f8fa;}
.otx-table tr:hover{background:#feecd4;}
.otx-table td,.otx-table th{border:1px solid #ccc;}

/*角色管理*/
.roleTitle th{border:1px solid #ccc;color:#fff;font-size: 16px; line-height: 54px;}
.innerTable th,.innerTable td{border:1px solid #ccc; line-height: 40px;}
.innerTable th{text-align: left; background-color: #ddd;}
.innerTable td:last-child label{margin-right: 15px;display: inline-block;}
.roleTable .icon31{margin: 0 10px;}

/*会场配置*/
.placeTable{line-height: 40px;border:1px solid #ccc;}
.placeTable th{color: #fff;}

/*弹出框*/
/*增加会议*/
.popbox{border:1px solid #ccc;}
.joinPart{border:1px solid #000;height: 65px;display: inline-block;+display: inline;zoom:1; vertical-align: middle; width: 492px;padding:2px 3px;border-radius: 2px;overflow-y: auto;}
.joinPart .onedepart{display: inline-block;margin-right: 10px;}
.joinPart u{cursor: pointer;margin-left: 5px;}
.placeChoose,.placeResult{width: 40%;margin:0 1%;height: 370px;}
.placeResult{border:1px solid #000; margin-top: 43px;overflow: auto;}
.placeResult li{height: 28px;line-height: 28px; overflow: hidden; text-indent: 8px;cursor: pointer;}
.placeResult u{margin-left: 6px;}
.placeBtns{width: 15%;text-align: center;}
.placeBtns a{margin-top: 20px;padding:0 10px;}
.placeBtns a:first-child{margin-top: 200px;}

/*弹框table*/
.popTable{text-align: center;line-height: 32px;border-top: 1px solid; margin-top: 10px;}
.popTable label{display: inline-block; width: 140px; overflow: hidden; height: 30px;text-align: left;}

/*智能中控*/
.controlRight,.conTop,.btnTop,.btnTop a,.conBottom{border-radius:6px;}
.controlRight{width: 270px;padding:15px;margin-top: 5px;background: #363f46;}
.conTop{padding:15px 12px;}
.btnTop{padding:15px 5px;background: #241f1c;}
.btnTop a{line-height: 38px;width: 95px; font-size: 18px;color: #fff;background: #363f46;border:1px solid #000;}
.operateBtn{margin-top: 15px;}
.operateBtn a{border-radius:4px; background: none; color: #fff;width: 100px;line-height: 32px;}
.operateLeft,.operateRight{width: 48px;}
.operateLeft a,.operateRight a{color: #fff;}
.btn-far,.btn-near{width: 47px;height: 47px;line-height: 47px; display: inline-block;margin-top: 10px;background:url(../images/distance.png);}
.fast,.normal,.slow,.arrow-up,.arrow-down,.focus{width: 40px;height: 40px;line-height: 40px; display: inline-block;margin-top: 15px;background: url(../images/black.png);}
.conTop,.conBottom{border:1px solid #000;}
.operateBtn a,.conInput input{border:2px solid #000;}
.focus{background: none;color: #fff;}
.key{left: 50px; top: 18px;width: 144px;height: 144px;}
.key u{position: absolute;cursor: pointer;}
.btn-parameter{top: 15px; left: 50%; background:none; width: 100px; margin-left: -50px;}
.conBottom{margin-top: 15px; padding: 15px 10px 0;}
.btnMiddle{margin: 20px auto;}
.numberList{margin-top: 10px;}
.numberList li{width: 25%; height: 54px; text-align: center; float: left; margin: 10px 0;}
.numberList span{width: 54px; height: 54px; line-height: 54px;display: inline-block;cursor: pointer;font-size: 22px;}
.numberList span{background: url(../images/white.png) center center no-repeat;}
.numberList span:hover{color: #fff;}
.conInput input{width:207px;height: 36px; line-height: 36px;background: none;padding:0 32px 0 5px;color:#fff;border-radius: 4px;}
.controlLeft{width: 920px;padding:0 15px 20px;}
.middleList{padding:33px 0;}
.middleList,.switchList,.objectList li{border:1px solid #ccc;}
.switchList li{float: left;width:20%;+width:183px; text-align: center;padding:20px 0;}
.switchList u{width: 120px;height: 120px;display: block;margin:0 auto; cursor: pointer;}
.objectList{margin:18px 0;}
.objectList li{float: left;width:216px;height:230px;margin-right: 16px;}
.objectList li.last{margin-right: 0;}
.objectList p{text-indent:10px;background: #cecece; font-size: 16px; line-height: 30px;margin-bottom: 10px;}
.hdList li{float: left;width: 33.3%;text-align:center; position: relative;}
.hdList .icon18{position: absolute;right: 41%;top:-7px;}
.nationalMsg{padding:5px 0;}
.nationalMsg img{width:48px;height:32px; vertical-align:middle;margin-right: 10px;}
.rankList{width:40%;margin-left: 20%;margin-top: 45px;}
.rankList span{display: block;width: 100%; background: #363f46;margin-bottom: 5px;cursor: pointer;}
.rank7{height: 5px;}
.rank6{height: 7px;}
.rank5{height: 9px;}
.rank4{height: 11px;}
.rank3{height: 13px;}
.rank2{height: 15px;}
.rank1{height: 17px;}
.rankList u{position: absolute; cursor: pointer;}

/*中控配置*/
/*table*/
.dataTables_wrapper{background: #fff;font-size: 14px;}
.dataTables_wrapper thead th{background: #e2e2e2;border-right: 1px solid #ccc;}
table.dataTable thead th, table.dataTable thead td{border-bottom-color:#ccc;}
.dataTable a{font-weight: bold;}

/*会议控制*/
.meetContent{overflow: auto;margin: 100px 0 54px 0; +padding-top: 120px; height:100%;}
.meetContent img{width: 45px;height: 30px;vertical-align: middle;cursor: pointer;}
.meetMain{width: 400px;margin:20px auto;border:1px solid #d6d6d6; text-align: left;}
.mainFoot{background: #f8f8f8;padding:5px 10px;height: 30px;}
.mainFoot u{width: 27px;height: 25px;margin:3px 0 0 15px;}
.meetContent .formList label:first-child{width: 110px;}
.meetMain .formList,.submeetList .formList{padding:5px 0 20px;}
.submeetList{display: inline-block;+display: inline;zoom:1;width: 350px;margin:20px 1%;border:1px solid #d6d6d6; text-align: left;position: relative;}
.meetHead{width: 100%; left: 0; top: 0;height: 100px;text-align: center;position: fixed;z-index: 2;}
.meetHead h2{margin-top: 26px;}
.meetHead u{width: 40px;height: 33px;margin-left: 15px;cursor: pointer;}
.clock{width: 190px;left: 15px;top: 9px;border:5px solid #363f46; border-radius: 6px;padding:3px 0 12px;}
.clock .timestr{font-size:40px;color:#ca0814;font-family: arial; height: 48px;overflow: hidden;}
.clock .fix{width: 100px; bottom: -16px; left: 42px; font-size: 22px;}
.oneMeet p{margin-left: 225px; text-align: left; line-height: 24px;}
.oneMeet p.first{margin-top: 20px;}
.oneMeet em{font-size: 20px; vertical-align: 0;padding:0 2px;}
.meetingRight{margin: 26px 15px 0 0;overflow: hidden;}
.meetFoot{width: 100%;left: 0;bottom: 0;padding: 10px;position: fixed;z-index: 2;}
.meetFoot u{cursor: pointer;width: 34px;height: 34px;margin-right: 15px;}
.meetFoot u.skinIcon3{width: 22px;height: 22px;}
.meetFoot span{border-right: 1px solid #909090; display: inline-block; margin-right: 10px;}
.meetFoot span:last-child{border:0;}
.nextMeet{border-top:1px solid #363f46;border-bottom:1px solid #363f46;padding-bottom:10px;}

/*上传图片btn*/
.webuploader-pick{vertical-align: middle;display:inline-block;}
.webuploader-container{
	margin-top:15px;
	height:40px;
	display:inline-block;
	+vertical-align: middle;
}

/*拾色器*/
#picker{cursor: pointer;}

/*验证信息*/
.formError .formErrorArrowBottom{display: none;}
.formError .formErrorContent{background: #fcbbbb;border-color:#e10b18;border-radius: 4px;color: #97040c;padding:6px 10px; font-size: 12px; width: 50%;margin:0;}

/*echarts样式*/
.echarts-dataview button{border:0;color:#fff;cursor: pointer;}

/*下拉多选*/
.ui-widget-content{font-size: 14px;}
.ui-multiselect-checkboxes label{cursor: pointer;}
.ui-state-hover, .ui-widget-content .ui-state-hover, .ui-widget-header .ui-state-hover, .ui-state-focus, .ui-widget-content .ui-state-focus, .ui-widget-header .ui-state-focus{background: #ddd;border-color:#ccc;font-weight: normal;}
.ui-widget-header{font-weight: normal;}
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default{font-weight: normal;cursor: pointer;}
.ui-corner-all input[type='checkbox']{height: 18px;line-height: 18px;}
.ui-multiselect{+vertical-align: middle;}
/*分屏模式*/
.split{border:1px solid #ddd;padding-bottom: 20px;}
.split u,.screenPop u{width: 67px;height: 67px;border:1px solid #fff; cursor: pointer;}
.split li{height: 68px; margin-top: 20px;}
.split span{vertical-align: middle;width: 60px;text-align: right;display: inline-block;margin-right: 10px;}
.split li u.current{border-color:#f00;}

/*手动分屏*/
.handWrap{padding-bottom: 45px;}
.handScreen{width: 31%;height: 500px;border:1px solid #ccc;float: left;background: #4E5571;color:white; text-align: center;position: relative;}
.divouter{height:100%;}
.screenList{height:100%;}
.handScreen .last{margin-right:0;}
.screenSelect{position: fixed; left: 0; bottom: 0; width: 100%; box-sizing:border-box; padding: 0 70px; +padding:0;}
.screenSelect u{width: 46px;height: 70px;top:85px; cursor: pointer;}
.optionMeet{width: 100%; +width: 90%;height: 220px;overflow: hidden; margin:0 auto;padding: 10px 0;}
.oneplace{float: left;width: 24%;background: #fff; margin-right: 1%;cursor: pointer; text-align: center;}
.oneplace:hover{background: #1d252b;}
.oneplace div{height:180px;}
.oneplace p{margin-top: 10px; background: #4d5054;color: #fff; line-height: 2;}
/*分屏样式*/
.screen1 div{width: 50%;height: 500px;line-height: 500px;float: left;}
.screen1 div:first-child{ border-right:1px solid #ccc;margin-left: -1px;}
.screen2 div{width: 100%;height: 250px;line-height: 250px;}
.screen2 div:first-child{ border-bottom:1px solid #ccc;margin-top: -1px;}
.screen3 div{width: 50%;height: 250px;line-height: 250px;border-right:1px solid #ccc;border-bottom:1px solid #ccc;float: left;margin-left: -1px;}
.screen4 div{width: 33.3%;height:166px;line-height:166px;border-right:1px solid #ccc;border-bottom:1px solid #ccc;float: left;margin-left: -1px;}
.screen5 div{width: 33.3%;height:166px;line-height:166px;border-right:1px solid #ccc;border-bottom:1px solid #ccc;float: left;margin-left: -1px;}
.screen5 div:first-child{width: 66.6%;height:332px;line-height:332px;}
/*screenPop
.screenPop{border-top:1px solid #ccc;}
.screenPop li{border-bottom: 1px solid #ccc;}
.screenPop span{width: 150px;text-align: center;display: inline-block;border-right:1px solid #ccc;height: 82px; line-height: 82px;}
.screenPop u{margin-left: 10px;}
.screenPop u.current{border:1px solid #f00;}*/
.screen6 div{width:50%;height:250px;line-height:250px;border-right:1px solid #ccc;border-bottom:1px solid #ccc;float: left;margin-left: -1px;margin-bottom: -1px;}
.screen6 div:first-child{width: 100%;height:250px;float: none;}
/*登录样式*/
/*.bg_login{background:url(../images/loginbg1.jpg) ; width:1920px; height:800px; background-position:center center}*/
.nav_login{  margin:0px   auto 0  auto ; height:71px; width:980px; padding-top:15px;}
.nav_login img{ float:left; cursor:pointer}
.logo{ padding-right:20px;}
.nav_login .zleft{ float:left; }
.nav_login .zright{ float:right; margin-top:8px;}
.nav_login .zright a{ color:#009c77; font-size:16px; line-height:30px; height:30px; padding-right:5px;}
/*login*/
.loginContent{width:100%;/*background:url(../images/alpha3.png);*/ position: relative;background:#02ad5e; padding:120px 0 86px 0px; overflow:hidden}
.loginTitle{color: #444444;font-size:22px; line-height:30px; text-align:center; height:39px; margin-bottom:30px; width:496px;}
.login_zc{ float:right; cursor:pointer; width:66px; height:29px; padding:10px 0 30px 0}
.loginArea{ float:right; width:536px;height:427px;}
.con-area{ width:296px; padding:30px 0px 0 18px;float: left; }
.code-area{width: 180px;float: left;padding:30px 20px 20px 20px;text-align: center;position: relative;}
.code-area h5{font-size: 14px;margin-bottom: 5px;}

.line-left{ 
			float: left;
			width: 1px;
			height:200px;
			margin-top:20px;
			margin-left: -5px;
			position: relative;
			 }
			 :root .line-left{filter:none;} 
.line-top{ 
			
			float: left;
			width: 1px;
			height:100px;
			
			background: -moz-linear-gradient(top, rgba(255,255,255,0), rgba(210,210,210,1)); 
			background: -webkit-gradient(linear, top, bottom, color-stop(0%,#fff), color-stop(100%,#d2d2d2)); 
			background: -webkit-linear-gradient(top, rgba(255,255,255,0), rgba(210,210,210,1)); 
			background: -o-linear-gradient(top, rgba(255,255,255,0), rgba(210,210,210,1)); 
			background: -ms-linear-gradient(top, rgba(255,255,255,0), rgba(210,210,210,1)); 
			background: linear-gradient(to bottom, rgba(255,255,255,0), rgba(210,210,210,1)); 
			filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#FFFFFFFF', endColorstr='#EEEEEEFF',GradientType=0 ); 
			
		} 
.line-bottom{
			float: left;
			width: 1px;
			height:100px;
			
			background: -moz-linear-gradient(top,  rgba(210,210,210,1),rgba(255,255,255,0)); 
			background: -webkit-gradient(linear, top, bottom, color-stop(0%,rgba(210,210,210,1)), color-stop(100%,rgba(255,255,255,0))); 
			background: -webkit-linear-gradient(top, rgba(210,210,210,1),rgba(255,255,255,0)); 
			background: -o-linear-gradient(top,  rgba(210,210,210,1),rgba(255,255,255,0)); 
			background: -ms-linear-gradient(top,  rgba(210,210,210,1),rgba(255,255,255,0)); 
			background: linear-gradient(to bottom, rgba(210,210,210,1),rgba(255,255,255,0)); 
			filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#EEEEEEFF", endColorstr='#FFFFFFFF',GradientType=0 ); 

}						
.code-div{padding-top: 20px;padding-bottom: 15px;position: relative;}
.code-div .bor-span{position:absolute;top:10px;left:8px;border-top:3px solid rgba(2,173,94,0.3);border-left:3px solid rgba(2,173,94,0.3);width: 22px;height: 22px;display: inline-block;}
.code-div .bor-span2{position:absolute;bottom:10px;right:8px;border-bottom:3px solid rgba(2,173,94,0.3);border-right:3px solid rgba(2,173,94,0.3);width: 22px;height: 22px;display: inline-block;}

/*.loginArea input{display: block;background: #eee;border:0;width:475px; height: 60px;line-height: normal;line-height: 60px\9;margin-bottom: 20px; font-size: 16px; padding-left:25px;}
.changeBg{right: 50px;bottom: 50px;}.,
.changeBg u{width: 40px;height: 62px;cursor: pointer;margin:0 50px;}*/
.loginContentdiv{ background:url(../images/loginbgbg.png) no-repeat; width:974px; height:461px; margin:0 auto}
.input-group{ clear:both; margin:0px 0 20px 0; width:100%; border:1px solid #dcdcdc; border-radius:4px; height:45px; line-height:45px; vertical-align:middle}
.glyphicon { background:url(../images/tel.png) no-repeat; width:20px; height:20px; background-size: 100% 100%; float:left; vertical-align:middle;margin: 13px 7px 13px 10px;}
.input-group input{ float:left; border:none; height:44px; line-height:44px; /*color:#cccccc;*/ font-size:inherit; width: 198px;}
.glyphicon1 { background:url(../images/post.png) no-repeat; width:20px; height:20px; background-size: 100% 100%; float:left; vertical-align:middle;margin: 13px 7px 13px 10px; }
.login_jz{ height:44px; width:530px; text-align:center ;clear: both;}
.login_jz a{ float:right; color:#23a8e0;}
.zbtn-login{  margin:20px 0; width:296px; height:49px; cursor:pointer; display:block; background:url(../images/denglu_btn.png) no-repeat}
.cl2{ color:#919199}
.for-wordtxt{ color:#4d8cff; float:right; cursor:pointer}
.footer{ text-align:center; line-height:40px; height:40px; color:#666666; font-size:12px; clear:both} 
.nav_loginright{
    float:left;
}
.login_imgcen li{float:left;padding-top:10px;}
.login_imgcen li a.login_hover{display:block;float:left;height:36px; vertical-align:middle;padding:0 0px 0 0;transition:all 0.3s;cursor:pointer;}
.login_imgcen {position:relative;}

.login_botdiv{display:none; width:365px;padding:10px; position:absolute;top:50px;left:0;z-index:1000;background-color:#fff;border:1px solid #cdcdcd;box-shadow:0 0 10px rgba(0,0,0,0.19);transition:all 0.3s;border-radius:3px;box-sizing:border-box;
-ms-filter:"progid:DXImageTransform.Microsoft.Shadow(Strength=10,Direction=120, Color='#000000')";
filter:progid:DXImageTransform.Microsoft.Shadow(Strength=10,Direction=120,Color='#000000');
}
.login_botdiv  u.zarrowUp_icon{position:absolute;top:-11px; z-index:1001; background-position:-99px 0;width:22px; height:22px; cursor:pointer;}
.login_botdiv  u,.navbg03 u{
    display: inline-block; background:url(../images/helpicon.png) no-repeat;
 
}
.navbg01{ background:url(../images/bg01.png) no-repeat #E9F7EB; font-size:12px; width:331px; height:300px; margin:10px auto 0 auto; color:#333333; padding-top:20px;}
.navbg02{ background:url(../images/bg02.png) no-repeat #E9F7EB; font-size:12px; width:331px; height:220px; margin:10px auto 0 auto; color:#333333; padding-top:20px;}
.navbg03{ background:url(../images/bg03.png) no-repeat #E9F7EB; font-size:12px; width:331px; height:220px; margin:10px auto 0 auto; color:#333333; padding-top:20px;}
.navbg04{ background:url(../images/bg04.png) no-repeat #E9F7EB; font-size:12px; width:331px; height:220px; margin:10px auto 0 auto; color:#333333; padding-top:20px;}
.navbg05{ background:url(../images/bg05.png) no-repeat #E9F7EB; font-size:12px; width:331px; height:220px; margin:10px auto 0 auto; color:#333333; padding-top:20px;}
.navbg06{ background:url(../images/bg06.png) no-repeat #E9F7EB; font-size:12px; width:331px; height:220px; margin:10px auto 0 auto; color:#333333; padding-top:20px;}
.navbg07{  font-size:12px; width:167px; height:238px; margin:0px auto 0 auto; color:#333333;  text-align:center }
.navbg08{  font-size:12px;  height:267px; margin:0px auto 0 auto; color:#333333;  text-align:center;  }
.navbg08 img{ margin:10px auto; text-align:center; float:none}
.navbg07 img{ margin:0 auto;} 
.znebCont img.zimg{ text-align:center; display:block; float:none; margin:0 auto; }
.znebCont h3{ font-size:18px; line-height:30px; height:30px; text-align:center; color:#02ad5e}
.navbg01 p,.navbg02 p,.navbg03 p,.navbg04 p,.navbg05 p,.navbg06 p,.navbg07 p{ line-height:23px; color:#333333; font-size:12px; clear:both; padding:0 15px}
.rdrem{ text-indent:2em;}
.remz{ border-bottom:1px dashed #afd9b5; height:1px; padding:10px 0}
.nav_jr{ color:#eb5300 ; line-height:30px; float:right; padding-right:5px; font-size:14px; border-bottom:1px solid #eb5300; height:30px; }
.nav_jr a{ color:#eb5300 ;}
.nav_jr img{ vertical-align:middle}
.nav_jr u{ background:url(../images/jinru.png) no-repeat; display:inline-block; width:22px; height:14px}
.loginTitle img{display:inline-block; /*width:50px; height:45px;*/ padding-right:5px;vertical-align: middle;text-decoration: none;}
.zright u,.znebCont u{background:url(../images/helpicon.png) no-repeat; display:inline-block;  }
.zright u.cio01{ background-position:-46px -68px;width:49px; height:43px; cursor:pointer}
.znebCont u.cio02{ background-position:0 -215px;width:78px; height:86px; cursor:pointer; position:absolute; right: -3px;
    top: -6px;}
	
.navbg03 u.navbgico1{ background-position:0 -81px;width:24px; height:16px; cursor:pointer}
.navbg03 u.navbgico2{ background-position:0 -103px;width:36px; height:16px; cursor:pointer}
.navbg03 u.navbgico3{ background-position:0px -124px;width:24px; height:16px; cursor:pointer}
.navbg03 u.navbgico4{ background-position:0 -146px;width:25px; height:16px; cursor:pointer}
			
/* 出勤人数*/
.cqrsbg{display:block; background:#E7614B; border-radius: 20px; position: relative; right: 25px; width: 25px; color: rgb(255, 255, 255); font-weight: bold; font-size: 12px; text-align: center; vertical-align: middle; padding-top:3px; height: 22px; float: right; top: -21px; cursor:pointer}
.cqrsbg_gray{display:block; background:#6aba49; border-radius: 20px; position: relative; right: 25px; width: 25px; color: rgb(255, 255, 255); font-weight: bold; font-size: 12px; text-align: center; vertical-align: middle; padding-top:3px; height: 22px; float: right; top: -21px; cursor:pointer}
.j_cqrsbg{display:block;  position: absolute;  float: right;   right: 0;top: 0; width: 24px; height:24px; cursor:pointer; background:url(../image/icon_markaskleave.png) no-repeat;background-size: 100%;}
.t_cqrsbg{display:block;  position:absolute;  float: right;   right: 0;top:0; width: 24px; height:24px; cursor:pointer; background:url(../image/icon_markstop.png) no-repeat;background-size: 100%;}
.b_cqrsbg{display:block;  position:absolute;  float: right;   right: 0;top:0; width: 24px; height:24px; cursor:pointer; background:url(../image/icon_markclass.png) no-repeat;background-size: 100%;}
.t_bg{ background:#F39C12}
.j_bg{ background:#30CD73}
.dj_cqrsbg{display:block;  position: absolute;  float: right;   right: 0;top: 0; width: 24px; height:24px; cursor:pointer; background:url(../image/icon_markaskleave.png) no-repeat;background-size: 100%;}
.dt_cqrsbg{display:block;  position:absolute;  float: right;   right: 0;top: 0; width: 24px; height:24px; cursor:pointer; background:url(../image/icon_markstop.png) no-repeat;background-size: 100%;}
.bt_cqrsbg{display:block;  position:absolute;  float: right;   right: 0;top: 0; width: 24px; height:24px; cursor:pointer; background:url(../image/icon_markclass.png) no-repeat;background-size: 100%;}
.dj_btn{ text-align:center;}
.btn{  background: #1da89e none repeat scroll 0 0;
    border: 1px solid #1da89e;
    border-radius: 4px;
    color: #fff;
    cursor: pointer;
    height: 26px;
    line-height: 20px;
    overflow: visible;
    padding: 0 5px;}

.calTable td span.rilicqrsbg{   background: #E7614B none repeat scroll 0 0;
    border-radius: 5px;
    color: rgb(255, 255, 255);
    cursor: pointer;
    display: block;
    float: right;
    font-size: 8px;
    font-weight: bold;
    height: 12px;
    line-height: 12px;
    padding-top: 1px;
    position:absolute;
    right: 0;
    text-align: center;
    top: 0px;
    vertical-align: middle;
    min-width: 12px; font-family:微软雅黑}
/** 年日历，当前天 */
.calTable td span.rilicqstate{
    border-radius: 5px;
    cursor: pointer;
    display: block;
    float: left;
    font-size: 8px;
    font-weight: bold;
    height: 12px;
    line-height: 12px;
    padding-top: 1px;
    position:absolute;
    left: 0;
    text-align: center;
    top: 0px;
    vertical-align: middle;
    min-width: 12px; font-family:微软雅黑}
	
.calTable td span.rilicqrsbg_gray{   background: #6aba49 none repeat scroll 0 0;
    border-radius: 20px;
    color: rgb(255, 255, 255);
    cursor: pointer;
    display: block;
    float: right;
    font-size: 8px;
    font-weight: bold;
    height: 12px;
    line-height: 12px;
    padding-top: 1px;
    position:absolute;
    right: 0;
    text-align: center;
    top: 0px;
    vertical-align: middle;
    width: 12px; font-family:微软雅黑}
	
/* 404页面*/
.wtfgdiv{ width:90%; margin:20px auto; text-align:center}
.wtfgdiv img{ margin:0 auto; width:646px; }
.fhbtn{  border-radius: 5px;box-shadow: 0 0 1px #1da89e, 1px 1px 1px #1da89e;
    color: #fff;
    display: block;
    font-size: 14px;
    height: 30px;
    line-height: 30px;
    margin: 15px  auto;
    text-align: center; background:#1da89e; width:80px; cursor:pointer }
		
/* 打印二维码*/
.wxdiv{width:566px; margin:20px auto; text-align:center; overflow:auto}
.wxdivleft{ float:left; width:260px; overflow:hidden; display:inline; margin-right:40px;  }
.wxdivleft img{ width:260px; height:260px; }
.wxdivleft p{ color:#333333; font-size:16px;}
.wxdivleft span{  clear:both; display:inline-block } 
.wxdiv span img{ margin-top:20px; cursor:pointer}


/*导航页*/

.dhnavbg{width:672px; height:440px; margin:0px auto; background:url(../images/dhbgpic.jpg) no-repeat; padding:60px 0 0 128px;}
.dhnavbgzleft { float:left; overflow:hidden}
.dhnavbgzright{ float:left; margin-left:30px; display:inline; overflow:hidden; width:561px;}
.dhnavbgzright1{ overflow:hidden}
.dhnavbgzright1 img{ float:left; display:block; width:54px; height:54px; padding-right:10px;}
.dhnavbgzright1 p{ float:left; width:410px; line-height:30px; font-size:14px}
.ltop20{ margin-top:20px}
.ltop40{ margin-top:40px}
.dhnavbgzright1 p span{  font-size:20px; font-style:italic}
.yellow{color:#ff7b23;}
.gree{ color:#05c195}
.blue{ color:#379ce9}

.dhleft{ text-align:left; width:200px; overflow:hidden; float:left; line-height:20px;	margin-top:24px; font-size:14px;}
.chk_1,.chk_2,.chk_3,.chk_4 {
    display: none;
}

 u.cio01{ background:url(../images/helpicon.png) no-repeat; display:inline-block; background-position:-46px -68px;width:49px; height:43px; cursor:pointer}
 
/*******STYLE 1*******/
.chk_1 + label {

	border: 1px solid #999;
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), inset 0px -15px 10px -12px rgba(0, 0, 0, 0.05);
	padding: 9px;
	border-radius:2px;
	display: inline-block;
	position: relative;
	margin-right:5px;
float:left;
}
.chk_1 + label:active {
	box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px 1px 3px rgba(0,0,0,0.1);
}

.chk_1:checked + label {

	border: 1px solid #1da89e;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05), inset 0px -15px 10px -12px rgba(0, 0, 0, 0.05), inset 15px 10px -12px rgba(255, 255, 255, 0.1);
	color: #243441;
}

.chk_1:checked + label:after {
	content: '\2714';
	position: absolute;
	top: -1px;
	left: 0px;
	color: #1da89e;
	width: 100%;
	text-align: center;
	font-size: 1.2em;
	padding: 1px 0 0 0;
	vertical-align: text-top;
}
.dhleft span{ display:inline-block; float:left}
@media screen and (min-width:1280px) and (max-width:1400px){
.loginContent{width:100%; background:#02ad5e; padding:80px 0 8px 0; overflow:hidden}
.loginContent .loginContentdiv{height: 410px;margin-top: -5px;}
.loginContent .loginArea{height: 410px;}
.nav_login{height: 67px;padding-top: 9px;}
.marginlt50{margin-left: 5px;}	
.marginleft{left: 445px;}	
}

.bgck{ margin:0 auto; width:610px; overflow:hidden}
.bgck div{ margin-top:20px}
.bgck label:first-child{ vertical-align:top}
.bgck input{ border:1px solid #BFBFBF; border-radius:0; width:161px; height:23px; line-height:23px;}
.tjfxbimg{ border:2px solid #F8F8F8; width:155px; height:110px; display:inline-block;}
.bgck .intxt{ width:280px; height:51px;border:1px solid #BFBFBF; border-radius:0;}

.cbtn-black{ background:#E8614B; text-align:center; line-height:26px; height:26px; width:105px; display:block; color:#FFF; font-size:14px; border-radius:5px; margin:30px 0px 0 158px;}
.btnsaveblack{background:#1CA89F; text-align:center; line-height:26px; height:26px; width:105px; display:block; color:#FFF; font-size:14px; border-radius:5px; margin:30px 0px 0 158px;}
.red{ color:#F00; padding-left:10px}
.bdsc{background:#1EC59A; text-align:center; line-height:26px; height:26px; width:78px;color:#FFF; font-size:14px; border-radius:5px; display:inline-block; margin-left:10px }
.bgck input.bgglinput{ border:none; border-bottom:1px solid #bfbfbf ; width:165px; margin-right:30px;}
.bgcolor{ color:#1da89e }
.bdsc1{background:#F2914A; text-align:center; line-height:26px; height:26px; width:78px;color:#FFF; font-size:14px; border-radius:5px; display:inline-block; margin-left:10px }
.bdsc2{background:#1ca89f; text-align:center; line-height:26px; height:26px; width:78px;color:#FFF; font-size:14px; border-radius:5px; display:inline-block; margin-left:10px }
.bdsc3{background:#BCC0C1; text-align:center; line-height:26px; height:26px; width:78px;color:#FFF; font-size:14px; border-radius:5px; display:inline-block; margin-left:10px }


/*分享食谱*/	
.fxspdiv{ line-height:30px; font-size:12px;width:90%; margin:0 auto;}
.bgfxspdiv{ background:#efefef; border-bottom:1px solid #c6c6c6;font-size:12px; width:90%; margin:0 auto;}
.fxspdiv1{  border-bottom:1px solid #c6c6c6;font-size:12px; width:90%; margin:0 auto; }
.fxspdiv2{width:90%; margin:20px auto; text-align:center }
.fxsp_bgwt {border-collapse:collapse;   margin:0px auto; width:90% ;border-left:#A9A9A9 solid 1px;border-top:#A9A9A9 solid 1px; color:#323232; clear:both }
.fxsp_bgwt td{white-space: nowrap;  line-height:30px; height:30px; font-size:12px; text-align:center;  border-right:#A9A9A9 solid 1px;border-bottom:#A9A9A9 solid 1px;  }
.cqright_bgwtar1{ text-align:center; background:#1CC599; color:#fff; height:23px; line-height:23px; cursor: pointer; text-align:center; font-size:12px;}
.fxsp_zhlb{margin:20px auto 10px auto; width:90% ; font-size:14px; line-height:30px;}

/*加入设置*/
.hnave{ background:#E5FBF9; height:42px; text-align:center; color:#026b64; font-size:18px; line-height:42px;}
.jr_div{ width:498px; margin:10px auto; overflow:hidden}
.jt_div{ width:309px; margin:10px auto; overflow:hidden}
.jr_div p,.sj_div p,.sr_div p,.jt_div p{ margin-top:15px; clear:both}
.jr_div span{ text-align:right; width:150px; display:inline-block; font-size:16px;}
.jr_div  input{
    border: 1px solid #BFBFBF;
    height:30px;
    line-height: 30px;
    padding-left: 3px;
	width:342px;
	border-radius:0;
	font-size:14px
}

.sj_div input,.sr_div input{
    border: 1px solid #BFBFBF;
    height: 25px;
    line-height: 25px;
    padding-left: 3px;
	width:342px;
	border-radius:0;
	font-size:14px
}
.btn-gree{
	background:#1da89e ;
    height: 32px;
    line-height: 32px;
    padding-left: 3px;
	width:342px;
	border-radius:3px; float:right; text-align:center; color:#FFF }
.jr_div input[type="radio"]{ width:13px;}
.jr_div p.csdiv{ color:#323232; font-size:12px; width:342px; float:right; margin:5px 0 20px 0}
.sr_div{ width:590px; margin:10px auto 0 auto; overflow:hidden; }
.jt_div p.csdiv1{ color:#f4a26c; font-size:12px; float:right; margin:5px 0 20px 0; font-weight:bold}
.jt_div input{ border:1px solid #bfbfbf; border-radius:0;}
.tjbtn-gree{
	background:#1da89e ;
    height: 30px;
    line-height: 30px;
    margin-right:10px;
	width:80px;
	border-radius:3px;  text-align:center; color:#FFF; font-size:14px; display:inline-block }
	
.tjbtn-gree1{
	border:1px solid #1da89e ;
	border-radius:3px;
    height: 28px;
    line-height: 28px;
	width:80px;
    text-align:center; color:#1da89e;font-size:14px; display:inline-block }
	
.quexiaobtn{
	background:#d2d2d2 ;
    height: 30px;
    line-height: 30px;
    margin-right:10px;
	width:80px;
	border-radius:3px;  text-align:center; color:#0d0d0d; font-size:14px; display:inline-block }
.edpossword{
	background:#f19149 ;
    height: 30px;
    line-height: 30px;
    margin-right:28px;
	width:80px;
	border-radius:3px;  text-align:center; color:#fff; font-size:14px; display:inline-block; margin-left:20px }
.deletebtn{
	background:#da4a4c ;
    height: 30px;
    line-height: 30px;
    margin-right:28px;
	width:80px;
	border-radius:3px;  text-align:center; color:#fff; font-size:14px; display:inline-block }
/*申请加入*/	
.sj_zdiv{ width:95%; margin:20px auto; padding:10px 0 30px 0 ; overflow:hidden; border:1px solid #1da89e }
.sj_div{ width:559px; margin:10px auto; overflow:hidden}
.sj_div span{ text-align:right; width:140px; display:inline-block; font-size:16px;}
.sj_div u,.yqtzmaindiv u,.yqtz_zleftDiv u,.yqtzmaindiv1 u,.yqtz_tyq u,.jgd_rl3 u,.fxsp_bgwt u,.fxsp_zhlb u{ background:url(../images/zicon.png) no-repeat; }
.sj_div u.ico01{ background-position:-4px -5px; display:inline-block; width:20px; height:20px}
.sj_div u.ico02{ background-position:-38px -5px; display:inline-block; width:20px; height:20px}
.sj_div u.ico03{ background-position:-66px -5px; display:inline-block; width:20px; height:20px}
.yqtzmaindiv u.ico04{ background-position:-2px -31px; display:inline-block; width:50px; height:39px; cursor:pointer}
.yqtzmaindiv u.ico05{ background-position:-55px -31px; display:inline-block; width:50px; height:39px; cursor:pointer}
.yqtzmaindiv u.ico06{ background-position:-110px -31px; display:inline-block; width:47px; height:39px; cursor:pointer}
.yqtzmaindiv u.ico061{ background-position:-157px -31px; display:inline-block; width:47px; height:39px; cursor:pointer}
.yqtz_zleftDiv u.ico07{ background-position:-4px -94px; display:inline-block; width:25px; height:39px; cursor:pointer}
.yqtzmaindiv1 u.ico08{ background-position:-34px -94px; display:inline-block; width:25px; height:39px; cursor:pointer}
.yqtzmaindiv1 u.ico09{ background-position:-164px -100px; display:inline-block; width:25px; height:39px; cursor:pointer}
.yqtzmaindiv1 u.ico10{ background-position:-58px -95px; display:inline-block; width:25px; height:39px; cursor:pointer}
.yqtz_zleftDiv u.ico11,.jgd_rl3 u.ico11{ background-position:-118px 3px; display:inline-block; width:25px; height:31px; cursor:pointer}
.yqtz_tyq u.ico12{ background-position:-127px -176px; display:inline-block; width:25px; height:31px; cursor:pointer}
.yqtz_tyq u.ico13{ background-position:-147px -176px; display:inline-block; width:25px; height:31px; cursor:pointer}
.yqtz_tyq u.ico121{ background-position:-192px -176px; display:inline-block; width:25px; height:31px; cursor:pointer}
.yqtz_tyq u.ico14{ background-position:-166px -176px; display:inline-block; width:25px; height:31px; cursor:pointer}
.yqtz_tyq u.upico15{ background-position:-37px -127px; display:inline-block; width:35px; height:31px; cursor:pointer}
.yqtz_tyq u.downico15{ background-position:-2px -131px; display:inline-block; width:35px; height:21px; cursor:pointer}
.yqtz_tyq u.upico16{ background-position:-104px -182px; display:inline-block; width:30px; height:31px; cursor:pointer}
.yqtzmaindiv1 u.upico17{ background-position:-72px -135px; display:inline-block; width:65px; height:31px; cursor:pointer}
.jgd_rl3 u.ico18{ background-position:-140px -136px; display:inline-block; width:25px; height:31px; cursor:pointer}
.fxsp_bgwt u.upico19{ background-position:0px -70px; display:inline-block; width:26px; height:31px; cursor:pointer}
.fxsp_bgwt u.upico20{ background-position:-83px -72px; display:inline-block; width:26px; height:31px; cursor:pointer}
.fxsp_bgwt u.upico21{ background-position:-112px -72px; display:inline-block; width:26px; height:31px; cursor:pointer}
.fxsp_zhlb u.upico22{ background-position:-173px -72px; display:inline-block; width:26px; height:31px; cursor:pointer; float:right}
.edit{ color:#6b6b6b}
.sj_div label{ display:inline-block; width:56px; height:30px; line-height:30px;  font-size:14px; color:#FFF; border-radius:3px; margin-left:6px; padding-left:8px; cursor:pointer}
.sj_div label.yellow{background:#ABD46C;}
.sj_div label.org{background:#F2914A;}
.yqtzmaindiv label{ display:inline-block; line-height:20px; margin-bottom:10px;}
.yaoqing{ color:#dddddd; padding:10px 0px 10px 30px; overflow:hidden; }
.yaoqing_right{ padding-left:126px; }		
/*邀请加入通知*/	
.yqtz_rldiv{ width:90%; margin:10px  auto 0 auto; overflow:hidden }
.yqtz_rl1{background: #fff; color:#323232; height: 33px; line-height:33px; cursor: pointer; text-align:center;border:1px solid #1da89e ; overflow: visible; width:180px;display:block;font-size:14px; float:left; position:relative}
.cjhdsyys_zbg{ background:#1da89e ; line-height:20px; height:28px; color:#FFF;}
.yqtz_rl2{background:#1da89e ; color:#fff; height: 33px; line-height:33px; cursor: pointer; text-align:center;border:1px solid #1da89e ; overflow: visible; width:180px; display:block;font-size:16px;float:left; border-right-style:none; position:relative }
.ssjh_rl1.hover{background:#1da89e ;color:#fff; height: 33px; line-height:33px; cursor: pointer; text-align:center;border:1px solid #1da89e ; overflow: visible; width:180px; display:block;font-size:16px;float:left }
.yqtz_botdiv{ border:1px solid #BFBFBF; overflow:hidden; margin:0 auto 10px auto; width:90%; padding-bottom:20px;  }
.yqtzmaindiv.znone{ border-bottom:none}
.yqtzmaindiv img{float: left;
    height:75px;
    margin:10px 0 5px 0;
    vertical-align: middle;
    width: 75px;
}
.yqtzmaindiv h3{ display:block; float:left; font-size:18px;  width:70%; margin-top:15px; height:30px;}
.yqtzmaindiv p{display:block; float:left; font-size:14px; width:80%}
.yqtzmaindiv p span{ width:30%; display:inline-block}
.yqtzmaindiv1{ margin:0 auto; border-bottom:1px solid  #d6d6d6; overflow:hidden; width:100%; color:#323232;line-height:30px; padding:20px 0 20px  40px; }
.yqtzmaindiv1 p{display:block; float:left; font-size:14px; width:100%}
.yqtzmaindiv1 p span{ width:33%; display:inline-block; float:left}
.yqtz_zleftDiv{ width:100%; margin:10px  auto 0 auto; overflow:hidden  }
.yqtz_zleftDiv span.jgd_rl{background:#fff; height:34px; line-height:34px; cursor: pointer; text-align:center;border:1px solid #1da89e; overflow: visible;  display:inline-block;font-size:16px;float:left; width:107px;
 }
 .jt_zleftDiv span.jgd_rl{background:#fff; height:34px; line-height:34px; cursor: pointer; text-align:center;border:1px solid #1da89e; overflow: visible;  display:inline-block;font-size:16px;float:left; width:172px;
 }
 
 .jt_zleftDiv{ width:350px; margin:10px 0; overflow:hidden; float:right}
 
.yqtz_zleftDiv span:first-child,.jt_zleftDiv span:first-child{
	border-left-color: #1da89e;
	border-radius:5px 0 0 5px;
}

.yqtz_zleftDiv span:last-child,.jt_zleftDiv span:last-child {
	border-radius: 0 5px 5px 0;
}
 
.yqtz_zleftDiv span.jgd_rl.currebg,.jt_zleftDiv span.jgd_rl.currebg,.yqtz_zleftDiv span.jgd_rl1.currebg,.yqtz_zleftDiv span.jgd_rl2.currebg{background:#1da89e ; color:#fff;border:1px solid #1da89e ;}

/*未搜索幼儿园*/	

.sr_div_xx{ text-align:center; padding:20px 0; color:#da5740; font-size:14px}

.sj_div select{   border: 1px solid #c3c3c3;
    border-radius: 0px;
    height: 32px;
    line-height: 32px;
    vertical-align: middle;}
	
/*邀请加入通知*/	
.yqtz_rldiv{ width:97%; margin:10px  auto 0 auto; overflow:hidden }
.yqtz_rl1{background: #fff; color:#1ca89f; height: 33px; line-height:33px; cursor: pointer; text-align:center;border:1px solid #1da89e ; overflow: visible; width:180px;display:block;font-size:14px; float:left }
.cjhdsyys_zbg{ background:#1da89e ; line-height:20px; height:28px; color:#FFF;}
.yqtz_rl2{background:#1da89e ; color:#fff; height: 33px; line-height:33px; cursor: pointer; text-align:center;border:1px solid #1da89e ; overflow: visible; width:180px; display:block;font-size:16px;float:left; border-right-style:none }
.ssjh_rl1.hover{background:#1da89e ;color:#fff; height: 33px; line-height:33px; cursor: pointer; text-align:center;border:1px solid #1da89e ; overflow: visible; width:180px; display:block;font-size:16px;float:left }
.yqtz_botdiv{ border:1px solid #BFBFBF; overflow:hidden; margin:0 auto 10px auto; width:97%; padding-bottom:0px;  }
.yqtzmaindiv{ margin:0 auto;  overflow:hidden; color:#323232;line-height:30px; padding:0px 0 0 100px;}
.yqtzmaindiv.znone{ border-bottom:none}
.yqtzmaindiv img{float: left;
    height:75px;
    margin:10px 0 5px 0;
    vertical-align: middle;
    width: 75px;
}
.rsbg{ background:#f2914a; display:block; text-align:center; color:#fff; font-size:12px; position:absolute; border-radius:4px; right:10px; top:0; width:30px; line-height:22px; height:22px; cursor:pointer}
.yqtzmaindiv h3{ display:block; float:left; font-size:18px;  width:70%; margin-top:15px; height:30px;}
.yqtzmaindiv p{display:block; float:left; font-size:14px; width:80%}
.yqtzmaindiv p span{ width:30%; display:inline-block}

.yqtz_zleftDiv{ width:100%; margin:0px  auto 0 auto; overflow:hidden; padding-top:10px;  }
.yqtz_zleftDiv span.jgd_rl{background:#1ca89f; height:34px; line-height:34px; color:#fff; cursor: pointer; text-align:center; overflow: visible;  display:inline-block;font-size:16px;float:left; width:107px; border-radius:5px}

.yqtz_zleftDiv span.jgd_rl1{background:#da4a4c; height:34px; line-height:34px; color:#fff; cursor: pointer; text-align:center; overflow: visible;  display:inline-block;font-size:16px;float:left; width:107px; border-radius:5px; margin-left:20px;}
.yqtz_zleftDiv span.jgd_rl2{background:#1ca89f; height:31px; line-height:31px; color:#fff; cursor: pointer; text-align:center; overflow: visible;  display:inline-block;font-size:14px;float:left; width:98px; border-radius:5px}
.yqtz_zleftDiv span.bjgd_rl2{background:#1ca89f; height:31px; line-height:31px; color:#fff; cursor: pointer; text-align:center; overflow: visible;  display:inline-block;font-size:14px;float:left; width:98px; border-radius:5px}

.yqtz_zleftDiv span.greyjgd_rl{background:#d9d9d9; height:34px; line-height:34px; color:#fff; cursor: pointer; text-align:center; overflow: visible;  display:inline-block;font-size:16px;float:left; width:107px; border-radius:5px}

.yqtz_zleftDiv span.greyjgd_rl1{background:#d9d9d9; height:34px; line-height:34px; color:#fff; cursor: pointer; text-align:center; overflow: visible;  display:inline-block;font-size:16px;float:left; width:107px; border-radius:5px; margin-left:20px;}

.jgd_rl3{background:#1ca89f; height:31px; line-height:31px; color:#fff; cursor: pointer; text-align:center; overflow: visible;  display:inline-block;font-size:14px;width:98px; border-radius:5px}
.jgd_rl4{background:#da494c; height:35px; line-height:35px; color:#fff; cursor: pointer; text-align:center; overflow: visible;  display:inline-block;font-size:16px;width:173px; border-radius:5px; margin:0 auto}

.jt_zleftDiv span.jgd_rl{background:#fff; height:34px; line-height:34px; cursor: pointer; text-align:center;border:1px solid #1da89e; overflow: visible;  display:inline-block;font-size:16px;float:left; width:172px;
 }
.jt_zleftDiv{ width:350px; margin:10px 0; overflow:hidden; float:right}
.bottombr{    border-bottom: 1px solid #d6d6d6; padding-left:40px; padding-bottom:10px}
.bottomxx{    border-bottom: 1px dashed #d6d6d6; width:95%; margin:20px auto 20px auto; padding-bottom:20px}

/*.yqtz_zleftDiv span:first-child,.jt_zleftDiv span:first-child{
	border-left-color: #1da89e;
	border-radius:5px 0 0 5px;
}
*/
/*.yqtz_zleftDiv span:last-child,*/
.jt_zleftDiv span:last-child {
	border-radius: 0 5px 5px 0;
}
 
.yqtz_zleftDiv span.jgd_rl.currebg,.jt_zleftDiv span.jgd_rl.currebg,.yqtz_zleftDiv span.jgd_rl1.currebg,.yqtz_zleftDiv span.jgd_rl2.currebg{background:#1da89e ; color:#fff;border:1px solid #1da89e ;}
.yqtz_tyq{ overflow:hidden; height:31px; line-height:31px; font-size:14px; color:#323232; padding:10px 20px 10px 40px; }
.yqtz_tyq span{ display:block; float:left; }
.yqtz_tyq span.lm1{ width:60%;}
.yqtz_tyq span.lm2{ width:20%;}
.yqtz_tyq img{ cursor:pointer}
.yqtz_tytyq{ overflow:hidden; width:100%;border-bottom: 1px solid #d6d6d6;}
.yqtzred{ color:#da4a4c}
.yqtzgreen{ color:#abd46e}
.yqtzzred{ color:#ff0000}
.yqtzblue{color:#0492e3; font-size:12px; padding-left:145px; display:block; margin-top:5px}

.yqtzmaindiv1 img{cursor:pointer}


/*日历增加图标*/
.cal-img{cursor:pointer;display: inline-block;width: 32px;position: relative;}
.cal-img span{border-radius:5px;position: absolute;right: -4px;bottom: 0;min-width: 15px;height: 15px;line-height:15px;text-align:center;font-size: 12px;color: #fff;padding:0 1px;}
.cal-img span.red-span{background:#E7614B;}
.cal-img span.oran-span{background:#ffa019;}
.cal-img i{position:absolute;top:0;left:0;}
.mark-span{background:#FF6C6C;top:-3px;right:-3px;border-radius:5px;}
.label-section{cursor:pointer;width:43%;height:24px;position:relative;display:inline-block;margin-left:5px;}
.askleave-note,.medicine-note,.birth-note ,.overdue-note1,.overdue-note2,.overdue-note3{width:100%;height:20px;line-height: 20px;font-size: 12px;text-align: center;float: left;}
.askleave-note span,.medicine-note span,.birth-note span,.overdue-note1 span,.overdue-note2 span,.overdue-note3 span{display:inline-block;text-align: left;font-size:12px;vertical-align:top;width: 65%;}
/*.askleave-note label{color:#E31E0B;}*/
/*.medicine-note label{color:#00ACD2;}*/
/*.birth-note label{color:#FF1BBA;}*/
.overdue-note1,.overdue-note2,.overdue-note3,.askleave-note,.medicine-note,.birth-note{font-size:0;overflow:hidden;cursor:pointer;border-radius: 2px;text-align: left;}
.askleave-note{background: #1e8ef2;color: #ffffff;}
.medicine-note{background: #fd9b5b;color: #ffffff;}
.birth-note{background: #f07d73;color: #ffffff;}
.overdue-note1{background: #1e8ef2;border: none;color: #ffffff;}
.overdue-note2{background: #fd9b5b;border: none;color: #ffffff;}
.overdue-note3{background: #f07d73;border: none;color: #ffffff;}
.overdue-note4{background: #00b093;border: none;color: #ffffff;}
.overdue-note1 .span-right,.overdue-note2 .span-right,.overdue-note3 .span-right{vertical-align:top;float: right;width: 35%;text-align: right;}
.askleave-note .span-right,.medicine-note .span-right,.birth-note .span-right{vertical-align:top;float: right;width: 35%;text-align: right;}
.divremind>div{margin-left:2px;overflow:hidden;cursor:pointer;position:relative;}
.divremind .label-btn{position:absolute;right:-6px;top:-6px;}
.lbnum{margin-right: 8px;}
#lanyuyue{font-weight:bold;}
/*查看宝宝生日弹框*/
.remind-left{width: 59%;padding-bottom: 20px;display: inline-block;}
.remind-left h5{padding-left:15px;padding-right:15px;height: 48px;line-height: 48px;}
.remind-left h5 span{color: #ff620c;}
.remind-left h5 label{float: right;}
.remind-left ul{height: 290px;border-bottom: 1px solid #ddd;overflow-y: scroll;}
.remind-left ul li{cursor:pointer;border:1px solid #E7E7E7;margin:0 15px 10px 15px;position: relative;padding-right: 15px;font-size:14px ;}
.remind-left ul li .rem-date{color:#F5602B;position: absolute;left: 10px;top:50%;margin-top:-7px;line-height:14px;height: 14px;}
.remind-left ul li.rem-update{border: 2px solid #FE632D;position: relative;}
.rem-update .sure-img{position: absolute;bottom: 0;right: 0;}
.remind-left ul li.rem-noupdate{position: relative;}
.remind-left ul li.rem-noupdate .rem-date{color: #A6A6A6;}
.rem-noupdate .bir-out{line-height:15px;height:65px;padding-top:3px;text-align:center;position:absolute;right:0;top:0;background: #DCDCDC;width: 25px;display: inline-block;color: #A9A9A9;}
.inp-txt{border: 1px solid #D8D8D8;margin-left: 15px;}
.inp-txt textarea{width: 96%;height: 100px;border: none;margin-top: 5px;margin-bottom: 10px;}
.inptxt-con{text-align: center}
.inptxt-con .school-name{text-align: right;margin-right: 10px;font-size: 14px;}
.send-bottom{margin-top:20px;padding:0 10px 0 10px;height: 35px;line-height:35px;border-top: 1px solid #D8D8D8;font-size: 14px;}
.send-bottom .del-btn{float: right;color: #40AAF8;text-decoration: underline;}
.mark-btn1,.mark-btn2{color:#fff;border-radius:0 20px 20px 0;text-align:center;font-size:12px;height: 24px;line-height: 24px;position: absolute;padding: 0 5px;}
.mark-btn1{background: #1CA89D;}
.mark-btn2{background: #CCCCCC;}
.remind-right{padding-top:20px;width: 40%;vertical-align:top;display: inline-block;}

.rem-list{margin-left:50px;height: 68px;}
.rem-list input{vertical-align: top;margin-top:27px;}
.rem-list dl,.rem-list .rem-right{display: inline-block;}
.rem-list dl dt,.rem-list dl dd{display: inline-block;}
.rem-list dl dt img{width: 50px;height: 50px;border-radius: 50%;margin-top:9px;}

.rem-list dl{height: 68px;}
.rem-list dl dd{margin-left:10px;padding-top:10px;font-size:14px;height: 58px;vertical-align: top;}
.rem-list dl dd h6,.rem-list dl dd p{height: 24px;line-height: 24px;}
.rem-list dl dd p img{margin-left: 10px;vertical-align:top;}
.rem-list dl dd p span{color: #999;}
.rem-list .rem-right{float: right;margin-top:19px;margin-right:15px;}
.rem-list .rem-right img{width: 30px;height: 30px;border-radius: 50%;margin-left: 5px;}
.rem-list .weixin-img img{width: 41px;height: 25px;border-radius: 0;}

.exp-note{margin:0 20px;font-size: 14px;height: 40px;line-height: 40px;}
.send-top{margin: 10px 15px;height: 30px;}
.send-top label{float: right;}
.send-top label span{cursor:pointer;font-size: 14px;color: #1CA89D;text-decoration: underline;margin-right: 10px;}
.talk-div{width: 210px;margin: auto;margin-top: 115px;}
.talk-list{font-size: 0;margin-bottom: 15px;}
.talk-list .pho-img{width: 32px;height: 32px;vertical-align: top;}
.talk-list .talk-txt{position: relative;padding:10px;margin-left: 13px;font-size:12px;text-align:left;background: #fff;display: inline-block;min-width:10px;max-width: 145px;min-height:12px;border-radius: 3px;}
.talk-list .talk-txt p{line-height: 18px;}
.talk-list .talk-txt .note-name{text-align: right;margin-top: 4px;}
.triangle-left {width: 0;height: 0;border-top: 6px solid transparent;border-right: 8px solid #fff;border-bottom:6px solid transparent;position: absolute;left:-8px;top:9px;}
.triangle-right {width: 0;height: 0;border-top:  6px solid transparent;border-left:  8px solid #fff;border-bottom:  6px solid transparent;position: absolute;right:-8px;top:9px;}
.mark-label{margin-left:2%;vertical-align:top;margin-top:4px;background: salmon;display: inline-block;width: 150px;height: 60px;background: #F6ECF2;border-left: 5px solid #C27CA3;color: #C27CA3;font-size: 14px;}
.mark-label p{margin-left: 70px;width: 100px}
/*生日提醒*/
.remind-box2 .rem-top{font-size: 14px;text-align: center;margin-top:20px;margin-bottom: 30px;}
.remind-box2 .rem-top .green-span{color: #1da89e;}
.remind-box2 .rem-top .red-span{color: #ff3b2c;}

.remcon{font-size: 14px;padding:0 30px 30px 30px;border-bottom: 1px solid #ccc}
.remcon .remcon-left,.remcon .remcon-right{display: inline-block;}
.remcon .remcon-left{vertical-align: top;width: 65%;margin-top:20px;}
.remcon .remcon-left h4{margin-bottom:10px ;}
.remcon .remcon-right{width: 130px;height: 130px;text-align: center;position: relative;}
.remcon .remcon-right img{margin-top:10px;}
.remcon .remcon-right span{position: absolute;display: inline-block;width: 18px;height: 18px;}
.remcon .remcon-right span.rem-span1{left:0;top:0;border-left: 3px solid #ff3b2c;border-top: 3px solid #ff3b2c;}
.remcon .remcon-right span.rem-span2{right: 0;top:0;border-right: 3px solid #ff3b2c;border-top: 3px solid #ff3b2c;}
.remcon .remcon-right span.rem-span3{left:0;bottom: 0;border-left: 3px solid #ff3b2c;border-bottom: 3px solid #ff3b2c;}
.remcon .remcon-right span.rem-span4{right:0;bottom: 0;border-right: 3px solid #ff3b2c;border-bottom: 3px solid #ff3b2c;}


/*订单*/
.order-div,.order-con{width: 96%;margin: auto;min-width: 1000px;}
.order-div{padding-top:20px;padding-bottom:20px;height: 160px;font-size: 0;border: 1px solid #D8D8D8;}
.order-top{margin-left:50px;position:relative;font-size: 0;vertical-align:top;width: 40%;display: inline-block;height: 160px;}
.order-top dl{width: 106px;margin-left: 50px;text-align: center;font-size: 14px;display: inline-block;}
.order-top dl dd{margin-top: 8px;}
.order-right{position:absolute;bottom:24px;margin-left:20px;font-size: 16px;display: inline-block;vertical-align: bottom;}
.order-right h5{color: #323232;font-size: 16px;}
.order-right h5 i{color: #e7001d;}
.order-right a{margin-top:16px;width: 180px;height: 35px;line-height:35px;text-align:center;color:#fff;background: #FC9825;font-size: 16px;display: inline-block;border-radius: 2px;}
.order-right .txt1,.order-right .txt2{cursor:pointer;display:inline-block;font-size: 14px;margin-top:10px;}
.order-right .txt1{width: 106px;text-align:center;}
.order-right .txt2{vertical-align:top;text-decoration:underline;color: #677DF5;}	
.order-right .txt2 img{vertical-align: top;}	
.order-right a.order-dda2{background: none;color:#FC9825 ;border: 1px solid #FC9825;}

.order-tit{margin-top:10px;text-align:center;color:#fff;background: #1CA89F;width: 100px;height: 30px;line-height: 30px;display: inline-block;}
.order-list{background:#F8F8F8;border: 1px solid #d8d8d8;/*position: relative;*/}
.order-list img{margin: auto;position: absolute;top:50%;left:50%;margin-left: -102px;margin-top:-94px;}

/*新增发票信息*/
.addbill{font-size: 14px;margin-top:20px;margin-bottom:10px;}
.addbill ul{width: 400px;margin: auto;}
.addbill ul i{color: #FF3F3F;height: 28px;line-height: 28px;}
.addbill ul li{min-height: 37px;}
.addbill ul li span{display: inline-block;width: 80px;text-align: right;}
.addbill ul li div{width: 300px;display: inline-block;}
.addbill ul li input[type="radio"]{margin-top: -1px;}
.addbill ul li input[type="text"]{border: none;border-bottom: 1px solid #ccc;border-radius: 0;width: 280px;}
.addbill ul li select{vertical-align:top;margin-right:5px;width:88px;border: 1px solid #ccc;color: #666;border-radius: 0;}

/*申请认证、开通正式版弹框*/
.apply-con{margin:20px;}
.apply-year{width: 500px;margin: auto;}
.apply-year ul{margin-left:20px;margin-top:10px;width: 270px;display: inline-block;}
.apply-year ul li{cursor:pointer;background:#B0AFB5;font-size:14px;width: 120px;height: 30px;line-height:30px;color:#fff;text-align:center;display: inline-block;margin:5px;}
.apply-year ul li.sel-tit{background: #FE9900;}
.apptop-right{display: inline-block;vertical-align: top;margin-left: 20px;margin-top:10px;}
.apptop-right a{margin-top:10px;display: inline-block;background: #1CA89D;width: 105px;height: 30px;line-height:30px;text-align:center;border-radius: 3px;color: #fff;font-size: 14px;}
.apptop-right p{height: 34px;line-height:34px;margin-top:1px;}
.apptop-right .pri-num{font-size: 24px;color: #FE9900;vertical-align: bottom;margin-top:-5px;}

.apptxt-h5{height: 40px;line-height: 40px;border-bottom: 1px solid #D7D7D7;}
.apptxt-h5 span{color: #F75151;}
.privi ul{width: 500px;margin: auto;font-size: 0;}
.privi ul li{margin-top:20px;text-align:center;vertical-align:top;display: inline-block;margin-left:11px;margin-right:11px;width: 20%;display: inline-block;font-size: 14px;}
.privi ul li p{text-align: left;}
.privi ul li img{margin-bottom: 10px;}
.appimg-list{position:relative;width: 500px;margin: auto;text-align: right;margin-top: 20px;}
.appimg-list img{margin-top: 5px;}
.advan-ident{position: absolute;top: 0px;left: 0;}


/*确认订单信息*/
.confirmbill{margin:0 20px;font-size: 14px;min-width: 780px;}
.firmtop-tit{font-size: 16px;margin-bottom: 15px;margin-top: 15px;text-align:left;}
.bill-mess{border-bottom: 1px solid #F2F2F2;padding-bottom: 13px;}
.bill-mess p span{margin-bottom:5px;display: inline-block;width: 27%;margin-left: 35px;}
.bill-mess p span i{color: #8e8e8e;}
.bill-mess p .actual-pay{margin-top:-12px;float: right;text-align: right;}
.bill-mess p .actual-pay i{font-size: 24px;color: #FC9825;margin-right: 20px;}
.bill-mess .sel-radio label{margin-left: 30px;}
.bill-mess .sel-radio label input{margin-top:-2px;}
.order-mess{margin: 15px auto;width: 350px;}
.order-mess li{line-height: 25px;}
.order-mess li span{display: inline-block;width: 69%;vertical-align: top;}

.order-mess2{margin: 15px auto;width: 200px;font-size: 14px;}
.order-mess2 li{line-height: 25px;}
.order-mess2 li span{display: inline-block;vertical-align: top;}
.order-mess2 label{display: inline-block;width:50%;text-align:right;}

.bill-code{/*width: 500px;*/text-align: center;margin:20px auto;}
.bill-code .pay-codecon{vertical-align:top;cursor:pointer;position:relative;border:1px solid #ddd;width:197px;height:70px;text-align: center;display: inline-block;margin-left: 10px;margin-right: 10px;}
.bill-code .pay-codecon .pay-txt{margin-left:-40px;margin-top:15px;font-size: 18px;color: #999;height: 40px;line-height: 40px;vertical-align: top;display: inline-block;}
.bill-code .pay-codecon img{margin-top: 15px;float: left;margin-left: 15px}
.bill-code .pay-codecon.pay-way{border: 2px solid #ff632d;}
.bill-code .pay-codecon.pay-way .pay-txt{color: #FF632D;}
.bill-code .pay-codecon div{border-top: 1px solid #F2F2F2;padding-top: 23px;margin-top: 5px;}
.bill-code .sure-img{bottom:0;right:0;position: absolute;}
.bill-code .pay-codecon .yl-img{position: absolute;top: 55px;right: -100px;}
.opr-btn{border-radius:3px;background: #1CA89D;min-width: 80px;padding:0 20px;height: 30px;line-height:30px;text-align:center;display: inline-block;color: #fff;}
.pay-btn{text-align: center;}
.pay-btn a{margin-left: 5px;margin-right: 5px;}
.opr-btn-cancel{border-radius: 3px;background: #F71A1A;min-width: 80px;padding:0 20px;height: 30px;line-height: 30px;text-align: center;display: inline-block;color: #fff;}
/*免费年攻略*/
.freeyear{left:-191px;background: url(../image/rec_bg.png) no-repeat;width: 464px;height: 375px;position: absolute;z-index: 999;display: none;}
.freeyear .free-con{padding: 45px 30px 20px 30px;}
.free-con h5{font-size: 16px;text-align: center;}
.free-con h5 span{color: #e7604a;}
.free-con p{margin-top:10px;margin-bottom: 10px;line-height: 25px;font-size: 14px;}
.free-con .illu-txt{text-align: center;color: #F8504F;}
.phone-num{cursor:pointer;margin-top:10px;width: 245px;height: 30px;display: inline-block;background: #E7604A;color: #fff;border-radius: 2px;line-height: 30px;text-align: center;}

/*认证流程*/
.apprflow{z-index:99;display:none;background: url(../image/rec_bg.png)no-repeat;background-size:570px 375px;width: 570px;height: 375px;position: absolute;left:-240px;}
.apprflow img{margin-top: 40px;margin-left: 20px;}
.appr-con{padding: 40px 25px 20px 40px;}
.appr-left{vertical-align: top;margin-top: 10px;}
.appr-left div{display: inline-block;margin-top: 20px;}
.appr-right{margin-left: 5px;}
.appr-left,.appr-right{display: inline-block;}
.appr-right p{text-align:center;height:30px;line-height:30px;text-decoration: underline;color: #BD341C;}
.appr-right p img{vertical-align: top;margin-top:3px;margin-right: 15px;}


/*确认订单信息（微信）*/
.order-code{font-size:14px;width: 130px;margin: auto;padding-bottom: 20px;}
.code-con{position:relative;width: 130px;height:130px;margin: auto;text-align: center;}
.code-con img{margin-top:10px}
.code-con span{position:absolute;width: 14px;height: 14px;display: inline-block;}
.code-con span.span1{top: 0;left: 0;border-top: 3px solid #ff3b2c;border-left: 3px solid #ff3b2c;}
.code-con span.span2{top: 0;right: 0;border-top: 3px solid #ff3b2c;border-right: 3px solid #ff3b2c;}
.code-con span.span3{bottom: 0;left: 0;border-bottom: 3px solid #ff3b2c;border-left: 3px solid #ff3b2c;}
.code-con span.span4{bottom: 0;right: 0;border-bottom: 3px solid #ff3b2c;border-right: 3px solid #ff3b2c;}
.order-code p{margin: 10px;text-align: center}

/*确认订单信息（支付宝）*/
.zfb-con{padding: 10px;border-bottom: 1px solid #d8d8d8;margin: 0 20px;}

/*支付确认结果*/
.pay-conseque{margin: 20px;border-bottom:1px solid #E6E6E6 ;padding-bottom: 20px;}
.pay-sure h5{font-size: 16px;margin-bottom: 8px;text-indent: 9px;}
.pay-sure ul{font-size: 14px;}
.pay-sure ul li{line-height: 24px}
.pay-sure ul li span{color: #F88661;}
.pay-sure p{font-size: 14px;color: #55B6AF;margin-top: 7px;text-indent: 9px;text-decoration: underline;}
.opr-again{background: #F1F1F1;border: 1px solid #E9E9E9;color: #565656;}

/*支付成功*/
.pay-succ p{margin:86px auto 40px auto;font-size: 34px;height: 100px;text-align:center;line-height: 100px;}
.pay-succ p img{vertical-align: top;margin-right: 20px;}
.pay-succ .order-mess2 {width: 400px;font-size: 18px;}
.pay-succ .order-mess2 li{line-height: 30px;}

/*祝福模板*/
.tem-con{font-size: 14px;min-width: 600px;}
.tem-con ul{height: 330px;overflow-y: scroll;border-bottom: 2px solid #E7E7E7;}
.tem-con label{line-height:20px;padding: 0 20px;position:relative;display: inline-block;}
.tem-con label input{position: absolute;top: -5px;left: 0;}
.tem-con ul li{border:1px solid #ddd;padding: 13px 0 13px 0;margin: 0 15px 0 15px;margin-top: 12px;}
.tem-con .edit-img{float: right;}
.tem-con .edit-img img{margin-left: 10px;cursor: pointer;}
.tem-con .tem-txt{font-size: 0;}
.tem-txt ul,.tem-txt .tem-right{font-size: 14px;display: inline-block;vertical-align: top;}
.tem-txt ul{width:70%;}
.tem-txt ul li{position: relative;cursor: pointer;}
.tem-txt ul li .sure-img{position: absolute;right: 0;bottom: 0;}
.tem-txt ul li.light-li{border: 2px solid #FA612C;}
.tem-txt .tem-right{width: 30%;margin-top: 20px;}
.tem-txt .tem-right a{display: block;margin: 10px auto}
/*编辑祝福*/
.edit-con{margin:auto 15px;position: relative;}
.edit-con p{height: 44px;line-height: 44px;font-size: 14px;text-align: right;}
.edit-con p a{cursor:pointer;margin-left: 20px;text-decoration: underline}
.edit-con textarea{width: 98%;border: 1px solid #D8D8D8;height: 150px;float: right;margin-bottom: 20px;}
.wish-note{position:absolute;top:-85px;right:-83px;padding-top:9px;background: url(../image/wish_bg.png) no-repeat;width: 400px;height: 125px;background-size: 400px 120px;padding-left: 20px;}
.wish-note p{text-align: left;margin: auto;height: 30px;line-height: 30px;display: inline-block;}
.wish-note p span{margin-right: 7px;font-size: 14px;}
.wish-note p span:hover{color: #1CA89D;}
.wish-note .light-txt{color: #1CA89D;}

/*查看全园考勤资产统计图*/
.asset{margin: 10px 15px 10px 15px;}
.asset-top{height: 120px;}
.asset-right{float: right;margin-top: 10px;}
.circle1,.circle2{margin-right:15px;text-align:center;width: 96px;height: 96px;display: inline-block;}
.circle1{background: url(../image/cir01.png) no-repeat;}
.circle2{background: url(../image/cir02.png) no-repeat;}
.circle1 span,.circle2 span{font-size: 19px;display: inline-block;width: 85px;margin-top:20px;}
.circle1 p,.circle2 p{font-size: 14px;width: 60px;text-align: center;margin:4px auto 0 auto;line-height: 17px}

/*数据资产*/
.asset-con{margin: 0 10px 0 10px;min-width: 1400px;}
.asset-num h5{height: 46px;line-height: 46px;}
.asset-num h5 img{vertical-align: top;margin-top: 11px;margin-right: 8px;}
.asset-list{color:#fff;vertical-align:top;margin-right:16px;border-radius:5px;display: inline-block;width: 23%;height: 123px;}
.asset-txt{margin: auto 30px;}
.asset-list h6{text-align: center;font-size: 14px;height: 42px;line-height: 42px;}
.asset-list p{height: 30px;line-height: 30px;}
.asset-list p label{margin-right:10px;width: 47%;display: inline-block;text-align: right;font-size: 14px;}
.asset-list p span{font-size: 24px;display: inline-block;vertical-align: middle;margin-top: -2px;}
.list-end{text-align: center;}
.list-end img{margin-top: 30px;}
.list-end p{color: #617AF1;text-decoration: underline;font-size: 14px;cursor: pointer;}

.asset-txt{}
.asset-bill{vertical-align:top;margin-top: 20px;background: #fff;display: inline-block;width: 32%;height: 163px;}
.asset-bill p{height: 40px;line-height: 40px;text-align: center;border-bottom: 1px solid #DDDDDD;}
.asset-bill ul li{font-size:14px;padding-left:20px;height: 40px;line-height: 40px;border-bottom: 1px dashed #ddd;}
.asset-bill ul li span{width: 33.3%;display: inline-block;}
.asset-bill ul{overflow-y: auto;height: 122px;}
.asset-bill i{font-size: 12px;}
.pur-span{color: #8693ef;}
.ora-span{color: #FA7E4A;}
.cir-div,.cir-div2{margin:auto;text-align:center;width: 112px;height: 112px;border-radius:50% ;}
.cir-div{border: 4px solid #00d1ec;}
.cir-div2{border: 4px solid #eee;}
.cir-div div,.cir-div2 div{margin-top:35px;}
.cir-div span,.cir-div2 div{font-size: 14px;}
.cir-div span,.cir-div2 span{display: block;margin-top:3px;}
.asset-ullist{font-size: 0;margin:auto 20px;padding: 30px 0;}
.asset-ullist li{font-size:14px;text-align: center;width: 10%;display: inline-block;}
.asset-ullist p{margin-top: 10px;color: #7A7A7A;}
/*资产排行榜*/
.chart-con{padding: 20px;background: #fff;}
.chart-con h5{font-size: 14px;color: #ff620c;height: 24px;line-height: 24px;}
.chart-con h5 img{vertical-align: top;margin-right: 5px;}
.chart-con ul{margin-top: 10px;height: 354px;overflow-y:auto ;border-bottom: 1px solid #DBDBDB;}
.chart-con ul li{height: 70px;line-height: 70px;border-bottom: 1px dashed #ddd;}
.chart-con ul li img{vertical-align: top;margin-top: 10px;margin-right: 12px}
.chart-con ul li i{margin-right: 20px;font-size: 18px;}
.chart-con ul li label{font-size: 14px;}
.chart-con ul li span{float: right;color: #FC8A50;font-size: 24px;margin-right: 20px;}
.first-num{color: #7F8CE8;}
.second-num{color: #F8A30A;}
.third-num{color: #EC2B4A;}

/*员工体检提醒*/
.employ-rem{background:#fff;padding: 30px 30px 20px 30px;min-width: 350px;}
.employ-rem h5{font-size: 16px;text-align: center;}
.employ-rem h5 span{color: #ff642e;}
.employ-con{margin: 18px auto;position: relative;width: 340px;}
.employ-con .rem-img{position: absolute;top:50%;margin-top: -35px;}
.employ-con ul{margin-left: 84px;font-size: 14px;}
.employ-con ul li{line-height: 24px;}
.employ-con ul li p{display: inline-block;color: #666666;}
.employ-con ul li label{display: inline-block;width: 70px;}
.rem-note{color: #1DA89E;font-size: 12px;border-bottom: 1px solid #ccc;}
.rem-note input{margin-top: 1px;}

/*即将过期、非保护状态弹框*/
.sec-remind{background: #fff;min-width: 480px;}
.sec-remind h5{background: #F0292C;color: #fff;}
.h5-tit{height: 42px;line-height: 42px;text-align: center;font-size: 16px;}
.remind-con{margin-top: 30px;}
.remindcon-left{width: 30%;text-align: center;}
.remindcon-right{width: 60%;}
.remindcon-left,.remindcon-right{display: inline-block;vertical-align: top;}
.remindcon-right h1{font-size: 26px;margin-left: -13px;margin-bottom: 15px;}
.remindcon-right h3{font-size: 20px;color: #F02F3D;margin-bottom: 1px;}
.remindcon-right p{font-size: 14px;}
.color-txt{color: #FC0019;}
.remindcon-list{font-size: 0;margin-top:10px;padding-top: 10px;margin-bottom:30px;border-top: 1px solid #ccc;}
.remindcon-list li{height:50px;line-height:50px;font-size:14px;width: 50%;display: inline-block;}
.remindcon-list li img{margin-right:10px;width: 28px;vertical-align: top;margin-top: 8px;}

/*免疫列表弹框*/
.immune-con{min-width: 100%;}
.immune-con h4{height: 45px;line-height: 45px;padding: 0 20px;font-size: 14px;}
.immune-con h4 span{float: right;}
.immune-con h4 span i{color: #64BAB1;}
.immune-con h5{font-size: 0;padding: 0 20px;background: #E5FBF9;height: 40px;line-height: 40px;border-bottom: 3px solid #28A89D;}
.immune-con h5 span{font-size: 14px;text-align: center;display: inline-block;}
.immune-con h6{height: 40px;line-height: 40px;font-size: 14px;margin: 0 20px;}
.immune-con ul{margin: 0 20px;border-top: 1px solid #e4e4e4;border-bottom: 1px solid #e4e4e4;}
.immune-con ul li{font-size: 0;line-height: 40px;border-bottom: 1px dashed #eee;}
.immune-con ul li:last-child{border-bottom: none;}
.immune-con ul li span{font-size: 14px;display: inline-block;text-align: center;}
.immune-con ul li span i{display: inline-block;text-align: left;vertical-align: top;}
.immune-con h5 span:nth-child(1),.immune-con ul li span:nth-child(1){width: 7%;}
.immune-con h5 span:nth-child(2),.immune-con ul li span:nth-child(2){width: 23%;}
.immune-con h5 span:nth-child(3),.immune-con ul li span:nth-child(3){width: 16%;}
.immune-con h5 span:nth-child(4),.immune-con ul li span:nth-child(4){width: 31%;}
.immune-con h5 span:nth-child(5),.immune-con ul li span:nth-child(5){width: 23%;}
.immune-con ul li span:nth-child(1) i,.immune-con ul li span:nth-child(3) i,.immune-con ul li span:nth-child(5) i{text-align: center;}
.immune-con .use-note{font-size: 14px;height: 40px;line-height: 40px;margin-top: 10px;padding-left: 53px;position: relative;}
.immune-con .use-note a{color: #687EF1;text-decoration: underline;}
.immune-con .use-note img{vertical-align: top;margin-top: 10px;margin-right: 5px}
.bot-btn{text-align: center;margin-top: 20px;}
.bot-btn a{margin-left: 10px;margin-right: 10px;}
.immune-box{width: 534px;height: 435px;background: url(../image/note_bg.png) no-repeat;position: absolute;bottom: 25px;left: 0;}
.imbox-con{margin:0 30px;padding-top:35px ;}
.imbox-con h3{color: #E6695A;font-size: 16px;text-align: center;margin-bottom: 15px;}
.imbox-con p{font-size: 14px;line-height: 25px;}
.imm-txt{height: 400px;overflow:auto;}
/**/
.leadto{position: relative;padding-bottom: 30px;min-width: 470px;}
.mess-list{padding:30px 0 20px 0;width: 94%;margin: 0 auto;border-bottom: 1px solid #e5e5e5;}
.mess-list li{font-size: 0;margin: auto;line-height: 25px;width: 73%;}
.mess-list li label{text-align:right;width: 30%;display: inline-block;font-size: 14px;}
.mess-list li span{vertical-align:top;width: 70%;display: inline-block;font-size: 14px;}
.lead-btn{position:absolute;top:20px;height: 36px;line-height: 36px;width: 70px;display: inline-block;background: #FF5D5E;border-radius: 0 20px 20px 0;font-size: 14px;color: #fff;text-align: center;}

/*添加教职工信息*/
.staffme{min-width: 780px;}
.staffme .addbill{margin: 0 30px 20px 30px;padding: 20px 0 30px 0;}
.staffme .addbill ul{margin: 0;width: auto;color: #444;}
.staffme .addbill input[type="text"],.staffme .addbill select{border: 1px solid #e8e8e8;height: 30px;vertical-align: middle;}
.staffme .addbill select{width: 285px;height: 30px;line-height: 30px;border-radius: 0;}
.staffme .addbill textarea{border: 1px solid #e8e8e8;min-height: 54px;vertical-align: top;width: 277px;border-radius: 0;}
.staffme .addbill label img{vertical-align: top;margin-top: 5px;margin-left: -25px}
.line-cut{border-top:1px dashed #ddd;height: 1px;text-align: center;margin: 20px 0 30px 0;}
.line-cut span{background: #fff;margin-top: -10px;display: inline-block;padding: 0 10px;color: #999;}
.staffme .addbill ul li{height: inherit;padding: 7px 0;font-size: 0; min-height: auto;width: 45%;display: inline-block;}
.staffme .addbill ul li em{font-style: normal;}
.staffme .addbill ul li span{width: auto;font-size: 14px;text-align: left;color: #999999;}
.staffme .addbill ul li label{display: inline-block;font-size: 14px;}
.staffme .addbill ul li .idcard{margin-right:50px;vertical-align:top;display: inline-block;position: relative;width: 214px;height: 128px;}
.staffme .addbill ul li .idcard img{width: 214px;height: 128px;}
.staffme .addbill ul li i{font-style: normal;line-height: normal;}
.staffme .layui-form-item .layui-input-block{min-height: auto;margin-left: 138px;}
.staffme .layui-form-item .layui-form-label{width: 120px;color: #999999;}
.staffme .layui-form-item{margin-bottom: 9px}
.img-addcard{position: absolute;top: 50%;left: 50%;margin-top: -33px;margin-left: -33px;cursor: pointer;}
.idcard p{text-align: center;color: #49a6f4;margin-top: 10px;font-size: 14px;}
.opr-btn{cursor: pointer;}
.close-btn{cursor: pointer;background: rgba(6,6,6,0.5);top: 0;right: 0;font-size: 25px;position: absolute;top: 0;right: 0;width: 30px;height: 30px;line-height: 30px;color: #fff;text-align: center;display: inline-block;}
.select-inp+.select-inp{margin-left: 15px}
.select-inp input{margin-right: 3px}
.upload-sub{position: absolute!important;left: 50%;top: 50%;width: 50px;height: 50px;margin: -25px;}
.upload-sub .webuploader-pick{background: url(../image/addcard.png)no-repeat;width: 50px;height: 50px;padding: 0;background-size: 100%;position: absolute;top: 0;left: 0;margin: 0;}
/*编辑老师*/
.ediper{margin: 20px;min-width: 960px;}
.ediper .staff-sear input{border-radius:0 ;border: 1px solid #C3C3C3;}
.ediper .edi-btn{float: right;margin-top: 15px;}
/*疫苗补种*/
.vacc-supp{padding: 40px;min-width: 800px}
.vacc-supp h3{font-size: 17px;font-family: "仿宋";}
.vacc-supp h5{text-align: center;font-size: 16px;margin: 20px 0 50px 0;position: relative}
.vacc-supp h5 span{float: right;position: absolute;right: 0;}
.vacc-supp h5 span input{border: none;}
.vacc-supp input{border: none;border-bottom: 1px solid #333;border-radius: 0;}
.vacc-supp .lineto{border-top:1px dashed #ddd;height: 1px;text-align: center;margin: 20px 0 30px 0;}
.widmins{width: 10%;}
.widsho{width: 25%;}
.widlen{width: 80%;}
.widmin{width: 39%;}
.supp-con{width: 85%;margin:0 auto;}
.supp-con div{margin-top: 5px;line-height: 30px;}
.vacc-list{border: 1px solid #333;margin-top: 15px;}
.vacc-list tr{height: 40px;}
.vacc-list td{border-bottom: 1px solid #333;border-right:1px solid #333 ;text-align: center;}
.vacc-list input {border: none;}
.lines-to{border-bottom: 1px dashed #333;position: relative;height: 1px;margin: 70px auto;}
.lines-to span{width:180px;display:inline-block;text-align:center;background:#fff;position: absolute;top:-11px;left: 50%;margin-left: -90px;}
.date-bot{clear: both;float: right;margin-bottom: 30px;text-align: right;margin-right: 70px;}
.date-bot input{border:none;}

/*身份证照片*/
.card-div{background: #fff;min-width: 160px;text-align: center;
	position: absolute;border:1px solid #b2b2b2;top:50%;margin-top:-165px;left:50%;margin-left:-240px;}
.card-div img{width:480px;height:288px;}
.card-div h5{
	height:42px;line-height:42px;background:#f1f1f1;}
.card-div h5 span{
	float: right;margin-right:15px;}
/*查看请假情况表格*/
.pro-askleava{text-align: center;}
.pro-askleava .progress-list{width: 80px;display: inline-block;margin: 20px 30px 0 30px;}
.askleave-tab{margin: 20px;}
.askleave-tab table tr{height: 40px;line-height: 40px;text-align: center;}
.askleave-tab table{border: 1px solid #ddd;font-size: 14px}
.askleave-tab table tr.first-tr{background: #E5FBF9;border-bottom: 2px solid #28A89D;}
/*过敏管理*/
.alle-tab td{border: 1px solid #DEE7E6;text-align: center;}
.alle-tab{margin: 10px;}
.alle1{color: #FED144;}
.alle2{color: #FE980F}
.alle3{color: #FF0302;}
.alle-tab img{margin: 0 3px;margin-top: 5px;}
.alle-tab .check-label{margin:auto 10px;color: #A5A5A5;}
.check-state {margin:0 10px ;}
.check-state .tab-btn{color: #454545;}
.check-state .sel-label{float: right;}
.check-state .sel-label i{color: #1CA89D;}
.check-state .sel-label a{margin-left:15px;}
.pass-txt{color: #4CB0A8 !important;}
.unpass-txt{color: #EC563E !important;}
/*员工体检提醒*/
.sel-ul{border:1px solid #F47883;margin-top:8px;display: inline-block;vertical-align: top;height: 30px;line-height: 30px;}
.sel-ul span{padding: 0 15px;color: #F47883;display: inline-block;cursor: pointer;}
.sel-ul span.curr-btn{background: #F47883;color: #fff;}
.rem-dl{margin-left: 20px;}
.rem-dl dd img{vertical-align: top;margin-left: 10px;}
.rem-dl dd .rem-time{color: #FE632D;}
.rem-label{display: inline-block;width: 120px;margin-left:40px;vertical-align: top;height: 68px;line-height: 68px;}
.rem-label .rem-num{color: #F8612C;font-size: 24px;}
/*体检记录*/
.rem-check{margin: 15px;min-width: 750px;}
.check-top{border:1px solid #DEDEDE;padding-bottom: 15px;}
.check-top .check-pho{display: inline-block;vertical-align: top;padding: 15px;}
.check-top ul{width:80%;display: inline-block;vertical-align: top;font-size: 14px;margin-top: 15px;}
.check-top ul li{line-height: 30px;}
.check-top ul li label.short-label{display: inline-block;width: 45%;}
.check-con{font-size: 0;margin-top: 15px;}
.check-left,.check-right{display: inline-block;vertical-align: top;width: 50%;font-size: 14px;}
.add-div{clear:both;margin-top:10px;border: 1px solid #dedede;width: 98%;height: 200px;text-align: center;}
.add-div2{clear:both;margin-top:10px;border: 1px solid #dedede;width: 98%;height: 200px;overflow-y: auto;overflow-x: hidden;float: right;}
.check-left .add-div img{margin-top: 55px;cursor: pointer;}
.check-right p{height: 30px;line-height: 30px;}
.check-right p label{float: right;}
.check-right p label a{margin-left: 10px;}
.check-right .addbill ul{width: 420px;}
.check-right .addbill ul li{line-height: 30px;height: auto;color: #505050;margin-left: 20px;}
.check-right .addbill ul li span{width:100px;}
.check-right .addbill ul li textarea{width: 80%;height: 50px;border: 1px solid #dedede;}
.check-right .addbill ul li .inp-sel input{border: 1px solid #dedede;width: 78%;}
.check-right .addbill ul li .inp-sel img{margin-left: -25px;margin-top: 3px;vertical-align: top;}
.check-right .addbill ul li label{margin-right: 15px;}
.addcheck-left,.addcheck-right{display: inline-block;vertical-align: top;}
.addcheck-left{width: 28%;border: 1px solid #dedede;height: 360px;}
.addcheck-right{width: 75%;float: right;}
.addcheck-right .check-top ul{width: 75%;}
/*添加工作人员信息*/
.add-mess{background: #fff;width: 680px;margin: 0 auto;}
.add-mess .short-txt{width: 500px;margin-left: 20px;}
.add-mess .short-txt ul{margin: 0;width: 500px;}
.add-mess .short-txt ul li{margin: 0;line-height: 40px}
.add-mess .short-txt ul li span{}
.add-mess .short-txt ul li div{}
.add-mess .short-txt ul li.short-con{width: 250px;float: left;}
.add-mess .short-txt ul li.short-con div{width: 150px;}
.add-mess .short-txt .addbill ul li select{vertical-align: middle;width: 119px;border: 1px solid #DEDEDE;}
.add-mess .check-photo{display: inline-block;margin: 23px 0 0 10px;cursor: pointer;}
.add-mess .long-txt{width: 600px;margin-left: 20px;}
.add-mess .long-txt ul{margin: 0;}
.add-mess .long-txt ul li{margin: 0;line-height: 40px}
.add-mess .long-txt .addbill ul li input{width: 508px;}
.add-mess .long-txt .addbill{margin: 0;}
/*儿童服药管理*/
.show-list .staff-sear{padding-top: 10px;}
.show-list .staff-sear label{display: inline-block;min-width: 70px;margin-top: 0;}
.divtoptxt{color: #545454;margin-left: 30px}
.divtoptxt label{margin-right: 20px}
.name-btn{height: 30px;line-height: 30px;color: #fff;border: none;text-align: center;border-radius: 20px 0 0 20px;margin-top: 5px;margin-left: 10px;padding: 0 20px;font-size: 14px}
.name-btn2{background: #FB9701;display: inline-block;height: 30px;line-height: 30px;padding: 0 10px;border-radius: 0 20px 20px 0;color: #fff;}
.medi-state .addbill{margin: 0;}
.medi-state .addbill ul{margin: 0;width: 100%;}
.medi-state .addbill ul li{line-height: 30px;height: auto;}
.medi-state .addbill ul li .medibill-left{width: 120px;vertical-align: top;}
.medi-state .addbill ul li .medibill-right .medi-time{display: block;}
.medi-state .addbill ul li .medibill-right .medi-time label{margin-right: 20px;}
.medi-state .addbill ul li .medibill-right .medi-img{margin-right: 10px;margin-top: 10px;width: 140px;}
.medi-state .addbill ul{border-bottom: 1px solid #EBEBEB;padding: 10px 0;}
.medi-state .addbill ul:last-child{border-bottom: none;}
.medi-state .addbill div{width: auto;vertical-align: top}
.medi-state .addbill p{margin: 0;}
.medi-state .addbill .textarea-con{border: 1px solid #E4E4E4;width: 330px;height: 78px;vertical-align: bottom;margin-right: 20px;}
.medi-top{position: relative;}
.state-label{position: absolute;top:15px;right: 20%;}
.check-turn{float: right;margin-right: 30px;}
.check-turn img{vertical-align: top;margin-top: 13px;cursor: pointer;margin: 13px 2px;}
/*服药管理使用时间*/
.state-dl{margin:12px 0;min-height: 80px;border: 1px solid #ddd;position: relative;width: 390px;}
.state-dl em{height: 100%;margin-top: 0;line-height: 32px;width: 30px;}
.state-dl em i{font-size:14px;color:#fff;width: 30px;display: inline-block;height: 60px;position: absolute;left: 0;top: 50%;margin-top: -30px;line-height: 20px}
.state-dl em.label-txt1{background: #58B040}
.state-dl em.label-txt2{background: #ccc;}
.state-dl dt{color:#484848;margin-left:30px;text-align:center;font-size:28px;display: inline-block;width: 25%;vertical-align: top;line-height: 104px;border-right: 1px solid #ddd;position: absolute;top: 0;bottom: 0;}
.state-dl dt i{color:#333;font-size:14px;height: 40px;line-height: 14px;display: inline-block;position: absolute;top: 50%;left: 50%;width: 54px;margin-top:-11px;margin-left: -27px;}
.state-dl dd{display: inline-block;vertical-align: top;margin-left: 150px;padding: 3px 0;}
.state-dl dd p{font-size:14px;line-height: 25px;color: #666;}
.state-dl .state-bot{margin: 0;padding: 0 15px;}
.label-txt1{background: #58CAC2;}
.label-txt2{background: #ccc;}
.label-txt3{background: #24C1EC;}
.label-txt1,.label-txt2,.label-txt3{margin-top:0.03rem;position:absolute;float:left;display:inline-block;width: 0.3rem;text-align:center;height: 0.78rem;line-height: 0.26rem;font-size: 0.12rem;font-style: normal;color: #fff;}
.finstate{color: #58B040;}
.medi-state .addbill .usetime-a{margin-top: 8px;}
.medi-state .addbill .usetime-a a{margin-right: 10px;}
.medi-state .addbill input[type="text"]{border: 1px solid #E4E4E4;width: 220px;}
.medibill-right label{margin-right: 20px;}
.medi-state .addbill select{width: 60px;vertical-align: middle}
.medibill-right .pho-list{font-size: 0;margin: 0;}
.medibill-right .pho-medi{display: inline-block;text-align: center;margin-top: 20px;margin: 0;    vertical-align: top;}
.medibill-right .pho-medi div{position: relative;width: 140px;margin: 5px 10px 10px 0;}
.medi-state .addbill ul li .medibill-right .medi-img{margin: 0;}
.medibill-right .pho-list div span{cursor: pointer;background: rgba(6,6,6,0.5);top: 0;right: 0;font-size: 30px;position: absolute;top: 0;right: 0;width: 30px;height: 30px;line-height: 30px;color: #fff;text-align: center;display: inline-block;}
.add-div{border: 1px dashed #e8e8e8;cursor: pointer;}
.add-div img{width: 44px;height: 38px;margin-top:40px;}
.add-div p{font-size: 14px;color: #999;}
.write-con .addbill ul{width: 340px;padding-bottom: 20px;}
.write-con .addbill ul li span{vertical-align: top;}
.write-con .addbill ul li select{width: 203px;}
.write-con .addbill ul li input{width: 200px;}
.write-con .addbill ul li textarea{width: 197px;border: 1px solid #ccc;height: 80px;}
.write-con .addbill ul li div{width: 250px;}

/*日历设置*/
.cal-set{font-size: 14px;margin: 20px 30px;border-bottom: 1px dashed #ddd;padding-bottom: 15px;}
.cal-set h3{font-size: 16px;height: 40px;line-height: 40px;}
.cal-set p{height: 30px;line-height: 30px;margin-left: 35px;color: #333;}
.cal-set input[type="text"]{width: 110px;height: 26px;border: 1px solid #BFBFBF;margin: 0 5px;}
.cal-set p img{vertical-align: top;margin-top: 5px;margin-left: -27px;}
.cal-set .tgl-light:checked + .tgl-btn{background: #1CA89D;margin: 0 5px;}
.cal-set p span{vertical-align: top;margin-top: 2px;}

/*成长记录*/
.toptxt-left{margin-left: 10px;height: 40px;line-height:40px;display: inline-block;}
.toptxt-left label{margin-right: 20px;font-size: 14px;}
.toptxt-left label select{width: 128px;}
.toptxt-left label .arrbtn-img{vertical-align: top;margin-top: 11px;margin-right: 10px;cursor: pointer;}
.record-con{font-size: 0;}
.record-left,.record-right{display: inline-block;font-size: 14px;vertical-align: top;}
.record-left{width: 55%;}
.record-right{width: 45%;}
.record-left table{width: 96%;margin: auto;}
.record-left table td{border: 1px solid #ddd;text-align: center;height: 40px;line-height: 20px;}
.record-left table td img{cursor: pointer;}
.tab-btn{color: #1CA89D;border: 1px solid #1CA89D;display: inline-block;height: 28px;line-height: 28px;font-size: 0;}
.tab-btn li{cursor:pointer;display: inline-block;padding: 0 10px;border-right: 1px solid #94D4D3;font-size: 14px;}
.tab-btn li:last-child{border-right: none;}
.tab-btn li.current-btn{background: #1DC499;color: #fff;}
.tab-btn2{color: #F47883;border: 1px solid #F47883;}
.tab-btn2 li.current-btn{background: #F47883;}
.tab-btn2 li{border-right: 1px solid #F47883;}
.record-right .supp-txt{color: #0BB1EF;margin-top: 10px;}
.record-right .supp-txt img{margin-left: 10px;cursor: pointer;}
/*过敏详情*/
.alle-check .addbill ul li{font-size: 0;}
.alle-check .addbill ul li .medibill-left{width: 15%;display: inline-block;font-size: 14px;}
.alle-check .addbill ul li .medibill-right{width: 85%;display: inline-block;font-size: 14px;}
.check-pro {text-align: center;}
.check-pro dl{display:inline-block;text-align: center;width: 130px;vertical-align: top;}
.check-pro .arrow-right{vertical-align: top;margin-top: 20px;}
.check-pro dl h5{color: #AFAFB5;margin-top: 10px;}
.check-pro dl p{color: #898989;}
.check-pro .light-btn{color: #ff6600;}
/*修改教职工管理*/
.staff-con table td{height: 34px;line-height: 24px;}
.staff-con table td img{cursor: pointer;margin-top: 2px;margin-left: 10px;margin-right: 10px;}
.staff-con .staff-sear select{width: 205px;}
.staff-con .staff-sear select,.staff-con .staff-sear input{border: 1px solid #D4D4D4;}
.staffme .check-photo p{margin-top:7px;font-size: 12px;}
.staff-con .cal-set{padding: 0;}

/*修改选择教师*/
.staff-con a:hover {color: #ffffff;}
.table-title .default-btn:hover{background:#258af7;}
.ediper{text-align: center}
.ediper .sel-person{margin-top:6px;border:1px solid #E4E4E4;text-align:left;margin-right:20px;display: inline-block;vertical-align: top;width:200px;height: 300px;overflow: hidden;text-overflow: ellipsis;}
.ediper .staff-con{text-align:left;width: 75%;display: inline-block;vertical-align: top;}
/*修改编辑教职工*/
.staffme ul.staff-mess{text-align: left;margin-top: 10px;}
.staffme ul.staff-mess li span{width: 35%;display: inline-block;}
.staffme ul.staff-mess li label{width: 65%;display: inline-block;}
.staffme ul.staff-mess li label input{width: 160px;}
.staffme ul.staff-mess li label select{width: 165px;}

/*新版教职工管理*/
.printbg{ background: #ffffff;padding: 5px 10px;}
.operation-img .printbg img{margin-left: 0;}
.bring_faculty {
    background: #ffffff none repeat scroll 0 0;
    border: 1px solid #EBEBEB;
    box-shadow: 0 0 15px #EBEBEB;
    left: 178px;
    position: absolute;
    top: 109px;
    z-index: 1000;
}

.bring_faculty p {
    display: block;
    font-size: 12px;
    height:35px;
    line-height: 35px;
    text-align:center;
	color:#323232;
	cursor:pointer;
}	
.bring_faculty p:hover,.bring_faculty p.hover{  background: #f3f4fa;}

.bring_class {
    background: #ffffff none repeat scroll 0 0;
    border: 1px solid #EBEBEB;
    box-shadow: 0 0 15px #EBEBEB;
    right: 20px;
    position: absolute;
    top: 109px;
    z-index: 1000;
	padding: 20px;
	width: 295px;
}

.bring_class label {
    float: left;
    padding: 10px 0 0 0;
    width: 50%;
}
.reghint_tit{font-size: 12px; color:#ed7c6c;background: #ffdfd9; padding: 5px 10px; background: #f8f5df;}
.reghint_tit .icon_remind::before{color:#ed7c6c;font-size:14px; }
.sound-name span{color: #0673fb; font-size: 12px; border: 1px solid #CEE2FF; background: #e8f1ff; border-radius:20px;display: inline-block;line-height: 20px; padding: 0px 10px;width: 85px;  margin-left: 10px;}
.sound-name span.curren.input-group input{background:#49a1ff; color: #ffffff; }
/*全国少儿膳食健康管理平台*/
.contain{background: #000 url(../images/bg_1.jpg) center center / cover no-repeat;height: 100%;width: 100%;min-height:640px;min-width:1000px;position: absolute;top:0;left: 0;}
/*contain的背景:
 .contain{background: url(../images/bg_2.jpg) center center / cover no-repeat;}
 .contain{background: url(../images/bg_3.jpg) center center / cover no-repeat;}
 */
.jb-logo{position: absolute;top:5%;left: 5%;}
.jb-code{position: absolute;top: 3%;right: 2%;}
.jb-code img{cursor: pointer;}
.jb-code h5{text-align: center;}
.jb-con{position: absolute;top:25.5%;left:2.7%;}

@media only screen and (max-height: 768px) {
	.jb-con{position: absolute;top:20%;left:2.7%;}
}

.jb-ident{background: rgba(255,255,255,0.3);width: 125px;padding-top: 0px;padding-bottom: 0px;position: absolute;left:0;}
.jb-ident img{display: block;margin: 15px auto;}
.jb-txt{color:#fff;position: absolute;width: 500px;left:200px;top:10px;}
.jb-txt h1{font-size: 45px;}
.jb-txt h3{font-size: 30px;margin-top:10px;}
.jb-txt p{font-size: 20px;margin-top:30px;}
.jb-txt a{margin-top:40px;font-size: 24px;color: #fff;width: 240px;height: 54px;display: inline-block;border: 1px solid #fff;line-height: 54px;text-align: center;}

.jb-login{position: absolute;bottom: 90px;left:50%;margin-left: -382px;width: 750px;}
.jb-login label{margin-left:15px;float:left;background: #fff;display: inline-block;width: 235px;height: 32px;vertical-align: top;}
.jb-login label i{text-align:center;display: inline-block;height: 32px;vertical-align: top;width: 15%;float: left;}
.jb-login label i img{margin-top: 7px;}
.jb-login label input{border:none;outline:none;width: 80%;display: inline-block;float: left;height: 24px;margin-top:2px;}
.jb-login .login-btn a{font-size:16px;text-align:center;line-height:32px;width: 156px;height: 32px;background: #8bc73d;color:#fefefe;display: inline-block;margin-left: 15px;}

.jb-footer{color:#fff;font-size:14px;line-height:26px;height:26px;position: absolute;bottom: 35px;left:50%;margin-left:-300px;width: 600px;text-align: center;}
.jb-footer img{vertical-align:text-bottom;margin-right:146px;margin-top:3px;}
.jb-footer span{margin-left: 25px;}
.jb-footer a{ text-decoration: underline; color: #fff; font-size: 24px}
/*政策提示*/
.point-txt{background: #fff;min-width: 750px;color: #404040;}
.point-txt p{font-size: 14px;text-indent: 28px;line-height: 25px;}
.point-txt .point-con{padding: 40px;}

/*关联政府端*/
.relagove{font-size: 14px;}
.relagove-txt{background: #e9e9e9;height: 100px;font-size: 14px;}
.relagove-txt p:nth-child(1){padding-top: 20px}
.relagove-txt p{height: 28px;line-height: 28px;margin-left: 20px;}
.relagove-txt p label{margin-right: 35px;}
.relagove .alle-tab table tr td{height: 34px;line-height: 24px;}
.relagove .alle-tab .gove-tit{height: 40px;line-height: 30px}
.relagove .alle-tab .gove-tit a{float: right;}
.relagove .alle-tab table .curr-tab{color: #1da89e;}
.relagove .alle-tab table a{margin: 0 8px;cursor: pointer;}
.arr-img{cursor: pointer;}
/*申请关联政府端*/
.heightnum{height: 45px;line-height: 45px;font-size: 14px;}
.txt-color-gary{color: #666;}
.apply-gover h3{border-bottom: 1px solid #1cc599;}
.apply-gover h3 img{vertical-align: top;margin-top: 10px;margin-right: 5px;}
.apply-gover{padding:0 15px;}
.apply-gover .apply-txt{height: 580px;overflow: auto;}


/*修改用户管理*/
li.li_chose{border: 1px solid #ff632d;position: relative;}
li.li_chose .li_chose_img{    position: absolute;bottom: 0;right: 0;}


/*未关联政府端*/
.no-gover{
    left: 50%;
    overflow: hidden;
    position: fixed;
    text-align: center;
    top: 50%;
    transform: translate(-50%, -50%);
    width:100%;
	z-index: 5000;
	text-align: center

}

.no-gover p{ line-height: 50px;}
.no-gover p a{ text-decoration: underline}


/*查看上报不通过*/
.rep-txt-list{margin: 20px;padding-bottom: 40px;}
.rep-txt-list .rep-list-congtent .div-tit{margin: 20px 0 40px 0;}
.rep-txt-list .rep-list-congtent .div-tit span{margin:0 60px 0 20px;color: #1AA094;}
.rep-txt-list .rep-progress{text-align: center;width: 100%;margin: 0 auto;}
.rep-txt-list .rep-img-pro:after{content: "";width: 190px;border-top:1px dashed #E5E5E5;position: absolute;top: 15px;right: -96px;}
.rep-txt-list .rep-img-pro:last-child:after{content: none;}
.rep-txt-list .rep-progress .rep-img-pro{display: inline-block;width: 250px;position: relative;font-size: 12px;}
.rep-txt-list .rep-progress .rep-img-pro img{margin-bottom: 10px;}
.rep-txt-list .rep-progress .rep-img-pro span{margin-top: 5px;display: inline-block;color: #999;}
.rep-textarea{margin: 20px 40px;border: 1px solid #E7E7E7;padding: 10px;}                   
.rep-textarea textarea{border: none;width: 100%;height: 100px;}
.rep-textarea p{text-align: right;color: #A5A5A5;}
.report-no{ width:50px; height:42px; text-align:center; border:3px solid #e3e3e3; border-radius:30px; padding-top:8px; line-height:16px; font-size:12px; float:left; margin:0 10px  20px 0px }
.Orangesize{ color:#ff8518}
.redsize{ color:#d91e06}
.greensize{ color:#1aa094}
/*上报*/
.mark-label{border: 1px solid #FE7B43;display: inline-block;height: 20px;line-height:20px;padding:0 10px 0 10px;font-size: 12px;color:#FE7B43;text-align: center;position: relative; width:60px; margin-top:8px}
.mark-label:before{content: "";width: 3px;height: 100%;display: inline-block;background: #FE7B43;position:absolute;left: 0;top: 0;}

.report-list h5{height: 44px;line-height: 44px;background: #47494A;margin:15px 20px;color: #fff;padding-left: 20px;}
.report-row{margin: 0 10px 0 10px !important;}
.rep-list{vertical-align: top;padding: 0 5px!important; position:relative}
.times-con{border: 1px solid #EEEEEE;border-radius: 4px;color: #4A4A4A;}
.times-con li{min-height: 40px;line-height: 40px;border-bottom: 1px dashed #E7E7E7;}
.report-list a{color: #63B3E8;text-decoration: underline;}
.times-con li:last-child{border-bottom: none;}
.cir-div,.cir-div2{position:relative;margin:15px 5%;text-align:center;width: 76px;height: 76px;line-height:26px;border-radius:50% ;display: inline-block;}
.cir-div{border: 4px solid #00d1ec;}
.cir-div2{border: 4px solid #eee;}
.cir-div div,.cir-div2 div{margin-top:14px;}
.cir-div span,.cir-div2 div{font-size: 12px;}
.cir-div span,.cir-div2 span{display: block;margin-top:3px;}
.report-num .cir-div span.pur-span{color: #8693ef;border-bottom: 1px solid #EFEFEF;margin: 0 5px;}
.report-num .cir-div span.ora-span{color: #FA7E4A;font-size: 20px;}
.report-num{text-align: center;}
.cir-div .report-label{position: absolute;top: -4px;right:-84px;font-size: 12px;color: #fff;margin-top: 0;}
.cir-div .report-label label{height:22px;line-height:22px;display: block;padding: 0 3px;border-radius: 3px;margin-bottom: 6px}
.green-label{background: #1FA296;}
.pink-label{background: #EC76E3;}
.report-month-quarter{background: #F5F5F5;margin: 0 20px 0 20px;padding: 20px 10px 10px 0;margin-bottom: 20px;}
.report-month-quarter .report-row{margin-right: 0!important;}
.report-month-quarter .rep-top{position: relative;}
.report-month-quarter .rep-top p{margin-bottom: 10px;font-size: 14px;}
.report-month-quarter .rep-top .iconfont{color: #1AA094;font-size: 20px;vertical-align: middle;margin: 0 5px;cursor: pointer;}
.report-month-quarter .rep-top .mark-left{background: url(../images/markle_bg.png)no-repeat;width: 33px;height: 33px;position: absolute;left: -10px;top: -20px;color: #fff;font-size: 12px;padding: 3px 0 0 3px;}
.month-quart-list{background: #fff;text-align: center;height: 235px;}
.month-quart-list h6{height: 40px;line-height: 40px;text-align: center;} 
.green-bg{background: #1AA094;color: #fff;}
.gray-bg{background: #E6E6E6;}
.gray-bg2{background: #F4F4F4;}
.orange-bg{background: #FE8652;color: #fff;}
.yellow-bg{background: #F5A044;color: #fff;}
.red-bg{background: #DA4A4C;color: #fff;}
.blue-bg{background: #12b7f5;}
.pink-bg{ background:#ff7884}
.pred-bg{ background:#ff5d5e}
.dred-bg{ background:#ff0d0d}
.lightpink-bg{ background:#f483eb}
.rep-leftbtn{
    border-radius: 0px 20px 20px 0px;
    color: #fff;
    height: 30px;
    line-height: 30px;
    padding: 0 15px; position:absolute; left:6px; top:6px;}

.rep-rightbtn{
    border-radius: 20px 0 2px 20px;
    color: #fff;
    height: 30px;
    line-height: 30px;
    padding: 0 15px; position:absolute; right:6px; top:6px;}

.times-con li p{ width:300px; margin:0 auto;}
 .fillbtn{  color: #fff; text-align:center; width:110px; line-height:30px; height:30px; background:#1aa094; display:inline-block; border-radius:5px;}
.reportbtn{  color: #fff; text-align:center; width:110px; line-height:30px; height:30px; background:#f2914a;display:inline-block;border-radius:5px; }
.rebtndiv .fillbtn,.rebtndiv .reportbtn{ margin: 20px auto 0px auto}
.report-num a,.report-num a:hover{ text-decoration:none;color: #fff; }
.rebtndiv {vertical-align: middle;  margin:40px auto 0 auto; height: 110px; width: 190px;}
.nostart{font-size: 18px; text-align: center; color: #666666; margin-top:70px;}
.redsbg{ background:#ff2318; display:block; text-align:center; color:#fff; font-size:12px; position:absolute; border-radius:4px; right:14px; top:10px; width:27px; line-height:18px; height:18px; cursor:pointer}
.month-quart-list .no-rep{margin-top: 50px;}
.month-quart-list .cir-div,.cir-div2{margin:15px 3px;position: relative;}
.month-quart-list .report-num{margin-top:10px;}
.month-quart-list .report-label{bottom: 65px !important;right: -20px;top: inherit;}
.month-quart-list .cir-div .report-label label{margin-bottom: 2px;}
.num-div{text-align: left;margin-left: 10px;font-size: 14px;}
.num-div .mark-label{margin-top: 3px;}
.report-month-quarter .month-quart-con{margin-bottom: 20px !important;}
.no-rep-txt{color: #6F6F6F;font-size: 18px;margin-top: 82px;}
.tab-tit{border-bottom: 1px solid #e2e2e2;}
.tab-tit .layui-tab-title{display: inline-block;border-bottom: 0;}
.sel-enr-btn{display: inline-block;}
.sel-enr-btn button{width: 105px;height: 30px;line-height: 30px;display: block;}
/*填写上报报表*/
.tit-top-table{margin: 0 15px;}
.tit-top-table span{margin-right:80px;height: 62px;line-height: 62px;}
.rep-table{border: 1px solid #DFDFDF;margin: 0 15px 15px 15px;}
a.blue-txt{color: #57B3E8;text-decoration: underline;}
.rep-table-con{margin: 0 15px;}
.rep-table-con table{text-align: center;}
.set-checkbox-style input[type="checkbox"]{-webkit-appearance: none;width: 14px;height: 14px;border: 1px solid #ccc;outline: none;}
.set-checkbox-style input[type="checkbox"]:checked{background: url(../images/checked_bg.png) 50%;background-size:14px 14px;outline: none;border: 0;}
/*.set-checkbox-style input[type="checkbox"]{vertical-align: top;margin:-2px 5px 0 20px;}	*/
.set-checkbox-style input[type="checkbox"]{vertical-align: top;margin-right: 7px;margin-top: 8px;}	
.set-checkbox-style.checkboxbg1 input[type="checkbox"]:checked{background: url(../images/buy_checked_bg.png) 50%;}
.rep-table{padding-bottom: 15px;}
/*填写幼儿信息*/
.fill-information{margin: 20px auto; width:938px}
.fill-information .layui-form-label{width:270px;}
.fill-information .layui-form-item{margin-bottom:15px; font-size: 14px; color: #333333}
.fill-information .layui-mark-red,.fam-information .layui-mark-red, .red-txt{color: #ff2626;}
.fill-information input[type="text"]{width:300px;}
.fill-information .layui-mat-block{ min-height: 36px; margin-top:20px}
.fill-information .layui-mat-block span{ text-align: center; color:#1da89e; padding:10px 7px; border: 1px solid #e6e6e6; border-radius: 5px; margin:0px 5px 0 0px;}
.fill-information .layui-mat-block span a{color:#1da89e; text-decoration: underline}
.fill-information .layui-mat-block span:last-of-type{margin:0px;}
.fill-information .layui-btn{ line-height: 35px; height: 35px;     padding: 0 30px; }
.fill-information .grey{ background: #f1f1f1}
.fill-information .blue{ background: #12b7f5}
.linkagediv{border: 1px solid #e6e6e6; padding: 10px 0 20px 0; margin: 15px 0px 15px 58px; width:690px;}
.linkagediv .layui-form-label{ padding-bottom:0 }
.linkagediv .layui-form-item{ margin-bottom: 0}
.link-delbtn{  display: inline-block;float:right; margin: 5px 15px 0 0px; cursor: pointer}
.link-delbtn img{  vertical-align: middle;}
.fill-child{ display:inline-block; line-height: 30px;background: #12b7f5;border-radius: 3px; color: #fff;height: 30px;text-align: center;  padding: 0 15px; float: right; cursor: pointer}
/*填写家长信息*/
.fam-information{margin: 20px auto; width:700px}
.fam-tit{border-bottom: 1px solid #E9E9E9;line-height: 35px; height: 35px; margin-bottom: 20px; padding-left:20px }
.fam-information .layui-form-item{ width:500px; margin: 0px auto 10px auto} 
.fam-information input{ width: 300px}

/*招生管理-没有数据*/
.enrol-top{background: #ECF0F5;height: 44px;line-height: 40px;}
.tab-ul{height: 32px;}
.tab-ul li{width: 180px;text-align: center;height: 32px;line-height: 32px;}
.tab-ul2{height: 32px;}
.tab-ul2 li{width: 180px;text-align: center;height: 32px;line-height: 32px;}
.tab-ul2 li.current-btn{background: #1CA89D;color: #fff;}
.enrol-con{clear: both;}
.no-data{position: absolute;top: 50%;margin-top: -108px;left: 50%;margin-left: -203px;}
/*上传证件照片*/
.credentials{padding: 20px 30px;width: 772px;margin: auto; color: #333333; overflow: auto; font-size: 14px}
.credentials-div{width: 694px; overflow: auto; margin:20px auto;  padding: 20px; background:#F7F7F7; }
.credentials-left{ width:150px;float: left;text-align: right; padding-top:100px;   }
.credentials-cen {border: 1px dashed #ddd;display: inline-block;font-size: 14px;padding: 10px ;width: 375px; float: left; border-radius: 2px; margin-left: 15px;}
.credentials-cen .pic-upload {height: 224px;position: absolute;width: 375px; cursor: pointer;background: rgba(0,0,0,0.3);border-radius: 5px}
.credentials-cen .pic-upload  a{cursor:pointer;background: #12B7F5;color: #fff;width:160px;height:34px;line-height:34px;text-align:center;border-radius: 4px;position: absolute;top: 50%;left: 50%;margin-left: -80px;margin-top: -17px;}
.credentials-cen .pic-list{ height:224px; position: relative}
.credentials-cen .pic-list img {height: 224px;width: 375px;border-radius: 5px}
.credentials-right{ float: left; overflow: auto;  width:80px;display:inline-block; margin-left: 15px;padding-top:100px  }
.credentials-right p{ line-height:25px; font-size: 14px; margin-bottom: 10px;  }
.credentials-bot{ clear: both; padding-top:20px; overflow: hidden}
.credentials-botleft{height:44px; width:150px;float: left;text-align: right;padding-top: 10px}
.credentials-botcen{height:49px;border: 1px solid #ddd; border-radius: 3px;width: 395px; float: left;margin-left: 15px; }
.credentials-botcen p{ color: #999999;padding:5px 10px}
.hou-reg{background: #E5FBF9;border-bottom: 2px solid #1DA89E;color: #1DA89E; height: 36px;width: 734px;margin:0 auto; line-height: 36px}
.credentials-div.topno{margin: 0 auto}
/*查看上传照片*/
.check-pic{padding: 20px 30px;text-align: center;width: 772px;margin: auto;}
.check-pic p{text-align: left;margin-bottom: 10px;}
.check-pic img{max-width: 750px;}
/*打印报名活动单*/
.print-active{padding: 10px 30px;width: 800px;margin: 0 auto;}
.print-active .addbill ul li .medibill-left{width: 200px;}
.print-active .addbill ul li label{margin: 0;}
.print-active .addbill ul li .medibill-right{width: 550px;}
.print-active .gray-txt{color: #B4B4B4;}
.print-active .green-txt{color: #37ACA3;text-decoration: underline;cursor: pointer;}
.rela-txt{border: 1px solid #eee;padding: 5px 0;margin-bottom: 10px;}
/*报名分班管理*/
.enr-divide-top{height: 114px;border-bottom: 1px solid #53D3B2;}
.dividr-top-left,.dividr-top-right{display: inline-block;vertical-align: top;margin-top:12px;}
.dividr-top-left p{margin: 8px 0 0 20px;}
.dividr-top-left p a.stat-btn{margin-right:10px;}
.dividr-top-right{float: right;margin-right: 150px;}
.dividr-top-right .pro-txt-list{display: inline-block;text-align: center;margin-top: 13px;}
.dividr-top-right .pro-txt-list p{margin-top: 10px;}
.divide-tit{height: 54px;line-height: 54px;}
.divide-tit .divide-btn-right{float: right;}
.divide-tit .divide-btn-right input[type="button"]{float: none;display: inline-block;vertical-align: top;margin-top: 9px;}
.divide-tit .divide-btn-right a{margin-right: 10px;}
.enr-per{margin-left: 20px;}
.enr-per img.sex-img{margin-right: 10px;vertical-align: top;margin-top: 14px;}
/* .enr-per img.close-img{float: right;margin: 10px 6px 0 0;cursor: pointer;} */
.enr-per .enr-per-name{display: inline-block;width: 70px;}
.red-line{border-bottom:1px solid #FF5F45;}
/*新增班级名称*/
.add-class{padding: 20px;color: #333;}
.add-class p{height: 30px;line-height: 30px;}
.add-class .textarea-txt{border: 1px solid #EAEAEA;padding: 5px;}
.add-class .textarea-txt textarea{width: 98%;border: none;outline: none;font-size: 14px}
.add-class .input-txt input{border: 1px solid #EAEAEA;margin-right: 10px; height: 30px;}
/*招生设置*/
.enr-set-connent{min-width: 840px;}
.textarea-list{display: inline-block;}
.text-content{border: 1px solid #E5E5E5;width: 74%;padding: 10px 0;text-align: center;}
.text-content textarea{width: 96%;height: 100px;border: none;outline: none; font-size: 14px}
.enr-set-list{padding:10px 30px 0 30px;margin: 0 30px;}
/*报名活动列表新版*/
.enr-stat-div{background: #FFFFFF; border-top: none;  border: 1px solid #E6ECF0; margin: 10px;}
.enr-stat-div .local-txt{display: inline-block;margin-top: 10px;color: #1b97e6;font-size: 14px; font-weight: bold;}
.enr-stat-sel{height: 160px;box-sizing: border-box;background: #ffffff; color: #333333;}
.enr-stat-sel .info-cell{padding:0px 0 0 10px; width: 100%;}
.enr-stat-sel .info-cell span{color:#666666;}
/*摇号*/
.signonline{position: relative;background: #fff;border: 1px solid #eeeeee;margin:10px;}
.signonline-bot{position: relative;background: #fff;margin:10px;}
.signonline-bot h5{font-size: 14px;color: #333333;font-weight: bold;height:46px;line-height: 46px;padding: 0 15px;border: 1px solid #dddddd;border-bottom: none;}    
.signonline-bot h5 span{font-size: 12px;color: #666666;font-weight: normal;}
.signonline-bot .selper-div{border: 1px solid #dddddd;height: 370px;padding: 0 0px;overflow-y: auto;}	
.signonline-bot .teacher-cell li{margin: 12px 0;}
.signonline-bot .sel-label{padding: 9px 5px 9px 15px;}
.signonline-bot .sel-label span{background: #f8f8f8;border-radius: 0;border: none;font-size: 12px;padding: 5px 12px 5px 12px; text-align: center;}
.signonline-bot .sel-label span i{display: block; text-align: center;}
.signonline-bot .btm-btndiv{border:1px solid #cccccc;border-right: none;}
/*报名活动初审新版*/
.first-tria-main,.first-tria-list{display: -moz-box;  /* Firefox */
display: -ms-flexbox;    /* IE10 */display: -webkit-box;    /* Safari */ display: -webkit-flex;    display: flex; align-items:center;
-webkit-align-items:center;box-align:center;-moz-box-align:center;-webkit-box-align:center; }
.first-tria-main .enr-chart-list{width: 50%;padding: 10px; border-radius: 0;height: auto;margin: 10px;}

/*报名活动列表*/
.enr-stat-list{display: inline-block;margin:0 0 10px 10px;width: 49%;border: 1px solid #ddd;/*border-radius: 4px;*/position: relative; color: #999;}
.enr-stat-list li{/*border-bottom: 1px dashed #eee;*/line-height: 30px;padding-left: 15px;font-size: 14px;
}
.enr-stat-list li.onebg{border-bottom: 1px solid #D8D8D8; background:#F9F9F9; padding:5px 0 5px 15px;    }
.enr-stat-list li:last-child{border-bottom: none;}
.enr-stat-list .enr-btn{text-align: center; border-top: 1px solid #D8D8D8; margin: 10px 15px 0 15px; padding: 5px 0 0 0;}
.enr-stat-list .enr-btn a{margin-left: 5px;}
.enr-stat-list a.opr-btnbg {
  background: #F9FBFC;
  padding: 0 10px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  display: inline-block;
  color: #8C9EB0;
  font-size: 12px; margin-bottom: 5px;
}
.enr-stat-list .enr-btn a img{margin-right:4px; width: 14px; vertical-align: top; margin-top: 8px;}
.mor-classes{ display:inline-block; line-height: 30px;background: #1ca89d;border-radius: 3px; color: #fff;height: 30px;text-align: center; float: right; padding: 0 10px}
.enr-stat-list li span{color: #34495E;font-size: 14px; display: inline-block; margin-right: 10px; }
/*报名初审*/
.enr-chart-list{width: 494px;height: 190px;display: inline-block;border: 1px solid #E8E8E8;border-radius: 4px;margin: 0 0 10px 10px;}
.define-form li{display: inline-block;line-height: 34px;margin: 5px 0 0px 10px;}
.define-form input[type="text"],.define-form select{width: 105px;border: 1px solid #eee;}
.cal-span{position: relative;}
.cal-span img{position: absolute;right: 5px;top: 0;}
/*早教图标*/
.early-child{position: relative;}
.early-child:before{content:"";background: url("../image/early_child.png") no-repeat;position: absolute;top: 4px;left: 4px;width: 14px;height: 14px;display: inline-block;color: #fff;line-height: 14px;font-size: 10px;text-align: center;border-radius: 3px;}
/*报名终审*/
.enr-num-list .cir-div{margin: 15px 10% 25px 10%;}
.enr-num-list .cir-div>div{margin-top: 10px;}
.enr-num-list .color-span{font-size: 20px;border-bottom: 1px solid #F0F0F0;display: inline;padding-bottom: 3px;}
.enr-num-list .def-span{font-family: 12px;}
.enr-num-list .red-cir .color-span{color: #F9675A;}
.enr-num-list .red-cir{border: 4px solid #F9675A;}
.enr-num-list .blue-cir .color-span{color: #00D1EC;}
.enr-num-list .blue-cir{border: 4px solid #00D1EC;}
/*发面审通知*/
.num-label{display: inline-block;float: right;margin:18px 10px 0 0;}
.num-label li{display: inline-block;background: #DCDCDC;border-radius: 5px;padding: 0 3px;color: #666;margin-left: 5px;}
.num-label li.cur-label{background: #1CA89D;color:#fff;}
.notice-list{width: 190px; float: left;background: #FAFAFA;text-align:center;height: 100%;padding: 20px 0;position: relative;}
.notice-con{position: absolute;top: 98px;bottom: 0;width: 100%;overflow-y: hidden;}
.notice-list .treebtn1{position: absolute;bottom: 80px;left: 50%;margin-left: -40px;height: 34px}
/*面审通知*/
.notice-detail{background: #E9E9E9;padding: 15px 30px;}
.notice-detail li{display: inline-block;line-height: 30px;margin-right: 150px;}	    	
.notice-con .ora-label,.notice-con .gre-label{margin: 0 7px 0 0;}
.notice-con .end-btn{position: absolute;bottom: 35px;left: 50%;margin-left: -180px;height: 34px}
/*查看已上报*/
.medi-state .addbill div{width: 400px;}
.rep-progress{margin: 0 auto;}
.rep-img-pro:after{content: "";width: 120px;border-top:1px dashed #E5E5E5;position: absolute;top: 15px;right: -60px;}
.rep-img-pro:last-child:after{content: none;}
.rep-progress .rep-img-pro{display: inline-block;width: 180px;position: relative;font-size: 12px;text-align: center;margin: 15px 0;}
.rep-progress .rep-img-pro img{margin-bottom: 10px;}
.rep-progress .rep-img-pro span{margin-top: 5px;display: inline-block;color: #999;}
.check-detail{padding: 20px;}
.check-detail .tab-ul2 li{width: 150px;}
.check-detail .mess-txt{font-size: 0;width: 814px;text-align: center;margin: 0 auto}
.check-detail .mess-txt .medi-state{display: inline-block;border:  1px dashed #ddd;padding:10px 15px;font-size: 14px;width: 365px;text-align: left;}
.mess-txt-list{display: inline-block; margin-right:8px}
.mess-txt .medi-state .addbill div{width: 240px;}
/*上传证件照*/
.pic-shadow{width: 366px;height:220px;background: rgba(0,0,0,0.3);position: absolute;}
.pic-shadow a{cursor:pointer;background: #12B7F5;color: #fff;width:160px;height:34px;line-height:34px;text-align:center;border-radius: 4px;position: absolute;top: 50%;left: 50%;margin-left: -80px;margin-top: -17px;}
.mess-txt .pic-list img{width: 366px;height:220px;}
/*上传证件照片*/
.cer-shadow{width:772px;height:463px;position: absolute;}
.cer-shadow a{cursor:pointer;background: #12B7F5;color: #fff;width:160px;height:34px;line-height:34px;text-align:center;border-radius: 4px;position: absolute;top: 50%;left: 50%;margin-left: -80px;margin-top: -17px;}
.cer-list {margin-top: 15px}
.cer-list img{width:772px;height:463px;}
/*招生分布图*/

.topframe {
height: 85px;
padding: 1px 0 0 1px;
font-size: 0; margin-top: 10px
}
.topframe>.data-show {
height: 96%;
margin-right:1%;
display: inline-block;
font-size: 14px;
vertical-align: middle;  position: relative; background:#eeeeee; 
}
.topframe>.data-show:first-child {width:250px;}
.topframe>.data-show:first-child>div {
padding-top: 3px;
padding-left: 8%;
}
.topframe>.data-show:first-child>div:last-child {
padding-top: 5px;
}
.topframe>.data-show:first-child>div>button {
background-color: #DDDDDD;
border: none;
padding:0 3px;
font-size: 14px;
color: #A1A1A1;
cursor: pointer;
}
.data-show .greyico_img{  display: inline-block; position: absolute; left: 10px}
.topframe>.data-show:first-child{  background-color: #fff;  }
.topframe .bluebg{ background:#48b0f2; color: #fff }
.topframe .purplebg{ background:#bf5ce8; color: #fff }
.topframe .greenbg{ background:#33c5d2; color: #fff }
.topframe .redbg{ background:#e7505a; color: #fff }
.selectmap_bg{position: absolute; background:url(../images/selectmap_bg.png) no-repeat; width: 38px; height: 33px; bottom:1px; right:1px }
.data-show .col-blue {
    color: #3ea9f3;
    display: inline-block;
    font-size: 26px;
    height: 24px;
    line-height: 16px;
    margin: 0 8px;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    width: 16px;
}
.data-show .col-pink {
    color: #ff7be2;
    display: inline-block;
    font-size: 26px;
    height: 24px;
    line-height: 16px;
    margin: 0 8px;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    width: 16px;
}


.topframe>.data-show:nth-child(2),
.topframe>.data-show:nth-child(3),
.topframe>.data-show:nth-child(4),
.topframe>.data-show:nth-child(5),
.topframe>.data-show:nth-child(6),
.topframe>.data-show:nth-child(7)  {
min-width: 185px;
width:19.6%;
border-radius: 4px;
font-size: 16px; text-align: center;}
.topframe>.data-show:nth-child(2)>div:first-child,
.topframe>.data-show:nth-child(3)>div:first-child,
.topframe>.data-show:nth-child(4)>div:first-child,
.topframe>.data-show:nth-child(5)>div:first-child,
.topframe>.data-show:nth-child(6)>div:first-child,
.topframe>.data-show:nth-child(7)>div:first-child
{height: 33px;
line-height: 33px;}
.topframe>.greenbg:nth-child(2)>div:first-child {
border-bottom: 1px solid #77cfd7}
.topframe>.redbg:nth-child(3)>div:first-child {
border-bottom: 1px solid #e3898f}
.topframe>.bluebg:nth-child(4)>div:first-child {
border-bottom: 1px solid #84c2ea}
.topframe>.purplebg:nth-child(5)>div:first-child {
border-bottom: 1px solid #cb90e4}

.topframe>.greybg:nth-child(2)>div:first-child {
border-bottom: 1px solid #e2e2e2}
.topframe>.greybg:nth-child(3)>div:first-child {
border-bottom: 1px solid #e2e2e2}
.topframe>.greybg:nth-child(4)>div:first-child {
border-bottom: 1px solid #e2e2e2}
.topframe>.greybg:nth-child(5)>div:first-child {
border-bottom: 1px solid #e2e2e2}

.topframe>.data-show:nth-child(2)>div:last-child>span:first-child,
.topframe>.data-show:nth-child(3)>div:last-child>span:first-child,
.topframe>.data-show:nth-child(4)>div:last-child>span:first-child,
.topframe>.data-show:nth-child(5)>div:last-child>span:first-child,
.topframe>.data-show:nth-child(6)>div:last-child>span:first-child,
.topframe>.data-show:nth-child(7)>div:last-child>span:first-child {
height: 50px;  font-size: 25px; line-height: 50px}
.data-down{ font-size: 25px; line-height: 50px}
.map-checkbox {display: none;}
.map-checkbox + label {
position: relative;
display: inline-block;
padding:5px;   
/* background-color: #5CB878;*/
border: 1px solid #D0D0D0;
}
.topframe>.data-show:nth-child(5){margin-right:0}
.map-checkbox + label:active, .map-checkbox + label:checked + label:active {
box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

@media screen and (min-width:1024px)and (max-width:1280px){	
.topframe>.data-show:nth-child(2),
.topframe>.data-show:nth-child(3),
.topframe>.data-show:nth-child(4),
.topframe>.data-show:nth-child(5),
.topframe>.data-show:nth-child(6),
.topframe>.data-show:nth-child(7)  {
min-width: 185px;
width:18.0%;}}
@media screen and (min-width:1280px)and (max-width:1400px){
.topframe>.data-show:nth-child(2),
.topframe>.data-show:nth-child(3),
.topframe>.data-show:nth-child(4),
.topframe>.data-show:nth-child(5),
.topframe>.data-show:nth-child(6),
.topframe>.data-show:nth-child(7)  {
min-width: 185px;
width:18.5%;
}
}

.map-checkbox:checked + label {
background-color: #5CB878;
box-shadow: 0 1px 2px rgba(0,0,0,0.05); border: 1px solid #5CB878;
}
/*选中后的效果*/
.map-checkbox:checked + label:after {
content: '\2714';
position: absolute;
font-size: 11px;
top: -3px;
left: 1px; 
color: #fff; }
/*智能分班弹框*/
.divide-class-list{border: 1px solid #DEDEDE;line-height: 26px;padding: 12px 20px 12px 90px;margin: 20px;position: relative;cursor: pointer;}
.divide-class-list p{font-size: 16px;}
.divide-class-list span{font-size: 14px;color: #666;}
.select-light{border: 2px solid #FF622C;}
.light-img{position: absolute;right: 0;bottom: 0;}
/*查看微信信息*/
.bind-form .addbill ul li{min-height: 37px;}
.bind-form .addbill ul li .medibill-left{text-align: left;width: auto;margin-right: 5px;position: relative;vertical-align: top}
.bind-form .addbill .inp-select{position: absolute;left: -30px;vertical-align: top;margin-top: 3px;}
.bind-form .addbill ul i{height: auto;line-height: normal;}
.bind-form .addbill ul label{margin: 0;}
.bind-form .addbill ul{width: 560px;}
.bind-form .addbill ul li .medibill-right{width: auto;}
/*一键操作*/
.class-list{margin: 10px 40px;}
.name-class{height: 30px;line-height: 30px;font-size: 12px;}
.name-class .class-con{cursor: pointer;}
.name-class img{margin-right: 5px;}
.class-detail{margin: 0 34px;}
.detail-list{border: 1px solid #ddd;margin:10px 5px;position: relative;}
.detail-list .addbill{padding: 20px 20px 10px 50px;margin: 0;}
.green-border{border: 1px solid #5FB878;}
.detail-list:before{z-index:9;position:absolute;left:20px;bottom:-24px;content: "";width: 8px;height: 36px;background: url(../images/link_bg.png)no-repeat;background-size: contain;}
.detail-list:after{z-index:9;position:absolute;right:20px;bottom:-24px;content: "";width: 8px;height: 36px;background: url(../images/link_bg.png)no-repeat;background-size: contain;}
.detail-list:last-child:before,.detail-list:last-child:after{content: none;}
.arr-right{vertical-align: top;margin-top: 10px;}
.arr-down{vertical-align: top;margin-top: 13px;}
.class-list .detail-list ul{margin: inherit;}
/*交费提示*/
.prompt-con{padding: 10px 0;width: 500px;margin: 0 auto;}
.prompt-con .prompt-txt{display: inline-block;vertical-align: top;margin-top: -10px;margin-left: 10px;font-size: 14px;}
.prompt-con .prompt-txt .title-img{margin-bottom: 20px}
.prompt-con .prompt-txt p{position: relative;}
.prompt-con .prompt-txt .heart-img{position: absolute;top: -18px}
/*报名材料设置*/
.txt-label-list{margin: 10px 0;text-align: left;}
.txt-label-list li{font-size:14px;padding:1px 5px 1px 10px;color: #25B8F2;border: 1px solid #25B8F2;display: inline-block;cursor: pointer;vertical-align: top}
.txt-label-list li .red{padding: 0;font-weight: bold;}
.txt-label-list li .close-label-list{display:inline-block;margin-left: 15px;width: 20px;text-align: center;float: right;}
.txt-label-list li{margin: 5px 2px;}
.txt-label-list li .exp-txt{font-size: 12px;color: #999999;line-height: 20px;}
/*添加幼儿信息栏目*/
.add-infor-form .fill-information{width: 750px;}
.add-infor-form .fill-information input[type="text"]{width: 320px;}
.add-infor-form .layui-input,.add-infor-form .layui-unselect{height: 28px;margin-top: 5px}
.add-infor-form .fill-information .layui-form-item{margin-bottom: 5px}
.add-infor-form .fill-information .layui-form-label{width: 175px;text-align: left;}
.add-infor-form .fill-information .set-checkbox-style input{}
.add-infor-form .layui-form-item .layui-form-checkbox{display: none}
.add-infor-form .layui-input-block{margin-left: 180px;}
.add-infor-form .layui-form-selected dl{top:30px;padding: 0;}
.add-infor-form .layui-form-select{width: 320px;}
.add-infor-form .select-list{display: inline-block;}
.add-infor-form .select-list .layui-input-block{width: 106px;display: inline-block;margin-left: 0;}
.add-infor-form .select-list .layui-form-select{width: 100px;}
.add-infor-form .select-list .layui-select-title input.layui-input{width: 100px;}
.add-infor-form .select-list .layui-select-title{margin-right: 10px;}
.sel-fillin{display: inline-block;vertical-align: top;margin-top: 2px;margin-left: 30px;}
.sel-fillin .layui-form-radio i{margin-right: 5px;}
.sel-fillin .layui-form-radio{margin-right: 5px;}
.sel-fillin .need-fill .layui-form-radioed{color: #ff0000;}
/*查看药品*/
.define-list{padding: 20px 30px;}
.define-list .layui-form-item{margin-bottom: 0;}
.define-list .layui-form-label{padding: 9px 0 9px 15px;width: 100px;}
.define-list .layui-input-block{min-height: auto;padding: 9px 0;margin-left: 115px;}	
.drug-detail{min-height:38px;border: 1px solid #ccc;display: inline-block;vertical-align: top;padding: 8px 10px;margin-right: 10px;}
.left-drug-detail{position: absolute;top: 0;bottom: 0;}
.left-drug-detail .txt-p{display: table;height: 100%;}
.left-drug-detail .txt-p .txt-span{display: table-cell;vertical-align: middle;}
.right-drug-detail{margin-left: 120px;min-width: 200px;}
.drug-tab{position: relative;margin-bottom: 10px;}
.drug-tab .opr-icon{position: absolute;top: 50%;margin-top: -8px;}
.drug-tab .current{border: 1px solid #F71A1A;color: #F71A1A;}
/*查看药单详情*/
.inven-top{position: relative;}
.inven-top .inven-mark{position: absolute;top: -10px;right: 0;}
.info-list{display: inline-block;font-size: 14px;color: #333;margin-right: 60px;margin-bottom: 10px;}
.inven-list{height:120px;border: 1px solid #DFDFDF;font-size: 14px;padding: 15px 20px;color: #999;}
.inven-list .inven-exp{margin-top: 8px;}
.inven-list .inven-exp span{display:inline-block;margin-right: 15px;vertical-align: top;}
.inven-list .inven-label{margin-top: 5px;}
.inven-list .inven-label span{margin-right: 10px;}
.inven-list .inven-label .current{color: #3F9CF4;border: 1px solid #3F9CF4;}
.inven-list .inven-label .current2{color: #F71C1C;border: 1px solid #F71C1C;}
.btn-unsel{display: inline-block;border: 1px solid #DFDFDF;padding: 3px 10px;border-radius: 20px;margin-top: 6px;position: relative;}
.btn-unsel .stat-icon{position: absolute;top: -5px;right: -6px;}
/*添加药品*/
.drug-form .layui-input-block{padding: 0;}
.drug-form .layui-form-item{margin-bottom: 10px;}
.def-select{padding-top: 6px;}
.def-select .layui-unselect,.def-select input{width: 120px;height: 28px;display: inline-block;margin-right: 10px;margin-bottom: 4px;}
.def-select .layui-form-select dl{top: 28px;}
.def-select .layui-form-select dl dd,.def-select .layui-form-select dl dt{line-height: 28px;}
.drug-pho .pho-list,.drug-pho .pho-medi{display: inline-block;vertical-align: top;}
/*忘记密码*/
.for_password{ margin:0 auto;width:980px; padding:30px 50px; overflow:hidden; background:#FFF; text-align:center }
.for_password p{ color:#666666; font-size:14px; line-height:30px;text-align:left;}
.for_password h3{ text-align:left; font-size:16px; color:#333333; display:block; font-weight:bold; line-height:30px; clear:both}
.for_password_img{ margin:30px auto; text-align:left}
.for_password_img li{ float:left}
.for_password_img p{ height:75px;position:relative; margin-top:30px }
.for_password_img span{background:#abc564;height:47px; color:#fff;font-size:16px; line-height:20px; display:block; padding:5px 10px 0 40px; width:200px;}
.for_password_img p img{ position:absolute; top:-20px; left:-1px}
.for_password_img span.yellowbg{background:#e6a855;}
/*退膳设置*/
.no-radio-txt .layui-input-block{margin-left: 50px;}
.no-radio-txt .layui-form-radio{margin: 0;padding: 0;}
.no-radio-txt .layui-form-radio span{display: none;}
.line-blue{border: none;border-bottom: 1px solid #25B8F2;border-radius: 0;}
/*退膳详情*/
.exit-tab .tabstyle tbody tr{color: #999999;}
.exit-tab .tabstyle.pd0 tr th,.tabstyle.pd0 tr td{padding: 0;min-width: 20px;position: relative;height: 40px;border:none;}
.exit-tab .tabstyle.pd0 tr:last-child td{border-bottom: 0;}
.exit-tab .tabstyle.pd0 tr th:last-child,.tabstyle.pd0 tr td:last-child{border-right: 0;}    	
.tab-mark{position: absolute;left: 50%;top: -8px;}	    	
.linkfirst:before,.linklast:before{content: "";display: inline-block;width: 6px;height: 6px;border-radius: 50%;background: #000;position: absolute;bottom: 2px;left:50%;margin-left: -3px;}
.linkfirst:after{content: "";width: 50%;height: 2px;background: #000;position: absolute;bottom: 4px;left: 53%;}
.linklast:after{content: "";width: 50%;height: 2px;background: #000;position: absolute;bottom: 4px;left: 0;}
.linklist:after{content: "";width: 108%;height: 2px;background: #000;position: absolute;bottom: 4px;left: -1px;}	    	
.blue-legend{background: #23AFE6;width: 14px;height: 14px;display: inline-block;vertical-align: sub;/*border-radius: 50%;*/}
.gray-legend{border: 1px solid #C3C3C3;box-sizing:border-box;width: 14px;height: 14px;display: inline-block;vertical-align: sub;}
.legend-list{display: inline-block;margin: 20px 20px;}
.legend-list>span{margin-left: 15px;font-size: 12px;}
.legend-list i{margin-right: 5px;}
.legend-list .iconfont{display: inline-block;vertical-align: middle;}

.cir-date label{width: 24px;height: 24px;background:#ffffff;border:1px solid #dadada;border-radius: 50%;display: inline-block;position: absolute;top: 50%;margin-top: -12px;left: 50%;margin-left: -12px;z-index: 9;color: #999999;}
.cir-date.current label{background: #12b7f5;color: #ffffff;border:1px solid #12b7f5;}
.cir-date.centrecurr:before,.cir-date.lastcurr:before{content: "";width: 50%;border-top: 4px solid #12b7f5;position: absolute;left: 0;top: 50%;}
.cir-date.centrecurr:after,.cir-date.firstcurr:before{content: "";width: 50%;border-top: 4px solid #12b7f5;position: absolute;right: 0;top: 50%;}

.calen-tit{padding: 12px 0 0 0;}
.calen-tit span{background: #b5e6ff;height: 26px;line-height: 26px;display: inline-block;padding: 0 10px;color: #007eff;}
/*关闭*/
.icon_close_red:after{
	content: "\e614";
	font-size: 16px;
	color: #FC0B22;
}
.icon_close_white:after{
	content: "\e614";
	font-size: 16px;
	color: #ffffff;
}
/*食品采购计划*/
.buy-title-top{height: 50px;border-top: 1px solid #eeeeee;border-bottom: 1px solid #eeeeee;position: relative; width: 100%;}
.buy-title{height: 50px;border-top: 1px solid #eeeeee;border-bottom: 1px solid #eeeeee;position: relative; width: 100%;}
.plan-left{width: 30px;height: 100%;text-align: center;border-left: 1px solid #c8c8c8;border-right: 1px solid #c8c8c8;position: relative;float: left; background: #F3F3F4;}
.plan-txt{font-size: 14px;color: #333333;padding: 13px 0;background: #dedede;line-height: 15px;cursor: pointer; /*border-bottom-left-radius: 10px; border-top-left-radius:10px;*/}
.plan-txt.current{background: #00ab9e; color: #fff;}
.plan-txt label{background: #c8faf7;width: 32px;height: 32px;border-radius: 50%;display: inline-block;text-align: center;margin-bottom: 10px;cursor: pointer;}
.plan-txt img{vertical-align: top;margin-top: 5px;}
.tend-label{background: #e6e6e6;width: 28px;height: 28px;line-height: 28px;text-align: center;border-radius: 50%;display: inline-block;color: #12c680;}	    	
.inset-label{padding: 0 20px;height: 60px;line-height: 60px;background: #ffffff;color: #666666;display: inline-block;vertical-align: middle;box-shadow: -4px -4px 5px #dddddd inset;}
.prompt-label{border: 1px solid #eeeeee;position: absolute;right: -80px;padding: 2px;width: 60px;border-radius: 3px;color: #666666;z-index: 9;background: #ffffff;}	
.prompt-label .triangle-left{width: 0;height: 0;border-top: 5px solid transparent;border-right: 7px solid #eeeeee;border-bottom: 5px solid transparent;position: absolute;left: -7px;top: 5px;}
.prompt-label .triangle-left2{width: 0;height: 0;border-top: 4px solid transparent;border-right: 6px solid #ffffff;border-bottom: 4px solid transparent;position: absolute;left: -6px;top: 6px;}
.prompt-label .triangle-right{width: 0;height: 0;border-top: 5px solid transparent;border-left: 7px solid #eeeeee;border-bottom: 5px solid transparent;position: absolute;right: -7px;top: 5px;}
.prompt-label .triangle-right2{width: 0;height: 0;border-top: 4px solid transparent;border-left: 6px solid #ffffff;border-bottom: 4px solid transparent;position: absolute;right: -6px;top: 6px;}
.opr-button{display: inline-block;height: 80px;padding: 8px 20px 0 20px;border-right: 1px solid #dddddd;vertical-align: top;}	
.opr-button input[type="text"]{width: 64px;height: 44px;background: #bcbcbc;border: 1px solid #333333;padding: 0 30px 0 10px;vertical-align: baseline;}
.examinei-btn{border-radius: 2px; color: #fff; margin-left: 10px; display: inline-block; text-align: center; color: #fff; padding: 2px;}
.relevance-label{padding: 2px 5px;border-radius: 3px;color: #f14030;z-index: 9;background: #f8dfde;font-size: 12px; position: relative;top: -22px;display: inline-block;left: -18px;}	
.relevance-label .triangle-left{width: 0;height: 0;border-top: 5px solid transparent;border-right: 7px solid #f8dfde;border-bottom: 5px solid transparent;position: absolute;left: -7px;top: 5px;}
.relevance-label .triangle-left2{width: 0;height: 0;border-top: 4px solid transparent;border-right: 6px solid #f8dfde;border-bottom: 4px solid transparent;position: absolute;left: -6px;top: 6px;}
.buy-title .common-tab li.current{background:#00AB9E;border: 1px solid #007169; color: #ffffff; margin-top:-1px;border-bottom: none;}
.buy-title .common-tab li{background: #EEEEEE; border-right: 1px solid #C4C4C4;border-top:1px solid #C4C4C4;color:#00ab9e }
.buy-title .common-tab li.current::before {
    content: "";
    position: absolute;
    top: -1px;
    left: 0;
    width: 100%;
    border-top: 1px solid #007169;}
.buy-title .common-tab{margin-top: -1px;}
.buy-title .common-tab li u.buyone_ico{background:url(../images/tongbuy/buyone_icoHL.png) no-repeat; width: 16px; height: 16px; display: inline-block; margin-right:5px;}
.buy-title .common-tab li.current u.buyone_ico{background:url(../images/tongbuy/buyone_ico.png) no-repeat;}
.buy-title .common-tab li u.buytwo_ico{background:url(../images/tongbuy/buytwo_icoHL.png) no-repeat; width: 10px; height: 15px; display: inline-block; margin-right:5px;}
.buy-title .common-tab li.current u.buytwo_ico{background:url(../images/tongbuy/buytwo_ico.png) no-repeat;}
.buy-title .common-tab li u.buythree_ico{background:url(../images/tongbuy/buythree_icoHL.png) no-repeat; width: 16px; height: 14px; display: inline-block; margin-right:5px;}
.buy-title .common-tab li.current u.buythree_ico{background:url(../images/tongbuy/buythree_ico.png) no-repeat;}
.buy-title .icon_tips:before{ color:#ff7800;font-size: 12px;margin-top: -22px;margin-left: -3px; }
/*加减按钮*/
.down_bg .opr-inp{border: 1px solid #F0F0F0;height: 22px;}
.down_bg .opr-inp input[type="button"]{background: #F0F0F0;font-size: 20px;line-height: 22px;cursor: pointer;color: #333333;vertical-align: top;padding: 0;}
.down_bg .opr-inp input[type="text"]{color: #333333;vertical-align: top;padding-left: 0;}
.down_bg .opr-inp input[type="button"]:first-child{font-size: 22px;}
/*订单管理*/
.def-btnspace .layui-btn{min-width: 88px;padding: 0 10px;}
.def-btnspace .layui-btn+.layui-btn{margin: 10px 0 0 0;}
/*收退费*/
.returns-top{height: 62px; background:#F3F3F3; border-bottom: 1px solid #D0D0D1;position: relative; color: #202428; font-size: 12px; padding-top: 10px;}
.returns-left{width:180px;height: 100%;border-left: 1px solid #D0D0D1;border-right: 1px solid #D0D0D1;position: relative;float: left; background:#e7e7e7;}
.paste-tit{ margin-left: 10px; width:80px; text-align: center; display: inline-block; }
.rshear-tit{width: 75px; display: inline-block; padding-top: 3px;border-right: 1px solid #D3D3D3}
.returns-main u,.plan-left u,.page-left u，.returns-footer u{display:inline-block;background:url(../images/tongcw/_icon.png) no-repeat; vertical-align: middle; text-decoration: none;cursor: pointer;}
u.returns_icon{background-position: 0px -706px;width:40px;height: 34px;}
u.returns_icon1{background-position: -35px -713px;width: 28px; height: 24px;}
u.returns_icon2{background-position: -60px -714px;width: 28px; height: 24px; margin-top: 2px;}
u.returns_icon3{background-position: -86px -715px;width: 28px; height: 24px;}
u.returns_icon4{background-position: -128px -715px;width: 28px; height: 24px;}
u.returns_icon5{background-position: -2px -742px;width: 28px; height: 24px;}
u.returns_icon6{background-position: -167px -709px;width: 16px; height: 24px;}
u.returns_icon7{background-position: -33px -742px;width: 28px; height: 24px;}
u.returns_icon8{background-position: -58px -742px;width: 28px; height: 24px;}
u.returns_icon9{background-position: -86px -742px;width: 28px; height: 24px;}
u.returns_icon10{background-position:-90px -787px;width: 24px; height: 24px;}
u.returns_icon11{background-position:-124px -787px;width: 24px; height: 24px;}
u.returns_icon12{background-position: -159px -787px;width: 15px; height: 24px;}
u.returns_icon13{background-position: 0px -828px;width: 15px; height: 24px;}
u.returns_icon21{background-position: -20px -828px;width: 15px; height: 24px;}
u.returns_icon14{background-position: -20px -785px;width: 20px; height: 15px;}
u.returns_icon15{background-position: -42px -785px;width: 20px; height: 15px;}
u.returns_icon16{background-position: -64px -785px;width: 20px; height: 16px;}
u.returns_icon17{background-position: -20px -814px;width: 20px; height: 15px;}
u.returns_icon18{background-position: -42px -814px;width: 20px; height: 15px;}
u.returns_icon19{background-position: -64px -814px;width: 20px; height: 15px;}
u.returns_icon20{background-position:-98px -811px;width: 30px; height: 24px;}
u.red_ico{background-position:-38px -670px;width: 15px; height: 24px;}
u.green_ico{background-position:-37px -692px;width: 13px; height: 24px;}
u.red_money_ico{background-position:-51px -670px;width: 16px; height: 24px;}
u.green_money_ico{background-position:-54px -692px;width: 13px; height: 24px;}
u.red_state_ico{background-position:-94px -668px;width: 12px; height: 24px;}
u.green_state_ico{background-position:-105px -668px;width: 12px; height: 24px;}
u.grey_reduce_ico{background-position:-115px -667px;width: 12px; height: 24px;}
.array-tit {overflow: hidden; width:93px; margin:0 20px; display: inline-block;}
.create-tit{width: 100px; display: inline-block; border-right: 1px solid #D3D3D3;border-left: 1px solid #D3D3D3; padding:0 20px;  }
.array-tit li{ text-align: center; width: 16px; padding: 5px; float: left; margin-left:5px; }
.array-tit li.cur-bg{ background:#c5c5c5 }
.create-btn{ display: block; border: 1px solid #c5c5c5; background: #FFF; line-height: 20px; margin-bottom: 7px; height: 20px;}
.create-tit span:last-child{ margin-bottom: 0;}
.pre-btn{ display:inline-block; background: #1da89e;line-height: 30px; height: 30px; border-radius: 3px; color: #fff; font-size:16px; text-align: center; padding: 5px 20px; margin:6px 20px 0 0px;}
.premonth { width: 99px; height:31px; margin: 0 auto 0px; background: url(../images/newtong/icon.png) no-repeat 0 0px; cursor: pointer; _filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src='../images/newtong/icon.png',sizingMethod='crop'); _background-image: none; background-position:0 10px; line-height:31px; position:relative; }
.nextmonth { width: 99px; height: 25px; margin: 1px auto; background: url(../images/newtong/icon.png) no-repeat 0 0px; cursor: pointer; _filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src='../images/newtong/icon.png',sizingMethod='crop'); _background-image: none; background-position:0 -23px; position:relative; top:-4px; }
.returns-list{ width:156px; background: #ffffff; color: #9f9f9f;border: 1px solid #eeeeee; margin: 10px; font-size: 16px; font-weight: bold;}
.newmouth{font-size: 60px; color: #9f9f9f; display:inline-block; text-align: center;}
.circle-btn{ text-align: center;width: 20px; height: 20px; text-align: center; line-height: 20px; border-radius: 50%; color: #fff; font-size:8px; display: inline-block; margin-left: 2px;margin-right: 2px;}
.red-con{ color:#e7624d;padding: 5px 0; position: relative; font-size: 12px;}
.red-con .circle-btn{background:#e7624d;width: 15px; height: 15px;line-height: 16px; margin-left: 5px; margin-right: 2px;}
.geeen-con{color:#25c26e; padding: 5px 0;position: relative;font-size: 12px;}
.geeen-con .circle-btn{background:#25c26e;width: 15px; height: 15px;line-height: 16px; margin-left: 5px; margin-right: 2px;}
.blue-con{color:#4699ff; padding: 5px 0;position: relative;}
.blue-con .circle-btn{background:#4699ff;font-size:12px;}
.ico_right{ position: absolute; right: 0px;}
.total-tit{ background: #fff; bottom:3px; height: 42px;  position: absolute;border-top: 1px solid #eeeeee;width: 100%; color: #575655; font-size: 12px; padding-top: 5px;}
.page-left{width:30px;height: 100%;position: relative;float: left; background:#e7e7e7; text-align: center; color: #333333; font-size:14px;}
.page-left li{padding: 2px; text-align: center;  display: block; margin: 0 auto; border: 1px solid #D0D0D1; margin: 10px 3px; background: #fff; line-height: 17px;}
.page-left li.cur-bg{color: #207246; border: 1px solid #207246;}
.page-left li.first-bg{color: #207246; border: 1px solid #d3d3d3; background:#d3d3d3}
.page-left li.more-bg{ border: none; background:#e7e7e7;}
.page-left ul{ border-top: 1px solid #D0D0D1;margin-top: 5px;}
.page-ico{ position: absolute; bottom: 15px;}
.page-ico u{ margin-top: 15px;}
.layui-form-top{ background:#e7e7e7; height: 35px; padding-top: 5px}
.oper-cen{text-align: center;  display:inline-block; margin: 0 auto; border: 1px solid #D0D0D1;  background: #fff; line-height: 17px;padding: 2px 6px;}
.eject-label{border: 1px solid #d3d3d3;position: absolute;right: -105px;top:18px;padding: 2px;width: 99px;border-radius: 3px;color: #666666;z-index: 9;background: #ffffff;}	
.eject-label .triangle-left{width: 0;height: 0;border-top: 5px solid transparent;border-right: 7px solid #eeeeee;border-bottom: 5px solid transparent;position: absolute;left: -7px;top: 5px;}
.eject-label .triangle-left2{width: 0;height: 0;border-top: 4px solid transparent;border-right: 6px solid #ffffff;border-bottom: 4px solid transparent;position: absolute;left: -6px;top: 6px;}
.page-cen{position:relative;}
.page-label{ border: 1px solid #d3d3d3;position: absolute;right: -105px;bottom:-7px;width: 103px;border-radius: 3px;color: #666666;z-index: 9;background: #ffffff;}	
.page-label .triangle-left{width: 0;height: 0;border-top: 5px solid transparent;border-right: 7px solid #eeeeee;border-bottom: 5px solid transparent;position: absolute;left: -7px;bottom: 16px;}
.page-label .triangle-left2{width: 0;height: 0;border-top: 4px solid transparent;border-right: 6px solid #ffffff;border-bottom: 4px solid transparent;position: absolute;left: -6px;bottom: 17px;}
.page-label p{ line-height: 34px; border-bottom:1px solid #d3d3d3; color:#333333; font-size:12px;display: flex; align-items: center; justify-content: center;}
.page-label p span{ display: inline-block; text-align:center; width:80px; }
.page-label input{vertical-align: middle; margin-right: 10px;}
.page-label .cur-cor{ color:#1da89e;}
.page-label p:last-child{border-bottom:none;}
.jump-btn{ text-align: center; display: inline-block; background:#1da89e;  font-size: 12px; color: #fff; padding: 2px 10px; /*float: right;*/ margin-top:2px;}
.money-tit{ display: inline-block;  line-height: 24px; vertical-align: middle;}
.returns-list.cur-redbg{ border: 1px solid #E7624D;color:#C64B4F;}
.returns-list.cur-redbg .newmouth{ color:#C64B4F;}
.nav-footer{ position:absolute; bottom:0;background:#e7e7e7; height:47px; border-top:1px solid #999999; width:100%;}
.add-prompt{ text-align: center; display: inline-block;   font-size: 12px; color: #999999;}
.select-year{ border-radius: 2px; overflow: hidden;margin: 10px 10px 0 10px;}
.select-year li{background:#f3f3f3; border-bottom: 1px solid #DDDDDD;text-align: center; color: #666666; font-size: 12px;line-height: 25px}
.select-year li.curren{background:#1da89e; color: #fff; }
.page-next{float: left;width: 238px;}
.page-next li{width: 20px; height: 20px; text-align: center;  display: block; border: 1px solid #D0D0D1; margin: 5px 3px; background: #fff; line-height: 20px; float: left;}
.page-next li.cur-bg{color: #207246; border: 1px solid #207246;}
.page-next li.first-bg{color: #207246; border: 1px solid #d3d3d3; background:#d3d3d3; margin-left: 10px;}
.page-next li.more-bg{ border: none; background:#e7e7e7;}
.statistics_pic{border: 1px solid #c5c5c5;width: 28px; height:27px; text-align: center;background: #fff; position: absolute;left: 1px;top:6px;line-height: 25px;}
/*通用切换*/
.returns-tab.layui-tab-title{ border-bottom:none;height:47px;}
.returns-tab.layui-tab-title li{position: relative;width: 100px;height:47px;margin-top: -1px;line-height:45px; color:#403b40; font-size:12px;}	    	
.returns-tab.layui-tab-title li:before{content: "";position: absolute;left: 0;border-left: 1px solid #999999;height: 100%;}
.returns-tab.layui-tab-title li:first-child:before{content: none;}
.returns-tab.layui-tab-title .layui-this{background: #fff;height: 45px;vertical-align: top; color:#207246;}
.returns-tab.layui-tab-title .layui-this:after {
    position: absolute;
    left: 0;
    top: 0;
    content: '';
    height: 47px;
    box-sizing: border-box;
    pointer-events: none;
    border-bottom:2px solid #01a39a;
	border-top:none; 
	border-left:none;
 
}
.nav-footer-rt{display: inline-block;float: right;margin-right: 30px;position: relative;margin-top: -34px; display: flex;}
.nav-footer-rt span{ padding:0 5px; margin-right:10px;display:flex; justify-content: center; align-items: center; color: #9d9f9e; font-size:14px; border:1px solid #c2c2c2; background:#fff;}
.nav-footer-rt span .iconfont{ margin-right:3px;}
.nav-footer-rt span.blue-bor{ border-bottom: 2px solid #207246;}


/*分园采购计划*/
.buy-title{height: 50px;border-top: 1px solid #eeeeee;border-bottom: 1px solid #eeeeee;position: relative;background: #ffffff;}
.tend-label{background: #e6e6e6;width: 28px;height: 28px;line-height: 28px;text-align: center;border-radius: 50%;display: inline-block;color: #12c680;}
.inset-label{padding: 0 12px;height: 64px;line-height: 64px;background: #ffffff;color: #666666;display: inline-block;vertical-align: middle;box-shadow: -4px -4px 5px #dddddd inset;}
.prompt-label{border: 1px solid #eeeeee;position: absolute;right: -80px;padding: 2px;width: 60px;border-radius: 3px;color: #666666;z-index: 9;background: #ffffff;}
.prompt-label .triangle-left{width: 0;height: 0;border-top: 5px solid transparent;border-right: 7px solid #eeeeee;border-bottom: 5px solid transparent;position: absolute;left: -7px;top: 5px;}
.prompt-label .triangle-left2{width: 0;height: 0;border-top: 4px solid transparent;border-right: 6px solid #ffffff;border-bottom: 4px solid transparent;position: absolute;left: -6px;top: 6px;}
.opr-button{display: inline-block;height: 80px;padding: 8px 18px 0 16px;border-right: 1px solid #dddddd;vertical-align: top;}
.opr-button input[type="text"]{width: 64px;height: 44px;background: #bcbcbc;border: 1px solid #333333;padding: 0 30px 0 10px;vertical-align: baseline;}
.form-info{display: block;border: 4px solid red;border-image: url(../image/borderline.png) 4 round;margin: 14px 15px;background: #f2f0db;padding: 12px 0;box-shadow: -5px 5px 7px -5px inset #999999;}
.form-info .inp-label{color: #666666;font-size: 14px;display: inline-block;line-height: 34px;}
.form-info .inp-label input[type="text"]{border: none;border-bottom: 1px dashed #5eaada;margin: 0 10px;color: #333333;font-size: 14px;}
.form-name{display: inline-block;vertical-align: top;color: #1478b7;font-size: 16px;font-weight: bold;margin: 8px 20px 0 40px;}
.form-opr{height: 54px;background: #eff4f7;border-top: 1px solid #cccccc;padding: 0 15px;}
.form-opr .a_close{font-size: 14px;margin-top: 17px;display: inline-block;}
.form-opr .a_close img{margin-right: 5px;vertical-align: top;margin-top: 3px;}
.form-opr .form-submit{float: right;line-height: 28px;margin-top: 12px;}
.form-top{height: 52px;line-height: 52px;min-width: 1000px;margin: 0 15px;font-size: 14px;color: #333333;}
.supplier-sign{font-weight: bold;}
.supplier-sign img{border: 1px solid #eeeeee;border-radius: 4px;margin-right: 8px;}
.form-num{background: url(../image/waveline_bg.png)no-repeat center bottom;padding: 3px 0;float: right;line-height: 22px;height: 20px;margin-top: 15px;}
.form-note{background: #f4faff;height: 40px;padding: 0 10px 0 5px;}
.form-note input[type="text"]{height: 30px;background: #ffffff;border: none;width: 50%;margin: 5px 0;}

/*对应园所食物库*/
.left-column{width: 194px;float: left;border: 1px solid #eeeeee;height: 100%;margin: 0 20px;background: #ffffff;box-sizing: border-box;}
.left-column h4{height: 58px;line-height: 58px;background: #ffffff;padding: 0 15px;cursor: pointer;}
.content-area{overflow: hidden;margin-right: 20px;height: 100%;box-sizing: border-box;}
.content-top{height: 58px;line-height: 58px;margin: 0 20px 0 20px;}
.tab-pic{padding: 0!important;}
.tab-pic>div{position: relative;width: 50px;height: 44px;margin: 3px auto!important;}
.amp-img{position: absolute;bottom: 0;right: 0;cursor: pointer;}
.content-area .layui-table td{padding: 0!important;}
.content-area .layui-table td>div,.layui-table td>a{margin: 17px 0;}
.column-menu.def-checkbox input[type="checkbox"]{display: inline-block;position: absolute;top: 9px;left: 50px;}
.food-list{line-height: 26px;color: #666666;display: inline-block;vertical-align: top;margin-left: 25px;}
.food-list li span{display: inline-block;width: 130px;color: #999999;}
.food-con{background: #f7f7f7;padding: 14px;position: relative;margin: 0 20px 20px 20px;}
.food-con .icon_del{position: absolute;right: 30px;bottom: 23px;width: 20px;height: 23px;line-height: 23px;cursor: pointer;}
.food-opr{padding: 0 20px;text-align: right;background: #ffffff;border: 1px solid #EEEEEE;border-bottom: none;position: absolute;bottom: 0;left: 234px;right: 292px;height: 98px;}
.gray-select .layui-form-select .layui-input{background: #F2F2F2;}
.foodtype{width: 22px;height: 22px;border: 1px solid #ffffff;border-radius: 4px;margin-right: 10px;padding: 2px;background: #ffffff;}
/*新版采购增加的样式*/
.data-buy-lt{ 
    background: #fff;
    box-shadow: 0 5px 5px -4px inset #DEDEDE;
    height: 42px;
	color: #34495E;
	font-size: 14px;
	border-bottom: 1px solid #EDEDED;
}
.data-buy-lt .common-tab li{color: #34495E;}
.data-buy-lt .common-tab.tabheight li.current{color: #34495E;font-weight: bold;}
.data-buy-lt .common-tab li.current {
    background:none;
}
.reacquirebg img{width: 20px;}
/*保教工作上传视频*/
.upload-list{background: #fff; font-size: 14px; color: #333333; padding: 10px 20px; border-bottom: 1px solid #EEEEEE;} 
.upload-tit{ border-bottom: 1px solid #EEEEEE; padding-left:20px;line-height: 35px; font-size: 16px;}
.upload-list .upload-left,.teaching-list .upload-left{display: inline-block;width: 75px;height:34px;line-height:34px;text-align:right;margin: 6px 5px 6px 0px;vertical-align: top;}
.upload-list .upload-right,.teaching-list .upload-right{display: inline-block;width: 635px;margin: 5px 0;line-height: 34px;height: 34px;position: relative;vertical-align: top;}
.upload-list input,.teaching-list input{height: 36px;line-height: 36px;width: 277px; padding: 0 10px;}
.upload-bor{ border: 1px solid #eeeeee;}
.upload-right label{ color:#bbbbbb; font-size: 12px; margin-left: 20px;}
.upload-cen label{ color:#999999; font-size: 12px; margin-right: 30px; display: inline-block;}
.upload-video,.upload-cen{ color: #666666; font-size: 12px; padding-left: 85px; line-height: 20px;margin: 5px 0;}
.upload-btn{text-align:right;box-shadow: 0 -6px 10px -2px #eeeeee; padding: 22px 20px; background: #fff}
.upload-btn .layui-btn{ margin-right: 10px; padding: 0 30px;border-radius: 6px;}
.upload-list .layui-btn-normal,.teaching-list-bom .upload-right .layui-btn,.cover-list .layui-btn{ color: #fff; font-size:14px; border-radius: 5px;}
.mar-greybg{ height:15px; background:#F6F6F6; width: 100%; display: block;}
.video-sel{position: relative;width: 100px;height: 100px;margin: 0 auto;}
.video-btn{background: rgba(0,0,0,0.6);cursor: pointer;font-size: 12px;color: #ffffff;position: absolute;left: 5px;bottom: 3px;padding: 0 10px;border-radius: 20px;}
.video-btn .icon_play{height: 20px;display: inline-block;vertical-align: top;}
/*保教工作计划*/
.protect-educate .plan-left{width: 30px;background: #ffffff;}			
.protect-educate .plan-txt{background: #f4f4f4;color: #666666;border-radius: 8px 0 0 8px;border: 1px solid #dddddd;border-right: none;}
.protect-educate .plan-txt+.plan-txt{margin-top: 3px;}	
.protect-educate .common-tab li .iconfont:before{color: #cccccc;}	
.protect-educate .common-tab li.current .iconfont:before{color: #fd9d9d;}	
.protect-educate .common-tab li.current{color: #fd9d9d;}	
.protect-educate .common-tab li.current:before{border-top: 4px solid #fd9d9d;}
.protect-educate .plan-left-icon{position: absolute;bottom: 10px;}
.protect-educate .plan-left-icon>div{cursor: pointer;width: 30px;height: 34px;}
.protect-educate .plan-left-icon>div.current{background: #f4f4f4;}
.protect-educate .plan-left-icon>div.current .icon_ratio:before{border: 2px solid #fd9d9d;}
.protect-educate .plan-left-icon .iconfont{width: 18px;height: 20px;display: inline-block;vertical-align: top;margin-top: 7px;}
.protect-educate .plan-left-icon .iconfont:before{vertical-align: top;}	    
.protect-educate .icon_week{vertical-align: top;margin-right: 10px;display: inline-block;width: 24px;height: 24px;line-height: 24px;vertical-align: middle;}	
.rest-plan{text-align: left!important;}
.rest-plan .icon-width{width: 30%;display: inline-block;text-align: right;font-size: 14px;}
.rest-plan .txt-width{width: 70%;display: inline-block;font-size: 14px;}
.rest-plan .rest-txt1,.rest-plan .rest-txt2{font-size: 0;}
.rest-plan .rest-txt1 label{border-bottom: 1px solid #eeeeee;padding: 4px 0;}
.rest-plan .rest-txt2{padding-top: 4px;}
.rest-plan i.iconfont{width: 16px;height: 16px;display: inline-block;vertical-align: top;margin-top: 1px;}
.rest-plan i.iconfont:before{vertical-align: top;}	    	
.stat-mark{background-size: 100% 100%;width: 21px;height: 21px;font-size: 9px;color: #ffffff;line-height: 12px;position: absolute;top: 0;}
.stat-mark.bluebg{background: url(../image/blue_markbg.png)no-repeat;}
.stat-mark.orangebg{background: url(../image/orange_markbg.png)no-repeat;}
.table-opr{width: 22px;height: 46px;background: #ffffff;position: absolute;top: 3px;right: 5px;border-radius: 3px;text-align: center;}
.table-opr .icon_collect{margin-top: 2px;}
.table-opr .icon_delete{margin-top: -2px;}
.table-opr .icon_collect:before{font-size: 14px;}
.table-opr .icon_delete:before{font-size: 19px;}			
.class-info{display: inline-block;margin-top: 5px;}
.timetable-num{width: 70px;height: 26px;line-height: 26px;color: #ffffff;font-size: 12px;border: 2px solid #ffffff;border-radius: 20px;display: inline-block;float: right;margin: 12px 5px 0 0;text-align: center;
	background: -webkit-linear-gradient(left, #4ae094 , #2fbd74); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(right, #4ae094, #2fbd74); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(right, #4ae094, #2fbd74); /* Firefox 3.6 - 15 */
    background: linear-gradient(to right, #4ae094 , #2fbd74); /* 标准的语法 */		
}			
.timetable-num i{font-size: 16px;margin-right: 5px;font-weight: bold;}			
.class-name{font-size: 16px;color: #333333;font-weight: bold;}	
.class-time{font-size: 12px;color: #999999;}			
.collect-btn{background: #ffffff;padding: 6px 4px;border-radius: 4px;position: absolute;top: 5px;left: 5px;}	
.fold-div{background: #eeeeee;height: 24px;line-height: 24px;color: #767b86;margin: 8px 12px 0 12px;text-align: left;padding: 0 5px;cursor: pointer;}			
.fold-div .iconfont{height: 14px;line-height: 14px;display: inline-block;vertical-align: top;margin-top: 6px;}
.online-coursebtn{margin: 0px 10px 8px 10px;overflow: hidden;}
.online-coursebtn li{line-height: 40px; text-align: center;height: 40px;color: #8892a0;font-size: 16px; border: 1px solid #b1dbf7; background:#f1f4f7;float: left;width: 118px;}
.online-coursebtn li.current{color: #ffffff;border: 1px solid #6bc0f6; background:#6bc0f6;}
.online-coursebtn li:first-child{margin-right:10px;}
/*保教工作上传音频*/
.audio-fre{background:#f8f8f8; border: 1px solid #EEEEEE; padding: 10px; overflow: hidden;margin-left: 85px; margin-top: 10px; width: 355px;}
.audio-fre span{display: block; float: left; background:#aed7ff; text-align: center;height: 33px; padding: 0 10px; border-radius: 5px; margin-right: 10px;}
.audio-fre h3{ color: #333333; float: left; width:287px; }
.audio-fre label{ color: #999999; font-size: 12px; float: left; display: inline-block;} 
/*保教工作选择材料*/
.cho-material{display:block;  padding:5px 30px 0 30px; color: #333333; font-size: 14px;background: #ffffff;}
.cho-material .layui-form-select .layui-input{ background:#F2F2F2;}
.cho-btn{ text-align:right;margin-bottom: 15px;}
.cho-cen{background:#f8f8f8;padding-bottom: 50px; }
/*保教工作计划-添加教案*/
.teaching-list{font-size: 14px; color: #333333; overflow: hidden;padding: 15px 0px;}
.upload-right li{ background:url(../images/education/grey.png) no-repeat right 14px; color:#666666; font-size: 14px; position: relative; border: 1px solid #EEEEEE; border-radius: 3px;
 display: inline-block; margin-right: 10px; padding: 0 15px; line-height: 24px;cursor: pointer; }
.upload-right li .icon_check4{position: absolute;bottom: -10px;right: -3px;}
.upload-right li.current{border: 1px solid #2abb71;color: #2abb71;}
.upload-right li.current .icon_check4:after{color: #2abb71;}
.upload-left em{color: #FCB6B6;}
.bor-bot{ border-bottom: 1px dashed #DADADA; min-width: 530px; float: left; margin: 18px 0 3px 0;}
.teaching-list .layui-btn{ background:#f8f8f8; border: 1px solid #eeeeee;}
.teaching-list-bom{ border-top:1px solid #EEEEEE; padding: 7px 0;}
.teaching-list-bom.bornone{border-top:none;}
.cover-tit{border-bottom:1px solid #EEEEEE; padding: 8px 0; color: #333333; font-size: 14px; margin: 0; }
.cove-img{ width: 117px; height:178px; float: left; margin:10px 17px 12px 0;}
.cover-list{ width:180px; display: inline-block; color: #666666; font-size: 10px; }
.cover-list span{ display: block; color:#37b6ff; text-decoration: underline; font-size: 14px; line-height: 30px; padding-top:74px; }
.cover-list label{line-height: 16px; display: block; margin: 3px 0 13px 0;font-size: 12px;color: #c0c0c0;}
.source-list{ overflow: hidden;}
.source-list span { display:block; border-bottom: 1px  solid #DADADA; margin-left: 42px;font-size: 14px; color: #666666;padding: 12px 0;}
.drag-tit{ float:right;}
.add-tit{text-align: center; margin: 7px 0 7px 80px;}
.add-list{ background: #f8f8f8;  border: 1px solid #e6e6e6; margin-left:80px; overflow: hidden; padding: 5px; position: relative;}
.add-picleft{ width:88px; height: 88px; border: 1px solid #eeeeee; float: left; position:relative; margin-right: 8px;}
.add-picleft img{ width:88px; height: 88px;}
.add-picright{margin-left:85px; overflow: hidden; }
.add-picright .layui-textarea{min-height:88px;border: 1px solid #eeeeee;border-radius: 0;}
.add-pictop{ background:url(../images/education/cover_img.png) no-repeat; text-align: center; display: block; height: 18px; width: 43px; color: #fff; font-size: 10px; position: absolute; left: -5px; top:2px;}
.add-picbottom{background-color: rgba(0, 0, 0, 0.5); position: absolute; bottom: 0; display: block; height: 20px; line-height: 19px; width: 100%; text-align: center; color: #fff; font-size: 12px; }
.enclosure-list{border: 1px solid #e6e6e6; border-bottom: none;margin-left:80px; padding: 5px 0; color: #666666; font-size: 14px; position: relative;line-height: 24px;}
.enclosure-list.grey,.upload-lable .grey{background:#f8f8f8; }
.enclosure-list .icon_enclosure:before{ color:#68a8e1;}
.enclosure-txt .enclosure-list:last-child{border-bottom: 1px solid #e6e6e6;}
.play-cen{ background-color: rgba(0, 0, 0, 0.5); border-radius: 50%; line-height: 28px;width: 30px; height: 30px;position: absolute;top:50%;left:50%;transform:translate(-50%,-50%);text-align: center;}
.play-cen .icon_play:before{color: #ffffff;font-size: 12px;}
.upload-lable{ position:relative; display: inline-block;margin-right: 10px;}
.upload-lable span{  border: 1px solid #eeeeee;display: inline-block; text-align: center; font-size: 12px; color: #666666; line-height: 24px; padding: 0px 10px; background:#ffffff;  border-radius: 3px;cursor: pointer;}
.grade-num{
	display: inline-block;
	vertical-align: middle;
	color: #ffffff;
	border-radius: 30px;	
	height: 36px;
	line-height: 36px;
	margin-top: 3px;
	padding: 0 7px;
	background: -ms-linear-gradient(left,#23c7e7, #21e7bc);
	background: -webkit-linear-gradient(left,#23c7e7, #21e7bc); /* Safari 5.1 - 6.0 */
	background: -o-linear-gradient(left,#23c7e7, #21e7bc); /* Opera 11.1 - 12.0 */
	background: -moz-linear-gradient(left,#23c7e7, #21e7bc); /* Firefox 3.6 - 15 */
	background: linear-gradient(left,#23c7e7, #21e7bc); /* 标准的语法 */
}
.grade-num .icon_arrowup,.grade-num .icon_arrowdown,.grade-num .icon_minus2{width: 12px;height: 12px;line-height: 12px;background: #ffffff;border-radius: 50%;display: inline-block;vertical-align: inherit;margin: 0 3px 2px 3px;}
.type-plan{color: #999999;font-size: 12px;display: inline-block;vertical-align: top;height: 42px;}
.type-plan li{display: inline-block;height: 42px;margin: 0 2px;cursor: pointer;}
.type-plan li p{margin: 0;margin-top: 1px;}
.type-plan .iconfont:before{color: #cccccc;}
.type-plan .iconfont.grayicon:before{color: #cccccc;}
.type-plan .iconfont.redicon1:before{color: #ffc9c9;}
.type-plan .iconfont.redicon3:before{color: #ff6f6f;}
.type-plan .icon_health,.type-plan .icon_science,.type-plan .icon_language,.type-plan .icon_art,.type-plan .icon_social{width: 18px;height: 18px;display: inline-block;vertical-align: top;margin-top: 6px;}
.timetable-list{margin: 0 auto 5px auto;width: 254px;height: 52px;position: relative;background: #e6e6e6;text-align: left;}
.timetable-list .circle-label{cursor: pointer;width: 42px;height: 42px;display: inline-block;border: 1px solid #ffffff;border-radius: 50%;color: #ffffff;position: absolute;left: 50%;margin-left: -24px;bottom: 4px;text-align: center;font-size: 12px;}
.timetable-list .circle-label .icon_arrowright{height: 15px;display: inline-block;vertical-align: top;margin-top: 4px;}
.plan-left .icon_collect,.plan-left .icon_delete{cursor: pointer;height: 17px;line-height: 17px;display: inline-block;}
/*.plan-left .icon_collect{position: absolute;left: 15px;top: 9px;}
.plan-left .icon_delete{position: absolute;left: 15px;bottom: 9px;}*/
/*.plan-left .icon_collect:before,.plan-left .icon_delete:before{color: #d1d1d1;}*/
.plan-left .icon_collect.current:before{color: #ff6f6f;}
.plan-left .icon_sort{cursor: pointer;position: absolute;right: 12px;bottom: 10px;background: #ffffff;width: 20px;height: 20px;line-height: 22px;border-radius: 50%;display: inline-block;box-shadow: 0 0 7px #666666;}

.timetable-list .icon_add{width: 22px;height: 22px;line-height: 22px;display: inline-block;vertical-align: top;}
.plan-left .white-btn{cursor: pointer;background: #ffffff;color: #1cb1ff;position: absolute;left: 5px;top: 13px;padding: 5px 8px;border-radius: 4px;font-size: 12px;}
.align-middle{display: table-cell;vertical-align: middle;}	
.align-middle .icon_add{cursor: pointer;width: 78px;height: 78px;line-height: 78px;border: 1px solid #ffffff;border-radius: 50%;display: inline-block;}
.align-middle .icon_add:before{color: #ffffff;font-size: 36px;}			
.opreate-icon{display: inline-block;vertical-align: top;margin-top: 15px;}
.opreate-icon .iconfont{width: 21px;height: 23px;margin-right: 5px;}		
.opreate-icon .icon-txt{display: inline-block;height: 23px;margin-right: 20px;cursor: pointer;} 	
.opreate-icon .icon-txt span{display: inline-block;vertical-align: middle;font-size: 14px;}		
.opreate-icon .icon-txt .iconfont{color: #cccccc;}	
.opreate-icon .icon-txt .iconfont.current{color: #fd9d9d;}							
.course-div{text-align: left!important;}
.course-img{position: relative;display: inline-block;vertical-align: top;float: left;width: 97px;height: 144px;}
.course-img p{background: #fffcf2;height: 28px;line-height: 28px;margin: 0;text-align: center;border-top: 1px solid #dddddd;border-right: 1px solid #dddddd;}
.course-div .course-txt{overflow: hidden;height: 144px;}			
.course-txt .course-mark{padding: 7px 10px 3px 7px;border-bottom: 1px solid #dddddd;height: 48px;overflow: hidden;}
.course-txt .course-mark li{cursor: pointer;position: relative;display: inline-block;background: #ffffff;height: 20px;line-height: 20px;font-size: 12px;padding: 0 5px;margin-bottom: 4px;margin-right: 6px;}
.course-txt .course-mark li img{position: absolute;top: 0;right: -4px;}
.course-txt .course-file{line-height: 56px;text-align: center;}
.course-txt .course-p{font-size: 12px;color: #7e7979;margin: 0 7px;line-height: 26px;}
.course-img .title-mark{font-size: 12px;padding: 1px 5px;min-width: 48px;text-align: center;position: absolute;top: 8px;}
.course-img .white-btn{top: 5px;right: 5px;left: auto;font-size: 14px;}
.course-info{height: 56px;font-size: 12px;color: #999999;border-bottom: 1px solid #dddddd;}
.person-list{font-size: 0;padding: 0 17px;}
.person-list img{margin-left: -10px;background: #ffffff;width: 26px;height: 26px;border-radius: 50%;border: 1px solid #ffffff;}
.course-type{text-align: left;margin: 0 0 0 12px;}
.course-type select{width: 95px;margin: 2px 8px 2px 0;font-size: 14px;border: 1px solid #e9e9e9;}
.course-type .type-sel{display: inline-block;width: 84%;font-size: 0;vertical-align: top;}
.course-type .type-name{height: 26px;line-height: 26px;display: inline-block;}
.course-type li{margin: 5px 0;}
.course-type li label{font-size: 14px;border-radius: 3px;height: 22px;line-height: 22px;display: inline-block;padding: 0 6px;margin: 2px 10px 2px 0;cursor: pointer;}
.course-type li label.current{color: #fd9d9d;}
.course-type .type-sel.noborder-sel label{border: none;margin-right: 1px;}
.course-type .type-sel.noborder-sel label.current{color: #fd9d9d;}	    	
.course-list .course-img{border: 1px solid #dddddd;width: 116px;height: 168px;margin: 0 5px 5px 0;}
.course-list .course-img img,.course-list .course-img p{border: none!important;}	    	
.loading-img{width: 36px;height: 36px;position: absolute;top: 50%;margin-top: -18px;left: 50%;margin-left: -18px;}	    	
.prompt-info{position: absolute;right: -200px;bottom: -120px;background: #f6f5e8;z-index: 99;text-align: left;color: #333333;font-size: 14px;width: 196px;border: 1px solid #e9e7d2;border-radius: 4px;}
.prompt-info li{border-bottom: 1px solid #e9e7d2;margin-left: 15px;padding: 8px 0;}
/*保教工作计划-查看课程详情*/
.title-top{font-size: 16px;height: 48px;line-height: 48px;padding: 0 20px;background: #ffffff;border-bottom: 1px solid #eeeeee;}
.title-top .icon_remind:before{font-size: 18px;color: #333333;}	    	
.course-detail h4{font-size: 22px;color: #333333;font-weight: bold;margin: 15px 0;}
.course-detail p{font-size: 14px;color: #666666;line-height: 26px;margin: 0;}
.play-con{position: relative;background: #f8f8f8;height: 50px;line-height: 50px;font-size: 0;border-radius: 3px;margin: 15px 0 30px 0;}	    	
.play-con .play-txt{font-size: 14px;color: #999999;display: inline-block;vertical-align: middle;text-align: center;}	    	  	
.play-con .progressbar_1{background: #dddfe0;height: 2px;width: 72%;display: inline-block;vertical-align: middle;}
.play-con .progressbar_1 .bar{background: #30ca79;height: 2px;}
.play-con .icon_play{cursor: pointer;height: 22px;line-height: 22px;width: 6%;text-align: center;padding-left: 1%;}
.play-con .icon_play:before{color: #30ca79;}
.annex-con{border: 1px solid #eceff3;}
.annex-con h6{height: 36px;line-height: 36px;background: #eceff3;padding: 0 15px;font-size: 16px;color: #333333;}
.annex-con .icon_annex{height: 18px;line-height: 20px;margin: -2px 5px 0 0;}
ul.annex-list{margin: 15px 20px;}
ul.annex-list li+li{margin-top: 15px;}
.annex-list li label{font-size: 14px;color: #666666;cursor: pointer;}
.annex-list li label .iconfont{height: 20px;line-height: 20px;margin: 0 10px;vertical-align: top;}
.annex-list .icon_download{height: 18px;line-height: 18px;float: right;cursor: pointer;}
.play-img{position: relative;height: 380px;margin: 10px 0;}
.play-img .icon_play{cursor: pointer;position: absolute;top: 50%;margin-top: -45px;left: 50%;margin-left: -45px;width: 90px;height: 90px;background: rgba(255,255,255,0.3);border-radius: 50%;line-height: 90px;text-align: center;}
.play-img .icon_play:before{color: #ffffff;font-size: 30px;}	    	
.course-opr{background: #ffffff;padding: 0 24px; overflow: hidden;}
.course-opr .icon_collect,.course-opr .icon_share,.course-opr .icon_edit{height: 18px;line-height: 18px;margin-right: 5px;}
.course-opr .icon_collect:before,.course-opr .icon_share:before{font-size: 18px;color: #cccccc;}
.course-opr .icon_edit:before{font-size: 18px;color: #ffffff;}	    	
.course-opr .type-label{display: inline;}
.course-opr .type-label li{padding: 0 8px;height: 24px;line-height: 24px;border: 1px solid #eeeeee;border-radius: 3px;margin: 2px;}
.collect-per img{margin: 0 3px 6px 0;}	    	
.comment-con .comment-list{padding: 12px 0;border-bottom: 1px solid #eeeeee;}
.comment-con .comment-list:last-child{border-bottom: none;}
.comment-con .comment-list .comment-per{width: 26px;height: 26px;margin-top: -3px;border-radius: 50%;float: left;}
.comment-con .comment-list .comment-txt{overflow: hidden;margin-left: 36px;line-height: 22px;}
.comment-con .comment-list .comment-txt p{margin: 0;font-size: 14px;color: #666666;}	  
.active-name{margin: 3px 15px;}
.active-name label{background: #ff9c00;font-size: 12px;color: #ffffff;margin-left: 5px;padding: 2px 8px;border-radius: 3px;}	    	
.play-exp{position: absolute;top: 45px;left: 0;font-size: 12px;height: 22px;line-height: 22px;color: #999999;border-radius: 3px;background: #fff;border: 1px solid #eeeeee;padding: 0 10px;box-shadow: 0 0 5px -1px #cccccc;}
.play-exp .triangle{position: absolute;left: 20px;border-width: 0 6px 8px;} 
/*保教工作计划-月目标*/
.plan-leftspan{display: inline-block;background: #dddddd;height: 34px;line-height: 34px;color: #666666;font-size: 14px;padding: 0 20px 0 15px;border-radius: 9px 0 0 9px;position: absolute;left: -60px;min-width: 35px;cursor: pointer;}									    	
.plan-rightspan{display: inline-block;background: #dddddd;height: 34px;line-height: 34px;color: #666666;font-size: 14px;padding: 0 0 0 25px;border-radius: 0 9px 9px 0;position: absolute;right: -70px;min-width: 55px;cursor: pointer;}
.plan-leftspan.currentred,.plan-rightspan.currentred{color: #ffffff;}
.plan-leftspan.currentpink,.plan-rightspan.currentpink{color: #ffffff;}
.plan-con{background: #f1f1f1;border-radius: 20px;height: 270px;position: absolute;width: 100%;box-sizing: border-box;font-size: 0;}
.plan-list{display: inline-block;vertical-align: top;width: 50%;height: 100%;font-size: 14px;position: relative;}
.plan-list ul{margin: 12px 0 12px 20px;font-size: 14px;}
.plan-list ul li{height: 30px;border-bottom: 1px dashed #dddddd;} 	
.plan-list .plan-top{height: 42px;line-height: 42px;border-bottom: 2px solid #dddddd;margin: 0 20px;font-size: 16px;color: #333333;margin-top: 12px;}
.plan-list .inp-title{background: url(../images/education/linebg.gif)repeat;height: 30px;line-height: 30px;margin: 0 20px;}
.plan-list .inp-textarea{position: relative;margin: 0 20px;}
.plan-list .inp-textarea .inp-word{position:absolute;line-height:30px;left:48px;top: 0px;z-index:10;font-weight: bold;}
.plan-list .inp-textarea textarea{border:none;text-indent:93px;line-height:30px;background:url(../images/education/linebg.gif) repeat;overflow:auto;width: 100%;padding: 0;}			
.table-list{display: inline-block;width: 50%;font-size: 14px;}
.table-list h5{height: 46px;line-height: 46px;text-align: center;}
.table-list .def-layui-table tr th:first-child,.table-list  .def-layui-table tr td:first-child{border-left: 1px solid #e2e2e2;}
.table-list .def-layui-table td,.table-list .def-layui-table th{padding: 0;text-align: center;}
.table-list .def-layui-table tr th:first-child,.table-list .def-layui-table tr td:first-child{padding: 0;}
.table-list .def-layui-table th>div,.table-list .def-layui-table td>div{min-height: 36px;display: table;width: 100%;text-align: center;}
/*保教设置*/
.calendar-tit{ background:#fd9d9d; border-bottom: 5px solid #ec8383; width:100%; height:50px; border-top-left-radius: 10px; border-top-right-radius: 10px; text-align:center;
 color: #ffffff; font-size: 24px; padding-top: 70px; font-weight: bold; }
.calendar-cen{padding:40px 15px 0 15px;position: relative; margin-top: 30px;}
.calendar-bot{ background: #fff; border-bottom-left-radius: 20px; border-bottom-right-radius: 20px; overflow: hidden; padding:30px 0 40px 0;}
.calendar-bot .cal-botleft{ width: 48%;margin-left: 15px;}
/*作息安排*/
.workrest-list{display: inline-block;font-size: 14px;width: 50%;height: 100%;box-sizing: border-box;vertical-align: top;position: relative;}
.workrest-list h5{height: 56px;line-height: 56px;color: #ffffff;font-size: 18px;padding: 0 10px 0 30px;}	    
.def-layui-table.space-table tr:nth-child(2n+1){background: #f8f8f8;}
.icon-list{float: right;}
.icon-list .iconfont{margin-right: 5px;}
.icon-list .iconfont:before{color: #ffffff;font-size: 20px;}
.icon-list .layui-btn+.layui-btn{margin-left: 1px;}
.workrest-con{position: absolute;top: 62px;bottom: 10px;left: 10px;right: 10px;}	    		    	
/*.workrest-con .layui-input-inline input[type="text"]{width: 70px;height: 28px;border: 1px solid #e6e6e6;text-align: center;}
.workrest-con .layui-form-select .layui-input{height: 30px;}
.workrest-con .layui-form-select dl{top: 30px;}*/
.switch-div{margin: 0;}
.switch-div .layui-form-onswitch,.switch-div .layui-form-switch{margin: 0;}
.switch-div .layui-input-block{margin: 0;min-height: auto;}
.icon-list .iconfont.icon_return1:before{font-size: 22px;}
/*关联课程*/
.course-cell{height: 680px;display: inline-block;border: 1px solid #e2e2e2;width: 570px;margin: 10px 3px;text-align: left;vertical-align: top;}
.course-cell h5{position: relative;font-size: 16px;color: #333333;background: #fbeded;height: 42px;line-height: 42px;text-align: center;border-bottom: 1px solid #e2e2e2;}		
.search-box{display: inline-block;margin-top: 15px;padding: 0 10px;}
.search-box input{width: 270px;display: inline-block;vertical-align: middle;height: 32px;line-height: 32px;}
.search-box .layui-btn{width: 65px;background: #fd9d9d;height: 32px;line-height: 32px;margin-left: 5px;}
.course-dl{margin: 10px;overflow-y: auto;}
.course-dl dl{background: #f8f8f8;border: 1px solid #e2e2e2;margin-bottom: 5px;position: relative;}
.course-dl dl dt{border: 1px solid #eeeeee;display: inline-block;}
.course-dl dl dt img{width: 88px;height: 88px;}
.course-dl dl dd{display: inline-block;font-size: 14px;color: #333333;vertical-align: middle;margin-left: 15px;}
.course-dl dl dd p{margin: 5px 0 0 0;}
.course-dl dl dd label{position: absolute;top: 50%;margin-top: -15px;right: 5px;}
.course-dl.rela-course dl dd label{margin-top: -9px;right: 10px;}
.course-dl.rela-course dl dd label .iconfont{height: 18px;display: inline-block;vertical-align: top;cursor: pointer;}
/*在线模版库*/
.template-cen {padding: 10px; position: absolute; height: 95%; margin: 0 auto;}
.template-list h3{text-align: center; color:#333333; font-size: 16px; border-bottom: 1px solid #eeeeee; line-height: 40px; height: 40px;}
.template-list p{padding: 0 0px 10px 0px; color: #19a6ff; text-decoration: underline; font-size: 14px;}
.template-left{height:90%;position: relative; background: #fff;border: 1px solid #eeeeee;width: 50%; float: left;}
.template-left .template-list{width: 24.88%; border-right:1px solid #e2e2e2;  height: 100%;}
.template-left .template-list:last-child{border-right:none;}
.template-list li{text-align: center; height: 30px; line-height: 30px; background:#eaeaea; margin-bottom:5px;font-size: 12px; color: #999999;}
.template-list li.time-worksel{ background:#fd9d9d; color: #fff;}
.template-right{ float: right;border: 1px solid #eeeeee; width: 49%;height:90%;position: relative;}
.template-left, .template-right { overflow-y: auto;}
/*新作息制度*/
.workrest-left{display: inline-block;font-size: 14px;height: 100%;box-sizing: border-box;vertical-align: top;position: relative;float: left;}
.workrest-left h5,.workrest-right h5{height: 51px;line-height: 51px;color: #ffffff;font-size: 18px;padding: 0 10px 0 30px;}	
.workrest-right{background:#fff;  overflow: hidden; height: 100%;}
.timetable-list.add-course{text-align: center; background:#fbfbfb;height: 50px;margin: 0 auto;border: 2px dashed #dddddd;font-size: 16px;color: #999999;margin-bottom: 5px;box-sizing: border-box;cursor: pointer;}
.time-work{overflow: hidden;}
.time-work li{text-align: center; height: 50px; line-height: 50px; background:#e6e6e6; margin-top:5px; font-size: 16px; color: #999999; position: relative;}
.time-worksel-img{position: absolute; left: 0; top:-16px;}
.time-work li.time-worksel{ color: #fff;}
.time-work .icon_edit{ width: 20px; height: 20px; background: #ECECEC; border-radius: 50%; text-align: center; position: absolute; right: 40px; top:5px;}
.time-work .icon_close_block::before{ right:10px; top:-9px;font-size: 25px;}
.time-work .icon_edit::before{ color:#009cff; font-size: 12px; line-height: 20px; }
.activity-set{position: absolute; bottom: 10px; left: 319px; background:#d4e6ff; border-radius: 5px; width:64px; height: 48px; padding: 8px 0;  }
.activity-set p{text-align: center; width:48px; margin: 0 auto; height: 40px; background: #70ADFF; color: #fff; font-size:14px; line-height: 15px; border-radius: 5px;  padding-top:8px; }
/*新作息制度无*/
.no_message{ position:absolute;top:50%;left:50%; width:244px;transform:translate(-50%,-50%); text-align: center;}
/*新周安排*/
.week-tab{display: inline-block;width: 112px;height: 40px;background: #dddddd;color: #999999;border-radius: 8px 8px 0 0;text-align: center;}
.week-tab.current{color: #ffffff;}
.week-tab h5{font-size: 18px;font-weight: bold;margin: 7px 0;}
.week_class{border: 1px solid #e2e2e2; margin: 10px; overflow: hidden;}
.week_classlist{margin: 0px; padding-bottom:15px;overflow: hidden; }
.week_classtit { background: #f9f3e5;border-bottom: 1px solid #e2e2e2;line-height: 40px;height: 40px;padding: 0 10px 0 17px;color: #333333; font-size: 16px; cursor: pointer;}
.week_classlist li{width: 132px; margin:10px 0 0 10px; height: 180px; text-align: center; float: left;}
.week_classlist li img{width: 132px; height: 155px;}
.week_classlist p{color: #333333; font-size: 16px; line-height: 25px;}
/*新学期计划内容*/
.plan-main{ padding: 15px 10px 10px 30px;}
.plan-content{ text-align: left;}
.plan-content h3{font-size: 22px;color: #333333; margin-bottom: 10px; padding-left:0; }
.plan-content h5{font-size: 14px;color: #333333; height: auto; line-height:30px;padding-left:0;font-weight: bold;}
.plan-content{overflow: hidden;}
.plan-content p{ font-size: 14px; color: #666666; line-height: 25px; padding-left: 0;margin:0px 0px;}
.plan-content-bot{background:#f8f8f8;overflow: hidden; padding: 10px 5px 5px 15px; margin-top: 15px;}
.plan-flow-con{margin: 0 15px 0 15px;height: 100%;}
.plan-flow-list{padding-left: 25px;position: relative;padding: 7px 0 14px 25px;min-height: 30px;}
.plan-flow-icon{background: #fff;width: 29px;height: 29px;position: absolute;left: -15px;top: 0; border-radius: 50%;}
.plan-flow-icon img{ width: 29px; height: 29px;}
.plan-flow-list h3{font-size: 16px;color: #333333;margin-top: -5px;}
.plan-flow-list p{font-size: 12px;color: #999999; margin: 3px 0px;}
.plan-see{border-radius:5px;padding: 3px 5px; margin-left: 10px;display: inline-block;}
.plan-template{border-radius:3px;padding: 5px 20px;display: inline-block; float: right;margin-top: -8px;cursor: pointer;}
.plan-marking{background:#f6f6f6; margin:0 10px 0px 10px; padding: 10px 20px 10px 10px; }
.plan-browse{ font-size: 10px; color: #666666; padding: 5px 10px;}
.plan-browse img{width: 20px; height: 20px; border-radius: 50%; margin-left:5px; }
.remark-btn{ background:#ffffff; border: 1px solid #5faafd; border-radius: 20px; color:#5faafd; font-size: 14px; padding: 2px 10px; float: right;cursor: pointer;}
.collect-div{color:#333333; font-size: 14px; overflow: hidden;margin-top: 10px;}
.collect-div li{float: left; width: 25%;line-height: 20px;}
.plan-proposal {padding: 0 10px 0 10px; color: #666666;}
.plan-proposal li{width:100%;line-height: 20px;}
.collect-div li i.yellow-tit,.plan-left .plan-proposal li i.yellow-tit{color:#fab54d; }
.collect-div li i.red-tit,.plan-left .plan-proposal li i.red-tit{color:#ed4b4a; }
.collect-div li i.gray-tit,.plan-left .plan-proposal li i.gray-tit{color:#9a9a9a; }
.collect-div li i,.plan-left .plan-proposal li i{color: #dddddd; font-size: 0; margin-left: 5px; }
.collect-div .icon_collect::before,.plan-left .plan-proposal li .icon_collect::before{font-size: 18px;}
.plan-flow-detail{background: #ffffff;padding: 10px 10px 5px 10px; margin-top: 5px;}
.plan-openbtn{background:#37b6ff; color: #fff; margin: 10px auto; text-align: center; border-radius: 3px; width: 133px; line-height:35px; height: 35px; font-size: 14px;}
.plan-proposal{ margin: 10px 10px 0 10px; border: 1px solid #DDDDDD; background: #ffffff; overflow-y: auto; height: 282px;}
.plan-left .plan-proposal li .icon_collect{ position: inherit;width:auto;height: auto;line-height: normal;background: #ffffff;border-radius: 0px;display: inline-block;}
.plan-left .plan-proposal li .icon_collect::before{vertical-align:super;}
.plan-proposal ul{border-top: 1px dashed #dddddd; padding: 10px 0; margin-top: 5px;}
.plan-proposal h3{ color: #333333; font-size: 14px; padding-top: 5px;margin-bottom:0px;}
.plan-proposal p{ color:#999999; font-size: 12px;}
.layui-form-item .layui-input-inline.plan-summary{width: 97%;}
@media screen and (min-width:900px) and (max-width:1000px){
	.layui-form-item .layui-input-inline.plan-summary{width: 93%;}
	.marginleft{left: 330px;}
	
	}
@media screen and (min-width:1000px) and (max-width:1100px){
	.layui-form-item .layui-input-inline.plan-summary{width: 93.5%;}
	.marginleft{left: 330px;}
	}
@media screen and (min-width:1100px) and (max-width:1360px){
	.layui-form-item .layui-input-inline.plan-summary{width: 94%;}
	.marginleft{left: 330px;}
	}
@media screen and (min-width:1360px) and (max-width:1440px){
	.layui-form-item .layui-input-inline.plan-summary{width: 95%;}
	}
@media screen and (min-width:1440px) and (max-width:1700px){
	.layui-form-item .layui-input-inline.plan-summary{width: 96%;}
	}
@media screen and (min-width:1700px) and (max-width:1800px){
	.layui-form-item .layui-input-inline.plan-summary{width: 96.5%;}
	}
/*新设置全园计划内容*/
.settup-plan {margin: 0px auto;position:absolute;height: 92%;  width: 100%; top:0;
}
.settup-plan ul{height:100%;overflow: hidden;position: relative;float: left;border-right: 1px solid #eeeeee;box-sizing: border-box; background: #fbfbfb; width:170px; }
.settup-plan li{text-align: center; height: 30px; line-height: 30px; background:#eaeaea; margin-bottom:5px; font-size: 12px; color: #999999; }
.settup-plan li.time-worksel{ background:#fd9d9d; color: #fff;}

.settup-cell{background: #ffffff;border: 1px solid #E2E2E2; height: 185px;}
.settup-cell h3{height: 42px;line-height: 42px;padding: 0 15px;border-bottom: 1px solid #E2E2E2;font-size: 16px;color: #333333; background: #e4ecf8;}
.settup-cell dt{display: inline-block;vertical-align: top;position: relative;float: left; width:35px; font-size: 14px;color:#333333; padding: 10px 5px 0 15px; }
.settup-cell dd{font-size: 14px;margin-left:50px;line-height: 25px; overflow: hidden; padding: 7px 10px 0 0px; color: #666666;}
.settup-cell dd .btmopr-btn{position: absolute;bottom: 25px;}
.settup-cell dd .btmopr-btn button{vertical-align: top;width: 88px;height: 30px;line-height: 30px;}	
/*新历史评语模版*/
.crit-template{ margin:0 15px; overflow: hidden;}
.crit-template-tit{ height: 10px; border-bottom: 1px dashed #E6E6E6;}
.crit-template-nt{position: relative;height: 30px; margin-top: 20px;}
.crit-template-list{ margin-bottom: 10px;}
.crit-template span{background: #fff;  display: block; position: absolute;  padding-right: 10px; font-size:14px; color: #333333; }
.crit-template-right{background:#f9f9f9; padding: 10px 30px 10px 10px; font-size: 14px; color:#666666; margin-left: 28px; line-height: 25px; position: relative;}
.crit-template-btn{font-size: 14px; color: #cdcdcd; position: absolute; right:8px; bottom:0px; cursor: pointer}
/*查看修改详情*/
.amen-details-left{ width:594px; height: 500px; overflow-y: auto;border: 1px solid #e2e2e2;}
.amen-details-left:last-child{ overflow-y: auto;}
.amen-details-left h1{ text-align: center; color: #333333; font-size: 16px;background:#E4ECF8;line-height: 44px; height: 44px;}
.amen-details-txt{background:#36b1ff; text-align: center;font-size: 12px; color: #ffffff; height: 20px; display: block;line-height: 20px;}
.amen-details-bg{ background:#ffc7c7; }
/*订单管理*/
.order-title .title-column:last-child{border: none;}
.title-column{display: inline-block;font-size: 12px;box-sizing: border-box;vertical-align: middle;min-width: 300px;margin: 0 10px;}
.title-column label{margin-left: 3px;color: #666666;}
.title-column .graymark{width: 70px;height: 70px;background: url(../images/tongbuy/mark_orangebg.png)no-repeat;background-size: 65% 65%;}
.title-column .graymark	i{top: 16px;left: 9px;}
.order-pic{font-size: 0!important;padding: 7px 5px 2px 5px!important;}
.order-pic img{margin: 0 5px 5px 0;}	    	
.lack-mark{background: #c42500;width: 30px;height: 30px;line-height: 30px;text-align: center;border-radius: 50%;display: inline-block;color: #fff;float: left;margin: 0 10px 0 0;}	    		    		    	
.sel-time{display: inline-block;vertical-align: top;margin: 8px 0 0 0;height: 30px;margin-left: 5px;}
.sel-time .icon_trian-top,.sel-time .icon_trian-down{height: 10px;display: block;cursor: pointer;}
.sel-time .icon_trian-top.current:after,.sel-time .icon_trian-down.current:after{color: #0dbd68;}
.info-left{min-width: 86px;display: inline-block;text-align: right;}
.form-text{display: inline-block;font-size: 12px;vertical-align: middle;width: 138px;}
.form-text>div{margin-left: 35px;}
.title-column .supplier-icon{width: 16px;height: 16px;vertical-align: text-bottom;margin-right: 3px;border-radius: 2px;}
.order-pic img{width: 80px;height: 80px;}
.order-type{text-align: left!important;vertical-align: top;padding: 10px 20px!important;}
.order-type .txt-elli{-webkit-line-clamp: 3;font-size: 14px;}
.order-money{text-align: left!important;font-size: 12px;color: #333333;vertical-align: top;padding: 10px 20px!important;}
.order-opr{text-align: center;font-size: 12px!important;}
.pulldown-lt{position: absolute;width: 529px; right:10px; top:50px; z-index: 999; height: 96px; background:#FFFFFF; border-bottom:5px solid #F0F1F5;box-shadow: 0 2px 3px -1px  #C9D5DC;}
/*查看入库信息*/
.library{ margin: 15px 20px 0 20px; border: 1px dashed  #dcdcdc ; border-radius: 3px; padding: 20px 30px; position: relative; overflow: hidden;}
.library-img{line-height: 25px; display: block; width: 27px;height: 25px; position: absolute; top:0px; left: 0; background: url(../image/kubg_left.png) no-repeat; text-align: center; color:#666666; font-size: 14px; }
.library .library-list{margin: 0;}
.library .library-list ul{width:50%;float: left; }
.library .library-list ul li{line-height:25px;height: auto; color: #666666; font-size: 14px;}
.library .library-list ul li .library-left{vertical-align: top; text-align: left; margin-right: 5px;}
.library-cor{ color: #24a5ff; text-decoration: underline}
.library .library-list p{margin: 0;}
/*添加食物*/
.sel-label{padding: 9px 15px;}
.sel-label span{border: 1px solid #cccccc;font-size: 12px;color: #666666;background: #ffffff;border-radius: 5px;padding: 5px 8px 4px 8px;position: relative;display: inline-block;margin-right: 10px;margin-bottom: 10px;}
.sel-label span img{position: absolute;top: -8px;right: -8px;width: 16px;height: 16px;background: #ffffff;border-radius: 50%;cursor: pointer;}
/*实时考勤*/
.atten-list{ background: #f9f8ea; margin:10px 10px 0 10px; border: 1px solid #e7e6d4; font-size: 14px; color: #6a5f5b; padding: 10px;}
.atten-top{border-bottom:1px solid #cccccc; overflow: hidden;}
.atten-top li{ float: left; text-align: center; font-size: 14px; color: #666666; line-height: 20px; height: 20px; width: 49.5%; border-right: 1px solid #d7d7d7; margin: 5px 0;}
.atten-top li a.cur{ border-bottom: 2px solid #1da89e;padding-bottom:3px;}
.atten-top li:last-child{border-right: none;}
/*食品采购计划*/
.week-cell{position: relative;text-align: center;display: inline-block;cursor: pointer;color: #333333;width: 191px;height: 60px;margin: 6px -10px 0 0;background: url(../image/week_bg.png)no-repeat;background-size: 100% 100%;overflow: hidden;}
.week-cell.current{background: url(../image/week_bg_HL.png)no-repeat;color: #ffffff;z-index: 9;}
.week-cell p{font-size: 18px;font-weight: bold;margin-top: 10px;}
.week-cell span{font-size: 12px;}
.audit-img{position: absolute;right: 8px;bottom: 0;
transform:rotate(-45deg);
-ms-transform:rotate(-45deg); 	/* IE 9 */
-moz-transform:rotate(-45deg); 	/* Firefox */
-webkit-transform:rotate(-45deg); /* Safari 和 Chrome */
-o-transform:rotate(-45deg); 	/* Opera */
}
/*采购系统第三版改版样式*/
.callnext_bg{position: relative; display:inline-block; width: 73px;margin:0 5px 0 5px;line-height:normal;}
.callnext_btn{width: 20px; height: 32px; margin: 1px; border-bottom-right-radius: 3px; border-top-right-radius: 3px;  display: inline-block;position: absolute; background:#f2f2f2; right: 0; top:0; border-left: 1px solid #dddddd; padding-top:1px; text-align: center;}
.callnext_bg .icon_trian-top,.callnext_bg .icon_trian-down{height: 15px;display: block;cursor: pointer;vertical-align: middle;  }
.callnext_bg .icon_trian-top::after,.callnext_bg .icon_trian-down::after{color: #b3b9c2; font-size: 14px;}
.explain-order{background:#FFF;box-shadow: 0 0 10px 3px #E0E0E0; position: absolute; top:138px; left:10px; z-index: 999; width: 407px; padding:0px 10px;}
.explain-order:before,.daily-order:after{content:"";display:block;border-width:10px;position:absolute; top:-20px;left:40px; border-color: transparent transparent #ffffff transparent;
        border-style: dashed dashed solid dashed;font-size:0; line-height:0;}
.explain-order:after{top:-18px; border-color: transparent transparent #ffffff transparent;} 
.explain-order th{color: #666666; text-align: center;}
.callnext_bgsel{position: absolute; left: 108px; top:52px; border: 1px solid #D8D8D8; border-radius: 3px;width: 160px;z-index: 999;background: #fff;}
.callnext_bgsel p{ border-bottom: 1px solid #F2F2F2; padding: 8px 0;}
.callnext_bgsel p:last-child{border-bottom: none;}
.selbg_div{cursor: pointer;margin-top: 18px;width: 13px; height: 13px;}
.this_week{ border: 1px solid #4eaeff; border-radius:0px; display: block; color: #4eaeff; margin: 0 auto; width:40px; font-size: 14px;line-height: 21px;}
.address-div{ border: 1px solid #eeeeee; background: #f8f8f8; padding: 10px; margin: 0 10px 10px 10px; position: relative;}
.address-div p{color:#666666;line-height: 22px;}
.address-div label{color:#333333; display: inline-block; width: 80px; text-align: right;}
.address-txt{display: inline-block; text-align: center; font-size: 12px; color: #fff; background:#fda032;border-radius: 2px; padding: 0 8px;line-height: 16px;}
.address-rt{font-size: 14px; color: #4292ff; position: absolute; right: 20px; top:10px;}
.dis_address h3{padding: 0 15px;margin: 10px 0;font-size: 16px;height: 30px;line-height: 30px;}
.address-btn{display: inline-block; color: #1da89e;  margin:10px 20px; width: 95px; text-align: center; font-size: 14px; background:#c5e5e3;border-radius: 2px;  border: 1px solid #01a39a; height: 30px; line-height: 30px;}
.edit-btn{font-size: 14px; color: #4292ff; position: absolute; right: 10px; bottom:10px;}
.edit-btn label{ color: #4292ff; width: auto; margin-right: 20px;}
.address-close{position: absolute; right: 20px; top:-10px;}
.address-div .radio-blue input[type="radio"]{margin-right: 0;}
.address-div .opr_default{float: right;color: #49a1ff;}
.daybuy-bot{position: absolute;top:340px;left: 0;width:100%;cursor: pointer; text-align: center;}
.daybuy-bot img{width: 20px; height: 20px; margin:5px 0; }
.daybuy-bot .set-people:hover,.daybuy-bot .set-send:hover,.daybuy-bot .set-address:hover,.daybuy-bot .set-food:hover{background:#E4E4E4; }
/*临时采购*/
.week_txt{  font-size:12px; color:#fff; text-align:center; padding:1px 2px; border-radius:3px; margin-right:1px;}
.week_green{background:#0ccb7c;}
.week_yellow{background:#FF8E29;}
.examine_txt{  font-size:12px; color:#fff; text-align:center; padding:1px 4px; border-radius:3px; margin-right:1px;}
.examine_txt.week_ltgreen{background:#FFF7E9; color:#FEB27E; }
.examine_txt.week_ltred{background:#F9F2F4;color:#DB4255; }
.new_txt{  font-size:12px; color:#fff; text-align:center; padding:0px 2px; border-radius:3px; margin-right:1px;}
/*添加食物*/
.rleft-column{width: 180px;float: left;border: 1px solid #eeeeee;height: 100%;background: #f2f2f2;box-sizing: border-box;}
.rleft-columnh4{height: 40px;line-height: 40px;padding: 0 15px;cursor: pointer;}
.rleft-column select{width: 170px;height: 40px;background: #f2f2f2;border: 1px solid #d8d8d8;display: inherit;margin: 8px auto;}     
.rleft-column select option{background: #ffffff;}  
.content-area{overflow: hidden;height: 100%;box-sizing: border-box;}
.right-column{width:390px;position: absolute;top: 0;right: 15px;float: left;border: 1px solid #eeeeee;height: 99%;box-sizing: border-box;}

/*日历弹框颜色*/
.data_blue{position: relative;background: #79C1FF;}
/*.data_blue:after{content: "";width: 8px;height: 8px;border-radius: 50%;background: #4eaeff;position: absolute;bottom: 3px;left: 50%;margin-left: -4px;}*/
.data_yellow{position: relative;background: #FFCC66;}
/*.data_yellow:after{content: "";width: 8px;height: 8px;border-radius: 50%;background: #f99b37;position: absolute;bottom: 3px;left: 50%;margin-left: -4px;}*/
.data_pink{position: relative;background: #FF8B99;}
/*.data_pink:after{content: "";width: 8px;height: 8px;border-radius: 50%;background: #fe7786;position: absolute;bottom: 3px;left: 50%;margin-left: -4px;}*/
/*幼儿体检 */
.measure-show{padding: 5px 20px;font-size: 16px;border-top: 1px solid #dddddd;border-bottom: 1px solid #dddddd;background: #f8f8f8;margin: -5px 0;}	
.measure-cell{height: 114px;background: #ffffff;} 
.measure-cell h5{height: 48px;line-height: 48px;border-bottom: 1px solid #eeeeee;padding: 0 20px;}   
/*最新版日订单样式 */
.amount-col{font-size: 18px; color:#ff6600; font-weight: bold;}
.operation-img{padding:5px 10px; margin-bottom:0;}
.operation-img img{margin-left: 10px; vertical-align: top; margin-top:6px; cursor: pointer; }
.reacquirebg{background: #eeeeee;color: #333333;cursor: pointer;padding: 4px 8px; display: inline-block; text-align: center; margin-left: 20px; position: relative;cursor: pointer;}
.reacquirebg img{margin-left: 0;margin-top: 2px;vertical-align:middle;}
/*.reacquirebg::before{border-left: 1px solid #DDDDDD; content: ""; position: absolute; width: 1px; height: 15px; left: -10px;}
*/.operation-img .rico{ position:relative;}
.operation-img .rico::before{border-right: 1px solid #DDDDDD; content: ""; position: absolute; width: 1px; height: 15px; right: -12px; top: 3px;}
#divseldate.table-com .tabselDate td {line-height: 40px;}
.relation-num{background: red;color: white;padding: 0 3px;border-radius: 50%;font-size: 12px;margin-left: 2px;min-width: 12px; height: 18px; line-height: 18px;display: inline-block;}
/*最新版调拨单信息样式 */
.redeploy-tit{line-height: 38px;height: 38px;border-bottom: 1px solid #eeeeee; font-size: 14px; font-weight: bold;}
/*校车接送*/
/*校车管理*/
.form-div{background: #ffffff;border: 1px solid #eeeeee;margin: 10px;}  	
.form-div h5{border-bottom: 1px solid #eeeeee;height: 48px;line-height: 48px;padding: 0 15px;font-size: 16px;color: #333333;}
.schoolbus-cell{background: #ffffff;padding: 27px 25px;}
.schoolbus-cell dt{display: inline-block;vertical-align: top;position: relative;}
.schoolbus-cell .shadow-box{background: rgba(255,255,255,0.7);}
.schoolbus-cell .shadow-mark{width: 50px;height: 50px;line-height: 50px;border-radius: 50%;background: #e70003;position: absolute;top: 50%;margin-top: -25px;left: 50%;margin-left: -25px;z-index: 999;font-size: 14px;color: #ffffff;text-align: center;}
.schoolbus-cell dt img{width: 180px;height: 180px;}
.schoolbus-cell dd{display: inline-block;font-size: 14px;margin-left: 20px;line-height: 28px;}
.schoolbus-cell dd label{font-weight: bold;}
.schoolbus-cell dd .btmopr-btn{margin-top: 10px;}
.schoolbus-cell dd .btmopr-btn button{vertical-align: top;width: 105px;height: 30px;line-height: 30px;}	    
.ban-bus.schoolbus-cell dd{color: rgba(51,51,51,0.5);}
/*线路管理*/
.route-con{border: 1px solid #dddddd;}
.route-title{height: 44px;line-height: 44px;border-bottom: 1px dashed #dddddd;padding: 0 20px;background: #f8f8f8;position: relative;}
.route-cell{white-space: nowrap;margin-left: 50px;}
.route-cell img{margin: 0 10px;}
.busopr-btn{position: absolute;right: 15px;top: 0;}
.busopr-btn button{height: 24px;line-height: 24px;}
.route-show{height: 120px;overflow-x: auto;}
.route-line{position: relative;margin: 13px 0;}
.bus-state{position: absolute;top: -1px;left: 16px;width: 20px;height: 20px;line-height: 20px;text-align: center;border-radius: 50%;display: inline-block;font-size: 12px;color: #ffffff;}
.bus-state.state-blue{background: #5e97e5;}
.bus-state.state-orange{background: #f99223;}
.remark-txt{font-size: 12px;color: #666666;margin: 5px 16px;}
.route-adddiv{border: 3px dashed #dddddd;background: #f8f8f8;height: 161px;text-align: center;font-size: 16px;color: #666666;}
.route-adddiv img{margin: 35px 0 10px 0;}
/*编辑车辆*/
.upload-dl dt{display: inline-block;}
.upload-dl dd{display: inline-block;vertical-align: bottom;line-height: 20px;margin-left: 20px;}
.upload-dl dd p{font-size: 12px;color: #999999;}	    
/*线路人员设置*/
.content-left{background: #f8f8f8;width: 260px;height: 100%;float: left;} 
.content-left h5{margin: 12px;font-size: 15px;color: #333333;} 
.content-right{overflow: hidden;height: 100%;}
.content-right form{height: 54px;background: #ffffff;border-bottom: 1px solid #dddddd;width: 100%;}
.clearbtn{background: #ff7070;color: #ffffff;font-size: 12px;padding: 2px 10px;border-radius: 4px;position: absolute;right: 12px;top: 5px;}
.mark-person{width: 31px;height: 30px;background: #ff942b;position: absolute;left: 0;top: 0;border-radius: 0 0 50px 0;font-size: 10px;padding-top: 5px;padding-right: 5px;text-align: center;line-height: 11px;}
.mark-person i{font-size: 16px;}
.route-name{font-size: 0;padding: 12px 0;line-height: 12px;height:100%;}
.route-name li{height: 25px;line-height: 25px;position: relative;width: 170px;display: inline-block;font-size: 14px;}
.route-name li.current{background: #e3e3e3;}
.route-name li a{display: inline-block;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;margin-left: 18px;cursor: pointer;}
/*新增线路*/
.route-top{font-size: 16px;color: #333333;height: 48px;line-height: 48px;border-bottom: 1px solid #eeeeee;padding: 0 20px;background: #ffffff;}	    	
.route-increase{width: 430px;background: #ffffff;position: absolute;top: 60px;right: 24px;border-top: 4px solid #1da89e;}
.note-route{background: #f4f4f4;margin: 8px 22px;padding: 4px 25px;font-size: 12px;color: #333333;line-height: 18px;position: relative;}
.note-route .icon_bulb{position: absolute;left: 5px;top: -3px;}
.search-route{padding: 0 22px;margin-bottom: 6px;}
.search-route .layui-btn{height: 32px;line-height: 32px;float: right;margin-top: 2px;}
.search-route .icon_location:before{color: #ffffff;}
.route-local{width: 405px;background: #ffffff;box-shadow: 0 0 9px 1px #e5e5e5;border-radius: 5px;padding-top: 20px;position: relative;}
.route-local .layui-form-item{margin: 5px 0 5px 20px;}
.route-local .icon_location:before{color: #000000;}
.route-local .triangle{position: absolute;bottom: -10px;left: 50%;margin-left: -6px;border-bottom-color: #ffffff;
	transform:rotate(180deg);
	-ms-transform:rotate(180deg); 	/* IE 9 */
	-moz-transform:rotate(180deg); 	/* Firefox */
	-webkit-transform:rotate(180deg); /* Safari 和 Chrome */
	-o-transform:rotate(180deg); 	/* Opera */
}
.website-con{padding: 12px 15px 12px 40px;border: 1px solid #ccc;margin: 0 22px;height: 390px;overflow-y: auto;}		
.website-mark{z-index: 9;width: 20px;height: 20px;line-height: 20px;border-radius: 50%;background: #b9b9b9;display: inline-block;font-size: 12px;color: #ffffff;text-align: center;position: absolute;top: 0;left: -30px;}
.website-cell{border: 1px solid #eeeeee;padding: 5px 10px;line-height: 20px;position: relative;margin-bottom: 12px;color: #666666;}
.website-cell:after{content: "";border-left: 4px dashed #dddddd;position: absolute;top: 0;bottom: -12px;left: -22px;}	    	
.website-con .website-cell:last-child:after{content: none;}
.website-con .website-cell:first-child .website-mark{width: 24px;height: 24px;line-height: 24px;background: #2ec16c;font-size: 14px;left: -32px;}
.website-con .website-cell:last-child .website-mark{width: 24px;height: 24px;line-height: 24px;background: #f24e4e;font-size: 14px;left: -32px;}
.website-con .triangle{position: absolute;bottom: -5px;left: 50%;margin-left: -3px;border-bottom-color: #b9b9b9;border-width: 0 3px 6px;
	transform:rotate(180deg);
	-ms-transform:rotate(180deg); 	/* IE 9 */
	-moz-transform:rotate(180deg); 	/* Firefox */
	-webkit-transform:rotate(180deg); /* Safari 和 Chrome */
	-o-transform:rotate(180deg); 	/* Opera */
}
.website-con .website-cell:first-child .triangle{border-bottom-color: #2ec16c;}
.website-con .website-cell:last-child .triangle{border-bottom-color: #f24e4e;}
.website-con .website-cell.current{background: #f0f0f0;}
.website-con .website-cell .website-name{color: #333333;}
.website-con .website-cell .stay-time{font-size: 12px;color: #999999;}
/*校车系统说明*/
.exp-moudle{height: 48px;line-height: 48px;padding: 0 13px;border-bottom: 1px solid #eeeeee;font-size: 16px;color: #333333;}    
.exp-moudle label{width: 22px;height: 22px;line-height: 22px;background: #53c2bc;border-radius: 50%;display: inline-block;color: #ffffff;text-align: center;margin-right: 8px;}	
.exp-step{border: 1px solid #dddddd;}
.exp-step h5{height: 46px;border-bottom: 1px solid #dddddd;display: table;width: 100%;}	
.exp-step h5 p{display: table-cell;vertical-align: middle;padding: 0 15px;}    
.exp-step img{width: 100%;}		
/*智慧饮水*/
.drink-title{height: 68px;line-height: 68px;text-align: center;border: 1px solid #f2f5f8;border-radius: 8px;background: #ffffff;font-size: 30px;color: #3aa9ff;font-weight: bold;letter-spacing: 3px;}    
.drink-cell{background: #ffffff;border: 1px solid #eef2f8;border-radius: 8px;}
.drink-cell h5{height: 48px;line-height: 48px;padding: 0 20px;border-bottom: 1px solid #eef2f8;font-size: 16px;color: #333333;}
.num-celltxt{text-align: left;margin: 10px 0 5px 15px; font-size: 12px;}
.num-cell{display: inline-block;height: 114px;width: 27%;margin: 19px 8px;color: #ffffff;}
.num-cell p{font-size: 24px; line-height: 30px;text-align: left;padding: 0 15px;}
.num-circle{width: 82px;height: 82px;line-height: 82px;text-align: center;border-radius: 50%;color: #ffffff;margin-bottom: 5px;}
.num-circle span{font-size: 28px;}
.num-tishi{position: relative;margin-left: 8px;display: inline-block;background: #f6f6f6;vertical-align: top;margin-top: 16px;line-height: 20px;padding: 15px 7px;font-size: 12px;color: #666666;border-radius: 4px;}
.num-tishi .triangle{top: 50%;margin-top: -7px;left: -2px;border-top:8px solid #f6f6f6;}
.circle-cell{display: inline-block;margin: 0 10px;position: relative;}
.circle-cell label{color: #666666;margin-top: 5px;}
.circle-con .circle-cell:after{content: "";border-right: 3px solid #f4f4f4;position: absolute;top: 14px;right: -15px;height: 60%;}
.circle-con .circle-cell:last-child:after{content: none;}
.checkcell-con{font-size: 0;}	    	
.check-cell{display: inline-block;font-size: 14px;width: 50%;position: relative;height: 415px;vertical-align: top;}
.check-cell:after{content: "";height: 60%;border-right: 1px solid #eef2f8;position: absolute;right: 0;top: 20%;}
.checkcell-con .check-cell:last-child:after{content: none;}
.check-cell .layui-table td,.check-cell .layui-table th{font-size: 12px;}
.check-cell .layui-table td img{vertical-align: top;margin: 3px 5px 0 0;}
.stat-circle{width: 300px;height: 300px;position: relative;font-size: 12px;color: #ffffff;background: url(../images/drinking/statdata_bg.png);}
.stat-circle h6{font-size: 14px;}
/*饮水监控*/
.tab-state label{background: #eef2f8;color: #666666;font-size: 14px;cursor: pointer;display: inline-block;width: 70px;height: 26px;line-height: 26px;margin-right: 1px;text-align: center;}   	          	        
.tab-state label.current{background: #64a5fc;color: #ffffff;}
.tab-state label:first-child{border-radius: 4px 0 0 4px;}
.tab-state label:last-child{border-radius: 0 4px 4px 0;}
.tabbtn{width: 24px;height: 250px;background: #f6f6f6;display: inline-block;text-align: center;}
.tabbtn i{margin-top: 100px;display: inline-block;}
.equip-ul{display: inline-block;}
.equip-ul li{border: 1px solid #dddddd;width: 130px;display: inline-block;margin: 3px;}
.equip-ul li h6{font-size: 14px;color: #358dff;height: 38px;line-height: 38px;padding: 0 13px;text-decoration: underline;cursor: pointer;}
.equip-img{background: #dddddd;text-align: center;padding: 6px 0;}	    	
.date-con{text-align: center;position: absolute;top: 35%;left: 50%;margin-left: -163px;z-index: 9;}
.date-cell{display: inline-block;vertical-align: top;margin: 0 2px;}
.date-arrow{background: #dfe8f5;border-radius: 3px;height: 20px;line-height: 20px;text-align: center;}
.date-num{position: relative;border: 1px solid #64a5fc;border-radius: 3px;font-size: 36px;color: #333333;height: 100px;line-height: 100px;padding: 0 20px;margin-top: 5px;font-weight: bold;}
.date-num:before{content: "";height: 50%;width: 100%;background: #f2f7fb;position: absolute;top: 0;left: 0;z-index: -1;}
.date-num:after{content: "";height: 50%;width: 100%;background: #ffffff;position: absolute;bottom: 0;left: 0;z-index: -1;}
.drinkper-cell{margin-left: 36px;padding: 12px 0;border-bottom: 1px dashed #e7e7e7;position: relative;}
.drinkper-cell .per-pho{width: 46px;height: 46px;border-radius: 50%;}
.water-cell{display: inline-block;max-width: 75%;overflow-x: auto;white-space: nowrap;vertical-align: middle;}
.water-cell img{width: 25px;height: 34px;margin: 0 3px;}
.water-txt{display: inline-block;vertical-align: middle;margin: 0 5px;}
.water-txt p{font-size: 16px;color: #333333;max-width: 100px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}
.water-txt span{font-size: 12px;color: #999999;}
.thumb-img{width: 17px;height: 17px;position: absolute;left: -3px;}	    	
.timebase-cell{align-items: flex-start;margin:7px 0;}
.timebase-num{justify-content: center;flex-direction: column;align-items: center;margin: 0 20px 0 12px;font-size: 18px;color: #999999;}
.timebase-num span{position: relative;}
.timebase-num span:before{content:"";width: 8px;height: 8px;border-radius: 50%;background: #cccccc;position: absolute;left: -15px;top: 7px;}
.timebase-txt:after{content: "";width: 100%;height: 1px;background: #e6e6e6;position: absolute;top: 50%;}
.timebase-txt{border-radius: 4px;position: relative;min-height: 24px;}
.timebase-cell.current .timebase-num span:before{content:"";width: 10px;height: 10px;border-radius: 50%;background: #eb4e3d;position: absolute;left: -16px;top: 7px;}
.timebase-cell.current .timebase-num{color: #333333;margin-top: 16px;}
.timebase-cell.current .timebase-txt{padding: 10px 12px;margin-bottom:5px;background: #ffffff;width: 36%;}
.timebase-cell.current .timebase-txt:after{content: none;}
.timebase-cell{margin: 0;position: relative;}
.timebase-cell:before{content: "";border-left: 2px dashed #dddddd;height: 100%;position: absolute;left: 24px;top: 34px;}
.timebase-txt{background: #f8f8f8!important;align-items: center;padding: 13px 12px!important;margin-right: 10px;}
.timebase-num{margin: 0 3px 0 17px;}
.word-txt1{font-size: 14px;margin-bottom: 0;align-items: center;color: #666666;}
.word-txt1 img{width: 9px;height: 9px;margin-right: 5px;}
.timebase-word{font-size: 11px;}
.timebase-num span{width: 20px;height: 20px;line-height: 20px;font-size: 10px;color: #ffffff;background: #4fa2f8;border-radius: 50%;}
.timebase-num span:before,.timebase-cell.current .timebase-num span:before{content: none;}
.rank-div{margin: 0 15px;}
.rank-div .timebase-cell:last-child:before{content: none;}
.circle-div{width: 58px;height: 42px;line-height: 18px;padding-top: 14px;border-radius: 50%;font-size: 13px;color: #ffffff;text-align: center;}
.timebase-txt .icon-arrow-right{position: absolute;right: 10px;top: 32px;}
.rank-div h5{border-bottom: none;font-size: 20px;padding: 0;line-height: 52px;}			
.blue-title{background: #64a5fc;font-size: 16px;color: #ffffff!important;text-align: center;border-radius: 8px 8px 0 0;}
.standard-cell{background: #e4f6f1;height: 54px;line-height: 54px;border-radius: 5px;font-size: 18px;padding: 0 20px;color: #333333;margin: 8px auto;}
.standard-cell img{width: 26px;height: 29px;margin-right: 10px;}
.standard-cell label{font-size: 36px;color: #00cd91;float: right;font-weight: bold;}
.life-cell{display: inline-block;text-align: center;}
.life-con{background: #f8f8f8;font-size: 12px;color: #999999;padding: 10px 12px;display: inline-block;vertical-align: middle;}
.life-cell i{display: inline-block;width: 190px;height: 12px;background: red;}
.life-con .life-cell:nth-child(1) i{background: rgba(230,51,51,0.2);border-radius: 30px 0 0 30px;}
.life-con .life-cell:nth-child(1).current i{background: rgba(230,51,51,1);}
.life-con .life-cell:nth-child(1).current{color: rgba(230,51,51,1);}
.life-con .life-cell:nth-child(2) i{background: rgba(255,156,0,0.2);}
.life-con .life-cell:nth-child(2).current i{background: rgba(255,156,0,1);}
.life-con .life-cell:nth-child(2).current{color: rgba(255,156,0,1);}
.life-con .life-cell:nth-child(3) i{background: rgba(36,215,125,0.2);border-radius: 0 30px 30px 0;}
.life-con .life-cell:nth-child(3).current i{background: rgba(36,215,125,1);}
.life-con .life-cell:nth-child(3).current{color: rgba(36,215,125,1);}
.waterfountain-form{display: inline-block;vertical-align: top;margin-top: 20px;}
.waterfountain-form>div{margin: 15px 0;}
.waterfountain-form h6{font-size: 18px;color: #333333;margin-top: 20px;}
.waterfountain-form h6 span{font-size: 14px;color: #666666;margin-left: 20px;}
.equip-sel{display: inline-block;width: 230px;height: 230px;line-height: 230px;text-align: center;background: #f4f6f9;margin: 23px 18px;position: relative;}
.equip-sel .contact-checkbox{position: absolute;top: 10px;left: 7px;line-height: 24px;}
/*未引水学生列表*/
.drink-name{font-size: 0;padding: 12px 0;line-height: 12px;height:100%;}
.drink-name li{height: 25px;line-height: 25px;position: relative;width:25%;display: inline-block;font-size: 14px;}
.drink-name li a{display: inline-block;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;margin-left: 18px;cursor: pointer;}
/*校历设置*/
.term-list{height: 52px;line-height: 52px;background: #e6e6e6;font-size: 16px;margin: 5px 12px;padding: 0 10px;text-align: left; position: relative;}
.term-list.redbg{color: #ffffff;}
.term-list.graybg1{background: #e6e6e6;color: #999999;}
.term-list.graybg2{background: #dddddd;color: #999999;}
.term-tab{display: inline-block;width: 35%;height: 72px;background: #dddddd;color: #999999;border-radius: 8px 8px 0 0;text-align: center;}
.term-tab h5{font-size: 18px;font-weight: bold;margin: 7px 0;}
.calendar-div{display: inline-block;background: #ffffff;border: 1px solid #bcbcbc;border-radius: 2px;position: relative;}
.calendar-div img{position: absolute;top: 5px;right: 8px;}
.calendar-div input{width: 94px;border: none;padding: 0 13px;}
.calen-top{height: 170px;text-align: center;}
.calen-top h5{height: 36px;line-height: 36px;border-radius: 10px 10px 0 0;font-size: 16px;font-weight: bold;color: #ffffff;}
.calen-txt{background: #f7f7f7;height: 134px;position: relative;}
.calen-txt h6{font-size: 48px;color: #594f42;font-weight: bold;}
.calen-txt p{font-size: 12px;}
.calen-txt button{background: #00c56f;height: 30px;line-height: 30px;margin-top: 5px;padding: 0 50px;}
.exp-con{margin: 10px 0;background: #f8f4e3;padding: 0 18px 0 18px;}
.exp-con .exp-list{border-bottom: 1px dashed #e2d9b3;padding: 15px 0 20px 0;position: relative;}
.exp-con .exp-list h5{font-size: 16px;color: #333333;margin-bottom: 7px;}
.exp-con .exp-list:last-child{border-bottom: none;}
.exp-con .exp-text{background: #ffffff;padding: 5px;}
.mark-state{position: absolute;top: 0;right: 0;}
/*选择图片*/
.group-ul li{height: 40px;line-height: 40px;border-bottom: 1px solid #eeeeee;padding: 0 18px;position: relative;}
.group-ul li.current{background: #f3f3f3;}
.seltxt-ul{display: inline-block;font-size: 14px;width: 180px;vertical-align: top;}
.selpic-list{display: inline-block;font-size: 12px;width: 620px;border-left: 1px solid #eeeeee;box-sizing: border-box;}
.selpic-top{height: 40px;line-height: 40px;background: #f8f8f8;border-bottom: 1px solid #eeeeee;position: relative;}						
.picdl-list{font-size: 0;padding: 0 9px;border-bottom: 1px solid #eeeeee;}
.picdl-list dl{width: 110px;margin: 13px 5px 3px 5px;display: inline-block;}
.picdl-list dl dt{width: 110px;height: 110px;position: relative;}
.picdl-list dl dt img{width: 110px;height: 110px;}
.picdl-list dl dt .com-checkbox{position: absolute;top: 4px;left: 4px;}
.picdl-list dl dd{font-size: 12px;color: #999999;text-align: center;margin: 5px 0;}		
.selpic-list .picdl-list:last-child{border-bottom: none;}							
.picdl-shadow{width: 110px;height: 110px;background: rgba(0,0,0,0.2);position: absolute;}
.picdl-shadow .icon_more{position: absolute;top: 4px;right: 4px;height: 14px;display: inline-block;cursor: pointer;}
.more-ul{color: #666666;font-size: 12px;width: 60px;background: #ffffff;padding: 5px 8px;position: absolute;top: 26px;right: 2px;line-height: 20px;border-radius: 3px;}
.more-ul .triangle-mark{top: -15px;right: -2px;}
.more-ul li{cursor: pointer;}
.group-list{z-index: 9;width: 126px;padding: 0;box-shadow: 0 0 10px 0 #dddddd;right: 15px;top: 38px;}
.group-list li{border-bottom: 1px solid #eeeeee;padding: 5px 15px;}
.group-list li.current{color: #37b6ff;}
.group-list li:last-child{border-bottom: none;}
/*访问管理*/
.visitor_regist{background:#ededed; height:40px; line-height:40px; font-size: 16px; color: #333333;padding-left: 20px; margin-bottom: 15px;}
.visiting-statis{-webkit-box-flex: 1;   /* OLD - iOS 6-, Safari 3.1-6 */  -moz-box-flex: 1;    /* OLD - Firefox 19- */  -webkit-flex: 1;   /* Chrome */ -ms-flex: 1;/* IE 10 */ }
.visitor_list{background:#f7f7f7; margin: 0px 20px 10px 20px; padding: 10px 0 5px 0; overflow: hidden;}
/*安全监督*/
.safe-title {
    height: 48px;
    line-height: 48px;
    border-bottom: 1px solid #eeeeee;
    padding: 0 10px;
    font-size: 16px;
    color: #333333;
}
/*受伤详情*/
.approval-label{top:10px; position: absolute; right:10px; }
.injury-details {margin: 0px auto;width: 1000px;padding: 0 0px 0 20px;}
.injuryinjury{padding:0px;margin: 5px 0px 0 0px; }
.injury-list{overflow: hidden; padding-bottom:15px;}
.injury .injury-list ul li .injury-left{color: #323232; width: 110px; text-align: right; display: inline-block;}
.injury-div{ margin: 0px 20px 0 20px; padding: 20px 30px; position: relative; overflow: hidden;}
.injury-img{line-height: 25px; display: block; width: 27px;height: 25px; position: absolute; top:0px; left: 0; background: url(../image/kubg_left.png) no-repeat; text-align: center; color:#666666; font-size: 14px; }
.injury-div .injury-list{margin: 0;}
.injury-div .injury-list ul{width:50%;float: left; }
.injury-div .injury-list ul li{line-height:28px;height: auto; color: #666666; font-size: 14px;}
.injury-div .injury-list ul li .injury-left{vertical-align: top; margin-right: 5px; color:#323232; font-weight: bold;}
.injury-cor{ color: #24a5ff; text-decoration: underline}
.injury-div .injury-list p{margin: 0;}
.feedback-cen{background:#f8f8f8; padding:10px 15px 5px 15px; overflow: hidden; width:823px;  float: left; margin-top: 8px;}
.feedback-list{border-bottom: 1px dashed #e4e4e4; color: #333333; padding: 5px 0;line-height: 20px; position: relative;}
.feedback-list:last-child{border-bottom: none;}
.feedback-list .feedback-txt{ font-size:16px; color: #333333;}
.feedback-list label img{width: 27px; height: 27px; border-radius: 50%;vertical-align: top;}
.feedback-right{display: inline-block;}
.feedback-time{position: absolute;right: 0px; top:0px; color: #999999; font-size: 12px;}
.handle-txt{font-size: 12px; margin-left: 3px; color:#00c762;}
.injury-div .injury-list .feedback-right ul{width: 100%; margin-top: 10px;}
.injury-div .injury-list .feedback-right ul li{float: left; margin-right: 10px;width: 60px;height: 60px;margin-bottom: 10px;}
.injury-div .injury-list .feedback-right  ul li img{width: 60px; height: 60px; }
.pic-rightimg{ width: 823px;float: left;}
.pic-rightimg img{margin-bottom: 10px;}
/*统计分析*/
.safestat-cen{height: 400px;text-align: center;position: relative;background: #fff;border: 1px solid #eeeeee;margin:10px;  }
.safestat-tit{ text-align: left; font-size:14px; color: #333333; overflow: hidden;  border-bottom: 1px solid #eeeeee; height: 40px; padding-right:15px;}
.safestat-tit span{ display: inline-block; line-height:28px; padding-left: 10px; }
.safenum-cell{display: inline-block;height: 130px;width:32%; margin-right: 0.5%; color: #ffffff; text-align: left;}
.safenum-cell:last-child{margin-right: 0;}
.safenum-cell p{font-size: 48px; padding-left: 20px;}
.safenum-celltxt{ padding:15px 0px 5px  20px; font-size: 18px;}
/*培训公用样式*/
.formList.train-con div label{width: 176px; display: inline-block;text-align: right; margin-right: 4px;}
.formList.train-con select{margin-left: 0;width: 168px}
.formList.train-con textarea{border: 1px solid #ccc;width: 160px; }
.formList.train-con input{margin-right: 5px;}
.formList.train-con input[type="number"] {border: 1px solid #ccc;height: 28px;line-height: 29px;padding: 0 0px 0 8px; }
.formList.train-con input[type="text"]{border:1px solid #ccc;height: 28px;line-height: 29px;padding: 0 8px;}
.formList.train-con select{border:1px solid #ccc;height: 30px;line-height: 30px;padding: 0 4px;border-radius: 0;color: #6C6C6C;margin-left: -4px;}
/*会议室管理*/
.equipment-txt{background:#f5f5f5; text-align: center;font-size: 12px; color: #666666; border-radius: 3px; padding: 1px 10px; margin-right: 5px;}
.meeting-time {background: #f8f8f8; margin: 10px 20px; overflow: hidden;}
.meeting-time li{float: left; width:40px;  margin-left: 10px; padding:5px 0; }
.meeting-time  p{font-size: 14px; color: #6e7f96; width:40px; text-align: center;}
.meeting-time  span{display: block; background:#e0e5eb; width:40px; height: 40px;  margin-bottom: 2px;  }
.meeting-time  span.topdiu{border-top-left-radius: 5px;border-top-right-radius: 5px;}
.bottomdiu{border-bottom-left-radius: 5px;border-bottom-right-radius: 5px;}
.meeting-time  span.picbg{background:url(../images/meeting/metting_time.png) no-repeat; width: 40px; height: 40px;}
.meeting-time  span.greenbg{background: #66e1bd;}
.meeting-time  span.bluebg{background: #accffe;}
.sign-in{width: 500px; margin: 0px auto; }
.sign-in ul{width: 100%; overflow: hidden;}
.sign-in  li{border-bottom: 1px solid #EEEEEE;   height: 30px;padding: 0 20px 0 25px; color: #323232; font-size: 14px; padding-top: 8px;}
.sign-name{width: 26%;display: inline-block;line-height: 20px;vertical-align: top;}
.sign-tel{width: 26%;display: inline-block;color: #666666; font-size: 14px;line-height: 20px;vertical-align: top;}
.sign-in-txt {width: 48%; text-align: center;display: inline-block;}
.sign-in-txt label{ border: 1px solid #cccccc; display: block;line-height: 20px; width: 58px;float: left;color: #666666; font-size: 12px;border-radius: 2px; margin-left: 10px;}
.sign-in-txt label.greenbor{border: 1px solid #01cd92; color: #01cd92;}
.sign-in-txt label.yellowbor{border: 1px solid #fe9f1f; color: #fe9f1f;}
.sign-in-txt label.redbor{border: 1px solid #ec2b2b; color: #ec2b2b;}
/*新版会议详情*/
.meeting-details{ background: #ffffff; padding: 10px;overflow: hidden;}
.meeting-div{border: 1px solid  #eeeeee;}
.meeting-div h4,.meeting-sel h4{background:#eaf8ff; height: 37px; line-height: 37px; position: relative;border-bottom: 1px solid #dcf3ff;padding: 0 10px 0 40px;font-size: 12px; color: #666666;}
.meeting-div h4 label,.meeting-sel h4 label{color: #333333;}
.meeting-div .icon_type:after,.meeting-sel .icon_type:after{content: "\e6b6";font-size: 14px;color: #333333;margin-right: 2px;}
.meeting-time{color:#666666;font-size: 12px;}
.meeting-details h3{font-size: 14px; color: #333333;line-height: 30px;margin-bottom: 5px;}
.meeting-details .icon_time:after{color: #333333;font-size: 16px;}
.meeting-list-dt{overflow: hidden; padding-bottom:15px;}
.meeting-list-dt ul li .meeting-left{color: #323232; width: 110px; text-align: right; display: inline-block;}
.meeting-img{line-height: 25px; display: block; width: 27px;height: 25px; position: absolute; top:0px; left: 0; background: url(../image/kubg_left.png) no-repeat; text-align: center; color:#666666; font-size: 14px; }
.meeting-list-dt{margin: 0;}
.meeting-list-dt ul{width:50%;float: left; }
.meeting-list-dt ul li{line-height:28px;height: auto; color: #666666; font-size: 14px;}
.meeting-list-dt ul li .meeting-left{vertical-align: top; margin-right: 5px; color:#333333;}
.meeting-cell-dt{background: #ffffff;border: 1px solid #eef2f8; height: 121px;}
.meeting-cell-dt h5{height: 35px;line-height: 35px;padding: 0 10px;background:#ddebff; font-size: 14px;color: #333333;}
.meeting-cell-dt .greenbgtxt{background: -webkit-linear-gradient(left, rgb(81, 233, 152), rgb(57, 219, 133));
background: -o-linear-gradient(left, rgb(81, 233, 152), rgb(57, 219, 133));
background: -moz-linear-gradient(left, rgb(81, 233, 152), rgb(57, 219, 133));
background: linear-gradient(left, rgb(81, 233, 152), rgb(57, 219, 133));height: 63px;}
.meeting-cell-dt .redbgtxt{background: -webkit-linear-gradient(left, rgb(250, 125, 113), rgb(246, 92, 78));
background: -o-linear-gradient(left, rgb(250, 125, 113), rgb(246, 92, 78));
background: -moz-linear-gradient(left, rgb(250, 125, 113), rgb(246, 92, 78));
background: linear-gradient(left, rgb(250, 125, 113), rgb(246, 92, 78));height: 63px;}
.meeting-cell-dt .bluebgtxt{background: -webkit-linear-gradient(left, rgb(131, 184, 252), rgb(131, 184, 252));
background: -o-linear-gradient(left, rgb(131, 184, 252), rgb(131, 184, 252));
background: -moz-linear-gradient(left, rgb(131, 184, 252), rgb(131, 184, 252));
background: linear-gradient(left, rgb(131, 184, 252), rgb(131, 184, 252));height: 63px;}
.meeting-cell-dt .yellowtxt{background: -webkit-linear-gradient(left, rgb(254, 158, 57), rgb(254, 139, 16));
background: -o-linear-gradient(left, rgb(254, 158, 57), rgb(254, 139, 16));
background: -moz-linear-gradient(left, rgb(254, 158, 57), rgb(254, 139, 16));
background: linear-gradient(left, rgb(254, 158, 57), rgb(254, 139, 16));height: 63px;}
.meeting-cell-dt .num-celltxt{text-align: left;margin: 10px 0 0px 15px; font-size: 12px;}
.meeting-cell-dt .num-cell{display: inline-block;margin: 10px 5px;color: #ffffff;width: 30%;float: left;}
.minutes-cen{ background: #ffffff; padding:0 10px 10px 10px;overflow: hidden; margin-top: 10px;}
.minutes-cen h3{ border-bottom: 1px solid #dddddd; color: #333333; font-size: 14px;line-height: 30px; height: 30px;font-weight: bold;margin-bottom: 10px;}
.layui-btn.audiofre{background: #ffffff; border: 1px solid #32a8ff;font-size: 12px; color:#32a8ff;height: 25px;line-height: 25px; border-radius: 0;}
/*会议统计*/
.conference-sta .safenum-cell{display: inline-block;height: 97px;width:15.9%; margin-right: 0.45%;text-align: left; background: #ffffff;}
.conference-sta .safenum-cell:last-child{margin-right: 0;}
.conference-sta .safenum-cell p{font-size: 12px; padding-left: 20px;  font-size: 32px; margin-top: 10px;}
.conference-sta .safenum-celltxt{ padding:15px 0px 5px 15px;color:#9aa2ac;font-size: 12px; }
.conference-sta .bluebgtxt{background: -webkit-linear-gradient(left, rgb(148, 190, 232), rgb(188, 217, 248));
background: -o-linear-gradient(left, rgb(148, 190, 232), rgb(188, 217, 248));
background: -moz-linear-gradient(left, rgb(148, 190, 232), rgb(188, 217, 248));
background: linear-gradient(left, rgb(148, 190, 232), rgb(188, 217, 248));height:97px; color: #ffffff;}
.conference-sta .yellowbgtxt{background: -webkit-linear-gradient(left, rgb(245, 164, 114), rgb(255, 198, 139));
background: -o-linear-gradient(left, rgb(245, 164, 114), rgb(255, 198, 139));
background: -moz-linear-gradient(left, rgb(245, 164, 114), rgb(255, 198, 139));
background: linear-gradient(left, rgb(245, 164, 114), rgb(255, 198, 139));height:97px; color: #ffffff;}
.conference-sta .bluebgtxt .safenum-celltxt,.conference-sta .yellowbgtxt .safenum-celltxt{color: #ffffff;}
.conference-sta .icon_class:after{font-size: 20px;color: #d5d8df;}
.conference-sta .icon_time6::after{color: #d5d8df;font-size: 20px;}
/*图书管管理*/
.book-div{background: #ffffff;border: 1px solid #eeeeee;margin: 10px;}  	
.book-div h5{border-bottom: 1px solid #eeeeee;height: 48px;line-height: 48px;padding: 0 15px;font-size: 16px;color: #333333;}
.bookleft-column{width: 194px;float: left;height: 100%;margin: 0 0px;background: #f0f0f0;box-sizing: border-box;}
.bookleft-column h3{height: 35px;line-height: 35px;text-align: center; color: #ffffff; cursor: pointer;font-size: 16px; background:#00ab9f; }
.bookstype-tit{padding-left: 10px;color: #333333;padding: 10px 17px;font-size: 14px;}
.book-cell{background: #ffffff;padding: 15px 25px; height: 176px;}
.book-cell dt{display: inline-block;vertical-align: top;position: relative;}
.book-cell .book-des{color: #666666;font-size: 12px; line-height: 20px;}
.book-cell .book-see{color: #999999;font-size: 12px; line-height: 20px; margin-top: 5px;}
.book-cell dt img{width: 125px;height: 155px;}
.book-cell dd{display: inline-block;font-size: 14px;margin-left: 20px;line-height: 28px;}
.book-cell dd label{font-weight: bold;}
.book-cell dd .btmopr-btn{margin-top: 10px;}
.book-cell dd .btmopr-btn button{vertical-align: top;width: 100%;height: 30px;line-height: 30px;}	 
.book-cell dd .btmopr-btn button.layui-btn-red{background:#e95d5d }
.book-borrow{ text-align: center;font-size: 12px;border-radius: 2px;background: #e0f6f5; color: #01a39a; padding: 0 10px;display:inline-block; line-height: 20px;}
.book-txt  span{display: inline-block; text-align: center; font-size: 12px; color: #666666; line-height: 20px; padding: 2px 5px 0 5px; background: #e7e7e7; border-radius: 3px;}
@media screen and (min-width:1000px) and (max-width:1460px){
	.num-circle{width: 58px; height: 58px; line-height: 58px;}
	}
/*打印*/
.print-leftcell{height: 50px;line-height: 50px;cursor: pointer;position: relative;padding: 0 15px;color: #ffffff;}
.print-leftcell img{margin: -2px 7px 0 0;}
/*.print-leftcell:after{content: "";width: 30px;height: 1px;background: rgba(255,255,255,0.5);position: absolute;bottom: 0;left: 10px;}
.print-leftcell:last-child:after{content: none;}*/
.print-leftcell.current{background: #008A80;}
.print-cell{display: inline-block;position: relative;vertical-align: top;padding: 0 15px;line-height: 38px;position: relative;color: #333333;font-size: 12px;font-weight: bold;}
.print-cell:after{content: "";height: 20px;width: 1px;background: #dddddd;position: absolute;top: 10px;right: 0;}
.print-cell:last-child:after{content: none;}
.print-cell img{cursor: pointer;width: 20px;}	
.print-opr{background: #ffffff;width: 170px;padding: 0 15px;}
.print-opr ul li{margin: 3px 0;cursor: pointer;}
.print-opr ul li img{margin-right: 15px;}
.opr-explain{position: relative;height: 30px;}
.opr-explain:before{content: "";border-top: 2px solid #f6f6f6;text-align: center;position: absolute;top: 50%;left: 0;width: 100%;}
.opr-explain span{background: #ffffff;padding: 0 10px;z-index: 999;position: absolute;left: 50%;margin-left: -34px;}
/*物品管理-物品预算设置*/
.buy-title .common-tab li u.goodsone_ico{background:url(../images/tonggoods/setup_ico.png) no-repeat; width: 16px; height: 16px; display: inline-block; margin-right:5px;}
.buy-title .common-tab li.current u.goodsone_ico{background:url(../images/tonggoods/setup_icoHL.png) no-repeat;}
.buy-title .common-tab li u.goodstwo_ico{background:url(../images/tonggoods/itembudget_ico.png) no-repeat; width: 16px; height: 16px; display: inline-block; margin-right:5px;}
.buy-title .common-tab li.current u.goodstwo_ico{background:url(../images/tonggoods/itembudget_icoHL.png) no-repeat;}
.buy-title .common-tab li u.goodsthree_ico{background:url(../images/tonggoods/classification_ico.png) no-repeat; width: 16px; height: 16px; display: inline-block; margin-right:5px;}
.buy-title .common-tab li.current u.goodsthree_ico{background:url(../images/tonggoods/classification_icoHL.png) no-repeat;}
.newly-addbtn{font-size: 12px;color: #333333;cursor: pointer; background:#e6e7e9; padding: 5px 10px;margin-top: 5px;}
/*物品管理-仓库物品*/
.sort-div{background: #fafafa;width: 22px;display: inline-block;position: absolute;right: 0;top: 90px;bottom: 0;}
.sort-div span{display: block;text-align: center;font-size: 12px;color: #666666;padding: 6px 0;cursor: pointer;} 
.sort-div span.current{color: #00ab9e;}	        
.manage-opr{background: #f6f8fa;height: 38px;border-top: 1px solid #e6ecf0;border-bottom: 1px solid #e6ecf0;padding: 0 10px;}	        
.local-txt{display: inline-block;margin-top: 10px;color: #333333;}
.local-txt span.current{color: #00ac9f;}
.goods-pic{position: relative;display: inline-block;}
.goods-pic i{background: #ff6600;font-size: 12px;color: #ffffff;padding: 0 3px;position: absolute;top: 0;left: 0;line-height: initial;}
.goods-info{display: inline-block;vertical-align: middle;margin-left: 5px;}
.goods-info h5{color: #49a1ff;}
.goods-info p{font-size: 12px;color: #666666;}
.spec-div{position: absolute;margin-top: -6px;right: 34px;z-index: 9;top: 50%;}
.fold-arrow{cursor: pointer;background: #ffffff;min-width: 24px;height: 12px;display: inline-block;text-align: center;line-height: 14px;border: 1px solid #00ab9e;z-index: 999;position: absolute;top: 0;color: #00ab9e;font-size: 12px;}
.fold-arrow .iconfont{vertical-align: middle;display: inline-block;margin-top: -1px;}
.fold-arrow .iconfont:after{color: #00ab9e;}
.spec-con{border: 1px solid #00ab9e;background: #ffffff;color: #333333;padding: 10px 13px;line-height: 24px;position: absolute;min-width: 180px;margin-top: 13px;}
.spec-sel{display: inline-block;vertical-align: top;width: 138px;}
.spec-sel span{cursor: pointer;margin-right: 10px;position: relative;background: #f8f8f8;display: inline-block;padding: 0 20px 0 5px;height: 18px;line-height: 18px;min-width: 48px;text-align: center;border: 1px solid #00ab9e;box-sizing: border-box;margin: 2px 10px 3px 0;}
.spec-sel span img{position: absolute;bottom: 0;right: 0;}
/*物品管理-使用中物品*/
.table-title{background: #eaf8ff;border: 1px solid #dcf3ff;padding: 7px 15px;}
.table-title span{margin-right: 10px;position: relative;}
.table-title span .iconfont{margin-right: 3px;}
/*物品管理-待归还*/
.label-sign{background: #ff6600;color: #ffffff;font-size: 12px;height: 16px;line-height: 16px;padding: 0 5px;position: absolute;top: 2px;left: 2px;}
.label-sign.sign-num{background: #ffede1;color: #ff6600;position: static;display: inline-block;}
.label-sign.sign-num .triangle-mark{left: 50%;margin-left: -11px;top: -16px;bottom: auto;border-right: 6px solid #ffede1;}
.label-sign.sign-time{color: #e24242;background: #f5dddd;vertical-align: top;margin-left: 10px;}
.label-sign.sign-time .triangle-mark{border-right: 6px solid #f5dddd;left: -21px;top: 2px;
	transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
}
.disable-sel{opacity: 0.5;}
/*申领单*/
.apply_top{margin: 5px 20px 5px 20px;}
.apply_top h3{border-bottom: 1px solid #eeeeee; font-size: 14px; color:#333333; line-height: 30px; font-weight: bold; margin-bottom: 10px;}
.apply_top h4{color:#333333;}
.apply_green,.goods-info p,.layui-record-list h5 label.apply_green{color: #00ac51;}
.layui-record-list h5 label.apply_yellow{color: #ff9000;}
/*新增物品*/
.title-info{margin: 0 0 10px 0;border-bottom: 1px solid #eeeeee;height: 40px;line-height: 40px;color: #333333;} 	       		
.type-mark{font-size: 12px;color: #ffffff;padding: 0 7px;border-radius: 10px 10px 10px 0;position: absolute;left: 0;top: -13px;line-height: 20px;
	background: -webkit-linear-gradient(left, #ff986a , #ff6564); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(right, #ff986a, #ff6564); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(right, #ff986a, #ff6564); /* Firefox 3.6 - 15 */
    background: linear-gradient(to right, #ff986a , #ff6564); /* 标准的语法 */	
}
.attribute-sel{display: inline-block;vertical-align: top;}
.attribute-sel span{height: 28px;line-height: 28px;position: relative;margin: 5px;border: 1px solid #eeeeee;background: #ffffff;font-size: 12px;color: #333333;display: inline-block;padding: 0 10px;min-width: 35px;text-align: center;}
.attribute-sel span .icon_close4{position: absolute;top: -11px;right: -3px;cursor: pointer;}
.attribute-sel span input[type="text"]{width: 40px;border: none;vertical-align: top;margin-top: 2px;text-align: center;}
.attribute-sel .attr-name{font-size: 12px;color: #333333;margin: 12px 0 8px 0;display: inline-block;}
.attribute-sel .attr-cell label{margin-right: 10px;font-size: 12px;}
.attribute-sel .attr-cell label i{width: 10px;height: 10px;border: 1px solid #dddddd;display: inline-block;vertical-align: top;margin: 3px 3px 0 0;}        
.attribute-sel .attr-cell.attr-edition label{background: #e9e9e9;padding: 2px 18px;border-radius: 20px;}
/*添加URL*/
.default-form.gain-form .layui-form-label{width: 102px;line-height: initial;}
.default-form.gain-form .layui-form-label input[type="checkbox"]{float: left;margin-top: 3px;}
.default-form.gain-form .layui-input-block{margin-left: 118px;}
/*查看物品详情*/
.type-span{background: #f9f9f9;display: inline-block;padding: 5px 20px;margin-left: 20px;}
.type-spancell{display: inline-block;margin: 0 5px;}
.type-spancell span{border: 1px solid #dddddd;width: 10px;height: 10px;display: inline-block;margin: 5px 3px 0 0;vertical-align: top;}
/*添加物品弹框*/
.content-title{background: #efefef;padding: 7px 15px;border: 1px solid #e2e2e2;}
.info-sel{border: 1px solid #e2e2e2;margin: 5px;padding: 5px;position: relative;}
.info-sel .icon_close4{position: absolute;top: -5px;right: -4px;cursor: pointer;}
.info-sel .icon_close4:after{color: #e24242;}
.info-sel .info-txt{font-size: 12px;padding: 5px 0;line-height: 20px;}
.info-sel .info-txt span{color: #666666;}
.info-sel .down_bg .opr-inp input[type="button"].current{border:1px solid #00ab9e;color: #00ab9e;}
.spec-sub{position: absolute;right: 5px;bottom: 8px;}
.spec-sub .default-line{float: right;padding: 0 4px;}
.spec-sub .spec-con{width: 230px;right: 0;margin-top: 18px;}
.spec-sub.current .default-line{border: 1px solid #00ab9e;border-bottom: none;}
/*物品入库*/
.purchase-right{background: #f8f8f8;margin: 10px;padding: 20px 30px 20px 30px;text-align: right;font-size: 14px;color: #666666;}
.purchase-right label{min-width: 100px;text-align: right;display: inline-block;}
.purchase-right span{min-width: 80px;text-align: right;display: inline-block;}
/*物品系统设置*/
.item-manager{background:#ffffff; padding: 10px; }
.item-manager  h3{color: #333333; font-size: 16px; line-height:35px; height: 35px; }
.item-manager-list{background:#f9f9f9; padding-bottom: 0px;}
.item-manager-list .good-sel{background: #ffffff;}
.item-manager-list .good-sel h4{background:#eaf8ff; height: 37px; line-height: 37px; position: relative;border-bottom: 1px solid #dcf3ff;padding: 0 10px 0 40px;font-size: 12px; color: #666666;}
.item-manager-list .good-sel h4 label{color: #333333;}
.item-manager-list .info-cell h5{color:#333333; font-size: 14px; font-weight:normal;}
.item-manager-list .info-cell{vertical-align: middle;line-height: 20px;padding: 0px 0 0 10px;}
.item-house-list{background:#f9f9f9; padding: 5px 0;}
.item-house-cell{background: #ffffff;padding: 10px 10px;display: -webkit-box;display: -webkit-flex;display: flex;height: 110px;}
.item-house-cell dt{display: inline-block;vertical-align: top;position: relative;}
.item-house-cell .shadow-mark{width: 50px;height: 24px;line-height: 24px;background: #797979;position: absolute;top: 50%;margin-top: -12px;left: 50%;margin-left: -25px;z-index: 999;font-size: 14px;color: #ffffff;text-align: center;}
.item-house-cell dt img{width: 110px;height: 110px;}
.item-house-cell dd{display: inline-block;font-size: 14px;margin-left:15px;line-height: 20px; color: #666666;}
.item-house-cell dd label{font-weight: bold;}
.item-house-cell h3{line-height: 22px; height: 22px;}
.no_manager{ text-align: center; padding: 30px 0;}
.no_manager p{color:#999999; font-size: 14px;line-height: 30px;}
/*申购申请*/
.approve-cell{font-size: 12px;margin-top: 10px;}
.approve-cell li{text-align: center;display: inline-block;vertical-align: top;}
.approve-cell .approve-pho{width: 50px;height: 50px;cursor: pointer;position: relative;margin: 0 auto;}
.approve-cell .approve-pho img{width: 100%;height: 100%;border-radius: 50%;}
.approve-cell .icon_close_grey{position: absolute;top: -7px;right: -8px;}
.approve-cell .icon_arrowright{margin: 11px;display: inline-block;}
.approve-cell .icon_arrowright:before{color: #B8BABB;position: absolute;top: 15px;right: -20px;}
.approve-cell li:last-of-type .icon_arrowright:before{content: none;}
.approve-cell .approve-pho.addapprove{background: #F4F4F4;border-radius: 50%;line-height: 50px;}
.approve-cell .approve-pho.addapprove .icon_add:before{font-size: 20px;}
/*物品管理*/
.tablist-brief.layui-tab-brief{margin: 0;height: 50px;color: #00ab9e;}
.tablist-brief.layui-tab-brief .layui-tab-title .layui-this{background: #00ab9e;color: #ffffff!important;border: 1px solid #007169;border-bottom: none;}
.tablist-brief.layui-tab-brief .layui-tab-title img{margin: -3px 5px 0 5px;}
.tablist-brief.layui-tab-brief .layui-tab-title .layui-this:after{content: none;}
.tablist-brief.layui-tab-brief .layui-tab-title{height: 50px;}
.tablist-brief.layui-tab-brief li{line-height: 50px;background: #eeeeee;min-width: 110px;border: 1px solid #c4c4c4;border-bottom: none;}                	           
/*我的物品*/
.budget-div{width: 236px;padding: 10px 12px;height: 80px;display: inline-block;margin-top: 3px;position: relative;color: #ffffff;
	background: -webkit-linear-gradient(left, #ffa11e , #ffcf83); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(right, #ffa11e, #ffcf83); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(right, #ffa11e, #ffcf83); /* Firefox 3.6 - 15 */
    background: linear-gradient(to right, #ffa11e , #ffcf83); /* 标准的语法 */	
}
.budget-div h6{font-size: 22px;font-weight: bold;margin-top: 3px;}
.budget-div .used-label{background: #ec8d1a;display: inline-block;padding: 2px 10px;min-width: 130px;border-radius: 20px;margin-top: 4px;}
.goods-cell{display: inline-block;width: 42%;vertical-align: top;margin-left: 5px;}
.goods-cell li{padding: 0 10px;color: #666666;background: #f3f3f4;display: inline-block;width: 20%;margin: 4px 2px;height: 46px;line-height: 46px;}
.goods-cell li img{vertical-align: top;margin: 9px 9px 0 0;}
.goods-cell li label{font-weight: bold;float: right;font-size: 16px;}
.goods-btn{background: #f3f3f4;border: 1px dashed #dddddd;padding: 7px;margin-top: 12px;}
.goods-btn .layui-btn+.layui-btn{margin-left: 5px;}
.goods-list{background: #ffffff;padding: 10px;position: relative;}
.goods-defimg{vertical-align: top;width: 120px;height: 120px;margin-right: 10px;}
.goods-list h6{font-size: 16px;font-weight: bold;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 3;-webkit-box-orient: vertical;}
.goods-list p{font-size: 13px;color: #666666;margin-top: 3px;}
.goods-list i{background: #ff6600;color: #ffffff;font-size: 12px;padding: 1px 3px;display: inline-block;margin: 5px 0 7px 0;}
.goods-oprbtn{font-size: 12px;margin-top: 8px;}
.goods-oprbtn a{color: #ffffff;padding: 1px 3px;margin-right: 3px;}
.goods-oprbtn a img{vertical-align: top;margin: 4px 2px 0 0;}
/*物品管理-搜索*/
.selgoods-ul li{padding: 10px 15px;color: #333333;position: relative;}
.selgoods-ul li.current{border: 1px solid #53c2bc;box-sizing: border-box;background: #ffffff;}
.selgoods-img{text-align: center;display: inline-block;width: 18px;}
.screen-label{border: 1px solid #cccccc;padding: 2px 5px;margin: 0 3px;}
.screen-label .icon_closed{vertical-align: top;margin: 2px 0 2px 3px;display: inline-block;}
.screen-label .icon_closed:after{font-size: 12px;}
.goods-list.borderlist{border: 1px solid #dddddd;box-sizing: border-box;}
.goods-list.borderlist .goods-oprbtn{position: absolute;bottom: 3px;width: 55%;}
.goods-list.borderlist .goods-oprbtn .icon_add_bigico{background: none;}
.goods-list.borderlist .goods-oprbtn .icon_add_bigico:before{color: #02ac9f;font-size: 25px;}
.goods-list.borderlist .goods-oprbtn .layui-btn.disabled{background: #b6d9ff;border-bottom: 3px solid #a8cbf1;}
.type-goods{position: absolute;width: 700px;background: #ffffff;z-index: 9;left: 248px;top: -1px;border: 1px solid #53c2bc;}
.type-goodscell:first-child h5{background: #ffffff;margin-left: -1px;}
.type-goodslist{padding: 7px 0;margin: 0 15px;}
.type-goodslist li{display: inline-block;padding: 3px 20px 3px 0;}
.type-goodslist li.current{color: #53c2bc;border: none;}
/*物品管理-消息中心*/
.message-top{background: #fffcf2;height: 42px;line-height: 42px;padding: 0 15px;border-bottom: 1px solid #dddddd;}
.message-ul li{padding: 14px 10px;height: 48px;position: relative;}   	
.message-ul li .message-img{float: left;}
.message-ul li .message-img img{width: 50px; height: 50px; border-radius: 50%;}
.message-ul li .message-txt{color: #333333;vertical-align: top;margin-left: 58px;margin-top: 3px;}
.message-ul li .message-txt i{color: #999999;font-size: 12px;float: right;margin-top: 4px;}
.message-ul li .message-txt label{font-size: 12px;margin-left: 7px;}
.message-ul li.current{background: #e0f1fe;}
.message-ul li.current:before{content: "";border-left: 4px solid #00ab9e;position: absolute;left: 0;top: 0;height: 100%;}
.message-mark{width: 8px;height: 8px;border-radius: 50%;display: inline-block;background: #e24242;position: absolute;left: 11px;top: 14px;}       
/*仓库调拨*/
.store-list{border: 1px solid #dcf3ff;margin-top: 10px;position: relative;}
.store-list h5{padding: 8px 15px;background: #eaf8ff;border-bottom: 1px solid #dcf3ff;}
.store-list h5 .icon_close_zred{float: right;cursor: pointer;}
.store-list h5 .icon_close_zred:before{font-size: 20px;}
.store-con{padding: 5px 0;margin: 0 10px;border-bottom: 1px dashed #eeeeee;position: relative;}
.store-con:last-of-type{border-bottom: none;}
.store-numcell{line-height: 24px;margin: 8px 0;}
.store-name{width: 130px;display: inline-block;text-align: right;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;vertical-align: top;} 
.store-num{width: 40px;display: inline-block;}	
.store-slide{display: inline-block;width: 300px;background: seashell;margin: 0 5px;}   
.store-info{display: inline-block;font-size: 12px;color: #666666;vertical-align: top;margin-top: 10px;border-right: 1px solid #e3e1df;padding-right: 20px;margin-right: 20px;}   		        
/*会议管理*/
.schedule-cell{background: #ffffff;padding: 9px 15px;width: 160px;}
.schedule-cell li{background: #eeeeee;height: 24px;line-height: 24px;padding: 0 6px;font-size: 12px;color: #666666;margin: 6px 0;display: -webkit-box;display: -webkit-flex;display: flex;}
.schedule-cell li p{display: inline-block;margin-left: 7px;-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}
.markcolor{width: 8px;height: 8px;display: inline-block;border-radius: 50%;background: #c3c3c3;margin: 8px 5px 0 0;vertical-align: top;}
/*新增会议*/
.selperson{position: absolute;top: 0;overflow-y: auto;border: 1px solid #e6e6e6;box-sizing: border-box;line-height: 28px;background: #ffffff;margin: 1px;width: 100%;display: inline-block;min-height: 30px;}
.selperson span{position: relative;padding: 0 10px;display: inline-block;}
.selperson .icon_close_grey{position: absolute;right: -7px;top: -4px;cursor: pointer;}
.selperson .icon_close_grey:before{font-size: 16px;}
.sign-label{font-size: 0;margin-left: -2px;}
.sign-label li{display: inline-block;background: #f5f5f5;font-size: 12px;color: #666666;margin: 0 2px;padding: 0 5px;height: 16px;line-height: 16px;text-align: center;min-width: 40px;}
.info-cell{display: inline-block;vertical-align: top;line-height: 22px;padding: 5px 0 0 10px;}
.info-cell h5{cursor: pointer;font-size: 16px;font-weight: bold;margin-right: 5px;}
.info-cell h5 .icon_tria-down,.info-cell h5 .icon_tria-top{display: inline-block;vertical-align: top;}
.info-cell h5 .icon_tria-down:after,.info-cell h5 .icon_tria-top:after{font-size: 10px;vertical-align: top;}
.time-quantum{font-size: 0;color: #333333;margin: 0 8px;width: 44px;display: inline-block;}
.time-quantum label{margin-bottom: 5px;font-size: 14px;color: #6e7f96;text-align: center;display: inline-block;margin-bottom: 5px;width: 100%;}
.time-quantum span{font-size: 13px;width: 44px;height: 43px;display: inline-block;margin-bottom: 2px;}
.time-quantum span.linediv{background: url(../images/meeting/icon_meetingbg.png);}
.time-quantum span.graydiv{background: #dae4ef;}
.time-quantum span.reddiv{background: url(../images/meeting/icon_meetingbg2.png);}
.time-quantum span.greendiv{background: #00d268;}					
.meeting-cell{text-align: center;padding: 5px 0 0 0;}	
.meeting-cell .icon_arrow_left,.meeting-cell .icon_arrow_right{display: inline-block;vertical-align: top;margin-top: 92px;}
.meeting-cell .icon_arrow_left:after,.meeting-cell .icon_arrow_right:after{font-size: 34px;color: #676767;}
/*外部参会人员*/
.select-ul{padding: 0 15px;}
.select-ul li{clear: both;border-bottom: 1px solid #e9ecee;height: 32px;line-height: 32px;text-align: center;}	
.select-ul li label{float: left;}
.select-ul li .icon_close_zred{float: right;margin-top: 2px;}
/*选择会议室弹框*/
.meeting-list{border: 1px solid #e2e2e2;margin: 10px 15px;position: relative;}
.calendar-top{background: #ffffff;height: 82px;background: #fafafa;border-bottom: 1px solid #e9ecee;text-align: center;}	
.month-mark{display: inline-block;position: relative;width: 150px;text-align: center;height: 82px;line-height: 82px;vertical-align: top;}
.month-mark:after{content: "";border-right: 1px solid #eeeeee;height: 50%;position: absolute;right: 0;top: 25%;}
.sel-circle{width: 40px;height: 40px;line-height: 40px;text-align: center;display: inline-block;margin: 0 auto;border-radius: 50%;}
.sel-circle.current{background: #00cd91;color: #ffffff;}
.calen-label{width: 60px;text-align: center;display: inline-block;position: relative;color: #333333;}
.calen-label span{font-size: 18px;}
.calen-label p{font-size: 14px;line-height: 26px;}
/*会议管理*/
.meeting-sel{border: 1px solid #e5e5e5;height: 168px;box-sizing: border-box;background: #ffffff;}
.meeting-opr{background: #ffffff;border-top: 1px solid #e5e5e5;padding: 7px 10px;text-align: right;}
.meeting-opr .layui-btn+.layui-btn{margin: 0;}
.type-select{font-size: 0;padding: 5px 11px;}
.type-select li{cursor: pointer;position: relative;background: #ffffff;margin: 5px 4px;font-size: 12px;color: #666666;border: 1px solid #eeeeee;text-align: center;display: inline-block;padding: 4px 8px;height: 18px;min-width: 50px;}
.type-select li .icon_close_grey{float: right;}
.type-select li .icon_close_grey:before{font-size: 16px;}
.remind-div{border: 1px solid #53c2bc;width: 253px;position: absolute;left: 50%;margin-left: -145px;top: -49px;z-index: 9;background: #ffffff;border-radius: 3px;padding: 7px 12px;line-height: initial;text-align: center;}
.remind-inp{display: inline-block;border: 1px solid #e8e8e8;height: 28px;line-height: 28px;vertical-align: top;padding: 0 5px;margin-right: 3px;}
.remind-inp input{border: none;vertical-align: top;height: 100%;}
.remind-inp button{vertical-align: top;margin-top: 5px;margin-left: 3px;position: relative;}
.remind-div .layui-btn+.layui-btn{margin-left: 3px!important;}
.remind-div .triangle-mark{
	transform: rotate(-90deg);
    -ms-transform: rotate(-90deg);
    -moz-transform: rotate(-90deg);
    -webkit-transform: rotate(-90deg);
    -o-transform: rotate(-90deg);
    bottom: -17px;
    left: 50%;
    margin-left: -6px;
    }
.type-select.form-select{padding: 0;}
.type-select.form-select li{padding: 1px 8px;}    
.type-select.form-select li.addsel{border: 1px solid #32a8ff;color: #32a8ff;}
/*采购统计*/
.count-list{height: 68px;padding: 15px;color: #ffffff;}
.count-list h5{font-size: 12px;color: #ffffff;margin-bottom: 10px;}
.count-numcell{display: inline-block;vertical-align: middle;}
.count-numcell i{font-size: 36px;}
.model-cell{padding: 6px;cursor: pointer;position: relative;}
.model-cell.current{background: #f4f6f8;}
.model-cell img{vertical-align: top;display: inline-block;margin: 1px 3px;}
.model-cell:nth-last-child(1){margin-left: 6px;}
.model-cell:nth-last-child(1):before{content: "";border-left: 1px dashed #dee1e5;width: 1px;height: 16px;position: absolute;left: -5px;top: 7px;}
.type-cell{background: #eaedf1;color: #7c8196;font-size: 12px;padding: 2px 8px;cursor: pointer;}
.type-cell.current{background: #279bff;color: #ffffff;}
.count-list .icon_arrow_goup:after,.count-list .icon_arrow_decline:after{color: #ffffff;}
.park-cell{background: #ffffff;width: 256px;position: absolute;top: 0;bottom: 0;right: 0;color: #7c8196;box-shadow: 10px 17px 12px 10px #dddddd;}
.park-cell h5{background: #edf7ff;border: 1px solid #d9ebfa;padding: 14px 10px;margin: 8px 6px;border-radius: 5px;font-size: 16px;}
.park-cell h5 img{margin: 2px 2px 0 0;display: inline-block;vertical-align: top;}
.park-cell label{color: #279bff;margin-right: 8px;}
.park-cell p{display: inline-block;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;width: 140px;vertical-align: middle;}
.park-cell ul{margin: 0 10px 0 25px;position: absolute;top: 60px;bottom: 0;left: 0;right: 0;overflow-y: auto;}
.park-cell ul li{margin: 7px 0;}
.point-cell{position: absolute;width: 550px;background: #ffffff;padding: 11px 15px;border: 1px solid #bbbbbb;border-radius: 5px;}
.point-cell .count-list h5{margin-bottom: 8px;}
.point-cell .count-list h5 img{display: inline-block;vertical-align: top;margin: 1px 5px 0 0;}
.point-cell .count-list{height: 55px;padding: 12px 15px 8px 15px;}
.point-cell .count-numcell i{font-size: 24px;}
.point-cell .triangle-mark{z-index: 9;border-right: 6px solid red;left: 50%;margin-left: -6px;
	transform: rotate(-90deg);
    -ms-transform: rotate(-90deg);
    -moz-transform: rotate(-90deg);
    -webkit-transform: rotate(-90deg);
    -o-transform: rotate(-90deg);
}
.point-text{margin-top: 10px;}
.point-text .iconfont{display: inline-block;vertical-align: middle;margin-right: 5px;}
.point-text .iconfont:after{color: #b1bbc4;}
.point-text p{margin: 3px 0;}
.point-text{color: #7c8196;font-size: 12px;}
/*出勤登记-按天*/
.buy-title .common-tab li u.outduty_ico{background:url(../images/outduty/day_ico.png) no-repeat; width: 16px; height: 16px; display: inline-block; margin-right:5px;}
.buy-title .common-tab li.current u.outduty_ico{background:url(../images/outduty/day_icoHL.png) no-repeat;}
.buy-title .common-tab li u.year_ico{background:url(../images/outduty/year_ico.png) no-repeat; width: 16px; height: 16px; display: inline-block; margin-right:5px;}
.buy-title .common-tab li.current u.year_ico{background:url(../images/outduty/year_ico.png) no-repeat;}
.outduty-manager{margin-top: 10px; background:#ffffff;}
.outduty-opr{background: #eff3f8;border-bottom: 1px solid #c3c6cb;padding: 10px 10px;overflow: hidden;}
.outduty-txt{background: #ffffff;display: block; padding:0px 10px;font-size: 14px; color: #666666;min-width: 95px;overflow: hidden;float: left; margin-right: 10px; }
.outduty-sel{background:#f9f9f9;position: relative;}
.outduty-txt i{font-size:24px;font-weight: bold; }
.outduty-sel .info-cell h5 span{ color: #ffffff;font-size:12px;padding:0 1px;display: inline;margin-left: 5px;}
.outduty-sel .bluebg{background: #6fa5ff;}
.outduty-sel .yellowbg{background: #f69c60;}
.outduty-sel .lightblueebg{background: #5cc8f0;}
.outduty-sel i.blue{color:#448afe;font-weight: bold;}
.outduty-sel .info-cell{padding: 0px 0 0 10px;}
.outduty-card{ position: absolute; background: #5ddbb7; font-size: 12px; color: #ffffff;right: 20px;top: 20px;padding: 5px 0px; text-align: center; width:74px;}
.outduty-card.nocard{background:#f9f9f9;color: #c91d1d; border: 1px solid #c91d1d;}
.outduty-card.twocard{background:#79ce1b;}
.outduty-sel .info-cell h5{font-size: 14px;}
.item-manager-div{background: #ffffff;padding-bottom: 10px;}
.outduty-num{position: absolute; top:0;left: 0; height: 65px; background: #EFEFEF;width: 28px;text-align: center; line-height:65px;font-size: 12px; color: #666666; }
.outduty-tab{ position: absolute;left: 210px;top: 15px; width:73px; }
.layui-table.outduty-tab  td{padding: 0px; line-height: 15px; height: 15px;font-size: 12px;color: #999999;}
.layui-table.outduty-tab  td div{padding: 1px 5px;}
.outduty-tab .greentxt{color: #15ca26;}
.outduty-tab .orangetxt{color: #ff6600;}
/*考勤卡*/
.card-list{background:url(../images/outduty/cardbg.png) no-repeat; width: 335px; height: 136px; background-size: 100% 136px;position: relative;}
.card-list span{background:url(../images/outduty/cardbgleft.png) no-repeat; width:52px; height:24px; position: absolute;display: block; left: -1px; top:20px; color: #ffffff; text-align: center; font-size: 12px;line-height: 24px;}
.card-list p{color: #ffffff; font-size: 16px; padding-left:23px;line-height: 20px; }
.card-num{ position: absolute;  font-size: 12px; color: #666666;right: 20px;top: 25px;}
.codeimg{ position: absolute;right: 15px;bottom: 20px;}

/*体检预约*/
.medical-cen{ background:#fff; }
.medical-cell .amountimg{width:213px; cursor: pointer;}
.medical-list{text-align: center; padding: 10px 0;}
.medical-management{position: absolute;top: 0px;bottom: 0;width: 100%;box-sizing: border-box;}
.medical-cell{display: inline-block;height: 250px;width:48%; margin-right: 0.9%; color: #ffffff; text-align: left;position: relative;}
.medical-cell:last-child{margin-right: 0;}
.medical-cell p{font-size: 16px; margin-left: 40px;background:#FFFFFF; border-radius: 20px;line-height: 30px; display: inline-block; margin-top: 10px; padding: 0 20px  ; cursor: pointer;}
.medical-cell p i{font-size: 16px; margin-right: 5px; color:#98B0FF;font-weight: bold; letter-spacing: 1px; }
.medical-txt{ padding:75px 0px 5px  40px; }
/*考勤日期*/
.att-period h3{font-size: 14px; color: #333333; line-height: 30px;}
.att-period{ background:#f7f7f7; padding:10px 15px; }
.att-add-btn{border: 1px dashed #008a80; text-align: center; font-size: 14px; color:#333333; padding: 5px 0; cursor: pointer; margin: 10px;}
.attcheck-sel{height: 168px;box-sizing: border-box;background: #ffffff; color: #333333;}
.attcheck-sel h5{color:#333333; font-size:16px;}
.attcheck-sel .info-cell{padding: 5px 0 0 35px;}
.attcheck-sel .graymark{width: 46px;height: 45px;background: url(../images/outduty/attblue.png);position: absolute;top: 5px;left: 5px;} 	
.attcheck-sel .graymark.failure{width: 46px;height: 45px;background: url(../images/outduty/attgray.png);}
.attcheck-sel .graymark	i{top: 16px;left: 9px;transform: rotate(-45deg);font-size: 12px;position: absolute;top: 7px;left: -2px;color: #ffffff;}

/*选择某一员工*/
.distance-txt{  background:#fd9a39; text-align: center; font-size: 12px; color: #ffffff; height: 20px;}
/*刷卡记录*/
.record-cell{position: relative;justify-content: space-between;background: #f7f7f7;margin: 7px 5px 7px 28px;padding: 10px 10px 10px 15px;}			
.record-cell h5{font-size: 14px;color: #333333;}
.record-cell p{font-size: 11px;color: #666666;margin-top: 3px;}
.record-cell .triangle-out-bottom{
	    transform: rotate(0deg);
	    -ms-transform: rotate(0deg);
	    -moz-transform: rotate(0deg);
	    -webkit-transform: rotate(0deg);
	    -o-transform: rotate(0deg);
        left: -5px;
	    bottom: 50%;
	    margin-bottom: -6px;
        border-right-color: #f7f7f7;
}
.card-number{
		width: 19px;
	    height: 19px;
	    border-radius: 50%;
	    background: #0ca6ff;
	    position: absolute;
	    top: 22px;
	    left: -26px;
	    font-size: 12px;
	    color: #ffffff;
	    z-index: 9; 
	   text-align: center; 
	   font-weight: bold; 
	   line-height: 19px;
}
.record-cell:after{
	    content: "";
	    border-left: 1px dashed #d0d0d0;
	    position: absolute;
	    left: -18px;
	    top: 37px;
	    bottom: -30px;
}
.record-cell:last-child:after{content: none;}
.card-datetitle{font-size: 14px;color: #333333;line-height: 24px;font-weight: bold;}
.record-cell.history-cell{background: #ffffff;margin: 7px 0 7px 25px;}
.record-cell.history-cell .triangle-out-bottom{border-right-color: #ffffff;}
.record-cell.history-cell .card-number{top: 12px;}
.record-cell.history-cell:after{top: 28px;}
.card-datetitle.title-num{align-items: center;justify-content: space-between;margin: 12px 15px;font-weight: normal;}
.history-list{border-bottom: 1px solid #d5d5d5;padding: 2px 0;}
.history-list:last-child{border-bottom:none;}
/*帮助中心*/
.help-top{height: 256px;background: url(../images/help/help_bg.png)no-repeat center center;background-size: cover;}
.sub-center{width: 1200px;height: 256px;margin: 0 auto;position: relative;}
.sub-center h1{font-size: 23px;color: #ffffff;}
.search-sub{background: #ffffff;height: 50px;border-radius: 50px;margin: 20px 190px 0 190px;box-shadow: 0 3px 8px 1px #3b82e0;text-align: left;}
.search-sub img{width: 14px;height: 14px;margin: 0 10px 0 20px;}
.search-sub input{border: none;height: 100%;width: 90%;}
.level1-txt{border-bottom: 1px solid #e8e8e8;display: block;padding: 10px 0 10px 12px;position: relative;color: #3f5266;cursor: pointer;}
.level1-txt:before{content: "";height: 22px;width: 3px;background: #d8d8d8;position: absolute;left: 0;top: 9px;}
.menu-left li.current .level1-txt{color: #4d9bed;background: #ffffff;}
.menu-left li.current .level1-txt:before{background: #4d9bed;}
.level2-txt{display: block;padding: 6px 0 6px 25px;border-bottom: 1px solid #e8e8e8;font-size: 12px;cursor: pointer;color: #46596d;}
.level3-txt{font-size: 12px;padding: 4px 0 4px 35px;cursor: pointer;color: #46596d;}
.menu-left li .level3-txt.current{background: #4d9bed;color: #ffffff;}
.icon-arrow{width: 11px;height: 11px;float: right;margin: 5px 7px;}
.sub-content{width: 1200px;margin: 0 auto;background: #ffffff;min-height: 800px;}
.page-local{color: #6f6f6f;font-size: 12px;margin: 35px 15px 24px 15px;display: inline-block;}
.leftcontent{float: left;width: 220px;background: #edf0f5;position: absolute;top: 0;bottom: 0;}
.rightcontent{overflow: hidden;margin: 0 20px 0 250px;padding-bottom: 15px;}
.rightcontent h1{font-size: 22px;color: #34495e;font-weight: bold;margin: 12px 0;}
.rightcontent h5{font-size: 16px;color: #34495e;font-weight: bold;margin: 12px 0;}
.eassy-con{border: 1px solid #e1e7f1;}
.eassy-con h6{font-size: 14px;color: #394d62;background: #edf0f5;padding: 5px 10px;}
.shadow-sub{background: rgba(0,0,0,0.5);position: absolute;top: 0;left: 0;width: 100%;height: 100%;}
.play-img{width: 54px;height: 54px;cursor: pointer;position: absolute;top: 50%;left: 50%;margin: -27px;}
.screen-img{width: 14px;height: 14px;position: absolute;right: 13px;bottom: 10px;cursor: pointer;}

/*查看供应商详情*/
.supplier_top{margin: 5px 20px 5px 20px;border-bottom: 1px solid #eeeeee; padding-bottom: 10px;
	display:-webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex; justify-content: space-between;line-height: 22px;color:#9b9b9b; font-size: 12px; }
.supplier_top h3{font-size: 24px; color:#333333; line-height: 30px; }
.supplier_left{flex: 1; }
.supplier_right{width:288px; margin-left: 10px; }
.supplier_right ul{position: relative; margin: 10px 15px 0 10px;}
.supplier_right li{float: left;width: 60px; height: 37px;margin: 0 2px;}
.supplier_right li::last-child{margin-right: 0px;}
.supplier_right li img{width: 60px; height: 37px;}
.supplier_right li.current{border: 2px solid #00ac9f}
.supplier_img img{width: 288px; height: 192px;}
.supplier_bottom{margin: 5px 20px 5px 20px;padding-bottom: 10px;}
.supplier_bottom h3{font-size: 14px; color:#4a4a4a; line-height: 30px;margin: 10px 0;}
.supplier_bottom p{color:#9b9b9b; font-size: 12px;line-height: 20px; }
.supplier_lf{width:774px;float: left;}
.supplier-region{border: 1px solid #DDDDDD; width: 750px;margin: 10px;overflow:hidden;display:-webkit-box; display: -moz-box;display: -ms-flexbox; display: -webkit-flex;display: flex;align-items: center;background:#FBF1EA; }
.supplier-region div{display: block; width:124px; color:#333333; text-align: center;}
.supplier-region ul{border-left: 1px solid #DDDDDD; padding: 10px 0;background:#fff; }
.supplier-region li{margin-left: 10px; min-width:103px; color: #9b9b9b; font-size: 12px; line-height: 30px;float: left;position:relative;}
.supplier-region li.current{ color:#333333;}
.supplier-region .type-mark{line-height: 15px;top: -10px;}
.supplier-license{padding: 10px 10px;background:#fff; margin-top: 10px;overflow: hidden;}
.supplier-license h3{color:#7E7E7E;font-size: 12px; line-height: 25px; }
.supplier-license .rgimg{background:#F8F8F8; border: 1px solid #DDDDDD; width:349px; height: 233px;text-align: center; padding: 10px 0;margin-right:10px;margin-bottom: 5px;}
.supplier-license .layui-tab-title li{float: left; padding-left: 10px; }
.supplier-license .layui-tab-title{border-bottom: 1px solid #DDDDDD;overflow: hidden;padding-bottom: 10px;margin-bottom: 10px;}

.supplier-license .pic-contain li{float: left; padding-left: 10px; }


.supplier-license li P,.supplier_food_list p{color: #9b9b9b; font-size: 12px;line-height: 20px;}
.supplier_rt{float: right; width: 375px; background:#ffffff;position: absolute; top: 0px; bottom: 0;right: 0; padding: 10px;}
.supplier_food{margin: 10px 0 0 0;}
.supplier_food h3{font-size:16px; color:#333333;border-bottom: 1px solid #F0F0F0;line-height: 30px; }
.supplier_food_list{ float: left;}
#foods div{border-bottom: 1px solid #F0F0F0; padding: 10px;width: 188px; height: 66px;}
.supplier_food_list img{border: 1px solid #F0F0F0; width: 65px; height: 65px; float: left;margin-right: 10px;}
.supplier_food_list h3{font-size:14px; color:#333333;margin-bottom: 5px;}
/*添加供应商*/
.region-div{box-shadow: 0 5px 5px -4px inset #F5F5F5; padding:20px 0 50px 20px; overflow: auto;}
.related-btn{background: #1CA89D;width:74px;height: 24px;color:#ffffff;display: block; position: absolute; text-align: center;line-height: 24px;bottom:0px; right: 0;cursor: pointer;}
.region-sel{width: 214px; margin:0 20px 20px 0;float: left; }
.region-sel:last-child{margin-right:0px;}
.region-img {position: relative;}
.region-img img{width:214px; height: 120px; }
.region-cell{position: relative;}
.region-cell h3{font-size: 16px; color:#333333; line-height: 25px;margin-top: 5px;width: 212px;white-space:nowrap; text-overflow:ellipsis; -o-text-overflow:ellipsis; overflow:hidden; }
.region-cell p{color:#9b9b9b; font-size: 12px;line-height: 23px;}

/*按目录展示的页面*/
.allcontentmain{position: absolute;top: 46px;bottom: 0;width: 100%;box-sizing: border-box;}
.allcontent-col4left{font-size: 14px;height: 100%;display: inline-block;background: #FFFFFC;width: 230px;}
.allcontent-col4cen{font-size: 14px;height: 100%;display: inline-block;position: absolute;left: 231px;right: 260px;}
.allcontent-col4right{font-size: 14px;height: 100%;display: inline-block;width: 260px;float: right;background: #F5F9FF;}
.catalog-top{height: 38px; background:#FAFAFA;}
.catalog-top .default-btn {
    height:38px;
    line-height: 38px;
    padding: 0 10px;background: none;color:#34495e;
}
.catalog-top  .default-btn img {
    margin:-3px 4px 0 1px;
    width: 20px;
    height: 20px;
}
.catalog-top .catalog-tit,.catalog-tit{
    background: #F2F8FF;
    border: 1px solid #A1C4EE;
    height: 22px;
    line-height: 22px;
    border-radius: 30px;
	display: inline-block;
    color: #6F7E90;
    margin:4px 0px 0 10px;
	font-size: 12px;
	cursor: pointer;
	float: left;
}
.catalog-tit .catalog-num{background:#4A90E2; color: #fff; font-size: 12px; border-radius: 20px; line-height: 20px; width: 20px;display: inline-block; text-align: center; margin: 0 5px 0 0;float: left;}
.catalog-edit{white-space:nowrap; text-overflow:ellipsis; -o-text-overflow:ellipsis; overflow:hidden;min-width: 80px;width:84px;color: #A2A3A4; margin: 0 10px;display: inline-block;float: left;}
.cycle-bg{height: 38px; background:#FAFAFA;line-height: 38px; padding-left: 10px;color: #34495e;border-bottom:1px solid #E9E9E9;font-size: 14px;}
.cycle-list li:hover{ background: #E7EFFA;}
.cycle-list li{line-height:30px; height: 30px; padding:0 15px; color: #34495e; position: relative;}
.cycle-list li  img{vertical-align: middle;  cursor: pointer;}
.cycle-list li span{font-weight: bold;}
.catalog-txt{background:#F5F1CE; padding: 0 10px;color: #34495e;font-size: 14px; border-bottom: 1px solid #ECE7BB;height: 38px;line-height: 38px;}
.tabel-txt{background:#D1E3FD; padding: 0 10px;color: #34495e;font-size: 14px; border-bottom: 1px solid #DBDBDB;height: 38px;line-height: 38px;}
.tabel-tit{ padding: 0 10px;color: #34495e;font-size: 14px; border-bottom: 1px solid #DBDBDB;height: 38px;line-height: 38px;font-size: 18px; text-align: center;}
.col-txt{background: #ffffff;margin: 20px;width: 200px;}
.col-txt th div,.col-txt td p{width:10px; height: 10px; }
.col-txt td{text-align: center;}
.col-txt th{margin-bottom: 5px;height: 30px;}
.col-txt th div{border: 2px solid #C5C5C5;}
.col-txt td div{width: 14px;border-top: 2px solid #C5C5C5;border-bottom: 2px solid #C5C5C5;}
.col-txt td p{border-left: 2px solid #C5C5C5; border-right: 2px solid #C5C5C5; }
.col-txt th.curent div,.col-txt p.curent{border: 2px solid #EF4810;}
/*新建文件*/ 
.operation-label{border: 1px solid #E8E8E8;padding:0 2px;width: 132px;border-radius: 3px;color: #666666; background: #F4F5F5; }	
.operation-label .triangle-left{width: 0;height: 0;border-top: 5px solid transparent;border-right: 7px solid #eeeeee;border-bottom: 5px solid transparent;position: absolute;left: -10px;top:8px;}
.operation-label .triangle-left2{width: 0;height: 0;border-top: 4px solid transparent;border-right: 6px solid #F4F5F5;border-bottom: 4px solid transparent;position: absolute;left: -9px;top: 9px;}
.operation-label .layui-form-radio{margin-top: 0;padding-right: 0px; }
.insert-tit{background:#ffffff; border: 1px solid #E1E1E1; padding: 3px 0px; display: inline-block;  border-radius: 3px;color:#7B7B7B;font-size: 12px;height:17px;vertical-align: top;}
.insert-tit img{width: 16px; margin-right: 5px; margin-left: 10px; }
.forms-top{background: #F5F5F5;overflow: hidden; padding: 10px 10px 5px 10px;}
.insert-tit label{border-right: 1px solid #E3E3E4;display: inline-block; padding-right: 10px;}
.insert-tit label:last-child{border-right:none;}
.import_txt{color:#34495E;font-size: 12px; display: inline-block; line-height: 25px; }
.import_txt img{width: 16px; margin-right: 5px; margin-left: 10px; }
.d-time{background:#F8F8F8; font-size: 12px; margin-left: 2px; color:#9C9C9C;padding:3px 10px;}
.d-time.redbg{background:#FFF2F4;  color:#E1596A;}
.layui-tab-title1.swiper-wrapper{height: 96%;}
.layui-tab-title1 {border-bottom: 1px solid #DDDDDD; font-size:0;position: relative; height: 35px;}
.layui-tab-title1  li{height:35px;display:inline-block;vertical-align:middle;
 font-size:14px;
 line-height:35px;
 min-width:65px;
 padding:0 15px;
 text-align:center; cursor:pointer; color: #A1A1A1;
}
.layui-tab-title1 li:first-child {
    /*border-right: none;*/
}
.layui-tab-title1  li.layui-this{background: #FAFAFA;color: #617182; border-bottom: none; line-height: 36px; height: 36px;}
.layui-tab-title1 .layui-this::after {
    position: absolute;
    left: 0;
    top: 0;
    content: "";
    width: 100%;
    height: 35px;
    border:1px solid #DDDDDD;
    border-bottom-color: #FAFAFA;
    box-sizing: border-box;
    pointer-events: none;
}
/*申请修改存档表格*/
.file-cen{margin: 20px; border-bottom: 1px solid #EEEEEE;height: 112px;}
.file-cen .upload-con{font-size: 0;}
.file-cen .upload-cell{position: relative;display: inline-block;width:52px;height: 52px;font-size: 14px;vertical-align: top;margin-right: 20px;} 
.file-cen .upload-cell .upload-img{width: 100%;height: 100%;border-radius: 50%;}     
.file-cen .upload-add{display: inline-block;width: 52px;height: 52px;line-height: 52px;text-align: center;margin-right: 10px;font-size: 14px;border: 2px dashed #e8e8e8;box-sizing: border-box;cursor: pointer;border-radius: 50%; background: #F8F8F8} 
.file-cen .upload-add .icon_add:before{color: #dbdbdb;font-size: 25px;}
.file-cen .upload-cell .shadow-con{width: 100%;height: 100%;position: absolute;top: 0;}
.examine-txt{margin-bottom: 10px;}
.file-list{margin: 20px;overflow: hidden;}
.file-sel{width: 100%; clear: both; }

/*办公自动化查看详情*/
.table-details{border: 1px solid  #EEEEEE;margin-bottom: 10px;}
.table-details h5{background:#F8F8F8; line-height:40px; height: 40px;  color: #333333; font-size: 14px; padding-left: 10px;}
.seer-line{ height: 4px; border-top: 1px dashed #e4e2e2; width: 80%;}
/*档案库图文*/
.layui-col-xs5{width: 19.9999992%; }
.report-sub .mark-div{position: absolute;top: 0px;right:0;}
.report-sub{height: auto;margin: 0px 14px 0px 14px;background: url(../images/formimg/booklet_bg.png) repeat-y;background-size: 100% 318px;padding: 0 20px;overflow: hidden;}
.report-cell{width: 184px;height: 236px;margin: 25px auto;position: relative;}
.report-cell .report-img{width: 184px;height: 236px;}
.report-cell .add-div{text-align: center;position: absolute;width: 100%;margin-top: 75px;cursor: pointer;}
.report-cell .add-div p{margin-top: 15px;font-size: 17px;color: #4a90e2;}
/*供应商关联*/ 
.class-ification{margin: 15px 15px 10px 15px;}
.class-ification label{color: #4C5E71;}
.class-ification span,.class-tit span{cursor: pointer;display: inline-block; padding: 5px 10px; color:#666666; border: 1px solid #EAEAEA;text-align: center; font-size: 12px; margin-right: 5px; border-radius: 20px; margin-bottom: 5px;}
.class-ification span.curent{background:#ECF5FF; border: 1px solid #a3d0fd; color:#409eff;}
.associated-ul{font-size: 0;margin: 5px 10px;}
.associated-ul li{display:inline-block;width: 14%;font-size: 14px;vertical-align: top;position: relative;  }
.associated-cell{margin: 5px;min-height: 84px;padding: 10px 10px 5px 10px;position: relative;background:#f6f6f6;border: 1px solid #F2F2F2;height: 240px;}
.associated-cell:hover{background:#ECF4FF;border: 1px solid #B3CBE3;margin: 5px;min-height: 84px;display:block;}
.associated-cell h6{text-align: center; height: 126px;}
.associated-cell p.associated-txt{text-align: center;font-size: 14px;color:#4C5E71; margin-bottom: 5px; }
.associated-cell p{font-size: 12px;}
.associated-cell h6 img{width: 103px;}
.class-tit{position: relative; padding-left: 20px;min-height: 30px;}
.class-tit span{background:#ffffff;margin-bottom: 5px;white-space:nowrap; text-overflow:ellipsis; -o-text-overflow:ellipsis; overflow:hidden;min-width: 50px;width:54px;}
.more-cen{background:#ffffff;padding: 0 5px 10px 5px;position: absolute;box-shadow:0 0 10px 3px #E0E0E0; overflow: hidden;border-radius: 3px;z-index: 1050;}
.more-cen:before{content:"";display:block;border-width:10px;position:absolute; top:-20px;left:40px; border-color: transparent transparent red transparent;
        border-style: dashed dashed solid dashed;font-size:0; line-height:0;}
.more-cen:after{top:-18px; border-color: transparent transparent red transparent;} 
.more-cen-sel{position: absolute;right:10px;top:10px; z-index: 999;cursor: pointer; color:#999999;text-decoration: underline;}
.associated-ul li:hover .more-cen-sel{color:#409eff; }
/*报修详情*/
.form-sub{border: 1px solid #eeeeee;margin-bottom: 10px;line-height: 20px;padding-bottom: 10px;}
.form-sub h6{background: #f8f8f8;border-bottom: 1px solid #eeeeee;padding: 9px 13px;}
.form-sub .layui-form-item{display: inline-block;width: 49%;vertical-align: top;}
.form-sub .layui-form-item .layui-form-label{color: #34495e;font-size: 12px;padding: 2px 0px 2px 15px;width: 110px;}
.form-sub .layui-form-item .layui-input-block{color: #9b9b9b;font-size: 12px;min-height: 24px;line-height: 24px;margin-left: 125px;}
.form-sub .layui-form-item .layui-inline{margin-bottom: 0;}
.form-sub .upload-cell{width: 70px;height: 70px;}
.form-sub .layui-record-list h5{font-size: 14px;color: #354a5f;}
.repair-state{margin-left: 15px;}
.form-sub .layui-record-icon{top: 0;}
.note-tishi{background: #fafcff;border: 1px solid #edf5fe;display: inline-block;padding: 2px 7px 7px 7px;border-radius: 2px;position: relative;min-width: 200px;}
/*食谱采购介绍*/
.purchase-frame{position: fixed;top: 0;bottom: 0;left: 0;right: 0;background: rgba(102,102,102,0.9);z-index: 998;}
.purchase-frame-box{position: relative;margin: 0px;border-radius: 6px;font-size: 13px;color: #333333;width: 100%; height: 100%;}
.purchase-frame-box h3{background: #313131;height: 43px;line-height: 43px;}
.purchase-frame-box h3 img{margin-left: 10px; cursor: pointer;}
.purchase-frame-tit{padding: 15px 20px; color: #ffffff; font-size: 14px; text-align: center;}
.details-txtcen{left:46%; width:120px;transform:translate(0,-50%); text-align: center;position: absolute;bottom: 60px;z-index: 999;}
.details-txt{width: 100%; }
.details-txtleft{left:20px; width:120px;transform:translate(0,-50%); text-align: center;position: absolute;bottom: 60px;z-index: 999;}
.details-txtright{right:20px; width:120px;transform:translate(0,-50%); text-align: center;position: absolute;bottom: 60px;z-index: 999;}
.details-txt p{color: #ffffff; font-size: 14px; width: 120px; height: 36px; line-height: 36px; background: -webkit-linear-gradient(left, #3D78FF , #1FA2FF);
background: -o-linear-gradient(right,#3D78FF ,#1FA2FF);
background: -moz-linear-gradient(right,#3D78FF ,#1FA2FF); /* Firefox 3.6 - 15 */
background: linear-gradient(to right,#3D78FF ,#1FA2FF); 
FILTER:progid:DXImageTransform.Microsoft.Gradient(gradientType=0,startColorStr=#1FA2FF,endColorStr=3D78FF);border: 2px solid #ffffff; text-align: center; border-radius: 4px;}
.purchase-frame-bot{position: absolute;bottom: 0px;width:100%; background: #FFFFFF; height: 60px;}
.purchase-frame-bot .leftdata{color: #666666;font-size: 13px;float: left;padding-left: 10px;padding-top: 20px;}
.purchase-frame-bot .rightdata{float: right;padding-right: 20px;}
.purchase-frame-bot .rightdata .tel-txt{color: #98a8ba;font-size: 12px;width: 35px;display: inline-block;border-right: 1px solid #98a8ba;line-height: 18px;margin-top: 12px;}
.purchase-frame-bot .rightdata .tel-tit{color: #ff8400;font-size: 28px;display: inline-block;margin-left: 15px;vertical-align: top;padding-top: 10px;}
.purchase-frame-box h5{width: 100%; text-align: center;}
.purchase-frame-box h5 p{border: 1px solid #909090; text-align: center; border-radius:20px; display:inline-block; line-height: 36px; margin: 20px auto; background: #727272;font-size: 16px; color: #ffffff;padding: 0 20px;}
.purchase-frame.width800{ /*width: 800px;*/ height:520px; margin: 0 auto;border-radius: 8px;transform:translate(0,-50%);position: absolute; top:50%;}
.purchase-frame-two{color: #ffffff; font-size: 18px; font-weight: bold; }
.purchase-frame-three{line-height: 40px; text-align: center;color: #ffffff; font-size: 16px;}
.purchase-frame-three label{margin: 0 10px; color:#ED5454;font-weight: bold; font-size: 20px;}
.purchase-frame-box h4{text-align: center; margin: 20px 0;}
.step-diagram{margin:10px 10px; text-align: center;}
.step-diagram img{width: 100%;max-height: 780px}
.purchase-frame.width800 .step-diagram{margin:0px 150px; text-align: center;}
.purchase-frame.width800 .purchase-frame-tit{padding: 10px 20px;}
.purchase-frame.width800 .purchase-frame-tit{padding: 0px 20px 10px 20px; color: #ffffff; font-size: 14px; text-align: center;}
.purchase-frame.width800 .purchase-frame-box h5 p{margin: 20px auto 15px auto;}
/*膳食空白表*/
.dietary-blank{overflow: hidden;}
.dietary-blank .good-sel{background: #ffffff;border: 1px solid #EAEAEA; padding: 10px 30px; text-align: center;height: 135px;}
.dietary-blank .good-sel h3 {color:#333333; font-size: 16px; }
.dietary-blank p {line-height: 30px; color: #617182; font-size: 14px; font-weight: bold;}
.dietary-blank .good-sel img{width: 143px;}
/*相册*/
.album-list{margin: 0px auto; overflow: auto;width:100%;	
	display:-webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;}
.album-left{ float: left; padding: 20px 30px;flex: 1;}
.album-pho{width: 90%; position: relative;margin:0 auto;}
.album-pho img{width: 100%;}
.album-right{float:right; margin-left:20px;width:310px; padding: 20px 10px; background: #f5f5f5;box-shadow: 5px 0px 5px -4px inset #edf5f5; }
.album-right-dt{margin: 10px;}
.album-right-two{margin-top: 15px;}
.album-right-two h3{font-size: 16px;vertical-align: middle;padding-bottom:5px;}
.album-right-two h3 .icon_trian-down::after{  color: #333333;vertical-align: middle;margin-left: 5px;}
.album-right-dt p,.album-right-two p{
	line-height: 22px;
    color: #333333;
    font-size: 14px;
	word-break: break-all;
}
.album-details-lt span{float: left;}
.album-details-lt label{display: block;}
.loading-div{text-align: center; margin: 15px 0 0 0;}
.loading-div p{font-size: 12px; color:#aaaaaa;}
.loading-div p i{color: #00ac9f; font-size: 16px; line-height: 30px;}

.peoplelist{ background:#F8F8F8;box-shadow: 0 0 10px 3px #E0E0E0; position: absolute; left:10px; z-index: 999; width: 180px; }
.peoplelist:before,.peoplelist:after{content:"";display:block;position:absolute; bottom:-14px;left:40px;border-right:15px solid transparent;
    	border-left:15px solid transparent;
    	border-top:15px solid #F1F1F3;font-size:0; line-height:0;}
.peoplelist:after{bottom:-14px; border-right:15px solid transparent;
    	border-left:15px solid transparent;
    	border-top:15px solid #F8F8F8; } 
.peoplelist p {
    border-bottom: 1px solid #eceff4;
    display: block;
    height:32px;
    line-height: 32px;
    margin: 0 6px 0 4px;
    font-size: 14px;
    text-align: center;
}



/*留样管理*/
.sample-setting-top{width:100%;float:left;margin-top: 10px; color:#34495E;}
.sample-setting-top img{width:20px; margin-right: 3px;}
.sample-setting-top a{color:#34495E;font-size: 12px;vertical-align: middle;line-height: 23px;}

/*留样管理视频页*/
.retention-sample{position: absolute;top: 46px;bottom: 0;width: 100%;box-sizing: border-box;}
.retention-sampleleft{font-size: 14px;height: 100%;display: inline-block;background: #FFFFFC;width: 180px;position: absolute;}
.retention-samplecen{font-size: 14px;height: 100%;display: inline-block;position: absolute;left: 190px}
.retention-sample-ul{font-size: 0;margin: 5px 10px;}
.retention-sample-ul li{display:inline-block;width: 16.66%;font-size: 14px;vertical-align: top;position: relative;  }
.retention-sample-cell{margin: 5px;min-height: 205px;padding: 10px 10px 5px 10px;position: relative;background:#fff;border: 1px solid #F4F4F4;height: 260px;}
.retention-sample-cell:hover{background:#fff;border: 1px solid #F4F4F4;margin: 5px;min-height: 84px;display:block;box-shadow: 0 0 10px 3px #E6E7E9;}
.retention-sample-cell h6{text-align: center;  margin: 0 auto;}
.retention-sample-cell p{font-size: 12px; text-align: left;color: #34495E;line-height: 30px; width: 85%; margin: 0 auto;}
.retention-sample-cell h6 img{width: 100%;}
.retention-date{position: absolute;bottom: 20px; font-size: 12px; font-weight: bold;color: #fff;width: 100%;text-align: left;z-index: 999;}
.album-pho .retention-date img{width: 18px; height: 20px; margin-right: 15px; cursor: pointer;}
.album-pho .close-txt img{width: 30px; height: 30px; position: absolute; z-index: 999; right: -40px; top:-30px; cursor: pointer;}
@media screen and (min-width:900px) and (max-width:1600px){
	.retention-sample-cell{min-height: 190px;height: 190px;}
}
/*自查*/
.selfexam-left{color: #34495E;font-size: 14px;height: 100%;display: inline-block;border-right:1px solid #DDDDDD;background: #FDFDFD;width:210px;position: absolute;}
.selfexam-right{font-size: 14px;height: 100%;display: inline-block;position: absolute;left: 210px;right: 0px;}
.kfgl-list h5{background: none;color: #0d1725;position: relative;padding-left: 15px;margin: 5px 20px;line-height: 45px;}
.kfgl-list  h5:before{content: "";width: 4px;height: 20px;background: #00ac9f;position: absolute;left: 0;top: 12px;}
.kfgl-list-st{margin: 0 20px 10px 20px; display: block;background: #fff;border: 1px solid #EAEAEA; color: #333333; font-size: 14px; padding: 10px; line-height: 25px; } .edit-btntxt{color:#00ac9f;cursor: pointer; margin-left: 20px;}    
.edit-btntxt img{vertical-align: top;}
/*获爱码管理*/
.code-management{position: absolute;top: 0px;bottom: 0;width: 100%;box-sizing: border-box;}
.allcontent-left{color: #34495E;font-size: 14px;height: 100%;display: inline-block;background: #FFFFFC;width:180px;position: absolute;/*box-shadow:5px 0px 5px -4px inset #edf5f5;*/}
.allcontent-left h3{line-height: 35px; text-align: left;  font-size: 14px; color:#34495E; height: 35px; padding: 0 10px; }
.allcontent-left h3 img{vertical-align: top; margin-top: 7px;width: 20px;margin-right: 4px;}
.allcontentg-txt{background:#F8F6E6; line-height:33px; padding: 0 10px; border-top: 1px solid #E9E9E9; border-bottom: 1px solid #E9E9E9;}
.add-icoimg{ float: right;font-size: 12px;}
.add-icoimg img{margin-top: 8px; vertical-align: top; margin-right: 4px;cursor: pointer}
.allcontent-left li{color: #34495E; font-size: 12px;cursor: pointer;line-height: 25px; padding-left: 20px; height: 25px; position: relative;}
.allcontent-left li .editim-btn,.hamactivity-list li .editim-btn{position: absolute; right: 10px;top:0;}
.allcontent-left li:hover .editim-btn,.hamactivity-list li:hover .editim-btn{display: block !important; cursor: pointer;}
.allcontent-right{font-size: 14px;height: 100%;display: inline-block;position: absolute;left: 180px;right: 0px;box-shadow: 5px 0px 5px -4px inset #EEEEEE;}
.code-managemen-cell{display: inline-block;height: 110px;width:32%; margin-right: 0.9%; color: #ffffff; text-align: left;position: relative;}
.code-managemen-cell:last-child{margin-right: 0;}
.code-managemen-cell p{font-size: 16px; padding-left: 20px;}
.code-managemen-cell p i{font-size: 48px; margin-right: 5px;}
.code-managemen-txt{ padding:15px 0px 5px  20px; font-size: 16px;}
.code-managemen-cen{ background:#F4F5F9; }
.code-managemen-cell .amountimg{width:76px; position: absolute; right: 30px; top: 10px;}
.code-managemen-list{text-align: center; padding: 10px 0;}
/*获爱码管理查看详情*/
.make-card { margin: 15px;}
.make-card .card-cell{line-height: 25px;font-size: 14px;border-bottom: 1px solid #dddddd;color: #999999;padding-bottom: 10px; margin-bottom: 15px;}
.make-card .card-cell:last-child{border-bottom: none;}
.make-card .card-cell span{color: #34495E;font-size: 14px;float: left; display: inline-block;width: 80px;}
.make-card .title-info{color: #34495E;font-weight: bold;}
/*获爱码设置*/
.hamactivity-top{ border-bottom: 1px solid #DDDDDD; margin: 0 10px; padding: 5px 0; overflow: hidden;}
.hamactivity-top label{width: 49%; float: left; border-right:1px solid #DDDDDD; display: block; text-align:left; line-height: 25px;}
.hamactivity-top label:last-child{border-right: none;}
.hamactivity-list{padding: 0 10px;}
.hamactivity-list p{ color:#666; line-height: 20px; clear: both;font-size: 12px;}
.hamactivity-list p span{width: 47%; float: left;display: block; }
.hamactivity-list p label{width: 49%; float: left;display: block;white-space:nowrap; text-overflow:ellipsis; -o-text-overflow:ellipsis; overflow:hidden; }
/*获爱码角色设置*/
.role-ul {
    font-size: 0;
    margin: 5px 5px;
}
.role-ul li{display:inline-block;width: 20%;font-size: 14px;vertical-align: top;position: relative;  }
.role-ul li  h3{ text-align: center; background:#F5F1CE; height: 30px; font-size: 14px; line-height: 30px;}
.role-ul-rit{float: right; margin-right: 3px; }
.role-ul-rit img{margin-right: 5px; cursor: pointer;}
.role-ul-list{height:175px; overflow: auto; }
.role-ul-list li:hover{ background: #F8F6E6;}
.role-ul li .role-ul-list li{line-height:25px; height: 25px;  position: relative;color: #666;width: 100%; font-size: 13px;border:none;}
.role-ul-list li  img{vertical-align: middle;  cursor: pointer;}
.role-ul-list li span{float: left; display:inline; }
.role-title{width: 90px;white-space:nowrap; text-overflow:ellipsis; -o-text-overflow:ellipsis; overflow:hidden;  }
.role-ul-list li label{float: left;  width: 90px;white-space:nowrap; text-overflow:ellipsis; -o-text-overflow:ellipsis; overflow:hidden; padding-left: 10px;}
.role-ul-list a{text-decoration: underline; padding: 0 5px;}
.role-ul .selbg{ display: block; width: 43px; height: 16px; line-height: 16px; background:#E1E2E5; border-bottom-right-radius: 5px; color: #333; font-size: 10px;float: left;}
.role-ul .selbg img{margin-right: 2px;}
.role-ul .selbg.greenbg{background:#4A90E2; color: #fff; }
.role-ul-cell{margin: 5px;position: relative; border: 1px solid #EEEEEE; background:#FFFFFC;}
.examine-tit{position: absolute; right: 8px;}
/*显示内容设置*/
.all-functions h3{background: #D1E3FD;border-bottom: 1px solid #B6D0F6;}
.all-functions p{line-height: 25px; padding-left: 10px;font-size: 14px;}
.all-functions p:hover{background: #E7EFFA; cursor: pointer}
.supplier-cen li{line-height: 30px; padding-left: 10px;font-size: 14px; height: 30px;white-space: nowrap;text-overflow: ellipsis;-o-text-overflow: ellipsis;overflow: hidden;padding-right: 24px;}

.content-setting-left{width:65%; color: #34495E;font-size: 13px;float: left;}
.content-setting-right{width:363px;float: right;}
.content-setting-one label{display: block; margin-top:8px;}
.entry-txt{text-align: right;color: #aaa;white-space: nowrap;
border-top: 1px solid #ccc;
line-height: 20px;
font-size: 12px;
font-family: Arial, Helvetica, Tahoma, Verdana, Sans-Serif; }
.select-local{background:#F9FBFC;cursor: pointer;font-size: 14px; text-align: center; height: 35px; line-height: 35px; border: 1px dashed #CBD6E1; color:#4A90E2; margin: 5px 10px 15px 10px; }
/*日常模板*/
.formwork-tab{border-bottom: 1px solid #F1F1F1;line-height: 45px; text-align: left;margin-right: 10px; height:45px;  }
.formwork-tab li{color: #cfcfcf; font-size: 16px;width:90px; float: left;cursor: pointer;}
.formwork-tab li.current{color: #00ac9f; }
.formwork_type li {
    height: 33px;
    color: #303030;
    line-height: 33px;
    background: #F8F8F8; 
    font-size: 14px;
    cursor: pointer;
	border: 1px solid #EFEFEF;
	font-size: 14px;
	float: left;
	margin: 15px 10px 0 0px;width:150px; text-align: center;
}
.formwork_type li.current{ color: #fff;background: #00ac9f;  }
.formwork_top{margin-bottom:20px; overflow: hidden;}
.formwork-list{ border: 1px solid #eeeeee; background: #f8f8f8; padding: 10px; margin:10px auto; position: relative;clear: both;margin-right: 10px;overflow: auto;}
.formwork-list h5{color: #303030;font-size: 14px; margin-bottom: 10px;}
.formwork-list p{color:#a7a8a9;line-height: 22px;font-size: 12px;}
.formwork-txt{float: right;display: inline-block; text-align: center; font-size: 12px; color: #fff; background:#00ac9f; padding: 0 8px;line-height: 25px; width:60px; cursor: pointer;}
/*配餐餐别设置样式*/
.layui-table.delivery-table td{line-height: 20px;height: 20px;}
.delivery-table  th{ line-height: 20px;height: 20px;}
.delivery-table  tr:nth-child(odd){
        background-color: #F8F6E6;
      }
.delivery-table  tr{
        background-color: #E7EFFA;
      }
.layui-table.delivery-table th, .layui-table.delivery-table td, .layui-table.delivery-table[lay-skin="line"], .layui-table.delivery-table[lay-skin="row"] {
    border-width: 1px;
    border-style: solid;
    border-color: #D8D8D8;
}
.advanced-set-tit{padding:0 10px; background: #F5F9FF; color: #34495E;font-size: 14px;line-height: 35px; height: 35px;}
.supplier-time{width: 85px; display: inline-block; color:#B1B8C2; }
.supplier-cen li:hover,.supplier-cen li.current,.hamactivity-list li.current{background: #E7EFFA; cursor: pointer}
.hamactivity-list .icon_trian-down:after{color: #B1B8C2; }

/*报名分布图*/
.datatop,.topframe.datatop{padding:8px 15px; color: #34495E;font-size: 14px; background: #fff;/*margin:10px;*/}
.topframe.datatop{height: 97px; display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;}
.datat-refres {display: inline-block; padding: 3px 10px 3px 20px; border-left: 1px solid #D8D8D8; margin: 0 10px 0 10px; }
.pattern-txt{background:#E7EBF4; font-size: 12px; display: inline-block; }
.pattern-txt i{padding: 0 5px; text-align: center;cursor: pointer;display: inline-block; }
.pattern-txt i.current{background:#4A90E2;color: #fff; }
.datat-bj{display: inline-block; padding: 0px 10px 0px 20px; border-left: 1px solid #D8D8D8;margin: 0 10px 0 10px;vertical-align: middle;}
.proportion-list {display: inline-block;font-size: 14px;vertical-align: middle;  position: relative; background:#F0F3F9; }
.data-yeyStat{background:#F0F3F9;border: 1px solid #F0F3F9; height: 95px;padding: 0 10px;}
.data-yeyStat div:first-child{height: 54px; line-height:54px; border-bottom:1px solid #E1E7F1;font-size: 16px; font-weight: bold;}
.data-switch.data-yeyStat.vcolor01{background: rgba(255,0,31,0.1); border: 1px solid #FF001F; color: #34495E; }
.data-switch.data-yeyStat.vcolor02{background: rgba(208, 2, 27,0.3); border: 1px solid #D0021B; color: #34495E; }
.data-switch.data-yeyStat.vcolor03{background: #E0EEFF;border: 1px solid #4A90E2; color: #34495E; }
.data-switch.data-yeyStat.vcolor04{background: rgba(255, 144, 0,0.3); border: 1px solid #FF9000; color: #34495E; }
.data-switch.data-yeyStat.vcolor05{background: rgba(255, 193, 0,0.1); border: 1px solid #FFC100; color: #34495E; }
.data-switch.data-yeyStat.vcolor06{background: rgba(188, 255, 51,0.3); border: 1px solid #BCFF33; color: #34495E; }
.data-switch.data-yeyStat.vcolor07{background: rgba(83, 163, 255,0.3); border: 1px solid #53A3FF; color: #34495E; }
.data-switch.data-yeyStat.vcolor08{background: rgba(82, 89, 255,0.3); border: 1px solid #5259FF; color: #34495E; }
.data-switch.data-yeyStat.vcolor01 div:first-child{border-bottom:1px solid rgba(255,0,31,0.1);}
.data-switch.data-yeyStat.vcolor02 div:first-child{border-bottom:1px solid rgba(208, 2, 27,0.1);}
.data-switch.data-yeyStat.vcolor03 div:first-child{border-bottom:1px solid #B8CEE8;}
.data-switch.data-yeyStat.vcolor04 div:first-child{border-bottom:1px solid rgba(255, 144, 0,0.1);}
.data-switch.data-yeyStat.vcolor05 div:first-child{border-bottom:1px solid rgba(255, 193, 0,0.1);}
.data-switch.data-yeyStat.vcolor06 div:first-child{border-bottom:1px solid rgba(188, 255, 51,0.1);}
.data-switch.data-yeyStat.vcolor07 div:first-child{border-bottom:1px solid rgba(83, 163, 255,0.1);}
.data-switch.data-yeyStat.vcolor08 div:first-child{border-bottom:1px solid rgba(82, 89, 255,0.1);}

.data-switch.data-yeyStat label{margin-left: 0;}
/*.data-switch.data-yeyStat.greenbg div:first-child,.data-switch.data-yeyStat.redbg div:first-child,.data-switch.data-yeyStat.bluebg div:first-child,.data-switch.data-yeyStat.purplebg div:first-child{border-bottom:1px solid #B8CEE8;}*/
.data-yeyStat div:last-child {line-height: 40px;}
.data-yeyStat div:last-child span{font-size: 14px;}
.data-yeyStat div:last-child span.data-down{font-size:24px }
.datatop label{margin-left:15px;}

 /*新建供应商*/ 
.video-img {position: relative;width: 120px; height: 69px;float: left; margin-right: 10px;}
.video-img-bt{ position: absolute; top:55%;left:50%;width:100%;transform:translate(-50%,-50%); text-align: center; cursor: pointer;}
.video-img-bt p{ text-align: center;margin: 5px 0px  0 0; color: #4A90E2;  font-size: 12px;line-height: 20px; }
.loader-pt{font-size: 12px; color: #999999;width: 280px;line-height: 20px;}
.will{color:#FF8B3F;}
.overdue{color:#D92F44;}
/*上传图片*/
.supplier-main .upload-con{font-size: 0;}
.supplier-main .upload-cell{position: relative;display: inline-block;width: 60px;height: 60px;margin-right: 20px;font-size: 14px;vertical-align: top;} 
.supplier-main .upload-cell .upload-img{width: 100%;height: 100%;}     
.supplier-main .upload-add{display: inline-block;width: 60px;height: 60px;line-height: 60px;text-align: center;margin-right: 10px;font-size: 14px;border: 2px dashed #e8e8e8;box-sizing: border-box;cursor: pointer;} 
.supplier-main .upload-add .icon_add:before{color: #dbdbdb;font-size: 30px;}
.supplier-main .upload-cell .shadow-con{width: 100%;height: 100%;position: absolute;top: 0;}
/*选择试卷*/
.content-list{background:#F9F9F9; border: 1px solid #EFEFEF;font-size: 14px;}
.content-list li{cursor: pointer;border-bottom: 1px solid #EFEFEF; line-height:55px;height:55px; padding-left: 20px; }
.content-list li input{vertical-align: middle;}
.content-list li.current{color:#1da89e; }
/*自查设置*/
.bifen{display:inline-block;line-height: 30px;margin-left: 5px;}
.percentage-bg{background:#FFC5C5; color:#ff5454;font-size: 12px; display:block; padding:6px 10px; margin:10px 20px 0 20px;width: 200px; text-align: center;}
.checkset td{position: relative;}
.editico_rt{position: absolute; top:5px; right: 10px; }
.editico_rt img{cursor: pointer;}
.checkset table{ color:#111924; height:135px;}
.checkset div{ float:left; width:49%;margin-left:10px; }
.checkboxdiv label{display: block;}


/*优选评价*/
.review-left{ margin: 20px 0 20px 0;border-bottom: 1px solid rgba(238, 238, 238, 1); padding-bottom: 15px; position: relative;}
.review-left span { display: inline-block; vertical-align: top}
.review-left img{ width:115px; height: 98px;  margin: 0 10px 0 0px;vertical-align: top }
.review-left .pximg-img{width:55px; height: 55px; border-radius: 50px;border: 1px solid #EEEEEE;}
.review-right { display: inline-block;  font-size: 20px; vertical-align: top;font-size: 14px;}
.review-right p{ color: rgba(102, 102, 102, 1);text-align: left;white-space: nowrap;line-height: 20px;}
.review-right .text1 {
height: 28px;
overflow-wrap: break-word;
color: #333333;
font-family: PingFangSC-Medium;
font-weight: 500;
white-space: nowrap;
line-height: 28px;
margin-bottom: 5px;
}
.spottxt{
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 160, 73, 1);
  font-size: 16px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  line-height: 22px;
 display: inline-block;
 width: 9px;
vertical-align: middle;
	margin-right: 5px;
}
.image_pingxing,.review-left .image_pingxing{width: 20px; height: 20px;margin: 6px 0 0 20px; }
.scoretxt {
  overflow-wrap: break-word;
  color: rgba(71, 156, 255, 1);
  margin: 2px 0 0 10px;
  display: inline-block;   line-height: 30px;height: 24px;vertical-align: middle;
}
.text-group {
  height: 20px;
  overflow-wrap: break-word;
  font-family: PingFangSC-Regular;
  text-align: center;
  white-space: nowrap;
  line-height: 20px;
  margin-top: 10px;
  color: #999999;
font-size: 12px;
	text-align: left;
}
.image-text_19 {
  position: absolute;
  left: 22px;
  top: 22px;
  width: 56px;
  height: 56px;
}
.text-photograph {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(102, 102, 102, 1);
  font-size: 14px;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 20px;
  margin-top: 7px;
}
.scoretxt.yellow{color:rgba(255, 160, 73, 1) }
.justify-between {
  display: flex;
  justify-content: space-between;align-items: center;
}
.flex-col {
  flex-direction: column;
}
.photograph{display:block; width: 37px;}
.lengthtxt{  font-size: 14px;color: rgba(102, 102, 102, 1); }
.lengthlist-txt{  font-size: 14px;color: rgba(102, 102, 102, 1); margin-top: 10px;}
.lengthlist-lt{font-size: 16px;color: rgba(102, 102, 102, 1); margin-top: 10px;}
.lengthtxt-lt {
  color: rgba(51, 51, 51, 1);
  font-size: 16px;
  font-family: PingFangSC-Regular;
  font-weight: NaN; 
  text-align: right;
  line-height: 22px;
  margin: 15px 0 0 20px;
text-align: left;
		word-wrap:break-word;
word-break:break-all;
}
	.uploadr-list{ margin: 10px 20px 0 20px; border-bottom: 1px solid #EEEEEE; padding-bottom: 10px;}
	.uploadr-list .upload-cell {
	width:88px;
	height: 88px;
	border: 1px solid #EEEEEE;	
}
.uploadr-list .upload-cell{margin:0 0 5px 1px;}
.review-left .image_del{width: 25px; position: absolute; right: 0; top:0px; cursor: pointer; height: 25px;}
.sequencelist {
  background-color: rgba(255, 255, 255, 1);
  width: 146px;
  height: 130px;
  border: 1px solid rgba(238, 238, 238, 1);
  margin-top: 10px;
  position: absolute;top: 22px;
right: 0;
z-index: 999;
}
.sequencelist p{
height: 30px;
width: 106px;
margin: 10px 0 0 10px;line-height: 30px; padding-left: 10px;}
.sequencelist p.curent{color: rgba(0, 171, 158, 1); background-color: rgba(238, 238, 238, 1);}
.detailed-txt{background-color: rgba(255, 255, 255, 1);
border-radius: 2px;
height: 150px;
border: 1px solid rgba(238, 238, 238, 1);
width: 260px;
position: absolute;
left: 6px;
top: 57px; z-index: 999;}
.detailed-txt .layui-form-label{width: 60px;padding-left: 10px;}
.detailed-txt .layui-form-item {
  margin-bottom: 6px;}
.detailed-txt .layui-input-block {
  margin-left: 79px;
}
.review-left .image_del-txt{ position: absolute; right: 0; top:0px; cursor: pointer; height: 25px; color:#666666;font-size: 14px; }
.tjyxlist{
height: 22px;
font-size: 16px;
font-family: PingFangSC-Regular, PingFang SC;
font-weight: 400;
color: #333333;
line-height: 22px; margin: 20px;}	
/*优选样式*/
.youxuan-div {margin-bottom: 50px;overflow: auto}
.youxuan-div .region-sel{width: 275px;height: 315px;background: #FFFFFF;border: 1px solid #EEF1F2;margin: 0 0px 20px 20px;}
.youxuan-div .region-sel:hover{border: 1px solid #00AC9F;}
.youxuan-div .region-img img { width: 275px;height: 145px;}	
.youxuan-div .region-cell{margin: 20px;}
.youxuan-div .related-btn{width: 90px;height: 36px;background: #00AB9E;border-radius: 25px;font-size:14px;line-height: 36px; }
.youxuan-div .region-cell p.association-txt{
font-size: 16px;
font-family: PingFangSC-Regular, PingFang SC;
font-weight: 400;
color: #FFA049;
line-height: 36px; padding: 20px 0 0 0;  height: 36px;
	clear: both;
}
.moretxt{ padding: 0px 0px 80px 0; color: #00AB9E; font-size: 16px; clear: both; text-align: right;width: 100%; cursor: pointer;}
.youxuan-bg{height: 42px;background: #00AB9E; color: #fff;  font-size: 16px; line-height: 42px; position: fixed; top:0px; width: 100%; z-index: 999;}
.youxuan-bg p{padding: 0 20px;}