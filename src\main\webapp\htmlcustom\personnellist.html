<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>体检人员列表</title>
		<meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
        <link rel="stylesheet" href="../css/reset.css"/>
	    <link rel="stylesheet" href="../css/style.css"/>
		<link rel="stylesheet" href="../layui-btkj/css/layui.css">
        <!--[if lt IE 9]>
        <script src="../sys/html5shiv.min.js"></script>
        <![endif]-->
	</head>
	<body>
        <div style="background-color: white; margin: 10px 10px;">
            <div style="width:100%;float:left;margin-top: 10px;">
                <span class="fr">
                     <a class="layui-btn btn-add btn-default" id="btn-delpersonnel"><i class="linyer icon-add"></i>清空体检人员</a>
                </span>
<!--                <span class="fr" style="margin-right:10px;">-->
<!--                     <a class="layui-btn btn-add btn-default" id="btn-addpersonnel"><i class="linyer icon-add"></i>添加人员</a>-->
<!--                </span>-->
<!--                <span class="fr" style="margin-right:10px;">-->
<!--                     <a class="layui-btn btn-add btn-default" id="btn-imppersonnel"><i class="linyer icon-add"></i>导入人员</a>-->
<!--                </span>-->
                <span class="fr" style="margin-right:10px;display: none;">
                     <a class="layui-btn btn-add btn-default" id="btn-chooseemp"><i class="linyer icon-add"></i>选择教职工</a>
                </span>
            </div>
            <div id="content" style="width: 100%;">
                <table id="tbmain" lay-filter="tbmain"></table>
            </div>
        </div>

        <script type="text/html" id="optTpl">
            <a class="layui-btn layui-btn-mini" lay-event="edit"  style="line-height: 24px;height: 25px;">编辑</a>
            {{#  if(d.orderstatus == 0){ }}
            <a class="layui-btn layui-btn-danger layui-btn-mini" lay-event="del" style="line-height: 24px;height: 25px;">删除</a>
            {{#  }}}
        </script>


        <script type="text/javascript">
            var v = top.version;
            document.write("<script type='text/javascript' src='../sys/jquery.js?v=" + v + "'><" + "/script>");
            document.write("<script type='text/javascript' src='../sys/arg.js?v=" + v + "'><" + "/script>");
            document.write("<script type='text/javascript' src='../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
            document.write("<script type='text/javascript' src='../js/custom/personnellist.js?v=" + v + "'><" + "/script>");
        </script>
	</body>
</html>
