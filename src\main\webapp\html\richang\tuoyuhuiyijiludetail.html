﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <link rel="shortcut icon" href="/images/favicon.ico" type="image/x-icon" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="Copyright" content="Copyright 2016 by tongbang" />
    <meta name="Author" content="" />
    <meta name="Robots" content="All" />
    <title>表1-15托育机构膳食委员会会议记录表</title>
    <link href="../../styles/admin.css" rel="stylesheet" type="text/css" />
    <link href="../../styles/tbstyles.css" type="text/css" rel="stylesheet" />
    <link href="../../plugin/ehaiform/css/tbbaseform.css" rel="stylesheet" type="text/css" />
    <link href="../../plugin/editselect/css/editselect.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../plugin/jquery_tabs/css/tbjquery.tabs.css" type="text/css"
          media="print, projection, screen" />
	<style>

		.rytjdjedit_table td{border:#eee solid 1px}
        /*滚动条样式*/
        html{
            color: #000;
            font-size: .7rem;
            font-family: "\5FAE\8F6F\96C5\9ED1",Helvetica,"黑体",Arial,Tahoma;
            height: 100%;
        }
        /*滚动条样式*/
        html::-webkit-scrollbar {
            display:none;
            width: 6px;
            height: 6px;
        }
        html:hover::-webkit-scrollbar{
            display:block;
        }
        html::-webkit-scrollbar-thumb {
            border-radius: 3px;
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
            border: 1px solid rgb(255, 255, 255);
            background: rgb(66, 66, 66);
        }
        html::-webkit-scrollbar-track {
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
            border-radius: 0;
            background: rgba(0,0,0,0.1);
        }
        .full_disease {
            display: inline-block;
            background: #eee;
            padding:0 10px;
            width:60px;
            text-align: center;
        }
	</style>
</head>
<body>
    <div class="bodywidth" style="min-width: 920px;">
        <div class="content">
            <div class="layui-form layui-comselect" style="position: relative;padding: 10px 10px 5px 10px;">
                <div style="margin:10px 0;">
                    <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                        <label class="layui-form-label" style="min-width:110px;width:auto;padding: 5px 0px 5px 10px;line-height: 28px;">时间：</label>
                        <div class="layui-input-inline" style="min-height: 30px;width:180px;">
                            <input id="meettime" type="text" placeholder="时间" readonly class="layui-input"/>
                        </div>
                    </div>
                </div>
                <div style="margin:10px 0;">
                    <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                        <label class="layui-form-label" style="min-width:110px;width:auto;padding: 5px 0px 5px 10px;line-height: 28px;">出席会议人员：</label>
                        <div class="layui-input-inline" style="min-height: 30px;width:375px;">
                            <textarea id="meetperson" class="layui-textarea" rows="5" cols="50" placeholder="请输入出席会议人员"></textarea>
                        </div>
                    </div>
                </div>
                <div style="margin:10px 0;">
                    <div class="layui-form-item" style="display: block;vertical-align: top;">
                        <label class="layui-form-label" style="min-width:110px;width:auto;padding: 5px 0px 5px 10px;line-height: 28px;">主持人：</label>
                        <div class="layui-input-inline" style="min-height: 30px;width:375px;">
                            <input id="host" type="text" placeholder="请输入主持人" class="layui-input"/>
                        </div>
                    </div>
                </div>
                <div style="margin:10px 0;">
                    <div class="layui-form-item" style="display: block;vertical-align: top;">
                        <label class="layui-form-label" style="min-width:110px;width:auto;padding: 5px 0px 5px 10px;line-height: 28px;">会议议题：</label>
                        <div class="layui-input-inline" style="min-height: 30px;width:375px;">
                            <input id="topic" type="text" placeholder="请输入会议议题" class="layui-input"/>
                        </div>
                    </div>
                </div>
                <div style="margin:10px 0;">
                    <div class="layui-form-item" style="display: block;vertical-align: top;">
                        <label class="layui-form-label" style="min-width:110px;width:auto;padding: 5px 0px 5px 10px;line-height: 28px;">会议记录：</label>
                        <div class="layui-input-inline" style="min-height: 30px;width:375px;">
                            <textarea id="record" class="layui-textarea" rows="8" cols="50" placeholder="请输入会议记录" style="height: 150px;"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript">
        var v = top.version;
        document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../sys/function.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../js/richang/tuoyuhuiyijiludetail.js?v='" + v + "><" + "/script>");
    </script>
</body>
</html>
