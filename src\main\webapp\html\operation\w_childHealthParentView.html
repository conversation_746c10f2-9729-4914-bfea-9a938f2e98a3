<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>入园体检家长查看情况</title>
    <link rel="stylesheet" type="text/css" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" type="text/css" href="../../css/icon.css"/>
    <link rel="stylesheet" type="text/css" href="../../styles/tbstyles.css"/>
    <link href="../../plugin/flexigrid/css/tbflexigrid.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css"/>
    <script type="text/javascript">
        // 检查 top.objdata 是否存在，如果不存在则设置默认主题
        var curtheme = (top && top.objdata && top.objdata.curtheme) ? top.objdata.curtheme : 'backstage';
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + curtheme + '.css" type="text/css" media="screen"/>');
    </script>

    <style type="text/css">
        html, body {
            background: #EAEFF3;
            overflow: hidden;
        }
        .layui-form-item {
            display: inline-block;
            vertical-align: top;
            margin-right: 15px;
            margin-bottom: 10px;
        }

        .layui-form-label {
            line-height: 28px;
            width: 110px;
        }

        .layui-input-inline {
            width: 120px;
        }

        .layui-form-mid {
            line-height: 28px;
            width: 30px;
            text-align: center;
        }


    </style>
</head>
<body>
<div class="marmain">
    <div class="content-medical">
        <div class="search-container layui-form layui-comselect" style="padding:10px;">
            <!-- 日期范围 -->
            <div class="layui-form-item">
                <label class="layui-form-label">体检日期：</label>
                <div class="layui-input-inline">
                    <input id="datestart" type="text" autocomplete="off" placeholder="开始日期" class="layui-input">
                </div>
                <div class="layui-form-mid">至</div>
                <div class="layui-input-inline">
                    <input id="dateend" type="text" autocomplete="off" placeholder="结束日期" class="layui-input">
                </div>
            </div>
            
            <!-- 报告是否已读 -->
            <div class="layui-form-item">
                <label class="layui-form-label">报告是否已读：</label>
                <div class="layui-input-inline">
                    <select id="reportread" lay-filter="reportread">
                        <option value="">请选择</option>
                        <option value="1">是</option>
                        <option value="0">否</option>
                    </select>
                </div>
            </div>
            
            <!-- 报告读取状态 -->
            <div class="layui-form-item">
                <label class="layui-form-label">报告读取状态：</label>
                <div class="layui-input-inline">
                    <select id="readstatus" lay-filter="readstatus">
                        <option value="">请选择</option>
                        <option value="1">完整</option>
                        <option value="2">部分</option>
                    </select>
                </div>
            </div>
            
            <!-- 阅读时段 -->
            <div class="layui-form-item">
                <label class="layui-form-label">阅读时段：</label>
                <div class="layui-input-inline" style="width:150px;">
                    <input id="readtimestart" type="text" autocomplete="off" placeholder="开始时间" class="layui-input" readonly>
                </div>
                <div class="layui-form-mid">至</div>
                <div class="layui-input-inline" style="width:150px;">
                    <input id="readtimeend" type="text" autocomplete="off" placeholder="结束时间" class="layui-input" readonly>
                </div>
            </div>
            
            <!-- 幼儿园 -->
            <div class="layui-form-item">
                <label class="layui-form-label">幼儿园：</label>
                <div class="layui-input-inline">
                    <select id="kindergarden" lay-filter="kindergarden">
                        <option value="">请选择</option>
                    </select>
                </div>
            </div>
            
            <!-- 年级 -->
            <div class="layui-form-item">
                <label class="layui-form-label">年级：</label>
                <div class="layui-input-inline">
                    <select id="grade" lay-filter="grade">
                        <option value="">请选择</option>
                        <option value="小班">小班</option>
                        <option value="中班">中班</option>
                        <option value="大班">大班</option>
                    </select>
                </div>
            </div>
            
            <!-- 班级 -->
            <div class="layui-form-item">
                <label class="layui-form-label">班级：</label>
                <div class="layui-input-inline">
                    <select id="classname" lay-filter="classname">
                        <option value="">请选择</option>
                        <option value="一班">一班</option>
                        <option value="二班">二班</option>
                        <option value="三班">三班</option>
                        <option value="四班">四班</option>
                        <option value="五班">五班</option>
                    </select>
                </div>
            </div>
            
            <!-- 幼儿性别 -->
            <div class="layui-form-item">
                <label class="layui-form-label">幼儿性别：</label>
                <div class="layui-input-inline">
                    <select id="childsex" lay-filter="childsex">
                        <option value="">请选择</option>
                        <option value="男">男</option>
                        <option value="女">女</option>
                    </select>
                </div>
            </div>
            
            <!-- 体检年龄 -->
            <div class="layui-form-item">
                <label class="layui-form-label">体检年龄：</label>
                <div class="layui-input-inline">
                    <select id="checkage" lay-filter="checkage">
                        <option value="">请选择</option>
                        <option value="3">3岁</option>
                        <option value="4">4岁</option>
                        <option value="5">5岁</option>
                        <option value="6">6岁</option>
                    </select>
                </div>
            </div>
            
            <!-- 关键字输入框 -->
            <div class="layui-form-item">
                <label class="layui-form-label">关键字：</label>
                <div class="layui-input-inline" style="width:190px;">
                    <input id="txtkeyword" type="text" autocomplete="off" placeholder="请输入幼儿姓名或家长姓名" class="layui-input">
                </div>
            </div>
            
            <!-- 家长性别 -->
            <div class="layui-form-item">
                <label class="layui-form-label">家长性别：</label>
                <div class="layui-input-inline">
                    <select id="parentsex" lay-filter="parentsex">
                        <option value="">请选择</option>
                        <option value="男">男</option>
                        <option value="女">女</option>
                    </select>
                </div>
            </div>
            
            <!-- 家长年龄 -->
            <div class="layui-form-item">
                <label class="layui-form-label">家长年龄：</label>
                <div class="layui-input-inline">
                    <select id="parentage" lay-filter="parentage">
                        <option value="">请选择</option>
                        <option value="20-30">20-30岁</option>
                        <option value="30-40">30-40岁</option>
                        <option value="40-50">40-50岁</option>
                    </select>
                </div>
            </div>
            
            <!-- 访问设备 -->
            <div class="layui-form-item">
                <label class="layui-form-label">访问设备：</label>
                <div class="layui-input-inline">
                    <select id="devicetype" lay-filter="devicetype">
                        <option value="">请选择</option>
                        <option value="安卓">安卓</option>
                        <option value="苹果">苹果</option>
                        <option value="PC">PC</option>
                    </select>
                </div>
            </div>
            
            <!-- 区域分类 -->
            <div class="layui-form-item">
                <label class="layui-form-label">区域分类：</label>
                <div class="layui-input-inline">
                    <select id="areatype" lay-filter="areatype">
                        <option value="">请选择</option>
                        <option value="城区">城区</option>
                        <option value="郊区">郊区</option>
                    </select>
                </div>
            </div>
            
            <!-- 孩子与家长关系 -->
            <div class="layui-form-item">
                <label class="layui-form-label">孩子与家长关系：</label>
                <div class="layui-input-inline">
                    <select id="relationship" lay-filter="relationship">
                        <option value="">请选择</option>
                        <option value="爸爸">爸爸</option>
                        <option value="妈妈">妈妈</option>
                        <option value="爷爷">爷爷</option>
                        <option value="奶奶">奶奶</option>
                        <option value="外公">外公</option>
                        <option value="外婆">外婆</option>
                        <option value="其他亲属">其他亲属</option>
                        <option value="保姆">保姆</option>
                        <option value="老师">老师</option>
                        <option value="朋友">朋友</option>
                        <option value="邻居">邻居</option>
                        <option value="医生">医生</option>
                        <option value="护士">护士</option>
                        <option value="心理师">心理师</option>
                        <option value="营养师">营养师</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
            </div>
            
            <!-- 查询按钮 -->
            <div class="layui-form-item">
                <button class="layui-btn layui-btn-normal" id="btnsearch">查询</button>
            </div>
        </div>
    </div>
    <div id="content" style="width: 100%;height: 694px;">
        <div class="marmain-cen">
            <div class="content-medical" id="divtable">
                <table id="laytable" lay-filter="laytable"></table>
            </div>
        </div>
    </div>
</div>

<script data-main="../../js/operation/w_childHealthParentView" src='../../sys/require.min.js'></script>
</body>
</html> 