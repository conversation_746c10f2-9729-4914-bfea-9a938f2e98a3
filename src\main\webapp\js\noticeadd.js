/**
 * 通知公告
 * 通知公告分为：type值分别为1，2，3，4的个通知公告， 其中1、2数据存储在市妇幼端，3、4存储在区县妇幼
 * Arg("comefrom") == "ypt" 且type=1时为市级的通知公告，本页面没有相关逻辑
 * 2为培训通知，（市端、区县端）
 * 3为专家讲座通知（发送小程序客户端，小程序客户端可以报名，签到等）
 * 4为专家坐诊通知（发送小程序客户端，小程序客户端挂号）
 * Arg("comefrom") == "fuyou" 且type=1时为普通通知（可以给机构、小程序客户端、医生端发送），给小程序客户发送时可选择是否可以预约挂号
 */
layui.config({
    base: './js/'
}).extend({ //设定模块别名
    system: '../../sys/system',
    promise: '../../plugin/promise/promise'
});
var curmoment1 = parent.objdata.curmoment.format('YYYY-MM-DD HH:mm:SS');
window.UEDITOR_HOME_URL = "../../plugin/ueditor";
var objdata = {
    strOssSize: "?x-oss-process=image/resize,m_fill,h_180,w_180",
    attacheCount: -1, //附件数
    attacheMaxSize: 200 * 1024 * 1024, //上传附件最大200M
    attacheMaxFileNum: 3, //单次最多上传文件数
    myid: parent.objdata.my.id,
    myname: parent.objdata.my.username,
    myPhone: parent.objdata.my.mobile,
    newNotice: {},
    noticeCache: { //缓存查询过的通知公告
        sent: [],
        saved: [],
        receive: []
    },
    pageConf: { //分页总配置
        pageNum: 3 //每页条数
    },
    strSearch: "", //搜索关键字
    upFile: {} //上传文件
    , needinfos: {'areacode': '区（市）县', 'sex': '性别', 'birthday': '出生年月', 'icard': '身份证号', 'edu': '学历', 'school': '毕业学校', 'professional': '专业', 'photo': '1寸免冠照片', 'eduphoto': '学历证明照片', 'workphoto': '工作证明照片', 'certnames': "证书名称"}//报名需要提交的资料//, 'isnhascert': '有无“托幼机构保健人员岗前培训证”'
    , selneedinfos: {}//现在的需要报名提交的资料
    , defaultneedinfos: {'yeyname': '单位名称', 'enrollname': '姓名', 'enrollmobile': '手机号', 'duty': '职务'}//默认需要报名提交的资料（不能取消）
    // , sendcountinfo: null
    , objnotice: {areanum: 0, toorganoryeynum: 0, areaname: [], name: {}, tousernamenum: 0}//通知选择群发内容初始变量//areanum: 区县个数, toorganoryeynum: 医院或托幼机构个数, areaname: 地区名称, name: 妇幼名称或医院名称, tousernamenum: 用户个数
    , objroletypename: {"ehos": "基层卫生服务机构", "ehosin": "入托体检机构", "ehosother": "其他体检机构", "eyey": "幼儿园", "echild": "托育机构", "echyey": "托幼一体园"}
    , totypename: {"e": {"ehos": "基层卫生服务机构", "ehosin": "入托体检机构", "ehosother": "其他体检机构"}, "ename": "医疗保健机构", "y": {"eyey": "幼儿园", "echild": "托育机构", "echyey": "托幼一体园"}, "yname": "托幼机构"}
    , objarr: {'e': ['e'], 'y': ['y'], "ey": ['e', 'y'],  "p": ['p'],  "employee": ['employee'],  "doctor": ['doctor'], }//包含的发送对象
    , ue: null
    , certids: ""
    , expertype: null//专家类型
    , objexpertype: {
        "1": "讲座型中医儿科专家",
        "2": "治疗型中医儿科专家"
    }
    , getdatafromypt: 0//读取存储在市级的通知
}, laydate;
var outCon = $("#new-notice"),
    typeList = $("#notice-type-list"),//选择通知公告类型
    newType = $("#add-notice-type");//新增类型
layui.use(['system', 'promise', 'form', 'laydate'], function () {
    form = layui.form;
    laydate = layui.laydate;
    var oneNotice = objdata.oneNotice = new NewNotice();
    oneNotice.init();
    oneNotice.initUEditor();
    // oneNotice.upAttachement({
    //     $_ele: "#js-cover",
    //     title: "Images",
    //     extensions: 'jpg,jpeg,png',
    //     mimeTypes: 'image/jpg,image/jpeg,image/png'
    // });
    objdata.attcheUpLoader = oneNotice.upAttachement({
        $_ele: "js-attache"
    });
    initEvent();
    if(Arg("type") == 1){//普通通知， 给机构，给小程序客户端（家长、老师。其实是给所有小程序用户发送），小程序医生端
        $("#sel_totype").html('<option value=""></option><option value="ey">辖区机构</option><option value="p">小程序客户端</option><option value="doctor">小程序医生端</option>');
    } else if(Arg("type") == 2){//培训通知
        $("#sel_totype").html('<option value=""></option><option value="ey">辖区机构</option>');
        if(Arg("isforward")){//转发
            $(".btnoperate").hide();//隐藏全部按钮
            $("#btnforward").show();//显示转发按钮
        }
        objdata.getdatafromypt = 1;
    } else if(Arg("type") == 3 || Arg("type") == 4){//3.专家讲座通知  4.专家坐诊
        $("#sel_totype").html('<option value="p">小程序客户端</option>');
    } else {
        $("#sel_totype").html('<option value=""></option>');
    }
    form.render();
    if(Arg("type") == 3 || Arg("type") == 4){//专家讲座或专家坐诊通知时
        $('#sel_totype').next().find('.layui-anim').children('dd').eq(0).click();//默认点击选中
    }
    reloadHandleUI();
    objdata.enrollBtn = undefined;
    objdata.newNotice.totype = $("select[name='city']").val();
    $("#new-notice").show().siblings().hide().eq(0).show();
    objdata.curType = "newNotice";
    //编辑草稿
    if (Arg("noticeid")) {
        getOneNotice(Arg("noticeid"), oneNotice);
    }
});

//UI模板
var templete = {
//    附件模板
        attacheTem: '<dl style="cursor: pointer;" class="layui-txt-add" data-path="{{filePath}}" data-file-id="{{fileId}}" data-attachid="{{attachid}}">' +
            '<dt><img src="{{icon}}"></dt>' +
            '<dd>' +
            '<p class="layui-txt-tit">{{fileName}}<label>（{{fileSize}}）</label></p>' +
            '<a class="js-del-attache" style="color:#1ca89f!important;">{{percent}}删除</a>' +
            '</dd>' +
            '</dl>',
        phoneAttacheTem: '<dl style="cursor: pointer;" class="layui-txt-add">' +
            '<dt><img src="{{icon}}"></dt>' +
            '<dd>' +
            '<p class="layui-txt-tit">{{file}}</p>' +
            '<a style="color: #1ca89f  !important;">查看</a>' +
            '</dd>' +
            '</dl>',
        noticeTypeTem: '<tr data-id="{{id}}">' +
            '<td style="text-align: center;">' +
            '<div class="layui-unselect layui-form-radio"><i class="layui-anim layui-icon layui-anim-scaleSpring"></i></div>' +
            '</td>' +
            '<td class="js-type-name" style="text-align: center;">{{typename}}</td>' +
            '<td style="text-align: center;">' +
            '<a class="js-edit-type" style="color: #1ca89f  !important;margin: 0 10px;display: {{czbtnisnshow}}">编辑</a>' +
            '<a class="js-del-type" style="color: #1ca89f  !important;margin: 0 10px;display: {{czbtnisnshow}}">删除</a>' +
            '</td>' +
            '</tr>',
        enrollTem: '   <span style="display: inline-block;" class="enroll-label noti-tit-right">\n' +
            '   <label class="enroll-label" style="color:#4A90E2;"><img src="../images/sel_icon01.png">需要报名</label>\n' +
            '   <span style="display: {{enTimeShow}}"><label class="time-txt" style="color:#FE3939"><img src="../images/time_01.png">报名截止时间</label> <label class="time-num" data-enrolltime = "{{enrolltime}}">{{enrollTime}}</label></span>\n' +
            '   <label class="end-label" style="display:  {{endTimeShow}};">已截止报名</label>\n' +
            '   <input datatype="Date" format="ymdhms" style="display: none;">\n' +
            '   <label class="layui-btn layui-btn-mini qr-delay-enroll-time" datatype="Date" format="ymdhms" style="height: 24px;line-height: 24px;margin-left:10px;color:#fff !important; background:#4A90E2 !important; padding: 0 10px; font-size: 12px;" noticeid="{{id}}">改截止时间</label><label class="layui-btn layui-btn-mini qr-delay-enroll-shenroll" style="height: 24px;line-height: 24px;margin-left:10px;color:#fff !important; background:#4A90E2 !important; padding: 0 10px; font-size: 12px;">报名资质审核</label>\n' +
            '      </span>',
        tuoyutem: '<div class="layui-form-item expert-is" style="">\
                       <label class="layui-form-label" style="padding-left: 0;"><i class="layui-mark-red">*</i>是否允许报名:</label>\
                       <div class="layui-input-inline qr-enroll">\
                           <input class="radqrenroll" name="qr-enroll" type="radio" checked="checked" value="0" title="不允许">\
                           <input class="radqrenroll" name="qr-enroll" type="radio" value="1" title="允许">\
                       </div>\
                       <div>\
                           <div class="layui-form-label" style="padding-left: 0;display: none;"><span\
                                   style="float: left;margin-top: 7px;"><i class="layui-mark-red">*</i>请选择截止报名日期：</span>\
                               <div class="layui-input-inline" style="margin-top: 5px;width: 200px;">\
                                   <input id="txtendtime" class="layui-input gray-select cal_ico txtweight"\
                                          type="text" require="true" datatype="Date" format="ymdhms" msg="请正确输入活动结束时间" style="width: 200px;display: inline-block;"/>\
                               </div>\
                           </div>\
                       </div>\
                   </div>\
                   <div class="layui-form-item" id="divisnneedinfo" style="display: none;">\
                       <label class="layui-form-label" style="padding-left: 0;"><i class="layui-mark-red">*</i>报名是否需要提交资料:</label>\
                       <div class="layui-input-inline qr-info">\
                           <input name="qr-info" type="radio" checked="checked" value="0" title="不需要">\
                           <input name="qr-info" type="radio" value="1" title="需要">\
                       </div>\
                   </div>\
                   <div class="layui-form-item" id="divneedinfos" style="display: none; margin-bottom: 0;">\
                       <label class="layui-form-label" style="padding-left: 0;"><i class="layui-mark-red">*</i>报名需要提交的资料:</label>\
                       <span id="selneedinfos" class="layui-btn layui-btn-mini" style="margin: 5px 0 0 0px; width: 88px;">选择</span>\
                   </div>\
                   <div class="layui-form-item">\
                       <label class="layui-form-label" style="padding-left: 0;width: auto;">\
                           <div style="display:block; text-align: left;text-align: left;line-height: 25px;"\
                                id="divseledneedinfos" class="layui-input-block"></div>\
                       </label>\
                   </div>\
                   <div class="layui-form-item" id="divisnprocess" style="display: none; ">\
                       <label class="layui-form-label" style="padding-left: 0;"><i class="layui-mark-red">*</i>报名资料是否需要自己审核:</label>\
                       <div class="layui-input-inline qr-process">\
                           <input name="qr-process" type="radio" value="0" title="不需要">\
                           <input name="qr-process" type="radio" checked="checked" value="1" title="需要">\
                       </div>\
                   </div>',//托育模板
        parenttem: '<div id="divisnaddexpert" class="layui-form-item" style="">\
                        <label class="layui-form-label" style="padding-left: 0;"><i class="layui-mark-red">*</i>是否添加专家:</label>\
                        <div class="layui-input-inline qr-expert">\
                            <input class="radisnaddexpert" name="qr-expert" type="radio" checked="checked" value="0" title="否">\
                            <input class="radisnaddexpert" name="qr-expert" type="radio" value="1" title="是">\
                        </div>\
                    </div>\
                    <div id="divisnshowregister" class="layui-form-item" style="">\
                        <label class="layui-form-label" style="padding-left: 0;"><i class="layui-mark-red">*</i>是否允许预约挂号:</label>\
                        <div class="layui-input-inline qr-register">\
                            <input class="radisnregister" name="qr-register" lay-filter="qr-register" type="radio" checked="checked" value="0" title="不允许">\
                            <input class="radisnregister" name="qr-register" lay-filter="qr-register" type="radio" value="1" title="允许">\
                        </div>\
                    </div>\
                    <div id="divselexpert" class="layui-form-item expert-is" style="display: none;">\
                        <label class="layui-form-label" style="padding-left: 0;float: left;width:auto;margin-right:10px;"><i class="layui-mark-red">*</i>专家:</label>\
                        <input id="btnselexpert" type="button" class="layui-btn js-choose-expert blockbg"\
                               value="选择" style="margin-top: 5px;height: 36px;width: 88px;">\
                        <div id="divexpertinfo"></div>\
                    </div>\
                    <div id="expertnoticeinfo"></div>'
        //讲座型
        , expertype1: '<div class="layui-form-item expert-is">\
                        <label class="layui-form-label" style="padding-left: 0;"><i class="layui-mark-red">*</i>讲座时间:</label>\
                        <div class="layui-input-inline" style="margin-top: 5px;width: 500px;">\
                            <input id="txtlecturestarttime" class="layui-input gray-select cal_ico txtweight"\
                                   type="text" require="true" datatype="Date" format="ymdhms" msg="请选择讲座开始时间" value="{{lecturestarttime}}" style="width: 200px;display: inline-block;"/>至\
                            <input id="txtlectureendtime" class="layui-input gray-select cal_ico txtweight" type="text" require="true"\
                                   datatype="Date" format="ymdhms" value="{{lectureendtime}}"  msg="请选择讲座结束时间" style="width: 200px;display: inline-block;"/>\
                        </div>\
                    </div>\
                    <div class="layui-form-item expert-is">\
                        <label class="layui-form-label" style="padding-left: 0;"><i class="layui-mark-red">*</i>讲座地点:</label>\
                        <div class="layui-input-inline" style="margin-top: 5px;width: 400px;">\
                            <input id="txtlectureaddress" type="text" name="title" lay-verify="required"\
                                   placeholder="请输入讲座地点" autocomplete="off" value="{{lectureaddress}}" class="layui-input">\
                        </div>\
                    </div>\
                    <div class="layui-form-item expert-is">\
                        <label class="layui-form-label" style="padding-left: 0;"><i class="layui-mark-red">*</i>是否允许报名:</label>\
                        <div class="layui-input-inline qr-enroll">\
                            <input class="radqrenroll" name="qr-enroll" type="radio" checked="checked" value="0" title="不允许">\
                            <input class="radqrenroll" name="qr-enroll" type="radio" value="1" title="允许">\
                        </div>\
                        <div>\
                            <div class="layui-form-label" style="padding-left: 0;display:none;">\
                                <span style="display: inline-block;float: left;margin-top: 7px;">\
                                    <i class="layui-mark-red">*</i>请选择截止报名日期：</span>\
                                <div class="layui-input-inline" style="margin-top: 5px;width: 200px;">\
                                    <input id="txtendtime" class="layui-input gray-select cal_ico txtweight"\
                                           type="text" require="true" datatype="Date" format="ymdhms" value="{{enrolltime}}" msg="请正确输入活动结束时间" style="width: 200px;display: inline-block;"/>\
                                </div>\
                            </div>\
                        </div>\
                    </div>'
        //治疗型
        , expertype2: '<div class="layui-form-item expert-is">\
                        <label class="layui-form-label" style="padding-left: 0;"><i class="layui-mark-red">*</i>坐诊时间:</label>\
                        <div class="layui-input-inline" style="margin-top: 5px;width: 500px;">\
                            <input id="txtsittingtimestart" class="layui-input gray-select cal_ico txtweight"\
                                   type="text" require="true" datatype="Date" format="ymdhms" value="{{sittingtimestart}}" msg="请选择坐诊开始时间" style="width: 200px;display: inline-block;"/>-\
                            <input id="txtsittingtimeend" class="layui-input gray-select cal_ico txtweight" type="text" require="true"\
                                   datatype="Date" format="ymdhms" value="{{sittingtimeend}}" msg="请选择坐诊结束时间" style="width: 200px;display: inline-block;"/>\
                        </div>\
                    </div>\
                    <div id="divdepartment" class="layui-form-item expert-is">\
                        <label class="layui-form-label" style="padding-left: 0; width:auto; margin-right:10px; float:left;"><i class="layui-mark-red">*</i>所属科室:</label>\
                        <input id="btndepartmentid" type="button" class="layui-btn js-choose-department blockbg" value="选择" style="margin-top: 5px;height: 36px;width: 88px;">\
                     <div id="divdepartmentidinfo" style="margin-top: 10px;" departmentid="{{departmentid}}">{{departnname}}</div>\
                    </div>\
                    <div class="layui-form-item expert-is">\
                        <div>\
                            <div class="layui-form-label" style="padding-left: 0;display: none;" id="divregisterendtime">\
                                <span style="display: inline-block;float: left;margin-top: 7px;">\
                                    <i class="layui-mark-red">*</i>请选择截止预约挂号时间：</span>\
                                <div class="layui-input-inline" style="margin-top: 5px;width: 200px;">\
                                    <input id="txtregisterendtime" class="layui-input gray-select cal_ico txtweight"\
                                           type="text" require="true" datatype="Date" format="ymdhms" value="{{registerendtime}}" msg="请选择截止预约挂号时间" style="width: 200px;display: inline-block;"/>\
                                </div>\
                            </div>\
                        </div>\
                    </div>'
    //专家信息
        , expertinfo: '<div class="layui-col-xs6 layui-col-sm6 layui-col-md6" style="margin-top: 10px;">\
                        <div class="health-list hedoc divselexpertinfo" expertid="{{expertid}}" expertfrom="{{expertfrom}}" expertype="{{expertype}}">\
                            <div class="health-listdiv pad10" style="background: #EEF6FF;width: 500px; ">\
                                <div style="position: relative;display: inline-block;vertical-align: top;"><img src="{{img}}" class="doc-defimg"></div>\
                                <div style="display: inline-block;">\
                                    <h3><b>{{name}}</b> <span>{{professional}}&nbsp;&nbsp;&nbsp; {{expertypename}}</span></h3>\
                                    <h5><span>{{grade}}</span>{{workunit}}</h5>\
                                    <div class="detail-doc">\
                                        <p>{{goodat}}</p>\
                                    </div>\
                                </div>\
                            </div>\
                            </div>\
                    </div>'
    },
    /**
     * 替换模板数据 模板与数据中的变量名完全一致
     * @param template {String} 模板字符串
     * @param model {Object} 模板数据
     * @returns {*} {strHtml} 数据替换后的模板字符串
     */
    renderTemp = function (template, model) {
        var reg = /\{\{\w+\}\}/g;
        template = template.replace(reg, function (regStr) {
            var reg2 = /\{\{(\w+)\}\}/g,
                key = reg2.exec(regStr)[1];
            return model[key];
        });
        return template;
    };

//初始化事件
function initEvent() {
    //监听提交
    form.on('submit(formforward)', function (data) {//转发通知
        parent.layer.load();
        if (objdata.isAdding) {
            parent.layer.msg("通知正在转发，请稍后");
            return;
        }
        objdata.isAdding = true;
        if (objdata.status === "edit") {
            objdata.saveHasCli = false;
            objdata.noticeCache.saved = [];
            publishNotice(1);
        } else {
            publishNotice(1);
        }
        return false;
    });
    form.on('submit(formDemo)', function (data) {//发布通知
        parent.layer.load();
        if (objdata.isAdding) {
            parent.layer.msg("通知正在创建中，请稍后");
            return;
        }
        objdata.isAdding = true;
        if (objdata.status === "edit") {
            objdata.saveHasCli = false;
            objdata.noticeCache.saved = [];
            publishNotice(1);
        } else {
            publishNotice(1);
        }
        return false;
    });
    form.on('submit(formDemo2)', function (data) {//保存草稿
        parent.layer.load();
        if (objdata.isAdding) {
            parent.layer.msg("通知正在创建中，请稍后");
            return;
        }
        objdata.isAdding = true;
        if (objdata.status === "edit") {
            publishNotice(0);
        } else {
            publishNotice(0);
            objdata.status = "edit"
        }
        return false;
    });
    $("#btncancel").click(function () {
        var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
        parent.layer.close(index);
    });
    outCon.find("#btnmoredetail").off("click").on("click", function () {
        parent.layer.open({
            type: 2,
            title: '查看群发对象',
            area: ['1000px', '80%'], //宽高
            btnAlign: 'c',
            content: 'html/noticesendobj.html?v=' + (Arg("v") || 1) + '&mid=' + Arg("mid"),
            btn: ['关闭'],
            success: function (layero, index) {
                noticesendobjpage = layero.find('iframe')[0].contentWindow; //得到iframe页的窗口对象，执行iframe页的方法：iframeWin.method();
                noticesendobjpage.$("#divsendcount").html($("#spselareainfo").html());
                var objareainfo = objdata.objSelectUser.objareainfo;
                var arrtab = [];
                if (objareainfo) {
                    arrtab = ['<table border="1" cellpadding="0" cellspacing="0" style="width:100%;border-collapse: collapse; margin:20px auto"><tbody>'];
                    for (var c in objareainfo) {
                        var item = objareainfo[c], arrt = [], objt = {}, strname = '', areaorganoryeynum = 0;
                        for (var t in item) {
                            arrt.push(t);
                            areaorganoryeynum += item[t]["toorganoryeynid"].length;
                        }
                        var idx = 0;
                        var arrtotype = objdata.objarr[objdata.newNotice.totype];
                        for (var i = 0; i < arrtotype.length; i++) {
                            var strtotype = arrtotype[i];
                            for (var t in item) {
                                strname = '<td>' + objdata.totypename[strtotype][t] + item[t]["toorganoryeynid"].length + "个：" + '</td><td class="graytxt">' + item[t]["toorganroyeyname"].join(",") + '</td>';
                                if (idx == 0) {
                                    arrtab.push('<tr><td rowspan="' + arrt.length + '" style="text-align:center; background:#FAFAFA;"><p style="font-size:14px;">' + objdata.objSelectobj.objareaname[c] + '</p><p class="graytxt">' + objdata.totypename[strtotype + "name"] + areaorganoryeynum + '个</p></td>');
                                    arrtab.push(strname + '</tr>');
                                } else {
                                    arrtab.push('<tr>' + strname + '</tr>');
                                }
                                idx++;
                            }
                        }
                    }
                    arrtab.push("</tbody></table>");
                } else {
                    parent.layer.msg("请先选择群发对象！");
                }
                noticesendobjpage.$("#divsenddetail").html(arrtab.join(""));
            }
            // yes: function (index, layero) {
            // }
        });
    });
    outCon.off("click", ".js-del-attache").on("click", ".js-del-attache", function (ev) {//    删除附件
        var curDl = $(this).parent().parent();
        //    删除uploader中的文件
        if(curDl.attr("data-file-id")){
            objdata.attcheUpLoader.removeFile(curDl.attr("data-file-id"), true);
            objdata.attcheUpLoader.reset();
        }
        curDl.remove();
        curDl = null;
    });
    outCon.off("focusout", ".js-title").on("focusout", ".js-title", function (ev) {//    标题栏
        var tit = $(this).encodeval();
        if (tit && tit.length <= 50) {
            outCon.find(".js-phone-title").text(tit);
        } else if (tit.length > 50) {
            parent.layer.msg("标题超出50字");
        }
        objdata.newNotice.title = tit;
    });
    outCon.off("focusout", ".js-from-depart").on("focusout", ".js-from-depart", function (ev) {//    发文单位
        var depart = $(this).encodeval();
        outCon.find(".js-phone-depart").text(depart);
    });
    outCon.off("focusout", ".js-summary").on("focusout", ".js-summary", function (ev) {//    摘要js-summary
        var summary = $(this).encodeval();
        outCon.find(".js-phone-summary").text(summary);
    });
    outCon.off("click", ".qr-get-summary").on("click", ".qr-get-summary", function (ev) {//    获取摘要
        var summary = UE.getEditor('js-editor').getContentTxt().substring(0, 55);
        outCon.find(".js-summary").val(summary);
    });
    form.on('select(sel_totype)', function (ev) {//发送对象更改时发起提示
        var totype = ev.value;
        if (!totype) {
            parent.layer.msg("请选择一个群发对象");
            return;
        }
        // if (typeof objdata.preTotype === "undefined") {
            objdata.preTotype = totype;
        // }
        switchsendobj(totype);
    });
    outCon.off("click", ".js-choose-user").on("click", ".js-choose-user", function (ev) {//    选择发送的对象(选择按钮点击)
        var eleToType = outCon.find("#sel_totype"),
            toVal = "";
        if (eleToType.length) {
            toVal = eleToType.val();
        }
        if (toVal === "") {
            parent.layer.msg("请先选择群发对象");
            return;
        }
        if (!objdata.newNotice.preTotype) objdata.newNotice.preTotype = toVal;
        objdata.newNotice.totype = toVal;
        var organoryeyname = "";
        if (toVal === "e") {
            organoryeyname = "医疗保健机构";
        } else if (toVal === "y") {
            organoryeyname = "托幼机构";
        } else if (toVal === "ey") {
            organoryeyname = "辖区机构";
        }
        parent.layer.open({
            type: 2,
            title: '选择群发' + organoryeyname,
            area: ['700px', '400px'], //宽高
            btnAlign: 'c',
            content: 'html/noticeselper.html?v=' + (Arg("v") || 1) + '&totype=' + toVal + "&mid=" + Arg("mid") + '&pretotype=' + objdata.newNotice.preTotype,
            btn: ['确定', "取消"],
            yes: function (index, layero) {
                // parent.layer.load();
                var subpage = layero.find('iframe')[0].contentWindow;
                var arrareacode = [], arrareaname = [], objarea = {}, strreceiveonj = '';
                var arrareainfo = subpage.formSelects.value("receivearea");
                objdata.objSelectUser.strareainfo = "";
                if(!objdata.objSelectUser.receiveobj){
                    objdata.objSelectUser.receiveobj = {};//接收对象
                }
                for (var i = 0; i < arrareainfo.length; i++) {
                    arrareacode.push(arrareainfo[i].value);
                    arrareaname.push(arrareainfo[i].name);
                    objarea[arrareainfo[i].value] = arrareainfo[i].name;
                }
                if (arrareacode.length <= 0) {
                    parent.layer.msg("请选择区县");
                    parent.layer.closeAll("loading");
                    return;
                }

                objdata.objSelectUser.strareainfo = arrareacode.join(",");
                objdata.objSelectUser.receiveobj = {};
                var arrroletype = [];
                var arrsole = [];
                var arrtoval = objdata.objarr[toVal];
                for (var i = 0; i < arrtoval.length; i++) {
                    var objroletype = objdata.totypename[arrtoval[i]];
                    for (var t in objroletype) {
                        if(subpage.$("input[name='chkroletype" + t + "']").is(":checked")){
                            if(!objdata.objSelectUser.receiveobj[t]){
                                objdata.objSelectUser.receiveobj[t] = [];
                            }
                            var arrrolename = [];
                            strreceiveonj += objroletype[t];
                            arrroletype.push(t);
                            subpage.$(".chkrole" + t + ":checked").each(function () {
                                arrsole.push($(this).val());
                                arrrolename.push($(this).prop("title"))
                                objdata.objSelectUser.receiveobj[t].push([$(this).val(), $(this).prop("title")]);
                            });
                            if(arrrolename.length > 0){
                                strreceiveonj += "：" + arrrolename.join(',') + "，";
                            }
                        }
                    }
                }
                if (arrroletype <= 0) {
                    parent.layer.msg("请选择发送的对象！");
                    parent.layer.closeAll("loading");
                    return;
                }
                if (arrsole.length <= 0) {
                    objdata.objSelectUser.arrsole = [];
                    parent.layer.msg("请选择发送的角色！");
                    parent.layer.closeAll("loading");
                    return;
                } else {
                    objdata.objSelectUser.arrsole = arrsole;
                    outCon.find("#spselareainfo").html("已选角色（" + strreceiveonj.substring(0, strreceiveonj.length - 1) + ")");
                    // outCon.find("#divareainfo").html(arrareaname.join("，"));
                    parent.layer.close(index);
                }
            }
        });
    });
    outCon.off("click", ".js-notice-type,#txt-js-notice-type").on("click", ".js-notice-type,#txt-js-notice-type", function (ev) {//    选择通知类型
        layer.open({
            title: "选择通知类型",
            type: 1,
            content: typeList,
            area: ["800px", "60%"],
            btn: ["确定"],
            btnAlign: "c",
            //icon : 1,
            success: function (layero, index) {
                new Promise(function (resolve, reject) {
                    var arrsm = ["notice.selNoticeType"];
                    if(objdata.getdatafromypt){
                        arrsm = ["notice.selNoticeTypeFromYpt", parent.objdata.my.id, parent.objdata.my.fuyouid];
                    }
                    $.sm(function (re, err) {
                        if (!err && re) {
                            resolve(re);
                        } else {
                            layer.msg("发送消息失败")
                        }
                    }, arrsm);
                }).then(function (data) {
                    objdata.notiTypeName = data;
                    typeList.find(".js-body").html('');
                    for (var i = 0; i < data.length; i++) {
                        data[i].czbtnisnshow = "inline-block";
                        if(data[i].id == 1){//id号是1的为默认不可改
                            data[i].czbtnisnshow = "none";
                        }
                        typeList.find(".js-body").append(renderTemp(templete.noticeTypeTem, data[i]));
                    }
                    objdata.newNotice.noticeType ? typeList.find(('tr[data-id="' + objdata.newNotice.noticeType + '"]')).find("i").click() : typeList.find(('tr[data-id="1"]')).find("i").click();
                });
            },
            yes: function (index, layero) {
                if (objdata.newNotice.noticeType === undefined) {
                    layer.msg("请选择通知类型");
                    return false;
                }
                layer.close(index);
            }
        });

    });
    typeList.off("click", ".layui-form-radio").on("click", ".layui-form-radio", function (ev) {//    确定通知类型
        $(this).addClass("layui-form-radioed").closest("tr").siblings().find("div").removeClass("layui-form-radioed");
        objdata.newNotice.noticeType = $(this).closest("tr").attr("data-id");
        outCon.find("#txt-js-notice-type").val($(this).closest("tr").find(".js-type-name").text());
    });
    typeList.off("click", ".js-del-type").on("click", ".js-del-type", function (ev) {
        var _this = this;//    删除通知类型
        layer.open({
            title: "提示",
            content: "<div>确认执行此操作？</div>",
            btn: ["确定", "取消"],
            yes: function (index) {
                var typeTr = $(_this).parent().parent(),
                    _index = typeTr.index() - 1,
                    typeId = typeTr.attr("data-id");
                typeTr.remove();
                typeTr = null;
                var arrsm = ["notice.delNoticeType", typeId];
                if(objdata.getdatafromypt){
                    arrsm = ["notice.delNoticeTypeFromYpt", typeId];
                }
                $.sm(function (re, err) {
                    err && parent.layer.msg("删除通知类型失败");
                }, arrsm);
                objdata.newNotice.noticeType === typeId && typeList.find(('tr[data-id="1"]')).find("i").click();
                objdata.notiTypeName.splice(_index, 1);
                layer.close(index);
            }
        });
    });
    typeList.off("click", ".js-edit-type").on("click", ".js-edit-type", function (ev) {//    编辑通知类型
        var _this = this,
            typeTr = $(_this).parent().parent(),
            typeId = typeTr.attr("data-id"),
            typeIndex = typeTr.index() - 1,
            typeName = typeTr.children().eq(1).text();
        $("#txtnoticetypename").val(typeName);
        layer.open({
            title: "编辑通知类型",
            type: 1,
            content: newType,
            area: ["500px", "230px"],
            btn: ["确定", "取消"],
            btnAlign: "c",
            yes: function (index, layero) {
                var isSameTypeName = function (typeName) {
                    if (typeName === "通知公告") {
                        return true;
                    }
                    for (var i = 0; i < objdata.notiTypeName.length; i++) {
                        if (objdata.notiTypeName[i].typename === typeName) {
                            return true;
                        }
                    }
                    return false;
                };
                typeName = newType.find("input").encodeval();
                var typeName1 = newType.find("input").val();
                if (isSameTypeName(typeName1)) {
                    parent.layer.msg("类型名称重复，请重新输入");
                    return;
                }
                if (typeName1.length > 10) {
                    parent.layer.msg("字数超长，请重新输入");
                    return;
                }
                if (typeName1.length === 0) {
                    parent.layer.msg("不可为空，请重新输入");
                    return;
                }
                return new Promise(function (resolve, reject) {
                    var arrsm = ["notice.upNoticeType", typeId, typeName];
                    if(objdata.getdatafromypt){
                        arrsm = ["notice.upNoticeTypeFromYpt", typeId, typeName];
                    }
                    $.sm(function (re, err) {
                        if (!err && re) {
                            resolve([typeId, typeName]);
                        } else {
                            parent.layer.msg("发送消息失败")
                        }
                    }, arrsm);
                }).then(function (arrType) {
                    var param = {
                        id: arrType[0],
                        typename: typeName1
                    };
                    objdata.notiTypeName.splice(typeIndex, 1, param);
                    typeTr.replaceWith(renderTemp(templete.noticeTypeTem, param)).find("i").click();
                    objdata.newNotice.noticeType === arrType[0] && typeList.find(('tr[data-id="' + arrType[0] + '"]')).find("i").click();
                    ;
                    newType.find("input").val("");
                    layer.close(index);
                });
            },
            cancel: function (index) {
                typeList.show();
                layer.close(index);
            }
        });
    });
    typeList.off("click", ".js-add-type").on("click", ".js-add-type", function (ev) {//    新增通知类型
        layer.open({
            title: "新增通知类型",
            type: 1,
            content: newType,
            area: ["500px", "230px"],
            btn: ["确定", "取消"],
            btnAlign: "c",
            yes: function (index, layero) {
                var isSameTypeName = function (typeName) {
                    if (typeName === "通知公告") {
                        return true;
                    }
                    for (var i = 0; i < objdata.notiTypeName.length; i++) {
                        if (objdata.notiTypeName[i].typename === typeName) {
                            return true;
                        }
                    }
                    return false;
                };
                var typeName = newType.find("input").encodeval();
                var typeName1 = newType.find("input").val();
                if (isSameTypeName(typeName1)) {
                    parent.layer.msg("类型名称重复，请重新输入");
                    return;
                }
                if (typeName1.length > 10) {
                    parent.layer.msg("字数超长，请重新输入");
                    return;
                }
                if (typeName1.length === 0) {
                    parent.layer.msg("不可为空，请重新输入");
                    return;
                }
                return new Promise(function (resolve, reject) {
                    var arrsm = ["notice.addNoticeType", typeName];
                    if(objdata.getdatafromypt){
                        arrsm = ["notice.addNoticeTypeFromYpt", typeName, jQuery.getparent().objdata.my.fuyouid, jQuery.getparent().objdata.my.id];
                    }
                    $.sm(function (re, err) {
                        if (!err && re) {
                            resolve([re, typeName]);
                        } else {
                            parent.layer.msg("发送消息失败")
                        }
                    }, arrsm);
                }).then(function (arrType) {
                    var param = {
                        id: arrType[0],
                        typename: typeName1
                    };
                    objdata.notiTypeName.push(param);
                    typeList.show().find(".js-body").append(renderTemp(templete.noticeTypeTem, param)).children().slice(-1).find("i").click();
                    newType.find("input").val("");
                    layer.close(index);
                });
            },
            cancel: function (index) {
                typeList.show();
                layer.close(index);
            }
        });
    });

    outCon.off("click", ".qr-phone-show").on("click", ".qr-phone-show", function (ev) {//    手机展示页图文和正文切换
        if (!$(this).hasClass("layui-btn-primary")) {
            return;
        }
        $(this).removeClass("layui-btn-primary").siblings().addClass("layui-btn-primary");
        outCon.find(".notice-list").children().hide().eq($(this).index()).show();
        outCon.find(".js-phone-content").html(UE.getEditor('js-editor').getContent());
        var attches = outCon.find("#filllist dl");
        if (!attches.length) {
            return;
        }
        var arrHtml = [];
        for (var i = 0; i < attches.length; i++) {
            var curAttache = attches.eq(i),
                param = {
                    icon: curAttache.find("img").attr("src"),
                    file: curAttache.find("p").html()
                };
            arrHtml.push(renderTemp(templete.phoneAttacheTem, param));
        }
        outCon.find(".js-phone-attache-list").html(arrHtml.join(""));
    });
    outCon.off('click', ".qr-enroll .layui-unselect").on("click", ".qr-enroll .layui-unselect", function (ev) {//    是否需要报名
        if ($(this).find("div").text() === "允许") {//允许报名
            $("#txtendtime").parent().parent().show();
            $("#divisnneedinfo").show();
            objdata.newNotice.isEnroll = 1;
            objdata.newNotice.enrolltime = $("#txtendtime").val();
        } else {
            $("#txtendtime").val("");
            $("#txtendtime").parent().parent().hide();
            $("#divisnneedinfo,#divneedinfos").hide();
            $("#divseledneedinfos").html("").closest(".layui-form-item").hide();
            objdata.newNotice.isEnroll = 0;
        }
    });
    outCon.off('click', ".qr-info .layui-unselect").on("click", ".qr-info .layui-unselect", function (ev) {//报名是否需要提交资料:
        if ($(this).find("div").text() === "需要") {
            $("#divneedinfos,#divisnprocess").show();
            // objdata.newNotice.isnNeedinfo = 1;
        } else {
            $("#divneedinfos,#divisnprocess").hide();
            // objdata.newNotice.isnNeedinfo = 0;
        }
    });
    $("#noticecontent").off('click', '#selneedinfos').on('click', '#selneedinfos', function () {//选择报名需要提交的资料
        parent.layer.open({
            type: 2,
            title: '选择报名需要提交的资料',
            area: ['800px', '80%'], //宽高
            btnAlign: 'c',
            content: 'html/noticeselneedinfos.html?v=' + (Arg("v") || 1) + '&mid=' + Arg("mid"),
            btn: ['确定', "取消"],
            success: function (layero, index) {
                jQuery.getparent().objdata.noticeselneedinfos = layero.find('iframe')[0]; //得到iframe页的窗口对象，执行iframe页的方法：iframeWin.method();
                // var allseled = layero.find("#txtallsel").is(":checked");
            },
            yes: function (index, layero) {
                var certpage = layero.find('iframe')[0].contentWindow;
                var arrneedinfo = [];
                objdata.selneedinfos = {};
                objdata.certids = "";
                certpage.$("#ulselneedinfo").find('input:checked').each(function () {
                    var selv = $(this).val();
                    if(selv == "certnames"){//证书名称
                        // var arrcertids = certpage.formSelects.value("selcert");// $("input[name='selcert']").val();
                        // for (var i = 0; i < arrcertids.length; i++) {
                        //     objdata.certids[arrcertids[i].value] = arrcertids[i].value.name
                        // }
                        objdata.certids = certpage.$("input[name='selcert']").val();
                    }
                    var strenroll = objdata.needinfos[selv] || objdata.defaultneedinfos[selv];
                    if(objdata.defaultneedinfos[selv]){
                        arrneedinfo.push('<span class="select-txt">' + strenroll + '</span>');
                    }else{
                        arrneedinfo.push('<span class="select-txt">' + strenroll + '<i class="iconfont icon_close1" style="font-size: 0;" selv="' + selv + '"></i></span>');
                    }
                    objdata.selneedinfos[selv] = strenroll;
                });
                if(certpage.$("#certnames").is(":checked") && !objdata.certids){
                    parent.layer.msg("请选择证书名称！");
                    objdata.selneedinfos = {};
                    return;
                }
                if (arrneedinfo.length > 0) {
                    $("#divseledneedinfos").html(arrneedinfo.join('')).closest(".layui-form-item").show();
                    parent.layer.close(index);
                } else {
                    parent.layer.msg("请选择要提交的资料！");
                }
            }
        });
    });

    initDate();
    outCon.find("#noticecontent").off('click', ".qr-expert .layui-unselect").on("click", ".qr-expert .layui-unselect", function (ev) {//是否添加专家
        if ($(this).find("div").text() === "是") {//添加
            $("#divisnneedinfo").show();
            $("#divselexpert").show();//选择专家
        } else {
            $("#txtendtime").parent().parent().hide();
            $("#divselexpert").hide();//选择专家
            $("#expertnoticeinfo,#divexpertinfo").html('').show();
            objdata.expertype = null;//置空专家选择
                // $("#divisnneedinfo,#divneedinfos").hide();
            // $("#divseledneedinfos").html("").closest(".layui-form-item").hide();
            objdata.newNotice.isEnroll = 0;
            objdata.newNotice.enrolltime = "";
        }
        if(!objdata.expertype || objdata.expertype == 2 || $(this).find("div").text() === "否"){//objdata.expertype == 2治疗性专家
            $("input[name='qr-register']").each(function (){
                $(this).prop({"disabled": false});
            });
        } else {
            $("input[name='qr-register']").each(function (){
                $(this).prop({"checked": true, "disabled": true});
            });
        }
        form.render();
    }).off('click', '#btnselexpert').on('click', '#btnselexpert', function (){//选择专家
        parent.layer.open({
            type: 2,
            title: '选择专家',
            area: ['80%', '90%'], //宽高
            btnAlign: 'c',
            content: 'html/noticeselexpert.html?v=' + (Arg("v") || 1) + "&mid=" + Arg("mid") + "&type=" + Arg("type"),
            btn: ['确定', "取消"],
            yes: function (index, layero) {
                var expertwin = layero.find('iframe')[0].contentWindow;
                expertwin.saveEvent(function (expertinfo){
                    objdata.expertype = expertinfo.type;
                    if(objdata.expertype == 1){//讲座型专家
                        $("input[name='qr-register'][value='0']").prop("checked", true);
                        $("input[name='qr-register']").each(function (){
                            $(this).prop({"disabled": true});
                        });
                    // } else if(objdata.expertype == 2){
                    //     $("input[name='qr-register'][value='1']").prop("checked", true);
                    //     $("input[name='qr-register']").each(function (){
                    //         $(this).prop({"disabled": true});
                    //     });
                    } else {
                        $("input[name='qr-register']").each(function (){
                            $(this).prop({"disabled": false});
                        });
                    }
                    var objexperinfo = {
                        lecturestarttime: "",
                        lectureendtime: "",
                        lectureaddress: "", //讲座地点
                        enrolltime: "",  //截止报名日期
                        sittingtimestart: "", //讲座开始时间
                        sittingtimeend: "", //讲座结束时间
                        departmentid: "", //科室id
                        departnname: "", //科室名称
                        registerendtime: "" //预约挂号结束时间
                    };
                    $("#divexpertinfo").html(renderTemp(templete.expertinfo, expertinfo));
                    $("#expertnoticeinfo").html(renderTemp(templete["expertype" + expertinfo.type] || "", objexperinfo));
                    if($("input[name='qr-register']:checked").val() == 1){//允许挂号
                        $("#divregisterendtime").show();
                    } else {
                        $("#divregisterendtime").hide();
                        $("#txtregisterendtime").val('');
                    }
                    initDate();
                    form.render();
                    parent.layer.close(index);
                });
            }
        });
    }).off('click', "#btndepartmentid").on('click', "#btndepartmentid", function (){
        parent.layer.open({
            type: 2,
            title: '选择科室',
            area: ['500px', '80%'], //宽高
            btnAlign: 'c',
            content: 'html/noticeseldepart.html?v=' + (Arg("v") || 1) + "&mid=" + Arg("mid"),
            btn: ['确定', "取消"],
            yes: function (index, layero) {
                var departwin = layero.find('iframe')[0].contentWindow;
                var selnode = departwin.objdata.selnode;
                $("#divdepartmentidinfo").html(selnode.name).attr("departmentid", selnode.id);
                initDate();
                parent.layer.close(index);
                // expertwin.saveEvent(function (expertinfo){
                //     $("#divexpertinfo").html(renderTemp(templete.expertinfo, expertinfo));
                //     $("#expertnoticeinfo").html(templete["expertype" + expertinfo.type] || "");
                //     initDate();
                //     form.render();
                //     parent.layer.close(index);
                // });
            }
        });
    });
    $("#js-cover").click(function () {//封面
        jQuery.getparent().layer.open({
            type: 2,
            title: '选择图片',
            shadeClose: false,
            area: ['1100px', '90%'],
            content: 'plugin/cropper/uploadoss.html?v=' + (Arg("v") || 1) + "&Ratio=" + (180 / 180),
            success: function (layero, index) {
            },
            btn: ["确定", "取消"],
            yes: function (index, layero) { //或者使用btn1
                var w = layero.find('iframe')[0].contentWindow;
                var option = {
                    region: "oss-cn-beijing",
                    bucket: bucketName,
                    path: uploadPrefix + "/notice",
                };
                w.uploadFile(option, function (file) {
                    jQuery.getparent().layer.close(index);
                    // $('#imgheadurl').attr('src', ossPrefix + file.name + '?x-oss-process=image/resize,m_lfit,w_200,h_250');
                    // $("#txtheadurl").val(file.name);

                    var url = file.key;
                    objdata.newNotice.cover = url;
                    $("#js-cover").prev().prop("src", ossPrefix + url + objdata.strOssSize);
                    outCon.find(".js-phone-cover").prop("src", ossPrefix + url + objdata.strOssSize);
                });
            }
        });
    });

//    阅读
    $("#qr-mobile-detail").off("click").on("click", function (ev) {
        outCon.find(".qr-phone-show").eq(1).click();
    });
    $("#divseledneedinfos").off("click", ".icon_close1").on("click", ".icon_close1", function (){//删除上报资料的显示
        $(this).parent().remove();
        delete objdata.selneedinfos[$(this).attr("selv")];
    });
    //是否允许预约挂号:
    form.on('radio(qr-register)', function(){
        if ($(this).val() === "1") {
            $("#divregisterendtime").show();
            // objdata.newNotice.isnNeedinfo = 1;
        } else {
            $("#divregisterendtime").hide();
            $("#txtregisterendtime").val('');
            // objdata.newNotice.isnNeedinfo = 0;
        }
    });

}

/**
 * 切换发送对象
 */
function switchsendobj(totype){
    if(totype == "ey"){//托幼机构
        outCon.find(".js-choose-user").show().click();
        if(Arg("type") == 2){
            $("#noticecontent").html(templete.tuoyutem);
        }
        if(Arg("type") == 1 && Arg("comefrom") == "fuyou"){//普通通知没有报名等选项
            $("#noticecontent").html("");
        }
        $("#txt-js-notice-type").attr({"readonly": "readonly", "lay-verify": "required"});
        $(".js-from-depart").attr("lay-verify", "required");
        $(".js-attache-list,#notice-type-isnshow,#divsenddepart,#divsummary").show();
        initDate();
        // $("#divisnaddexpert").hide();
    }else if(totype == "p"){//小程序家长端
        outCon.find(".js-choose-user").hide();
        $("#txt-js-notice-type").removeAttr("readonly").attr({"lay-verify": ""});
        $(".js-from-depart").attr("lay-verify", "");
        $(".js-attache-list,#notice-type-isnshow,#divsummary").hide();
        $("#noticecontent").html(templete.parenttem);
        if(Arg("type") == 1 && Arg("comefrom") == "fuyou"){//普通通知不可以添加专家
            $("#divisnaddexpert").hide();
        }
    } else if(totype == "employee" || totype == "doctor"){//小程序职工端，小程序医生端
        outCon.find(".js-choose-user").hide();
        $("#txt-js-notice-type").removeAttr("readonly").attr("lay-verify", "");
        $(".js-from-depart").attr("lay-verify", "");
        $(".js-attache-list,#notice-type-isnshow,#divsummary").hide();//隐藏上传文件
        $("#noticecontent").html("");
    }
    form.render();
}
function initDate(){
    var curmoment = parent.getnextday(parent.objdata.curmoment.format('YYYY-MM-DD HH:mm:ss'), 30) + " 23:59:59";
    // var curdate = parent.getnextday(parent.objdata.curmoment.format('YYYY-MM-DD HH:mm:ss'), 1) + " 23:59:59";
    laydate.render({//    报名活动截止时间
        elem: '#txtendtime',
        type: "datetime",
        value: curmoment,
        done: function (value, date, endDate) {
            objdata.newNotice.enrolltime = value;
        }
    });
    laydate.render({//    讲座开始时间
        elem: '#txtlecturestarttime',
        type: "datetime",
        // value: curmoment,
        done: function (value, date, endDate) {
            objdata.newNotice.lecturestarttime = value;
        }
    });
    laydate.render({//    讲座结束时间
        elem: '#txtlectureendtime',
        type: "datetime",
        // value: curmoment,
        done: function (value, date, endDate) {
            objdata.newNotice.lectureendtime = value;
        }
    });

    laydate.render({//    坐诊开始时间
        elem: '#txtsittingtimestart',
        type: "datetime",
        // value: curmoment,
        done: function (value, date, endDate) {
            objdata.newNotice.sittingtimestart = value;
        }
    });
    laydate.render({//    坐诊结束时间
        elem: '#txtsittingtimeend',
        type: "datetime",
        // value: curmoment,
        done: function (value, date, endDate) {
            objdata.newNotice.sittingtimeend = value;
        }
    });
    laydate.render({//    讲座结束时间
        elem: '#txtregisterendtime',
        type: "datetime",
        done: function (value, date, endDate) {
            objdata.newNotice.registerendtime = value;
        }
    });
}

/**
 * 编辑转发时查询获取通知
 * @param noticeId
 * @param oneNotice
 * @returns {Promise<void>}
 */
function getOneNotice(noticeId, oneNotice) {
    if (!noticeId) {
        return;
    }
    objdata.curEditId = noticeId;
    objdata.status = "edit";
    objdata.objSelectUser = {};
    objdata.objSelectobj = {};
    return new Promise(function (resolve, reject) {
        var arrsm = ["notice.getNotice", noticeId];
        if(objdata.getdatafromypt){
            arrsm = ["notice.getNoticeFromYpt", noticeId];
        }
        $.sm(function (re, err) {
            if (!err && re) {
                resolve(re[0]);
            } else {
                parent.layer.msg("获取通知信息失败！")
            }
        }, arrsm);
    }).then(function (data) {
        if (!data) {
            return;
        }
        if(Arg("isforward")){//转发
            data.totype = $("#sel_totype").val();
        }else{
            outCon.find(".js-from-depart").val(data.fromdepart);
        }
        //指定发送对象类型
        objdata.preTotype = data.totype;
        // outCon.find("#sel_totype option[value='" + data.totype + "']").click();
        outCon.find("#sel_totype").val(data.totype);
        form.render();
        //指定通知类型
        objdata.newNotice.noticeType = data.noticetype;
        objdata.newNotice.totype = objdata.newNotice.preTotype = data.totype;
        if(data.totype == "ey"){
            $("#noticecontent").html(templete.tuoyutem);
        } else if(data.totype == "p"){//给家长发送通知
            $("#noticecontent").html(templete.parenttem);//
            $("input[name='qr-expert'][value='" + data.isnaddexpert + "']").prop("checked", true);
            if(data.isnaddexpert == 1){//需要添加专家
                $("#divselexpert").show();//显示选中专家
                if(data.expertfrom == "f"){//自建库
                    $.sm(function (re, err, objre){
                        if(err){
                            parent.layer.msg(err);
                        }else{
                            objdata.expertype = objre.type;
                            objre.expertype = objre.type;
                            objre.expertfrom = data.expertfrom;
                            objre.expertypename = objdata.objexpertype[objre.type];
                            objre.img = (islocal ? 'https://testshenyangfuyou.cpmch.com:553/' + objre.img : ossPrefix + objre.img);// + objre.img;
                            $("#divexpertinfo").html(renderTemp(templete.expertinfo, objre));
                            $("#expertnoticeinfo").html(renderTemp(templete["expertype" + objre.type] || "", data));
                        }
                    }, ['xjexpert.detail', $.msgwhere({id:[data.expertid]})], null, null, {async: false});
                } else if(data.expertfrom == "p"){//童帮人库
                    $.sm(function (re, err, objre){
                        if(err){
                            parent.layer.msg(err);
                        }else{
                            objdata.expertype = objre.type;
                            objre.expertype = objre.type;
                            objre.expertfrom = data.expertfrom;
                            objre.expertypename = objdata.objexpertype[objre.type];
                            objre.img = tbptossPrefix + objre.img;
                            $("#divexpertinfo").html(renderTemp(templete.expertinfo, objre));
                            $("#expertnoticeinfo").html(renderTemp(templete["expertype" + objre.type] || "", data));
                        }
                    }, ['xjexpert.detail', $.msgwhere({id:[data.expertid]})], "w", "", {rpc: 'tbpt', async: false});
                }

                $("input[name='qr-register'][value='" + data.isnregister + "']").prop('checked', true);//是否允许预约挂号
                if(data.isnregister == "1" && data.expertype == "2"){//允许预约挂号且是治疗性专家
                    $("#divregisterendtime").show();
                }else{
                    $("#divregisterendtime").hide();
                    $("#txtregisterendtime").val('');
                }
            }
            $("#txt-js-notice-type").removeAttr("readonly").attr({"lay-verify": ""});
            $(".js-from-depart").attr("lay-verify", "");
            $(".js-attache-list,#notice-type-isnshow,#divsenddepart,#divsummary").hide();
        }else if(data.totype == "employee" || data.totype == "doctor"){
            outCon.find(".js-choose-user").hide();
            $("#txt-js-notice-type").removeAttr("readonly").attr({"lay-verify": ""});
            $(".js-from-depart").attr("lay-verify", "");
            $(".js-attache-list,#notice-type-isnshow,#divsenddepart,#divsummary").hide();
            $("#noticecontent").html("");
        }
        // form.render();
        if(!Arg("isforward")){//不是转发的时候赋值
            outCon.find("#txt-js-notice-type").val(data.typename);
        }
        //    通知发文单位
        outCon.find("input[name='qr-enroll'][value='" + data.isenroll + "']").prop("checked", true);
        if (data.isenroll == "1") {//允许报名
            $("#txtendtime").val(data.enrolltime);
            $("#txtendtime").parent().parent().show();
            $("#divisnneedinfo").show();
            objdata.newNotice.isEnroll = 1;
            objdata.newNotice.enrolltime = $("#txtendtime").val();
        } else {
            $("#txtendtime").parent().parent().hide();
            $("#divisnneedinfo,#divneedinfos").hide();
            objdata.newNotice.isEnroll = 0;
            objdata.newNotice.enrolltime = "";
        }
        outCon.find("input[name='qr-info'][value='" + data.isnneedinfo + "']").prop("checked", true);
        if (data.isnneedinfo == "1") {//
            $("#divneedinfos,#divisnprocess").show();
            var arrneedinfo = [];
            objdata.selneedinfos = {};
            var needinfos = JSON.parse(data.needinfos || '{}');
            for (var k in needinfos) {
                var strenroll = objdata.needinfos[k] || objdata.defaultneedinfos[k];
                if(objdata.defaultneedinfos[k]){
                    arrneedinfo.push('<span class="select-txt">' + strenroll + '</span>');
                }else{
                    arrneedinfo.push('<span class="select-txt">' + strenroll + '<i class="iconfont icon_close1" style="font-size: 0;" selv="' + k + '"></i></span>');
                }
                objdata.selneedinfos[k] = strenroll;
            }
            if (arrneedinfo.length > 0) {
                $("#divseledneedinfos").html(arrneedinfo.join('')).closest(".layui-form-item").show();
                if(needinfos["certnames"] && data.certids){
                    objdata.certids = data.certids;
                }
            } else {
                parent.layer.msg("请选择要提交的资料！");
            }
        } else {
            $("#divneedinfos,#divisnprocess").hide();
        }
        var arrsm = [["notice.getNoticeDetail", noticeId], ["notice.getAttache", noticeId]];
        if(Arg("isforward")){//转发时默认为需要自己审核
            outCon.find("input[name='qr-process'][value='" + data.isnprocess + "']").prop("checked", true);
        }
        if(objdata.getdatafromypt){
            arrsm = [["notice.getNoticeDetailFromYpt", noticeId], ["notice.getAttacheFromYpt", noticeId]];
        }
        initDate();
        form.render();
        //    获取通知详情、发送具体人及附件
        return new Promise(function (resolve, reject) {
            $.sm(function (re, err) {
                if (!err && re) {
                    resolve(re);
                } else {
                    parent.layer.msg("获取通知相关信息失败！")
                }
            }, arrsm);
        }).then(function (data2) {
            var objDetail = data2[0][0],
                // arrUsers = data2[1],
                arrAttaches = data2[1];
            //    初始化详情
            outCon.find(".js-title").val(objDetail.title);
            outCon.find(".js-phone-title").text(objDetail.title);
            objdata.newNotice.title = objDetail.title;
            objdata.ue.ready(function () {
                UE.getEditor('js-editor').setContent($.DecodeSpChar(objDetail.content));
            });
            objdata.ue.ready(function () {
                UE.getEditor('js-editor').setContent($.DecodeSpChar(objDetail.content));
            });
            $("#js-cover").prev().prop("src", (objDetail.cover.indexOf('notice_cover.jpg') > -1 ? "" : ossPrefix) + objDetail.cover + objdata.strOssSize);
            objdata.newNotice.cover = objDetail.cover;
            outCon.find(".js-phone-cover").prop("src", objDetail.cover + objdata.strOssSize);
            outCon.find(".js-summary").val(objDetail.summary);
            outCon.find(".js-phone-summary").text(objDetail.summary);
            if(!Arg("isforward") && data.totype != "p" && data.totype != "employee" && data.totype != "doctor"){//非转发的才赋值发送对象,小程序发送不用赋值发送对象
                var arrarea = data.receiveareacode.split(",");
                var arrareaname = [];
                objdata.objSelectUser.strareainfo = data.receiveareacode;
                for (var i = 0; i < arrarea.length; i++) {
                    arrareaname.push(parent.getAreaNamebyCode(arrarea[i]));
                }
                var receiveobj = JSON.parse(data.receiveobj || '{}');
                objdata.objSelectUser.receiveobj = receiveobj;
                var strreceiveobj = "";
                var arrsole = [];
                var arrtotype = objdata.objarr[objdata.newNotice.totype];
                for (var j = 0; j < arrtotype.length; j++) {
                    var strtotype = arrtotype[j];
                    var objroletype = objdata.totypename[strtotype];
                    for (var k in receiveobj) {
                        if(objroletype[k]){
                            var arrrolename = [];
                            var items = receiveobj[k];
                            strreceiveobj += objroletype[k];
                            for (var i = 0; i < items.length; i++) {
                                arrsole.push(items[i][0]);
                                arrrolename.push(items[i][1])
                            }
                            if (arrrolename.length > 0) {
                                strreceiveobj += "：" + arrrolename.join(',') + "，";
                            }
                        }
                    }

                }
                objdata.objSelectUser.arrsole = arrsole;
                outCon.find("#spselareainfo").html("已选角色（" + strreceiveobj.substring(0, strreceiveobj.length - 1) + ")");
            }
            //    初始化附件
            if (arrAttaches.length > 0) {
                outCon.find("#filllist").html("");
                for(var i = 0; i < arrAttaches.length; i++){
                    var curAttache = arrAttaches[i],
                        regExt = /\w+\.(\w+)$/i,
                        curExt = regExt.exec(curAttache.name) && regExt.exec(curAttache.name)[1] || "txt",
                        param = {
                            fileId : "",
                            attachid : curAttache.id,
                            fileName : curAttache.name,
                            filePath : curAttache.path,
                            icon : oneNotice.attacheIcon.baseUrl + oneNotice.attacheIcon[curExt],
                            fileSize : curAttache.size,
                            percent : ""
                        },
                        strTem = renderTemp(templete.attacheTem,param);
                    outCon.find("#filllist").append(strTem);
                }
            }
        });
    });
}

/**
 * 发布通知公告
 * @param sendStatus
 * @returns {*}
 */
function publishNotice(sendStatus) {
    var objNewNotice = objdata.newNotice;
    // var objNewNotice = objdata.newNotice;
    objNewNotice.content = UE.getEditor('js-editor').getContent();
    objNewNotice.txtContent = UE.getEditor('js-editor').getContentTxt();
    objNewNotice.summary = outCon.find(".js-summary").encodeval() || objNewNotice.txtContent.substring(0, 55);
    var totype = $("#sel_totype").val();
    if(!totype){
        return parent.layer.msg("请选择发送对象");
    }
    var arrareainfo = [];
    var receiveobj = "";
    var strareainfo = "";
    var noticetype = outCon.find("#txt-js-notice-type").val() || "";//通知类型
    var fromDepart = outCon.find(".js-from-depart").encodeval();//单位
    if(totype == "ey"){//托幼机构
        if(!objdata.objSelectUser){
            parent.layer.closeAll("loading");
            parent.layer.msg("请选择发送对象");
            objdata.isAdding = false;
            return;
        }
        strareainfo = objdata.objSelectUser.strareainfo;
        receiveobj = objdata.objSelectUser.receiveobj;
        if (!objNewNotice.totype || !receiveobj || isEmpty(receiveobj) || !strareainfo) {//发送对象
            parent.layer.closeAll("loading");
            parent.layer.msg("请选择发送对象");
            objdata.isAdding = false;
            return;
        }
        arrareainfo = strareainfo.split(",");
        if (!noticetype) {
            parent.layer.closeAll("loading");
            parent.layer.msg("请选择通知公告类型");
            objdata.isAdding = false;
            return;
        }
        if (fromDepart.length > 30) {
            parent.layer.closeAll("loading");
            parent.layer.msg("发文单位超出30字");
            objdata.isAdding = false;
            return;
        }
    }else if(totype == "p"){
        strareainfo = parent.objdata.my.areacode && parent.objdata.my.areacode.substring(0, 6);
        arrareainfo = strareainfo.split(",");
    }else if(totype == "employee" || totype == "doctor"){
        strareainfo = parent.objdata.my.areacode && parent.objdata.my.areacode.substring(0, 6);
        arrareainfo = strareainfo.split(",");
    }
    if (!objNewNotice.title || objNewNotice.title.length > 50) {//标题
        parent.layer.closeAll("loading");
        parent.layer.msg("请输入不超过50字的标题！");
        objdata.isAdding = false;
        return;
    }
    if (!objNewNotice.content) {//正文内容
        parent.layer.closeAll("loading");
        parent.layer.msg("请输入通知正文内容！");
        objdata.isAdding = false;
        return;
    }
    if (outCon.find(".js-summary").encodeval().length > 100) {//摘要
        parent.layer.closeAll("loading");
        parent.layer.msg("摘要超出100字");
        objdata.isAdding = false;
        return;
    }
    if (!objNewNotice.cover) {
        objNewNotice.cover = '../images/notice_cover.jpg';
    }
    //查看附件是否正在上传中
    function isUploading() {
        var attacheLists = outCon.find("#filllist").children();
        if (attacheLists.length === 0) {
            return false;
        }
        for (var i = 0; i < attacheLists.length; i++) {
            var cur = attacheLists.eq(i);
            if (cur.find(".js-del-attache").text().indexOf("%") > -1) {
                return true;
            }
        }
        return false;
    }

    if (isUploading()) {
        parent.layer.closeAll("loading");
        parent.layer.msg("附件上传中，请稍后再试");
        objdata.isAdding = false;
        return;
    }
    // var role = objNewNotice.totype === "2" ? objdata.objSelectUser.role : 0,
    var isEnroll = objNewNotice.isEnroll || 0;
    var enrolltime = objNewNotice.enrolltime || '';
    if(isEnroll && !enrolltime){
        return parent.layer.msg("请选择报名截至时间！");
    }
    var isnneedinfo = $("input[name='qr-info']:checked").val() || 0//报名是否需要提交资料
        , isnprocess = $("input[name='qr-process']:checked").val() || 0;//报名资料是否需要审核:
    if (isnneedinfo == 1) {//报名是否需要提交资料
        if(isEmpty(objdata.selneedinfos)){
            objdata.isAdding = false;
            parent.layer.closeAll("loading");
            return parent.layer.msg("请选择需要提交的报名材料！");
        }
    }else{
        isnprocess = 0;//不需要提交资料时不需要审核
    }
    var isnaddexpert = $("input[name='qr-expert']:checked").val();//是否添加专家
    var expertid = $(".divselexpertinfo").attr("expertid");//专家id
    var lecturestarttime = $("#txtlecturestarttime").val();//讲座开始时间
    var lectureendtime = $("#txtlectureendtime").val();//讲座结束时间
    var lectureaddress = $.trim($("#txtlectureaddress").val());//讲座地点
    var sittingtimestart = $("#txtsittingtimestart").val();//坐诊开始时间
    var sittingtimeend = $("#txtsittingtimeend").val();//坐诊结束时间
    var departmentid = $("#divdepartmentidinfo").attr("departmentid");//所属科室id
    var departnname = $("#divdepartmentidinfo").html() || '';//所属科室
    var isnregister = $("input[name='qr-register']:checked").val();//是否允许预约挂号 0 不允许 1 允许
    var registerendtime = $("#txtregisterendtime").val();//预约挂号截止时间
    var expertype = objdata.expertype;//专家类型 1讲座型中医儿科专家 2治疗型中医儿科专家
    var expertfrom = $(".divselexpertinfo").attr("expertfrom") || '';//专家来源,f自建库，p童帮库
    if(isnaddexpert === "1"){//添加了专家
        if(!expertid){
            parent.layer.closeAll("loading");
            return parent.layer.msg("请选择专家！");
        }
        if(!expertfrom){
            parent.layer.closeAll("loading");
            return parent.layer.msg("选中专家有误，请重新选择！");
        }
        if(expertype == 1){//1讲座型中医儿科专家
            if(!lecturestarttime || !lectureendtime){
                parent.layer.closeAll("loading");
                return parent.layer.msg("请选择讲座时间！");
            }
            if(!lectureaddress){
                parent.layer.closeAll("loading");
                return parent.layer.msg("请输入讲座地点！");
            }
        }else if(expertype == 2){//2治疗型中医儿科专家
            if(!sittingtimestart || !sittingtimeend){
                parent.layer.closeAll("loading");
                return parent.layer.msg("请选择坐诊时间！");
            }
            if(!departmentid || !departnname){
                parent.layer.closeAll("loading");
                return parent.layer.msg("请选择所属科室！");
            }
            if(isnregister == 1 && !registerendtime){//允许预约挂号
                parent.layer.closeAll("loading");
                return parent.layer.msg("请选择预约挂号截至日期！");
            }
        }
    }
    objdata.isAdding = true;
    // var arrsm = ["notice.newNotice", objNewNotice.noticeType, objNewNotice.totype, 'null', 'null', objdata.myid, fromDepart, sendStatus, isEnroll, enrolltime, isnneedinfo, JSON.stringify(objdata.selneedinfos), isnprocess, arrareainfo.length, parent.objdata.my.roletype, parent.objdata.my.username, parent.objdata.my.fuyouid, parent.objdata.my.fuyouname, parent.objdata.my.organid || "null", parent.objdata.my.organname || "null", strareainfo, JSON.stringify(receiveobj), Arg("type"), objdata.certids, objdata.curEditId || 0];
    var fromnoticeid = "", cureditnoticeid = objdata.curEditId || 0;
    if(Arg("isforward")){//转发
        fromnoticeid = objdata.curEditId;
        cureditnoticeid = 0;
        // arrsm = ["notice.newNotice", objNewNotice.noticeType, objNewNotice.totype, 'null', 'null', objdata.myid, fromDepart, sendStatus, isEnroll, enrolltime, isnneedinfo, JSON.stringify(objdata.selneedinfos), isnprocess, arrareainfo.length, parent.objdata.my.roletype, parent.objdata.my.username, parent.objdata.my.fuyouid, parent.objdata.my.fuyouname, parent.objdata.my.organid || "null", parent.objdata.my.organname || "null", strareainfo, JSON.stringify(receiveobj), Arg("type"), objdata.certids, objdata.curEditId || 0];
    }

    new Promise(function (resolve, reject) {
        var objcol = {
            noticetype: objNewNotice.noticeType,
            totype: totype,
            fromid: objdata.myid,
            fromdepart: fromDepart,
            sendstatus: sendStatus,
            isenroll: isEnroll,
            enrolltime: enrolltime,
            isnneedinfo: isnneedinfo,
            needinfos: objdata.selneedinfos,
            isnprocess: isnprocess,
            toareanum: arrareainfo.length || 'null',
            sendtype: parent.objdata.my.roletype,
            fromname: parent.objdata.my.username,
            fuyouid: parent.objdata.my.fuyouid,
            fuyouname: parent.objdata.my.fuyouname,
            organid: parent.objdata.my.organid || "null",
            organname: parent.objdata.my.organname || "null",
            receiveareacode: strareainfo,
            receiveobj: receiveobj ? receiveobj : "",
            noticeflag: Arg("type"),
            certids: objdata.certids,
            noticeid: fromnoticeid || 0,//转发通知的id
            isnaddexpert: isnaddexpert || 'null',
            expertid: expertid || 'null',
            lecturestarttime: lecturestarttime || 'null',
            lectureendtime: lectureendtime || 'null',
            lectureaddress: lectureaddress,
            sittingtimestart: sittingtimestart || 'null',
            sittingtimeend: sittingtimeend || 'null',
            departmentid: departmentid || 'null',
            departnname: departnname,
            isnregister: isnregister || 'null',
            registerendtime: registerendtime || 'null',
            expertype: expertype || 'null',
            expertfrom: expertfrom
        }
        var arrsm = ["notice.newNotice", JSON.stringify(objcol), $.msgwhere({"cureditnoticeid": [cureditnoticeid]})];
        if(objdata.getdatafromypt){
            arrsm = ["notice.newNoticeFromYpt", JSON.stringify(objcol), $.msgwhere({"cureditnoticeid": [cureditnoticeid]})];
        }
        $.sm(function (re, err) {
            if (!err && re) {
                resolve(re);
            } else {
                objdata.isAdding = false;
                parent.layer.closeAll("loading");
                parent.layer.msg("发送通知失败")
            }
        }, arrsm);//objNewNotice.noticeType, objNewNotice.totype, 'null', 'null', objdata.myid, fromDepart, sendStatus, isEnroll, enrolltime, isnneedinfo, JSON.stringify(objdata.selneedinfos), isnprocess, arrareainfo.length || 'null', parent.objdata.my.roletype, parent.objdata.my.username, parent.objdata.my.fuyouid, parent.objdata.my.fuyouname, parent.objdata.my.organid || "null", parent.objdata.my.organname || "null", strareainfo, receiveobj ? JSON.stringify(receiveobj) : "", Arg("type"), objdata.certids, fromnoticeid || 0, isnaddexpert,expertid,lecturestarttime,lectureendtime,lectureaddress,sittingtimestart,sittingtimeend,departmentid,departnname,isnregister,registerendtime,expertype,expertfrom, cureditnoticeid
    }).then(function (noticeId) {
        //插入通知详情
        var addDetail = function (noticeId) {
                return new Promise(function (resolve, reject) {
                    var arrsm = ["notice.newNoticeDetail", noticeId, $.EncodeSpChar(objNewNotice.title, true), 8, $.EncodeSpChar(objNewNotice.content, true), $.EncodeSpChar(objNewNotice.summary, true), objNewNotice.cover || '', objNewNotice.cover || ''];
                    if(objdata.getdatafromypt){
                        arrsm = ["notice.newNoticeDetailFromYpt", noticeId, $.EncodeSpChar(objNewNotice.title, true), 8, $.EncodeSpChar(objNewNotice.content, true), $.EncodeSpChar(objNewNotice.summary, true), objNewNotice.cover || '', objNewNotice.cover || ''];
                    }
                    $.sm(function (re, err) {
                        if (!err && re) {
                            resolve();
                        } else {
                            parent.layer.closeAll("loading");
                            objdata.isAdding = false;
                            parent.layer.msg("发送通知失败");
                            return new Promise.reject(noticeId);
                        }
                    }, arrsm);
                });
            },
            //插入通知要发送的人
            addReceive = function (noticeId) {
                if (totype === "p" || totype === "employee" || totype === "doctor" || (objdata.objSelectUser && objdata.objSelectUser.arrsole.length > 0)) {//发送小程序无需选择角色
                    var objpushmsg = null;
                    if(objdata.getdatafromypt && (totype === "p" || totype === "employee" || totype === "doctor")){//
                        var thing2 = objdata.expertype == 1 ? "专家讲座" : objdata.expertype == 2 ? "专家治疗" : "通知";
                        var msgtime = jQuery.getparent().objdata.moment().format("YYYY-MM-DD HH:mm:ss");
                        objpushmsg = getmsgobj("xcxcpmch", 1, "", {
                            "time3": {
                                "value": msgtime
                            },
                            "thing2": {
                                "value": thing2
                            }
                        });
                    }
                    var arrareainfo2 = [];
                    for (var i = 0; i < arrareainfo.length; i++) {
                        arrareainfo2.push([arrareainfo[i], parent.getAreaNamebyCode(arrareainfo[i])]);
                    }
                    var objreceiveinfo = {
                        noticeid: noticeId//通知id
                        // , strareacode: objdata.objSelectUser.strareacode//接收地区
                        , totype: totype || objdata.newNotice.totype//接收对象e.妇幼保健院，y.托育机构，ey.妇幼保健院和托育机构
                        , arrtype: objdata.objarr[totype ||　objdata.newNotice.totype]
                        , strsole: objdata.objSelectUser && objdata.objSelectUser.arrsole && objdata.objSelectUser.arrsole.join(',') || ""//发送到妇幼或托幼机构的角色
                        , arrareainfo: arrareainfo2//地区信息
                        , roletype: jQuery.getparent().objdata.my.roletype
                        , organid: jQuery.getparent().objdata.my.organid
                        , msgtype: objdata.expertype//专家类型
                        , noticetitle: objdata.newNotice.title || ""
                        // , fromnoticeid: fromnoticeid
                        // , isforward: Arg("isforward")
                    };
                    if(objpushmsg) objreceiveinfo.objpushmsg = objpushmsg;//发送小程序提醒使用
                    return new Promise(function (resolve, reject) {
                        $.sm(function (re, err) {
                            if (!err && re) {
                                resolve();
                            } else {
                                parent.layer.closeAll("loading");
                                objdata.isAdding = false;
                                parent.layer.msg("发送通知失败");
                                return new Promise.reject(noticeId);
                            }
                        }, ['notice.newReceiveNotice', JSON.stringify(objreceiveinfo)], "", "", {route: objdata.getdatafromypt ? "fyyypt" : ""});
                    });
                } else {
                    parent.layer.closeAll("loading");
                    objdata.isAdding = false;
                    parent.layer.msg("请选择角色");
                    return new Promise.reject(noticeId);
                }
            },

            //插入通知附件
            addAttache = function (noticeId) {
                var attacheLists = outCon.find("#filllist").children();
                if (attacheLists.length === 0) {
                    return new Promise(function (resolve, reject) {
                        setTimeout(function () {
                            resolve();
                        }, 0)
                    });
                }
                var arrSm = [];
                for (var i = 0; i < attacheLists.length; i++) {
                    var curAttache = attacheLists.eq(i),
                        path = curAttache.attr("data-path"),
                        attachid = Arg("isforward") ? 0 : curAttache.attr("data-attachid");//转发时存储新的
                    // if(path){
                    var tmpSm = ["notice.newAttache"],
                        name = curAttache.find(".layui-txt-tit")[0] && curAttache.find(".layui-txt-tit")[0].childNodes[0].nodeValue || '',
                        size = curAttache.find(".layui-txt-tit label").text() && curAttache.find(".layui-txt-tit label").text().replace(/（([\w\d\.]+)）/, function (pattern, $0) {
                            return $0;
                        }) || '';
                    if(objdata.getdatafromypt){
                        tmpSm = ["notice.newAttacheFromYpt"];
                    }
                    tmpSm.push(noticeId);
                    tmpSm.push(1);
                    tmpSm.push(path);
                    tmpSm.push(name);
                    tmpSm.push(size);
                    tmpSm.push(attachid || 0);
                    arrSm.push(tmpSm);
                    // }
                }
                return new Promise(function (resolve, reject) {
                    if (arrSm.length > 0) {
                        $.sm(function (re, err) {
                            if (!err && re) {
                                resolve();
                            } else {
                                parent.layer.closeAll("loading");
                                objdata.isAdding = false;
                                parent.layer.msg("发送通知失败");
                                return new Promise.reject(noticeId);
                            }
                        }, arrSm);
                    }
                });
            };
        if (sendStatus === 1) {//发布
            Promise.all([addDetail(noticeId), addReceive(noticeId), addAttache(noticeId)])
                .then(function () {
                    publishback(sendStatus, noticeId)
                }).catch(function (noticeId) {
                    var arrsm = ["notice.delNotice", noticeId];
                if(objdata.getdatafromypt){
                    arrsm = ["notice.delNoticeFromYpt", noticeId];
                }
                $.sm(function (re, err) {
                    if (!(!err && re)) {
                        parent.layer.msg("发送通知失败");
                        parent.layer.closeAll("loading");
                    }
                }, arrsm);
            });
        } else {
            Promise.all([addDetail(noticeId), addAttache(noticeId)])
                .then(function () {
                    publishback(sendStatus, noticeId)
                }).catch(function (noticeId) {
                var arrsm = ["notice.delNotice", noticeId];
                if(objdata.getdatafromypt){
                    arrsm = ["notice.delNoticeFromYpt", noticeId];
                }
                $.sm(function (re, err) {
                    if (!(!err && re)) {
                        parent.layer.msg("发送通知失败");
                        parent.layer.closeAll("loading");
                    }
                }, arrsm);
            });
        }
    });
}

function publishback(sendStatus, noticeId){
    var tips = (Arg("isforward") ? "转发成功" : sendStatus === 1 ? "发布成功" : "已保存");
    parent.layer.msg(tips);
    if (sendStatus === 1) {
        setTimeout(function () {
            window.location.reload();
            objdata.noticeList = "undefined";
        }, 500);
    } else {
        //处理保存后的列表
        objdata.curEditId = noticeId;
        objdata.saveHasCli = false;
        objdata.noticeCache.saved = [];
    }
    //刷新底部
    var win = parent.$("#win_" + Arg("mid")).find("iframe")[0].contentWindow;
    var idx = sendStatus === 1 ? 0 : 2;
    if(win.$("#js-saved .noti-content[data-id='" + noticeId + "']").length > 0){
        win.$("#js-saved .noti-content[data-id='" + noticeId + "']").remove();
    }
    if(Arg("isforward") && win.$("#js-receive .noti-content[data-id='" + Arg("noticeid") + "']").length > 0){//转发
        win.$("#js-receive .noti-content[data-id='" + Arg("noticeid") + "']").find(".qr-delay-enroll-forward").hide();
    }
    win.$(".layui-tab-title li").eq(idx).trigger('click');
    win && win.reloadPage && win.reloadPage();
    objdata.isAdding = false;
    parent.layer.closeAll("loading");
    $("#btncancel").trigger("click");
}
//新建通知公告
function NewNotice() {
    this.noticeType = objdata.noticeType || 1; //消息类型 默认1 ：活动公告
    this.totype = objdata.noticeType || "y"; //发送给谁 默认 kindergarten ：幼儿园
    this.fromid = parent.objdata.my.id; //发布通知者id
}

NewNotice.prototype.attacheIcon = {
    baseUrl: "../images/",
    png: "txt_img1.png",
    jpg: "txt_img1.png",
    doc: "txt_img2.png",
    docx: "txt_img2.png",
    zip: "txt_img3.png",
    txt: "txt_img5.png",
    xlsx: "txt_img6.png",
    xls: "txt_img6.png",
    pdf: "txt_img7.png",
    ppt: "txt_img10.png",
    pptx: "txt_img10.png"
};
/**
 * 初始化新建通知(发文单位)
 */
NewNotice.prototype.init = function () {
//    手机端显示通知时间
    outCon.find('.js-phone-date').text(getNowDate("MM-DD", null, "zh"));
//    填写默认发文单位 处理接收的通知中未读的条数
    var objwhere = {};
    objwhere.fuyouid = [parent.objdata.my.fuyouid];
    objwhere.noticeflag = [Arg("type")];
    var arrsm = [["notice.getLastNotice", objdata.myid, $.msgwhere(objwhere)]];
    if(objdata.getdatafromypt){
        arrsm = [["notice.getLastNoticeFromYpt", objdata.myid, $.msgwhere(objwhere)]];
    }
    $.sm(function (re, err) {
        if (!err && re) {
            if (re[0].length) {
                outCon.find(".js-from-depart").val(re[0][0].fromdepart);
            }
            // var num = Number(re[1][0][0]);
            // if (num) {
            //     $("#qr-tab-receive").attr("data-num", num).html('已接收<span class="layui-badge">' + (num > 99 ? "99+" : num) + '</span>');
            // }
        } else {
            parent.layer.msg("发送消息失败");
        }
    }, arrsm);//, ["notice.countUnreadNum", parent.objdata.my.id, parent.objdata.my.fuyouid]
};
//上传附件
NewNotice.prototype.upAttachement = function (option) {
    var _this = this;
    var pick = option.$_ele || 'js-picker'
    var config = {
        id: pick,
        accept: {
            title: option.title || "",
            extensions: option.extensions || "",
            mimeTypes: option.mimeTypes || ""
        },
        fileSingleSizeLimit: objdata.attacheMaxSize, //限制单个文件大小
        fileNumLimit: objdata.attacheMaxFileNum,
        duplicate: false, //不允许文件重复上传
        beforeCb: function(obj, data, headers){
            objdata.curPicker = pick;
            obj.filepath = data.key;
            objdata.upFile[data.id] = {
                fileName: data.name,
                curExt: obj.file.ext,
                curSize: obj.blob.size
            };
            if (objdata.curPicker !== "#js-cover") {
                var param = {
                        fileId: data.id,
                        attachid: "",
                        fileName: data.name,
                        filePath: "",
                        icon: _this.attacheIcon.baseUrl + (_this.attacheIcon[obj.file.ext] || _this.attacheIcon.txt),
                        fileSize: WebUploader.Base.formatSize(obj.blob.size),
                        percent: "0% "
                    },
                    strTem = renderTemp(templete.attacheTem, param),
                    attacheList = outCon.find("#filllist");
                attacheList.append(strTem);
            }
        },
        progressCb: function(file, percentage){
            var fileId = file.id,
                DomFile = outCon.find('#filllist dl[data-file-id="' + fileId + '"]');
            if (!DomFile.length) {
                return;
            }
            var percent = Math.floor(percentage * 100);
            DomFile.find("a").text(percent + "% 删除");
        },
        successCb: function(file, ret){
            var path = ret.key,
                url = ossPrefix + path;
            if (pick === "#js-cover") {
                //    封面图
                objdata.newNotice.cover = path;
                $("#js-cover").prev().prop("src", url + objdata.strOssSize);
                outCon.find(".js-phone-cover").prop("src", url + objdata.strOssSize);
            } else {
                //    附件
                objdata.attacheCount++;
                var fileId = file.id,
                    DomFile = outCon.find('#filllist dl[data-file-id="' + fileId + '"]');
                if (!DomFile.length) {
                    return;
                }
                DomFile.attr("data-path", path).find("a").text("删除");
            }
        }
    }
    return inituploadUtil(config, uploadPrefix + '/notice');
};
//初始化富文本编辑器
NewNotice.prototype.initUEditor = function () {
    objdata.ue = UE.getEditor('js-editor', {
        toolbars: [
            ['fullscreen',
                'undo',
                'redo',
                'bold',
                'italic',
                'underline',
                'forecolor',
                'fontfamily',
                'fontsize',
                'insertorderedlist',
                'insertunorderedlist',
                'justifyleft',
                'justifycenter'
            ]
        ],
        autoHeightEnabled: true,
        autoFloatEnabled: true,
        elementPathEnabled: false,
        // serverUrl: $.projectpath + "/ueditorController?upRePath=" + uploadPrefix + objdata.upfile_defaltdir
        serverUrl: $.projectpath + '/ueditorController?upRePath=' + uploadPrefix + objdata.upfile_defaltdir + (islocal ? "&location=local" : "") + $.getAuthParam()
    });
    objdata.ue.ready(function () {
        //    正文内容
        var editor = $("#js-editor").find("iframe")[0].contentWindow.document.body;
        $(editor).on("focusout", function (ev) {
            outCon.find(".js-phone-content").html(UE.getEditor('js-editor').getContent());
        });
    });
};

function calculate_object_name(filename, type) {
    if (type && type == 'local_name') {
        return "${filename}";
    } else {//随机生成文件名
        var chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';
        var maxPos = chars.length;
        var random_string = '';
        for (var i = 0; i < 10; i++) {
            random_string += chars.charAt(Math.floor(Math.random() * maxPos));
        }
        var pos = filename.lastIndexOf('.');
        var suffix = '';
        if (pos != -1) {
            suffix = filename.substring(pos);
        }
        return random_string + new Date().getTime() + suffix;
    }
}

//签名是否超时
function signatureIsTimeOut() {
    if ((+new Date() - objdata.sigTime) > 25 * 1000) {
        return true;
    }
    return false;
}

/**
 * 获得当前指定格式日期
 * @param type{String} 指定返回格式 "YY-MM-DD HH:MM:SS"
 * @param time {Date | ""} 格式化时间对象，不传默认为当前系统时间
 * @param concatType {"zh"|null} 拼接格式 zh:年月日时分秒
 * @returns {String} 返回指定格式的日期字符串 "2017-07-19 15:21:46"
 */
function getNowDate(type, time, concatType) {
    var date = time || new Date(),
        year = date.getFullYear(),
        month = date.getMonth() + 1,
        day = date.getDate(),
        hour = date.getHours(),
        minute = date.getMinutes(),
        second = date.getSeconds(),
        week = "日一二三四五六".charAt(date.getDay()),
        concatChar = "-- ::";
    if (concatType) {
        concatChar = "年月日时分秒";
    }
    switch (type) {
        case "YY-MM-DD HH:MM:SS" :
            return year + concatChar.charAt(0) + add0(month) + concatChar.charAt(1) + add0(day) + concatChar.charAt(2) + add0(hour) + concatChar.charAt(3) + add0(minute) + concatChar.charAt(4) + add0(second) + (concatChar.charAt(5) || "");
        case "YY-MM-DD" :
            return year + concatChar.charAt(0) + add0(month) + concatChar.charAt(1) + add0(day) + concatChar.charAt(2);
        case "MM-DD" :
            return add0(month) + concatChar.charAt(1) + add0(day) + concatChar.charAt(2);
    }
}

function add0(val) {
    var reval = val < 10 ? "0" + val : val;
    return reval;
}

//页面点击刷新时，处理UI层初始化样式
function reloadHandleUI() {
//    是否允许报名初始化到不允许
    outCon.find(".qr-enroll .layui-unselect").eq(0).click();
}

/*
 功能：判断对象是否为空
 */
function isEmpty(obj) {
    for (var name in obj) {
        return false;
    }
    return true;
}