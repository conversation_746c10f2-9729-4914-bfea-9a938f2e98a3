/*大数据总览*/
body {
	background: #0B3372 url(../images/bigdata/data_topbg.jpg)center center no-repeat;
	background-size: cover;
}
.bigdata-title{vertical-align: top;padding: 0 130px;height:0.82rem;line-height: 0.6rem;display: inline-block;font-size: 0.3rem;color: #ffffff;}
.bigdata-top{ text-align: center;width: 100%;height:0.82rem;background:url(../images/xbimages/titlebg.png) no-repeat 100% 100%;background-size: 100% 100%;}
.bigdata-leftright{font-size: 0.16rem;color: #52ffff;position: absolute;top: 0.35rem;left: 0.2rem;} 
.bigdata-topright{display: inline-block;font-size: 0.16rem;color: #52ffff;position: absolute;top: 0.3rem;right: 0.2rem;}    	    	
.bigdata-con{background-size: 100% auto;position: absolute;top: 0.7rem;bottom: 0;width: 100%; left: 0;right: 0; min-height: 555px; min-width: 1000px;}
.bigdata-con-cen {
    height: 100%;
    min-height: 100%;
	overflow: hidden;
}
.bigdata-con .bigdata-cell{display: inline-block;font-size: 14px;vertical-align: top;}
.bigdata-list.whole-border:after{bottom: -15px;right: 0;}
.btm-mark{width: 55px;height: 4px;display: inline-block;background: #52ffff;position: absolute;left: -14px;bottom: -8px;z-index: 9;}   	 
.bigdata-detail{font-size: 14px;color: #ffffff;position: absolute;top: 51px;bottom: 0;width: 100%; left: 0;right: 0;}         
.bigdata-time{position: absolute; left: 10px;top:0.3rem;font-family: LCD2-Ultra;font-size:0.2rem;color: #D5D6E2;line-height: 28px;}
.bigdata-time img{vertical-align: top; margin-top: 5px;}
.bigdata-topright img{vertical-align: top; margin-top:0.1rem;}
.diseasedata-top{height:100%;position: relative;/*border: 1px solid rgba(72,255,255,0.7);background: linear-gradient(180deg, rgba(0,255,235,0.12) 0%, rgba(0,255,251,0) 100%);*/}
.diseasedata-pad{background: linear-gradient(180deg, rgba(0,255,235,0.12) 0%, rgba(0,255,251,0) 100%); position: absolute; top: 3px;left: 2px;right: 3px;bottom: 0;}
.diseasedata-pad span i{font-size: 0.14rem;}
.diseasedata-top h3{position:relative; line-height: 51px; height:51px; width: 100%; background: url(../images/bigdata/datatoprihgt.png) repeat-x;}
.diseasedata-top h3 p{background: url(../images/bigdata/datatopleft.png)  left no-repeat; line-height: 51px; height:51px; }
.diseasedata-top h3 span{color: #ffffff;font-size: 0.18rem; padding-left: 58px;}
.data-physical01 {background: rgba(35,132,221,0.7);
border-radius: 4px 4px 4px 4px; text-align: center; height: 100%;}
.font-txt{font-size: 0.3rem;}
.datacell-title{font-size: 0.15rem;color: #ffffff;padding: 10px 0; position: relative; padding-left: 0.2rem;border-bottom: 2px solid;
border-image: linear-gradient(90deg, rgba(80, 165, 249, 1), rgba(80, 165, 249, 0.54), rgba(80, 165, 249, 0)) 2 2; margin-left: 10px;}  
.diseasedata-top .report-rtit{display: inline-block; background: #2CA6FF;width:4px;height: 20px;position: absolute;top: 8px;left:0px; }

.nutrientHL{width: 25px;height: 25px;}
.fill-h {
    height: 100%;
    min-height: 100%;
	display: flex;
}
.bigdata-xpanel {
    padding: 0px 0px 15px 15px;
    height: 100%;
    min-height: 170px;
    background-size: 100% 100%;
    box-sizing: border-box;
}
.bigdata-xpanelrt {
    padding: 0px 15px 15px 15px;
    height: 100%;
    min-height: 170px;
    background-size: 100% 100%;
    box-sizing: border-box;
}

.bigdata-box01-xpanel {
    padding: 0px 10px;
    height: 100%;
    background-size: 100% 100%;
    box-sizing: border-box;
}
.bigdata-box01-xpanel.padding-bt10{ padding-bottom: 0px;}
.bigdata-box01-xpanel.padding-top10{padding-top: 0.2rem;}
.bigdata-box-02 {
    position: relative;
    width: 100%;
    height: 100%;
}


.bigdata-box-02 .bigdata-container {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0px;
    left: 0px;
}
.bigdata-box-ht{display: inline-block;width: 100%;}
.jqcloud {color:#fff; }
/*大数据-膳食营养数据展示*/
.tower-con{display: inline-block;position: relative;height: 100%;width:99%; }
.tower-cell{background-size: 100% 100%;display: table;text-align: left;float: right;margin-left: -12px;}
.tower-txt{display: table-cell;vertical-align: middle;}
.cirbefore{position: relative;}
.cirbefore:before{content: "";width: 8px;height: 8px;display: inline-block;border-radius: 50%;background: red;position: absolute;left: -15px;top: 8px;}
.cirbefore.cirred:before{background: #ef8678;}
.cirbefore.cirpink:before{background: #e39b6a;}
.cirbefore.ciryellow:before{background: #ebc66c;}
.cirbefore.cirgreen:before{background: #7bc497;}
.cirbefore.cirblue:before{background: #5db9e9;}
.nutrient-cell{font-size: 0; margin-top: 0.1rem;}
.nutrient-cell img{width: 0.18rem;}
.nutrient-cell p{height: 20px;line-height: 20px; color: #FFFFFF;font-size: 0.12rem;}
.nutrient-cell li{cursor: pointer;display: inline-block;text-align: center;font-size: 12px;width: 10%;vertical-align: top;color: #506490;position: relative;}
.nutrient-cell li.current{color: #48FFFF;}
.nutrient-cell li.current .iconfont{color: #52ffff;}
/*.nutrient-cell li.current:after{content: "";height: 4px;width: 100%;position: absolute;bottom: -4px;left: 0;background: #eee;}*/
.progress-list.nutrient-pro{margin: 0;padding-bottom: 7px;}
.progress-list.nutrient-pro .iconfont{vertical-align: middle;height: 22px;display: inline-block;}
.progress-list.nutrient-pro .iconfont:after{color: #ffd800;}
.progress-list.nutrient-pro li{margin: 10px 0;}
.progress-list.nutrient-pro li .pro-content{background:#215387;width: 64%;}
.progress-list.nutrient-pro li .pro-content .pro-bar{background: #52ffff;}
.nutrient-name{width:0.53rem;display: inline-block;text-align: left;font-size: 0.12rem;min-width:51px;}
table.data-table{background: rgba(255,255,255,0.05);color: #ffffff;font-weight: normal; height: 100%;}
table.data-table thead{color: #ffffff;}
table.data-table tr th,table.data-table tr td{padding: 1px;text-align: center;line-height: 0.18rem;font-weight: normal;font-size: 0.12rem;}
table.data-table thead tr th:first-child{width: 65px;}
.tower-sub{position: absolute;height: 94%;left: 50%;top: 0;margin-top: 6%; font-size: 0.12rem; color: #fff;}
.data-table td,.data-table th{ border-right: 1px solid #0B417C; border-bottom:1px solid #0B417C; }
.font14txt{font-size: 0.14rem;}
.font12txt{font-size: 0.12rem;}
/*大数据-传染病患病监控*/
.data-state{position: relative;margin: 3px auto;text-align: right;margin-right:40px;font-size: 14px;color: #e4b23d;font-weight: bold;/*background: url(../images/data_numbg3.png)no-repeat;background-size: 100% 100%;*/height:30px;line-height: 30px;width: 80%;}
.data-state.current{/*background: url(../images/data_numbg4.png)no-repeat;background-size: 100% 100%*/;color: #fe8d97;}

.progress-list li{margin: 9px 0;}
.pro-content{display: inline-block;height:18px;background: none;width: 60%;margin: 0 5px;vertical-align: middle;}
.pro-bar{height: 6px;vertical-align:middle;display: inline-block; margin-top: 4px; }
.pro-barlist{height: 18px; border-right: 1px solid #fff;}
.pro-name{display: inline-block;width: 98px;text-align: right;}
.pro-txt{display: inline-block;margin: -4px 0 0 10px;vertical-align: top;}

/*大数据疾病数据*/
/*.diseasedata{margin: auto;}
.diseasedata-left{width:70%;}
.diseasedata-right{width:29.5%;}
.diseasedata-list{display: inline-block;font-size: 14px;vertical-align: top;}
.diseasedata-top{height:100%;position: relative;margin-bottom: 15px;margin: 15px 0;border: 1px solid rgba(72,255,255,0.7);background: linear-gradient(180deg, rgba(0,255,235,0.12) 0%, rgba(0,255,251,0) 100%);}
.diseasedata-top h3{position:relative; line-height:50px; height: 50px; width: 100%; background:#0B4B81;}
.diseasedata-top .report-rtit{display: inline-block; background: #ffffff;width:4px;height: 20px;position: absolute;top: 15px;left: 20px; }
.diseasedata-top h3 span{color: #ffffff;font-size: 18px; padding-left: 34px;}
.datacell-title .report-rtit{display: inline-block; background: #ffffff;width:4px;height: 18px;position: absolute;top: 0px;left:10px; }
.diseasedata-top h3 span{color: #ffffff;font-size: 18px; padding-left: 34px;}
.img_map{margin: 0 auto; text-align: center; height: 82%;}
.img_map img{height: 100%;}
ul.chart-list{color: #fff;padding:0 15px 0px 15px;}
ul.chart-list li{margin-bottom: 9px;overflow:hidden;text-overflow: ellipsis;white-spa.datacell-titlece: nowrap; border-bottom: 2px solid #0A121F;line-height: 40px; height: 40px;}
ul.chart-list li img{margin-right: 7px;}
ul.chart-list li span{float: right;}*/
.body-num{
	text-align: center;
	display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-ms-flex-align: center;
	align-items: center;
	cursor: pointer;}
.body-num p{text-align: center;}
.data-per-num{display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-ms-flex-align: center;
	align-items: center;
	cursor: pointer;}
.data-per-num span{margin:0 30px 0 8px;font-size: 20px;vertical-align: middle;}
.data-per-num label{margin-right: 5px; font-size: 24px;}
.data-zhzs{display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-ms-flex-align: center;
	align-items: center;flex-wrap:wrap;  height: 70%;}

.progress-list{font-size: 0.12rem;color: #BBB;display: inline-block;width: 100%;padding: 2% 2%;}



@media screen and (max-height: 880px){
	.result-cell .result-img{width: 35px;height: 35px;}
	.result-cell .result-txt{line-height: 14px;}
	.result-txt p{font-size: 13px;}
	.result-label span{font-size: 12px;}
}
/*食物过敏监控*/
.progress-list li{}
.school-list{text-align: center;}
.school-list li{font-size: 0;margin: 10px 0;}
.school-name{color: #BBB;display: inline-table;width: 23%;vertical-align: sub;text-align: center;line-height: 14px;font-size: 14px;height: 28px;padding: 0 8px;}
.school-name span{display: table-cell;vertical-align: middle;margin: 0 10px;}
.school-txt{color: #BBB;display: inline-block;vertical-align: top;background: #023a4d;height: 28px;line-height: 28px;border-radius: 4px;width: 70%;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;font-size: 14px;}
.school-txt p{width: 96%;margin: 0 10px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;}
.analyse-cell{margin: 8px 0;}
.analyse-cell .pro-text{display: inline-block;width: 45%;text-align: center;}
.analyse-cell .pro-content{height: 26px;width: 50%;margin: 0;font-size: 12px;}
.analyse-cell .pro-content .pro-bar{height: 26px;}
.analyse-cell .pro-content .pro-txt{line-height: 26px;margin:  0;float: right;}
.analyse-cell .pro-num{display: inline-block;width: 50px;height: 50px;line-height: 44px;text-align: center;border: 4px solid #e15d68;border-radius: 50%;box-sizing: border-box;}
/*幼儿体检指标监控*/
.phy-label{width: 180px;height: 60px;margin: 2px 0;text-align: center;background: rgba(255,255,255,0.05);vertical-align: middle;}
.phy-label i{width: 39px;height: 36px;line-height: 36px;display: inline-block;vertical-align: middle;text-align: center;background-size: 100% 100%!important;}
.phy-name{display: inline-block;margin: 0px 0 4px 0;position: relative;}
.check-eye{color: #06C4EA;font-size: 26px;}
.check-lis{color: #E9AE39;font-size: 26px;}
.check-hematin{color: #FA5467;font-size: 26px;}
.check-car{color: #5D87F0;font-size: 26px;}
.bodycheck_stxc{color:#fff;}
.phy-num p{color: #fff;}
.phy-num i{font-weight: bold;font-size: 36px;vertical-align: text-top;}
.phy-name i{position: absolute;/*top: -8px;*/left: -45px;}
.phy-cell{background:  rgba(255,255,255,0.05);border-radius: 7px;padding: 2px 0;margin: 6px 15px;line-height: 26px;}
.phy-num{display: inline-block;font-size: 14px;color: #52ffff;vertical-align: middle;width: 22%;height:100%;text-align: center;}
/*@media screen and (max-height: 880px){*/
/*	.phy-label{width: 150px;height: 46px;margin: 2px 0;font-size: 13px;}*/
/*	.phy-name{margin: 8px 0 0 5px;}*/
/*	.analyse-txt{margin: 0 0 0 10px!important;}*/
/*	.analyse-txt .analyse-label{width: 190px;font-size: 13px!important;}*/
/*	.analyse-txt .analyse-label span{font-size: 15px;}*/
/*	.phy-label i{width: 34px;height: 30px;line-height: 30px;}*/
/*	.phy-cell{line-height: 24px;margin: 3px 5px;}*/
/*	.phy-num{font-size: 12px;line-height: 18px!important;}*/
/*	.phy-num i{font-size: 24px!important;}*/
/*	.phy-name i{top: -6px;}*/
/*}*/








/*时间*/
.bigdata-sub { background: url(../images/bigdata/data_topbg.jpg)no-repeat 100% 100%; background-size: 100% 69px; height: 69px; position: absolute; width: 100%; z-index: 99; }
.data-zhzs{display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-ms-flex-align: center;
	align-items: center;flex-wrap:wrap;}
.data-zhzs1{
	display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: center;
	-webkit-align-items: center;
	-ms-flex-align: center;
	align-items: center;flex-wrap:wrap; justify-content: center;}
.header-menu .layui-main .side-menu-switch{height: 50px;}


/*传染病*/
.commun-disease{}