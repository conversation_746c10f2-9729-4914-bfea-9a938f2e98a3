﻿<!DOCTYPE html>
<html>
<head>
    <title>消息提醒</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <link rel="stylesheet" href="../css/reset.css"/>
    <link rel="stylesheet" href="../css/style.css"/>

	<link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../css/medical.css">
		<link rel="stylesheet" href="../styles/xbcomstyle.css">
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>

    <!--[if lt IE 9]>
    <script src='sys/html5shiv.min.js'></script>
    <![endif]-->
    <style>
        .spnum{
            top:-10px!important;
            left:0px;
            background: #ed1717;
            right: -10px !important;
            color: white;
            border-radius: 5px;
            position: inherit;
			padding: 0 3px;
        }
        .div-content{display:none;}
		body {
            background: #EAEFF3;
        }
		.heightnum {height: auto;}
		.layui-btn.greenbg,.layui-btn{border-bottom: none;}
		.layui-btn{height: 35px;line-height: 35px;}
		.layui-btn-own img{  margin-top: 7px; }
	
    </style>
</head>
<body>
<div class="content-medical marleft"  style="border-radius:0px;">
    <div class="layui-tab layui-tab-brief" style="padding:  0; margin:0px 0 5px 0;">
        <ul class="layui-tab-title">
            <li class="layui-this">园所申请<span id="lishenqing" class="spnum">0</span></li>
            <li>已加入<span id="lijiaru" class="spnum">0</span></li>
            <li>已拒绝</li><!--<span id="lijujue" class="spnum">0</span>-->
            <li>已解除<span id="lijiechu" class="spnum">0</span></li>
        </ul>
    </div>
</div>
<div id="divmaincontent">
    <!-- 园所申请-->
    <div class="div-content">
        <script id="tplJoin0" type="text/html">
            {{#  layui.each(d.list, function(index, item){ }}
            <div class="layui-row layui-col-space5 heightnum oneMessage" style="border: 1px solid #e6e6e6;margin: 0 8px;"
                 data-id="{{ item.id }}">
                <div class="layui-col-xs10 layui-col-sm10 layui-col-md10">
                    <div class="layui-row grid-demo">
                        <div class="layui-col-xs3 layui-col-sm3 layui-col-md3">
                            <div class="grid-demo grid-demo-bg1">
                                <p>园所名称：<label class="layui-color-gary1"><span class="sp-yeyname"
                                                                               data-yeyid="{{ item.yeyid }}">{{ item.yeyname }}</span></label>
                                </p>
                                <p style="width: 270px;">
                                    {{# if(!item.agreestatus){ }}
                                    <button class="layui-btn layui-btn-small layui-btn-own" layEvent="btnyes">
                                        <img src="../images/right2_icon.png"> 同意加入
                                    </button>
                                    <button class="layui-btn layui-btn-small layui-btn-own" layEvent="btnno"
                                            style="background-color: #da4a4c;">
                                        <img src="../images/wrong2_icon.png"> 拒绝加入
                                    </button>
                                    {{# } else if(item.agreestatus==1){ }}
                                <p>加入状态：<label class="layui-color-red"><img src="../images/wrong_icon.png"> 已拒绝</label></p>
                                {{# } else if(item.agreestatus==2){ }}
                                <p>加入状态：<label class="layui-color-green"><img src="../images/right_icon.png"> 已加入</label>
                                </p>
                                {{# } }}
                                </p>
                            </div>
                        </div>
                        <div class="layui-col-xs3 layui-col-sm3 layui-col-md3">
                            <div class="grid-demo grid-demo-bg3">
                                <p>申请账号：<label class="layui-color-gary1">{{ item.joinname }}</label></p>
                                {{# if(!item.agreestatus){ }}

                                {{# } else if(item.agreestatus==1){ }}
                                <p>拒绝时间：<label class="layui-color-gary1">{{ item.agreetime }}</label></p>
                                {{# } else if(item.agreestatus==2){ }}
                                <p>加入时间：<label class="layui-color-gary1">{{ item.agreetime }}</label></p>
                                {{# } }}
                            </div>
                        </div>
                        <div class="layui-col-xs3 layui-col-sm3 layui-col-md3" style="width: 260px;">
                            <div class="grid-demo grid-demo-bg3">
                                <p>申请加入时间：<label class="layui-color-gary1">{{ item.sendtime }}</label></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-xs2 layui-col-sm2 layui-col-md2">
                    <div class="layui-row grid-demo grid-demo-bg1">
                        <button class="layui-btn" layEvent="btndetail" data-status="{{item.agreestatus}}">查看详情</button>
                        {{#  if(item.agreestatus){ }}
                        <a style="margin-left: 40px;" class="a-del"><img src="../images/del_icon.png"></a>
                        {{#  } }}
                    </div>
                </div>
            </div>
            {{#  }); }}
            {{#  if(d.list.length === 0){ }}
            <div style="margin-top: 100px;text-align: center"><img src="../images/nodata.png" /></div>
            {{#  } }}
        </script>
    </div>
    <!-- 已加入-->
    <div class="div-content">
        <script id="tplJoin1" type="text/html">
            {{#  layui.each(d.list, function(index, item){ }}
            <div class="layui-row layui-col-space5 heightnum oneMessage" style="border: 1px solid #e6e6e6;margin: 0 8px;"
                 data-id="{{ item.id }}" data-checkstatus="{{ item.checkstatus }}">
                <div class="layui-col-xs10 layui-col-sm10 layui-col-md10">
                    <div class="layui-row grid-demo">
                        <div class="layui-col-xs3 layui-col-sm3 layui-col-md3">
                            <div class="grid-demo grid-demo-bg1">
                                <p>园所名称：<label class="layui-color-gary1"><span class="sp-yeyname"
                                                                               data-yeyid="{{ item.yeyid }}">{{ item.yeyname }}</span></label>
                                </p>
                                <p>
                                    {{# if(!item.agreestatus){ }}
                                    <button class="layui-btn layui-btn-small layui-btn-own" layEvent="btnyes">
                                        <img src="../images/right2_icon.png"> 同意加入
                                    </button>
                                    <button class="layui-btn layui-btn-small layui-btn-own" layEvent="btnno"
                                            style="background-color: #da4a4c;">
                                        <img src="../images/wrong2_icon.png"> 拒绝加入
                                    </button>
                                    {{# } else if(item.agreestatus==1){ }}
                                <p>加入状态：<label class="layui-color-red"><img src="../images/wrong_icon.png"> 已拒绝</label></p>
                                {{# } else if(item.agreestatus==2){ }}
                                <p>加入状态：<label class="layui-color-green"><img src="../images/right_icon.png"> 已加入</label>
                                </p>
                                {{# } }}
                                </p>
                            </div>
                        </div>
                        <div class="layui-col-xs3 layui-col-sm3 layui-col-md3">
                            <div class="grid-demo grid-demo-bg3">
                                <p>申请账号：<label class="layui-color-gary1">{{ item.joinname }}</label></p>
                                {{# if(!item.agreestatus){ }}

                                {{# } else if(item.agreestatus==1){ }}
                                <p>拒绝时间：<label class="layui-color-gary1">{{ item.agreetime }}</label></p>
                                {{# } else if(item.agreestatus==2){ }}
                                <p>加入时间：<label class="layui-color-gary1">{{ item.agreetime }}</label></p>
                                {{# } }}
                            </div>
                        </div>
                        <div class="layui-col-xs3 layui-col-sm3 layui-col-md3" style="width: 260px;">
                            <div class="grid-demo grid-demo-bg3">
                                <p>申请加入时间：<label class="layui-color-gary1">{{ item.sendtime }}</label></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-xs2 layui-col-sm2 layui-col-md2">
                    <div class="layui-row grid-demo grid-demo-bg1">
                        <button class="layui-btn" layEvent="btndetail" data-status="{{item.agreestatus}}">查看详情</button>
                        {{#  if(item.relieve){ }}
                        <a style="margin-left: 40px;" class="a-del"><img src="../images/del_icon.png"></a>
                        {{#  } }}
                    </div>
                </div>
            </div>
            {{#  }); }}
            {{#  if(d.list.length === 0){ }}
            <div style="margin-top: 100px;text-align: center"><img src="../images/nodata.png" /></div>
            {{#  } }}
        </script>
    </div>
    <!-- 已拒绝-->
    <div class="div-content">
        <script id="tplJoin2" type="text/html">
            {{#  layui.each(d.list, function(index, item){ }}
            <div class="layui-row layui-col-space5 heightnum oneMessage" style="border: 1px solid #e6e6e6;margin: 0 8px;"
                 data-id="{{ item.id }}">
                <div class="layui-col-xs10 layui-col-sm10 layui-col-md10">
                    <div class="layui-row grid-demo">
                        <div class="layui-col-xs3 layui-col-sm3 layui-col-md3">
                            <div class="grid-demo grid-demo-bg1">
                                <p>园所名称：<label class="layui-color-gary1"><span class="sp-yeyname"
                                                                               data-yeyid="{{ item.yeyid }}">{{ item.yeyname }}</span></label>
                                </p>
                                <p>
                                    {{# if(!item.agreestatus){ }}
                                    <button class="layui-btn layui-btn-small layui-btn-own" layEvent="btnyes">
                                        <img src="../images/right2_icon.png"> 同意加入
                                    </button>
                                    <button class="layui-btn layui-btn-small layui-btn-own" layEvent="btnno"
                                            style="background-color: #da4a4c;">
                                        <img src="../images/wrong2_icon.png"> 拒绝加入
                                    </button>
                                    {{# } else if(item.agreestatus==1){ }}
                                <p>加入状态：<label class="layui-color-red"><img src="../images/wrong_icon.png"> 已拒绝</label></p>
                                {{# } else if(item.agreestatus==2){ }}
                                <p>加入状态：<label class="layui-color-green"><img src="../images/right_icon.png"> 已加入</label>
                                </p>
                                {{# } }}
                                </p>
                            </div>
                        </div>
                        <div class="layui-col-xs3 layui-col-sm3 layui-col-md3">
                            <div class="grid-demo grid-demo-bg3">
                                <p>申请账号：<label class="layui-color-gary1">{{ item.joinname }}</label></p>
                                {{# if(!item.agreestatus){ }}

                                {{# } else if(item.agreestatus==1){ }}
                                <p>拒绝时间：<label class="layui-color-gary1">{{ item.agreetime }}</label></p>
                                {{# } else if(item.agreestatus==2){ }}
                                <p>加入时间：<label class="layui-color-gary1">{{ item.agreetime }}</label></p>
                                {{# } }}
                            </div>
                        </div>
                        <div class="layui-col-xs3 layui-col-sm3 layui-col-md3" style="width: 260px;">
                            <div class="grid-demo grid-demo-bg3">
                                <p>申请加入时间：<label class="layui-color-gary1">{{ item.sendtime }}</label></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-xs2 layui-col-sm2 layui-col-md2">
                    <div class="layui-row grid-demo grid-demo-bg1">
                        <button class="layui-btn greenbg" layEvent="btndetail" data-status="{{item.agreestatus}}">查看详情</button>
                        {{#  if(item.agreestatus){ }}
                        <a style="margin-left: 40px;" class="a-del"><img src="../images/del_icon.png"></a>
                        {{#  } }}
                    </div>
                </div>
            </div>
            {{#  }); }}
            {{#  if(d.list.length === 0){ }}
            <div style="margin-top: 100px;text-align: center"><img src="../images/nodata.png" /></div>
            {{#  } }}
        </script>
    </div>
    <!-- 已解除-->
    <div class="div-content">
        <script id="tplJoin3" type="text/html">
            {{#  layui.each(d.list, function(index, item){ }}
            <div class="layui-row layui-col-space5 heightnum oneMessage" style="border: 1px solid #e6e6e6;margin: 0 8px;"
                 data-id="{{ item.id }}" data-checkstatus="{{ item.checkstatus }}">
                <div class="layui-col-xs10 layui-col-sm10 layui-col-md10">
                    <div class="layui-row grid-demo">
                        <div class="layui-col-xs3 layui-col-sm3 layui-col-md3">
                            <div class="grid-demo grid-demo-bg1">
                                <p>园所名称：<label class="layui-color-gary1"><span class="sp-yeyname"
                                                                               data-yeyid="{{ item.yeyid }}">{{ item.yeyname }}</span></label>
                                </p>
                                {{# if(!item.agreestatus){ }}

                                {{# } else if(item.agreestatus==1){ }}
                                <p>拒绝时间：<label class="layui-color-gary1">{{ item.agreetime }}</label></p>
                                {{# } else if(item.agreestatus==2){ }}
                                <p>加入时间：<label class="layui-color-gary1">{{ item.agreetime }}</label></p>
                                {{# } }}
                            </div>
                        </div>
                        <div class="layui-col-xs3 layui-col-sm3 layui-col-md3">
                            <div class="grid-demo grid-demo-bg3">
                                <p>申请账号：<label class="layui-color-gary1">{{ item.joinname }}</label></p>
                                {{# if(!item.relieve){ }}

                                {{# } else if(item.relieve==1){ }}
                                <p>解除方：<label class="layui-color-gary1">地区</label></p>
                                {{# } else if(item.relieve==2){ }}
                                <p>解除方：<label class="layui-color-gary1">园所</label></p>
                                {{# } }}
                            </div>
                        </div>
                        <div class="layui-col-xs3 layui-col-sm3 layui-col-md3" style="width: 260px;">
                            <div class="grid-demo grid-demo-bg3">
                                <p>申请加入时间：<label class="layui-color-gary1">{{ item.sendtime }}</label></p>
                                <p>解除时间：<label class="layui-color-gary1">{{ item.relievetime }}</label></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-xs2 layui-col-sm2 layui-col-md2">
                    <div class="layui-row grid-demo grid-demo-bg1">
                        <button class="layui-btn greenbg" layEvent="btndetail">查看详情</button>
                        <a style="margin-left: 40px;" class="a-del"><img src="../images/del_icon.png"></a>
                    </div>
                </div>
            </div>
            {{#  }); }}
            {{#  if(d.list.length === 0){ }}
            <div style="margin-top: 100px;text-align: center"><img src="../images/nodata.png" /></div>
            {{#  } }}
        </script>
    </div>
</div>

    <script type="text/javascript" src="../sys/jquery.js"></script>
    <script type="text/javascript" src="../sys/arg.js"></script>
    <script type="text/javascript" src="../layui-btkj/layui.js"></script>
    <script type="text/javascript" src="../js/citycode.js"></script>
    <script type="text/javascript" src="../plugin/nicescroll/jquery.nicescroll.min.js"></script>
    <script type="text/javascript" src="../js/message.js"></script>
</body>
</html>
