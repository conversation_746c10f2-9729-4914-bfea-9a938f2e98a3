﻿/*绿色皮肤*/
/*color*/




/*新增加样式*/

.usualFun{ background:#FFFFFF; padding-left:3px; padding-bottom:3px}
.usualFun a{float: left; width: 32%; +width: 32%; margin-top:3px;position: relative;text-align: center;font-size: 12px;line-height: 28px;color:#ffffff; background:#53C2BC; margin-right:3px; font-size:14px;}
.usualFun img{width: 35px;height: 35px; display: block; margin:0 auto;/*border-radius: 50%;*/ /*background: url(../images/gray.png) no-repeat center center;*/ padding-top:10px}
.usualFun u{position: absolute;display: none;}

/*.inner a:hover{width: 32%; +width: 32%; margin-top:3px;position: relative;text-align: center;font-size: 12px;line-height: 28px;color:#ffffff; background:#53C2BC; margin-right:3px; font-size:14px; display:block}
*/


.inner a:hover{ color:#FFF; background:#53C2BC; display:inline-block}




.bg1{background: #1da89e;}
/*.bg2{background: #05a9a0;}*/
.bg3{background: #22282b;}
.bg4{background: #f8f8f8;}/**/
.bg5{background: #e9e9e9;}
.cl1{color: #47494a;}
.cl2{color: #05a9a0;}

/*icon*/
u[class^=skinIcon],.layim_chateme .layim_chatsay .layim_zero,skinIcondenglu u,skinIconbq u{background-image:url(../images/icon-green.png);}

/*btn*/
.btn-border{border:1px solid #04a89f;}
.btn-big,.personalImg .webuploader-pick,.btn-black,.btn-border:hover,.monthHead,.echarts-dataview button{background: #1da89e;}
.btn-black:hover{background: #008a80;}
.list .btn-lang{border-color:#b5b5b5; color:#b5b5b5;}
.btn-lang:hover,.current.btn-lang{border-color:#1da89e;color:#1da89e;}
.layui-layer-btn a.layui-layer-btn0,.btnTop a:hover,.btn-control:hover,.operateBtn a:hover{background:#1da89e;border-color:#008a80;}

/*tab*/
.tab li:hover, .tab li.current{color: #fff; background:#1da89e!important;border: 1px solid #1da89e;}
.tab li.pulldown.current{color: #fff; background:#1da89e url(../images/icon_pulldown_white.png) no-repeat 75px center!important;background-size: 12px !important;border: 1px solid #1da89e;}
.tab li.pulldown:hover{color: #fff; background:#1da89e url(../images/icon_pulldown_white.png) no-repeat 75px center!important; background-size: 12px !important;border: 1px solid #1da89e;}

/*page*/
.dataTables_wrapper .dataTables_paginate .paginate_button.current, .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover,.dataTables_wrapper .dataTables_paginate .paginate_button:hover{ background: -webkit-linear-gradient(#1da89e 0%, #037c73 100%);background: linear-gradient(#1da89e 0%, #037c73 100%);color: #fff !important;border-color:#037c73;filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#1da89e', endColorstr='#037c73', GradientType=0);}

/*聊天窗口*/
.layim_chatmore{border-right:1px solid #00968b; background-color:#1da89e;}
.layim_chatlist li:hover,.layim_chatlist .layim_chatnow,.layim_chatlist .layim_chatnow:hover{background-color:#8ed3ce;}
.layim_sendbtn{ background-color:#1da89e;}
.layim_enter{border-left-color:#8ed3ce;}
.layim_sendbtn:hover,.layim_enter:hover{background-color:#00968b;}
.layim_sendtype span:hover,.layim_sendtype span:hover i{background-color:#1da89e;color:#fff;}
.layim_chateme .layim_chatsay{background: #b0d3d0;}

/*letter*/
.popTitle,.fc-day-header{background: #1da89e;}
.btn-head:hover{color: #1fc2b6;}
.curList .current,.curList a:hover{color: #2c4e4b;}

/*scroll*/
/*.scrollArea,.foldList{background:#2b4f4b;}*/

/*左侧面板*/
.usualFun a:hover,.inner a:hover{/*color:#1fc2b6;*/ color:#FFF; }
.funList li{color:#1fc2b6;}

/*.head{background: #2c4e4b;color:#fff;}*/
.leftTitle{background: #1da89e;color: #fff;border-bottom:1px solid #137870;}
.letterList li:hover,.letterList li.current,.noticeTitle a:hover,.articleGray a:hover,.loadMore:hover,.innerHistory li:hover,.ztree li a:hover,.foldList li:hover,.dataTables_wrapper a,.otx-table a{color: #53C2BC;}/*深绿色*/
.fc-sat .fc-day-num, .fc-sun .fc-day-num,.today_date{color: #1da89e;}
.foldList li p{color: #05a9a0;}
footer a.current,footer a:hover{color: #16a085;}
.list .current{color: #05a9a0;}
.usualFun a:hover img,.inner a:hover img,.foldList li:hover img{/*background: url(../images/hover-green.png) no-repeat center center;*/ background:none}
.historyList{background: #1da89e;}
.list li{border-bottom:1px solid #16a085;}
.list img.face{border:2px solid #b8b8b8;}
/*.contaceHead,.controlHead{background: #2c4e4b;}*/
.letterIndex{background: #ccc;}
.historyList{background: #08a8a0;}
/*智能中控*/
.btn-far:hover,.btn-near:hover{background:url(../images/distance-green.png);}
.fast:hover,.normal:hover,.slow:hover,.arrow-up:hover,.arrow-down:hover{background: url(../images/hover-green.png);}
.numberList span:hover{background: url(../images/green-big.png);}

/*.numberList span:hover,.curList a,.letter,.btn-border:hover,.workTotal,.logo,.layim_chatlist li span,.focus,.usualFun a, .inner a,footer a{color: #fff;}
*/
/*日历*/
.fc-state-down,.fc-state-active,.fc-button:hover{border-color:#1da89e;}
.fc-planmeeting-button{border:0;border-radius: 0;}
.fc-year-button:hover,.fc-month-button:hover,.fc-agendaWeek-button:hover,.fc-agendaDay-button:hover,.fc-state-active{background: #1da89e;color:#fff;}
 
/*会控*/
.meetingIndex,.fc-planmeeting-button{color:#fff; background: #1da89e;}
.rankList span.current{background: #04a89f;}

/*栏目管理*/
.meauList span{border-top: 2px solid #1da89e; border-bottom: 2px solid #1da89e;}
.meauList .inner a{color:#1da89e;}
.inner a.selected{background: #c0dcda;}

/*单位管理*/
.jOrgChart .node,.formList .webuploader-pick{background:#1da89e;}

/*tree*/
.ztree li a.curSelectedNode{border: 1px solid #1da89e;color: #1da89e;}
.ztree li a.curSelectedNode{background-color: #e8f6f5;}

/*会议控制*/
.meetMain{border-top: 4px solid #1da89e;}
.submeetList{border-top: 4px solid #a5dcd8;}

/*下拉多选*/
.ui-widget-header{background: #05a9a0; border-color:#05a9a0;}
.ui-state-hover, .ui-widget-content .ui-state-hover, .ui-widget-header .ui-state-hover, .ui-state-focus, .ui-widget-content .ui-state-focus, .ui-widget-header .ui-state-focus{color: #05a9a0;}
.ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active{background: none; border-color:#05a9a0;color: #05a9a0;}
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default{ border-color:#05a9a0;color: #05a9a0;}


.bjchuqin_bto{ text-align:left; font-size:12px; color:#FE0000; line-height:30px; height:20px; display:inline-block; margin:5px 0px 0 50px }
.bjchuqin_bto u{ background:url("../images/newtong/icon.png") no-repeat; }
/*.djbg_bgwt .bjchuqinico{ float:right; padding-right:10px; width:11px; height:28px; display:block; background-position:-9px -164px}*/
.bjchuqin_bto .bjchuqinico{ float:right;  width:11px; height:20px; display:block; margin-top:10px;  background-position:-63px -646px;padding-right:10px; cursor:pointer}
.bjchuqin_bto .bjchuqinico1,.kqdjbg_bgwt .bjchuqinico1{ float:left; width:39px; height:26px; display:block; background-position:-40px -229px;margin-top:3px}
.bjchuqin_bto .bjchuqinico2,.kqdjbg_bgwt .bjchuqinico2{ float:left; width:35px; height:26px; display:block; background-position:-79px -229px; margin-top:3px}
.bjchuqin_bto .bjchuqinico3,.kqdjbg_bgwt .bjchuqinico3{  float:left; width:35px; height:26px; display:block; background-position:-114px -229px;margin-top:3px}
.bjchuqin_bto .bjchuqinico04{  float:left; width:53px; height:26px; display:block; background-position:16px -568px}
.bjchuqin_bto .bjchuqinico12,.kqdjbg_bgwt .bjchuqinico12{ float:left; width:35px; height:26px; display:block; background-position:-153px -229px; margin-top:3px}
.bjchuqin_bto .bjchuqinico13,.kqdjbg_bgwt .bjchuqinico13{  float:left; width:35px; height:26px; display:block; background-position:-3px -266px;margin-top:3px}
.bjchuqin_bto .bjchuqinico14{  float:left; width:53px; height:26px; display:block; background-position:16px -568px}
.bjchuqin_bto .bjchuqinico23,.kqdjbg_bgwt .bjchuqinico23{  float:left; width:35px; height:26px; display:block; background-position:-386px -859px;margin-top:3px}
.bjchuqin_bto .bjchuqinico24{  float:left; width:53px; height:26px; display:block; background-position:-420px -856px}

.bjchuqin{ float:left; padding-left:10px;  display:inline-block}
.zright10{ margin-right:10px}
/*幼儿园名称下拉背景色*/
.selectColor{background-color:#1da89e }

/*智慧数字幼儿园*/
/*桌面头部菜单*/
.ultopnums li.current,.ultopnums li:hover,.topultool li:hover,.topultool li.current {
    color: #ffffff;
    background: -webkit-linear-gradient(#00d6c6, #00ab9f); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(#00d6c6, #00ab9f); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(#00d6c6, #00ab9f); /* Firefox 3.6 - 15 */
    background: linear-gradient(#00d6c6, #00ab9f); /* 标准的语法 */
}
/*菜单切换*/
.menutab ul{color: #34495E!important;}
.menutab ul li{color: #F3F4F4;border-bottom:3px solid #D5D6D6; 
	background: #F3F4F4;		
	border-image: -webkit-linear-gradient(#F2F3F4,#D5D6D6) 40 20;
    border-image: -moz-linear-gradient(#F2F3F4,#D5D6D6) 40 20;		
	border-image: -o-linear-gradient(#F2F3F4,#D5D6D6) 40 20;
	border-image: linear-gradient(#F2F3F4,#D5D6D6) 40 20;}
.menutab ul li.current{color: #34495E!important; background: #fff;}
#divdesktop .scrollArea{background: none;}
#divmenulist .funList li:hover{background: #F2F2F2;color: #666!important;}
#divdeskleft:after{content: "";width: 20px;position: absolute;top: 0px;bottom: 0;right: -20px;box-shadow: 5px 0px 5px -4px inset #DCE0E4;
  
}
.funtop .desktop-logo .tabout-div{box-shadow: 0px 0px 5px 2px #00bfb2;}
.funtop .desktop-logo .tab-cell{box-shadow: 0px -8px 4px -5px inset #aed0ce;}

/*保教*/
.term-tab.current{background: #1fd3c9;color: #ffffff;}
.term-tab.current .calendar-div{border: 1px solid #1ea29b;}
.term-list.redbg{background: #1fd3c9;}
.calen-top h5{background: #1fd3c9;}
.calen-txt p{color: #1fd3c9;}
.div-right .layui-table tbody.current{border: 2px solid #1fd3c9;}
.div-right .layui-table tbody.current tr:nth-child(1) td:nth-child(1){background-color: #1fd3c9;}
.div-right .layui-table td.pinkcurrent{background-color: #1fd3c9;}
.workrest-left h5{background: #1bc9bf!important;}
.workrest-right h5{background: #1fd3c9!important;}
.time-work li.time-worksel{ background:#1fd3c9;}
.week-tab.current{background: #1fd3c9;}
.icon-list .layui-btn{background: #0ea59d!important;}
.protect-educate .plan-txt.current{background: #1fd3c9;color: #ffffff;border: 1px solid #1fd3c9;}
.play-sub{border: 2px solid #1fd3c9!important;}
.def-icon .icon_calen:before{color: #1fd3c9!important;}
.opreate-icon .icon-txt .iconfont{color: #1fd3c9!important;}
.protect-educate .plan-left-icon>div .iconfont:before{color: #1fd3c9;}
.protect-educate .plan-left-icon>div .icon_ratio:before{border: 2px solid #1fd3c9;}
.def-icon .icon_tableft:before,.def-icon .icon_tabright:before{color: #1fd3c9;}
#a_unselect{color: #1fd3c9!important;}
.upload-btn .opr_select{background: #1fd3c9!important;}
.opr-btn.pay-save{background: #1fd3c9!important;}
#pgriddivshipuarea.def-layui-table tr.current td:not(.tr-firsttd){background: #dbf4f3;}
#pgridplantable.def-layui-table tr.current td:not(.tr-firsttd){background: #dbf4f3;}
/*作息弹框按钮颜色*/
.opr-btn{background: #1fd3c9;}
.cho-main .layui-btn{background: #1fd3c9;}
.protect-educate .plan-left-icon>div.current .iconfont:before{color: #1fd3c9;}
.protect-educate .plan-left-icon>div.current .iconfont:before{color: #1fd3c9;}
.plan-con{border: 4px solid #1fd3c9;}
.plan-leftspan.currentpink, .plan-rightspan.currentpink{background: #1fd3c9;}
.plan-leftspan.currentred, .plan-rightspan.currentred{background: #00b1a7;}
.left-contain .common-tab3 li.current a{color: #1fd3c9;font-weight: bold;border-bottom: 2px solid #1fd3c9;}
.left-contain .icon_search2:before{color: #1fd3c9;}
.type-plan .iconfont.redicon2:before{color: #1fd3c9;}
#divdt .layui-table tbody.current{border: 2px solid #1fd3c9;}
#divdt .layui-table tbody.current tr:nth-child(1) td:nth-child(1){background-color: #1fd3c9;}
#divdt .layui-table td.pinkcurrent{background-color: #1fd3c9;}
#divdt .tdexist{background-color: #01bab0;}
.timetable-list .circle-label{background: #1fd3c9;}
.teaching-list .teachadd-btn .teachlayui-btn{background: #1fd3c9;}
.upload-list .layui-btn-normal, .teaching-list-bom .upload-right .layui-btn, .cover-list .layui-btn{background: #1fd3c9;}
#add_pic .webuploader-pick{background: #1fd3c9;}
.teaching-list .layui-btn{color: #1fd3c9;}
.upload-btn .opr_save{background: #1fd3c9!important;}
.upload-btn .opr_saveadd{background: #1fd3c9!important;}
.course-opr .layui-tab-brief>.layui-tab-title .layui-this{color: #1fd3c9;}
.course-opr .layui-tab-brief>.layui-tab-more li.layui-this:after,.course-opr .layui-tab-brief>.layui-tab-title .layui-this:after{border-bottom: 4px solid #1fd3c9;}
#add_pic .webuploader-pick{background:#1fd3c9;}
#add_pic .webuploader-pick, #add_video .webuploader-pick{background:#1fd3c9;}
#add_audio .webuploader-pick{background:#1fd3c9;}
#add_files .webuploader-pick{background:#1fd3c9;}


.notice-pages .selected {background: #189F92;}
.notice-pages a:hover{background:#189F92;border:1px solid #189F92}
.notice-pages .pages-skip .skip-sure{background: #189F92;}