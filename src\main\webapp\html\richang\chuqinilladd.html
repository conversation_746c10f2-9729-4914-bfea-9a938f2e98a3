﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>因病缺勤登记</title>
    <link type="text/css" rel="stylesheet" href="../../css/reset.css" />
    <link type="text/css" rel="stylesheet" href="../../layui-btkj/css/layui.css" />
    <link rel="stylesheet" type="text/css" href="../../css/icon.css" />
    <link rel="stylesheet" type="text/css" href="../../css/commonstyle-layui-btkj.css" />
    <link rel="stylesheet" type="text/css" href="../../css/style.css" />
	<link rel="stylesheet" href="../../plugin/ueditor/third-party/webuploader/webuploader.css" />
    <style type="text/css">
        /*滚动条样式*/
        html{
            color: #000;
            font-size: .7rem;
            font-family: "\5FAE\8F6F\96C5\9ED1",Helvetica,"黑体",Arial,Tahoma;
            height: 100%;
        }
        /*滚动条样式*/
        html::-webkit-scrollbar {
            display:none;
            width: 6px;
            height: 6px;
        }
        html:hover::-webkit-scrollbar{
            display:block;
        }
        html::-webkit-scrollbar-thumb {
            border-radius: 3px;
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
            border: 1px solid rgb(255, 255, 255);
            background: rgb(66, 66, 66);
        }
        html::-webkit-scrollbar-track {
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
            border-radius: 0;
            background: rgba(0,0,0,0.1);
        }

        .default-form .layui-form-label { width: 100px; color: #778CA2; }
		.form-display .layui-form-checkbox i{margin: 2px 0;}
		.layui-form-checkbox[lay-skin="primary"]{padding-left: 0px;}
		.layui-form-item .layui-form-checkbox[lay-skin="primary"]{margin-top: -4px;}
        /*.webuploader-container { height: 85px; }*/
        .course-cen .webuploader-pick { position: relative; display: inline-block; cursor: pointer; background: #1da89e; padding: 2px 10px; color: #fff; text-align: center; border-radius: 0px; overflow: hidden; }
        .coverart p { text-align: center; color: #666666; font-size: 12px; margin: 5px 0; }
        .coverart { height: auto; border: 1px solid #eee; background: #FFFFFF; box-sizing: border-box; border-radius: 3px; text-align: center; overflow: hidden; }
        .upload-cell { height: auto; }
		.webuploader-pick {
            background: #fff !important;
            border: 1px solid #4A90E2;
            color: #4A90E2;
        }
        #btnupload{width:140px;height:140px;display: inline-block;position:absolute;}
        .webuploader-pick{ color: #4A90E2 !important; width: 100%;height: 100%;}
        .webuploader-pick img {
            position: absolute;
            top:67px;
            left: 67px;
        }
        .webuploader-container {
            margin-top: 0px;
        }
		.heightnum {padding:0px 0px;}
        #arrimg li{position:relative;display:inline-block;width:148px;height:148px;margin-right:10px; margin-bottom: 10px;}
        #arrimg li img{width:100%;height:100%;}
    </style>
</head>
<body>
    <div class="layui-page" style="padding: 0 15px;min-width: 740px">
        <form id="form" action="" class="layui-form form-display" lay-filter="formOk">
            <div class="default-form" style="margin:20px 10px;">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>日期：</label>
                        <div class="layui-input-block" style="min-height: 30px;margin-left: 0px; position: relative;float: left; width: 150px;">
                            <input autocomplete="off" class="layui-input alendar-padding" type="text" id="txtstartdate" placeholder="出勤日期" style="width:150px; display: inline-block;" lay-verify="required" notip="1" />
                            <i class="layui-alendar"><img id="iconstartdate" src="../../images/cal_icon.png" style="width: 14px;" /></i>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>班级：</label>
                        <div class="layui-input-block">
                            <select id="selclass" lay-filter="selclass" lay-verify="required"></select>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>幼儿：</label>
                        <div class="layui-input-block" style="width:150px;">
                            <select id="selstu" lay-filter="selstu" lay-verify="required|sheckstu"></select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">性别：</label>
                        <div class="layui-input-block">
                            <label id="labsex"></label>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">年龄：</label>
                        <div class="layui-input-block">
                            <label id="labage"></label>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">排查原因：</label>
                        <div class="layui-input-block" style="width:150px;">
                            <input type="radio" name="radreason" value="1" title="因病缺勤" checked="checked" />
                            <input type="radio" name="radreason" value="2" title="事假" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">发病日期：</label>
                        <div class="layui-input-block" style="min-height: 30px;margin-left: 10px; position: relative;float: left; width: 150px;">
                            <input autocomplete="off" class="layui-input alendar-padding" type="text" id="txtilldate" placeholder="发病日期" style="width:150px; display: inline-block;" />
                            <i class="layui-alendar"><img id="iconilldate" src="../../images/cal_icon.png" style="width: 14px;" /></i>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">主要症状：</label>
                        <div class="layui-input-block">
                            <div style="display: inline-block;">
                                <input name="chksymptom" type="checkbox" value="1" title="发热" />
                                <input id="txttiwen" class="layui-input" style="display:inline-block; width:120px;" placeholder="请注明温度" notip="1" data-type="number" data-precision="1" />
                            </div>
                            <div style="display: inline-block; margin-left: 15px;">
                                <input name="chksymptom" type="checkbox" value="2" title="咳嗽" />
                            </div>
                            <div style="display: inline-block;">
                                <input name="chksymptom" type="checkbox" value="3" title="咽痛" />
                            </div>
                        </div>
                        <div class="layui-input-block" style="margin-top:5px;">
                            <div style="display: inline-block;">
                                <input name="chksymptom" type="checkbox" value="4" title="呕吐" />
                                <input id="txtoutu" class="layui-input" style="display:inline-block; width:120px;" placeholder="请注明次数" notip="1" data-type="number" />
                            </div>
                            <div style="display: inline-block;margin-left: 15px;">
                                <input name="chksymptom" type="checkbox" value="5" title="腹泻" />
                                <input id="txtfuxie" class="layui-input" style="display:inline-block; width:120px;" placeholder="请注明次数" notip="1" data-type="number" />
                            </div>
                        </div>
                        <div class="layui-input-block" style="margin-top:5px;">
                            <div style="display: inline-block;">
                                <input name="chksymptom" type="checkbox" value="6" title="皮疹" />
                            </div>
                            <div style="display: inline-block;">
                                <input name="chksymptom" type="checkbox" value="7" title="结膜充血" />
                            </div>
                        </div>
                        <div class="layui-input-block" style="margin-top:5px;">
                            <div style="display: inline-block;">
                                <input name="chksymptom" type="checkbox" value="8" title="其他" />
                                <input id="txtxiangxi" class="layui-input" style="display:inline-block; width:430px;" placeholder="请详细描述" notip="1" maxlength="100" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">是否就诊：</label>
                        <div class="layui-input-block" style="width:150px;">
                            <input name="radhasdoctor" type="radio" value="1" title="是" />
                            <input name="radhasdoctor" type="radio" value="2" title="否" />
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">就诊医院：</label>
                        <div class="layui-input-block">
                            <input id="txthospitalname" class="layui-input" style="display:inline-block; width:212px;" placeholder="请输入就诊医院" notip="1" maxlength="50" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">排查结果：</label>
                        <div class="layui-input-block">
                            <input id="txtresults" class="layui-input" style="display:inline-block; " placeholder="请输入排查结果" notip="1" maxlength="100" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">诊断：</label>
                        <div class="layui-input-block">
                            <input id="txtzhenduan" class="layui-input" style="display:inline-block;" placeholder="请输入诊断" notip="1" maxlength="100" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">登记人：</label>
                        <div class="layui-input-block">
                            <input id="txtregistrant" class="layui-input" style="display:inline-block; " placeholder="请输入登记人" notip="1" maxlength="10" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">备注：</label>
                        <div class="layui-input-block">
                            <input name="radnotes" type="radio" value="1" title="传染病早期症状" />
                            <input name="radnotes" type="radio" value="2" title="传染病患儿" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">返园时间：</label>
                        <div class="layui-input-block" style="min-height: 30px;margin-left: 0px; position: relative;float: left; width: 150px;">
                            <input autocomplete="off" class="layui-input alendar-padding" type="text" id="txtreyeydate" placeholder="返园时间" style="width:150px; display: inline-block;" />
                            <i class="layui-alendar"><img id="iconreyeydate" src="../../images/cal_icon.png" style="width: 14px;" /></i>
                        </div>
                    </div>
                </div>
<!--                <div class="layui-form-item">-->
<!--                    <label class="layui-form-label">病例上传：</label>-->
<!--                    <div class="layui-input-block" style="width: 488px;">-->
<!--                        <div class="coverart" style="padding-bottom: 10px;">-->
<!--                            <div class="upload-con">-->
<!--                                <div id="divlistfiledata" class="pic-show img_wrapper" style="display: none; text-align: left; float: left;" lay-verify="chkfile">-->
<!--                                </div>-->
<!--                                <div style="display: block; width: 100%; margin: 0 auto; float: left;">-->
<!--                                    <div id="uploadBtn" style="margin: 0px;" lay-verify="fileurl">-->
<!--                                        <img src="../../images/downloadico.png" style="width: 69px; height: 67px; margin: 14px 0 0 0px;" />-->
<!--                                    </div>-->
<!--                                    <p style="line-height: 11px;">点击上传照片</p>-->
<!--                                    <p style="line-height: 11px;">支持扩展名：*jpg*.pjpeg;*.jpeg;*.png;*.gif，大小不超过10MB</p>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
				
				  <div style="margin:10px 0;">
                <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                    <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">上传病例：</label>
                    <div class="layui-input-inline" style="min-height: 30px;width: 480px;">
                        <ul id="arrimg" style="display: inline-block;">
                            <!--<li>-->
                            <!--   <img src="../../images/add_icolt.png"/>-->
                            <!--   <input class="imgpath" type="hidden"/>-->
                            <!--</li>-->
                        </ul>
                        <div id="btnupload" class="heightnum">
                           
                            <img src="../../images/add_icolt.png">
                    
                        </div>
                    </div>
                </div>
            </div>
                <div style="display:none;">
                    <div class="layui-input-block">
                        <a id="btnok" class="layui-btn" lay-submit=lay-submit>立即提交</a>
                    </div>
                </div>
            </div>
        </form>
    </div>
<!--    <script data-main="../../js/richang/chuqinilladd" src="../../sys/require.min.js"></script>-->
    <script type="text/javascript">
        var v = top.version;
        document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../sys/function.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../sys/uploadutil.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
        document.write('<script type="text/javascript" src="../../plugin/ueditor/third-party/webuploader/webuploader.min.js?v=' + v + '"><' + '/script>');
        document.write("<script type='text/javascript' src='../../js/richang/tuoyucommon.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../js/richang/chuqinilladd.js?v=" + v + "'><" + "/script>");
    </script>
</body>
</html>