layui.config({
    base: './js/'
}).extend({ //设定模块别名
    system: '../../sys/system',
    waitSeconds: 0,
    promise: '../../plugin/promise/promise'
});
var objdata = {
        openinfo: {},
        arrsendobj: ['e', 'y', 'ey']
    },
    zTreeObj, form, formSelects;
var outCon = $("#container"),
    toType = Arg("totype"),
    placeholderTxt = "输入区域关键词",
    objSelectUser = { //选择的发送人信息 : 幼儿园时 数据为 key为yeyid,值时包含发送人信息(Object)的数组, 同事时 key为areaCode
        userCount : 0,
        yeyTotal : 0,
        selYeyNum : 0,
        collTotal : 0,
        selCollNum : 0
        // role : "4" // 当前选择的身份 4:园长 3:保健医 0:二者都
    },
    objSelectobj = {
        // toorganoryeynum : 0
    }, //选择发送的幼儿园
    noticeObjdata = parent.objdata.noticeAddFrame.contentWindow.objdata;
//定义模板
var templete = {
    //选中用户右边展现用户模板
    selectUser: '<tr data-phone="{{mobile}}" data-yeyid="{{yeyid}}">' +
    '<td>' +
    '<div class="layui-row grid-demo">' +
    '<div class="layui-col-xs5 layui-col-sm5 layui-col-md5">' +
    '<span style="margin-left: 20px;">{{username}}</span>' +
    '</div>' +
    '{{temWeXin}}' +
    '<div class="layui-col-xs2 layui-col-sm2 layui-col-md2">' +
    '<img class="js-del-user" src="../images/close.png" style="cursor: pointer;">' +
    '</div>' +
    '</div>' +
    '</td>' +
    '</tr>',
    //微信标
    weXinLogo:  '<div class="layui-col-xs5 layui-col-sm5 layui-col-md5">' +
    '<img src="../images/weixin.png">' +
    '</div>'
};
if(typeof noticeObjdata.objSelectobj !== "undefined" && Arg("totype") === Arg("pretotype")){
    //再次进入用户选择页面，保留原选择用户
    objSelectUser = noticeObjdata.objSelectUser;
    objSelectobj = noticeObjdata.objSelectobj;
    show2SelUser();
    objdata.firstFlag = false;
}else{
    //第一次进入用户选择页面
    noticeObjdata.objSelectUser = objSelectUser;
    noticeObjdata.selectCount = 0; //已选择的人数
    noticeObjdata.objSelectobj = objSelectobj;
    objdata.firstFlag = true;
}
layui.use(['system', 'promise', 'form','formSelects'], function () {
    formSelects = layui.formSelects;
    form = layui.form;
    if(Arg("totype") == 'ey'){
        objdata.arrsendobj = ['e', 'y'];
    }else if(Arg("totype") == 'e'){
        objdata.arrsendobj = ['e'];
    }else if(Arg("totype") == 'y'){
        objdata.arrsendobj = ['y'];
    }
    initData();
    initEvent();
});
function initData(){
    var arrsm = [["noticeselper.getopenarea", parent.objdata.my.areacode.substring(0, 6)]];
    $.sm(function (re, err, obj) {
        if (!err) {
            var arrdata = [], item = re[0], arrselareacode = [parent.objdata.my.areacode.substring(0, 6)];
            for (var i = 0; i < item.length; i++) {
                objdata.openinfo[item[i].areacode] = item[i];
                arrdata.push({name: item[i].areaname, value: item[i].areacode});
            }
            formSelects.data('receivearea', 'local', {
                arr: arrdata
            });
            if(objSelectUser.objareainfo){
                var objareainfo = objSelectUser.objareainfo;
                for (var c in objareainfo) {
                    arrselareacode.push(c);
                }
            }
            formSelects.value('receivearea', arrselareacode);
            setselnoticeinfo();
        } else {
            parent.layer.msg("发送消息失败")
        }
    }, arrsm);
}

function setselnoticeinfo(){
    var arrsendobj = objdata.arrsendobj;
    for (var i = 0; i < arrsendobj.length; i++) {
        var arrhtml = [];
        var totype = arrsendobj[i];
        if(parent.ishospital() && totype == 'e'){
            $("#divhospital").hide();
            continue;
        }
        var totypeinfo = noticeObjdata.totypename[totype];
        // $("#lbseltotype").html("选择" + noticeObjdata.totypename[Arg("totype") + "name"] + "及接收对象:");
        var receiveobj = noticeObjdata.objSelectUser.receiveobj;
        for (var t in totypeinfo) {
            arrhtml.push('<tr>\n' +
                '<td style="background: #FAFAFA;width: 160px;"><div><label><input type="checkbox" name="chkroletype'  + t +  '" value="'  + t +  '" title="' + totypeinfo[t] + '" lay-filter="chkroletype'  + t +  '" ' + (receiveobj && receiveobj[t] ? "checked='checked'" : "") + '>' + totypeinfo[t] + '</label></div></td>\n' +
                '<td style="text-align: center"><div id="divroles'  + t +  '" class="zhiweitxt"></div></td>\n' +
                '</tr>');
            getsole(t, 'chkrole'  + t, "divroles" + t, true);
            outCon.off("click", "input[name='chkroletype"  + t +  "']").on("click", "input[name='chkroletype"  + t +  "']", function (){
                var roletype = $(this).val();
                if($(this).is(":checked")){
                    getsole(roletype, 'chkrole'  + roletype, "divroles" + roletype, true);
                }else{
                    getsole(roletype, 'chkrole'  + roletype, "divroles" + roletype, false);
                }
            });
            outCon.off("click", ".chkrole"  + t).on("click", ".chkrole"  + t, function (){//至少选择一个角色
                var roletype = $(this).attr("roletype");
                if($(".chkrole" + roletype + ":checked").length <= 0){
                    parent.layer.msg("至少选择一个角色！");
                    $(this).prop("checked", "checked");
                }
            });
        }
        $("#divseltotype_" + totype).html(arrhtml.join(""));
    }
}
function getsole(rotypetype, name, eid, isncheck){
    var receiveobj = noticeObjdata.objSelectUser.receiveobj;
    var strareacode = jQuery.getparent().objdata.my.areacode;
    if(strareacode){
        $.sm(function (re, err){
            var objrole = {};
            for (var t in receiveobj) {
                var items = receiveobj[t];
                objrole[t] = [];
                for (var i = 0; i < items.length; i++) {
                    objrole[t].push(items[i][0]);
                }
            }
            var arrhtml = [];
            var checked = "";
            for (var i = 0; i < re.length; i++) {
                checked = "";
                if(objrole && objrole[re[i].roletype] && $.inArray(re[i].id + "", objrole[re[i].roletype]) >= 0){
                    checked = "checked";
                    $("input[name='chkroletype" + re[i].roletype + "']").attr("checked", true);
                }
                if((!objrole || !objrole[re[i].roletype]) && $("input[name='chkroletype" + re[i].roletype + "']").is(":checked")){
                    checked = "checked";
                }
                if(!isncheck){
                    checked = "";
                }
                arrhtml.push('<label class="zhiweitxt">' + re[i].rname + '<input type="checkbox" roletype="' + re[i].roletype + '" class="' + name + '" ' + (checked ? 'checked="' + checked + '"' : '') +  'name="' + name + re[i].id + '" value="' + re[i].id + '" title="' + re[i].rname + '" lay-filter="' + name + re[i].id + '"></label>');
            }
            $("#" + eid).html(arrhtml.join(""));
        }, ["noticselper.getsole", rotypetype]);
    }
}
//初始化事件
function initEvent() {
//    发送类型切换
    outCon.off("click", ".layui-tab-title li").on("click", ".layui-tab-title li", function (ev) {
        if ($(this).hasClass("layui-this")) {
            return false;
        }
        var index = $(this).index();
        $(this).addClass("layui-this").siblings().removeClass("layui-this");
        $(this).parent().nextAll().hide().eq(index).show();
    });
}
function groupData(arr, key) {
    var objData = {},
        arrKey = [];
    for (var i = 0; i < arr.length; i++) {
        var tmpObj = arr[i],
            keyVal = tmpObj[key];
        if (objData[keyVal]) {
            objData[keyVal].push(tmpObj);
        } else {
            arrKey.push(keyVal);
            objData[keyVal] = [tmpObj];
        }
    }
    arrKey.sort(function (arr1, arr2) {
        return Number(arr1) - Number(arr2);
    });
    return {arrKey: arrKey, objData: objData};
}
//检索区域名字
function getAreaName(areaCode) {
    if(areaCode.length === 6){
        var areaName = objCityCode[areaCode];
        if(areaName) return areaName;
    }
    for(var i = 0; i < objdata.lingInfo.arrarea.length; i++){
        var cur = objdata.lingInfo.arrarea[i];
        if(cur.areacode === areaCode){
            return cur.areaname;
        }
    }
    return areaCode.length === 6 ? "本部" : "未定义";
}
//处理选择的节点是区域
function checkArea(pNode, toType) {
    var children1 = pNode.children,
        children = [];
    pNode.preChecked = true;
    for(var i = 0; i < children1.length; i++){
        var cur = children1[i];
        if(!cur.isHidden){
            children.push(cur);
        }
    }
    if(toType === 1){
        if(typeof children[0].self_areaCode !== "undefined"){
            for(var i = 0; i < children.length; i++){
                children[i].preChecked = true;
                arguments.callee(children[i],1);
            }
            return;
        }else{
            checkUserNode(children, pNode.self_areaCode, 1);
            //处理已经选择对象的提示信息
            objSelectUser.selCollNum += children.length;
            outCon.find(".qr-coll-num i").eq(0).text(objSelectUser.selCollNum);
        }
    }else{
        //发送对象为幼儿园
        //选择得还是区域
        if(typeof children[0].self_yeyid === "undefined"){
            for(var i = 0; i < children.length; i++){
                children[i].preChecked = true;
                arguments.callee(children[i],toType);
            }
            return;
        }
        //    选择的区域直接管辖幼儿园
        for(var i = 0; i < children.length; i++){
            var yeyNode = children[i],
                yeyId = yeyNode.self_yeyid,
                showNodes = [],
                userNodes = yeyNode.children;
            children[i].preChecked = true;
            objSelectobj[yeyId] = 0;
            objSelectUser[yeyId] = [];
            for(var j = 0; j < userNodes.length; j++){
                var cur = userNodes[j];
                if(!cur.isHidden){
                    showNodes.push(cur);
                }
            }
            checkUserNode(showNodes, yeyId);
        }
        //处理已经选择对象的提示信息
        objSelectUser.selYeyNum += children.length;
        objSelectobj.toorganoryeynum += children.length;
        outCon.find(".stat-num-right i").eq(0).text(objSelectUser.selYeyNum);
    //    幼儿园直属片区做统计
        var arrCount = /(\S+)（(\S+)）/.exec(pNode.name),
            name = "",
            aObj = $("#" + pNode.tId + "_a");
        if(!arrCount){
            arrCount = /(\S+)（(\S+)）/.exec(aObj.find("span").eq(1).text());
        }
        name = arrCount[1] + "（" + children.length + "/" + children.length + "）";
        pNode.name = name;
        zTreeObj.updateNode(pNode);
    }
}
//处理取消选择的节点是区域
function uncheckArea(pNode, toType) {
    //    发送对象为同事
    var children1 = pNode.children,
        children = [];
    for(var i = 0; i < children1.length; i++){
        if(children1[i].preChecked) children.push(children1[i]);
    }
    pNode.preChecked = false;
    if(toType === 1){
        if(children[0] && typeof children[0].self_areaCode !== "undefined"){
            (function (children1,fn) {
                for(var j = 0; j < children1.length; j++){
                    if(children1[j].preChecked !== true) continue;
                    children1[j].preChecked = false;
                    fn(children1[j],1);
                }
            })(children,arguments.callee);
        }else{
            delete objSelectobj[pNode.self_areaCode];
            delete objSelectUser[pNode.self_areaCode];
            //处理已经选择对象的提示信息
            objSelectUser.selCollNum -= children.length;
            objSelectUser.userCount -= children.length;
            outCon.find(".qr-coll-num i").eq(0).text(objSelectUser.selCollNum);
        }
    }else{
        //发送对象为幼儿园
        if(typeof children[0].self_yeyid === "undefined"){
            for(var j = 0; j < children.length; j++){
                if(children[j].preChecked !== true) continue;
                children[j].preChecked = false;
                arguments.callee(children[j],toType);
            }
            return;
        }
        //    取消选择的区域直接管辖幼儿园
        for(var i = 0; i < children.length; i++){
            var yeyNode = children[i],
                yeyId = yeyNode.self_yeyid,
                userNodes = yeyNode.children;
            delete objSelectobj[yeyId];
            objSelectobj.toorganoryeynum --;
            yeyNode.preChecked = false;
            delete objSelectUser[yeyId];
            for(var j = 0; j < userNodes.length; j++){
                var tmpNode = userNodes[j];
                if(tmpNode.isHidden){
                    continue;
                }
                objSelectUser.userCount --;
            }
        }
        //处理已经选择对象的提示信息
        objSelectUser.selYeyNum -= children.length;
        outCon.find(".stat-num-right i").eq(0).text(objSelectUser.selYeyNum);
        //    幼儿园直属片区做统计
        pNode.preChecked = false;
        var aObj = $("#" + pNode.tId + "_a"),
            arrCount = /(\S+)（(\S+)）/.exec(aObj.find("span").eq(1).text()),
            sel = 0,
            all = arrCount[2].split("/")[1];
        pNode.name = arrCount[1] + "（" + sel + "/" + all + "）";
        zTreeObj.updateNode(pNode);
    }
}
//复选框选择或取消选择
function zTreeOnCheck(event, treeId, treeNode) {
    if(treeNode.checked){
        //    选择
        //    按幼儿园保存发送对象信息
        if(typeof treeNode.self_yeyid !== "undefined"){
            //    选择了幼儿园
            var yeyId = treeNode.self_yeyid,
                userNodeList = treeNode.children;
            objSelectobj[yeyId] = 0;
            objSelectobj.toorganoryeynum ++;
            objSelectUser[yeyId] = [];
            checkUserNode(userNodeList, yeyId);
            //处理已经选择对象的提示信息
            objSelectUser.selYeyNum ++;
            outCon.find(".stat-num-right i").eq(0).text(objSelectUser.selYeyNum);
            //    幼儿园直属片区做统计
            var aObj = $("#" + treeNode.tId + "_a"),
                pNode = treeNode.getParentNode();
            treeNode.preChecked = pNode.preChecked = true;
            var arrCount = /(\S+)（(\S+)）/.exec(pNode.name);
            if(!arrCount){
                arrCount = /(\S+)（(\S+)）/.exec(aObj.parent().parent().prev().find("span").eq(1).text());
            }
            var sel = Number(arrCount[2].split("/")[0]) + 1,
                all = arrCount[2].split("/")[1],
                name = arrCount[1] + "（" + sel + "/" + all + "）";
            pNode.name = name;
            zTreeObj.updateNode(pNode);
        }else if(typeof treeNode.self_areaCode !== "undefined"){
            //    选择的是区域
            checkArea(treeNode,toType);
        }else{
            //    选择的是具体用户(发送对象为同事时)
            var objUser = {
                    yeyid : 0,
                    mobile : treeNode.self_phone,
                    name : treeNode.name
                },
                pNode = treeNode.getParentNode();
            treeNode.preChecked = pNode.preChecked = true;
            objSelectUser[pNode.self_areaCode] = objSelectUser[pNode.self_areaCode] || [];
            objSelectUser[pNode.self_areaCode].push(objUser);
            objSelectUser.selCollNum ++;
            objSelectUser.userCount ++;
            outCon.find(".qr-coll-num i").eq(0).text(objSelectUser.selCollNum);
        }
    }else{
        //    取消选择
        if(typeof treeNode.self_yeyid !== "undefined"){
            //    取消幼儿园
            var yeyId = treeNode.self_yeyid,
                userNodeList = treeNode.children;
            delete objSelectobj[yeyId];
            objSelectobj.toorganoryeynum --;
            delete objSelectUser[yeyId];
            for(var i = 0; i < userNodeList.length; i++){
                var tmpNode = userNodeList[i];
                if(tmpNode.isHidden){
                    continue;
                }
                objSelectUser.userCount --;
            }
            //处理已经选择对象的提示信息
            objSelectUser.selYeyNum --;
            outCon.find(".stat-num-right i").eq(0).text(objSelectUser.selYeyNum);
            //    幼儿园直属片区做统计
            treeNode.preChecked = false;
            var aObj = $("#" + treeNode.tId + "_a"),
                arrCount = /(\S+)（(\S+)）/.exec(aObj.parent().parent().prev().find("span").eq(1).text()),
                sel,
                all = arrCount[2].split("/")[1],
                pNode = treeNode.getParentNode();
            if(!pNode.checked) {
                pNode.preChecked = false;
                sel = 0;
            }else {
                sel = Number(arrCount[2].split("/")[0] - 1);
                sel = sel < 0 ? 0 : sel;
            }
            pNode.name = arrCount[1] + "（" + sel + "/" + all + "）";
            zTreeObj.updateNode(pNode);
        }else if(typeof treeNode.self_areaCode !== "undefined"){
            //    取消选择的是区域
            uncheckArea(treeNode,toType);
        }else{
            //取消选择的是同事用户
            objSelectUser.selCollNum --;
            objSelectUser.userCount --;
            var areaCode = treeNode.getParentNode().self_areaCode;
            for(var i = 0; i < objSelectUser[areaCode].length; i++){
                if(objSelectUser[areaCode][i].mobile === treeNode.self_phone){
                    objSelectUser[areaCode].splice(i,1);
                }
            }
        }
    }
}
//checked时处理具体用户节点
function checkUserNode(userNodeList,yeyId, totype) {
    for(var i = 0; i < userNodeList.length; i++){
        var tmpNode = userNodeList[i],
            tmp = {
                yeyid : totype === 1 ? 0 : yeyId,
                mobile : tmpNode.self_phone,
                name : tmpNode.name
            };
        if(tmpNode.isHidden){
            continue;
        }
        tmpNode.preChecked = true;
        objSelectobj[yeyId] = (objSelectobj[yeyId] || 0);
        objSelectobj[yeyId] ++;
        objSelectUser.userCount ++;
        objSelectUser[yeyId] = objSelectUser[yeyId] || [];
        objSelectUser[yeyId].push(tmp);
    }
}
//再次进入展现已经选择的用户
function show2SelUser() {
    if(toType === 2){
        outCon.find(".stat-num-right i").eq(0).text(objSelectUser.selYeyNum).next().text(objSelectUser.yeyTotal);
    }else{
        outCon.find(".qr-coll-num i").eq(0).text(objSelectUser.selCollNum).next().text(objSelectUser.collTotal);
    }
    var rolse = outCon.find(".qr-role");
    if(objSelectUser.role == "3"){
        rolse[0].checked = false;
        rolse[1].checked = true;
    }else if(objSelectUser.role == "10"){
        rolse[1].checked = true;
    }
}
