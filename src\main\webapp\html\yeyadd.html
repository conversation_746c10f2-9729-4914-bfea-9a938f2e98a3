<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <!--    <link rel="stylesheet" href="../plugin/layer/skin/layer.css">-->
    <link rel="stylesheet" href="../css/reset.css"/>
    <link rel="stylesheet" href="../css/style.css"/>
    <link rel="stylesheet" href="../css/icon.css"/>
    <link rel="stylesheet" href="../styles/tbstyles.css">
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../css/medical.css">
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <title>添加托幼机构</title>
    <style>
        .layui-form-label {
            width: 260px;
        }
    </style>
</head>
<body>
<div style="margin: 10px;">
    <form id="form" action="" class="layui-form">
        <section class="emergency-cen">
            <!--            <div id="divyeycode" class="layui-form-item" style="margin:0px 0  10px 0px;">-->
            <!--                <label class="layui-form-label"><em>*</em>申请管理人：</label>-->
            <!--                <div class="layui-input-block" style="margin:0 20px 0 94px;">-->
            <!--                    <input id="self" lay-verify="required" autocomplete="off" placeholder="" class="layui-input" type="text" style="display: inline-block;width:441px;" maxlength="20"/>-->
            <!--                </div>-->
            <!--            </div>-->
            <div class="layui-form-item" style="margin:0px 0  10px 0px ">
                <label class="layui-form-label"><em>*</em>托幼机构名称：</label>
                <div class="layui-input-block" style="margin:0 20px 0 94px;">
                    <input id="txtyeyname" lay-verify="required" autocomplete="off" placeholder="" class="layui-input" type="text" style="display: inline-block;width:441px;" maxlength="100"/>
                    <!--                    <button class="search-btn search-btn-md theme-bgc" id="btncheckyey" style="position: absolute;top: 70px;left: 550px;">查询开通</button>-->
                </div>
            </div>
            <div class="layui-form-item" style="margin:0px 0  10px 0px ">
                <label class="layui-form-label">托幼机构名称全拼：</label>
                <div class="layui-input-block" style="margin:0 20px 0 94px;">
                    <input id="txtsp" lay-verify="required" autocomplete="off" placeholder="" class="layui-input" type="text" style="display: inline-block;width:441px;" maxlength="200"/>
                </div>
            </div>
            <div class="layui-form-item" style="margin:0px 0  10px 0px ">
                <label class="layui-form-label">托幼机构名称简拼：</label>
                <div class="layui-input-block" style="margin:0 20px 0 94px;">
                    <input id="txtspe" lay-verify="required" autocomplete="off" placeholder="" class="layui-input" type="text" style="display: inline-block;width:441px;" maxlength="50"/>
                </div>
            </div>
            <div class="layui-form-item" style="margin:0px 0  10px 0px ">
                <label class="layui-form-label"><em>*</em>所属地区：</label>
                <div id="selareacode">
                    <div class="layui-input-inline" style="width: 140px;">
                        <select id="selprovince" name="province" lay-verify="required" class="province-selector" lay-filter="province-1">
                            <option value="">请选择省</option>
                        </select>
                    </div>
                    <div class="layui-input-inline" style="width: 140px;">
                        <select id="selcity" name="city" lay-verify="required" class="city-selector" lay-filter="city-1">
                            <option value="">请选择市</option>
                        </select>
                    </div>
                    <div class="layui-input-inline" style="width: 140px;">
                        <select id="selcounty" lay-verify="required" name="county" class="county-selector" lay-filter="county-1">
                            <option value="">请选择区</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-form-item" style="margin:0px 0  10px 0px ">
                <label class="layui-form-label"><em>*</em>托幼机构地址：</label>
                <div class="layui-input-block" style="margin:0 20px 0 94px;">
                    <input id="mapaddress" lay-verify="required" autocomplete="off" placeholder="输入详细地址" class="layui-input" type="text" style="display: inline-block;width:441px;" maxlength="200"/><em>&nbsp;<span class="location" id="mapmsg" style="color: #4C5E71;"><a class="theme-fgc">点击图标定位</a></span><a id="bmapicon" title="点击查看百度地址"></a></em>
                </div>
            </div>
            <div class="layui-form-item" style="margin:0px 0  10px 0px ">
                <label class="layui-form-label"><em>*</em>所属基层卫生服务机构：</label>
                <div class="layui-input-inline" style="width: 292px;">
                    <select id="selorgan" lay-filter="selorgan" lay-verify="required">
                    </select>
                </div>
            </div>
            <div class="layui-form-item" style="margin:0px 0  10px 0px ">
                <label class="layui-form-label"><em>*</em>托幼机构类型：</label>
                <div class="layui-input-inline" style="width: 292px;">
                    <select id="yeyType" lay-filter="yeyType" lay-verify="required">
                    </select>
                </div>
            </div>
<!--            <div id="divyey" style="display: none;">-->
                <div class="layui-form-item div_yey" style="margin:0 0 10px 0; display: none;">
                    <label class="layui-form-label"><em>*</em>机构所属关系：</label>
                    <div class="layui-input-inline" style="width: 292px;">
                        <select id="affiliation" lay-verify="required">
                            <option value="1">教育部门办园</option>
                            <option value="2">其他部门办园</option>
                            <option value="3">事业单位</option>
                            <option value="4">部队幼儿园</option>
                            <option value="5">集体办园</option>
                            <option value="6">地方企业</option>
                            <option value="7">民办幼儿园</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item div_yey" style="margin:0 0 10px 0; display: none;">
                    <label class="layui-form-label"><em>*</em>幼儿园等级：</label>
                    <div class="layui-input-inline" style="width: 292px;">
                        <select id="classify" lay-verify="required">
                            <option value="0">无等级</option><!--其他-->
                            <option value="1">一级</option>
                            <!--<option value="2">一级二类</option>-->
                            <!--<option value="3">一级三类</option>-->
                            <option value="4">二级</option>
                            <!--<option value="5">二级二类</option>-->
                            <!--<option value="6">二级三类</option>-->
                            <option value="7">三级</option>
                            <!--<option value="8">三级二类</option>-->
                            <!--<option value="9">三级三类</option>-->
                        </select>
                    </div>
                </div>
            <div class="layui-form-item div_yey" style="margin:0px 0  10px 0px; display: none;">
                <label class="layui-form-label"><em>*</em>是否通过招生前卫生评价：</label>
                <div class="layui-input-inline" style="width: 140px;">
                    <input type="radio" lay-skin="primary" lay-filter="radisnpassevaluate" lay-verify="radisnpassevaluate" name="radisnpassevaluate" value="1" title="是" checked>
                    <input type="radio" lay-skin="primary" lay-filter="radisnpassevaluate" lay-verify="radisnpassevaluate" name="radisnpassevaluate" value="2" title="否">
                    <input type="radio" lay-skin="primary" lay-filter="radisnpassevaluate" lay-verify="radisnpassevaluate" name="radisnpassevaluate" value="3" title="未做">
                </div>
            </div>
                <div id="divevaluatedate" class="layui-form-item div_yey" style="margin:0px 0  10px 0px; display: none;">
                    <label class="layui-form-label"><em>*</em>通过招生前评价时间：</label>
                    <div class="layui-input-inline" style="width: 140px;">
                        <input id="txtevaluatedate" type="text" style="width: 140px;" lay-verify="required" placeholder="" class="layui-input"/>
                        <img id="iconevaluatedate" src="../images/newicon/ico_calendar.png" style="cursor: pointer;position: relative;top: -29px;width: 20px;left: 115px;">
                    </div>
                </div>
<!--            </div>-->
<!--            <div id="divtuoyu" style="display: none;">-->
                <div class="layui-form-item div_tuoyu" style="margin:0 0 10px 0; display: none;">
                    <label class="layui-form-label"><em>*</em>机构性质：</label>
                    <div class="layui-input-inline" style="width: 292px;">
                        <input type="radio" lay-skin="primary" lay-filter="radyeynature" lay-verify="radioReqyeynature" name="radyeynature" value="1" title="营利性">
                        <input type="radio" lay-skin="primary" lay-filter="radyeynature" lay-verify="radioReqyeynature" name="radyeynature" value="2" title="非营利性">
                    </div>
                </div>
                <div class="layui-form-item div_tuoyu" style="margin:0 0 10px 0; display: none;">
                    <label class="layui-form-label"><em>*</em>是否为示范性托育机构：</label>
                    <div class="layui-input-inline" style="width: 292px;">
                        <input type="radio" lay-skin="primary" lay-filter="radisnexample" lay-verify="radioReqisnexample" name="radisnexample" value="1" title="是">
                        <input type="radio" lay-skin="primary" lay-filter="radisnexample" lay-verify="radioReqisnexample" name="radisnexample" value="0" title="否">
                    </div>
                </div>
                <div class="layui-form-item div_tuoyu" style="margin:0px 0  10px 0px; display: none;">
                    <label class="layui-form-label"><em>*</em>备案时间：</label>
                    <div class="layui-input-inline" style="width: 140px;">
                        <input id="txtfilingtime" type="text" style="width: 140px;" lay-verify="required" placeholder="" class="layui-input"/>
                        <img id="iconfilingtime" src="../images/newicon/ico_calendar.png" style="cursor: pointer;position: relative;top: -29px;width: 20px;left: 115px;">
                    </div>
                </div>
<!--            </div>-->
            <div class="layui-form-item div_isbeian" style="margin:0px 0  10px 0px;display: none;">
                <label class="layui-form-label"><em>*</em>是否通过教育部门或卫生行政部门备案：</label>
                <div class="layui-input-inline" style="width: 140px;">
                    <select id="isbeian" lay-verify="required">
                        <option value="">请选择</option>
                        <option value="0">否</option>
                        <option value="1">是</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item" style="margin:0px 0  10px 0px ">
                <label class="layui-form-label"><em>*</em>联系人：</label>
                <div class="layui-input-block" style="margin:0 20px 0 94px;">
                    <input id="legalperson" lay-verify="required" autocomplete="off" placeholder="" class="layui-input" type="text" style="display: inline-block;width:441px;" maxlength="30"/>
                </div>
            </div>
            <div class="layui-form-item" style="margin:0px 0  10px 0px;display: none; ">
                <label class="layui-form-label"><em>*</em>联系人角色：</label>
                <div class="layui-input-inline" style="width: 140px;">
                    <select id="legalpersonrole" lay-verify="required">
                    </select>
                </div>
            </div>
            <div class="layui-form-item" style="margin:0px 0  10px 0px ">
                <label class="layui-form-label"><em>*</em>联系电话：</label>
                <div class="layui-input-block" style="margin:0 20px 0 94px;">
                    <input id="phone" lay-verify="required|cusphone" autocomplete="off" placeholder="" class="layui-input" type="text" style="display: inline-block;width:441px;" maxlength="11"/>
                </div>
            </div>

            <!--                <div id="divpwd" class="layui-form-item" style="margin:0px 0  10px 0px ">-->
            <!--                    <label class="layui-form-label"><em>*</em>初始登录密码：</label>-->
            <!--                    <div class="layui-input-block" style="margin:0 20px 0 94px;">-->
            <!--                        <input id="rdmpwd" lay-verify="required" autocomplete="off" placeholder="" class="layui-input" type="text" style="display: inline-block;width:441px;" maxlength="20"/>-->
            <!--                        <input type="button" class="layui-btn" id="gener" value="随机生成"/><input type="button" class="layui-btn" id="defaultpwd" value="默认"/>-->
            <!--                    </div>-->
            <!--                </div>-->

            <!--                <div class="layui-form-item" style="margin:0px 0  10px 0px; display:none;">-->
            <!--                    <label class="layui-form-label">服务器地址：</label>-->
            <!--                    <div class="layui-input-inline" style="width: 140px;">-->
            <!--                        <select id="server" lay-verify="required" lay-reqText="尚未设置开通服务器，请先是设置开通服务器">-->
            <!--                        </select>-->
            <!--                    </div>-->
            <!--                </div>-->
            <!--                <div class="layui-form-item" style="margin:0px 0  10px 0px; display:none;">-->
            <!--                    <label class="layui-form-label"><em>*</em>开通版本：</label>-->
            <!--                    <div class="layui-input-inline" style="width: 140px;">-->
            <!--                        <select id="systemright" lay-verify="required" lay-reqText="未设置开通版本">-->
            <!--                        </select>-->
            <!--                    </div>-->
            <!--                </div>-->
            <input type="hidden" name="invofrom" value="1" id="kpdw"><!--开票单位-->
            <input type="hidden" id="txtorganid"><!--医院id-->
            <input type="hidden" id="txtjgname"><!--医院名称-->
        </section>
        <div style="display:none;">
            <div class="layui-input-block">
                <a id="btnok" class="layui-btn" lay-submit=lay-submit lay-filter="formOk">保存</a>
                <!--                    <a id="formOkapply" class="layui-btn" lay-submit=lay-submit lay-filter="formOkapply">保存并申请</a>-->
            </div>
        </div>
    </form>
</div>
<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../js/yeyadd.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>