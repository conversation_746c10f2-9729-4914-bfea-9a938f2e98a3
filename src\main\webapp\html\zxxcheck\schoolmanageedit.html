<!DOCTYPE html>
<html>
<head>
    <title>年度编辑</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="../../styles/xbcomstyle.css">
    <link rel="stylesheet" href="../../styles/tbstyles.css"/>
    <link rel="stylesheet" href="../../css/reset.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">

    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + top.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <style>
        .project-form {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
            gap: 10px;
        }

        .form-group {
            line-height: 35px;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
        }

        .label-text {
            margin-right: 10px;
            font-size: 15px;
            width: 80px;
            text-align: right;
            flex-shrink: 0;
        }

        .form-control {
            width: 200px;
            height: 35px;
            border: none;
            padding-left: 10px;
        }

        .layui-btn{
            margin: 30px 30px 0px 30px;
            background-color: #4A90E2;
        }

        .input-style{
            border: #cbd6e1 solid 1px;
        }
        .layui-form-label{
            width: 100px;
        }
    </style>
</head>
<body>
<div class="">
    <form class="project-form layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label" style="color:#778CA2;"><em>*</em>学校类型:</label>
            <div class="layui-input-inline" style="width: 310px;">
                <select lay-filter="yeytype" id="yeytype">
                    <option value="">请选择</option>
                    <option value="1">小学</option>
                    <option value="2">初中</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="color:#778CA2;"><em>*</em>学校名称:</label>
            <div class="layui-input-inline" style="width: 310px;">
                <input type="text" id="yeyname" name="yeyname" placeholder="请输入" class="layui-input" maxlength="60">
            </div>
        </div>
    </form>
</div>
<script data-main="../../js/zxxcheck/schoolmanageedit" src='../../sys/require.min.js'></script>
</body>
</html>
