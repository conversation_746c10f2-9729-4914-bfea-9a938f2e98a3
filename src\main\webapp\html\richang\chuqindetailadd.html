﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>报表条件</title>
    <link type="text/css" rel="stylesheet" href="../../css/reset.css" />
    <link type="text/css" rel="stylesheet" href="../../layui-btkj/css/layui.css" />
    <link rel="stylesheet" type="text/css" href="../../css/icon.css" />
    <link rel="stylesheet" type="text/css" href="../../css/commonstyle-layui-btkj.css" />
    <link rel="stylesheet" type="text/css" href="../../css/style.css" />
    <style type="text/css">
        /*滚动条样式*/
        html{
            color: #000;
            font-size: .7rem;
            font-family: "\5FAE\8F6F\96C5\9ED1",Helvetica,"黑体",Arial,Tahoma;
            height: 100%;
        }
        /*滚动条样式*/
        html::-webkit-scrollbar {
            display:none;
            width: 6px;
            height: 6px;
        }
        html:hover::-webkit-scrollbar{
            display:block;
        }
        html::-webkit-scrollbar-thumb {
            border-radius: 3px;
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
            border: 1px solid rgb(255, 255, 255);
            background: rgb(66, 66, 66);
        }
        html::-webkit-scrollbar-track {
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
            border-radius: 0;
            background: rgba(0,0,0,0.1);
        }

        .isview { margin-top: 0; }

        .layui-table-view { margin: 0px; }

        .webuploader-container { height: 120px; }

        .default-form .layui-form-label { width: 90px; color: #778CA2; }

        .layui-table td { border-width: 1px !important; }
        .form-display .layui-form-radio { margin: 0 0px 0 0; width: 33%; }
		.form-display .layui-form-checkbox i{margin: 2px 0;}
    </style>
</head>
<body>
    <div class="layui-page" style="padding: 0 15px;min-width: 400px;">
        <form id="form" action="" class="layui-form form-display" lay-filter="formOk">
            <div class="default-form" style="margin: 20px 10px;">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>日期：</label>
                        <div class="layui-input-block" style="min-height: 30px; margin-left: 10px; position: relative; float: left; width: 150px;">
                            <input autocomplete="off" class="layui-input alendar-padding" type="text" id="txtstartdate" placeholder="出勤日期" style="width: 150px; display: inline-block;" lay-verify="required" />
                            <i class="layui-alendar">
                                <img id="iconstartdate" src="../../images/cal_icon.png" style="width: 14px;" /></i>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>班级：</label>
                        <div class="layui-input-block">
                            <select id="selclass" lay-filter="selclass" lay-verify="required"></select>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>幼儿：</label>
                        <div id="divstulist" lay-verify="selstu">
                        </div>
                    </div>
                </div>
                <div style="display: none;">
                    <div class="layui-input-block">
                        <a id="btnok" class="layui-btn" lay-submit="lay-submit">立即提交</a>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <script type="text/javascript" data-main="../../js/richang/chuqindetailadd" src="../../sys/require.min.js"></script>
</body>
</html>
