/*新增日期: 2025.06.23
作 者: 朱辽原
修改时间：
修改人：
內容摘要: 家长预约详情
*/
require.config({
    paths: {
        system: '../../sys/system',
        jquery: '../../sys/jquery',
        layui: '../../layui-btkj/layui',
        dataSource: './enterData',
        commonUtils: './commonUtils'
    },
    shim: {
        system: {
            deps: ['jquery']
        },
        layui: {
            deps: ['jquery', 'system']
        }
    },
    waitSeconds: 0
})

const openMock = false

const baseModule = ['jquery', 'commonUtils', 'layui', 'system']

if (openMock) {
    baseModule.push('dataSource')
}

require(baseModule, ($, utils) => {
    const openLog = true
    layui.config().extend({ //设定模块别名
        system: '../../sys/system',
    });
    layui.use(['system', 'form', 'table', 'laydate'], function () {
        layer = layui.layer;
        form = layui.form;
        laydate = layui.laydate;
        // 获取路径参数
        const params = Arg.all();
        // 初始化日期选择器
        initDatePicker();
        // 初始化下拉框数据
        initSelectData();
        // 初始化事件
        initEvent();
        // 初始化表格
        initTable();

        /*
         * 初始化日期选择器
         */
        function initDatePicker() {
            const isoDate = new Date().toISOString();
            const nowDate = isoDate.split('T')[0];
            // 开始日期
            laydate.render({
                elem: '#startDate',
                type: 'date',
                value: params.startDate || nowDate
            });
            // 结束日期
            laydate.render({
                elem: '#endDate',
                type: 'date',
                value: params.endDate || nowDate
            });
            // 初始化开始时间选择器
            laydate.render({
                elem: '#startTime',
                type: 'time',
                format: 'HH:mm',
                value: params.startTime || '00:00'
            });
            // 初始化结束时间选择器
            laydate.render({
                elem: '#endTime',
                type: 'time',
                format: 'HH:mm',
                value: params.endTime || '23:59'
            });
        }

        /*
         * 初始化下拉框数据
         */
        function initSelectData() {
            // 预约科室数据
            if (openMock) {
                var deptData = [
                    { id: '', name: '请选择' },
                    { id: '儿科', name: '儿科' },
                    { id: '耳鼻喉科', name: '耳鼻喉科' },
                    { id: '眼科', name: '眼科' },
                    { id: '口腔科', name: '口腔科' },
                    { id: '保健科', name: '保健科' }
                ];
                var deptSelect = $('#deptName');
                deptSelect.empty();
                deptData.forEach(function (item) {
                    deptSelect.append('<option value="' + item.id + '">' + item.name + '</option>');
                });
                deptSelect.val(params.deptId || '');
            } else {
                $.sm(function (re1, err1) {
                    if (re1) {
                        utils.logWithCondition(openLog, new Error().stack, re1);
                        const deptList = re1
                        var deptSelect = $('#deptName');
                        deptSelect.empty();
                        deptSelect.append(`<option value=''>请选择</option>`);
                        deptList.forEach(function (item) {
                            deptSelect.append(`<option value='${item.id}'>${item.nname}</option>`);
                        });
                        utils.logWithCondition(openLog, new Error().stack, params);
                        deptSelect.val(params.deptId || '');
                    } else {
                        // jQuery.getparent().layer.msg(err1, { icon: 5 });
                    }
                }, ["depart.list"], null, null, { async: false });
            }

            // 取消预约
            var isCancelData = [
                { id: '', name: '请选择' },
                { id: '1', name: '是' },
                { id: '0', name: '否' }
            ];
            var isCancelSelect = $('#isCancel');
            isCancelSelect.empty();
            isCancelData.forEach(function (item) {
                isCancelSelect.append('<option value="' + item.id + '">' + item.name + '</option>');
            });
            utils.logWithCondition(openLog, new Error().stack, params)
            isCancelSelect.val(params.isCancel === '' ? '' : params.isCancel);

            // 区域分布数据
            var areaData = [
                { id: '', name: '请选择' },
                { id: '0~1', name: '1km以内' },
                { id: '1~3', name: '1km~3km' },
                { id: '3~5', name: '3km~5km' },
                { id: '5', name: '≥5km' }
            ];
            var areaSelect = $('#areaDistribution');
            areaSelect.empty();
            areaData.forEach(function (item) {
                areaSelect.append('<option value="' + item.id + '">' + item.name + '</option>');
            });
            areaSelect.val(params.areaId || '');

            // 性别
            var genderData = [
                { id: '', name: '请选择' },
                { id: '1', name: '男' },
                { id: '2', name: '女' }
            ];
            if (!openMock) {
                genderData = [
                    { id: '', name: '请选择' },
                    { id: '男', name: '男' },
                    { id: '女', name: '女' }
                ]
            }
            var parentGenderSelect = $('#parentGender');
            parentGenderSelect.empty();
            genderData.forEach(function (item) {
                parentGenderSelect.append('<option value="' + item.id + '">' + item.name + '</option>');
            });
            parentGenderSelect.val(params.parentGender || '');

            // 访问设备
            var deviceTypeData = [
                { id: '', name: '请选择' },
                { id: '安卓', name: '安卓' },
                { id: 'ios', name: 'ios' },
                { id: '未知', name: '未知' }
            ];
            var deviceTypeSelect = $('#deviceType');
            deviceTypeSelect.empty();
            deviceTypeData.forEach(function (item) {
                deviceTypeSelect.append(`<option value='${item.id}'>${item.name}</option>`);
            });
            deviceTypeSelect.val(params.deviceType || '');


            // 获取家长孩子关系数据
            $.sm(function (re1, err1) {
                if (re1) {
                    const relationList = re1.map(item => item.ptype)
                    var parentRelationSelect = $('#parentRelation');
                    parentRelationSelect.empty();
                    parentRelationSelect.append(`<option value="">请选择</option>`);
                    relationList.forEach(item => {
                        parentRelationSelect.append(`<option value="${item}">${item}</option>`);
                    });
                    parentRelationSelect.val(params.parentRelation || '');
                } else {
                    // jQuery.getparent().layer.msg(err1, { icon: 5 });
                }
            }, ["parentReservation.relation"], null, null, { async: false });

            // 幼儿性别
            var childGenderSelect = $('#childGender');
            childGenderSelect.empty();
            genderData.forEach(function (item) {
                childGenderSelect.append('<option value="' + item.id + '">' + item.name + '</option>');
            });
            childGenderSelect.val(params.childGender || '');

            form.render('select');
        }

        /*
         * 初始化事件
         */
        function initEvent() {
            $("#btnsearch").click(function () {
                btnsearch();
            });
            // 关闭按钮
            $("#closeBtn").click(function () {
                parent.layer.closeAll();
            });
        }

        /*
         * 初始化表格
         */
        function initTable() {
            var objwhere = getswhere();
            var arrcol = [ // 标题栏
                { field: 'id', type: "numbers", title: '序号', width: 100, align: 'left' },
                { field: 'fromtype', title: '来源', width: 200, align: 'center', templet: () => "家长主动预约" },
                { field: 'childclassify', title: '子分类', width: 100, align: 'center', templet: () => "/" },
                { field: 'childname', title: '就诊幼儿', width: 100, align: 'center' },
                {
                    field: 'childgender', title: '性别', width: 100, align: 'center', templet: function (d) {
                        if (openMock) {
                            return d.childgender === '1' ? '男' : '女';
                        }
                        return d.childgender
                    }
                },
                { field: 'birthdate', title: '出生日期', width: 200, align: 'center' },
                // {field: 'gardenname', title: '幼儿园', width: 300, align: 'center'},
                // { field: 'classname', title: '班级', width: 130, align: 'center' },
                { field: 'parentrelation', title: '家长身份', width: 130, align: 'center' },
                { field: 'parentname', title: '家长姓名', width: 130, align: 'center' },
                { field: 'parentgender', title: '性别', width: 130, align: 'center' },
                { field: 'parentage', title: '年龄', width: 100, align: 'center' },
                { field: 'address', title: '家庭住址', width: 300, align: 'center' },
                { field: 'phone', title: '家长手机号', width: 150, align: 'center' },
                { field: 'devicetype', title: '设备类型', width: 130, align: 'center' },
                { field: 'deptname', title: '预约科室', width: 200, align: 'center' },
                { field: 'appointmenttime', title: '预约挂号时间', width: 180 },
                {
                    field: 'iscancel', title: '是否取消预约', width: 130, align: 'center', templet: function (d) {
                        return d.iscancel === '3' ? '是' : '否';
                    }
                },
                { field: 'canceltime', title: '取消预约时间', width: 200, align: 'center' },
            ];
            layui.table.render({
                elem: '#laytable',
                url: encodeURI($.getLayUrl() + "?" + $.getSmStr([openMock ? "parentReservation.list" : "parentReservation.parentReservationInfo"])),
                height: 'full-180',
                where: {
                    swhere: $.msgwhere(objwhere),
                    fields: 'id',
                    types: 'asc',
                    page: 1,  // 初始页码
                    limit: 30 // 初始每页条数
                },
                cols: [arrcol],
                done: function (res, curr, count) {
                    // 表格加载完成后的回调
                    utils.logWithCondition(openLog, new Error().stack, '表格数据加载完成', res);
                },
                countNumberBool: true,
                even: true,
                page: true,
                skin: 'row', // 表格风格
                limits: [30, 50, 100, 200],
                limit: 30,
                skin: 'row'
            });

            // 监听分页事件
            layui.table.on('page(laytable)', function (obj) {
                utils.logWithCondition(openLog, new Error().stack, '分页切换', obj);
                var curr = obj.curr; // 当前页码
                var limit = obj.limit; // 每页条数
                // 重新加载表格
                layui.table.reload('laytable', {
                    where: {
                        swhere: $.msgwhere(getswhere()),
                        page: curr,
                        limit: limit
                    }
                });
            });
            layui.table.on('tool(laytable)', function (obj) {
                // 表格工具条事件处理
            });
        }

        /*
         * 功能：查询
         */
        function btnsearch() {
            var objwhere = getswhere();
            layui.table.reload('laytable', {
                page: { curr: 1 },
                where: {
                    swhere: $.msgwhere({ ...objwhere }),
                    fields: 'id',
                    types: 'asc'
                }
            });
        }

        /**
         * 查询条件
         * @returns {{}}
         */
        function getswhere() {
            var objwhere = {};
            utils.logWithCondition(openLog, new Error().stack, params)
            var startDate = $("#startDate").val() || params.startDate;
            var endDate = $("#endDate").val() || params.endDate;
            var gardenName = $("#gardenName").val();
            var deptId = $("#deptName").val() || params.deptId;
            var isCancel = $("#isCancel").val() || params.isCancel;
            var parentGender = $("#parentGender").val();
            var grade = $("#grade").val();
            var parentAge = $("#parentAge").val();
            var className = $("#className").val();
            var childGender = $("#childGender").val();
            var physicalAge = $("#physicalAge").val();
            var keyword = $("#keyword").val();
            var deviceType = $("#deviceType").val();
            var areaDistribution = $("#areaDistribution").val();
            var parentRelation = $("#parentRelation").val();
            var startTime = params.startTime || '00:00';
            var endTime = params.endTime || '23:59';

            if (gardenName) objwhere.gardenId = [gardenName];
            if (deptId) objwhere.deptId = [deptId];
            if (isCancel !== '') {
                if (isCancel == '1') {
                    objwhere.isCancel1 = 1
                } else if (isCancel == '0') {
                    objwhere.isCancel0 = 1
                }
            }
            if (parentGender !== '') objwhere.parentGender = [parentGender];
            if (grade) objwhere.grade = [grade];
            if (deviceType) objwhere.deviceType = [deviceType];
            if (openMock) {
                if (parentAge) objwhere.parentAge = [parentAge];
                if (areaDistribution) objwhere.areaId = [areaDistribution];
                // 处理日期时间组合
                if (startDate) {
                    if (startTime) {
                        objwhere.startDateTime = [`${startDate} ${startTime}:00`];
                    } else {
                        objwhere.startDateTime = [startDate];
                    }
                }
                if (endDate) {
                    if (endTime) {
                        objwhere.endDateTime = [`${endDate} ${endTime}:59`];
                    } else {
                        objwhere.endDateTime = [endDate];
                    }
                }
            } else {
                if (parentAge) {
                    objwhere.parentAge = [parentAge * 10, parentAge * 10 + 9];
                    if (parentAge == 8) {
                        objwhere.parentAge2 = [parentAge * 10];
                    }
                }
                if (areaDistribution && areaDistribution != '5') {
                    objwhere.area = [areaDistribution[0], areaDistribution[2]]
                } else if (areaDistribution == '5') {
                    objwhere.area2 = [areaDistribution]
                }
                if (startDate) objwhere.startDate = [startDate]
                if (endDate) objwhere.endDate = [endDate]
                if (startTime) objwhere.startTime = [startTime]
                if (endTime) objwhere.endTime = [endTime]
            }
            if (className) objwhere.className = [className];
            if (childGender !== '') objwhere.childGender = [childGender];
            if (physicalAge) objwhere.physicalAge = [physicalAge];
            if (keyword) objwhere.keyword = [keyword];
            if (parentRelation) objwhere.parentRelation = [parentRelation];

            utils.logWithCondition(openLog, new Error().stack, objwhere)
            return {
                ...objwhere
            };
        }
    });
})
