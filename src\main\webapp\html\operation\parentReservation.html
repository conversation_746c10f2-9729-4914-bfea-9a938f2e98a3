﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>家长预约大数据</title>
    <link rel="stylesheet" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../css/icon.css"/>
    <link rel="stylesheet" href="../../css/style.css"/>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css"/>
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css"/>
    <link rel="stylesheet" href="../../css/physical.css"/>
    <style type="text/css">
        html,
        body {
            background: #eaeff3;
        }

        .layui-form-label {
            padding: 8px 0px 5px 10px;
            width: auto;
        }

        #parentChildRelation {
            width: 100%;
            height: 90%;
        }

        .safestat-tit,
        .num-label {
            cursor: pointer;
            user-select: none;
            -webkit-user-select: none; /* Chrome, Safari, Opera */
            -moz-user-select: none; /* Firefox */
            -ms-user-select: none; /* Internet Explorer/Edge */
        }
        #avgAppointmentsPerParent {
            cursor: default;
        }
    </style>
</head>
<body>
<div class="marmain" style="min-width: 950px">
    <div class="content-medical">
        <div class="layui-form layui-comselect" style="position: relative">
            <div class="btn-nextfl">
                <span class="layui-form-label" style="text-align: left; padding-left: 0">预约时间：</span>
                <div class="layui-input-inline" style="float: left; width: 120px">
                    <input id="startDate" type="text" autocomplete="off" placeholder="选择开始日期" class="layui-input">
                </div>
                <div class="layui-form-mid">-</div>
                <div class="layui-input-inline layui-form isshowenrollstatus" style="float: left; width: 120px">
                    <input id="endDate" type="text" autocomplete="off" placeholder="选择结束日期" class="layui-input">
                </div>
                <div class="layui-input-inline layui-form isshowenrollstatus" style="float: left; width: 120px">
                    <button class="layui-btn form-search" style="margin-left: 5px; vertical-align: top;" id="btnSearch">查询</button>
                </div>
            </div>
        </div>
    </div>
    <div id="content" style="width: 100%">
        <div class="marmain-cen">
            <div class="cenmartop">
                <div class="conference-sta">
                    <div class="total-div" style="margin-left: 0">
                        <img src="../../images/physical/homekindergarten01.png" class="kindergartenimg"/>
                        <div class="total-panel">
                            <div class="total-left">
                                <img src="../../images/physical/homekindergarten01img.png" class="kinder-ltimg"/>
                                <div class="total-numbg">
                                    <div class="num-label" id="appointmentSuccess">0</div>
                                    <div class="num-peo">预约成功</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="total-div">
                        <img src="../../images/physical/homekindergarten02.png" class="kindergartenimg"/>
                        <div class="total-panel">
                            <div class="total-left">
                                <img src="../../images/physical/homekindergarten02img.png" class="kinder-ltimg"/>
                                <div class="total-numbg">
                                    <div class="num-label" id="appointmentCancellation">0</div>
                                    <div class="num-peo">取消预约</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="total-div">
                        <img src="../../images/physical/homekindergarten06.png" class="kindergartenimg"/>
                        <div class="total-panel">
                            <div class="total-left">
                                <img src="../../images/physical/homekindergarten06img.png" class="kinder-ltimg"/>
                                <div class="total-numbg">
                                    <div class="num-label" id="appointmentsDepartment">0</div>
                                    <div class="num-peo">约数科室数</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="total-div">
                        <img src="../../images/physical/homekindergarten03.png" class="kindergartenimg"/>
                        <div class="total-panel">
                            <div class="total-left">
                                <img src="../../images/physical/homekindergarten03img.png" class="kinder-ltimg"/>
                                <div class="total-numbg">
                                    <div class="num-label" id="parentCount">0</div>
                                    <div class="num-peo">家长数</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="total-div">
                        <img src="../../images/physical/homekindergarten04.png" class="kindergartenimg"/>
                        <div class="total-panel">
                            <div class="total-left">
                                <img src="../../images/physical/homekindergarten04img.png" class="kinder-ltimg"/>
                                <div class="total-numbg">
                                    <div class="num-label" id="childCount">0</div>
                                    <div class="num-peo">幼儿数</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="total-div">
                        <img src="../../images/physical/homekindergarten05.png" class="kindergartenimg"/>
                        <div class="total-panel">
                            <div class="total-left">
                                <img src="../../images/physical/homekindergarten05img.png" class="kinder-ltimg"/>
                                <div class="total-numbg">
                                    <div class="num-label" id="avgAppointmentsPerParent">0</div>
                                    <div class="num-peo">家长人均预约数</div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="visiting-statis">
                    <div class="safestat-cen">
                        <div class="safestat-tit"><i class="left-tit"></i><span>科室预约量对比</span></div>
                        <div class="colunwit" id="departmentappointments"></div>
                    </div>
                    <div class="safestat-cen">
                        <div class="safestat-tit"><i class="left-tit"></i><span>家长预约与取消情况</span></div>
                        <div class="colunwit" id="cancelappointment"></div>
                    </div>
                </div>
                <div class="visiting-statis">
                    <div class="safestat-cen">
                        <div class="safestat-tit"><i class="left-tit"></i><span>家长预约时段分布（24小时）</span></div>
                        <div class="colunwit" id="timeappointment"></div>
                    </div>
                    
                </div>
                <div class="visiting-statis">
                    <div class="safestat-cen heighrow">
                        <div class="safestat-tit"><i class="left-tit"></i><span>家长画像</span></div>
                        <div class="colunwit">
                            <div class="weui-flex hit50">
                                <div class="columnlist" id="parentGender">
                                    <h3>性别</h3>
                                    <div></div>
                                </div>
                                <div class="columnlist" id="parentDevice">
                                    <h3>访问设备</h3>
                                    <div></div>
                                </div>
                            </div>
                            <div class="weui-flex hit50">
                                <div class="columnlist" id="parentAge">
                                    <h3>年龄分布</h3>
                                    <div></div>
                                </div>
                                <div class="columnlist" id="parentRegion">
                                    <h3>区域分布</h3>
                                    <div></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="visiting-statis">
                    <div class="safestat-cen">
                        <div class="safestat-tit"><i class="left-tit"></i><span>孩子与家长关系</span></div>
                        <div id="parentChildRelation"></div>
                    </div>
                </div>
                <div class="visiting-statis">
                    <div class="safestat-cen">
                        <div class="safestat-tit"><i class="left-tit"></i><span>幼儿画像</span></div>
                        <div class="colunwit weui-flex">
                            <div class="columnlist" id="childrenGender">
                                <h3>性别</h3>
                                <div></div>
                            </div>
                            <div class="columnlist" id="childrenAge">
                                <h3>年龄分布</h3>
                                <div></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" data-main="../../js/operation/parentReservation.js" src="../../sys/require.min.js"></script>
<!-- <script type="text/javascript" data-main="../../js/operation/parentReservationMock.js" src="../../sys/require.min.js"></script> -->
</body>
</html>
