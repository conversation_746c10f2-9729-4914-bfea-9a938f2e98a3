﻿/*
日期： 
作者：
內容摘要:贫血管理
*/
var fpsetting = {
    arrcard: [], //学生的学号  用于上一个下一个
    curstuname: "",
    strsex: '',
    strbirthday: '',
    curstuno: ''//当前学生的学号
    , objtjinfo: {}
    , arrnopxtxt: []//血红蛋白评价需包含贫血字样的评价
};

require.config({
    paths: {
        sys: "../../sys/system",
        jquery: "../../sys/jquery",
        editselect: "../../plugin/editselect/js/editselect",
        validator: "../../plugin/validator/js/validator",
        wdatepicker: "../../plugin/wdatepicker/wdatepicker"
    }, shim: {},
    waitSeconds: 0
});
var parentobj =null;
require(["jquery", "sys"], function () {
    require(["editselect", "validator", "wdatepicker"], function () {
        parentobj = jQuery.getparent().$("#win_" + Arg('mid')).children('iframe')[0].contentWindow;
        fpsetting.arrnopxtxt = parentobj.tongsetting.objhematinstandard && parentobj.tongsetting.objhematinstandard.arrnopxtext || fpsetting.arrnopxtxt;
        fpsetting.stuno = Arg("stuno");
        initclassdata(fpsetting.stuno);
        initselect1();
        $('#chkvest').click(function () {
            check(1);
        });
        $("#btnsave").click(function (event, callback) {
            btnsave(fpsetting.stuno, callback);
        });
        $('#txtendtime').prop('disabled', true);
        $("#txtmanagetime").click(function () {
            WdatePicker({skin: 'whyGreen'});
        });
        $("#txtendtime").click(function () {
            WdatePicker({skin: 'whyGreen', onpicking: function (dp) {
                    var age = parentobj.GetAge(fpsetting.strbirthday, dp.cal.getNewDateStr());
                    strhematin('end', 1);
                }});
        });
        $("#txtweek,#txtdosage,#txttreatment,#txtfefoodage").blur(function () {
            check(2, this);
        });
        $("#txtmohematinval").blur(function () {
            check(3, this);
        });
    });
});

/*
功能：初始化下拉框  
*/
function initselect1() {
    var selmt = $("#selmt");
    selmt.append('<option value="">请选择</option>');
    // if(parentobj.tongsetting.qdhematin == 1) {//轻度贫血也进入专案
    selmt.append('<option value="轻度">轻度贫血</option>');
    // }
    selmt.append('<option value="中度">中度贫血</option><option value="重度">重度贫血</option>');
    selmt.editableSelect({//监测库类型
        bg_iframe: true,
        isreadonly: 1,
        width: '60px',
        case_sensitive: false, // If set to true, the user has to type in an exact
        items_then_scroll: 10// If there are more than 10 items, display a scrollbar
    });
};

function check(type, t) {
    if (type == 1) {
        if ($("#chkvest")[0].checked) {
            $('#txtendtime').attr('require', true);
            $('#txtendtime').prop('disabled', false);
            $('#lbvest').html('<input id="txtvest0" name="radvest" type="radio" value="0" checked="checked"/><label for="txtvest0">痊愈</label>\
	                <input id="txtvest1" name="radvest" type="radio" value="1"/><label for="txtvest1">好转</label>\
	                <input id="txtvest2" name="radvest" type="radio" value="2"/><label for="txtvest2">未愈</label>\
	                <input id="txtvest3" name="radvest" type="radio" value="3"/><label for="txtvest3">失访</label>\
	                <input id="txtvest3" name="radvest" type="radio" value="4"/><label for="txtvest4">离园</label>\
	        		<label style="margin-left:20px;">血红蛋白：<input type="text" id="txtendhematin" onchange="strhematin(\'end\',1);" style="width: 50px;"/>g/L</label>\
	        		<label style="margin-left:20px;">评价：<label id="txtendhematinvalue"></label></label>').show();
        } else {
            $('#txtendtime').attr('require', false);
            $('#txtendtime').prop('disabled', true);
            $('#txtendtime').val("");
            $('#lbvest').hide().html('');
        }
    } else {
        var v = $(t).val();
        if (isNaN(v)) {
            jQuery.getparent().layer.msg("请输入数字！");
            $(t).val("");
            return;
        }
        if (v < 0) {
            jQuery.getparent().layer.msg("请输入正确的数字！");
            $(t).val("");
            return;
        }
        if (type == 3 && Math.floor(v) < 20 || Math.floor(v) >= 1000) {//母孕期贫血情况  血红蛋白值
            jQuery.getparent().layer.msg('您输入的血红蛋白不符合常规,请重新输入!', function (r) {
                jQuery.getparent().layer.close(r);
            });
            $(t).val("");
        }
    }
};

/*
功能：加载数据
*/
function initclassdata(stuno) {
    $("#divtbody").html("");
    jQuery.getparent().layer.load();
    var strwhere = {};
    $.sm(function (re, err) {
        if (err) {
            jQuery.getparent().layer.msg(err, {icon: 5});
            jQuery.getparent().layer.closeAll('loading');
        } else if (re && re[0]) {
            fpsetting.curstuno = re[0][0];
            fpsetting.strbirthday = re[0][4];
            fpsetting.strsex = re[0][2];
            fpsetting.hest = re[0][15];
            fpsetting.curstuname = re[0][1];
            fpsetting.yeyid = re[0][25];
            // fpsetting.stuno = re[0][3];
            $("#txtname").html(re[0][1]);
            $("#txtsex").html(re[0][2]);
            $("#txtcredentialsnum").html(re[0][3]);
            $("#txtbirthday").html(re[0][4]);
            $("#txtmanagetime").val(re[0][5]);//开始管理日期
            $("#txtweek").val(re[0][6]);//母孕期贫血情况周
            $("#txtmohematinval").val(re[0][7]);//母孕期贫血情况血红蛋白值
            $('input[name="radisferralia"][value="' + (re[0][8] || 0) + '"]').attr("checked", true);//铁剂治疗
            $("#txtmodrugs").val(re[0][9]);//药物
            $("#txtdosage").val(re[0][10]);//剂量
            $("#txttreatment").val(re[0][11]);//疗程
            $('input[name="radbreastmilk"][value="' + (re[0][12] || 1) + '"]').attr("checked", true);//母乳喂养情况
            $("#txtfefoodage").val(re[0][13]);//儿童开始添加含铁食物年龄
            $("#selmt").val(re[0][15]);
            $("#chkvest").val(re[0][16]);//
            if (re[0][16] == '1') {//是否结案1是0否
                $("#lbvest").show();
                $('#chkvest').prop('checked', true);
                $('input[name="radvest"][value="' + (re[0][20] || 0) + '"]').attr("checked", true);//转归
                $('#txtendtime').attr('require', true);
                $('#txtendtime').prop('disabled', false);
                $('#txtendtime').val(re[0][17]);//结案日期
                $('#txtendhematin').val(re[0][18]);//
                $('#txtendhematinvalue').text(($.inArray(re[0][19], fpsetting.arrnopxtxt) >= 0 ? re[0][19] + "贫血" : re[0][19]) || "").attr("endhematinvalue", re[0][19]);//
            } else {
                $('#chkvest').prop('checked', false);
                $('#txtendtime').attr('require', false);
                $('#txtendtime').prop('disabled', true);
                $('#txtendtime').val("");
                $("#lbvest").hide();
            }
            $('#txtendtime').val(re[0][17]);//结案日期
            $('input[name="radvest"][value="' + (re[0][20] || 0) + '"]').attr("checked", true);//转归
            $('#txtfeedhistory').val(re[0][21]);//患儿既往病史、喂养（饮食情况）
            $("#txthematin").val(re[0][22]);
            $("#txthematinvalue").val($.inArray(re[0][23], fpsetting.arrnopxtxt) >= 0 ? re[0][23] + "贫血" : re[0][23]).attr("hematinvalue", re[0][23]);
            $('#txtremark').val(re[0][26]);//备注
            init(stuno);
        } else {
            jQuery.getparent().layer.msg("未查到有关数据！");
            jQuery.getparent().layer.closeAll('loading');
        }
    }, ["tanemiacard.initcladata", stuno, $.msgwhere(strwhere)]);
};

/*
功能：读取管理卡
*/
function init(stuno) {
    var strwhere = {};
    $.sm(function (re, err) {
        if (err) {
            jQuery.getparent().layer.msg(err, {icon: 5});
            jQuery.getparent().layer.closeAll('loading');
        } else {
            var obj = {};
            for (var i = 0; i < re.length; i++) {
                obj = re[i];
                fpsetting.objtjinfo[obj.id] = obj;
                setHtml(obj);
            }
            setHtml();
            jQuery.getparent().layer.closeAll('loading');
        }
    }, ["tanemiacard.init", stuno, $.msgwhere(strwhere)]);/*, strwhere*/
};

/*
设置肥胖明细记录行
*/
function setHtml(obj) {
    if(!obj){
        obj = {};
    }
    //id,managetime,age,hest,hematin,hematinvalue,problem,treatment,guidance,doctorname,jgid,yeyid,redcell,spirit,complexion,appetite,cissa,viscus
    var len = $("#divtbody")[0].rows.length;
    var fno = len ? $($("#divtbody")[0].rows[len - 1]).attr('fno') : 0;
    fno = fno ? Math.floor(fno) + 1 : 1;
    var arrstr = ['<tr fno=' + fno + ' ftime="' + (obj.managetime ? obj.managetime : '') + '"><td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;padding:0 5px;">'];
    arrstr.push(obj.id ? '<input type="button" onclick="delrowevent(this,' + fno + ',' + obj.id + ');" id="btdelete' + fno + '" value="删除"  class="btn"  style=" margin-bottom:2px"/>' : '&nbsp;');
    arrstr.push('</td><td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;padding:0 5px;"><input type="text" id="txtdate' + fno + '" style="width: 95px;" class="Wdate" value="' + (obj.managetime ? obj.managetime : '') + '" onclick="initdate(' + fno + ');" onblur="changeDate(' + fno + ');" /></td>');//检查日期
    arrstr.push('<td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;padding:0 5px;"><label id="txtage' + fno + '" age="' + (obj.managetime ? parentobj.GetAge(fpsetting.strbirthday, obj.managetime) : "") + '">' + (obj.managetime ? parentobj.GetAge(fpsetting.strbirthday, obj.managetime, "zh") : "") + '</label>&nbsp;</td>');
    //精神
    arrstr.push('<td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">');
    arrstr.push('<select id="selspirit' + fno + '"><option value="">请选择</option><option value="1">好</option><option value="2">差</option></select>');
    arrstr.push('</td>');
    //面色
    arrstr.push('<td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">');
    arrstr.push('<select id="selcomplexion' + fno + '"><option value="">请选择</option><option value="1">黄</option><option value="2">苍白</option><option value="3">正常</option></select>');
    arrstr.push('</td>');
    //食欲
    arrstr.push('<td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">');
    arrstr.push('<select id="selappetite' + fno + '"><option value="">请选择</option><option value="1">好</option><option value="2">差</option></select>');
    arrstr.push('</td>');
    //异食癖
    arrstr.push('<td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">');
    arrstr.push('<input id="txtcissa' + fno + '" value="' + (obj && obj.cissa ? obj.cissa : '') + '" type="text" style="width:90%;" maxlength="10" class="tjzagl_input" /><span class="FancyInput__bar___1P3wW" ></span></td>');
    //心肺肝脾
    arrstr.push('<td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">');
    arrstr.push('<input id="txtviscus' + fno + '" value="' + (obj && obj.viscus ? obj.viscus : '') + '" type="text" style="width:90%;" maxlength="50" class="tjzagl_input" /><span class="FancyInput__bar___1P3wW" ></span></td>');
    //血红蛋白
    arrstr.push('<td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;padding:0 5px;">');
    arrstr.push('<input type="text" id="txthematin' + fno + '" onchange="strhematin(' + fno + ',1);" value="' + (obj && obj.hematin ? obj.hematin : '') + '" style="width:95%;" class="tjzagl_input" />  <span class="FancyInput__bar___1P3wW" ></span>');
    //评价
    var pxpj = obj && obj.hematinvalue ? ($.inArray(obj.hematinvalue, fpsetting.arrnopxtxt) >= 0 ? obj.hematinvalue + "贫血" : obj.hematinvalue) : "";
    arrstr.push('</td><td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;"><label id="txthematinvalue' + fno + '" hematinvalue' + fno + '="' + (obj && obj.hematinvalue) + '">' + pxpj + '</label></td>');
    //红细胞
    arrstr.push('<td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">');
    arrstr.push('<input id="txtredcell' + fno + '" onchange="strredcell(' + fno + ',1);" value="' + (obj && obj.redcell ? obj.redcell : '') + '" type="text" style="width:90%;" maxlength="10" class="tjzagl_input" /><span class="FancyInput__bar___1P3wW" ></span></td>');
    // //存在问题
    // arrstr.push('<td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">');
    // arrstr.push('<input id="txtproblem' + fno + '" value="' + (obj && obj.problem ? obj.problem : '') + '" type="text" style="width:90%;" maxlength="500" class="tjzagl_input" /><span class="FancyInput__bar___1P3wW" ></span></td>');
    //干预治疗
    arrstr.push('<td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">');
    arrstr.push('<textarea id="txttreatment' + fno + '" style="width:95%;" maxlength="500" class="tjzagl_textarea">' + (obj && obj.treatment ? obj.treatment : '') + '</textarea><span class="FancyInput__bar___1P3wW" ></span></td>');
    //签名
    arrstr.push('<td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">');
    arrstr.push('<input id="txtdoctorname' + fno + '" value="' + (obj && obj.doctorname ? obj.doctorname : '') + '" type="text" style="width:90%;" maxlength="20" class="tjzagl_input" /><span class="FancyInput__bar___1P3wW" ></span></td>');
    arrstr.push('</tr>');
    $("#divtbody").append(arrstr.join(""));
    $('#selspirit' + fno).val(obj && obj.spirit || '');
    $('#selcomplexion' + fno).val(obj && obj.complexion || '');
    $('#selappetite' + fno).val(obj && obj.appetite || '');
};

/*
功能：删除功能
*/
function delrowevent(obj, num, id) {
    jQuery.getparent().layer.confirm('请确认是否真的进行删除操作？', function (r) {
        $.sm(function (re, err) {
            if (err) {
                jQuery.getparent().layer.msg(err, {icon: 5});
            } else {
                $(obj).closest('tr').remove();
            }
            jQuery.getparent().layer.close(r);
        }, ["tanemiacard.delmanage", id]);
    });
};

/*
功能：初始化日期
*/
function initdate(num) {
    WdatePicker({
        skin: 'whyGreen', minDate: Arg("sdate"), maxDate: Arg("edate"), onpicking: function (dp) {
            var age = parentobj.GetAge(fpsetting.strbirthday, dp.cal.getNewDateStr());
            $("#txtage" + num).html(parentobj.getZhAgeByAge(age)).attr("age", age);
        }, onpicked: function (dp) {
            strhematin(num, 1);
        }
    });
}

function changeDate(num) {
    var strdate = $("#txtdate" + num).val()
    var age = parentobj.GetAge(fpsetting.strbirthday, strdate);
    $("#txtage" + num).html(parentobj.getZhAgeByAge(age)).attr("age", age);
}

function strredcell(num, type) {
    var txtredcell = 'txtredcell' + num;
    if (type == 1) {//血红蛋白
        var redcell = $("#" + txtredcell).val();
        if (isNaN(redcell)) {
            jQuery.getparent().layer.msg('请输入数字！', function (r) {
                jQuery.getparent().layer.close(r);
            });
            $("#" + txtredcell).val("");
            $("#" + txtredcell).focus();
        }
    }
};
/*
功能：
*/
function strhematin(num, type) {
    var strhematinid = isNaN(num) ? 'txt' + num + 'hematin' : 'txthematin' + num;
    var strhematinvalueid = isNaN(num) ? 'txt' + num + 'hematinvalue' : 'txthematinvalue' + num;
    var hematinvalueid = isNaN(num) ? num + 'hematinvalue' : 'hematinvalue' + num;
    var strmanagetime = isNaN(num) ? 'txt' + num + 'time' : 'txtdate' + num;
    var len = $("#divtbody")[0].rows.length - 1;
    var fno = $($("#divtbody")[0].rows[len]).attr("fno");
    var managetime = $("#" + strmanagetime).val();
    var age = (managetime ? parentobj.GetAge(fpsetting.strbirthday, managetime) : "");
    if (type == 1) {//血红蛋白
        var hematin = $("#" + strhematinid).val();
        if (isNaN(hematin)) {
            jQuery.getparent().layer.msg('请输入数字！', function (r) {
                jQuery.getparent().layer.close(r);
            });
            $("#" + strhematinid).val("");
            $("#" + strhematinid).focus();
        } else if (Math.floor(hematin) < 20 || Math.floor(hematin) >= 1000) {
            jQuery.getparent().layer.msg('您输入的血红蛋白不符合常规,请重新输入!', function (r) {
                jQuery.getparent().layer.close(r);
            });
            $("#" + strhematinid).val("");
            $("#" + strhematinid).focus();
        } else {
            hematin = parentobj.getOneFloat(hematin);
            var pxpj = parentobj.GetHematin(hematin, age, parentobj.tongsetting.hematinstandard);
            $("#" + strhematinid).val(hematin);
            $("#" + strhematinvalueid).text($.inArray(pxpj, fpsetting.arrnopxtxt) >= 0 ? pxpj + "贫血" : pxpj).attr(hematinvalueid, pxpj);
            if (fno == num)
                setHtml();
        }
    }
};

/*
功能：保存  
*/
function btnsave(stuno, cb) {
    if (!Validator.Validate($('#tabcard0').val(), 2)) {
        return;
    }
    jQuery.getparent().layer.load();
    var arrpm = [];
    var len = $("#divtbody")[0].rows.length;
    var fno = $($("#divtbody")[0].rows[len - 2]).attr("fno");
    // var stuno = $("#txtstuno").html();
    var endtime = $("#txtendtime").val();
    var txtweek = $.trim($("#txtweek").val()) || 'null',
        txtmohematinval = $.trim($("#txtmohematinval").val()) || 'null',//母孕期贫血情况值
        radisferralia = $('input[name="radisferralia"]:checked').val() || 'null',//铁剂治疗
        txtmodrugs = $.trim($("#txtmodrugs").val()),//药物
        txtdosage = $.trim($("#txtdosage").val()) || 'null',//剂量
        txttreatment = $.trim($("#txttreatment").val()) || 'null',//疗程
        radbreastmilk = $('input[name="radbreastmilk"]:checked').val(),//母乳喂养情况
        txtfefoodage = $.trim($("#txtfefoodage").val()) || 'null',//儿童开始添加含铁食物年龄
        txtfeedhistory = $.trim($("#txtfeedhistory").val()),//患儿既往病史、喂养（饮食情况）
        txtremark = $.trim($("#txtremark").val()),//患儿既往病史、喂养（饮食情况）
        chkvest = $("#chkvest")[0].checked ? 1 : 0,//是否结案
        txtmanagetime = {datevalue: [$("#txtmanagetime").val() || ""]},//开始管理日期
        txtendtime = {datevalue: [endtime || ""]},//结案日期
        txtendhematin = $.trim($("#txtendhematin").val()) || 'null',//结案血红蛋白值
        txtendhematinvalue = $("#txtendhematinvalue").attr("endhematinvalue"),//结案评价
        radvest = 'null',//转归
        selmt = $("#selmt").val();//监测库类型
    // txtendhematinvalue = txtendhematinvalue.replace("贫血", "");
    if (chkvest == 1) {
        radvest = $('input[name="radvest"]:checked').val();//转归
        if (!txtendhematin) {
            jQuery.getparent().layer.msg('请输入转归血红蛋白值！');
            jQuery.getparent().layer.closeAll('loading');
            return;
        }
        if (!endtime) {
            jQuery.getparent().layer.msg('请输入结案日期！');
            jQuery.getparent().layer.closeAll('loading');
            return;
        }
    }
    var curhematinvalue = '';
    var strtip = '', arrdate = [];
    for (var i = 0; i < len; i++) {
        var num = $($("#divtbody")[0].rows[i]).attr("fno");
        var fno1 = $($("#divtbody")[0].rows[i]).attr("ftime");//检查时间
        var date = $("#txtdate" + num).val();//检查日期
        if (date && !parseFloat($.trim($("#txthematin" + num).val()))) {//有日期，没有血红蛋白
            strtip += '第' + (i + 1) + '行信息不完整，数据将不会被处理</br>';
        } else if (parseFloat($.trim($("#txthematin" + num).val())) && !date) {//有血红蛋白，没有日期
            strtip += '第' + (i + 1) + '行信息不完整，数据将不会被处理</br>';
        }
        if (!parseFloat($.trim($("#txthematin" + num).val()))) {
            continue;
        }
        if (date && $.inArray(date, arrdate) >= 0) {
            jQuery.getparent().layer.msg("第" + (i + 1) + "行日期" + date + "与其它行日期重复，请检查！", {area: '400px'});
            jQuery.getparent().layer.closeAll('loading');
            return;
        } else if (date) {
            arrdate.push(date);
        }
        if (date != "" && $("#txthematin" + num).val() != "") {
            curhematinvalue = $("#txthematinvalue" + num).attr("hematinvalue" + num);
            // curhematinvalue = curhematinvalue.replace("贫血", "");
            var arr = ["tanemiacard.addmanage", fpsetting.curstuno, date, $("#txtage" + num).attr("age"), $("#txthematin" + num).val(), curhematinvalue, $("#txtproblem" + num).val(), $("#txttreatment" + num).val(), $("#txtguidance" + num).val(), $("#txtdoctorname" + num).val(), (fno1 ? fno1 : date), fpsetting.stuno, jQuery.getparent().objdata.my.organid || 'null', fpsetting.yeyid || 'null', 'ry', $("#txtredcell" + num).val() || 'null',$("#selspirit" + num).val(),$("#selcomplexion" + num).val(),$("#selappetite" + num).val(),$("#txtcissa" + num).val(),$("#txtviscus" + num).val()];
            arrpm.push(arr);
        }
    }
    //birthday,stusex,claname,week,mohematin,isferralia,modrugs,dosage,treatment,breastmilk,fefoodage,feedhistory,idend,managetime,endtime,vest,mt,endhematin,rank,hematin,hematinvalue
    arrpm.push(["tanemiacard.addhematin", fpsetting.stuno, txtweek, txtmohematinval, radisferralia, txtmodrugs, txtdosage, txttreatment, radbreastmilk, txtfefoodage, txtfeedhistory, chkvest, $.msgwhere(txtmanagetime), $.msgwhere(txtendtime), radvest, selmt, txtendhematin, txtendhematinvalue, fpsetting.curstuno, fpsetting.strbirthday, fpsetting.strsex, $.trim($("#txthematin").val()), $("#txthematinvalue").attr("hematinvalue"), jQuery.getparent().objdata.my.organid, fpsetting.yeyid || 'null', txtremark, curhematinvalue]);
    // stuno,stuname,stusex,stuno,endtime,endheight,endweight,rank,wrank,endhematin,vest,mt,casetype,jgid,yeyid
    if (chkvest == 1) {
        arrpm.push(["tanemiacard.addclosecaserecord", fpsetting.curstuno, fpsetting.curstuname, fpsetting.strsex, fpsetting.stuno, endtime, txtendhematin, txtendhematinvalue, radvest, selmt, 3, jQuery.getparent().objdata.my.organid || 'null', fpsetting.yeyid || 'null']);
    }
    if (strtip) {
        jQuery.getparent().layer.confirm(strtip + '请确定是否真的进行保存操作?', function (r) {
            btsavesm(arrpm, stuno, r, cb);
        }, function (r) {
            jQuery.getparent().layer.closeAll('loading');
        });
    } else {
        btsavesm(arrpm, stuno, null, cb);
    }
};

function btsavesm(arrpm, stuno, r, cb) {
    $.sm(function (re, err) {
        if (err) {
            jQuery.getparent().layer.msg(err, {icon: 5});
            jQuery.getparent().layer.closeAll('loading');
        } else {
            if (stuno) {
                $('#txtendtime').attr('require', true);
                $('#txtendtime').prop('disabled', false);
                // initclassdata(stuno);
            }
            jQuery.getparent().layer.closeAll('loading');
            jQuery.getparent().layer.msg('保存成功！');
            cb && cb();
            if (r)
                jQuery.getparent().layer.close(r);
        }
    }, arrpm, null, null, null, null, 1);
}

/*
功能：打印管理卡
*/
function btnprint() {
    jQuery.getparent().objdata.tanemiaindex = jQuery.getparent().layer.open({
        type: 2,
        title: "贫血报表",
        shadeClose: false,
        area: ['100%', '100%'],
        content: "html/zhuanan/tanemiareport.html?v=" + (Arg("v") || 1) + "&mid=" + Arg("mid") + "&stno=" + fpsetting.curstuno + "&stuno=" + fpsetting.stuno + (Arg("sdate") ? "&sdate=" + Arg("sdate") : "") + (Arg("edate") ? "&edate=" + Arg("edate") : "") + "&type=3",
        btn: ["打印预览", "导出Excel", "关闭"],
        success: function (layero, index) {
//			jQuery.getparent().objdata.jgjsframe = layero.find('iframe')[0]; //得到iframe页的窗口对象，执行iframe页的方法：iframeWin.method();
        }, yes: function (index, layero) {
            layero.find('iframe')[0].contentWindow.$('#btprint').trigger('click');
        }, btn2: function (index, layero) {
            layero.find('iframe')[0].contentWindow.$('#btexcel').trigger('click');
            return false;
        }
    });
    //jQuery.getparent().layer.full(jQuery.getparent().objdata.jcjgbindex);
};
