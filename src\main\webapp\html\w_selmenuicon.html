﻿<!DOCTYPE html>
<html>
<head>
    <title>选择图标</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <link href='../css/reset.css' rel='stylesheet'/>
    <link href='../css/style.css' rel='stylesheet'/>
</head>
<body>
<div id="divform" class="selImg"></div>
<script type="text/javascript" src="../sys/jquery.js"></script>
<script type="text/javascript" src="../sys/arg.js"></script>
<script type="text/javascript" src="../layui-btkj/layui.js"></script>
<script type="text/javascript" src="../sys/system.js"></script>
<script>
    var rootimgurl="/images/function";
    var objicon = {//键是文件夹名 或 文件名
        objfolder: {//文件夹配置说明 [名称,描述]
            'emoji': ['表情', '']
            , 'sfxt': ['收费系统', '']
        },
        objfile: {//文件配置说明
            'xitong.png': ['系统管理', '系统管理']
            , 'rcgz.gif': ['日常工作', '日常工作']
            , 'ywgz.png': ['业务工作', '业务工作']
            , 'hose.gif': ['后勤工作', '后勤工作']
            , 'wsbj.gif': ['营养保健', '营养保健']
            , 'sztsg.gif': ['数字图书馆', '数字图书馆']
            , 'tsg.png': ['数字图书馆', '数字图书馆']
            , 'yhzx.gif': ['用户中心', '用户中心']
            //系统管理
        }
    };
    initImglist(rootimgurl);
    /*
     功能：选择栏目组图标 外层弹出选择
     imgtaburl
     按文件夹分类
     按文件名分大小 name_16 name (32*32) name_64
     显示选择中间大小的文件name (32*32)
     */
    function initImglist(rootimgurl,isup){
        //存放图标的路径 根据需要调整值
        $.sm(function(re) {
            var arrstr=[],arrstrimg=[];
            for(var i=0,j=re.length;i<j;i++)
            {
                if(re[i][0].indexOf('.png')>=0||re[i][0].indexOf('.gif')>=0||re[i][0].indexOf('.jpg')>=0||re[i][0].indexOf('.jpeg')>=0||re[i][0].indexOf('.ico')>=0){
                    if(re[i][0].indexOf('_')>=0)continue;
//                var title=objicon.objfile&&objicon.objfile[re[i][0]]?objicon.objfile[re[i][0]][0]+'\r\n'+objicon.objfile[re[i][0]][1]:re[i][0];
                    var title = re[i][0];
                    arrstrimg.push('<img src="..'+rootimgurl+'/'+re[i][0]+'" src2="..'+rootimgurl+'/'+re[i][0]+'" alt="选择'+title+'" title="选择'+title+'" id="imgsel'+i+'" onclick="selectednewimg(this)" />');
                }
                else if(re[i][4]=="1" && objicon.objfolder[re[i][0]]){
                    arrstr.push('<a target="_self" title="选择'+objicon.objfolder[re[i][0]][0]+'\r\n'+objicon.objfolder[re[i][0]][1]+'" href="javascript:initImglist(%27'+rootimgurl+'/'+re[i][0]+'%27,1)"><img alt="" src="../images/openfile.gif" style="margin-right:5px;">'+(objicon.objfolder[re[i][0]][0]||re[i][0])+'</a>');
                }
            }
            if(isup){
                var arr=rootimgurl.replace('\\','/').split('/');
                if(arr.length>0){
                    arr.pop();
                }
                var v1=(arr[0])?arr.join('/'):'/image';
                arrstr.push('<img src="../images/up.gif" alt="上级图标" title="上级图标" style="cursor:pointer; margin-left:3px; margin-top:3px;" onclick="initImglist(\''+v1+'\')" />');
            }
            $("#divform").html(arrstr.join('<br />') + (arrstr.length > 0 ? '<br />' : '') + arrstrimg.join(''));
        }, ["w_menu.4", rootimgurl]);
    }
    /*
     功能： 选择中图标
     */
    function selectednewimg(obj){
        var src = $(obj).attr('src');
        var win = parent.$("#win_" + window.Arg("mid")).find('iframe')[0].contentWindow;
        win.$("#txtmicon").val(src);
        win.$("#imgicon").attr("src" , src);
        var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
        parent.layer.close(index);
    }
</script>
</body>
</html>
