﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>卫生保健——心理列表</title>
    <link rel="stylesheet" type="text/css" href="../../css/reset.css"/>
    <link rel="stylesheet" type="text/css" href="../../css/icon.css"/>
    <link rel="stylesheet" type="text/css" href="../../styles/tbstyles.css"/>
    <link href="../../plugin/flexigrid/css/tbflexigrid.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="../../layui-btkj/css/layui.css"/>
    <link rel="stylesheet" type="text/css" href="../../css/commonstyle-layui-btkj.css"/>
    <link rel="stylesheet" href="../../css/medical.css"/>
    <style type="text/css">
        html, body {
            background: #EAEFF3;
            overflow: hidden;
        }

        .layui-form-item {
            display: inline-block;
            vertical-align: top;
            margin: 5px 5px 0px 0;
        }

        .layui-form-label {
            line-height: 28px;
        }

        /*自定义*/
        .layarea, .layyey {
            display: none;
        }
    </style>
</head>
<body>
<div class="">
    <div class="content-medical">
        <div class="layui-form layui-comselect" style="padding:10px;">
            <div class="layui-form-item layarea">
                <label class="layui-form-label">托幼机构类型：</label>
                <div class="layui-input-inline" style="float: left;width:150px;">
                    <select id="selyeytype" lay-filter="selyeytype"></select>
                </div>
            </div>
            <div class="layui-form-item layarea">
                <label class="layui-form-label">托幼机构名称：</label>
                <div class="layui-input-inline" style="float: left;width:200px;">
                    <div id="selyeyid" class="xm-select-demo" style="display: inline-block;width:100%;"></div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">关键字：</label>
                <div class="layui-input-inline" style="width:200px;">
                    <input type="text" placeholder="请输入姓名/身份证号" class="layui-input" id="txtstudent">
                </div>
            </div>
            <div class="layui-form-item" style="height:39px;">
                <label class="layui-form-label">筛查日期：</label>
                <div class="layui-input-inline" style="float: none;width: auto;margin-right: 0px;">
                    <input id="txtstartdate" type="text" style="width: 130px;" readonly placeholder="开始时间" class="layui-input"/>
                    <img id="iconstartdate" src="../../images/newicon/ico_calendar.png" style="cursor: pointer;position: relative;top: -29px;width: 20px;left: 103px;">
                </div>
                <span class="linebg">-</span>
                <div class="layui-input-inline" style="float: none;width: auto;">
                    <input id="txtenddate" type="text" style="width: 130px; " readonly placeholder="结束时间" class="layui-input"/>
                    <img id="iconenddate" src="../../images/newicon/ico_calendar.png" style="cursor: pointer;position: relative;top: -29px;width: 20px;left: 103px;">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">筛查年龄：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="selage" class="editable-select" style="margin-left: 10px;width:89px;">
                        <option value="">请选择</option>
                        <option value="0.03">3月</option>
                        <option value="0.06">6月</option>
                        <option value="0.08">8月</option>
                        <option value="1">1岁</option>
                        <option value="1.06">1岁6月</option>
                        <option value="2">2岁</option>
                        <option value="2.5">2岁半</option>
                        <option value="3">3岁</option>
                        <option value="4">4岁</option>
                        <option value="5">5岁</option>
                        <option value="6">6岁</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">是否结案：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="selidend">
                        <option value="">全部</option>
                        <option value="1">结案</option>
                        <option value="0">未结案</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">状态：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="selstatus">
                        <option value="">请选择状态</option>
                        <option value="0" selected>在园</option>
                        <option value="1">离园</option>
                        <option value="2">毕业</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <button class="layui-btn form-search" style="margin-left: 5px;vertical-align: top;" id="btnsearch">查询</button>
                <button class="layui-btn form-search" style="margin-left: 5px;vertical-align: top;" id="btnReviewRemind">批量提醒复查</button>
                <button class="layui-btn layyey" style="margin-left: 5px;vertical-align: top;" id="btnadd">添加</button>
            </div>
        </div>
    </div>
    <div id="content" style="width: 100%;">
        <div class="marmain-cen">
            <div class="content-medical" id="divtable">
                <table id="laytable" lay-filter="laytable"></table>
            </div>
        </div>
    </div>
</div>
<script data-main="../../js/zhuanan/psychologylist" src='../../sys/require.min.js'></script>
</body>
</html>
