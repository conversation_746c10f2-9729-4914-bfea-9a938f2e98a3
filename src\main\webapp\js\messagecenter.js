﻿require.config({
    paths: {
        system: '../sys/system',
        promise: '../plugin/promise/promise1',
    },
    waitSeconds: 0
});
var objdata = {
    pageConf: { //分页总配置
        pageNum: 10 //每页条数
    },
    strSearch: "", //搜索关键字
    noticeCache: {
        receive: []
    },
    pageCounts: 0
    , fromsys: ""

    //体检通知的变量start
    , objnotice: {},
    page: {
        curpage: 1,
        limit: 10
    }
    //体检通知的变量end
};
require(['system', 'promise'], function () {
    messNum();
    initEvent();
});

function messNum() {
    // if(Arg("mid") == "fuyounotice")
    var objwhere = {};
    objwhere.yeyid = [parent.objdata.my.yeyid];
    objwhere.fuyouid = [parent.objdata.my.fuyouid];
    if(parent.ishospital()){
        objwhere.organid = [parent.objdata.my.organid];
    }
    var phone = parent.objdata.arrmy[3];
    $.sm(function (re, err) {
        if (re && re[0]) {//0总数1未读
            objdata.pageCounts = re[0][0] || 0
            var all = re[0][0] || 0,
                noread = re[0][1] || 0;
            // fuyounum = re[0][2] || 0//消息数量
            if (all) {
                $("#num").show();
                $("#num").html("未读" + noread + '/' + "总数" + all);
                if(noread > 0){
                    parent.$("#msgtotalnum").show().html(noread);
                }else {
                    parent.$("#msgtotalnum").hide().html("");
                }
            } else {
                $("#num").hide();
            }
        } else {
            parent.layer.msg("请求通知数量出错！");
        }
    }, ["index.xiaoxiNum", phone, $.msgwhere(objwhere)]);
}

//UI模板
var templete = {
        attacheTem: '<dl style="" class="layui-txt-add" data-path="{{filePath}}" data-file-id="{{fileId}}">' +
            '<dt><img src="{{icon}}"></dt>' +
            '<dd>' +
            '<p class="layui-txt-tit">{{fileName}}<label>（{{fileSize}}）</label></p>' +
            '<a class="js-del-attache" style="color:#1ca89f!important;">{{percent}}删除</a>' +
            '</dd>' +
            '</dl>',
        phoneAttacheTem: '<dl style="" class="layui-txt-add">' +
            '<dt><img src="{{icon}}"></dt>' +
            '<dd>' +
            '<p class="layui-txt-tit">{{file}}</p>' +
            '<a style="color: #1ca89f  !important;">查看</a>' +
            '</dd>' +
            '</dl>',
        noticeTypeTem: '<tr data-id="{{id}}">' +
            '<td style="text-align: center;">' +
            '<div class="layui-unselect layui-form-radio"><i class="layui-anim layui-icon layui-anim-scaleSpring"></i></div>' +
            '</td>' +
            '<td class="js-type-name" style="text-align: center;">{{typename}}</td>' +
            '<td style="text-align: center;">' +
            '<a class="js-edit-type" style="color: #1ca89f  !important;margin: 0 10px;">编辑</a>' +
            '<a class="js-del-type" style="color: #1ca89f  !important;margin: 0 10px;">删除</a>' +
            '</td>' +
            '</tr>',
        noticeListTem: '<div class="noti-content" data-id="{{id}}" data-areacode="{{areacode}}" data-fromsys="{{fromsys}}" data-sendtype="{{sendtype}}" data-totype="{{totype}}" data-noticeflag="{{noticeflag}}">' +
            '<i class="{{readstatus}}"></i>' +
            '<dl style="" class="qr-show-detail">' +
            '<dt><img src="{{cover1}}"  class="qr-show-detail"></dt>' +
            '<dd style=""></dt>' +
            '<h5 style="cursor: pointer;" class="checknotice">{{statusIconTem}}{{title}}<span class="notice-label noti-tit-right" style="margin: 0; height: auto;"><span>{{typename}}</span></span></h5>' +
            '<span class="qr-show-detail" style="padding: 10px 0;display: block;">{{summary}}</span>' +
            '<p style="font-size: 12px;">' +
            '   <span style="display: {{attShow}};margin-right:15px"><img src="../images/association_ico.png" style="margin-right: 6px;">{{attachelen}}</span>' +
            // '   <label class="lb-enrollstate" style="display: {{enrollNumShow}};cursor: pointer;">\n' +
            // '       <img src="../images/nonread.png" style="width: 18px;vertical-align: top;margin-top: 11px;">\n' +
            // '       <span class="b-number">{{enrollnum}}</span>人报名' +
            // '   </label>\n' +
            '   <label style="display: {{enrollShow}}; color: {{enrollColor}}"><span>{{enrollStatus}}</span></label>' + //本人报名信息
            '{{enrollTem}}' +
            '</p>' +
            '<p style="border-top: 1px solid #E6E6E6;">' +
            '<span>发布机构 <label class="layui-color-gary2">{{fromdepart}}</label></span>' +
            '<span style="margin-left: 15px;">发布人 <label class="layui-color-gary2">{{userName}}</label></span>' +
            '<span style="margin-left: 15px;">发布时间 <label class="layui-color-gary2">{{publishtime1}}</label></span>' +
            '<label style="margin-left: 15px;display: none;">接收人</label>' +
            '<label class="qr-to-user-list layui-color-gary2" style="display: none;"></label>' +
            '<span class="js-read-info" style="float: right;">' +
            '</span></p>' +
            '</dd>' +
            '</dl>' +
            '</div>',
        noContent: '<div style="margin-top: 100px;text-align: center"><img src="../images/nodata.png" /></div>',
        enrollTem: '   <span style="display: inline-block;" class="enroll-label noti-tit-right">\n' +
            // '   <label class="enroll-label" style="color:#4A90E2;display: {{lbneedenrollshow}}"><img src="../images/sel_icon01.png">需要报名</label>\n' +
            '   <span style="display: {{enTimeShow}}"><label class="time-txt" style="color:#FE3939">报名截止时间</label> <label class="time-num" data-enrolltime = "{{enrolltime}}">{{enrollTime}}</label> &nbsp;&nbsp;<label class="end-label" style="display:  {{endTimeShow}};color: red;">已截止报名</label></span>\n' +
            '      </span>'
    },
    /**
     * 替换模板数据 模板与数据中的变量名完全一致
     * @param template {String} 模板字符串
     * @param model {Object} 模板数据
     * @returns {*} {strHtml} 数据替换后的模板字符串
     */
    renderTemp = function (template, model) {
        var reg = /\{\{\w+\}\}/g;
        template = template.replace(reg, function (regStr) {
            var reg2 = /\{\{(\w+)\}\}/g,
                key = reg2.exec(regStr)[1];
            return model[key];
        });
        return template;
    };

//初始化事件
function initEvent() {
    $(window).on("resize", function () {
        $(".div-content").height($(window).height() - $(".layui-tab").height() - ($(".notice-pages").height() || 50) - 16);
    }).trigger("resize");
    $(".div-content").niceScroll({autohidemode: true});
    handleReceived();
    if (Arg("noticeid")) {
        openNotice(Arg("noticeid"), 1, parent.objdata.yeyinfo.areacode);
    }
    $("#imgsearch").off("click").on("click", function (ev) {//    点击搜索
        var strSearch = $.trim($("#qr-search").val());
        if (objdata.strSearch === strSearch) {
            return;
        }
        objdata.strSearch = strSearch;
        objdata.receivedHasCli = false;
        objdata.noticeCache.receive = [];
        handleReceived();
    });
    $("#qr-search").off("keyup").on("keyup", function (ev) {//Enter键触发搜索
        if (ev.keyCode === 13) {
            //    enter键
            $("#imgsearch").trigger("click");
        }
    });
    $("body").off("click", ".checknotice").on("click", ".checknotice", function (ev) {// 已接收通知详情
        var _this = this;
        var curnoti = $(_this).closest(".noti-content");
        var noticeId = curnoti.attr("data-id"),
            areacode = curnoti.attr("data-areacode");
        var fromsys = curnoti.attr("data-fromsys");
        var ishas = 0;
        if (curnoti.find("i").hasClass("unread-mark")) {
            ishas = 1;
        } else {
            ishas = 0;
        }
        // parent.openNotice(noticeId,ishas,areacode);
        openNotice(noticeId, ishas, areacode, fromsys);
    });
    $("#tltab li").click(function () {
        var idx = $(this).index();
        $(this).siblings().removeClass("layui-this");
        $(this).addClass("layui-this");
        $(".noticesection").hide();
        $(".noticesection").eq(idx).show();
        if (idx == 1) {
            layui.use(['form', 'laypage'], function () {
                tjnotice.initlist();
            });
        }
    });
    $("#js-receive").off("click", ".lb-enrollstate").on("click", ".lb-enrollstate", function (ev) {//报名情况
        var noticontent = $(this).closest(".noti-content");
        var noticeid = noticontent.attr("data-id");
        var sendtype = noticontent.attr("data-sendtype");
        var noticeflag = noticontent.attr("data-noticeflag");
        var totype = noticontent.attr("data-totype");
        parent.layer.open({
            type: 2,
            title: '查看报名情况',
            // skin : 'layui-layer-rim', //加上边框
            btnAlign: 'c',
            area: ['100%', '100%'],
            btn: ["关闭"],
            content: 'html/noticereadlist.html?v=' + (Arg("v") || 1) + "&mid=" + (Arg("mid") || "") + "&noticeid=" + noticeid + "&totype=" + totype + "&readtype=isenroll" + "&type=" + noticeflag
        });
        return false;
    });
}

/**
 * 获取接收到的通告
 * @param index
 * @returns {Promise<unknown>}
 */
function getReceNotice(index) {
    var pageELe = "#receive-page",
        strWhere = {},//"nr.isdel=0",
        pageNum = objdata.pageConf.pageNum,
        offset = index * pageNum; //分页偏移量
    strWhere.yeyid = [parent.objdata.my.yeyid];
    strWhere.fuyouid = [parent.objdata.my.fuyouid];
    if (objdata.strSearch !== "") {
        strWhere.strSearch = [objdata.strSearch];
    }
    var objwhere = {};
    objwhere.yeyid = [parent.objdata.my.yeyid];
    objwhere.fuyouid = [parent.objdata.my.fuyouid];
    return new Promise(function (resolve, reject) {
        $.sm(function (re, err) {
            if (!err && re) {
                re[0].mobile = parent.objdata.arrmy[3];
                resolve(re[0]);
            } else {
                parent.layer.msg("发送消息失败")
            }
        }, ["notice.receGetPage", objdata.pageConf.pageNum, $.msgwhere(objwhere)]);
    }).then(function (data) {
        if (data.total === 0) {
            $("#nodata").show();
            $(".div-content").css("height", 0);
            $("#receive-page").hide();
            parent.layer.msg("暂无接收通知");
            return;
        } else {
            $("#nodata").hide();
            $("#receive-page").show();
        }
        if (index === 0) {
            objdata.noticeCache.receive.length = data.pagecount;
            handlePage(data, pageELe);
        }
        if (objdata.fromsys) {
            strWhere.fromsys = [objdata.fromsys];
        }
        return new Promise(function (resolve, reject) {
            $.sm(function (re, err) {
                if (!err && re) {
                    resolve(re);
                } else {
                    parent.layer.msg("发送消息失败")
                }
            }, ["notice.selReceivedNotice", pageNum, offset, $.msgwhere(strWhere)]);
        });
    });
}

var curindex;

/**
 * 查询接收到的公告
 */
function handleReceived(index, pageFlag) {
    if (objdata.receivedHasCli && !pageFlag) {
        return;
    }
    index = (index || 0);
    curindex = index;
    objdata.receivedHasCli = true;
    if (typeof objdata.noticeCache.receive[index] === "undefined") {
        objdata.noticeCache.receive[index] = getReceNotice(index);
    }
    objdata.noticeCache.receive[index].then(function (noticeList) {
        if (noticeList.length === 0) {
            $("#nodata").show();
            $("#receive-page").hide();
            $(".div-content").css("height", 0);
            $("#js-receive").children().eq(0).html("");
            return;
        } else if (noticeList.total === 0) {
            $("#nodata").show();
            $("#receive-page").hide();
            $(".div-content").css("height", 0);
        } else {
            $("#nodata").hide();
            $("#receive-page").show();
        }
        var arrHtml = [];
        for (var i = 0; i < noticeList.length; i++) {
            var curNotice = noticeList[i],
                tempTem = "";
            if (!curNotice.cover) {
                curNotice.cover = location.origin + '/images/notice_cover.jpg';
            }
            curNotice.userName = curNotice.fromdepart;
            // curNotice.unreadNum = curNotice.tousernum - curNotice.readernum;
            curNotice.filecountA = curNotice.attachelen;//curNotice.filecount;
            curNotice.unreadNum = 0;
            curNotice.publishtime1 = "发布于" + curNotice.publishtime;
            curNotice.cover1 = ossPrefix + curNotice.cover + "?x-oss-process=image/resize,m_fill,h_180,w_180";
            curNotice.enrollStatus = curNotice.myenroll ? (curNotice.myenroll === 1 ? "我已报名" : "不报名") : "未报名";
            curNotice.areacode = parent.objdata.yeyinfo.areacode;
            curNotice.readstatus = curNotice.isread != "0" ? "stat-mark" : "unread-mark";
            // curNotice.enrolltimenoticeid = curNotice.id;
            curNotice.enrollTem = curNotice.isenroll ? makeEnrollTem(curNotice) : "";
            tempTem = templete.noticeListTem;
            arrHtml.push(renderTemp(tempTem.replace("{{statusIconTem}}", ""), curNotice));
        }
        $("#js-receive").children().eq(0).html(arrHtml.join(""));
    });
}

//报名时间组件
function makeEnrollTem(curNotice) {
    var moment = parent.moment;
    var enT = moment(curNotice.enrolltime)._d,
        now = new Date();
    // curNotice.setEntime = "inline-block";
    curNotice.enTimeShow = "inline-block";
    // curNotice.timeInfo = "报名进行中";
    curNotice.enrollTime = retTime(enT);
    curNotice.endTimeShow = "none";
    if (+enT <= +now) {
        // //报名已经结束
        curNotice.endTimeShow = "inline-block";
        // var objwatermark = {"watermarl_element": "enrolltime" + curNotice.id, "watermark_txt": "已截止报名", watermark_x_space: 80, watermark_y_space: 80};
        // watermark(objwatermark);//添加水印
        //    报名结束超过7天，不允许延迟报名
        if ((+now - +enT) / (1000 * 60 * 60 * 24) >= 7) {
            curNotice.setEntime = "none";
            curNotice.enTimeShow = "none";
        }
    }
    return renderTemp(templete.enrollTem, curNotice);
}

function retTime(enT) {
    return '<i>' + enT.getFullYear() + '</i>' + '-<i>' + (enT.getMonth() + 1) + '</i>' + '-<i>' + enT.getDate() + '</i>' + ' <i>' + enT.getHours() + '时</i>' + '<i>' + enT.getMinutes() + '分</i>';
}

/**
 * 公告分页底部组件
 * 分页组件 UI基于layUI框架
 * @param param {Object} 分页有关统计参数
 *      param.total {Number} 总条数
 *      param.pagecount {Number} 总页数
 * @param eleId {String} 页码组件box选择器
 */
function handlePage(param, eleId) {
    var pageCount = Math.ceil(objdata.pageCounts / objdata.pageConf.pageNum),
        domPage = $(eleId);
    if (pageCount <= 1) {
        domPage.html("").hide();
        return;
    }

    function init() {
        initPageUI();
        changePage();
    }

    //初次生成分页UI
    function initPageUI() {
        var arrHtml = ['<a class="qr-prev">上一页</a>', '<a class="selected">1</a>', '<em style="display: none;">...</em>'];
        for (var i = 2; i <= pageCount; i++) {
            //    页数大于6时，从第四页开始直到倒数第三页，中间的页码都用一个 ... 表示
            if (pageCount >= 6) {
                if (i === 4) {
                    arrHtml.push('<em>...</em>');
                } else if (i > 4 && i < pageCount - 1) {
                    continue;
                } else if (i === pageCount) {
                    arrHtml.push('<em style="display: none;">...</em>');
                    arrHtml.push('<a>' + i + '</a>');
                } else {
                    arrHtml.push('<a>' + i + '</a>');
                }
            } else {
                arrHtml.push('<a>' + i + '</a>');
            }
        }
        arrHtml.push('<a class="qr-next">' + '下一页' + '</a>');
        arrHtml.push('<em>共' + pageCount + '页</em>');
        //    直接跳转到具体某一页
        arrHtml.push('<span class="pages-skip">转到第<input type="text">页<a class="skip-sure">确定</a></span>');
        domPage.html(arrHtml.join("")).show();
        domPage.attr("data-pageCount", pageCount);
        domPage.attr("data-pageCur", "1");
    }

//    页码切换
    function changePage() {
        domPage.off("click", "a").on("click", "a", function (ev) {
            var tar = ev.target || ev.srcElement,
                tarClass = $(tar).prop("class"),
                pageCur = Number(domPage.attr("data-pageCur"));
            switch (tarClass) {
                case "" : {
                    //    点击某一个页码
                    var tmpCurPage = Number($(tar).text());
                    renderPage(tmpCurPage, pageCount);
                    handleReceived(tmpCurPage - 1, true);
                    break;
                }
                case "qr-prev" : {
                    //    上一页
                    if (pageCur === 1) {
                        return;
                    }
                    renderPage(pageCur - 1, pageCount);
                    handleReceived(pageCur - 2, true);
                    break;
                }
                case "qr-next" : {
                    //    下一页
                    if (pageCur === pageCount) {
                        return;
                    }
                    renderPage(pageCur + 1, pageCount);
                    handleReceived(pageCur, true);
                    break;
                }
                case "skip-sure" : {
                    //    输入框输入具体某一页
                    var inputPage = Number($.trim($(tar).prev().val()));
                    if (isNaN(inputPage) || inputPage === pageCur || inputPage > param.pagecount || inputPage < 1) {
                        if (inputPage === pageCur) {
                            parent.layer.msg("已是当前页");
                            return
                        }
                        parent.layer.msg("输入页码不合法");
                        return
                    }
                    renderPage(inputPage, pageCount);
                    handleReceived(inputPage - 1, true);
                    break;
                }
                case "selected" : {
                    //    当前页
                    return;
                }
            }
        });
    }

    /**
     * 切换页码时重新渲染分页UI
     * @param index {Number} 要展现的页码
     * @param pageCount {Number} 页码总数
     */
    function renderPage(index, pageCount) {
        var arrHtml = ['<a class="qr-prev">上一页</a>', '<a>1</a>'],
            domPageChi = domPage.children(),
            replaceEle = domPageChi.slice(0, domPageChi.length - 3);
        domPage.attr("data-pageCur", index);
        //默认大于等于6时出现省略号
        if (pageCount < 6) {
            domPageChi.removeClass("selected");
            for (var i = 0; i < domPageChi.length; i++) {
                if (domPageChi.eq(i).text() === index.toString()) {
                    domPageChi.eq(i).addClass("selected");
                    break;
                }
            }
            return;
        } else if (pageCount === 6) {
            //始终只有一个...出现
            if (index < 3) {
                arrHtml.pop();
                for (var i = 1; i <= pageCount; i++) {
                    if (i === 4) {
                        arrHtml.push('<em>...</em>');
                    } else if (i > 4 && i < pageCount - 1) {
                        continue;
                    } else {
                        i === index ? (arrHtml.push('<a class="selected">' + i + '</a>')) : (arrHtml.push('<a>' + i + '</a>'));
                    }
                }
            } else if (index === 3) {
                //    ...出现在后部分
                for (var i = 2; i <= index + 1; i++) {
                    i === index ? (arrHtml.push('<a class="selected">' + i + '</a>')) : (arrHtml.push('<a>' + i + '</a>'));
                }
                arrHtml.push('<em>...</em>');
                arrHtml.push('<a>' + pageCount + '</a>');
            } else {
                //    ...出现在前部分
                arrHtml.push('<em>...</em>');
                for (var i = pageCount - 3; i <= pageCount; i++) {
                    i === index ? (arrHtml.push('<a class="selected">' + i + '</a>')) : (arrHtml.push('<a>' + i + '</a>'));
                }
            }
        } else {
            // 两个...和一个...情况二选一
            if (index < 3) {
                arrHtml.pop();
                for (var i = 1; i <= pageCount; i++) {
                    if (i === 4) {
                        arrHtml.push('<em>...</em>');
                    } else if (i > 4 && i < pageCount - 1) {
                        continue;
                    } else {
                        i === index ? (arrHtml.push('<a class="selected">' + i + '</a>')) : (arrHtml.push('<a>' + i + '</a>'));
                    }
                }
            } else if (index === 3) {
                //    ...出现在后部分
                for (var i = 2; i <= index + 1; i++) {
                    i === index ? (arrHtml.push('<a class="selected">' + i + '</a>')) : (arrHtml.push('<a>' + i + '</a>'));
                }
                arrHtml.push('<em>...</em>');
                arrHtml.push('<a>' + pageCount + '</a>');
            } else if (index >= pageCount - 2) {
                //    ...出现在前部分
                arrHtml.push('<em>...</em>');
                for (var i = pageCount - 3; i <= pageCount; i++) {
                    i === index ? (arrHtml.push('<a class="selected">' + i + '</a>')) : (arrHtml.push('<a>' + i + '</a>'));
                }
            } else {
                //    出现两个...
                arrHtml.push('<em>...</em>');
                arrHtml.push('<a>' + (index - 1) + '</a>');
                arrHtml.push('<a class="selected">' + index + '</a>');
                arrHtml.push('<a>' + (index + 1) + '</a>');
                arrHtml.push('<em>...</em>');
                arrHtml.push('<a>' + pageCount + '</a>');
            }
        }
        replaceEle.remove();
        replaceEle = null;
        domPage.prepend(arrHtml.join(""));
    }

    init();
}

//reload当前页
function resetNoticeIcon() {
    delete objdata.noticeCache.receive[curindex];
    handleReceived(curindex, true);
}

function openNotice(noticeId, ishas, areacode, fromsys) {//ishas判断是否已读 0已读 1未读
    //第一次打开通知详情时，调用 parent.messageNum();messNum();这两个方法，改变消息数目的状态
    parent.objdata.messagecenterpage = parent.layer.open({
        type: 2,
        title: '查看通知详情',
        area: ['50%', '80%'], //宽高
        btnAlign: 'c',
        content: 'html/comnotidetail.html?yeyid=' + parent.objdata.yeyinfo.id + '&noticeid=' + noticeId + '&areacode=' + areacode + '&v=' + Arg("v") + '&mobile=' + parent.objdata.arrmy[3] + '&readstatus=' + (ishas == 0 ? "read" : "noread"),
        // btn: ["关闭"],
        success: function () {
            var iframe = parent.$("#win_" + Arg("mid")).find('iframe')[0].contentWindow;
            parent.objdata.messagecenteriframe = iframe;
            if (ishas == 1) {
                setTimeout(function () {
                    messNum();// parent.messageNum();
                    if (iframe && iframe.messNum) {
                        iframe.messNum();
                        // iframe.$("div[data-id='"+noticeId+"']").find("i").removeClass("unread-mark");
                        //处理对象
                        iframe.resetNoticeIcon();
                    }
                }, 300);
            }
        },
        yes: function (index, layero) {
            parent.layer.close(index);
        }
    });
}

var tjnotice = {
    initlist: function () {
        jQuery.getparent().layer.load();
        var swhere = tjnotice.getswhere();
        $.sm(function (re, err) {
            if (err) {
                jQuery.getparent().layer.msg(err);
            } else {
                var count = re[0].total || 0;
                layui.laypage.render({
                    elem: 'laypage'
                    , count: count // 数据总数
                    , limit: objdata.page.limit
                    , layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
                    , jump: function (obj) {
                        objdata.page.curpage = obj.curr;
                        objdata.page.limit = obj.limit;
                        tjnotice.initlistdata();
                    }
                });
            }
        }, ["fuyounotice.getlistcount", swhere]);
    }
    , initlistdata: function () {
        var swhere = tjnotice.getswhere();
        $.sm(function (re, err) {
            if (err) {
                jQuery.getparent().layer.msg(err);
            } else {
                objdata.objnotice = {};
                var arrhtml = [];
                for (var i = 0; i < re.length; i++) {//id,eventtime,eventtitle,eventcontent,readstate
                    arrhtml.push('<div style="padding: 10px;">');
                    arrhtml.push('    <div class="table-title" style="position: relative;padding-left: 40px;">');
                    arrhtml.push(re[i].eventtitle);
                    arrhtml.push('        <label id="labreadstate' + re[i].id + '">' + (re[i].readstate == 1 ? "已读" : "未读") + '</label>');
                    arrhtml.push('        <a class="btnview" data-id="' + re[i].id + '" style="color: #49a1ff;text-decoration: underline;margin-left: 10px;" >查看详情</a>');
                    arrhtml.push('        <div>');
                    arrhtml.push('            <label>' + re[i].eventtime + '</label>');
                    arrhtml.push('        </div>');
                    arrhtml.push('    </div>');
                    arrhtml.push('</div>');
                    objdata.objnotice[re[i].id] = re[i];
                }
                if (arrhtml.length > 0) {
                    $("#divlist").html(arrhtml.join(''));
                    $(".btnview").click(function () {
                        tjnotice.openwin("view", $(this).data("id"));
                    });
                } else {
                    arrhtml.push('<div style="text-align: center;margin-top: 16%;">');
                    arrhtml.push('    <img src="../images/noinfo_img.png" />');
                    arrhtml.push('    <p style="color: #999999;margin-top: 15px;">暂无通知信息哦~</p>');
                    arrhtml.push('</div>');
                    $("#divlist").html(arrhtml.join(''));
                }
                jQuery.getparent().layer.closeAll('loading');
            }
        }, ["fuyounotice.getjsonlist", swhere, objdata.page.limit, (objdata.page.curpage - 1) * objdata.page.limit]);
    }
    , openwin: function (type, id) {
        switch (type) {
            case "view":
                var noticedata = objdata.objnotice[id];
                var arrdata = JSON.parse(noticedata.eventcontent);
                var arrhtml = [];
                for (var i = 0; i < arrdata.length; i++) {
                    arrhtml.push('<div style="margin:10px 10px 0 0;display: -webkit-box;">');
                    arrhtml.push('    <div style="width:20%;float:left;text-align:center;">');
                    arrhtml.push('        <img id="imgphotopath" width="100" height="100" src="' + ossPrefix + arrdata[i].photopath + '"/>');
                    arrhtml.push('        <p id="labempname"></p>');
                    arrhtml.push('    </div>');
                    arrhtml.push('    <div style="width:80%;float:left;">');
                    arrhtml.push('        <table class="layui-table" style="margin: 0px; text-align: center;">');
                    arrhtml.push('            <thead>');
                    arrhtml.push('                <tr>');
                    arrhtml.push('                    <td>' + arrdata[i].empname + '</td>');
                    arrhtml.push('                    <td>体检日期</td>');
                    arrhtml.push('                    <td>体检结果</td>');
                    arrhtml.push('                    <td>下次体检日期</td>');
                    arrhtml.push('                    <td>是否核发健康证</td>');
                    arrhtml.push('                    <td>健康证到期时间</td>');
                    arrhtml.push('                </tr>');
                    arrhtml.push('            </thead>');
                    arrhtml.push('            <tbody>');
                    arrhtml.push('                <tr>');
                    arrhtml.push('                    <td>园所端上传信息</td>');
                    arrhtml.push('                    <td>' + arrdata[i].checktime + '</td>');
                    arrhtml.push('                    <td>' + (arrdata[i].ckresult == 1 ? "通过" : "未通过") + '</td>');
                    arrhtml.push('                    <td>' + arrdata[i].jhchecktime + '</td>');
                    arrhtml.push('                    <td>' + (arrdata[i].ispubliccard == 1 ? "是" : "否") + '</td>');
                    arrhtml.push('                    <td>' + arrdata[i].jhchecktime + '</td>');
                    arrhtml.push('                </tr>');
                    arrhtml.push('                <tr>');
                    arrhtml.push('                    <td>体检中心信息</td>');
                    arrhtml.push('                    <td>' + arrdata[i].orderdate + '</td>');
                    arrhtml.push('                    <td>' + (arrdata[i].doctor_isabnormal == 1 ? "通过" : "未通过") + '</td>');
                    arrhtml.push('                    <td>' + arrdata[i].card_expiretime + '</td>');
                    arrhtml.push('                    <td>' + (arrdata[i].iscardpublic == 1 ? "是" : "否") + '</td>');
                    arrhtml.push('                    <td>' + arrdata[i].card_expiretime + '</td>');
                    arrhtml.push('                </tr>');
                    arrhtml.push('            </tbody>');
                    arrhtml.push('        </table>');
                    arrhtml.push('    </div>');
                    arrhtml.push('</div>');
                }
                jQuery.getparent().layer.open({
                    type: 1,
                    title: '信息不一致',
                    shadeClose: false,
                    area: ['900px', '50%'],
                    content: arrhtml.join(''),
                    btn: ["关闭"]
                });
                if (noticedata.readstate == 0) {
                    $.sm(function (re, err) {
                        if (err) {
                            jQuery.getparent().layer.msg(err);
                        } else {
                            $("#labreadstate" + noticedata.id).html("已读");
                            jQuery.getparent().getfuyounoticecount();
                        }
                    }, ["fuyounotice.setreadstate", $.msgwhere({id: [noticedata.id]})]);
                }
                break;
            default:
                break;
        }
    }
    , getswhere: function () {
        return "";
    }
};
//
// /**
//  * 添加水印
//  * @param settings
//  */
// function watermark(settings) {
//     //默认设置
//     var defaultSettings = {
//         watermarl_element: "body",
//         watermark_txt: "",
//         watermark_x: 10,//水印起始位置x轴坐标
//         watermark_y: 10,//水印起始位置Y轴坐标
//         watermark_rows: 1,//水印行数
//         watermark_cols: 1,//水印列数
//         watermark_x_space: 50,//水印x轴间隔
//         watermark_y_space: 50,//水印y轴间隔
//         watermark_color: '#d7d7d7',//水印字体颜色
//         watermark_alpha: 0.5,//水印透明度
//         watermark_fontsize: '20px',//水印字体大小
//         watermark_font: '微软雅黑',//水印字体
//         watermark_width: 200,//水印宽度
//         watermark_height: 50,//水印长度
//         watermark_angle: 15//水印倾斜度数
//     };
//     if (settings) $.extend(defaultSettings, settings);
//     //采用配置项替换默认值，作用类似jquery.extend
//     if (arguments.length === 1 && typeof arguments[0] === "object") {
//         var src = arguments[0] || {};
//         for (key in src) {
//             if (src[key] && defaultSettings[key] && src[key] === defaultSettings[key]) {
//                 continue;
//             } else if (src[key]) {
//                 defaultSettings[key] = src[key];
//             }
//         }
//     }
//     var oTemp = document.createDocumentFragment();
//     var maskElement = document.getElementById(defaultSettings.watermarl_element) || document.body;
//     //获取页面最大宽度
//     var page_width = Math.max(maskElement.scrollWidth, maskElement.clientWidth);
//     //获取页面最大高度
//     var page_height = Math.max(maskElement.scrollHeight, maskElement.clientHeight, maskElement.scrollTop);
//     //水印数量自适应元素区域尺寸
//     defaultSettings.watermark_cols = Math.ceil(page_width / (defaultSettings.watermark_x_space + defaultSettings.watermark_width));
//     defaultSettings.watermark_rows = Math.ceil(page_height / (defaultSettings.watermark_y_space + defaultSettings.watermark_height));
//     var x;
//     var y;
//     for (var i = 0; i < defaultSettings.watermark_rows; i++) {
//         y = defaultSettings.watermark_y * 1 + (defaultSettings.watermark_y_space + defaultSettings.watermark_height) * i;
//         for (var j = 0; j < defaultSettings.watermark_cols; j++) {
//             x = defaultSettings.watermark_x + (defaultSettings.watermark_width + defaultSettings.watermark_x_space) * j;
//             var mask_div = document.createElement('div');
//             mask_div.id = 'mask_div' + i + j;
//             mask_div.className = 'mask_div';
//             //mask_div.appendChild(document.createTextNode(defaultSettings.watermark_txt));
//             mask_div.innerHTML = (defaultSettings.watermark_txt);
//             //设置水印div倾斜显示
//             mask_div.style.webkitTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
//             mask_div.style.MozTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
//             mask_div.style.msTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
//             mask_div.style.OTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
//             mask_div.style.transform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
//             mask_div.style.visibility = "";
//             mask_div.style.position = "relative";
//             mask_div.style.left = x + 'px';
//             mask_div.style.top = y + 'px';
//             mask_div.style.overflow = "hidden";
//             mask_div.style.zIndex = "1029"; // 9999
//             // pointer-events:none  让水印不遮挡页面的点击事件
//             mask_div.style.pointerEvents = 'none';
//             // 设置边框
//             // mask_div.style.border="solid #eee 1px";
//             // 兼容IE9以下的透明度设置
//             mask_div.style.filter = "alpha(opacity=50)";
//             mask_div.style.opacity = defaultSettings.watermark_alpha;
//             mask_div.style.fontSize = defaultSettings.watermark_fontsize;
//             mask_div.style.fontFamily = defaultSettings.watermark_font;
//             mask_div.style.color = defaultSettings.watermark_color;
//             mask_div.style.textAlign = "center";
//             mask_div.style.width = defaultSettings.watermark_width + 'px';
//             mask_div.style.height = defaultSettings.watermark_height + 'px';
//             mask_div.style.display = "block";
//             oTemp.appendChild(mask_div);
//         }
//     }
//     maskElement.appendChild(oTemp);
// }

