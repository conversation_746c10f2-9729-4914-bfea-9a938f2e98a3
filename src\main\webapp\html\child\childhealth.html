﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>儿童健康档案</title>
    <link rel="stylesheet" type="text/css" href="../../css/reset.css"/>
    <link rel="stylesheet" type="text/css" href="../../layui-btkj/css/layui.css"/>
    <link rel="stylesheet" type="text/css" href="../../css/icon.css"/>
    <link rel="stylesheet" type="text/css" href="../../css/commonstyle-layui-btkj.css"/>
    <link rel="stylesheet" type="text/css" href="../../css/style.css"/>
    <link rel="stylesheet" href="../../css/medical.css"/>
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <style>
        html, body {
            background: #EAEFF3;
            overflow: hidden;
        }

        .layui-form-item {
            display: inline-block;
            vertical-align: top;
            margin: 5px 5px 0px 0;
        }

        .layui-form-label {
            line-height: 28px;
        }

        /*.layui-table-cell{height:auto;}*/
    </style>
</head>
<body>
<div class="marmain">
    <div class="content-medical">
        <div class="layui-form layui-comselect" style="padding:10px;">
            <div class="layui-form-item">
                <label class="layui-form-label">关键字：</label>
                <div class="layui-input-inline">
                    <input type="text" id="txtkeyword" lay-verify="required" placeholder="姓名/档案号/身份证号" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">性别：</label>
                <div class="layui-input-inline" style="text-align: center;">
                    <select id="sex" lay-filter="sex">
                        <option value="">请选择</option>
                        <option value="男">男</option>
                        <option value="女">女</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">年龄：</label>
                    <div class="layui-input-inline" style="width: 100px;">
                        <select id="selage1" lay-filter="selage1">
                            <option value="">请选择</option>
                            <option value="0.01">1月龄</option>
                            <option value="0.03">3月龄</option>
                            <option value="0.06">6月龄</option>
                            <option value="0.08">8月龄</option>
                            <option value="1">1岁</option>
                            <option value="1.06">1岁6月龄</option>
                            <option value="2">2岁</option>
                            <option value="2.06">2岁6月龄</option>
                            <option value="3">3岁</option>
                            <option value="4">4岁</option>
                            <option value="5">5岁</option>
                            <option value="6">6岁</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">来源：</label>
                <div class="layui-input-inline">
                    <select id="fromtype" lay-filter="fromtype">
                        <option value="">请选择</option>
                        <option value="1">新建</option>
                        <!-- <option value="2">照片录入</option> -->
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">户口状态：</label>
                <div class="layui-input-inline">
                    <select id="huk_state" lay-filter="huk_state">
                        <option value="">请选择</option>
                        <option value="1">迁入</option>
                        <option value="2">迁出</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item" style="height:39px;">
                <label class="layui-form-label">登记日期：</label>
                <div class="layui-input-inline" style="float: none;width: auto;margin-right: 0px;">
                    <input id="txtstartdate" type="text" style="width: 170px;" readonly placeholder="请选择" class="layui-input"/>
                    <img id="iconstartdate" src="../../images/newicon/ico_calendar.png" style="cursor: pointer;position: relative;top: -29px;width: 20px;left: 138px;">
                </div>
            </div>
            <div class="layui-form-item" style="height:39px;">
                <label class="layui-form-label">出生日期：</label>
                <div class="layui-input-inline" style="float: none;width: auto;margin-right: 0px;">
                    <input id="txtbirthday" type="text" style="width: 210px;" readonly placeholder="请选择" class="layui-input"/>
                    <img id="iconbirthday" src="../../images/newicon/ico_calendar.png" style="cursor: pointer;position: relative;top: -29px;width: 20px;left: 170px;">
                </div>
            </div>
            <div class="layui-form-item" style="height:39px;">
                <label class="layui-form-label">体检日期：</label>
                <div class="layui-input-inline" style="float: none;width: auto;margin-right: 0px;">
                    <input id="txtjckdate" type="text" style="width: 210px;" readonly placeholder="请选择" class="layui-input"/>
                    <img id="icontjckdate" src="../../images/newicon/ico_calendar.png" style="cursor: pointer;position: relative;top: -29px;width: 20px;left: 170px;">
                </div>
            </div>
            <div class="layui-form-item" >
                <label class="layui-form-label">社区：</label>
                <div class="layui-input-inline" style="margin-right: 0px;">
                    <div id="abc1" class="xm-select-demo-alert"> </div>
                </div>
            </div>
            <div class="layui-form-item">
                <button class="layui-btn form-search" style="margin-left: 5px;vertical-align: top;" id="btnSearch">查询</button>
                <button class="layui-btn form-search" style="margin-left: 5px;vertical-align: top;" id="btnadd">新建</button>
                <button class="layui-btn form-search" style="margin-left: 5px;vertical-align: top;" id="btnsetup">设置</button>
                <button class="layui-btn form-search" style="margin-left: 5px;vertical-align: top;" id="btnimport">导入档案</button>
                <button class="layui-btn form-search" style="margin-left: 5px;vertical-align: top;" id="btnBatchPrint">批量打印</button>
                <button class="layui-btn form-search" style="margin-left: 5px;vertical-align: top;" id="btnsendmsg">测试小程序消息</button>
<!--                <a href="javascript:void(0)" id="executepj" style="margin-left: 5px;vertical-align: top;">处理评价</a>-->
            </div>
        </div>
    </div>
    <div id="content" style="width: 100%;">
        <div class="marmain-cen">
            <div class="content-medical" id="divtable">
                <table id="laytable" lay-filter="laytable"></table>
            </div>
        </div>
    </div>
</div>
<script type="text/html" id="toolbarlaytable">
    <div style="">
        <span style="font-size:14px;font-weight: bold;">儿童档案列表</span>
    </div>
</script>
<script data-main="../../js/child/childhealth" src="../../sys/require.min.js"></script>
</body>
</html>
