// 模板注册器
var templateRegistry = (function () {
    var templates = {};

    return {
        register: function (xcxType, tplType, template) {
            if (!templates[xcxType]) {
                templates[xcxType] = {};
            }
            templates[xcxType][tplType] = template;
        },
        get: function (xcxType, tplType) {
            return templates[xcxType][tplType];
        }
    };
})();

// 注册默认模板
templateRegistry.register("xcxcpmch", 1, {
    "touser": [],
    "template_id": "nk7LHZKQlVGB8ARyDdL84yXWfy-cJiYM5_MHD2e0d4U",
    "data": {
        "time3": {
            "value": "00:00"
        },
        "thing2": {
            "value": "这是个消息内容"
        }
    }
});
/* templateRegistry.register("xcxhome", 1, {
    "touser": [],
    "template_id": "zejVJR5upREx2NfTXYduBKZO0NqctBEA6c-tKkdxp8I",
    "data": {
        "time3": {
            "value": "00:00"
        },
        "thing2": {
            "value": "这是个消息内容"
        }
    }
}); */

/**
 * 
 * @param {小程序类型} xcxtype 
 * @param {模板类型} tplType 
 * @param {openids} openids 
 * @param {数据} data 
 * @param {跳转链接} link
 * @returns 
 */
function sendMsg(xcxtype, tplType, openids, data, link, tip) {
    var template = templateRegistry.get(xcxtype, tplType);
    if (!template) {
        return jQuery.getparent().layer.msg("模板不存在");
    }

    var pushmsg = Object.assign({}, template); // 深拷贝模板
    pushmsg.touser = openids;
    pushmsg.type = xcxtype;
    pushmsg.data = Object.assign({}, template.data, data);
    if (link) {
        pushmsg.page = link;
    }
    
    //根据域名判断环境 
    const DEV_HOSTS = ["localhost", "127.0.0.1", "testyunfuyoukang.cpmch.com"];
    const isDevEnvironment = DEV_HOSTS.includes(window.location.hostname);
    if (isDevEnvironment) {
        pushmsg.mpstate = 1; //miniprogram_state 小程序版本类型  正式版  测试版
    }

    $.smaction(function (re, err) {
        if (err) {
            return jQuery.getparent().layer.msg("发送失败");
        }
        if(tip){
            jQuery.getparent().layer.msg(tip);
        } else {
            jQuery.getparent().layer.msg("发送成功");
        }
    }, pushmsg, { datastring: true, action: "message/sendmsg/send" });
}

function getmsgobj(xcxtype, tplType, openids, data, link){
    var template = templateRegistry.get(xcxtype, tplType);
    if (!template) {
        return jQuery.getparent().layer.msg("模板不存在");
    }

    var pushmsg = Object.assign({}, template); // 深拷贝模板
    if(openids){
        pushmsg.touser = openids;
    }
    pushmsg.type = xcxtype;
    pushmsg.data = Object.assign({}, template.data, data);
    if (link) {
        pushmsg.page = link;
    }
    //根据域名判断环境 
    const DEV_HOSTS = ["localhost", "127.0.0.1", "testyunfuyoukang.cpmch.com"];
    const isDevEnvironment = DEV_HOSTS.includes(window.location.hostname);
    if (isDevEnvironment) {
        pushmsg.mpstate = 1; //miniprogram_state 小程序版本类型  正式版  测试版
    }
    return pushmsg;
}