﻿<!DOCTYPE html>
<html style="height: 100%">
<head>
	<meta charset="utf-8" />
	<title>栏目管理</title>
	<link rel="stylesheet" href="../css/reset.css" />
	<link rel="stylesheet" href="../css/style.css" />
	<link rel="stylesheet" type="text/css" href="../css/icon.css">
	<script type="text/javascript">
		document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
	</script>
	<!--[if lt IE 9]>
			<script src='../sys/html5shiv.min.js'></script>
		<![endif]-->
	<style>
	label.error {
		margin-left: 10px;
		color: red;
		font-style: italic
	}
	/*input:focus { border:1px solid #fdaa2e; }*/
	input.error {
		border: 1px dotted red;
	}
	.dash {
		border: 1px dashed #f00;
	}
	.clone {
		position: absolute;
	}
	.inner u {
		display: none;
	}
	.iconfont{font-size: 23px; margin-left: 10px;}
	</style>
</head>
<body style="height: 100%">
	<section class="columnList fl bg5" style="height:100%;overflow: auto">
		<h4 class="title">
			<u class="icon21 fr pointer"></u>栏目管理<u style="display: none;" class="skinIcon5" title="保存"></u><u class="skinIcon2" title="编辑"></u>
		</h4>
		<div class="pr meauSearch">
			<input type="text" id="txtsearch" placeholder="请输入栏目名称或编号" style="width: 85%;" /><u class="skinIcon3 pointer"></u>
		</div>
		<ul id="meauList" class="meauList appList font14"></ul>
		<div id="divsearch" style="display: none;" class="inner clearfix"></div>
		<input type="hidden" id="txtclass" />
	</section>
	<section class="fl bg4 pd10-20 meauRight font14">
		<h2 class="noticeTitle bg1">新栏目</h2>
		<form class="cmxform" id="formenu" method="get" action="">
			<div class="bg6 formList pd20">
				<div>
					<label class="fl">所属系统：</label>
					<div id="divselsys" class="funLabel fl">
						<label><input type="checkbox" name="txtsys" value="1"/>儿保</label>
						<label><input type="checkbox" name="txtsys" value="16"/>基层卫生服务机构</label>
						<label><input type="checkbox" name="txtsys" value="64"/>入托体检机构</label>
						<label><input type="checkbox" name="txtsys" value="128"/>其他体检机构</label>
						<label><input type="checkbox" name="txtsys" value="4"/>幼儿园</label>
						<label><input type="checkbox" name="txtsys" value="8"/>托育机构</label>
						<label><input type="checkbox" name="txtsys" value="32"/>托幼一体园</label>
						<br />
						<label><input type="checkbox" name="txtsys" value="2"/>体检中心</label>
					</div>
				</div>
				<div>
					<label>所属栏目组：</label> <select id="selmenugroup" name="menugroup" style="width: 157px;"></select>
				</div>
				<div>
					<label>栏目id：</label><label id="lbid"></label>
				</div>
				<div>
					<label><em>*</em>栏目名称：</label> <input type="text" maxlength="50" id="txtmname" placeholder="栏目名称" name="mname" />
				</div>
				<div>
					<label><em>*</em>EnglishName：</label> <input type="text" maxlength="100" id="txtEname" placeholder="English Name" name="Ename" />
				</div>
				<div>
					<label><em>*</em>栏目编号：</label> <input type="text" maxlength="20" id="txtmno" placeholder="栏目编号" name="mno">
				</div>
				<div>
					<label><em>*</em>栏目图标：</label> <input type="text" readonly maxlength="50" id="txtmicon2" name="micon2" /> <img id="imgicon" alt="" src="" style="vertical-align: top; width: 28px; height: 28px;" /><a id="btnpic" class="btn-border">选择</a>
				</div>
				<div>
					<label><em>*</em>桌面图标：</label> <input type="text" readonly maxlength="50" id="txtmicon" name="micon" /><i id="little" class="iconfont"></i><a id="btnpicicon" class="btn-border">选择</a>
				</div>
				<div>
					<label><em>*</em>URL地址：</label> <input type="text" maxlength="250" id="txtmurl" placeholder="URL地址" name="murl" />
				</div>
				<div id="divmfun" class="pr clearfix">
					<label class="fl">功能权限：</label>
					<div id="divcheck" class="funLabel fl">
						<label>1：<input type="text" placeholder="添加" /></label><label>2：<input type="text" placeholder="编辑" /></label><label>4：<input type="text" placeholder="删除" /></label><label>8：<input type="text" placeholder="审核" /></label><label>16：<input type="text" placeholder="添加" /></label><label>32：<input type="text" placeholder="编辑" /></label><label>64：<input type="text" placeholder="删除" /></label><label>128：<input type="text" placeholder="审核" /></label><label>256：<input type="text" placeholder="添加" /></label>
					</div>
					<u class="icon23 pointer pa"></u><u class="icon24 pointer pa"></u>
				</div>
				<div style="display: none;">
					<label>数据权限：</label><input type="checkbox" id="chkisdata" /> <label for="chkisdata"></label> <input type="checkbox" id="chkisclass" /> <label for="chkisclass">显示成"所属班级"(默认"所属部门")</label>
				</div>
				<div>
					<label>隐藏栏目：</label><input type="checkbox" id="chkishide" /> <label for="chkishide">（勾选后，此栏目将作为隐藏栏目使用，业务系统栏目树中将不显示）</label>
				</div>
				<div>
					<label></label> <a id="btnsave" class="btn-black">保存</a> <input style="display: none;" class="btn-black btn-save" type="submit" id="submit" value="保存" /> <a id="btncancel" class="btn-black">取消</a>
				</div>
			</div>
		</form>
		<div id="formenugroup" class="formList bg6 pd20" style="display: none;">
			<div>
				<label class="fl"><em>*</em>所属系统：</label>
				<div class="funLabel fl">
					<label><input type="checkbox" name="txtgroupsys" value="1"/>儿保</label>
					<label><input type="checkbox" name="txtgroupsys" value="16"/>基层卫生服务机构</label>
					<label><input type="checkbox" name="txtgroupsys" value="64"/>入托体检机构</label>
					<label><input type="checkbox" name="txtgroupsys" value="128"/>其他体检机构</label>
					<label><input type="checkbox" name="txtgroupsys" value="4"/>幼儿园</label>
					<label><input type="checkbox" name="txtgroupsys" value="8"/>托育机构</label>
					<label><input type="checkbox" name="txtgroupsys" value="32"/>托幼一体园</label>
					<br />
					<label><input type="checkbox" name="txtgroupsys" value="2"/>体检中心</label>
				</div>
			</div>
			<div>
				<label><em>*</em>栏目组名称：</label> <input type="text" maxlength="50" id="txtmgroupname" placeholder="栏目名称" name="mgroupname" />
			</div>
			<div>
				<label><em>*</em>English Group Name：</label> <input type="text" maxlength="50" id="txtEgroupname" placeholder="English Group Name" name="Egroupname" />
			</div>
			<div>
				<label><em>*</em>栏目分类：</label>
				<select id="selmtype">
					<option value="erbao">儿保</option>
					<option value="tijian">体检</option>
					<option value="richang">日常</option>
				</select>
			</div>
			<div>
				<label><em>*</em>栏目组图片：</label> <input type="text" maxlength="200" id="txtgroupmicon2" placeholder="图标路径" name="micon2" />
			</div>
			<div>
				<label></label> <a id="btnsavegroup" class="btn-black">保存</a> <a id="btncancelgroup" class="btn-black">取消</a> <input type="hidden" id="txtmid" /> <input type="hidden" id="txtEgroup" />
			</div>
		</div>
	</section>
	<script data-main="../js/w_menu.js" src='../sys/require.min.js'></script>
</body>
</html>
