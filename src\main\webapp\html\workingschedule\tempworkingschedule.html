<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>医生[查看|编辑]临时排班</title>
    <link rel="stylesheet" href="../../css/style.css"/>
    <link rel="stylesheet" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../plugin/calendar/fullcalendar.css"/>
    <link rel="stylesheet" href="../../plugin/calendar/fullcalendar.print.css" media="print"/>
    <link type="text/css" rel="stylesheet" href="../../plugin/jquery-ui/jquery-ui.min.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <script src="../../sys/html5shiv.min.js"></script>
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + top.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <style>
        ul {
            line-height: 40px;
        }

        html, body {
            background: #EAEFF3;
        }
        .ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default{ color:#34495E;background:none !important;border: none;}
        .ui-widget-header {background: none;border:1px solid #E7ECF0;color: #34495E;font-weight:normal;}
        .ui-widget-content{background: #fff;}
        .fc-day-num{color: #34495E;font-size: 18px;font-weight: normal;float: left;padding-left: 20px;}
        .fc th, .fc td{border:1px solid #E7ECF0;}
        .fc th{text-align: left;padding-left: 20px;}
        .ui-state-default .ui-icon {
            background-image:none;
        }
        .ui-state-default .ui-icon.ui-icon-circle-triangle-w  {
            background-image:url(../../images/medical/left.png);width: 6px; height: 12px; background-size:6px 12px;
        }
        .ui-state-default .ui-icon.ui-icon-circle-triangle-e {
            background-image:url(../../images/medical/right.png);width: 6px; height: 12px; background-size:6px 12px;
        }
        .ui-icon-circle-triangle-e {
            background-position:left;
        }
        .fc-prev-button span, .fc-next-button span {
            visibility:visible;
        }
        .ui-icon-circle-triangle-w {
            background-position:left;
        }
        .gardenhouse-left{padding: 30px 20px; }
        .fc-day-header { line-height: 40px;}
        .fc-toolbar .fc-left {
            float:none;
            position: absolute;
            top: 3px;
        }
        .ui-widget{position: relative;}
        .checkbox-blue select{border:1px solid #CBD6E1; margin-right: 10px;}

        .layui-laydate-content>.layui-laydate-list {
            padding-bottom: 0px;
            overflow: hidden;
        }
        .layui-laydate-content>.layui-laydate-list>li{
            width:50%
        }
        .content-medical{
            /** 临时排班 保存按钮固定在界面上方 **/
            position: fixed;
            top: 0;
            z-index: 99;
            width: 99%;
        }
        /** 临时排班 给保存按钮预留位置 **/
        .cal-margin{
            margin-top: 35px;
        }
    </style>
</head>
<body>
<section>
    <div class="">
        <div class="content-medical">
            <div class="comtitletop"><span class="manufacturer-txt">排班设置</span>
                <div style="float: right">
                    <div>
                        <button id="btnok" class="layui-btn addbtn">保存</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="datemain">
            <ul>
                <li>
                    <div id="main" style="width:100%">
                        <div id='calendar' class="cal-margin"></div>
                        <div id="window"></div>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</section>
<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/system.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../plugin/js/moment.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../plugin/nicescroll/jquery.nicescroll-tmpwork.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../plugin/wdatepicker/wdatepicker.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../plugin/calendar/fullcalendar.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../js/workingschedule/tempworkingschedule.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>
