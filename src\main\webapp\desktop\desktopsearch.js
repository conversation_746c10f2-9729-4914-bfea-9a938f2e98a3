// 2016.04.11
// 郭玉峰
require.config({
    paths: {
        system: '../sys/system',
        jquery: '../sys/jquery'
    },
    waitSeconds: 0

});
var objdata = {};
require(['jquery', 'system'], function () {
    var startmid = Arg("startmid");
    var toopenmenu1 = null;
    if (startmid) {
        var arrgroupsort = parent.objdata.arrgroupsort;
        for (var i = 0; i < arrgroupsort.length; i++) {
            //var arrsonsort = parent.
            var arrone1menu = parent.objdata.objMenu[arrgroupsort[i]];
            if (arrone1menu && arrone1menu[4]) {
                var arrsonmenu = arrone1menu[4].split(",");
                for (var j = 0; j < arrsonmenu.length; j++) {
                    if(arrsonmenu[j]==startmid){
                        toopenmenu1 = arrone1menu;
                        break;
                    }
                }
            }
        }
    }
    $("#btnclose").click(function () {
        parent.layer.close(parent.layer.getFrameIndex(window.name))
    })
    $("#btnliuchengdaohang").click(function () {
        parent.layer.msg("功能建设中")
    })
    //点击一级菜单
    $("#ulmenutype").children().click(function () {
        var type = $(this).data('type');
        var text = $(this).text()
        var strhtml = '';
        for (var i = 0; i < parent.objdata.arrgroupsort.length; i++) {
            var curmenu = parent.objdata.objMenu[parent.objdata.arrgroupsort[i]];
            if (curmenu && curmenu[7] == type) {
                strhtml += '<li class="menu_1" mid="' + curmenu[0] + '"><img src="../' + curmenu[8] + '" alt=""/>' + curmenu[2] + ' ></li>';
            }
        }
        $("#divsonmenu").show().children('ul').html(strhtml);
        $("#divmainmenu").hide();
        $("#divsearchresult").hide()
        $(".menuLink").html('<label class="hasson">开始菜单</label> > <label data-type="' + type + '">' + text + '</label>')
    })
    //点击顶部导航
    $(".menuLink").on('click', '.hasson', function () {
        var mtype = $(this).index();
        if (mtype == '0') {
            $("#divmainmenu").show();
            $("#divsonmenu").hide()
            $("#divsearchresult").hide()
            $(".menuLink").html('<label class="hasson">开始菜单</label>');
        } else if (mtype == '1') {
            var datatype = $(this).data('type');
            $("#ulmenutype").children('[data-type=' + datatype + ']').trigger('click');
        } else if (mtype == '2') {
            openmenu1($(this).attr('mid'));
        }
    })
    //点击栏目
    $("#divsonmenu").children('ul').on('click', '.menu_1', function () {//点击一级栏目
        var mid = $(this).attr('mid');
        openmenu1(mid);
    }).on('click', '.menu_2', function () {//点击二级栏目
        parent.desktop.openwin($(this));
        parent.layer.close(parent.layer.getFrameIndex(window.name))
    })
    function openmenu1(mid) {
        var menuname = parent.objdata.objMenu[mid][2];
        var strmids = parent.objdata.objMenu[mid][4];
        var arrmids = strmids.split(",");
        var strhtml = '';
        for (var i = 0; i < arrmids.length; i++) {
            var curmenu = parent.objdata.objMenu[arrmids[i]];
            if (!curmenu) continue;
            strhtml += '<li class="menu_2" mid="' + curmenu[0] + '"><img src="../' + curmenu[8] + '" alt=""/>' + curmenu[2] + '</li>';
        }
        $("#divsonmenu").show().children('ul').html(strhtml);
        var typemenutype = $(".menuLink").children('label').eq(1).data('type');
        var typemenutext = $(".menuLink").children('label').eq(1).text()
        $(".menuLink").html('<label class="hasson">开始菜单</label> > <label class="hasson" data-type="' + typemenutype + '">' + typemenutext + '</label> > <label>' + menuname + '</label>')
    }

    $("#txtsearch").on('input propertychange', function () {
        var val = $(this).val();
        dosearch(val);
    })
    $("#btnsearch").click(function () {
        dosearch($("#txtsearch").val());
    })
    function dosearch(val) {
        if (!val) {
            $(".menuLink").children().eq(0).trigger('click');
            return
        }
        var arrsonmenu = parent.objdata.arrsonmenu;
        var arrre = [];
        for (var i = 0; i < arrsonmenu.length; i++) {
            if (arrsonmenu[i][2].indexOf(val) > -1) {
                arrre.push(arrsonmenu[i])
            }
        }
        if (!arrre.length) {
            $("#divsearchresult").html('<div style="text-align: center;margin-top: 100px;color: grey;">没有搜索到结果</div>').show();
            $("#divmainmenu").hide()
            $("#divsonmenu").hide()
        } else {
            var strhtml = '';
            for (var i = 0; i < arrre.length; i++) {
                var curmenu = arrre[i];
                strhtml += '<li class="menu_2" mid="' + curmenu[0] + '"><img src="../' + curmenu[8] + '" alt=""/>' + curmenu[2] + '</li>';
            }
            $("#divsearchresult").html('<ul>' + strhtml + '</ul>').show();
            $("#divmainmenu").hide()
            $("#divsonmenu").hide()
        }
    }

    $("#divsearchresult").on('click', 'li', function () {
        parent.desktop.openwin($(this));
        parent.layer.close(parent.layer.getFrameIndex(window.name))
    })
    if(toopenmenu1){
        $("#ulmenutype").children('[data-type="'+ toopenmenu1[7] +'"]').trigger('click');
        $("#divsonmenu").children('ul').children('[mid="'+ toopenmenu1[0] +'"]').trigger('click');
        $("#divsonmenu").children('ul').children('[mid="'+ startmid +'"]').addClass('current');
    }
});