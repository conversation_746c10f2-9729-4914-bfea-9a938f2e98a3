<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title>智力筛选系统</title>
    <link rel="stylesheet" type="text/css" href="../../css/reset.css" />
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" type="text/css" href="../../css/icon.css" />
    <link rel="stylesheet" type="text/css" href="../../styles/tbstyles.css" />
    <link href="../../plugin/flexigrid/css/tbflexigrid.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css" />
    <style type="text/css">
        html,
        body {
            background: #EAEFF3;
            overflow: hidden;
        }
    
        .layui-form-item {
            display: inline-block;
            vertical-align: top;
        }
    
        .layui-form-label {
            line-height: 28px;
            width: 110px;
        }
    
        .layui-input-inline {
            width: 180px;
        }
    </style>
</head>

<body>
    <div class="marmain">
        <div class="content-medical">
            <div class="search-container layui-form layui-comselect">
                <!-- 量表名称 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">量表名称：</label>
                    <div class="layui-input-inline">
                        <input id="scaleName" type="text" autocomplete="off" placeholder="请输入量表名称" class="layui-input">
                    </div>
                </div>
                <!-- 量表分类 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">量表分类：</label>
                    <div class="layui-input-inline">
                        <select id="scaleClass" lay-filter="scaleClass">
                            <option value="">请选择</option>
                            <option value="1">1km以内</option>
                            <option value="2">1km~3km</option>
                            <option value="3">3km~5km</option>
                            <option value="4">≥5km</option>
                        </select>
                    </div>
                </div>
                <!-- 适用年龄 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">适用年龄：</label>
                    <div class="layui-input-inline">
                        <select id="applyAge" lay-filter="applyAge">
                            <option value="">请选择</option>
                            <option value="1">1km以内</option>
                            <option value="2">1km~3km</option>
                            <option value="3">3km~5km</option>
                            <option value="4">≥5km</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <button class="layui-btn layui-btn-normal" id="btnsearch">查询</button>
                </div>
            </div>
            <div id="content" style="width: 100%;">
                <div class="marmain-cen">
                    <div class="content-medical" id="divtable">
                        <table id="laytable" lay-filter="laytable"></table>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <script data-main="../../js/operation/IntelligenceScreening" src="../../sys/require.min.js"></script>
</body>

</html>