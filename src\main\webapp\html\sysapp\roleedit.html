<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>角色信息处理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link type="text/css" rel="stylesheet" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css" media="all">
<!--    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">-->
    <style type="text/css">
    </style>
</head>
<body>
<div class="layui-page" style="padding: 0 15px;">
    <form id="from" action="" class="layui-form form-display" lay-filter="formOk">
        <div class="default-form" style="margin: 10px;">
            <div class="layui-form-item">
                <label class="layui-form-label" ><em style="color: #aa1111">* </em>角色名称</label>
                <div class="layui-input-block" >
                    <input type="text" maxlength="20" class="layui-input" id="rname" placeholder="请输入角色名称"
                           name="rname" style="width: 300px" lay-verify="rname"/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" >权限字符串</label>
                <div class="layui-input-block" >
                    <input type="text" maxlength="20" class="layui-input" id="rolekey" placeholder="请输入角色权限字符串"
                           name="rolekey" style="width: 300px" lay-verify="rolekey"/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><em style="color: #aa1111">* </em>显示顺序</label>
                <div class="layui-input-block">
                    <input type="text" maxlength="4" class="layui-input" id="rolesort" placeholder="请输入显示顺序数字"
                           name="rolesort" style="width: 300px" lay-verify="rolesort"/>
                </div>
            </div>
<!--            <div class="layui-form-item">-->
<!--                <label class="layui-form-label">数据范围</label>-->
<!--                <div class="layui-input-block" style="width: 300px">-->
<!--                    <select id="data_scope" name="data_scope">-->
<!--                        <option value="">请选择数据范围</option>-->
<!--                        <option value="1">全部数据权限</option>-->
<!--                        <option value="2">自定数据权限</option>-->
<!--                        <option value="3">本部门数据权限</option>-->
<!--                        <option value="4">本部门及以下数据权限</option>-->
<!--                    </select>-->
<!--                </div>-->
<!--            </div>-->
            <div class="layui-form-item">
                <label class="layui-form-label" ><em style="color: #aa1111">* </em>状态</label>
                <div class="layui-input-block">
                    <input type="radio" name="status" value="0" title="正常" checked>
                    <input type="radio" name="status" value="1" title="停用">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label" style="margin-left: -10px">备注</label>
                <div class="layui-input-block">
                    <input type="text" maxlength="20" class="layui-input" id="remark" placeholder="请输入备注"
                           name="remark" style="width: 300px" />
                </div>
            </div>
            <div style="display: none;">
                <div class="layui-input-block">
                    <input type="hidden" name="roletype" id="roletype"/>
                    <a id="saveOK" class="layui-btn" lay-submit="lay-submit">提交</a>
                </div>
            </div>
        </div>
    </form>
</div>
<script data-main="../../js/sysapp/roleedit" src='../../sys/require.min.js'></script>
</body>
</html>