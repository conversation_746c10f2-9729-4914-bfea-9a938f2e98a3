# 后端API修改需求文档

## 概述
前端已完成标签管理功能的增强，新增了三个字段。现需要后端API相应地支持这些新字段的处理。

## 需要修改的API接口

### 1. labellist.add (保存/更新标签)
**当前问题**: 后端接口未处理新增的三个字段，导致保存时新字段值丢失。

**需要支持的新字段**:
```json
{
  "algorithm_type": "EXPRESSION|SCRIPT|RULE",
  "algorithm_content": "算法代码内容（文本）",
  "is_active": 1|0
}
```

**字段说明**:
- `algorithm_type`: 字符串类型，枚举值：EXPRESSION、SCRIPT、RULE
- `algorithm_content`: 文本类型，可为空，用于存储算法代码
- `is_active`: 整数类型，1表示活动，0表示非活动

### 2. labellist.getbyid (获取单个标签详情)
**需要修改**: 返回数据中需要包含新增的三个字段。

**期望返回格式**:
```json
{
  "id": 1,
  "labelname": "标签名称",
  "labelremark": "标签描述",
  "algorithm_type": "EXPRESSION",
  "algorithm_content": "算法代码内容",
  "is_active": 1,
  "creatime": "2024-01-01 00:00:00",
  "altime": "2024-01-01 00:00:00"
}
```

### 3. labellist.getdata (获取标签列表)
**需要修改**: 列表数据中需要包含新增字段，特别是 `algorithm_type` 和 `is_active` 用于列表显示。

## 数据库表结构修改

### 建议的表结构变更
假设标签表名为 `tb_labellist` 或类似，需要添加以下字段：

```sql
-- 添加算法类型字段
ALTER TABLE tb_labellist ADD COLUMN algorithm_type VARCHAR(20);

-- 添加算法内容字段  
ALTER TABLE tb_labellist ADD COLUMN algorithm_content TEXT;

-- 添加是否活动字段
ALTER TABLE tb_labellist ADD COLUMN is_active SMALLINT DEFAULT 0;

-- 添加字段注释
COMMENT ON COLUMN tb_labellist.algorithm_type IS '算法类型：EXPRESSION、SCRIPT、RULE';
COMMENT ON COLUMN tb_labellist.algorithm_content IS '算法代码内容';
COMMENT ON COLUMN tb_labellist.is_active IS '是否活动：1-活动，0-非活动';
```

## 前端调用示例

### 保存数据时的请求格式
```javascript
$.sm(function (re, err) {
    // 处理响应
}, ['labellist.add', JSON.stringify({
    "labelname": "测试标签",
    "labelremark": "标签描述",
    "algorithm_type": "EXPRESSION",
    "algorithm_content": "if (condition) { return true; }",
    "is_active": 1
}), $.msgwhere({ id: [123] })])
```

### 获取数据时的期望响应
```javascript
// labellist.getbyid 响应
{
  "id": 123,
  "labelname": "测试标签",
  "labelremark": "标签描述", 
  "algorithm_type": "EXPRESSION",
  "algorithm_content": "if (condition) { return true; }",
  "is_active": 1,
  "creatime": "2024-01-01 12:00:00",
  "altime": "2024-01-01 12:00:00"
}
```

## 验证要点

### 1. 数据验证
- `algorithm_type` 必须是 EXPRESSION、SCRIPT、RULE 中的一个
- `algorithm_content` 可以为空
- `is_active` 必须是 0 或 1

### 2. 兼容性
- 确保现有的标签数据不受影响
- 新字段应该有合理的默认值
- 支持渐进式迁移

### 3. 测试建议
1. 测试新增标签时新字段的保存
2. 测试编辑现有标签时新字段的处理
3. 测试列表查询时新字段的返回
4. 测试字段验证逻辑

## 注意事项

1. **向后兼容**: 确保修改不会影响现有功能
2. **数据迁移**: 现有数据的新字段需要设置默认值
3. **错误处理**: 对无效的 `algorithm_type` 值进行适当的错误处理
4. **性能考虑**: `algorithm_content` 字段可能包含较长文本，注意查询性能

## 前端已完成的工作

1. ✅ HTML表单已添加三个新字段
2. ✅ JavaScript验证和数据处理已完成
3. ✅ 列表显示已支持新字段
4. ✅ 复选框样式对齐已修复
5. ✅ 数据格式转换已实现（复选框布尔值↔数值）

## 下一步行动

1. 后端开发人员根据此文档修改相关API
2. 数据库管理员执行表结构变更
3. 进行联调测试
4. 部署到测试环境验证
