<!DOCTYPE html>
<html>
<head>
<title>表1-15托育机构膳食委员会会议记录表</title>
<meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
<link rel="stylesheet" href="../css/reset.css"/>
<link rel="stylesheet" href="../css/style.css"/>
<link rel="stylesheet" href="../css/icon.css">
<script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
<style>
body {
	background: #fff;
}
th, td {
	padding: 10px 20px;
}
</style>
</head>
<body>
<div style="width: 90%;margin: 0 auto;">
  <div class="rep-table tab_parent_sec" style="width: 90%;margin: 0 auto 0 auto;padding: 30px 0 0px 0;">
    <div class="divhead" title="表1-12托育机构婴幼儿伤害登记表" style="text-align: center; font-size: 16pt; font-weight: bold; font-family: 宋体, 微软雅黑, 黑体; margin: 0px auto; width: 100%;">
      <div class="divprintreport" style="font-size:12pt;font-weight:bold;text-align:center; display:block; margin-bottom:10px;"> 表1-12托育机构婴幼儿伤害登记表 </div>
    </div>
  </div>
  <div style="height:25px;line-height: 25px;">
    <div class="divprintreport" style="font-size:10pt;font-weight:bold;text-align: left;display:inline;"> <span style="float:right;"> 年
      <label style="display:inline-block; margin-left: 50px"> 月</label>
      </span> </div>
  </div>
  <div style="margin-top:10px;">
    <table class="reporttable" style="clear: both; margin: 0px auto; font-size: 11pt; font-family: 宋体; border: 2px solid black; width: 100%; text-align:left;" cellspacing="0" cellpadding="1" border="0">
      <tbody>
        <tr>
          <td height="35" style="border-top:1px solid Black; vertical-align:top;" ><div style="width:20%; float:left;">班级</div>
            <div style="width:20%; float:left; ">姓名</div>
            <div style="width:20%; float:left;">性别</div>
            <div style="width:20%; float:left;">年龄 </div></td>
        </tr>
        <tr >
          <td height="33" style="border-top:1px solid Black;vertical-align:top;" ><div style="width:200px; float:left;">发生的地点： </div>
            <div style="width:200px; float:left;">发生时的活动： </div></td>
        </tr>
        <tr >
          <td height="33" style="border-top:1px solid Black;vertical-align:top;" ><div style="width:200px; float:left;">损伤的部位： </div>
            <div style="width:200px; float:left;">损伤恢复时间： </div></td>
        </tr>
        <tr >
          <td height="33" style="border-top:1px solid Black;vertical-align:top;" ><div style="width:200px; float:left;">转归： </div>
            <div style="width:200px; float:left;">当班责任人： </div></td>
        </tr>
        <tr>
          <td height="80" style="border-top:1px solid Black; vertical-align:top;" >简述伤害发生经过：（对损伤过程作综合描述）： </td>
        </tr>
        <tr>
          <td height="80" style="border-top:1px solid Black; vertical-align:top;" >医疗处理：（医院的最后诊断和治疗意见） </td>
        </tr>
        <tr>
          <td height="80" style="border-top:1px solid Black; vertical-align:top;" >伤害原因分析： </td>
        </tr>
        <tr>
          <td height="80" style="border-top:1px solid Black; vertical-align:top;" >园领导意见： </td>
        </tr>
   
      </tbody>
    </table>
    <div class="divprintreport" style="font-size:10pt;font-weight:bold;text-align: left;display:inline;">备注：1．登记范围：交通事故、跌伤（跌、摔、滑、绊）、被下落物击中（高处落下物）、锐器伤（刺、割、扎、划）、钝器伤（碰、砸）、烧烫伤（火焰、高温固/液体、化学物质、锅炉、烟火、爆竹炸伤）、溺水、动物伤害（狗、猫、蛇等咬伤、蜜蜂、黄蜂等刺蜇）、窒息（异物，压、闷、捂窒息，鱼刺/骨头卡喉）、中毒（药品、化学物质、一氧化碳等有毒气体,农药,鼠药,杀虫剂，腐败变质食物除外）、电击伤（触电、雷电）、他伤/攻击伤等。2．伤害发生地点：户外活动场、活动室、寝室、卫生间、盥洗室、其他（请说明）。3．转归：按痊愈、好转、残疾、死亡分别填写。4.此表由保健人员填写。</div>
  </div>
</div>
<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../system/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../system/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../plugin/nicescroll/jquery.nicescroll.min.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../plugin/js/moment.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../plugin/viewer/viewer.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../js/order/orderdetail.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>
