﻿
.cuptop{background: #ffffff;border-radius: 5px; padding: 10px 10px;color: #31373D;  }
.cuptopbg{ margin:10px 0; background: #ffffff;border-radius: 5px; padding: 10px 15px;color: #31373D;}
.cuptopcenbg{ margin:10px; background: #ffffff;border-radius: 5px; }

/*选项开会样式*/
.layui-form-onswitch {
  border-color: #174898;
  background-color: #174898;
}
/*表格从写*/
.layui-table td, .layui-table th{font-size: 14px;border-color: #E6E8EB; text-align: center;}
.layui-input, .layui-select, .layui-textarea{border-radius: 2px;}
.layui-table thead tr, .layui-table-click, .layui-table-header,  .layui-table-mend, .layui-table-patch, .layui-table-tool, .layui-table-total, .layui-table-total tr{background:#FAFAFA;}
.layui-table,input,select{color: #666666;;}
.gray-main .layui-table td, .gray-main .layui-table th,.gray-main .layui-table-col-set,.gray-main .layui-table-fixed-r,.gray-main .layui-table-grid-down,.gray-main .layui-table-header,.gray-main .layui-table-page,.gray-main .layui-table-tips-main,.gray-main .layui-table-tool,.gray-main .layui-table-total,.gray-main .layui-table-view,.gray-main .layui-table[lay-skin="line"],.gray-main .layui-table[lay-skin="row"] {
	border-bottom: 1px solid #DCDFE6;border-width:0px; }
.gray-main .cuptop{padding: 10px;}
.layui-table{margin: 0;}
.layui-table[lay-even] tr:nth-child(even){background-color: transparent;}
.layui-table th{padding: 3px 15px;font-weight: bold;height: 28px;line-height: 28px; color: #31373D;}
/*.layui-table td{padding: 3px 15px;position: relative;text-align: left;height: 28px;line-height: 28px;}*/
.layui-table tbody tr:hover, .layui-table-hover{background: #f5f5f5!important;}
/*.layui-table-view .layui-table td, .layui-table-view .layui-table th{padding: 3px 10px;}*/
.layui-table[lay-skin="row"] td, .layui-table[lay-skin="row"] th {
    border-width: 0 1px 1px 0;}
.default-table{margin: 0 15px;}
.default-table .layui-table{margin: 0;}
.lineinput input[type="text"]{border: none;width: 100%;height: 100%;position: absolute;bottom: 0;left: 0;text-align: center;box-sizing: border-box;border-bottom: 1px solid #00968b;border-radius: 0;display: inline-block;}
.lineinput input[type="text"]:focus{border-bottom: 2px solid #00968b;box-sizing: border-box;bottom: -1px;}
.cuptop .layui-form-item{margin-bottom: 5px;}
.layui-input, .layui-select, .layui-textarea{height: 32px;}
.layui-table-cell a,.layui-table-cell i{cursor: pointer;}
/*新版日历*/
.layui-comselect .layui-alendar{width: auto;height: auto;left: 10px;position: absolute; top:5px;cursor: pointer;}
.default-btn{min-width: 44px;height: 32px;line-height: 26px;margin:0 10px 0 0;text-align: center;padding: 3px 8px;box-sizing: border-box;font-size: 13px;display: inline-block;color: #ffffff;border-radius: 2px;vertical-align: top;cursor: pointer;}
.default-btn.yellow{background:#FFC65E; }
.default-btn.purple{background:#8486F8; }
.default-btn.green{background:#1DCE75; }
.default-btn.blue{background: #0080FF; }
.default-btn.ltblue{background: #174898; }
.default-btn img{width:14px; margin-right: 2px;vertical-align: top;margin-top: 6px; }
.default-btn a:hover, .default-btn:hover{
  color: #fff !important;
}


.select-open{border: 1px solid #DDDDDD; }
/*栏目重写*/
.noticeTitle{font-weight: bold;border-top-left-radius:5px;border-top-right-radius:5px;background: #D1DEEB; height: 45px; line-height: 45px; padding: 0 10px; font-size: 15px;}
.noticeTitle,.noticeTitle a{color: #34495E;}
.formList label{width: 101px;text-align: right;display: inline-block;}
.formList input[type="text"] {
    border: 1px solid #E6E8EB;
    height: 35px;
    line-height: 35px;
    padding: 0 8px;
    border-radius:4px;
}
.formList input[type="text"] {
    width: 520px;
}
.formList select {
    border: 1px solid #ccc;
    height: 35px;
    line-height: 35px;
    padding: 0 4px;
    border-radius: 0;
    color: #6C6C6C;
    margin-left: -4px;
    border-radius:4px;
}

.layui-input.form-ordernum{padding-left: 35px;}
.knowledgebox,.recipeinfobox{cursor: pointer;}

/*入院体检记录*/
.default-tab .layui-tab-title .layui-this{color: #174898;margin-left: 15px;}
.default-tab .layui-tab-title .layui-this:after{border-bottom: 4px solid #174898;}
.btncom,.report-list a.btncom {display: inline-block;
height: 24px;
background: #FFFFFF;
border-radius: 2px 2px 2px 2px;
opacity: 1;
border: 1px solid #E6E8EB; text-align: center;color: #0D3372; line-height: 24px; padding: 0px 8px; margin-right: 5px;text-decoration: none; }
.cuptop-rtbtn{position: absolute;top: 23px;right: 20px;}
/*体检公示列表*/
.table-details{border: 1px solid #E6E8EB;margin-bottom: 15px;}
.table-details h5{ line-height:45px; height: 45px;  color: #174898; font-size: 14px;font-weight: bolder;padding-left: 10px;border-bottom: 1px solid #E6E8EB;}
.announcement { margin: 20px; display: block;}
/*入院体检记录查看详情*/ 
.form-list{margin: 10px auto; position: relative;border-bottom: 1px solid #F0F3F7; padding: 5px 0px}
.form-list .layui-form-label{padding: 5px 0;color:#31373D; }
.form-list .layui-input-block{ color:#666666;line-height: 30px; }


/*专家建议模板*/ 
.tem-list{border: 1px solid #E6E8EB;margin-bottom: 15px;}
.tem-list h3{background: #FAFCFF;line-height:45px; height: 45px;  color: #31373D;font-size: 14px;padding-left: 10px;}
.tem-list h3 span{ margin-top: -5px; display: inline-block; cursor: pointer; margin-right: 10px;}
.tem-down{ padding: 10px 20px; line-height: 20px; color:#666666; font-size: 14px; }
.tem-top{ background: #FFFDF8; height: 40px;  width: 100%; border-bottom: 1px solid #E6E8EB;border-top: 1px solid #E6E8EB; clear: both;}
.tem-top:first-child{border-top:none;}
.tem-top span{line-height:45px; height: 45px;  color: #31373D;font-size: 14px;padding-left: 10px;}
.tem-right{ float: right; margin-top: -5px; display: inline-block; cursor: pointer; margin-right: 10px;}


/*调查问卷*/ 

.questionnaire-list{font-size: 0;margin: 10px 0px;}
.questionnaire-list li{ margin-bottom: 10px; margin-left: 15px; background: #fff; display:inline-block;width: 24%;height:180px;font-size: 14px;vertical-align: top;position: relative; box-shadow: 0px 3px 8px -1px rgba(50,50,71,0.05), 0px 0px 1px 0px rgba(12,26,75,0.24); }
.questionnaire-list h3{color: #31373D; font-size: 16px;  line-height:20px;font-weight: bold;}
.questionnaire-list p{font-size:14px; color: #666666;line-height:30px;}
.question-list-st {border-bottom: 1px solid rgb(230, 232, 235); padding: 15px 15px;}
.question-list-st label{color: #31373D; display: inline-block; margin-left: 10px;}
.layui-btn.orange{background: #FF7E3E;height: 32px; line-height: 32px; margin-top: 15px; border-radius: 20px;}
.edit-txt{color: #A3A3A3; font-size: 14px;padding:15px 6px;}
.edit-txt img{width: 17px;vertical-align: top; margin-top: 2px;}
.edit-txt span{display: inline-block;width: 19%;cursor: pointer;text-align: center;}
.topic-txt{height: 38px;background: #FAFAFA; line-height: 38px; margin: 15px 0px; color: #31373D; text-align: center; font-size: 16px;}
.allcontent-col3right .layui-form-label{text-align: left; width:75px;color: #000000;padding: 9px 0px}
.allcontent-col3right .layui-input-block{margin-left: 75px;}
.questionnaire-title { background: #fff; margin:10px; padding: 15px;color: #31373D;}
.questionnaire-title  .layui-form-label{ width:99px;padding: 6px 0px}
.questionnaire-title  .investi-list{margin: 0px;}
.questionnaire-title  .investi-list .titletxt{font-size: 14px;}
.questionnaire-title  .layui-form-radio{margin: 0px 0px 0 0;}
.questionnaire-title   .investi-tit  i{ display: inline-block;}
.edit-txtblue span{display: inline-block;cursor: pointer;text-align: center;margin-right: 15px;}
.edit-txtblue{color: #174898; font-size: 14px;padding:5px 0px; }
.edit-txtblue img{width: 17px;}
.editdel{float: right;color: #A3A3A3;display: inline-block;}
.editdel span{display: inline-block;cursor: pointer;text-align: center;margin-left: 15px;font-size: 12px;}
.editdel img{width: 17px;vertical-align: top;}
.allcontent-col3left .default-tabsel{display: block;font-size: 0;background: #F5F6FA; padding: 5px;border: none;}
.allcontent-col3left .default-tabsel li{ display: block; cursor: pointer;border-right: none;font-size: 12px;height: 24px;line-height: 24px;color: #31373D;}
.mobantxt{cursor: pointer;margin: 10px auto 10px auto;width: 88px;height: 30px;color: #174898;line-height: 30px;font-size: 14px;background: #FFFFFF;border-radius: 2px 2px 2px 2px;opacity: 1;border: 1px solid #E6E8EB; text-align: center;}
.allcontent-col3left .default-tabsel li:last-child{border-right: none;}
.allcontent-col3left .choose-txt li{margin-left: 40px;height: 40px;/*background: #F7F8FA;*/border-radius: 2px 2px 2px 2px;color: #31373D; margin-top: 10px; line-height: 40px; cursor: pointer;}
.allcontent-col3left .choose-txt li img{width: 16px; margin-right: 5px;vertical-align:top; margin-top: 12px;}
.allcontent-col3left .radio-blue input[type="radio"]{-webkit-appearance: none;width: 16px;height: 16px;margin: -2px 5px 0 0px}
.allcontent-col3left .radio-blue input[type="radio"]:checked{width: 16px;height: 16px;}
.allcontent-col3left .allcontent-col3left .default-tabsel li.set-checkbox-style input[type="checkbox"]{margin-top: 11px;}
.questionstxt{height: 200px;background: #F7FAFC;opacity: 1;border: 1px dashed #000000;color: #000000; margin: 0 10px;}
.questionstxt  p{padding-top: 90px; text-align: center;}
.questionstxt span{ display: inline-block; margin-right: 5px; cursor: pointer;}
.questionstxt span img{width: 16px; margin-right: 5px;}

/*园所审核*/
.kindergarten-list{font-size: 0;margin: 10px 0px;}
.kindergarten-list li{min-width: 360px; margin-bottom: 10px; margin-left: 15px; background: #fff; display:inline-block;width: 24%;font-size: 14px;vertical-align: top;position: relative; box-shadow: 0px 3px 8px -1px rgba(50,50,71,0.05), 0px 0px 1px 0px rgba(12,26,75,0.24); }
.kindergarten-list h3{color: #31373D; font-size: 16px;  line-height:20px;font-weight: bold;}
.kindergarten-list p{font-size:14px; color: #666666;line-height:30px;}
.kindergarten-list-st {border-bottom: 1px  solid #E6E8EB; padding: 15px 15px;overflow: hidden;}
.kindergarten-list-st label{color: #31373D; display: inline-block; margin-left: 10px;}
.layui-btn.orange{background: #FF7E3E;height: 32px; line-height: 32px; margin-top: 15px; border-radius: 20px; }
.kindergarten-list-bot{background: #F7FAFC; padding: 5px 10px; margin:20px 0 0px 0;overflow: hidden;}
.layui-btn.kinderblue {
  background: #174898;
  height: 32px;
  line-height: 32px;
  margin-top: 15px;
  border-radius: 5px;
	position: absolute; top:10px;
	right: 15px;
}
.kindergarten-list-bot p{font-size:14px; color: #666666; line-height: 21px;font-size: 14px; }
.agree-txt{ text-align: center;  color: #fff; margin: 10px 0 10px 0;}
.agree-txt span{
height: 32px; line-height: 32px; padding: 0 10px;
background: #1DCE75;
border-radius: 4px;}
.agree-txt span.redbg{
background:#FC4956;
}
.agree-txt span.blue{
  background: #174898;
  color: #fff;
}
.agree-txt span.del,.cl2.del{
 color: #FC4956;
 background: #FFE3DB;
  border: 1px solid #FC4956; padding: 0 20px;
}
.cl2.del{ height: 33px; line-height: 33px;display:inline-block;border-radius: 3px; margin-left: 10px;
}
.agree-txt span.del, .cl2.del {
  color: #FC4956;
  background: #FFE3DB;
  border: 1px solid #FC4956;
  padding: 0 20px;
}
a {
  color: #333;
  text-decoration: none;
}
a {
  font-size: 100%;
  vertical-align: baseline;
  background: transparent;
  text-decoration: none;
  outline: none;
  cursor: pointer;
  color: #7f7f7f;
}
.formList {
  font-size: 14px;
  word-break: break-all;
}
body {
  line-height: 1.6;
  color: #333;
  color: rgba(0,0,0,.85);
  font: 14px Helvetica Neue,Helvetica,PingFang SC,Tahoma,Arial,sans-serif;
    font-size: 14px;
    line-height: normal;
}

.agree-txt span{ display: inline-block; margin-right:10px; cursor: pointer;}
.agree-txt span img{width: 16px; margin-right: 5px;}
@media screen and (min-width:1400px) and (max-width:1500px){
    .kindergarten-list li{ width: 23.5%;}
}
@media screen and (min-width:800px) and (max-width:1400px){
	.kindergarten-list li{ width: 23.5%;}
}
.state-img{position: absolute; right: 10px; top:15px;}
/*添加选项*/
.batch-add-left{float: left; width: 580px;}
.batch-add-right{float: right;width:307px;  }
.batch-add-right h3{color: #31373D;line-height: 30px; height: 30px; margin-bottom: 5px; margin-left: 10px;}
.batch-add-right li{cursor: pointer;float: left; margin-left:10px;margin-bottom: 10px; height: 32px;background: #FFFFFF;border-radius: 4px 4px 4px 4px;opacity: 1;border: 1px solid #E6E8EB; width: 90px;text-align: center; line-height: 32px;color: #174898;}
/*页面布局*/
.allcontent{position: absolute;bottom: 0;width: 100%;box-sizing: border-box;height: 100%;}
.allcontent .contentleft{float: left;width: 200px;height: 100%;}
.allcontent .contentright{overflow: hidden;height: 100%;position: relative;}
.allcontent-col3{width: 100%;height:100%;position: relative;}
.allcontent-col3left{font-size: 14px;height: 100%;display: inline-block;background: #fff;width:204px;}
.allcontent-col3cen{font-size: 14px;height: 100%;display: inline-block;position: absolute;left: 204px;right: 390px;}
.allcontent-col3right{font-size: 12px;height: 100%;display: inline-block;width: 390px;float: right;background: #fff;}
.batch-add{width:900px; margin: 0 auto;}
/*增减箭头*/
.sp_creatime,.sp_buydate{display: inline-block;width: 14px;height: 18px;line-height: 8px;vertical-align: middle;}

/*幼儿园绩效考核*/
 .tab-blue {
 color: #0F3F8E;
}
 a.tab-blue,a.tab-red {
  text-decoration: underline;
}
.kindergarten-name {font-size: 24px; text-align: center;
color: #31373D;
line-height: 30px; margin: 0 0 10px 0;}
.kindergarten-name span{display: inline-block; float: right;font-size: 14px; color: #31373D;}
.kindergarten-name label{margin-right: 10px;}

.kindergarten-bg{background: #F6FBFF;
 padding: 15px; margin:15px;overflow: auto; position: relative;
}

.kindergarten-bg p{font-size: 14px;color: #666666;}
.kindergarten-bg p label{color: #31373D;font-size: 14px;}
.kindergarten-bg .codeimg{ position: absolute; top:15px; left: 15px;width: 24px; }
.kindergarten-bgleft{ margin-left: 35px;display: block;float: left;line-height: 25px;}
.kindergarten-bgleft p.font12{font-size: 12px;}
.kindergarten-bgleft b{color: #174898;}
/*统计*/
.default-btn-rt{float: right; margin-top: 15px;}

.legend_bjbg {
  background: #fff;
  padding: 10px;
  position: relative;
}
.kindergarten-listleft{float: left; margin-right: 5px;}


/*添加用户信息*/

.adduser-div .layui-form-label {
	display: block;
	float: none;
	text-align: left;
	padding: 10px 20px 10px 20px;
	min-width: auto;
	width: auto;
	color: rgba(102, 102, 102, 1);
}
.adduser-div .layui-input-block {
	margin: 0 20px 0 20px;
	color: rgba(102, 102, 102, 1);
}
.adduser-list {
	float: left;
	width: 50%;
}
.adduser-div {
	margin: 15px 20px 0 20px;
	overflow: hidden;
}

.div_btn .layui-btn{line-height: 32px; height: 32px;}
/*历史上报*/
.agent-left { border-right: 1px solid #EEEEEE;width: 216px; display: inline-block; font-size: 16px; position: absolute; height: 100%; overflow-y: auto; overflow-x: hidden; }
.agent-left ul{ padding-top: 10px;}
.agent-left  li{color: #666666;line-height: 30px; font-size: 14px; padding-left: 15px; cursor: pointer;}
.agent-left  li.curent{color: #174898;border-right: 1px solid #174898;}
.agent-right { vertical-align: top; font-size: 14px; margin-left: 216px; padding: 0 10px;}
.btableleft{float: left; border-right:1px solid #E6E8EB; margin: 10px; padding-right: 20px;}
.btableleft p{ margin-top: 5px;color: #174898;font-size: 14px; text-decoration: underline;}
.btableleft p img{margin-right: 5px;}
.rel-list-congtent{float: left;}
.history-list{border-bottom: 1px solid #E6E8EB; overflow: hidden;}