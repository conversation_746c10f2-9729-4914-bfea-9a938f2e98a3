// 2016.04.11
// 郭玉峰
require.config({
    paths: {
        system: '../sys/system',
        jquery: '../sys/jquery'
    },
    waitSeconds: 0

});
var objdata = {};
require([], function () {
    var type = Arg("type");
    var stuno = Arg("id");
    loadData();
    $("#btnsearch").on('click', function () {
        loadData();
    });
    $("#ulchangetype").children().click(function () {
        objdata.strmsgtype = $(this).attr('type');
        $(this).addClass('current').siblings().removeClass('current');
        loadData();
    })
    function loadData() {
        var swhere = {};
        if(stuno){
            swhere.stuno = [stuno];
        }
        if($("#txtsearch").val()){
            swhere.msgcontent = [$("#txtsearch").val()];
        }
        if(objdata.strmsgtype){
            if(objdata.strmsgtype=='image'){
                swhere.msgtype = [2];
            } else if(objdata.strmsgtype=='voice'){
                swhere.msgtype = [3];
            } else if(objdata.strmsgtype=='askleave'){
                swhere.msgtype2 = [11,12];
            }
        }
        var totalmsg = ["index.homemsg.totalnum", $.msgwhere(swhere)];
        $.sm(function (re, err, obj) {
            if (!obj) {
                obj = {count: 0};
            }
            var objbabyinfo = parent.objdata.babys[stuno];
            layui.laypage.render({
                elem: 'laypage1'
                , groups: 4
                , limit: 20
                , count: obj.count //数据总数，从服务端得到
                , jump: function (obj, first) {
                    //obj包含了当前分页的所有参数，比如：
                    console.log(obj.curr); //得到当前页，以便向服务端请求对应页的数据。
                    console.log(obj.limit); //得到每页显示的条数
                    if (first) {
                        $("#uldata").html('')
                    }
                    $.sm(function (re, err) {//fromtype,fromid,ptype,msgtype,msgcontent,creatime,fromname
                        var strhtml = '';
                        for (var i = 0; i < re.length; i++) {
                            var obj = re[i];
                            var strclass = '';
                            var face = '../images/desktop/default.png';
                            var name = '';
                            if(obj.fromtype==1){
                                if (obj.ptype == 1) {
                                    name = objbabyinfo.name + "爸爸";
                                    face = '../images/newtong/defaultbaba.png';
                                } else if (obj.ptype == 2) {
                                    name = objbabyinfo.name + "妈妈";
                                    face = '../images/newtong/defaultmama.png';
                                } else {
                                    name = objbabyinfo.name + "家长";
                                    face = '../images/newtong/defaultbaba.png';
                                }
                            } else {
                                if (obj.fromid == parent.objdata.my.id) {
                                    name = "我";
                                } else {
                                    name = obj.fromname;
                                }
                                strclass = 'layim-chat-mine';
                            }
                            strhtml +=
                                '<li class="'+ strclass +'">\
                                <div class="layim-chat-user">\
                                <img src="'+ face +'">\
                                '+ (strclass?'<cite><i>'+ re[i].creatime.substring(0,19) +'</i>'+ name +'</cite>':'<cite>'+ name +'<i>'+ re[i].creatime.substring(0,19) +'</i></cite>') +'\
                                </div>\
                                <div class="layim-chat-text">'+parent.layim.getContentHtml({ msgtype:obj.msgtype,objmsgcontent:JSON.parse(obj.msgcontent) }) +'</div>\
                            </li>';
                        }
                        $("#uldata").html(strhtml)
                    }, ["index.homemsg.pagedata", swhere, obj.curr, obj.limit])
                }
            });
        }, totalmsg);
    }
});