﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <link rel="shortcut icon" href="/images/favicon.ico" type="image/x-icon" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="Copyright" content="Copyright 2016 by tongbang" />
    <meta name="Author" content="" />
    <meta name="Robots" content="All" />
    <title>表1-1托育机构儿童晨午检及全日健康观察表</title>
    <link href="../../styles/admin.css" rel="stylesheet" type="text/css" />
    <link href="../../styles/tbstyles.css" type="text/css" rel="stylesheet" />
    <link href="../../plugin/ehaiform/css/tbbaseform.css" rel="stylesheet" type="text/css" />
    <link href="../../plugin/editselect/css/editselect.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../plugin/jquery_tabs/css/tbjquery.tabs.css" type="text/css"
          media="print, projection, screen" />
	<style>
		.layui-form-label{width: 120px;}
		.rytjdjedit_table td{border:#eee solid 1px}
        /*滚动条样式*/
        html{
            color: #000;
            font-size: .7rem;
            font-family: "\5FAE\8F6F\96C5\9ED1",Helvetica,"黑体",Arial,Tahoma;
            height: 100%;
        }
        /*滚动条样式*/
        html::-webkit-scrollbar {
            display:none;
            width: 6px;
            height: 6px;
        }
        html:hover::-webkit-scrollbar{
            display:block;
        }
        html::-webkit-scrollbar-thumb {
            border-radius: 3px;
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
            border: 1px solid rgb(255, 255, 255);
            background: rgb(66, 66, 66);
        }
        html::-webkit-scrollbar-track {
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
            border-radius: 0;
            background: rgba(0,0,0,0.1);
        }
        .full_disease {
            display: inline-block;
            background: #eee;
            padding:0 10px;
            width:60px;
            text-align: center;
        }
		.rytj_tz{padding-left: 0;}
	</style>
</head>
<body>
    <div class="bodywidth" style="min-width: 920px;">
        <div class="content">
            <div class="layui-form layui-comselect" style="position: relative;padding: 10px 10px 5px 10px;">
                <div style="margin:10px 0;">
                    <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                        <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">日期：</label>
                        <div class="layui-input-inline" style="min-height: 30px;width:180px;">
                            <input id="day" type="text" placeholder="日期" readonly class="layui-input"/>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                        <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">班级：</label>
                        <div class="layui-input-inline" style="min-height: 30px;width:180px;">
                            <select lay-filter="sel_classno" id="classno">
                                <option value="">请选择</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                        <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">幼儿：</label>
                        <div class="layui-input-inline" style="min-height: 30px;width:180px;">
                            <select lay-filter="sel_stuno" id="stuno">
                                <option value="">请选择</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div style="margin: 10px 0;">
                    <div class="layui-form-label">晨检情况：</div>
                    <div class="layui-form-item" style="display: block;vertical-align: top;">
                        <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">家长主诉与检查：</label>
                        <div class="layui-input-inline" style="min-height: 30px;width:580px;">
                            <div class="rytj_tz">
                                <input type="text" class="txtweight" id="morning_check" maxlength="200" style="width: 95%;"/>
                                <span class="FancyInput__bar___1P3wW" ></span>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item" style="display: block;vertical-align: top;">
                        <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">处理：</label>
                        <div class="layui-input-inline" style="min-height: 30px;width:580px;">
                            <div class="rytj_tz">
                                <input type="text" class="txtweight" id="morning_handle" maxlength="200" style="width: 95%;"/>
                                <span class="FancyInput__bar___1P3wW" ></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="rytjdjedit_table" id="divrytjdj" style="margin: 10px 0;">
                    <div class="layui-form-label">全日健康观察：</div>
                    <table border="1" cellspacing="0" cellpadding="0" id="tbrytjdj" style="width: 80%;">
                        <tr>
                            <td style="width: 20%;">
                                <div>症状</div>
                            </td>
                            <td>
                                <div style="text-align:left;padding:5px 15px;">
                                    <div class="full_disease" data-choose="0">咳嗽</div>
                                    <div class="full_disease" data-choose="0">喉咙发炎</div>
                                    <div class="full_disease" data-choose="0">感冒</div>
                                    <div class="full_disease" data-choose="0">发热</div>
                                    <div class="full_disease" data-choose="0">乏力</div>
                                    <div class="full_disease" data-choose="0">嗅味减退</div>
                                </div>
                                <div style="text-align:left;padding:5px 15px;">
                                    <div class="full_disease" data-choose="0">流涕</div>
                                    <div class="full_disease" data-choose="0">皮疹</div>
                                    <div class="full_disease" data-choose="0">腹泻</div>
                                    <div class="full_disease" data-choose="0">鼻塞</div>
                                    <div class="full_disease" data-choose="0">咽痛</div>
                                    <div class="full_disease" data-choose="0">结膜炎</div>
                                    <div class="full_disease" data-choose="0">肌痛</div>
                                </div>
                                <div class="layui-form-item" style="display: block;vertical-align: top;">
                                    <label class="layui-form-label" style="padding: 5px 0 5px 10px;line-height: 28px;width: 75px;">其他症状：</label>
                                    <div class="layui-input-inline" style="min-height: 30px;width:180px;">
                                        <div class="rytj_tz">
                                            <input type="text" class="txtweight" id="full_disease" maxlength="150" style="width: 95%;"/>
                                            <span class="FancyInput__bar___1P3wW" ></span>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div>体检</div>
                            </td>
                            <td>
                                <div class="rytj_tz">
                                    <input type="text" class="txtweight" id="full_check" maxlength="200" style="width: 95%;"/>
                                    <span class="FancyInput__bar___1P3wW" ></span>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div>处理</div>
                            </td>
                            <td>
                                <div class="rytj_tz">
                                    <input type="text" class="txtweight" id="full_handle" maxlength="200" style="width: 95%;"/>
                                    <span class="FancyInput__bar___1P3wW" ></span>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>
                <div class="layui-form-item" style="display: block;vertical-align: top;">
                    <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">检查者：</label>
                    <div class="layui-input-inline" style="min-height: 30px;width:180px;">
                        <div class="rytj_tz">
                            <input type="text" class="txtweight" id="check_person" maxlength="20" style="width: 95%;"/>
                            <span class="FancyInput__bar___1P3wW" ></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript">
        var v = top.version;
        document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../sys/function.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../js/richang/tuoyucommon.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../js/richang/tuoyuchenwujiandetail.js?v=" + v + "'><" + "/script>");
    </script>
</body>
</html>
