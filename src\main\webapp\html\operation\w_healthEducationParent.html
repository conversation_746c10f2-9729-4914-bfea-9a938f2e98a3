<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>家长阅读情况</title>
    <link rel="stylesheet" type="text/css" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" type="text/css" href="../../css/icon.css"/>
    <link rel="stylesheet" type="text/css" href="../../styles/tbstyles.css"/>
    <link href="../../plugin/flexigrid/css/tbflexigrid.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css"/>
    <script type="text/javascript">
        // 检查 top.objdata 是否存在，如果不存在则设置默认主题
        var curtheme = (top && top.objdata && top.objdata.curtheme) ? top.objdata.curtheme : 'backstage';
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + curtheme + '.css" type="text/css" media="screen"/>');
    </script>

    <style type="text/css">
        html, body {
            background: #eaeff3;
            overflow: hidden;
        }
        .layui-form-item {
            display: inline-block;
            vertical-align: top;
            margin: 5px 5px 0px 0;
        }

        .layui-form-label {
            line-height: 28px;
            width: 110px;
        }

        .layui-input-inline {
            width: 120px;
        }
        
        .search-container {
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin: 10px;
            padding: 15px;
        }
        
        .search-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }
        
        .search-item {
            display: flex;
            align-items: center;
            margin-right: 15px;
            margin-bottom: 5px;
        }
        
        .search-item label {
            font-size: 14px;
            color: #333;
            margin-right: 8px;
            white-space: nowrap;
            line-height: 32px;
        }
        
        .search-item input,
        .search-item select {
            width: 120px;
            height: 32px;
            line-height: 32px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 0 8px;
            font-size: 14px;
        }
        
        .search-item.wide input {
            width: 160px;
        }
        
        .search-item.keyword input {
            width: 180px;
        }
        
        .date-range {
            display: flex;
            align-items: center;
            margin-right: 15px;
        }
        
        .date-range input {
            width: 120px;
            height: 32px;
            line-height: 32px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 0 8px;
            font-size: 14px;
        }
        
        .date-separator {
            margin: 0 8px;
            color: #666;
        }
        
        .btn-search {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            height: 32px;
            margin-left: 10px;
        }
        
        .btn-search:hover {
            background: #40a9ff;
        }
        
        .table-container {
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin: 10px;
            padding: 15px;
            height: calc(100vh - 220px);
            overflow: auto;
        }

        .search-row {
            display: flex;
            flex-wrap: wrap;
        }
        .search-item {
            width: 280px;
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            margin-right: 0;
        }
        .search-item label {
            width: 100px;
            text-align: right;
            margin-right: 8px;
        }
        .search-item input,
        .search-item select {
            flex: 1;
            min-width: 0;
            height: 32px;
            line-height: 32px;
        }


    </style>
</head>
<body>
<div class="marmain">
    <div class="search-container">
        <form class="layui-form">
            <div class="search-row">
                <div class="search-item" style="width:340px;">
                    <label>开始日期：</label>
                    <input id="datestart" type="text" autocomplete="off" placeholder="开始日期" class="layui-input" />
                    <span style="margin: 0 8px; color: #666;">至</span>
                    <input id="dateend" type="text" autocomplete="off" placeholder="结束日期" class="layui-input" />
                </div>
                <div class="search-item">
                    <label>分类：</label>
                    <select id="category" lay-filter="category">
                        <option value="">请选择</option>
                        <option value="健康教育">健康教育</option>
                        <option value="营养指导">营养指导</option>
                        <option value="疾病预防">疾病预防</option>
                        <option value="心理健康">心理健康</option>
                    </select>
                </div>
                <div class="search-item">
                    <label>宣教文章：</label>
                    <select id="articleTitle" lay-filter="articleTitle">
                        <option value="">请选择</option>
                    </select>
                </div>
                <div class="search-item">
                    <label>行为类别：</label>
                    <select id="satisfaction" lay-filter="satisfaction">
                        <option value="">请选择</option>
                        <option value="点赞">点赞</option>
                        <option value="分享">分享</option>
                        <option value="收藏">收藏</option>
                    </select>
                </div>
                <div class="search-item">
                    <label>幼儿园：</label>
                    <select id="kindergarden" lay-filter="kindergarden">
                        <option value="">请选择</option>
                    </select>
                </div>
                <div class="search-item">
                    <label>年级：</label>
                    <select id="grade" lay-filter="grade">
                        <option value="">请选择</option>
                        <option value="小班">小班</option>
                        <option value="中班">中班</option>
                        <option value="大班">大班</option>
                    </select>
                </div>
                <div class="search-item">
                    <label>班级：</label>
                    <select id="classname" lay-filter="classname">
                        <option value="">请选择</option>
                    </select>
                </div>
                <div class="search-item">
                    <label>幼儿性别：</label>
                    <select id="childsex" lay-filter="childsex">
                        <option value="">请选择</option>
                        <option value="男">男</option>
                        <option value="女">女</option>
                    </select>
                </div>
                <div class="search-item">
                    <label>体检年龄：</label>
                    <select id="childage" lay-filter="childage">
                        <option value="">请选择</option>
                        <option value="3">3岁</option>
                        <option value="4">4岁</option>
                        <option value="5">5岁</option>
                        <option value="6">6岁</option>
                    </select>
                </div>
                <div class="search-item">
                    <label>关键字：</label>
                    <input id="txtkeyword2" type="text" autocomplete="off" placeholder="请输入幼儿姓名或家长姓名" class="layui-input" />
                </div>
                <div class="search-item">
                    <label>家长性别：</label>
                    <select id="parentsex" lay-filter="parentsex">
                        <option value="">请选择</option>
                        <option value="男">男</option>
                        <option value="女">女</option>
                    </select>
                </div>
                <div class="search-item">
                    <label>家长年龄：</label>
                    <select id="parentage" lay-filter="parentage">
                        <option value="">请选择</option>
                        <option value="20-30">20-30岁</option>
                        <option value="30-40">30-40岁</option>
                        <option value="40-50">40-50岁</option>
                        <option value="50-60">50-60岁</option>
                        <option value="60-70">60-70岁</option>
                    </select>
                </div>
                <div class="search-item">
                    <label>访问设备：</label>
                    <select id="device" lay-filter="device">
                        <option value="">请选择</option>
                        <option value="安卓">安卓</option>
                        <option value="苹果">苹果</option>
                        <option value="pc">PC</option>
                    </select>
                </div>
                <div class="search-item">
                    <label>区域分布：</label>
                    <select id="distance" lay-filter="distance">
                        <option value=""></option>
                        <option value="0">1km-3km</option>
                        <option value="1">3km-5km</option>
                        <option value="2">5km以上</option>
                    </select>
                </div>
                <div class="search-item">
                    <label>孩子与家长关系：</label>
                    <select id="relationship" lay-filter="relationship" lay-search>
                        <option value="">请选择</option>
                        <option value="爸爸">爸爸</option>
                        <option value="妈妈">妈妈</option>
                        <option value="爷爷">爷爷</option>
                        <option value="奶奶">奶奶</option>
                        <option value="外公">外公</option>
                        <option value="外婆">外婆</option>
                        <option value="其他亲属">其他亲属</option>
                        <option value="保姆">保姆</option>
                        <option value="老师">老师</option>
                        <option value="朋友">朋友</option>
                        <option value="邻居">邻居</option>
                        <option value="医生">医生</option>
                        <option value="护士">护士</option>
                        <option value="心理师">心理师</option>
                        <option value="营养师">营养师</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
                <div class="search-item" style="width:120px;">
                    <button class="btn-search" id="btnsearch">查询</button>
                </div>
            </div>
        </form>
    </div>
    
    <div class="table-container">
        <table id="laytable" lay-filter="laytable"></table>
    </div>
</div>

<script data-main="../../js/operation/w_healthEducationParent" src='../../sys/require.min.js'></script>
</body>
</html>
