<!DOCTYPE html>
<html>
	<head lang="en">
	    <meta charset="utf-8">
	    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
	    <meta name="apple-mobile-web-app-capable" content="yes">
	    <meta name="apple-mobile-web-app-status-bar-style" content="black">
	    <title>通知详情</title>
        <link rel="stylesheet" href="../css/reset.css"/>
        <link rel="stylesheet" href="../css/style.css"/>
        <link rel="stylesheet" href="../layui-btkj/css/layui.css">
        <script type="text/javascript" src="../layui-btkj/layui.js"></script>
        <script type="text/javascript" src="../sys/jquery.js"></script>
        <script type="text/javascript" src="../sys/system.js"></script>
	    <script type="text/javascript">
	        new function() {
	            var _self = this;
	            _self.width = 640; //设置默认最大宽度
	            _self.fontSize = 100; //默认字体大小
	            _self.widthProportion = function() {
	                var p = (document.body && document.body.clientWidth || document.getElementsByTagName("html")[0].offsetWidth) / _self.width;
	                return p > 1 ? 1 : p < 0.5 ? 0.5 : p;
	            };
	            _self.changePage = function() {
	                document.getElementsByTagName("html")[0].setAttribute("style", "font-size:" + _self.widthProportion() * _self.fontSize + "px !important");
	            }
	            _self.changePage();
	            window.addEventListener('resize', function() {
	                _self.changePage();
	            }, false);
	        };
	    </script>
	    <style>
            /*查看通知详情*/
            body{-webkit-tap-highlight-color:rgba(0,0,0,0);font-size:16px;/* font-family:"microsoft yahei";*/ background: #fff; color:#333333; font-family:"冬青黑体简体中文 W3";}
            .notice-detail{padding-bottom: 0.5rem;}
            .notice-detail .noti-tit-list>div{margin-right: 0.2rem;margin-left: 0.7rem;}
            .notice-detail .notidetail-tit h3{text-align: center;}
            .notice-detail .notidetail-tit .noti-tit-list div p{display:inline-block;font-size: 0.24rem;color: #999;margin: 0 0 10px 0;}
            .notice-detail .notidetail-tit .noti-txt-con{font-size: 0.28rem;padding:0 0.3rem;line-height: 0.44rem;margin-top: 10px;}
            .notice-detail .notidetail-tit .down-txt dl dt{float: left;}
            .notice-detail .notidetail-tit .down-txt dl dd{font-size: 0.24rem;margin-left: 0.7rem;}
            .notice-detail .notidetail-tit .down-txt dl dd p{margin: 0;}
            .notice-detail .notidetail-tit .down-txt dl dd a{cursor: pointer;}
            .notice-detail .notidetail-tit .down-txt dl{clear: both;margin: 0.1rem 0.5rem;}
            .notice-detail .end-txt{margin:10px 10px;font-size: 14px;}
            .layui-layer-btn a.layui-layer-btns{background-color: deepskyblue}
	    </style>
	</head>
	<body>
		<section>
            <section class="notice-detail" id="container">
                <div class="notidetail-tit">

                    <div class="noti-txt-con">

                    </div>
                    <div class="down-txt" style="display: none;">

                    </div>
                </div>
                <hr style="height:1px;border:none;border-top:1px dashed lightgray;margin: 0 10px;opacity: 0.5;">
                <p class="end-txt"></p>
                <div id="qr-enroll-btn" class="layui-layer-btn" style="display: none; text-align: center; margin-top: 50px;font-size: 14px">
                    <div class="qr-type0" style="display: none;">
                        <a class="qr-enroll" style="background: #1CA89D; color: #fff">我要报名</a>
                        <a class="qr-no-enroll" style="background:#F1F1F1; color: #3B3B3B;">不报名</a>
                    </div>
                    <div class="qr-type1" style="display: none;">
                        <a class="qr-my-enroll" style="color: #2EA89D; background: #F1F1F1;">我的报名历史</a>
                        <a class="qr-cancel-enroll" style="background: #F1F1F1; color: #FF3939;">取消报名</a>
                    </div>
                    <div class="qr-type2" style="display: none;">
                        <a class="qr-not-enroll" style="border-color: rgb(155, 155, 155);background-color: rgb(155, 155, 155);">取消不报名(我已不报名)</a>
                    </div>
                    <div class="qr-type3" style="display: none;">
                        <a class="qr-end-enroll" style="border-color: rgb(155, 155, 155);background-color: rgb(155, 155, 155); color: #ffffff;">已截止报名(我已报名)</a>
                        <a class="qr-my-enroll" style="color: #2EA89D; background: #F1F1F1;">我的报名历史</a>
                    </div>
                    <div class="qr-type4" style="display: none;">
                        <a class="qr-end-enroll" style="border-color: rgb(155, 155, 155);background-color: rgb(155, 155, 155); color: #ffffff;">已截止报名(未报名)</a>
                    </div>
                </div>
            </section>
            <section id="qr-enroll-info" style="display: none;">
                <form class="layui-form" style="margin-right: 10px">
                    <div class="layui-form-item">
                        <label class="layui-form-label"><i class="layui-mark-red">*</i>联系人：</label>
                        <div class="layui-input-block">
                            <input id="txtname" type="text" name="title" required="" lay-verify="required" placeholder="请输入联系人姓名" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><i class="layui-mark-red">*</i>手机号：</label>
                        <div class="layui-input-block">
                            <input id="txtmobile" type="text" name="title" required="" lay-verify="required" placeholder="请输入联系人手机号码" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="text-align:left; width:90px;padding:9px "><i class="layui-mark-red" >*</i>报名人数：</label>
                        <div class="layui-input-block">
                            <input id="txtnum" type="text" name="title" required="" lay-verify="required" value="1" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">备注：</label>
                        <div class="layui-input-block">
                            <textarea id="txtbeizhu" name="desc" placeholder="" class="layui-textarea"></textarea>
                        </div>
                    </div>
                </form>
            </section>
        </section>
		<script type="text/javascript" src="../sys/arg.js"></script>
		<script type="text/javascript" src="../plugin/promise/promise.js"></script>
        <script type="text/javascript" src="../plugin/js/moment.js"></script>
        <script type="text/javascript" src="../plugin/js/jquery.cookie.js"></script>
		<script type="text/javascript">
            var script = document.createElement("script");
            script.src = "../js/comnotidetail.js?v="+ (Arg("homeversion") ? Arg("homeversion") : Arg("v"));
            document.body.appendChild(script);
        </script>
	</body>
</html>
