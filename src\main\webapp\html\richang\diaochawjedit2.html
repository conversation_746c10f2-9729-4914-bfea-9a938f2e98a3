<!DOCTYPE html>
<html>
<head>
    <title>通知公告</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <script type="text/javascript" src="../../layui-btkj/layui.js"></script>
    <script type="text/javascript" src="../../sys/jquery.js"></script>
    <script type="text/javascript" src="../../sys/system.js"></script>
    <script src="../../plugin/wdatepicker/wdatepicker.js" type="text/javascript"></script>
    <link rel="stylesheet" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../css/style.css"/>
   <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
	<link rel="stylesheet" href="../../styles/xbcomstyle.css">
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>

    <!--[if lt IE 9]>
    <script src='../../sys/html5shiv.min.js'></script>
    <![endif]-->
    <style>
		body {
            background: #F4F5F7;
        }
		.layui-btn-primary{background-color:#EDEFF2; }
        #js-cover input,#js-attache input{
            opacity: 0;
            filter: alpha(opacity=0);
            cursor: pointer;
            display: none;
        }
        #js-saved .js-read-info label{
            display: none;
        }
        #js-sent .js-read-info button, #js-receive .js-read-info button{
            display: none;
        }
        #js-receive .qr-delay-enroll-time, #js-saved .qr-delay-enroll-time{
            display: none;
        }
        #js-sent .noti-content h3, #js-saved .noti-content h3{
            padding: 0 15px;
        }
	   .layui-form-label {padding-bottom: 0;width: 110px;line-height: 50px; padding-right: 10px;}
	   .layui-form-item{margin-bottom: 0;}
	   .layui-input-block{margin-left:120px;}
	   .layui-input-inline{margin-left: 10px;margin-top: 15px;}
	   .layui-textarea{width: 95%;}
    </style>
</head>
<body>
<!--新建通知公告-->
<section>
    <div id="new-notice" class="layui-row" style="margin: 0 20px;">
        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12  bg6">
            <div class="layui-form-item" style="margin: 10px 0;">
                <div class="layui-input-block" style="margin-left: 0;">
                    <button class="layui-btn bluebtn">导入模板</button>
                </div>
            </div>
            <div class="layui-row grid-demo">
                <div class="layui-col-xs10 layui-col-sm10" style="width: 100%;">
                    <div class="site-text site-block">
                        <div class="layui-form" action="">
                            <div class="layui-form-item">
                                <label class="layui-form-label"><i class="layui-mark-red">*</i>问卷标题:</label>
                                <div class="layui-input-block" style="padding-top: 10px;">
                                    <input type="text" name="title" required="" lay-verify="required" placeholder="请输入问卷标题" style="width: 95%;" class="layui-input js-title"/>
                                </div>
                            </div>
                            <div class="layui-form-item layui-form-text">
                                <label class="layui-form-label">问卷说明:</label>
                                <div class="layui-input-block" style="padding-top: 15px;">
                                    <textarea name="desc" placeholder="请填写问卷说明标题（最多500字，可不填）" class="layui-textarea js-summary" style="padding-top: 10px;"></textarea>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label" style="padding-left: 0;"><i class="layui-mark-red">*</i>是否匿名答卷:</label>
                                <div class="layui-input-inline qr-enroll">
                                    <input name="qr-enroll" type="radio" value="匿名" title="匿名">
                                    <input name="qr-enroll" type="radio" checked="checked" value="不匿名" title="不匿名">
                                </div>
                            </div>
                        </div>
                        <div id="arrquestion">
                            <div class="question">
                                <div class="layui-form-item layui-form-text">
                                    <label class="layui-form-label">1.</label>
                                    <div class="layui-input-block" style="padding-top: 15px;">
                                        <input type="text" name="title" required="" lay-verify="required" placeholder="请输入问卷标题" style="width: 95%;" class="layui-input js-title"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
		   <div class="layui-form-item" style="margin: 10px 0;">
				<div class="layui-input-block" style="margin-left: 0;">
					<button class="layui-btn bluebtn">添加题目</button>
				</div>
		   </div>
        </div>
    </div>
</section>

<!--选择通知公告类型-->
<section id="notice-type-list" style="margin: 10px;display: none;">
    <div style="text-align: left;">
        <button class="layui-btn layui-btn-small js-add-type">新增类型</button>
    </div>
    <table class="layui-table">
        <colgroup>
            <col width="150">
            <col width="200">
            <col>
        </colgroup>
        <thead>
        <tr>
            <th style="width: 17.5%;text-align: center;">选择</th>
            <th style="width: 47.5%;text-align: center;">类型名称</th>
            <th style="width: 35%;text-align: center;">操作</th>
        </tr>
        </thead>
        <tbody class="js-body">

        </tbody>
    </table>
</section>

<script type="text/javascript" src="../../sys/jquery.js"></script>
<script type="text/javascript" src="../../layui-btkj/layui.js"></script>
<script type="text/javascript" src="../../plugin/ueditor/third-party/webuploader/webuploader.min.js"></script>
<script type="text/javascript" charset="utf-8" src="../../plugin/ueditor/ueditor.config.js"></script>
<script type="text/javascript" charset="utf-8" src="../../plugin/ueditor/ueditor.all.min.js"> </script>
<script type="text/javascript" charset="utf-8" src="../../plugin/ueditor/lang/zh-cn/zh-cn.js"> </script>
<script type="text/javascript" src="../../sys/arg.js"></script>
<script type="text/javascript" src="../../js/richang/diaochawjedit.js"></script>
</body>
</html>
