var ossEqParam_50 = '?x-oss-process=image/resize,m_fill,w_50,h_50';
var ossEqParam_100 = '?x-oss-process=image/resize,m_fill,w_100,h_100';
var ossEqParam = '?x-oss-process=image/resize,m_fill,w_300,h_300';
var socketaddress = "http://127.0.0.1:3334";//"http://*************:3333";//**********
var objdata = {
    zhiXiaShi: ["110000", "120000", "500000", "310000"],
    cityMap: {
        "110100": "北京市",
        "120100": "天津市",
        "130100": "石家庄市",
        "130200": "唐山市",
        "130300": "秦皇岛市",
        "130400": "邯郸市",
        "130500": "邢台市",
        "130600": "保定市",
        "130700": "张家口市",
        "130800": "承德市",
        "130900": "沧州市",
        "131000": "廊坊市",
        "131100": "衡水市",
        "140100": "太原市",
        "140200": "大同市",
        "140300": "阳泉市",
        "140400": "长治市",
        "140500": "晋城市",
        "140600": "朔州市",
        "140700": "晋中市",
        "140800": "运城市",
        "140900": "忻州市",
        "141000": "临汾市",
        "141100": "吕梁市",
        "150100": "呼和浩特市",
        "150200": "包头市",
        "150300": "乌海市",
        "150400": "赤峰市",
        "150500": "通辽市",
        "150600": "鄂尔多斯市",
        "150700": "呼伦贝尔市",
        "150800": "巴彦淖尔市",
        "150900": "乌兰察布市",
        "152200": "兴安盟",
        "152500": "锡林郭勒盟",
        "152900": "阿拉善盟",
        "210100": "沈阳市",
        "210200": "大连市",
        "210300": "鞍山市",
        "210400": "抚顺市",
        "210500": "本溪市",
        "210600": "丹东市",
        "210700": "锦州市",
        "210800": "营口市",
        "210900": "阜新市",
        "211000": "辽阳市",
        "211100": "盘锦市",
        "211200": "铁岭市",
        "211300": "朝阳市",
        "211400": "葫芦岛市",
        "220100": "长春市",
        "220200": "吉林市",
        "220300": "四平市",
        "220400": "辽源市",
        "220500": "通化市",
        "220600": "白山市",
        "220700": "松原市",
        "220800": "白城市",
        "222400": "延边朝鲜族自治州",
        "230100": "哈尔滨市",
        "230200": "齐齐哈尔市",
        "230300": "鸡西市",
        "230400": "鹤岗市",
        "230500": "双鸭山市",
        "230600": "大庆市",
        "230700": "伊春市",
        "230800": "佳木斯市",
        "230900": "七台河市",
        "231000": "牡丹江市",
        "231100": "黑河市",
        "231200": "绥化市",
        "232700": "大兴安岭地区",
        "310100": "上海市",
        "310200": "崇明县",
        "320100": "南京市",
        "320200": "无锡市",
        "320300": "徐州市",
        "320400": "常州市",
        "320500": "苏州市",
        "320600": "南通市",
        "320700": "连云港市",
        "320800": "淮安市",
        "320900": "盐城市",
        "321000": "扬州市",
        "321100": "镇江市",
        "321200": "泰州市",
        "321300": "宿迁市",
        "330100": "杭州市",
        "330200": "宁波市",
        "330300": "温州市",
        "330400": "嘉兴市",
        "330500": "湖州市",
        "330600": "绍兴市",
        "330700": "金华市",
        "330800": "衢州市",
        "330900": "舟山市",
        "331000": "台州市",
        "331100": "丽水市",
        "340100": "合肥市",
        "340200": "芜湖市",
        "340300": "蚌埠市",
        "340400": "淮南市",
        "340500": "马鞍山市",
        "340600": "淮北市",
        "340700": "铜陵市",
        "340800": "安庆市",
        "341000": "黄山市",
        "341100": "滁州市",
        "341200": "阜阳市",
        "341300": "宿州市",
        "341500": "六安市",
        "341600": "亳州市",
        "341700": "池州市",
        "341800": "宣城市",
        "350100": "福州市",
        "350200": "厦门市",
        "350300": "莆田市",
        "350400": "三明市",
        "350500": "泉州市",
        "350600": "漳州市",
        "350700": "南平市",
        "350800": "龙岩市",
        "350900": "宁德市",
        "360100": "南昌市",
        "360200": "景德镇市",
        "360300": "萍乡市",
        "360400": "九江市",
        "360500": "新余市",
        "360600": "鹰潭市",
        "360700": "赣州市",
        "360800": "吉安市",
        "360900": "宜春市",
        "361000": "抚州市",
        "361100": "上饶市",
        "370100": "济南市",
        "370200": "青岛市",
        "370300": "淄博市",
        "370400": "枣庄市",
        "370500": "东营市",
        "370600": "烟台市",
        "370700": "潍坊市",
        "370800": "济宁市",
        "370900": "泰安市",
        "371000": "威海市",
        "371100": "日照市",
        "371200": "莱芜市",
        "371300": "临沂市",
        "371400": "德州市",
        "371500": "聊城市",
        "371600": "滨州市",
        "371700": "菏泽市",
        "410100": "郑州市",
        "410200": "开封市",
        "410300": "洛阳市",
        "410400": "平顶山市",
        "410500": "安阳市",
        "410600": "鹤壁市",
        "410700": "新乡市",
        "410800": "焦作市",
        "410900": "濮阳市",
        "411000": "许昌市",
        "411100": "漯河市",
        "411200": "三门峡市",
        "411300": "南阳市",
        "411400": "商丘市",
        "411500": "信阳市",
        "411600": "周口市",
        "411700": "驻马店市",
        "420100": "武汉市",
        "420200": "黄石市",
        "420300": "十堰市",
        "420500": "宜昌市",
        "420600": "襄阳市",
        "420700": "鄂州市",
        "420800": "荆门市",
        "420900": "孝感市",
        "421000": "荆州市",
        "421100": "黄冈市",
        "421200": "咸宁市",
        "421300": "随州市",
        "422800": "恩施土家族苗族自治州",
        "429000": "湖北省直辖县市",
        "430100": "长沙市",
        "430200": "株洲市",
        "430300": "湘潭市",
        "430400": "衡阳市",
        "430500": "邵阳市",
        "430600": "岳阳市",
        "430700": "常德市",
        "430800": "张家界市",
        "430900": "益阳市",
        "431000": "郴州市",
        "431100": "永州市",
        "431200": "怀化市",
        "431300": "娄底市",
        "433100": "湘西土家族苗族自治州",
        "440100": "广州市",
        "440200": "韶关市",
        "440300": "深圳市",
        "440400": "珠海市",
        "440500": "汕头市",
        "440600": "佛山市",
        "440700": "江门市",
        "440800": "湛江市",
        "440900": "茂名市",
        "441200": "肇庆市",
        "441300": "惠州市",
        "441400": "梅州市",
        "441500": "汕尾市",
        "441600": "河源市",
        "441700": "阳江市",
        "441800": "清远市",
        "441900": "东莞市",
        "442000": "中山市",
        "445100": "潮州市",
        "445200": "揭阳市",
        "445300": "云浮市",
        "450100": "南宁市",
        "450200": "柳州市",
        "450300": "桂林市",
        "450400": "梧州市",
        "450500": "北海市",
        "450600": "防城港市",
        "450700": "钦州市",
        "450800": "贵港市",
        "450900": "玉林市",
        "451000": "百色市",
        "451100": "贺州市",
        "451200": "河池市",
        "451300": "来宾市",
        "451400": "崇左市",
        "460100": "海口市",
        "460200": "三亚市",
        "460300": "三沙市",
        "469000": "省直辖县级行政区划",
        "500100": "重庆市",
        "510100": "成都市",
        "510300": "自贡市",
        "510400": "攀枝花市",
        "510500": "泸州市",
        "510600": "德阳市",
        "510700": "绵阳市",
        "510800": "广元市",
        "510900": "遂宁市",
        "511000": "内江市",
        "511100": "乐山市",
        "511300": "南充市",
        "511400": "眉山市",
        "511500": "宜宾市",
        "511600": "广安市",
        "511700": "达州市",
        "511800": "雅安市",
        "511900": "巴中市",
        "512000": "资阳市",
        "513200": "阿坝藏族羌族自治州",
        "513300": "甘孜藏族自治州",
        "513400": "凉山彝族自治州",
        "520100": "贵阳市",
        "520200": "六盘水市",
        "520300": "遵义市",
        "520400": "安顺市",
        "522200": "铜仁市",
        "522300": "黔西南布依族苗族自治州",
        "522400": "毕节市",
        "522600": "黔东南苗族侗族自治州",
        "522700": "黔南布依族苗族自治州",
        "530100": "昆明市",
        "530300": "曲靖市",
        "530400": "玉溪市",
        "530500": "保山市",
        "530600": "昭通市",
        "530700": "丽江市",
        "530800": "普洱市",
        "530900": "临沧市",
        "532300": "楚雄彝族自治州",
        "532500": "红河哈尼族彝族自治州",
        "532600": "文山壮族苗族自治州",
        "532800": "西双版纳傣族自治州",
        "532900": "大理白族自治州",
        "533100": "德宏傣族景颇族自治州",
        "533300": "怒江傈僳族自治州",
        "533400": "迪庆藏族自治州",
        "540100": "拉萨市",
        "542100": "昌都地区",
        "542200": "山南地区",
        "542300": "日喀则地区",
        "542400": "那曲地区",
        "542500": "阿里地区",
        "542600": "林芝地区",
        "610100": "西安市",
        "610200": "铜川市",
        "610300": "宝鸡市",
        "610400": "咸阳市",
        "610500": "渭南市",
        "610600": "延安市",
        "610700": "汉中市",
        "610800": "榆林市",
        "610900": "安康市",
        "611000": "商洛市",
        "620100": "兰州市",
        "620200": "嘉峪关市",
        "620300": "金昌市",
        "620400": "白银市",
        "620500": "天水市",
        "620600": "武威市",
        "620700": "张掖市",
        "620800": "平凉市",
        "620900": "酒泉市",
        "621000": "庆阳市",
        "621100": "定西市",
        "621200": "陇南市",
        "622900": "临夏回族自治州",
        "623000": "甘南藏族自治州",
        "630100": "西宁市",
        "632100": "海东地区",
        "632200": "海北藏族自治州",
        "632300": "黄南藏族自治州",
        "632500": "海南藏族自治州",
        "632600": "果洛藏族自治州",
        "632700": "玉树藏族自治州",
        "632800": "海西蒙古族藏族自治州",
        "640100": "银川市",
        "640200": "石嘴山市",
        "640300": "吴忠市",
        "640400": "固原市",
        "640500": "中卫市",
        "650100": "乌鲁木齐市",
        "650200": "克拉玛依市",
        "652100": "吐鲁番地区",
        "652200": "哈密地区",
        "652300": "昌吉回族自治州",
        "652700": "博尔塔拉蒙古自治州",
        "652800": "巴音郭楞蒙古自治州",
        "652900": "阿克苏地区",
        "653000": "克孜勒苏柯尔克孜自治州",
        "653100": "喀什地区",
        "653200": "和田地区",
        "654000": "伊犁哈萨克自治州",
        "654200": "塔城地区",
        "654300": "阿勒泰地区",
        "659000": "自治区直辖县级行政区划",
        "710000": "台湾省",
        "810100": "香港特别行政区",
        "820000": "澳门特别行政区"
    },
    moment: moment,
    arrhistroy: [],
    lan: "zh",
    wins: {}, // id title src img
    users: null, // [id,truename,headimg,sp,spe]
    babys: {},
    yeyinfo: {},//[类型,guid,yeyname,address,groupname,fname]
    dmcinfo: { //dmc关联后areacode areaname
        areacode: "",
        areaname: ""
    },
    feeinfo: {}, //收费及认证
    arrwin: [],
    menuids: [],
    fmids: [],//常用栏目
    expmids: {},//体验栏目设置
    menuenobjs: {
        main: ['main', 'workspace']
    },
    dics: {},
    zindex: 1,
    size: {
        title: 18
    },
    yid: "3d03a1c1195049ed9c2ba8a4c3544cb9",// 幼儿园id
    gid: '1',// 集团id
    my: {
        id: 2,
        name: "",
        img: 'http://tp1.sinaimg.cn/1827469584/180/40006354283/0'
    },
    upconfig: {
        upfile_endpoint: ossPrefix,//上传地址
        upfile_nametype: 'random_name',//local_name random_name  上传文件的文件名类型
        upfile_defaltdir: 'fuyou/im'  //上传路径 多层  格式  upload/floder1/floder2
    },
    ossPrefix: ossPrefix + "/",
    arrmy: [],// 用户信息
    defindex: 3,// 面板默认打开
    onlineids: null,// 即时通在线id
    onlinepids: null,// 即时通在线的surfaceid
    hasload: [0, 0, 0, 0, 0],
    arrtime: [], // 时间
    keytype: 0,// 0 回车发送 1 Alt+回车 发送
    keyflag: 0,// 按下alt 标记
    termyear: 0,// 学年
    prenum: 20,//每次读取聊天记录条数
    imguploader: null,
    /**
     * 上传多文件 上传队列 toupque { 1:{ ids:[],//待上传ids has:3,// 已经缩略的个数
     * html:[],//预览的html strs:[]//保存到数据库的串 } }
     */
    toupque: {},
    toupfile: {},
    arrroletype: [],//角色类型
    layers: {},//记录弹出框id
    chatarrs: [],
    objMenuName: {},//栏目名称和id对应关系
    objmygn: {},
    dpi: 96,
    holidays: {
        //假期变量
    }, tbptdays: {}//平台总的放假设置
    , ishxlogin: false //慧享对接登录
    , utype: "" // 对接utype
    , arrGouptTime: []//当前日期前的升班日期和当前日期后的升班日期（如果当前日期后有升班）。
    , isEDU: (location.hostname.indexOf('edu-n') > -1) ? true : false //教育版判断
    //    chdc.tb-n.org
    , isChdc: (location.hostname == "www.tb-n.com") ? true : false
    , noread: 0
    , isfirst: false//是否首次登陆
    , sysInfo: {} //登录客户端系统信息
    , sysToken: ''//登录token
    , loginPage: (location.origin.indexOf("test") > -1 || location.origin.indexOf("localhost") > -1 ? "login.html" : location.origin)//登录跳转页面名称
    , isnchangeyey: 0//标记是否切换幼儿园 1是
    , fyinfo: {}//妇幼信息
    , objParName: {
        '01': "血液检查",
        '02': "功能检查",
        '03': "专项检查"
    }
    , objSex: {
        "1": "男性"
        , "2": "女性"
        , "3": "男性、女性"
    }
    , objIntype: {
        "1": "拍照",
        "2": "输入数值",
        "3": "下拉选择",
        "4": "文字输入",
        "5": "选择日期",
        "6": "过敏原分级"
    }
    , objusers: {}
    , arrpost: [["园长", "4"], ["副园长", "13"], ["保健医", "3"], ["老师", "2"], ["保教主任", "5"], ["保育员", "12"], ["年级组长", "17"], ["教研组长", "18"], ["后勤主任", "6"], ["炊事员", "7"], ["保洁", "8"], ["保安", "9"], ["财务", "10"], ["跟车老师", "14"], ["办公室人员", "15"], ["办事员", "16"], ["其他", "11"]]//教职工职务
    , pageids: {
        yeyadd: 83//基础档案
        , sbedit: 87//升班管理
    }
    , noticeident: "fuyou"//发通知身份标识
    , arryeytype: [["幼儿园", "1"], ["托育机构", "3"], ["托幼一体园", "6"]]
    , objyeytype: {
        "1": "幼儿园",
        "3": "托育机构",
        "6": "托幼一体园"
    }
    , objroletype: {
        "s": "区县系统角色",
        "e": "区县妇幼保健院",
        "ehos": "基层卫生服务机构",
        "ehosin": "入托体检机构",
        "ehosother": "其他体检机构",
        "eyey": "幼儿园",
        "echild": "托育机构",
        "echyey": "托幼一体园",
        "m": "体检中心",
        "mcom": "企业用户",
        "mhos": "体检机构"
    }
    , syssort: 1//幼儿排序方式
    , strorder: "sex desc, birthday"//默认排序方式
    , overweightobesity: 1//超重肥胖的评价方法
    , errCount:0
    , isnmock: 1//是否使用模拟数据
};
/**
 * 替换模板数据 模板与数据中的变量名完全一致
 * @param template {String} 模板字符串
 * @param model {Object} 模板数据
 * @returns {*} {strHtml} 数据替换后的模板字符串
 */
var renderTemp = function (template, model) {
    var reg = /\{\{\w+\}\}/g;
    template = template.replace(reg, function (regStr) {
        var reg2 = /\{\{(\w+)\}\}/g,
            key = reg2.exec(regStr)[1];
        return model[key];
    });
    return template;
};
var objtheme = {
    gray: "#363f46", blue: "#2c99de", green: "#05a9a0", orange: "#ff761b", purple: "#6777d7", red: "#de575d", lightGreen: "#6eb92b"
};
var Logid = '';
var myrv = {};
var form, laydate;
layui.use(["element", 'layer', "form", "laydate"], function () {
    window.$ = window.jQuery = layui.jquery;
    var layer = layui.layer,
        element = layui.element,
        device = layui.device();
    form = layui.form;
    laydate = layui.laydate;
    try {
        //校对访问版本
        if (version && Arg("v")) {
            if (Arg("v") < version) {
                $("#loading").hide().remove();
                return layer.alert("您访问的网址不是最新版，请点击刷新后再使用！", {btn: ["刷新"]}, function () {
                    //logOutEvent();
                    window.location.href = location.href.split("?")[0] + "?v=" + version;
                });
            } else {
                version = Arg("v");
            }
        }
    } catch (e) {
        console.log(e);
    }
    //读取登录记录
    $.sm(function (re1, err1) {
        Logid = re1;
    }, ["init.getlogid"], null, null, {async: false});
    //     larry = {},
    //     form = layui.form,
    //     side = layui.side;
    var alertOnlineStatus = function () {
        objdata.onLine = navigator.onLine;
        // window.alert(navigator.onLine ? 'online' : 'offline');
    };
    window.addEventListener('online', alertOnlineStatus);
    window.addEventListener('offline', alertOnlineStatus);
    alertOnlineStatus();
    systemTitle();
    changeObjPost();
    var layer = layui.layer;
    /*layer.config({
       zIndex:100
    });*/
    layer.config({
        btnAlign: 'c'
    });
    objdata.sysInfo = layui.systeminfo;
    //获取token
    // $.sm(function (re, err, obj) {
    //     objdata.sysToken = obj.re2;
    // }, ['login.encrypt', (new Date()).valueOf()]);
    var _this = null;
    setTimeout(function () {
        //desktop.fastdaohang(null, 28)
        //desktop.openmyinfo();
        //desktop.setting();
    }, 1000)
    /*$(window).bind('beforeunload', function () {
        return '';
    });*/
    $(window).unload(function () {
        //关闭页面清除session，处理登出时间
        if (Logid) {
            $.sm(function () {
            }, ["w_logintime.logout"]);
        }
        // $.sm(function () {
        // }, ["index.udtLogoutTime"]);
    });
    window.isInZhixiashi = function (areacode) {
        var temp = areacode.substring(0, 2);
        var arrZXS = objdata.zhiXiaShi;
        for (var i = 0; i < arrZXS.length; i++) {
            if (temp == arrZXS[i].substring(0, 2)) {
                return true;
            }
        }
        return false;
    }
    /**
     * 获取区域信息
     * @return {*}
     */
    window.getAreaInfo = function (areacode) {
        areacode = areacode || objdata.my.areacode;
        var obj = {
            areacode: areacode,
            citycode: areacode,//所在城市编码
            mapcode: areacode,//地图code
            acode: areacode,//如果是市  返回前4位
            isAll: false//是不是查询数据库所有数据
        };
        if (areacode) {
            //zhiXiaShi:['110000','120000','500000','310000'],
            if (areacode.length == 6) {
                if (isInZhixiashi(areacode)) {//直辖市只能看市
                    obj.isAll = true;
                    //直辖市到市
                    obj.citycode = areacode;
                    obj.mapcode = areacode.substring(0, 2) + "0000";
                } else if (areacode.substring(4, 6) == "00") {//市
                    obj.isAll = true;
                    obj.acode = areacode.substring(0, 4);
                    obj.mapcode = areacode.substring(0, 4) + "00";
                    obj.citycode = obj.mapcode;
                } else {//区
                    obj.mapcode = areacode.substring(0, 4) + "00";
                    obj.citycode = obj.mapcode;
                }
            } else {
                if (isInZhixiashi(areacode)) {
                    //直辖市到市
                    obj.mapcode = areacode.substring(0, 2) + "0000";
                    obj.citycode = areacode.substring(0, 6);
                } else {
                    obj.mapcode = areacode.substring(0, 4) + "00";
                    obj.citycode = obj.mapcode;
                }
            }
            return obj;
        }
        return null;
    }
    $(document).ready(function () {
        // 浏览器兼容检查
        if (device.ie && device.ie < 9) {
            layer.alert("最低支持ie9，您当前使用的是古老的 IE" + device.ie + "！");
        }
        var userAgent = navigator.userAgent.toLowerCase(), uaMatch;
        window.browser = {}
        layui.browser = window.browser;
        /**
         * 判断是否为谷歌浏览器
         */
        if (!uaMatch) {
            uaMatch = userAgent.match(/chrome\/([\d.]+)/);
            if (uaMatch != null) {
                window.browser['name'] = 'chrome';
                window.browser['version'] = uaMatch[1];
            }
        }
        /**
         * 判断是否为火狐浏览器
         */
        if (!uaMatch) {
            uaMatch = userAgent.match(/firefox\/([\d.]+)/);
            if (uaMatch != null) {
                window.browser['name'] = 'firefox';
                window.browser['version'] = uaMatch[1];
            }
        }
        /**
         * 判断是否为opera浏览器
         */
        if (!uaMatch) {
            uaMatch = userAgent.match(/opera.([\d.]+)/);
            if (uaMatch != null) {
                window.browser['name'] = 'opera';
                window.browser['version'] = uaMatch[1];
            }
        }
        /**
         * 判断是否为Safari浏览器
         */
        if (!uaMatch) {
            uaMatch = userAgent.match(/safari\/([\d.]+)/);
            if (uaMatch != null) {
                window.browser['name'] = 'safari';
                window.browser['version'] = uaMatch[1];
            }
        }
        // // 001界面初始化
        // AdminInit();
    });
    window.desktop = {
        zaixiankefu: function () {
            //在线客服
            var objcustomerFields = {"客户名称": objdata.fyinfo[3], "姓名": objdata.my.username};
            var strcustomerFields = encodeURI(JSON.stringify(objcustomerFields));
            layer.open({
                type: 2,
                title: '在线客服',
                zIndex: 99999999,
                shade: 0,
                shadeClose: false,
                maxmin: true,
                area: ['780px', '600px'],
                content: 'https://webchat-sh.clink.cn/chat.html?accessId=a1da8eed-8204-409c-a47a-9090f030efef&visitorId=' + objdata.fyinfo[2] + "_" + objdata.my.id + '&visitorName=' + objdata.fyinfo[3] + "-" + (objdata.objroletype[objdata.my.roletype] || "区县") + '-' + objdata.my.username + "(" + objdata.my.mobile + ")" + "&tel=" + objdata.my.mobile + "&customerFields=" + strcustomerFields,
                btn: ["关闭"],
                success: function (layero, index) {
                }
            });
        },
        onthemechange: function (type) {
            objdata.curtheme = type;
            $.cookie('curtheme', type);
            $("#linktheme").attr('href', 'css/' + type + ".css");
            $(".layerdesktopapp").each(function () {
                $(this).children('.layui-layer-title').css('background-color', objtheme[type]);
            })
        },
        refreshdesk: function () {
            location.reload()
        }, fastdaohang: function (_obj, mid) {
            desktop.hidemenu();
            layer.open({
                type: 2,
                title: "快速导航",
                id: "desktopsearch",
                content: 'desktop/desktopsearch.html?v=' + version + (mid ? "&startmid=" + mid : ""),
                //shade: false,
                fixed: false, //不固定
                area: ['360px', '500px'],//
                success: function (layero, index) {
                    layer.iframeAuto(index)
                }
            });
        },
        setting: function (a) {
            desktop.hidemenu();
            layer.open({
                type: 2,
                title: ["系统设置", 'background-color:' + objtheme[objdata.curtheme] + ';text-align:center;color:#ffffff'],
                id: "desktopset",
                content: 'desktop/desktopset.html?v=' + version,
                fixed: false, //不固定
                area: ['780px', '540px'],//
                success: function (layero, index) {
                    layero.find(".layui-layer-close").addClass('layui-layer-ico2');
                    layer.iframeAuto(index)
                }
            });
        }, tool_tool: function () {
            $("#divtool").hide();
            layer.msg("功能建设中")
        }, loginout: function (a) {
            desktop.hidemenu();
            logOutEvent();
        }, technicalsupport: function (a) {
            desktop.hidemenu();
        }, closetask: function () {
            desktop.hidemenu();
            var winindex = _this.curContextmenumTask.attr("winindex");
            layer.close(winindex);
        }, closeothertask: function () {
            desktop.hidemenu();
            var winindex = _this.curContextmenumTask.attr("winindex");
            $(".desktop-taskbar-app-list").children().each(function () {
                if ($(this).attr("winindex") != winindex) {
                    layer.close($(this).attr("winindex"));
                }
            })
        }, closealltask: function (a) {
            a = $(".taskbar-app").length;
            desktop.hidemenu();
            1 > a || layer.alert("确定关闭所有窗口？", {
                icon: 0,
                btn: ["确定", "取消"],
                yes: function (a, e) {
                    $(document).find(".taskbar-app").remove();
                    layer.closeAll("iframe");
                    layer.close(a)
                }
            })
        }, showdesktop: function (a) {
            desktop.hidemenu();
            $(document).find(".layui-layer .layui-layer-min").click();
            $(document).find(".taskbar-app").removeClass("taskbar-app-on")
        }, hidemenu: function (othis) {
            $(".desktop-menu").hide()
            $(".divyeylist").hide()
            $("#divchangyongmenu").hide();
            $("#divtool").hide();
            $("#divloginhistory").hide()
            $(".desktop-app").removeClass('desktop-app-select')
        }, openloginhistory: function (othis) {
            objdata.index_loginhistory = layer.open({
                type: 2,
                title: "登录历史", //不显示标题
                btn: ['关闭'],
                content: "html/loginhistory.html?v=" + (version || 1) + "&mid=" + Arg("mid"),
                area: ['900px', '80%'],
                success: function (layero) {
                    objdata.layero_loginhistory = layero;
                },
                end: function () {
                    objdata.index_loginhistory = null;
                }
            });
        }, tongzhi: function (othis, param) {//通知
            getnoticenum(1);
        }, openmyinfo: function (othis, param) {
            desktop.openwinbymid(29, param);
        }, openyeyinfo: function () {
            //弹出用户信息
            var data = objdata.yeyinfo;//[类型,guid,yeyname,address,groupname,fname]
            var html = [];
            html.push('<section class="clearfix personalPop font14">');
            html.push('<img class="fl" src="' + (objdata.yeyinfo[28] ? objdata.yeyinfo[28] + '?x-oss-process=image/resize,m_fill,h_150,w_150' : 'images/default.png') + '" alt=""  onerror="this.src=\'images/default.png\'" />');
            // html.push('<h3 style="text-align:center;">' + data[1] + '</h3>');
            html.push('<ul>');
            if (!objdata.ishxlogin) {//hxedit
                html.push('<li>用户：<b>' + objdata.my.username + '</b></li>');
            }
            html.push('</ul>');
            html.push('<input class="layui-btn" type="button" value="跳转到医院信息" style="background: #3aa9ff;margin: 15px auto;display: inherit;height: 30px;line-height: 30px;padding: 0 30px;">');
            html.push('</section>');
            objdata.layers.userinfo = layer.open({
                type: 1,
                title: ["医院信息", 'background-color:' + objtheme[objdata.curtheme] + ';text-align:center;color:#ffffff'],
                area: ['480px', '320px'],
                shadeClose: true, // 点击遮罩关闭
                content: html.join(''),
                success: function (layero, index) {
                    layero.find('input').click(function () {
                        layer.close(index);
                        desktop.openwinbymid(58)
                    })
                }
            });
        }, hidopeningemenu: function () {
            $(".opening-menu").removeClass("opening-menu-on")
        }, handlelock: function (_dom) {//处理锁
            /*var totype = 0;
            if (_dom.hasClass('lockstate')) {//解锁
                objdata.menulock = false;
                _dom.children('img').prop('src', 'images/desktop/icon_unlock.png');
                _dom.removeClass('lockstate')
                $("#divdeskleft").css('position', 'absolute')
                //$(".funtop").hide()
                //$("#divmenulist").css("height", $(window).height() - $("#taskbar").height()  - $(".menutab").height())//
                $(".taskbar-app").each(function (c, onetaskbarapp) {
                    layer.style($(this).attr('winindex'), {
                        width: $("#divdesk").width() + 'px',
                        left: 0
                    });
                });
                $("#topbar").css('visibility', 'visible')
                $(".desktop-container").width($(window).width())
                //$(".swiper-slide").width($(window).width())
                //_this.deskswiper.updateSize()
            } else {//加锁
                objdata.menulock = true;
                _dom.children('img').prop('src', 'images/desktop/icon_lock.png');
                _dom.addClass('lockstate')
                $("#divdeskleft").css('position', 'relative')

                $(".taskbar-app").each(function (c, onetaskbarapp) {
                    layer.style($(this).attr('winindex'), {
                        width: ($(window).width() - $("#divdeskleft").width()) + 'px',
                        left: $("#divdeskleft").width() + 'px'
                    });
                });
                totype = 1;
                $("#topbar").css('visibility', 'hidden')
                $(".desktop-container").width($(window).width() - $("#divdeskleft").width())
            }
            $.sm(function () {

            }, ['desktop.saveuserlock', totype])*/
        }, initStartMenu: function () {//开始菜单逻辑
            $("#divdeskleft").mouseenter(function () {
                if ($("#divdeskleft").is(":animated")) {
                    $("#divdeskleft").stop(true);
                    $("#divdeskleft").animate({
                        'opacity': 1
                    });
                }
            }).mouseleave(function () {
                if (objdata.menulock) return;
                console.log("左侧菜单" + " " + "mouseleave");
                $("#divdeskleft").fadeOut(1000);
            })
            //开始菜单
            $("#btnstartmenu").mouseenter(function () {
                if (!objdata.menulock) {//已经锁定 解锁
                    objdata.tipindex_tingkaozuocecaidan = layer.tips('点击停靠左侧菜单', '#btnstartmenu', {
                        skin: 'layui-tipsno',
                        tips: 1,
                        time: 10000,
                        zIndex: 29891012
                    });
                } else {
                    objdata.tipindex_tingkaozuocecaidan = layer.tips('点击隐藏左侧菜单', '#btnstartmenu', {
                        skin: 'layui-tipsno',
                        tips: 1,
                        time: 10000,
                        zIndex: 29891012
                    });
                }

                $("#divchangyongmenu").hide();
                $("#divtool").hide();
                if ($("#divdeskleft").is(":animated")) {
                    $("#divdeskleft").stop(true);
                    $("#divdeskleft").animate({
                        'opacity': 1
                    });
                } else {
                    $("#divdeskleft").show();
                }
            }).mouseleave(function () {
                layer.close(objdata.tipindex_tingkaozuocecaidan);
                if (objdata.menulock) return;
                console.log("开始菜单" + " " + "mouseleave");
                $("#divdeskleft").fadeOut(1000);
            }).click(function () {
                layer.close(objdata.tipindex_tingkaozuocecaidan);
                //$(".menulock").trigger('click')
                //layer.tips('菜单已锁定', '#btnstartmenu');
                var totype = 0;
                if (objdata.menulock) {//已经锁定 解锁
                    $("#divdeskleft").css('position', 'absolute')
                    totype = 0;
                    objdata.menulock = false;
                    console.log();
                    $(".taskbar-app").each(function (c, onetaskbarapp) {
                        layer.style($(this).attr('winindex'), {
                            width: $(window).width() + 'px',
                            left: 0
                        });
                    });
                    $("#topbar").css('visibility', 'visible')
                    $(".swiper-wrapper").css('visibility', 'visible')
                    $(".desktop-container").width($(window).width())//更新siwper
                    $("#icon_lockmenu").hide()//隐藏锁定图标

                    $("#divdeskleft").hide();
                    //layer.tips('左侧菜单已解除锁定', '#btnstartmenu');
                } else {//未锁定 加锁
                    $("#divdeskleft").css('position', 'relative')
                    totype = 1;
                    objdata.menulock = true;


                    $(".taskbar-app").each(function (c, onetaskbarapp) {
                        layer.style($(this).attr('winindex'), {
                            width: ($(window).width() - $("#divdeskleft").width()) + 'px',
                            left: $("#divdeskleft").width() + 'px'
                        });
                    });
                    $("#topbar").css('visibility', 'hidden')
                    $(".swiper-wrapper").css('visibility', 'hidden')
                    $(".desktop-container").width($(window).width() - $("#divdeskleft").width())
                    $("#icon_lockmenu").show()//显示锁定图标

                    $("#divdeskleft").show();
                    layer.tips('左侧菜单已停靠，点击取消', '#btnstartmenu');
                }
                $.sm(function () {
                    _this.arrange();
                }, ['desktop.saveuserlock', totype])
            })
            //常用菜单
            $("#btnchangyong").mouseenter(function () {
                objdata.tipindex_changyongcaidan = layer.tips('常用菜单', '#btnchangyong', {
                    skin: 'layui-tipsno',
                    tips: 1,
                    time: 10000
                });
            }).mouseleave(function () {
                layer.close(objdata.tipindex_changyongcaidan)
            }).click(function () {
                $(".divyeylist").hide()
                layer.close(objdata.tipindex_changyongcaidan)
                $("#divtool").hide();
                if ($("#divchangyongmenu").css('display') != 'none') {
                    $("#divchangyongmenu").hide();
                    return;
                }
                if (!$("#divchangyongmenu").data('hasload')) {
                    $.sm(function (res) {
                        var relast = res[0];
                        if (!relast.length) return layer.msg("没有常用栏目");
                        var rechangyong = res[1];
                        var strhtmllast = '';
                        var lastid = relast[0].menuid;
                        for (var i = 0; i < relast.length; i++) {
                            var today = moment().format("YYYY-MM-DD");
                            var theday = relast[i].creatime.substring(0, 10);
                            var strday = today == theday ? '今天' : theday;
                            if (objdata.objMenu[relast[i].menuid]) {
                                strhtmllast += '<li mid="' + relast[i].menuid + '" class="small-click" data-type="openchangyongwin"><img src="' + objdata.objMenu[relast[i].menuid].icon.replace('../', '') + '">' + objdata.objMenu[relast[i].menuid].menu_name + '<span style="float: right">' + (strday + ' ' + relast[i].creatime.substring(11, 19)) + '</span></li>';
                            }
                        }
                        $("#divchangyongmenu_last").html(strhtmllast);
                        var strhtmlchangyong = '';
                        for (var i = 0; i < rechangyong.length; i++) {
                            //if (lastid == rechangyong[i].menuid) continue;
                            if (objdata.objMenu[rechangyong[i].menuid]) {
                                strhtmlchangyong += '<li mid="' + rechangyong[i].menuid + '" class="small-click" data-type="openchangyongwin"><img src="' + objdata.objMenu[rechangyong[i].menuid].icon.replace('../', '') + '">' + objdata.objMenu[rechangyong[i].menuid].menu_name + '<span style="float: right">' + rechangyong[i].count + '次</span></li>';
                            }
                        }
                        $("#divchangyongmenu_fre").html(strhtmlchangyong);
                        $("#divchangyongmenu").show().css('zIndex', $("#divdeskleft").css('zIndex') * 1 + 1);
                    }, [['index.getlastusemenu', 1], ['index.getchangyongmenu', 8]])
                } else {
                    $("#divchangyongmenu").show().css('zIndex', $("#divdeskleft").css('zIndex') * 1 + 1);
                }
            })
            $("#btntool").mouseenter(function () {
                objdata.tipindex_changyonggongju = layer.tips('常用工具', '#btntool', {
                    skin: 'layui-tipsno',
                    tips: 1
                });
            }).mouseleave(function () {
                layer.close(objdata.tipindex_changyonggongju)
            }).click(function () {
                layer.close(objdata.tipindex_changyonggongju)
                $(".divyeylist").hide()
                $("#divchangyongmenu").hide();
                if ($("#divtool").css('display') != 'none') {
                    $("#divtool").hide();
                    return;
                }
                if (!$("#divtool").data('hasload')) {
                    $("#divtool").show().css('zIndex', $("#divdeskleft").css('zIndex') * 1 + 1);
                } else {
                    $("#divtool").show().css('zIndex', $("#divdeskleft").css('zIndex') * 1 + 1);
                }
            })
            $("#taskbar_rili").mouseenter(function () {
                objdata.tipindex_taskbar_rili = layer.tips('日历', '#taskbar_rili', {
                    skin: 'layui-tipsno',
                    tips: 1
                });
            }).mouseleave(function () {
                layer.close(objdata.tipindex_taskbar_rili)
            }).click(function () {
                layer.close(objdata.tipindex_taskbar_rili)
            })

            // $("#taskbar_sjfx").mouseenter(function () {
            //     objdata.tipindex_taskbar_sjfx = layer.tips('幼儿健康智慧大数据分析', '#taskbar_sjfx', {
            //         skin: 'layui-tipsno',
            //         tips: 1
            //     });
            // }).mouseleave(function () {
            //     layer.close(objdata.tipindex_taskbar_sjfx)
            // }).click(function () {
            //     layer.close(objdata.tipindex_taskbar_sjfx)
            // });
            // $("#taskbar_bighanddata").mouseenter(function () {
            //     objdata.tipindex_taskbar_bighanddata = layer.tips('幼儿园健康监测', '#taskbar_bighanddata', {
            //         skin: 'layui-tipsno',
            //         tips: 1
            //     });
            // }).mouseleave(function () {
            //     layer.close(objdata.tipindex_taskbar_bighanddata)
            // }).click(function () {
            //     layer.close(objdata.tipindex_taskbar_bighanddata)
            // });
            // $("#taskbar_bigfunction").mouseenter(function () {
            //     objdata.tipindex_taskbar_bigfunction = layer.tips('功能视图', '#taskbar_bigfunction', {
            //         skin: 'layui-tipsno',
            //         tips: 1
            //     });
            // }).mouseleave(function () {
            //     layer.close(objdata.tipindex_taskbar_bigfunction)
            // }).click(function () {
            //     layer.close(objdata.tipindex_taskbar_bigfunction)
            // });
        }, openchangyongwin: function (_dom) {
            $("#divchangyongmenu").hide();
            _this.openwin(_dom)
        }, hide: function () {
            layer.closeAll("tips")
        }, pattern: function (a) {
            var b = new Date, e = {"M+": b.getMonth() + 1, "d+": b.getDate(), "h+": 0 == b.getHours() % 12 ? 12 : b.getHours() % 12, "H+": b.getHours(), "m+": b.getMinutes(), "s+": b.getSeconds(), "q+": Math.floor((b.getMonth() + 3) / 3), S: b.getMilliseconds()}, c = {
                0: "日", 1: "一",
                2: "二", 3: "三", 4: "四", 5: "五", 6: "六"
            };
            /(y+)/.test(a) && (a = a.replace(RegExp.$1, (b.getFullYear() + "").substr(4 - RegExp.$1.length)));
            /(E+)/.test(a) && (a = a.replace(RegExp.$1, (1 < RegExp.$1.length ? 2 < RegExp.$1.length ? "星期" : "周" : "") + c[b.getDay() + ""]));
            for (var d in e) (new RegExp("(" + d + ")")).test(a) && (a = a.replace(RegExp.$1, 1 == RegExp.$1.length ? e[d] : ("00" + e[d]).substr(("" + e[d]).length)));
            return a
        }, stope: function (e) {
            e = e || window.event;
            e.stopPropagation ? e.stopPropagation() : e.cancelBubble = !0
        }, arrange: function (curindex) {
            curindex = $(".swiper-slide-active").index();
            var curpage = $(".desktopContainer:eq(" + ("" == curindex || void 0 == curindex ? 0 : curindex) + ")");
            var arrleft = false;
            if (arrleft) {
                var _dc = $(".desktopContainer"), left = 0, top = 0, owidth = 96, oheight = 96, h = 0, height = _dc.height() - ($("#taskbar").height() || 0);
                _dc.width();
                curpage.find(".desktop-app").each(function (i, othis) {
                    $(othis).css("top", top + "px");
                    $(othis).css("left", left + "px");
                    oheight = $(othis).height();
                    owidth = $(othis).width();
                    top = top + oheight + 10 + 10;
                    top >= height - $("#topbar").height() && (top = 0, left = left + owidth + 10)
                })
            } else {
                //排序一个桌面
                function arronepage(curpage) {
                    var dcwidth = _dc.width();
                    var owidth = 138, oheight = 138, h = 0, height = _dc.height() - ($("#taskbar").height() || 0);
                    var left = dcwidth - owidth - 10, top = 0
                    curpage.find(".desktop-app").each(function (i, othis) {
                        $(othis).css("top", top + "px");
                        $(othis).css("left", left + "px");
                        oheight = $(othis).height();
                        owidth = $(othis).width();
                        top = top + oheight + 10 + 10;
                        if (top >= height - $("#topbar").height()) {
                            top = 0;
                            left = left - owidth - 10
                        }
                    })
                }

                _dc = $(".desktopContainer");

                arronepage(curpage);
                //为了解决浏览器缩放时现在前一个桌面的图标问题，排序时 把前一个桌面也排序
                if (curindex != 0) {
                    var prepage = $(".desktopContainer:eq(" + (curindex - 1) + ")");
                    arronepage(prepage);
                }
                /*var
                var owidth = 138, oheight = 138, h = 0, height = _dc.height() - ($("#taskbar").height() || 0);
                var left = dcwidth - owidth - 10, top = 0
                curpage.find(".desktop-app").each(function (i, othis) {
                    $(othis).css("top", top + "px");
                    $(othis).css("left", left + "px");
                    oheight = $(othis).height();
                    owidth = $(othis).width();
                    top = top + oheight + 10 + 10;
                    if (top >= height - $("#topbar").height()) {
                        top = 0;
                        left = left - owidth - 10
                    }
                })*/
            }
        }, refreshWind: function (a) {
            a = a.data("id");
            url = $("#layui-layer-iframe" + a).attr("src");
            layer.iframeSrc(a, url)
        }, resizeInit: function () {
            $(window).resize(function (a) {
                _this.initHeight()
                setTimeout(function () {
                    if ($("#divdeskleft").css('display') == 'none') {
                        $("#divdesk").width($(window).width())
                    } else {
                        $("#divdesk").width($(window).width() - $("#divdeskleft").width())
                    }
                    setTimeout(function () {
                        desktop.arrange()
                    }, 500)
                }, 500)
            })
        }, launchFullscreen: function () {
            var element = document.documentElement;
            var pro = null;
            if (element.requestFullscreen) {
                pro = element.requestFullscreen();
            } else if (element.mozRequestFullScreen) {
                pro = element.mozRequestFullScreen();
            } else if (element.webkitRequestFullscreen) {
                pro = element.webkitRequestFullscreen();
            } else if (element.webkitRequestFullScreen) {//safari
                pro = element.webkitRequestFullScreen();
            } else if (element.msRequestFullscreen) {
                pro = element.msRequestFullscreen();
            }
            objdata.isfull = true;
        }, exitFullscreen: function () {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.mozCancelFullScreen) {
                document.mozCancelFullScreen();
            } else if (document.webkitCancelFullScreen) {//safari
                document.webkitCancelFullScreen();
            } else if (document.webkitExitFullscreen) {
                document.webkitExitFullscreen();
            }
            objdata.isfull = false;
        }, checkFullscreen: function () {
            var isFull = false;
            if (document.fullscreenEnabled || document.msFullscreenEnabled || document.webkitFullscreenEnabled || document.webkitIsFullScreen) {
                isFull = window.fullScreen || document.webkitIsFullScreen || document.webkitIsFullScreen;
                if (isFull === undefined) {
                    isFull = false;
                }
            }
            return isFull;
        }, changefullscreen: function (o) {
            if ($(o).text() == '全屏') {
                _this.launchFullscreen();
            } else {
                _this.exitFullscreen();
            }
            $(".desktop-menu").hide()
        }, hidetaskbar: function (othis) {
            desktop.hidemenu();
            $("#taskbar").hide()
            _this.initHeight();
            if ($(".desktop-taskbar-app-list").children().length) {
                $(".desktop-taskbar-app-list").children().each(function () {
                    layer.style($(this).attr('winindex'), {
                        width: $("#divdesk").width() + 'px',
                        height: $("#divdesk").height() + 'px',
                        left: ($(window).width() - $("#divdesk").width()) + 'px',
                        right: 0
                    });
                })
            }
            $(".layui-layer-opttaskbar").show();
        }, showdesktopicon: function (othis) {
            desktop.hidemenu();
            $("#topbar").css('visibility', 'visible')
            $(".swiper-wrapper").css('visibility', 'visible')
        }, hidedesktopicon: function (othis) {
            desktop.hidemenu();
            $("#topbar").css('visibility', 'hidden')
            $(".swiper-wrapper").css('visibility', 'hidden')
        }, showtaskbar: function (othis) {
            desktop.hidemenu();
            $("#taskbar").show()
            _this.initHeight();
            if ($(".desktop-taskbar-app-list").children().length) {
                $(".desktop-taskbar-app-list").children().each(function () {
                    layer.style($(this).attr('winindex'), {
                        width: $("#divdesk").width() + 'px',
                        height: $("#divdesk").height() + 'px',
                        left: ($(window).width() - $("#divdesk").width()) + 'px',
                        right: 0
                    });
                })
            }
            $(".layui-layer-opttaskbar").hide();
        }, contextmenuInit: function () {
            $("#divdesktop").contextmenu(function () {
                return !1
            });
            $(".lockscreen-con").contextmenu(function () {
                return !1
            });
            $("#taskbar").contextmenu(function () {
                return !1
            });
            var cmurlpath = 'images/desktop/clickmenu/';
            //桌面上右键
            $("#divdesk").on("contextmenu", function (e) {
                clearTimeout(objdata.taskrighttime)
                $(".desktop-app").removeClass('desktop-app-select')
                var _target = $(e.target);
                var _desktopmenu = $(".desktop-menu");
                if (_target.hasClass('desktop-app') || _target.parents(".desktop-app").length) {
                    var _app = null;
                    if (_target.hasClass('desktop-app')) {
                        _app = _target;
                    } else {
                        _app = _target.parents(".desktop-app");
                    }
                    _app.addClass('desktop-app-select');
                    _this.curContextmenumApp = _app;
                    _desktopmenu.html('<ul>\
                        <li><a href="javascript:;" class="small-click" data-type="openrighttarget"><img src="' + cmurlpath + 'icon_dakai.png">打开</a></li>\
                        <li><a href="javascript:;" class="small-click" data-type="deleterighttarget"><img src="' + cmurlpath + 'icon_shanchu.png">删除</a></li>\
                        </ul>')
                } else {
                    var openlength = $(".desktop-taskbar-app-list").children().length;
                    var strrenwulan = '';
                    if ($("#taskbar").css('display') == 'none') {
                        strrenwulan = '<li><a href="javascript:;" class="small-click" data-type="showtaskbar"><img src="' + cmurlpath + 'icon_pailie.png">显示任务栏</a></li>';
                    } else {
                        strrenwulan = '<li><a href="javascript:;" class="small-click" data-type="hidetaskbar"><img src="' + cmurlpath + 'icon_pailie.png">隐藏任务栏</a></li>';
                    }
                    var strdesktopicon = '';
                    if ($("#topbar").css('visibility') == 'hidden') {
                        strdesktopicon = '<li><a href="javascript:;" class="small-click" data-type="showdesktopicon"><img src="' + cmurlpath + 'icon_pailie.png">显示桌面图标</a></li>';
                    } else {
                        strdesktopicon = '<li><a href="javascript:;" class="small-click" data-type="hidedesktopicon"><img src="' + cmurlpath + 'icon_pailie.png">隐藏桌面图标</a></li>';
                    }

                    _desktopmenu.html('<ul>\
                        ' + strdesktopicon + '\
                        <li><a href="javascript:;" class="small-click" data-type="refreshdesk"><img src="' + cmurlpath + 'icon_shuaxin.png">刷新</a></li>\
                        <li><a href="javascript:;" class="small-click" data-type="setting"><img src="' + cmurlpath + 'icon_shezhi.png">桌面设置</a></li>\
                        ' + strrenwulan + '\
                        ' + (openlength ? '<li><a href="javascript:;" class="small-click" data-type="closealltask"><img src="' + cmurlpath + 'icon_guanbi.png">关闭所有窗口</a></li>' : '') + '\
                        <li><a href="javascript:;" class="small-click" data-type="changefullscreen"><img src="' + cmurlpath + 'icon_' + (objdata.isfull ? 'no' : '') + 'quanping.png">' + (objdata.isfull ? '退出全屏' : '全屏') + '</a></li>\
                        <li><a href="javascript:;" class="small-click" data-type="lockscreen"><img src="' + cmurlpath + 'icon_suoping.png">锁屏</a></li>\
                        <li><a href="javascript:;" class="small-click" data-type="fastdaohang"><img src="' + cmurlpath + 'icon_daohang.png">快速导航</a></li>\
                        <li><a href="javascript:;" class="small-click" data-type="loginout"><img src="' + cmurlpath + 'icon_tuichu.png">退出系统</a></li>\
                        </ul>')
                    setTimeout(function () {
                        objdata.isfull = _this.checkFullscreen();
                        if (objdata.isfull) {
                            if (_desktopmenu.find('[data-type="changefullscreen"]').text() == '全屏')
                                _desktopmenu.find('[data-type="changefullscreen"]').html('<img src="' + cmurlpath + 'icon_noquanping.png">退出全屏');
                        } else {
                            if (_desktopmenu.find('[data-type="changefullscreen"]').text() == '退出全屏')
                                _desktopmenu.find('[data-type="changefullscreen"]').html('<img src="' + cmurlpath + 'icon_quanping.png">全屏');
                        }
                    }, 1000)
                }
                var c = e.clientX;
                var a = e.clientY;
                var g = document.body.clientWidth, k = document.body.clientHeight, c = c + _desktopmenu.width() >= g ? g - _desktopmenu.width() - 15 : c;
                a = a + _desktopmenu.height() >= k - 40 ? k - _desktopmenu.height() - 15 : a;
                _desktopmenu.css({top: a, left: c}).show()
                return false;
            })
            //底部任务栏上右击
            $(".desktop-taskbar-app-list").on("contextmenu", function (e) {
                var _target = $(e.target);
                var _desktopmenu = $(".desktop-menu");
                var openlength = $(".desktop-taskbar-app-list").children().length;
                if (_target.hasClass('taskbar-app') || _target.parents(".taskbar-app").length) {
                    var _app = null;
                    if (_target.hasClass('taskbar-app')) {
                        _app = _target;
                    } else {
                        _app = _target.parents(".taskbar-app");
                    }
                    _this.curContextmenumTask = _app;
                    _desktopmenu.html('<ul>' +
                        '<li><a href="javascript:;" class="small-click" data-type="showdesktop"><img src="' + cmurlpath + 'icon_pailie.png">显示桌面</a></li>' +
                        '<li><a href="javascript:;" class="small-click" data-type="hidetaskbar"><img src="' + cmurlpath + 'icon_quanping.png">隐藏任务栏</a></li>' +
                        (openlength > 1 ? '<li><a href="javascript:;" class="small-click" data-type="closealltask"><img src="' + cmurlpath + 'icon_guanbi.png">关闭所有窗口</a></li>' : '') +
                        (openlength > 1 ? '<li><a href="javascript:;" class="small-click" data-type="closeothertask"><img src="' + cmurlpath + 'icon_guanbi.png">关闭其他窗口</a></li>' : '') +
                        '<li><a href="javascript:;" class="small-click" data-type="closetask"><img src="' + cmurlpath + 'icon_guanbi.png">关闭窗口</a></li>' +
                        '</ul>')
                } else {
                    _desktopmenu.html('<ul>' +
                        '<li><a href="javascript:;" class="small-click" data-type="showdesktop"><img src="' + cmurlpath + 'icon_pailie.png">显示桌面</a></li>' +
                        '<li><a href="javascript:;" class="small-click" data-type="hidetaskbar"><img src="' + cmurlpath + 'icon_quanping.png">隐藏任务栏</a></li>' +
                        '<li><a href="javascript:;" class="small-click" data-type="changefullscreen"><img src="' + cmurlpath + 'icon_' + (objdata.isfull ? 'no' : '') + 'quanping.png">' + (objdata.isfull ? '退出全屏' : '全屏') + '</a></li>' +
                        (openlength ? '<li><a href="javascript:;" class="small-click" data-type="closealltask"><img src="' + cmurlpath + 'icon_guanbi.png">关闭所有窗口</a></li>' : '') +
                        '</ul>')
                }
                clearTimeout(objdata.taskrighttime)
                _target.mouseenter(function () {
                    clearTimeout(objdata.taskrighttime)
                }).mouseleave(function () {
                    clearTimeout(objdata.taskrighttime)
                    objdata.taskrighttime = setTimeout(function () {
                        _desktopmenu.hide();
                    }, 1000)
                })
                _desktopmenu.mouseenter(function () {
                    clearTimeout(objdata.taskrighttime)
                }).unbind('mouseleave').mouseleave(function () {
                    clearTimeout(objdata.taskrighttime)
                    objdata.taskrighttime = setTimeout(function () {
                        _desktopmenu.hide();
                    }, 1000)
                })

                var c = e.clientX;
                var a = e.clientY;
                var g = document.body.clientWidth, k = document.body.clientHeight, c = c + _desktopmenu.width() >= g ? g - _desktopmenu.width() - 15 : c;
                a = a + _desktopmenu.height() >= k - 40 ? k - _desktopmenu.height() - 15 : a;
                _desktopmenu.css({top: a, left: c}).show()
                return false;
            });
            //左侧菜单栏右击
            $("#divmenulist").on("contextmenu", function (e) {

                var _target = $(e.target);
                var _desktopmenu = $(".desktop-menu");
                if (_target.hasClass('onmenuapp') || _target.parents(".onmenuapp").length) {
                    var _app = null;
                    if (_target.hasClass('onmenuapp')) {
                        _app = _target;
                    } else {
                        _app = _target.parents(".onmenuapp");
                    }
                    _this.curContextmenumApp = _app;
                    _desktopmenu.html(
                        '<ul>' +
                        '<li><a href="javascript:;" class="small-click" data-type="openrighttarget"><img src="' + cmurlpath + 'icon_dakai.png">打开</a></li>' +
                        '<li><a href="javascript:;" class="small-click" data-type="sendtodesk"><img src="' + cmurlpath + 'icon_fasong.png">发送到桌面</a></li>' +
                        '</ul>'
                    );
                    _desktopmenu.mouseenter(function () {
                        if ($("#divdeskleft").is(":animated")) {
                            $("#divdeskleft").stop(true);
                            $("#divdeskleft").animate({
                                'opacity': 1
                            });
                        }
                    }).unbind('mouseleave').mouseleave(function () {
                        if (objdata.menulock) return;
                        $("#divdeskleft").fadeOut(1000);
                        _desktopmenu.hide();
                    })
                }
                var c = e.clientX;
                var a = e.clientY;
                var g = document.body.clientWidth, k = document.body.clientHeight, c = c + _desktopmenu.width() >= g ? g - _desktopmenu.width() - 15 : c;
                a = a + _desktopmenu.height() >= k - 40 ? k - _desktopmenu.height() - 15 : a;
                _desktopmenu.css({top: a, left: c}).show()
            });
            $('body').on('click', '.layerdesktopapp .layui-layer-title', function (e) {
                desktop.hidemenu();
            });
            $('body').on('contextmenu', '.layerdesktopapp .layui-layer-title', function (e) {
                var _desktopmenu = $(".desktop-menu");
                var _target = $(e.target);
                if (_target.hasClass('layui-layer-title') || _target.parents(".layui-layer-title").length) {
                    var _titledom = null;
                    if (_target.hasClass('layui-layer-title')) {
                        _titledom = _target;
                    } else {
                        _titledom = _target.parents(".onmenuapp");
                    }
                }
                _this.curContextmenum_titledom = _titledom;
                _desktopmenu.html(
                    '<ul>' +
                    '<li><a href="javascript:;" class="small-click" data-type="cmwintitle" key="daohang"><i class="iconfont icon_navigation blueicon"></i>快速导航</a></li>' +
                    '<li><a href="javascript:;" class="small-click" data-type="cmwintitle" key="refreash"><i class="iconfont icon_refresh1 blueicon"></i>刷新</a></li>' +
                    '<li><a href="javascript:;" class="small-click" data-type="cmwintitle" key="min"><i class="iconfont icon_minimized blueicon"></i>最小化</a></li>' +
                    '<li><a href="javascript:;" class="small-click" data-type="cmwintitle" key="close"><i class="iconfont icon_closepage blueicon"></i>关闭</a></li>' +
                    '</ul>'
                );
                _desktopmenu.mouseenter(function () {

                }).unbind('mouseleave').mouseleave(function () {
                    _desktopmenu.hide();
                })
                var c = e.clientX;
                var a = e.clientY;
                var g = document.body.clientWidth, k = document.body.clientHeight, c = c + _desktopmenu.width() >= g ? g - _desktopmenu.width() - 15 : c;
                a = a + _desktopmenu.height() >= k - 40 ? k - _desktopmenu.height() - 15 : a;
                _desktopmenu.css({top: a, left: c}).show()
                return false;
            })
        }, cmwintitle: function (othis) {
            var key = othis.attr('key');
            _this.curContextmenum_titledom.siblings('.layui-layer-setwin').find('.layui-layer-' + key).trigger('click');
            desktop.hidemenu();
        }, lockingCover: function (a) {
            $(a).toggle().siblings(".locking-unlock").removeClass("layui-hide")
        }, lockscreen: function (a) {
            desktop.hidemenu()
            if (!objdata.objmy.lockscreenpwd) {
                layer.confirm('您尚未设置锁屏密码', {
                    btnAlign: 'c',
                    btn: ['去设置', '继续锁屏'] //按钮
                }, function (index) {
                    layer.close(index);
                    desktop.openwinbymid(9);
                }, function () {
                    _this.dolock();
                });
            } else {
                _this.dolock()
            }
        }, dolock: function () {
            $("#divdeskleft").attr("zIndex", $("#divdeskleft").css("zIndex")).css("zIndex", 0);
            _this.startlockscreeninter();
            $(".lockscreen-con").show();
            $.sm(function () {

            }, ["desktop.lockscreen.change", 1])
        }, startlockscreeninter: function () {
            var _screentime = $(".screen-time");
            objdata.lockscreeninter = setInterval(function () {
                _screentime.children('div').eq(0).text(objdata.curmoment.format("HH"))
                _screentime.children('div').eq(1).text(objdata.curmoment.format("mm"))
                _screentime.children('div').eq(2).text(objdata.curmoment.format("ss"))
            }, 1000)
        }, unlockscreen: function () {
            var can = true;
            if (objdata.objmy.lockscreenpwd) {
                if ($("#lockscreenpwd").val() != objdata.objmy.lockscreenpwd) {
                    can = false;
                    $("#lockscreentip").text("锁屏密码输入错误").show();
                    return
                }
            }
            clearInterval(objdata.lockscreeninter)
            $(".lockscreen-con").hide();
            $("#divdeskleft").css("zIndex", $("#divdeskleft").attr("zIndex"));
            $("#divdesktop").css('visibility', 'visible')
            $("#taskbar").css('visibility', 'visible')
            $("#lockscreentip").hide();
            $("#lockscreenpwd").val("")
            $.sm(function () {

            }, ["desktop.lockscreen.change", 0])
        }, initmenu: function (fmids, re) {
            if (re.length == 0)
                return layer.msg("未开通栏目权限，请联系管理员开通");

            var mainlength = 1;
            if (mainlength == 1) {
                $("#ulmenutab li").css("width", "100%");
            } else if (mainlength == 2) {
                $("#ulmenutab li").css("width", "50%");
            }
            //点击菜单栏顶部切换
            var _divmenulist = $('#divmenulist');
            var objhtml = {};
            $("#ulmenutab").children().each(function () {
                var type = $(this).data('type');
                objhtml[type] = '';
            })
            var ismergegroup = 1;
            var menuHtml = getMenuHtml(re, ismergegroup);
            var hasnum = 0;
            var groupname = {};//栏目组名称
            if (ismergegroup == 1) {//合并
                var key = "erbao";
                // _divmenulist.children('[data-type="' + key + '"]').html(objhtml[key]);
                objdata.my.curmenutype = key;
            } else {
                for (var key in objhtml) {
                    if (objhtml[key]) {
                        hasnum++;
                        var $li = $("#ulmenutab li[data-type='" + key + "']");
                        if (groupname[key]) {//重命名
                            $li.find('label').text(groupname[key]);
                        }
                        $li.show();
                    }
                    // _divmenulist.children('[data-type="' + key + '"]').html(objhtml[key]);
                }
            }
            $("#divmenulist").html(menuHtml);
            //处理大栏目组下无栏目情况
            if (hasnum <= 1) {
                // $(".menutab").hide();
                $("#ulmenutab").height(0);
            } else if (hasnum == 2) {
                $("#ulmenutab li").css("width", "50%");
            }

            $("#ulmenutab").children().off('click').click(function () {
                $(this).addClass('current').siblings().removeClass('current');
                var type = $(this).data('type');
                if ($(this).attr('noneedrecord')) {
                    $(this).removeAttr('noneedrecord')
                } else {
                    $.sm(function (re, err) {

                    }, ['user.changecurmenutype', type])
                }
                _divmenulist.children('[data-type="' + type + '"]').show().siblings().hide()
            })
            $("#ulmenutab").children('[data-type="' + (objdata.my.curmenutype || 'erbao') + '"]').attr('noneedrecord', true).trigger('click');
            $('#divmenulist').niceScroll();
            $('#divmenulist').on('click', 'h5', function () {
                if ($(this).children('i').hasClass('icon_trian-down')) {//展开着  关闭
                    $(this).children('i').removeClass('icon_trian-down').addClass('icon_trian-right');
                    $(this).siblings().slideUp(function () {
                        $('#divmenulist').getNiceScroll().resize();
                    });
                } else {
                    $(this).children('i').removeClass('icon_trian-right').addClass('icon_trian-down');
                    $(this).siblings().slideDown(function () {
                        $('#divmenulist').getNiceScroll().resize();
                    });
                }
            });
            $("#divmenulist").on('click', '.ulsonmenu li', function () {
                _this.openwin($(this));
            });

            //童帮体检预约处理登陆打开页面
            var query = window.location.search.substring(1);
            var vars = query.split("&");
            for (var i = 0; i < vars.length; i++) {
                var arrs = vars[i].split("=");
                var clickel = "";
                if (arrs[1] == "checkin") {//入园体检和年度体检预约,
                    // $("#erbao").trigger('click');
                    clickel = $('#divmenulist .ulsonmenu>li[mid="66"]');
                } else if (arrs[1] == "check") {//入园体检和年度体检预约,
                    // $("#erbao").trigger('click');
                    clickel = $('#divmenulist .ulsonmenu>li[mid="78"]');
                } else if (arrs[1] == "teachcheck") {//teachcheck教师体检预约
                    // $("#tijian").trigger('click');
                    clickel = $('#divmenulist .ulsonmenu>li[mid="28"]');
                    // $("#divmenulist").find('.menulist[data-type="tijian"] .funList:first').trigger('click');
                }
                if (clickel) {
                    clickel.parent().parent("li").find("h5").trigger('click');
                    setTimeout(function () {
                        clickel.trigger('click');
                        $(window).trigger("resize");
                    }, 500);
                    break;
                }
            }
        },
        getdefaultdeskdata: function () {
            var objdeskdata = [{
                menukey: "erbao",
                image: "images/desktop/top/icon_baoyu.png",
                name: "儿保",
                arrmenuid: ["43", "41", "44", "40", "35", "36", "51", "42"]
            }, {
                menukey: "tijian",
                image: "images/desktop/top/icon_baojiao.png",
                name: "体检",
                arrmenuid: ["12", "13", "33", "15", "16", "17", "19", "20", "28", "22", "23", "24", "25", "26", "31"]
            }, {
                menukey: "richang",
                image: "images/desktop/top/icon_richang.png",
                name: "日常",
                arrmenuid: ["58", "59", "3", "9", "10", "29", "30"]
            }, {
                menukey: "bangong",
                image: "images/desktop/top/icon_bangong.png",
                name: "办公",
                arrmenuid: []
            }];
            if (istuoyou()) {//幼儿园用户默认删除儿保
                objdeskdata.shift();
            }
            return objdeskdata;
        },
        initHeight: function () {
            /*$(".desktop-container").css("height", $(window).height() - 40);
            $(".desktopContainer").css("height", $(".desktop-container").height());*/
            var wh = $(window).height();
            var taskbarh = $("#taskbar").css('display') == 'none' ? 0 : $("#taskbar").height();
            $("#divdesktop").css("height", wh - taskbarh);//桌面高度
            $("#divdeskleft").css("height", wh - taskbarh);//左侧高度
            $(".swiper-wrapper").css("height", wh - taskbarh - $("#topbar").height());
            $("#divmenulist").css("height", wh - taskbarh - /*$("#ulmenutab").height() - */$("#leftyeytop").height());//
        },
        initTips: function () {
            $('body').on('mouseenter', '*[lay-tips]', function () {
                var othis = $(this);
                var tips = othis.attr('lay-tips')
                    , offset = othis.attr('lay-offset')
                    , zIndex = othis.attr('lay-tips-zindex')
                    , skin = othis.attr('lay-skin');
                var objp = {
                    tips: othis.attr('lay-direction') || 1
                    , time: -1
                    , success: function (layero, index) {
                        if (offset) {
                            layero.css('margin-left', offset + 'px');
                        }
                    }
                }
                if (zIndex) {
                    objp.zIndex = zIndex;
                }
                if (skin) {//layui-tipsno
                    objp.skin = skin;
                }
                this.layerindex = layer.tips(tips, this, objp);
            }).on('mouseleave', '*[lay-tips]', function () {
                layer.close(this.layerindex);
            });
        },
        init: function () {
            _this = this;
            //处理开始菜单逻辑
            _this.initStartMenu();
            _this.initTips();
            var arrthismsg = ["index.init"];
            $.sm(function (re, err) {//id,truename,headimg,mobile,fmids,recordstatus,keytype,defindex,lastpages,roles,depart_id,curbackimg,curtheme,curlan,roletype,uname,lasttime,pids,mainid,employee_id,deskindex,jsondeskdata,islockmenu
                if (re) {
                    objdata.my = {
                        id: re[0][0],
                        username: re[0][1],
                        headimg: re[0][2],
                        role: re[0][9],//2 地区管理员 5 普通用户
                        roletype: re[0][14],
                        areacode: re[0][10],
                        mobile: re[0][3],
                        departId: re[0][33],//企业或外部体检机构的id
                        mainid: re[0][18]
                        , utype: re[0][29]//f妇幼用户，g医院用户，y幼儿园用户,qykh企业客户，wbtjjg外部体检机构
                        , organid: re[0][30] || ""//机构id，机构用户才有 ,园所用户时为关联体检医院的机构id
                        , yeyid: re[0][31]//幼儿园id，园所用户才有
                        , isjky: 1
                        , canCreatSlice: re[0][32] * 1//re[0][19] * 1;
                    };
                    // getxcxuser();
                    var jgname = "";
                    //查询园所信息
                    if (istuoyou()) {
                        $.sm(function (re, err) {
                            if (err) {
                            } else {
                                objdata.yeyinfo = re && re[0] || {};
                                objdata.my.fuyouorganid = re[0].fuyouorganid;//所属机构
                                objdata.my.organid = re[0].fuyoumdicalorganid;//体检医院1
                                objdata.my.organid2 = re[0].fuyoumdicalorganid2;//体检医院2
                                jgname = objdata.yeyinfo.yeyname;
                            }
                            getsysset(objdata.yeyinfo.id);//获取园所学生排序设置
                        }, ["index.yeyinfo"], null, null, {async: false});
                    } else if (iserbao()) {
                        $.sm(function (re, err) {
                            if (err) {
                            } else {
                                objdata.my.organname = re && re[0] && re[0].jgname || "";
                                objdata.my.isnzhishu = re && re[0] && re[0].isnzhishu || "";
                                jgname = objdata.my.organname;
                            }
                        }, ["index.organinfo"], null, null, {async: false});
                    }
                    jgname = jgname + (jgname ? "（" + objdata.my.username + "）" : objdata.my.username);
                    $("#selcurname").prop('title', jgname).html(jgname);
                    //获取系统名称
                    getSysName(function () {
                        getnoticenum(1);//获取加入通知、通知公告等消息
                        getcaseremind();//获取专案新增提醒
                        if (istuoyou()) {
                            initGoupTime(function (re1) {//获取升班日期
                                var gouptime = "";
                                if (re1[0] && re1[0][0]) {//当前日期前的升班日期
                                    objdata.arrGouptTime[0] = re1[0][re1[0].length - 1][5];
                                    gouptime = re1[0][re1[0].length - 1][5];
                                }
                                if (re1[1] && re1[1][0]) {//当前日期后的升班日期
                                    objdata.arrGouptTime[1] = re1[1][re1[1].length - 1][5];
                                    gouptime = re1[1][re1[1].length - 1][5];
                                }
                                if (gouptime) {
                                    var curdate = objdata.curmoment._d;//objdata.moment(new Date("2021-10-12"))._d;//
                                    var year = curdate.getFullYear();
                                    var month = curdate.getMonth();
                                    var ms = DateTimeDiff('m', getDateByStr(gouptime), curdate);//升班日期与当前日期相差的月份
                                    if (!DateCompare(gouptime, curdate.getDateString()) && ms >= 12) {//升班超过1年添加升班提醒。
                                        objdata.isopenad = 1;
                                        layer.open({
                                            id: "ad",
                                            zIndex: 2147483647,
                                            type: 2,
                                            title: "升班提示",
                                            shadeClose: false,
                                            // offset: 'rb',
                                            area: ['580px', '280px'],
                                            btn: ["去升班"],
                                            content: 'tongall/remindpage.html?mid=' + Arg("mid") + '&v=' + (Arg("v") || 1) + "&type=sb" + "&months=" + ms + '&gouptime=' + gouptime,
                                            success: function (layero1, index1) {
                                                objdata.layers.adlayer = layero1;
                                                objdata.layers.adindex = index1;
                                                layero1.css("z-index", 2147483647);
                                            },
                                            yes: function (index, layero) {
                                                openwinbyid(objdata.pageids.sbedit);
                                                layer.close(index);
                                            }
                                        });
                                    }
                                }
                            });
                        }
                    });
                    objdata.objmy = {
                        id: re[0][0],
                        name: re[0][1],
                        img: re[0][2],
                        dataareacode: re[0][10],
                        curbackimg: re[0][11],
                        curtheme: re[0][12],
                        deskindex: re[0][20],
                        strdeskdata: re[5],
                        islockmenu: re[0][22],
                        unreadmsgnum: re[0][23],
                        isimautostart: re[0][24],
                        signature: re[0][25],
                        islockscreen: re[0][26],
                        lockscreenpwd: re[0][27],
                        curmenutype: re[0][28]
                    };
                    objdata.fyinfo = re[3];//妇幼信息
                    //获取区域信息
                    getarrarea(function () {
                        var fyareaname = getAreaNamebyCode(objdata.fyinfo[2] || re[0][10]);
                        objdata.fyinfo.push(fyareaname);//3第三位
                    });

                    if (objdata.objmy.islockscreen == 1) {
                        _this.dolock();
                    } else {
                        $("#divdesktop").css('visibility', 'visible')
                        $("#taskbar").css('visibility', 'visible')
                    }
                    //设置高度
                    _this.initHeight();
                    $("#headimg").prop('src', objdata.my.headimg ? ossPrefix + objdata.my.headimg : 'images/desktop/default.png')

                    $(".lockscreen-user").children('p').text(objdata.my.username);
                    $(".lockscreen-user").children('img').prop('src', objdata.my.headimg ? ossPrefix + objdata.my.headimg : 'images/desktop/default.png');
                    if (re[0][7]) {
                        objdata.defindex = re[0][7];
                    }
                    objdata.recordstatus = re[0][5];
                    objdata.keytype = re[0][6];
                    objdata.lastpages = re[0][8];
                    objdata.curback = re[0][11];
                    objdata.curtheme = 'blue';
                    if (re[0][12]) {
                        objdata.curtheme = re[0][12] || 'blue';
                    }
                    $("#linktheme").attr('href', 'css/' + objdata.curtheme + ".css");
                    objdata.curback = objdata.curback || "1";
                    if (objdata.curback) {
                        if (objdata.curback.indexOf("#") > -1) {
                            $("#loading").hide().remove();
                            $("body").css("background", objdata.curback);
                        } else {
                            $("body").css("background", "url(images/back/" + objdata.curback + ".jpg) center");
                            var dimg = new Image();
                            dimg.src = "images/back/" + objdata.curback + ".jpg";
                            dimg.onload = function () {
                                //隐藏loading
                                $("#loading").hide().remove();
                            };
                        }
                    } else {
                        //隐藏loading
                        $("#loading").hide().remove();
                    }

                    var fmids = "";
                    if (re[0].length > 0) {
                        fmids = re[0][4];
                        objdata.fmids = fmids.split(',');
                    }
                    objdata.arrmy = re[0];
                    objdata.expmids = re[0][re[0].length - 1];//体验栏目设置
                    //获取是不是第一次登陆
                    // if (!re[0][16]) {
                    //     objdata.isfirst = true;
                    //     objdata.wenav = layer.open({
                    //         type: 2,
                    //         title: '系统说明',
                    //         area: ['800px', '580px'],
                    //         content: 'html/w_Wenav.html?v=' + version//'html/pop/timezone.html?v=' + version
                    //     });
                    // }
                    //获得权限
                    getrv(function () {
                        //得到通知数目
                        //getnoticenum();
                    });
                    // 初始化栏目
                    _this.initmenu(fmids, re[1]);
                    //时间
                    objdata.arrtime = re[2];
                    //时间显示
                    objdata.curmoment = moment(new Date(re[2][0], re[2][1] * 1 - 1, re[2][2], re[2][3], re[2][4], re[2][5]));
                    var strlastlogin = re[4][0].replace(/____/g, ":")
                    objdata.loginmoment = moment(getTimeByStr(strlastlogin));
                    objdata._labellogintime = $("#labellogintime");
                    objdata.strtoday = re[2][0] + "-" + (re[2][1].length == 1 ? "0" + re[2][1] : re[2][1]) + "-" + (re[2][2].length == 1 ? "0" + re[2][2] : re[2][2]);
                    //打开默认页面
                    // objdata.headtime = $(".headTime").html(objdata.curmoment.format('YYYY-MM-DD HH:mm:ss'));//时间显示
                    setInterval(interfun, 1000);
                    //记录退出时间
                    setInterval(function () {
                        if (Logid)
                            $.sm(function () {
                            }, ["w_logintime.recordlogout"])
                    }, 3 * 60 * 1000)
                    // //默认打开页面类型 re[5] re[8]
                    // var strpages = re[0][8];
                    // if (strpages) {
                    //     var arrpages = strpages.split(",");
                    //     if (arrpages.length) {
                    //         for (var i = 0; i < arrpages.length; i++) {
                    //             openwinbyid(arrpages[i]);
                    //         }
                    //     } else {
                    //         openwinbyid(10);
                    //     }
                    // } else {
                    //     openwinbyid(10);
                    // }
                    //登录加载完成
                    if (Logid) {
                        var sysinfo = objdata.sysInfo;
                        $.sm(function (reend, errend) {
                        }, ["w_logintime.udt", sysinfo.areacode || "", sysinfo.areaname || "", sysinfo.sysname || "", sysinfo.sysversion || "", sysinfo.brotype || "", sysinfo.broversion || "", sysinfo.cip || "", sysinfo.sessionid || ""]);
                    }
                    //处理角色
                    var arrrole = objdata.arrmy[9].split(',');
                    objdata.isadmin = 0;
                    objdata.my.role = objdata.arrmy[9];
                    for (var j = 0; j < arrrole.length; j++) {
                        if (arrrole[j] == '1') {
                            objdata.isadmin = 1;
                        }
                    }
                    //首次登陆修改密码
                    //获取是不是第一次登陆
                    if (!re[0][16]) {
                        objdata.isfirst = true;
                        alterpwd(function () {
                        }, "为了您的账号安全，登录后请第一时间修改密码");
                    }
                    resolve();
                } else {
                    alert("登录过期，请重新登录！");
                    if (Logid) {
                        $.sm(function () {
                        }, ["w_logintime.logout"]);
                    }
                    $.sm(function () {
                        $(window).unbind('beforeunload');
                        if (Arg("type") == "jctech") {//其他页面登录
                            location.href = "../jctechlogin.html";
                        } else {
                            location.href = objdata.loginPage;
                        }
                    }, ["index.logout"]);
                }
            }, arrthismsg);
            // function getxcxuser(){
            //     $.sm(function(re, err, obj){
            //         if(obj){
            //             objdata.my.xcxuserid = obj.id;
            //         }
            //     },["xcxuser.selectbymobile", objdata.my.mobile])
            // }
            function resolve() {//获取完结果
                $(".desktop-taskbar-app-list").niceScroll();
                if (objdata.objmy.islockmenu == "1") {
                    objdata.menulock = true;
                    var _dom = $(".menulock");
                    _dom.children('img').prop('src', 'images/desktop/icon_lock.png');
                    _dom.addClass('lockstate')
                    $("#divdeskleft").show().css('position', 'relative')
                    $("#icon_lockmenu").show()//显示锁定图标

                    $("#topbar").css('visibility', 'hidden')
                    $(".swiper-wrapper").css('visibility', 'hidden')
                } else {
                    $("#topbar").css('visibility', 'visible')
                    $(".swiper-wrapper").css('visibility', 'visible')
                }
                // 修改头像信息
                /*objdata.objmy = {
                    id: re[0][0],
                    name: re[0][1],
                    img: re[0][2],
                    deskindex: re[0][20],
                    strdeskdata:re[0][21],
                    islockmenu:re[0][22]
                };*/
                var curindex = objdata.objmy.deskindex || 0;
                var strhtml = '';
                var strtoptab = '';//top切换的html
                var arrdeskdata = objdata.objmy.strdeskdata;
                if (!arrdeskdata) {
                    arrdeskdata = _this.getdefaultdeskdata();
                }
                objdata.objmy.arrdeskdata = arrdeskdata;
                for (var i = 0; i < arrdeskdata.length; i++) {
                    var stronepagehtml = '';
                    for (var j = 0; j < arrdeskdata[i].arrmenuid.length; j++) {
                        var mid = arrdeskdata[i].arrmenuid[j];
                        var oneappdata = objdata.objMenu[mid];
                        if (!oneappdata) continue;
                        var oneapp = {//["66", "0508", "园所信息", "../images/function/yeyinfo.png", "", "html/yeyInfo.html", "0"]
                            mid: oneappdata.id,
                            name: oneappdata.menu_name,
                            image: oneappdata.icon,
                            url: oneappdata.path
                        };
                        /*<div class="desktop-app" mid="66" style="top: 0px; left: 0px;">
                                <img src="images/desktop/backgroud/icon_zaojiaoban.png">
                                <span class="desktop-title layui-elip">早教班管理</span>
                            </div>*/
                        stronepagehtml +=
                            '<div class="desktop-app" mid="' + mid + '">\
                                <img src="' + (oneapp.image ? oneapp.image.replace('../', '') : '') + '">\
                                <span class="desktop-title layui-elip">' + oneapp.name + '</span>\
                            </div>';
                    }
                    strtoptab +=
                        '<li' + (i == curindex ? ' class="current"' : '') + (arrdeskdata[i].menukey ? ' data-menukey="' + arrdeskdata[i].menukey + '"' : '') + (arrdeskdata[i].menuid ? ' data-menuid="' + arrdeskdata[i].menuid + '"' : '') + '>\
                            <div class="desktop-cell"><img src="' + arrdeskdata[i].image.replace('../', '') + '"></div>\
                            <div class="text"><span class="spanname">' + arrdeskdata[i].name + '</span></div>\
                        </li>';
                    strhtml +=
                        '<div class="swiper-slide">\
                            <div class="desktopContainer" data-menuid="m001" data-name="1">\
                            ' + stronepagehtml + '\
						</div>\
					</div>';
                }
                $(".swiper-wrapper").html(strhtml);
                _this.deskswiper = new Swiper(".swiper-container", {
                    mousewheel: true
                    , observer: true
                    , observeParents: true
                    // , effect:'cube'
                    , on: {
                        slideChangeTransitionEnd: function () {
                            $("#ultopnums").children().eq(this.activeIndex).addClass('current').siblings().removeClass('current');
                            desktop.arrange()
                        }
                    }
                })
                $("#ultopnums").html(strtoptab).children().click(function () {//点击顶部导航
                    _this.deskswiper.slideTo($(this).index());
                    $.sm(function (re) {

                    }, ['desktop.savedeskindex', $(this).index()])
                });
                /*layer.tips('快捷切换菜单', '#ultopnums',{
                     tips:3
                 });*/
                _this.deskswiper.slideTo(curindex, 0);
                _this.arrange();
                _this.resizeInit();
                _this.contextmenuInit();
                if (false) {//暂时隐藏
                    //登录socket后初始化
                    window.socket = io.connect(socketaddress);
                    $.sm(function (re, err) {
                        if (re && re.length > 0) {
                            socket.emit("login", {
                                uid: objdata.my.id,
                                pwd: re[0][0] || Arg('pwd') || '123456',
                                yid: objdata.yeyinfo.id,
                                ytype: objdata.my.roletype,
                                type: 2
                            }, function (issucc, re, ids) {//re 基本数据 和 ids（在线id）
                                if (issucc) {// 登录成功
                                    document.title = re.truename + " 已登录";
                                    socket.islogin = 1;
                                    objdata.onlineids = ids;
                                    desktop.initSocket();
                                }
                            });
                        } else {
                            alert('即时通登录失败:没有随机密码');
                        }
                    }, ["index.impwd", objdata.my.id]);
                    //读取未读总数目
                    $.sm(function (arr) {
                        if (arr && arr.length && arr[0].sum) {
                            $("#unreadmessnum").text(arr[0].sum).show();
                        }
                    }, ['index.homemsg.getunreadnum'])
                    //打开即时通
                    $("#btnopenim").on('click', function () {
                        desktop.hidemenu();
                        var toopen = false;
                        if (!$(this).data("hasinit")) {//没有初始化
                            _this.loadIm();
                            layer.tips('正在初始化即时通', '#btnopenim', {
                                time: 1000
                            });
                            $(this).data("hasinit", true);
                            toopen = true;
                        } else {
                            if (!$(this).data("isopen")) {
                                layim.showLayimMain();
                                toopen = true;
                            } else {
                                layim.hideLayimMain();
                                toopen = false;
                            }
                        }
                        $(this).data("isopen", toopen);
                        $.sm(function () {

                        }, ["desktop.isimautostart", toopen ? 1 : 0])
                    });
                    objdata.objmy.isimautostart == "1" && $("#btnopenim").trigger('click');
                }
                $(".desktopContainer").off("click", ".desktop-app").on("click", ".desktop-app", function (){
                    $(this).addClass('desktop-app-select')
                }).off("dblclick", ".desktop-app").on("dblclick", ".desktop-app", function (){
                    desktop.openwin($(this));
                });
            }
        }, loadIm: function () {
            $.sm(function (regroup) {
                var objyear = {};//当前学期对象
                var now = objdata.curmoment._d;
                var year = now.getFullYear();
                var strtoday = getStrByDate(now);
                var objyears = {};
                for (var i = 0; i < regroup.length; i++) {
                    objyears[regroup[i].goupyear] = regroup[i].goupday;
                }
                for (var i = 0; i < regroup.length; i++) {
                    objyears = {};
                    if (regroup[i].goupday < strtoday) {
                        objyear.goupyear = regroup[i].goupyear;
                        break;
                    }
                }
                if (objyears[objyear.goupyear + 1]) {//下一年有升班时间
                    objyear.enddate = getpreday(objyears[objyear.goupyear + 1]);
                } else {
                    if (strtoday > year + "-09-01") {
                        objyear.enddate = strtoday;
                    } else {
                        objyear.enddate = year + "-09-01";
                    }
                }

                $.sm(function (re2, err) {
                    objdata.arrclass = [];
                    for (var i = 0; i < re2.length; i++) {
                        objdata.arrclass.push({
                            "claname": re2[i].claname,
                            "classno": re2[i].classno,
                            "stucount": re2[i].stucount
                        })
                    }
                }, ['index.getYeyClassAndStuNum', strtoday, objyear.enddate, objdata.strorder])
            }, ['tongpublic.getgouptime'])//, ["tong.system", ""]
            $.sm(function (re, err) {
                if (re && re.length > 0) {
                    var arrbabyid = [];
                    for (var i = 0; i < re.length; i++) {
                        arrbabyid.push(re[i][3]);
                    }
                    //config.type 'class' 'stu'   config.classno   config.arrstuno
                    //获取宝宝信息
                    getBabyInfo({type: 'stu', arrstuno: arrbabyid}, function (re1, re2) {
                        for (var i = 0; i < re.length; i++) {
                            //r.id,r.talktype,r.fromtype,r.fromid,m.fromname,m.ptype,m.msgtype,m.msgcontent,m.creatime, num  ,photopath
                            var objstu = objdata.babys[re[i][3]];
                            var imgsrc = 'images/defaultbaby.png';
                            var type = "one";
                            var objmsgcontent = {};
                            try {
                                objmsgcontent = JSON.parse(re[i][7]);
                            } catch (e) {

                            }
                            /*var msgsummry = "";
                            if (re[i][6] == 1) {
                                var obj = JSON.parse(re[i][7]);
                                msgsummry = obj.text;
                            } else if (re[i][6] == 2) {
                                msgsummry = "[图片]";
                            } else if (re[i][6] == 3) {
                                msgsummry = "[视频]";
                            } else if (re[i][6] == 11) {
                                msgsummry = "[请假]";
                            } else if (re[i][6] == 12) {
                                msgsummry = "[取消请假]";
                            } else if (re[i][6] == 15 || re[i][6] == 16 || re[i][6] == 17 || re[i][6] == 18 || re[i][6] == 19) {
                                msgsummry = "[喂药]";
                            }*/
                            var name = "";
                            var strname = "";
                            if (re[i][3] && objstu) {
                                name = objdata.babys[re[i][3]].name;
                            }
                            if (!name) continue;
                            if (!re[i][1] || re[i][1] == '1') {//老师和家长沟通
                                imgsrc = re[i][10] ? objdata.ossPrefix + re[i][10] : 'images/defaultbaby.png';
                                type = 'baby';
                                /*var ptype = re[i][5];
                                //家长说的
                                if (re[i][2] == 1 && ptype) {
                                    if (ptype == 1) {
                                        strname = '[' + name + '爸爸]';
                                    } else if (ptype == 2) {
                                        strname = '[' + name + '妈妈]';
                                    } else {
                                        strname = '[' + name + '家长]';
                                    }
                                } else {//老师说的
                                    strname = '[' + re[i][4] + ']';
                                }*/
                            } else if (re[i][1] == '2') {//老师和老师沟通
                                if (objdata.users && objdata.users[re[i][1]]) {
                                    imgsrc = objdata.users[re[i][1]][2];
                                    type = "one";
                                }
                            }
                            /*var obj = $(wim.dorecenthtml(objdata.yid, re[i][3], imgsrc, re[i][8], name, re[i][6], msgsummry, re[i][9], false, type, ptype));
                            wim.node.chatul.append(obj);
                            obj.time = re[i][5];
                            objdata.chatarrs.push(obj);*/
                            var arrweixin = objstu.arrweixin || [];
                            arrweixin = arrweixin.sort(function (a, b) {
                                return a.ptype > b.ptype ? 1 : a.ptype == b.ptype ? 0 : -1;
                            })
                            objdata.arrhistroy.unshift({
                                type: 'friend',
                                id: re[i][3],
                                fromtype: re[i][2],//1 家长 2 老师
                                username: re[i][4],
                                ptype: re[i][5],
                                avatar: imgsrc,
                                name: name,//宝宝名称
                                strname: strname,//宝宝爸爸   某某老师
                                arrweixin: arrweixin,
                                msgtype: re[i][6],
                                objmsgcontent: objmsgcontent,
                                unreadnum: re[i][9]
                            });
                        }

                        //wim.freshbottomnum(objdata.noread);
                    });
                }
                _this.imInit();

            }, ["index.recent"]);
        }, initSocket: function () {
            //初始化socket
            socket.on("msg", function (data, fn) {
                if (fn) {
                    if (data.t == "smarttablefile") {
                        var json = {};
                        json.fileid = data.fileid;
                        json.index = data.index;
                        json.num = data.num;
                        json.uid = objdata.arrmy[0];
                        json.yid = objdata.yid;
                        var win = $("#win_212");
                        if (win.length > 0) {
                            var iframe = win.find('iframe')[0].contentWindow;
                            if (iframe && iframe.isopen) {
                                var isopen = iframe.isopen(data.fileid);
                                json.isopend = isopen;
                                fn(json);
                            }
                        } else {
                            json.isopend = false;
                            fn(json);
                        }
                    } else {
                        fn(true); // 消息接收成功
                    }
                }
                if (data.t == 'talk') {
                    if (data.type == 'tb_parenttalk') {
                        if (!window.layim) {
                            return;
                        }
                        var ptype = data.msg.ptype;// 如果有ptype  代表是家长发来的 没有 代表是园所发来的
                        var name = "";
                        var face = "";
                        if (!objdata.babys[data.msg.stuno]) {
                            getBabyInfo({type: 'stu', arrstuno: [data.msg.stuno]});
                        }
                        var stuname = objdata.babys[data.msg.stuno].name;
                        if (ptype == 1) {
                            name = stuname + "爸爸";
                            face = 'images/newtong/defaultbaba.png';
                        } else if (ptype == 2) {
                            name = stuname + "妈妈";
                            face = 'images/newtong/defaultmama.png';
                        } else {
                            name = stuname + "家长";
                            face = 'images/newtong/defaulthome.png';
                        }
                        layim.getMessage({
                            fromtype: 1
                            , name: stuname
                            , username: name
                            , avatar: face
                            , id: data.msg.stuno
                            , type: "friend"
                            , msgtype: data.msg.msgtype
                            , objmsgcontent: data.msg.data
                        });
                    }
                } else if (data.t == 'brozsy') {// 接收到群发通知
                    var lino = wim.node.ulfun.children('[type=notice]');
                    lino.children('.dot').show().html(lino.children('.dot').html() * 1 + 1);
                    lino.children('.text').html(data.msg.title);
                    var win = $("#win_46");
                    if (win.length > 0) {
                        var iframe = win.find('iframe')[0].contentWindow;
                        if (iframe && iframe.renotice) {
                            iframe.renotice(data.msg.id);
                        }
                    }
                } else if (data.t == 'askleavesocket') {// 请假通知
                    var obj = JSON.parse(data.msg);
                    var win = $("#win_10");
                    if (win.length > 0) {
                        var iframe = win.find('iframe')[0].contentWindow;
                        if (iframe && iframe.resetHtml) {
                            iframe.resetHtml(objdata.moment(obj.startdate), objdata.moment(obj.enddate));
                        }
                    }
                    if (objdata.layers.kqconfirmframe) {
                        parent.objdata.layers.kqconfirmframe.contentWindow.resetChild(obj);
                    }
                    if (obj.type == "1")
                        layer.msg('宝宝' + obj.stuname + '请假了，请假日期：' + obj.startdate + "至" + obj.enddate, {
                            icon: 5,
                            time: 2000
                        });
                    else {
                        layer.msg('宝宝' + obj.stuname + '取消了 ' + obj.startdate + "至" + obj.enddate + '的 请假', {
                            icon: 6,
                            time: 2000
                        });
                    }
                } else if (data.t == 'dianming' || data.t == 'homeshuaka') {// 点名通知 刷卡
                    var obj = data.msg;
                    if (typeof obj == 'string') {
                        obj = JSON.parse(obj);
                    }
                    if (data.t == 'homeshuaka') {//刷卡时 开始和结束时间一直
                        obj.startdate = obj.date;
                        obj.enddate = obj.date;
                    }
                    var win = $("#win_10");
                    if (win.length > 0) {
                        var iframe = win.find('iframe')[0].contentWindow;
                        if (iframe && iframe.resetHtml) {
                            iframe.resetHtml(objdata.moment(obj.startdate), objdata.moment(obj.enddate));
                        }
                    }
                    if (objdata.layers.kqconfirmframe) {
                        parent.objdata.layers.kqconfirmframe.contentWindow.resetChild(obj);
                    }
                    //				if(obj.type == "1")
                    //					layer.msg('宝宝' + obj.stuname + '请假了，请假日期：' + obj.startdate + "至" + obj.enddate, {icon:  5,time:2000});
                    //				else{
                    //					layer.msg('宝宝' + obj.stuname + '取消了 ' + obj.startdate + "至" + obj.enddate + '的 请假', {icon:  6,time:2000});
                    //				}
                } else if (data.t == 'huodongmsg') {
                    //事实通讯 活动祝福
                    var obj = data.msg;
                    var curHdId = "#win_huodong";
                    if ($(curHdId).css("display") === "block" && obj.hdid == objdata.curHdId) {
                        //	活动页地图样式在当前页展现，将消息动态展现在地图上
                        objdata.huodongList[objdata.curHdId].instantMessage(obj);
                    } else {
                        //活动页不是当前展现页，小弹窗祝福语
                        // layer.msg(obj.fromname + ": " + obj.msgcontent);
                    }
                } else if (data.t == "wxcmd") {
                    //微信语音命令
                    var obj = data.msg;
                    var t = obj.t;
                    var menu = obj.menu;
                    if (menu && objdata.objMenuName[menu]) {
                        if (t == "1") {//打开
                            openwinbyid(objdata.objMenuName[menu]);
                        } else if (t == "2") {//关闭
                            $("#win_" + objdata.objMenuName[menu] + " .btn-close").trigger("click");
                        }
                    }
                } else if (data.t == "chdcnotice") { //chdc通知公告
                    noticeread(data.msg.noticeid, data.msg.fromdepart, data.msg.title, data.msg.areacode);//noticeid,fromdepart,title
                    // layer.msg("TODO 通知id：" + data.msg.noticeid);
                    console.log("TODO 通知id：" + data.msg.noticeid);
                } else if (data.t == "enroll_notice") { //招生报名
                    var win = $("#win_68");
                    if (win.length > 0) {
                        var pframe = win.find('iframe')[0].contentWindow;
                        if (pframe) {
                            var iframe = pframe.$("#fmmap")[0].contentWindow;
                            if (iframe && iframe.dynamicData) {
                                iframe.dynamicData(data.msg);
                            }
                        }
                    }
                } else if (data.t == 'acountforbid') {
                    if (data.msg && data.msg.isforbid) {
                        $("#btnlogout").trigger('click');
                    }
                } else if (data.t == "smarttable") { //智能表格
                    var win = $("#win_212");
                    if (win.length > 0) {
                        var iframe = win.find('iframe')[0].contentWindow;
                        if (iframe && iframe.dynamicEditUsers) {
                            iframe.dynamicEditUsers(data.reqUsers, data.isreload, data.type);
                        }
                    }
                }
            });
        }, imInit: function () {
            //初始化im
            layui.use('layim', function (layim) {
                window.layim = layim;
                //基础配置
                layim.config({
                    //初始化接口
                    init: {
                        mine: {
                            "username": objdata.my.username
                            , "id": objdata.my.id
                            , "status": "online" //在线状态 online：在线、hide：隐身
                            , "remark": objdata.objmy.signature || ""
                            , "avatar": objdata.my.headimg ? objdata.my.headimg : "images/desktop/default.png"
                        }
                        , openindex: objdata.objmy.imlasttabindex//上次打开的页签
                        , friend: objdata.arrclass
                        , group: []
                        , history: objdata.arrhistroy
                    }, searchbaby: function (searchval, cb) {//获取班级学生
                        getBabyInfo({
                            type: 'search',
                            searchval: searchval
                        }, function (arrbind, arrstu) {
                            var arrstudent = [];
                            var commonptype = ["", "爸爸", "妈妈", "爷爷", "奶奶", "姥爷", "姥姥", "叔叔", "阿姨", "哥哥", "姐姐"];
                            for (var i = 0; i < arrstu.length; i++) {
                                var arrweixin = objdata.babys[arrstu[i][0]].arrweixin;
                                var arrptype = [];
                                if (arrweixin && arrweixin.length) {
                                    arrweixin = arrweixin.sort(function (a, b) {
                                        return a.ptype > b.ptype ? 1 : a.ptype == b.ptype ? 0 : -1;
                                    })
                                    for (var j = 0; j < arrweixin.length; j++) {
                                        arrptype.push(commonptype[arrweixin[j].ptype]);
                                    }
                                }
                                arrstudent.push({
                                    "id": arrstu[i][0],
                                    arrweixin: arrweixin,
                                    "username": arrstu[i][1],
                                    "avatar": arrstu[i][7] ? ossPrefix + "/" + arrstu[i][7] + ossEqParam_50 : 'images/defaultbaby.png',
                                    "birday": arrstu[i][6],
                                    "isbirth": objdata.moment(arrstu[i][6]).format("MM-DD") == objdata.curmoment.format("MM-DD"),
                                    "summary": arrptype.join(",") ? arrptype.join(",") + "已绑定微信" : ""
                                });
                            }
                            cb(arrstudent);
                        });
                    }, getClassStu: function (classno, cb) {//获取班级学生
                        getBabyInfo({
                            type: 'class',
                            classno: classno
                        }, function (arrbind, arrstu) {
                            var arrstudent = [];
                            var commonptype = ["", "爸爸", "妈妈", "爷爷", "奶奶", "姥爷", "姥姥", "叔叔", "阿姨", "哥哥", "姐姐"];
                            for (var i = 0; i < arrstu.length; i++) {
                                var arrweixin = objdata.babys[arrstu[i][0]].arrweixin;
                                var arrptype = [];
                                if (arrweixin && arrweixin.length) {
                                    arrweixin = arrweixin.sort(function (a, b) {
                                        return a.ptype > b.ptype ? 1 : a.ptype == b.ptype ? 0 : -1;
                                    })
                                    for (var j = 0; j < arrweixin.length; j++) {
                                        arrptype.push(commonptype[arrweixin[j].ptype]);
                                    }
                                }
                                arrstudent.push({
                                    "id": arrstu[i][0],
                                    arrweixin: arrweixin,
                                    "username": arrstu[i][1],
                                    "avatar": arrstu[i][7] ? ossPrefix + "/" + arrstu[i][7] + ossEqParam_50 : 'images/defaultbaby.png',
                                    "birday": arrstu[i][6],
                                    "isbirth": objdata.moment(arrstu[i][6]).format("MM-DD") == objdata.curmoment.format("MM-DD"),
                                    "summary": arrptype.join(",") ? arrptype.join(",") + "已绑定微信" : ""
                                });
                            }
                            cb(arrstudent);
                        });
                    }, onFirstOpenChat: function (objuser, cb) {//读取聊天记录并设置已读
                        var num = objuser.elem.find('.unreadnum').text() * 1;
                        objuser.elem.find('.unreadnum').text(0).hide()
                        var allnum = $("#unreadmessnum").text() * 1 - num;
                        if (allnum) {
                            $("#unreadmessnum").text(allnum);
                        } else {
                            $("#unreadmessnum").text(allnum).hide();
                        }
                        if (objuser.type == 'friend') {
                            //1.设置已读
                            $.sm(function (re, err) {
                                if (err) {

                                }
                            }, ["index.recentlast", objuser.id, objdata.my.id]);
                            //2.读取聊天记录
                            $.sm(function (re, err) {
                                var arr = [];
                                if (re && re.length) {
                                    for (var i = 0; i < re.length; i++) {
                                        //fromtype,fromid,ptype,msgtype,msgcontent,creatime,fromnamevar
                                        var objbabyinfo = objdata.babys[objuser.id];
                                        var objc = JSON.parse(re[i][4]);
                                        var obj = {
                                            name: re[i][6],
                                            isyey: false,//决定左边还是右边显示
                                            msgtype: re[i][3],
                                            objmsgcontent: objc,
                                            timestamp: re[i][5]
                                        }
                                        if (re[i][0] == '1') {
                                            obj.ptype = re[i][2];
                                            if (obj.ptype == 1) {
                                                obj.name = objbabyinfo.name + "爸爸";
                                                obj.avatar = 'images/newtong/defaultbaba.png';
                                            } else if (obj.ptype == 2) {
                                                obj.name = objbabyinfo.name + "妈妈";
                                                obj.avatar = 'images/newtong/defaultmama.png';
                                            } else {
                                                obj.name = objbabyinfo.name + "家长";
                                                obj.avatar = 'images/newtong/defaultbaba.png';
                                            }
                                        } else {
                                            obj.isyey = true;
                                            if (re[i][1] == objdata.my.id) {
                                                obj.name = objdata.my.username;
                                                obj.mine = true;
                                                obj.avatar = objdata.my.headimg ? ossPrefix + "/" + objdata.my.headimg : "images/desktop/default.png";
                                            } else {
                                                obj.mine = true;
                                                obj.name = obj.name;
                                                obj.avatar = 'images/default.png';
                                            }
                                        }
                                        arr.unshift(obj)
                                    }
                                }
                                cb && cb(arr);
                            }, ["index.readparenttalk", objuser.id]);
                        }
                    }, getYeyUserRole: function (cb) {
                        $.sm(function (re) {
                            if (re && re.length) {
                                cb(re);
                            }
                        }, ['index.im.getRoleAndNum'])
                    }, getRoleUser: function (roleid, cb) {
                        $.sm(function (re) {
                            if (re && re.length) {
                                cb(re);
                            }
                        }, ['index.im.getRoleUser', roleid])
                    }
                    , uploadImage: true
                    //,brief: true //是否简约模式（若开启则不显示主面板）
                    //,title: 'WebIM' //自定义主面板最小化时的标题
                    //,right: '100px' //主面板相对浏览器右侧距离
                    , bottom: 40//主面板相对浏览器底部距离 郭玉峰加
                    //,minRight: '90px' //聊天面板最小化时相对浏览器右侧距离
                    , initSkin: '5.jpg' //1-5 设置初始背景
                    //,skin: ['aaa.jpg'] //新增皮肤
                    , isfriend: true //是否开启好友
                    , isgroup: true //是否开启群组
                    //,min: true //是否始终最小化主面板，默认false
                    , notice: true //是否开启桌面消息提醒，默认false
                    //,voice: false //声音提醒，默认开启，声音文件为：default.mp3

                    , msgbox: 'css/modules/layim/html/msgbox.html' //消息盒子页面地址，若不开启，剔除该项即可
                    , find: layui.cache.dir + 'css/modules/layim/html/find.html' //发现页面地址，若不开启，剔除该项即可
                    , chatLog: 'desktop/desktopimrecord.html' //聊天记录页面地址，若不开启，剔除该项即可

                });

                /*layim.chat({
                  name: '在线客服-小苍'
                  ,type: 'kefu'
                  ,avatar: 'http://tva3.sinaimg.cn/crop.0.0.180.180.180/7f5f6861jw1e8qgp5bmzyj2050050aa8.jpg'
                  ,id: -1
                });
                layim.chat({
                  name: '在线客服-心心'
                  ,type: 'kefu'
                  ,avatar: 'http://tva1.sinaimg.cn/crop.219.144.555.555.180/0068iARejw8esk724mra6j30rs0rstap.jpg'
                  ,id: -2
                });
                layim.setChatMin();*/

                //监听在线状态的切换事件
                layim.on('online', function (data) {
                    //console.log(data);
                });

                //监听签名修改
                layim.on('sign', function (value) {
                    $.sm(function (re) {

                    }, ['desktop.im.upsignature', objdata.my.id, value])
                });
                //监听签名修改
                layim.on('layimMainClose', function () {
                    $.sm(function () {

                    }, ["desktop.isimautostart", 0])
                });


                //监听layim建立就绪
                /* setTimeout(function () {
                     //接受消息（如果检测到该socket）
                     layim.getMessage({
                         username: "朱声融爸爸"
                         , avatar: "http://tp1.sinaimg.cn/1571889140/180/40030060651/1"
                         , id: "2343"
                         , type: "friend"
                         , msgtype: 1
                         , objmsgcontent: {
                             text: "嗨，你好！欢迎体验LayIM。演示标记：" + new Date().getTime()
                         }
                     });
                 }, 1000);*/

                //监听发送消息
                layim.on('sendMessage', function (data) {
                    console.log(data)
                    if (objdata.my.role == "1") {
                        return alert("管理员不能发送消息");
                    }
                    var data = {
                        fromtype: objdata.my.role,
                        teacherid: objdata.my.id,
                        fromname: objdata.my.username,
                        fromopenid: objdata.myweixin ? objdata.myweixin.openid : "",
                        stuno: data.to.id,
                        msgtype: data.mine.msgtype,
                        data: data.mine.objmsgcontent
                    };
                    $.sm(function (re, err, bdata) {
                        if (err) {
                            alert(err);
                        }
                    }, ["index.sendTowx", "wxhome", "talkmsg", JSON.stringify(data)]);
                });

                layim.on('chatClose', function (res) {
                    $("#btncurimchat").hide();
                });
                layim.on('chatMin', function (res) {
                    $("#btncurimchat").removeClass('active');
                });
                //监听聊天窗口的切换
                layim.on('chatChange', function (res) {
                    objdata.chatIndex = res.chatIndex;
                    $("#btncurimchat").addClass('active');
                    $("#btncurimchat").show();
                    $("#btncurimchat").children('img').prop('src', res.data.avatar);
                    $("#btncurimchat").children('span').html(res.data.name);
                });
            });
        }, changechatshow: function (othis) {
            if (othis.hasClass('active')) {
                othis.removeClass('active')
                layim.setChatMin();
            } else {
                othis.addClass('active')
                layim.setChatShow();
            }
        }, maxApp: function () {
            var a = $(".desktop-taskbar").width() - 160, c = $(".desktop-taskbar-app-list").width();
            return 34 < a - c ? !0 : !1
        }, openwin: function (_dom) {
            var mid = _dom.attr('mid');
            if (_dom.data("ismenu") == 'no') {
                var _appdata = {
                    mid: mid,
                    title: "",
                    src: ""
                };
                _appdata.title = _dom.data("title")
                _appdata.src = _dom.data("src")
                _this.openwinbyobj(_appdata);
            } else {
                _this.openwinbymid(mid);
            }
        }, openwinbymid: function (mid, objparam) {
            var curmenu = objdata.objMenu[mid];//["97", "1504", "线路管理", "../images/function/routemanage.png", "", "tongbus/routemanage.html", "0"]
            //记录打开菜单操作
            if (curmenu) {
                $.sm(function () {
                }, ['index.addmenuvisite', mid, curmenu.menu_name]);
                var obj = {
                    mid: mid,
                    title: curmenu.menu_name,
                    src: curmenu.path,
                    micon: curmenu.icon
                };
                _this.openwinbyobj(obj, objparam);
            }
        }, openwinbyobj: function (_appdata, objparam) {
            _appdata.src = _appdata.src + (_appdata.src.indexOf('?') > -1 ? "&" : "?") + "v=" + (version || 1) + "&mid=" + _appdata.mid;
            if (objparam) {
                for (var key in objparam) {
                    _appdata.src += "&" + key + "=" + objparam[key];
                }
            }
            //隐藏右键菜单 防止不准确
            _this.hidemenu();
            if (!objdata.menulock) {
                $("#divdeskleft").hide()
            }

            var hasopened = false;
            var winindex = -1;
            var mid = _appdata.mid;
            if (mid != "12") {
                $(".btnscore").hide();
            } else {
                $(".btnscore").show();
            }
            var _onetaskbarapp = null;
            $(".taskbar-app").each(function (c, onetaskbarapp) {
                if (!hasopened && $(onetaskbarapp).attr("mid") == mid) {
                    //$(onetaskbarapp).click();
                    _onetaskbarapp = $(onetaskbarapp);
                    hasopened = true;
                    winindex = $(onetaskbarapp).attr("winindex");
                }
            });
            if (hasopened) {//已经打开  则置顶
                _onetaskbarapp.addClass("taskbar-app-on").siblings().removeClass("taskbar-app-on");
                $("#layui-layer" + winindex).show();
                layer.zIndex = parseInt(layer.zIndex + 1);
                layer.index++;
                console.log(layer.zIndex)
                layer.style(winindex, {
                    width: $("#divdesk").width() + 'px',
                    left: ($(window).width() - $("#divdesk").width()) + 'px',
                    zIndex: layer.zIndex
                })
                //置顶左侧菜单 防止挡上  设置很高  新的弹框有问题
                $("#divdeskleft").css('zIndex', layer.zIndex);
                return;
            }
            var windomid;

            var objlayerparam = {
                type: 2,
                title: [(_appdata.micon && false ? '<img class="layui-layer-title-menuimg" onerror="this.style.display=\'none\'" src="' + (_appdata.micon.replace('/menu/', '/menu2/')) + '">' : '') + _appdata.title, 'background-color:' + objtheme[objdata.curtheme] + ';text-align:center;color:#34495E;background-color: #fff;'],
                id: "win_" + mid,
                skin: "layerdesktopapp",
                content: _appdata.src,
                offset: "rt",
                anim: 5,
                isOutAnim: false,
                move: false,
                shade: false,
                area: [($("#divdesk").width()) + 'px', $("#divdesk").height() + 'px'],//
                success: function (layero, index) {
                    if (_appdata.title == '智能配餐') {
                        $(".btnscore").show();
                    }
                    // if ($("#btncurimchat").css('display') != 'none' && $("#btncurimchat").hasClass('active') && objdata.chatIndex) {
                    //     layer.zIndex = layer.zIndex + 1;
                    //     layer.style(objdata.chatIndex, {
                    //         zIndex: layer.zIndex
                    //     })
                    // }
                    if (layero.attr('hasload')) return;//防止reload的时候有问题
                    layero.attr('hasload', true);
                    var _min = $('<a class="layui-layer-min" href="javascript:;"><cite></cite></a>');
                    var _refreash = $('<a title="刷新" class="layui-layer-refreash" href="javascript:;" style="margin: -6px 5px 0 10px;"><i class="iconfont icon_refresh1"></i></a>');
                    var _daohang = $('<a title="快速导航" class="layui-layer-daohang" style="margin: -3px 0 0 10px;" href="javascript:;"><i class="iconfont icon_navigation"></i></a>');
                    var _opttaskbar = $('<a class="layui-layer-opttaskbar" style="width: 60px;color: #ffffff;margin-top: -4px;' + ($("#taskbar").css('display') == 'none' ? '' : 'display: none') + '" href="javascript:;">显示任务栏</a>');
                    layero.find(".layui-layer-close").addClass('layui-layer-ico2');
                    layero.find(".layui-layer-setwin").prepend(_min)
                    layero.find(".layui-layer-setwin").prepend(_refreash)
                    layero.find(".layui-layer-setwin").prepend(_daohang)
                    layero.find(".layui-layer-setwin").prepend(_opttaskbar)
                    _opttaskbar.click(function () {
                        _this.showtaskbar();
                    })
                    _daohang.click(function () {
                        _this.fastdaohang($(this), mid);
                    })
                    _refreash.click(function () {
                        layero.find('iframe')[0].contentWindow.location.reload();
                    })
                    _min.click(function () {
                        $("#layui-layer" + index).hide();
                        $("#" + windomid).removeClass("taskbar-app-on");
                        var arrindex = [];
                        $(document).find(".layui-layer-iframe:visible").each(function (a, c) {
                            arrindex.push($(c).css("z-index"))
                        });
                        if (arrindex.length < 1) {
                            $(".btnscore").hide();
                            return;
                        }
                        var maxzindex = arrindex.sort().pop();
                        $(document).find(".layui-layer-iframe:visible").each(function (index, elem) {
                            if ($(elem).css("z-index") == maxzindex) {
                                if ($("#taskbar-" + $(elem).attr("id")).attr("mid") != "12") {
                                    $(".btnscore").hide();
                                } else {
                                    $(".btnscore").show();
                                }
                                return $("#taskbar-" + $(elem).attr("id")).addClass("taskbar-app-on"), !1
                            }
                        });
                        return !1
                    })
                    $(window).resize(function () {
                        function resizewin() {
                            var deskwidth = $("#divdesk").width();
                            layer.style(index, {
                                width: deskwidth + 'px',
                                height: $("#divdesk").height() + 'px',
                                left: ($(window).width() - deskwidth) + 'px',
                                right: 0
                            });
                        }

                        resizewin();
                        setTimeout(function () {
                            resizewin();
                        }, 1000)
                    })
                }, cancel: function(index, layero){
                    var win = layero.find('iframe')[0].contentWindow;
                    if(win && win.beforeWinClose){
                        win.beforeWinClose && win.beforeWinClose(index);
                        return false;
                    }
                }, end: function () {
                    $("#" + windomid).remove();
                    if (mid == "12") {
                        $(".btnscore").hide();
                    }
                }
            };
            var winindex = layer.open(objlayerparam);
            //置顶左侧菜单 防止挡上  设置很高  新的弹框有问题
            $("#divdeskleft").css('zIndex', layer.zIndex);
            windomid = "taskbar-layui-layer" + winindex;
            var strhide = $(".taskbar-app span.desktop-title").hasClass("layui-hide") ? "layui-hide" : "";
            var strtasktar =
                _appdata.isicon ?
                    ('<div class="layui-inline layui-elip taskbar-app taskbar-app-on" winindex="' + winindex + '" mid="' + mid + '" id="' + windomid + '"><i class="layui-icon" style="background-color:' + _appdata.iconbg + '">' + _appdata.icon + '</i><span class="desktop-title layui-elip ' + strhide + '">' + _appdata.title + "</span></div>")
                    :
                    ('<div class="layui-inline layui-elip taskbar-app taskbar-app-on" winindex="' + winindex + '" mid="' + mid + '" id="' + windomid + '"><span class="desktop-title layui-elip ' + strhide + '">' + _appdata.title + '</span></div>');
            if (!$("#" + windomid).is(":visible")) {
                $(".desktop-taskbar-app-list").append(strtasktar);
                $("#" + windomid).on("click", function (e) {
                    var _this = $(this);
                    if (_this.hasClass("taskbar-app-on")) {
                        $("#layui-layer" + winindex).find(".layui-layer-setwin .layui-layer-min").click()
                    } else {
                        if (_this.attr("mid") != "12") {
                            $(".btnscore").hide();
                        } else {
                            $(".btnscore").show();
                        }

                        _this.addClass("taskbar-app-on").siblings().removeClass("taskbar-app-on");
                        $("#layui-layer" + winindex).show();
                        layer.zIndex = layer.zIndex + 1;
                        layer.index++;
                        //置顶左侧菜单 防止挡上  设置很高  新的弹框有问题
                        console.log(layer.zIndex);
                        var objconfig = {
                            width: $("#divdesk").width() + 'px',
                            left: ($(window).width() - $("#divdesk").width()) + 'px',
                            zIndex: layer.zIndex
                        };
                        layer.style(winindex, objconfig)
                        // layer.zIndex = layer.zIndex + 1;
                        $("#divdeskleft").css('zIndex', layer.zIndex);
                        // if ($("#btncurimchat").css('display') != 'none' && $("#btncurimchat").hasClass('active') && objdata.chatIndex) {
                        //     layer.zIndex = layer.zIndex + 1;
                        //     layer.style(objdata.chatIndex, {
                        //         zIndex: layer.zIndex
                        //     })
                        // }
                    }
                }).siblings().removeClass("taskbar-app-on")
            }
        }, sendtodesk: function () {
            desktop.hidemenu();
            _this.showdesktopicon();
            var menuid = _this.curContextmenumApp.attr("mid");
            var apps = $(".swiper-slide-active").find(".desktop-app");
            var has = false;
            for (var i = 0; i < apps.length; i++) {
                if (apps.eq(i).attr("mid") == menuid) {
                    has = true;
                    break;
                }
            }
            if (has) {
                layer.msg("快捷方式在桌面已经存在");
            } else {
                $(".swiper-slide-active").children('.desktopContainer').append('<div class="desktop-app" mid="' + menuid + '" style="top: 140px; left: 0px;">' +
                    '<img src="' + _this.curContextmenumApp.find('img').prop('src') + '">' +
                    '<span class="desktop-title layui-elip">' + _this.curContextmenumApp.text() + '</span>' +
                    '</div>');
                _this.arrange();
                _this.savedesktopjson();
            }
        }, openrighttarget: function () {
            _this.openwin(_this.curContextmenumApp)
        }, deleterighttarget: function () {
            desktop.hidemenu();
            _this.curContextmenumApp.remove();
            _this.arrange()
            _this.savedesktopjson();
        }, savedesktopjson: function () {
            var arrjson = [];
            /*var objdeskdata = [{
                menuid: "1",
                image: "images/desktop/top/icon_baoyu.png",
                "name": "保育",
                arrmenuid: ["66", "28", "29", "27", "31", "35", "70"]
            }, {
                menuid: "2",
                image: "images/desktop/top/icon_baojiao.png",
                "name": "保教",
                arrmenuid: ["12", "13", "14", "17", "18", "49", "63", "77", "98", "112"]
            }, {
                menuid: "3",
                image: "images/desktop/top/icon_richang.png",
                "name": "日常",
                arrmenuid: ["20", "21", "24", "23", "22", "25", "48"]
            }, {
                menuid: "4",
                image: "images/desktop/top/icon_bangong.png",
                "name": "办公",
                arrmenuid: ["20", "21", "24", "23", "22", "25", "48"]
            }];*/
            $("#ultopnums").children().each(function () {
                var obj = {};
                var _this = $(this);
                if (_this.data('menukey'))
                    obj.menukey = _this.data('menukey');
                if (_this.data('menuid'))
                    obj.menuid = _this.data('menuid');
                obj.name = _this.find('.spanname').text();
                obj.image = _this.find('img').attr('src');//用prop会显示完整路径
                obj.arrmenuid = [];
                var _curdesk = $(".swiper-slide").eq(_this.index()).children('.desktopContainer');
                _curdesk.children().each(function () {
                    obj.arrmenuid.push($(this).attr('mid'));
                })
                arrjson.push(obj);
            })
            $.sm(function (re, err) {

            }, ['user.updatestrdeskdata', JSON.stringify(arrjson)])
        }
    };
    desktop.init();
    $("body").on("click", ".small-click", function () {
        var a = $(this), d = a.data("type");
        desktop[d] ? desktop[d].call(this, a) : ""
    }).on("dblclick", ".small-dblclick", function () {
        var a = $(this), d = a.data("type");
        desktop[d] ? desktop[d].call(this, a) : ""
    });
    $("#msgtotalnum").on('click', function () {
        getnoticenum(1);
    });
});

/*
 * 获取升班日期函数
 */
function initGoupTime(cb) {
    // 1 当前时间的学期的班级  2.当前日期升班后的班级 3.升班时间
    var arrsm = [['initclass.sbqclass', objdata.curmoment.format('YYYY-MM-DD')], ['initclass.sbhclass'], ['tongpublic.getgouptime']];
    // if (objdata.my.role == 2 || objdata.my.role == 7) {//获取教师班级信息
    //     arrsm.push(['tong.teachclass', objdata.my.id, objdata.curmoment.format('YYYY-MM-DD')]);
    // }
    $.sm(function (re, err) {
        if (err) {
            jQuery.getparent().layer.msg(err, {icon: 5});
        } else if (re) {
            objdata.reclassandgroup = re;
            cb && cb(re);
        }
    }, arrsm);
}

/**
 * 专案新增提醒弹框
 */
function getcaseremind() {
    var objwhere = {};
    objdata.istuoyou = istuoyou();
    if (objdata.istuoyou) {
        objwhere.yeyid = [];
    } else if (iserbao() && objdata.my.roletype !== "e") {//非区县用户
        objwhere.fuyouorganid = [objdata.my.organid || 0];
    } else {
        return;
    }
    $.sm(function (re, err) {
        if (err) {
        } else {
            if (re && re.length > 0) {
                var arrpm = [];
                var arrhtml = [];
                arrhtml.push('<div style="text-align: center;line-height: 30px;overflow-y: auto;padding:5px 10px;">');
                for (var i = 0; i < re.length; i++) {
                    //c.id,cc.name,yeyname,casenum,yyblnum,fpnum,tanemianum,content
                    var item = re[i];
                    if (objdata.istuoyu) {
                        arrhtml.push('<div>"' + item.name + '" 新增专案' + item.casenum + '个，贫血' + item.tanemianum + '个，营养不良' + item.yyblnum + '个，肥胖儿' + item.fpnum + '个，心里行为发育异常' + item.psychnum + '个</div>');
                    } else {
                        if (i == 0 || (i > 0 && item.checkid != re[i - 1].checkid)) {
                            arrhtml.push('<div style="text-align: left;font-weight: bold;padding-left: 10px;">' + item.name + '</div>');
                        }
                        arrhtml.push('<div style="text-align: left;padding-left:40px;">"' + item.yeyname + '" 新增专案' + item.casenum + '个，贫血' + item.tanemianum + '个，营养不良' + item.yyblnum + '个，肥胖儿' + item.fpnum + '个，心里行为发育异常' + item.psychnum + '个</div>');
                    }
                    arrpm.push(["caseremind.read", item.id]);
                }
                arrhtml.push('</div>');
                objdata.layers.caseremindIndex = layer.open({
                    type: 1,
                    title: ["专案新增提醒", 'background-color:' + objtheme[objdata.curtheme] + ';color:#ffffff'],
                    area: ['630px', '350px'],
                    // offset: 'rb',
                    shadeClose: false, // 点击遮罩关闭
                    btn: ["去查看", "关闭"],
                    content: arrhtml.join(''),
                    success: function (layero, index) {
                    },
                    yes: function (index, layero) { //或者使用btn1
                        $.sm(function (re1, err1) {
                            if (err1) {
                                console.log(err1);
                            } else {
                                //跳转至
                                var mid = 108;//高危儿管理
                                if (objdata.istuoyou) {
                                    mid = 74;//专案管理
                                }
                                desktop.openwinbymid(mid);
                                layer.close(index);
                            }
                        }, arrpm, null, null, null, null, 1);
                    }
                });
            }
        }
    }, ["caseremind.list", $.msgwhere(objwhere)]);
}

/**
 * 功能：获取通知数量，不同用户登录有不同的通知，
 * 儿保管理员：为园所加入通知，
 * 医院：为妇幼的通知公告
 * 园所：没有通知公告
 * 企业，外部体检机构目前没有消息
 */
function getnoticenum(isnfirst, cb) {
    var objwhere = {};
    if (istuoyou()) {
        objwhere.yeyid = [];
    } else if (iserbao()) {
        objwhere.organid = [objdata.my.organid];
    }
    objwhere.fuyouid = [objdata.my.fuyouid];
    var arrsm = [];
    arrsm.push(['desktop.getnoticenum', objdata.my.id, $.msgwhere(objwhere)]);//区县普通通知公告阅读情况，
    arrsm.push(['desktop.getnoticenumFromYpt', objdata.my.id, $.msgwhere(objwhere)]);//市级通知（普通通知、培训通知）阅读情况
    if (isnfirst) {
        arrsm.push(['desktop.getnoreadinfo', objdata.my.id, $.msgwhere(objwhere)]);//最近一次未读的通知公告的记录
        arrsm.push(['desktop.getnoreadinfoFromYpt', objdata.my.id, $.msgwhere(objwhere)]);//最近一次未读的培训通知的记录
    }
    if (arrsm.length > 0) {
        $.sm(function (re, error) {
            if (re) {
                var ptre = re[0], pxre = re[1];
                var noticenum = re[0] && re[0][0] && re[0][0].noticenum ||　0,//区县通知未读数
                    noticenumypt = re[1] && re[1][0] && re[1][0].noticenum || 0;//市级通知未读数

                var ptnoticenum = re[0] && re[0][0] && re[0][0].ptnoticenum ||　0, //普通通知未读数（区县的）
                    ptnoticenumypt = re[1] && re[1][0] && re[1][0].ptnoticenum || 0, //普通通知未读数（市级的）
                    pxnoticenumypt = re[1] && re[1][0] && re[1][0].pxnoticenum || 0;//培训通知未读数（市级的数据）
                if (noticenum > 0 || noticenumypt > 0) {
                    if (parseInt(noticenum) || parseInt(noticenumypt)) {
                        var notocinum = parseInt(noticenum) || parseInt(noticenumypt)
                        $("#msgtotalnum").show().html(parseInt(notocinum) || "");
                    } else {
                        $("#msgtotalnum").hide();
                    }
                    var ptnoticeiframeypt = $("#win_275").find("iframe")[0];//市级栏目（普通通知接收)
                    var pxnoticeiframeypt = $("#win_189").find("iframe")[0];//培训通知(市级)
                    var ptnoticeiframe = $("#win_168").find("iframe")[0];//普通通知接收区县

                    if (ptnoticeiframe) {//处理栏目接收通知未读个数的显示
                        var ptnoticepage = ptnoticeiframe.contentWindow;
                        var idx = ptnoticepage.$(".layui-tab-title").find("li.layui-this").index();
                        if (idx == 1 && ptnoticenum != ptnoticepage.$("#receivenum").html()) {
                            ptnoticepage.$(".layui-tab-title").find("li.layui-this").trigger("click");
                        }
                        if (ptnoticenum > 0) {
                            ptnoticepage.$("#receivenum").show().html(ptnoticenum);
                        } else {
                            ptnoticepage.$("#receivenum").hide();
                        }
                    }
                    if (ptnoticeiframeypt) {//处理栏目接收通知未读个数的显示
                        var ptnoticepage = ptnoticeiframeypt.contentWindow;
                        var idx = ptnoticepage.$(".layui-tab-title").find("li.layui-this").index();
                        if (idx == 1 && ptnoticenumypt != ptnoticepage.$("#receivenum").html()) {
                            ptnoticepage.$(".layui-tab-title").find("li.layui-this").trigger("click");
                        }
                        if (ptnoticenumypt > 0) {
                            ptnoticepage.$("#receivenum").show().html(ptnoticenumypt);
                        } else {
                            ptnoticepage.$("#receivenum").hide();
                        }
                    }
                    if (pxnoticeiframeypt) {
                        var pxnoticepage = pxnoticeiframeypt.contentWindow;
                        var idx = pxnoticepage.$(".layui-tab-title").find("li.layui-this").index();
                        if (idx == 1 && pxnoticenumypt != pxnoticepage.$("#receivenum").html()) {
                            pxnoticepage.$(".layui-tab-title").find("li.layui-this").trigger("click");
                        }
                        if (pxnoticenumypt > 0) {
                            pxnoticepage.$("#receivenum").show().html(pxnoticenumypt);
                        } else {
                            pxnoticepage.$("#receivenum").hide();
                        }
                    }
                    if (isnfirst) {//首次或右下角通知进入有未读通知弹框提示
                        var noticeid = re[2][0].noticeid;
                        var noticeidypt = re[3][0].noticeid;
                        var strcontent = '<div style="width: 100%;text-align: center;line-height: 30px;overflow-y: hidden;height:100%;"><div style="height:100%; display:flex;flex-direction: column;">';
                        if (istuoyou()) {//园所通知
                            if(noticenum > 0){
                                strcontent += '<div style="height: fit-content;top:40%;transform:translateY(-50%);position: relative;">您有' + noticenum + '条普通通知<button style="color: #4A90E2;margin-left: 20px;background: #fff !important; padding: 2px 10px;  border: 1px solid #4A90E2;cursor: pointer;"  id="btnchecknotice">点击查看</button></div>';
                            }
                            if(pxnoticenumypt > 0){
                                strcontent += '<div style="height: fit-content;top:40%;transform:translateY(-50%);position: relative;">您有' + pxnoticenumypt + '培训通知<button style="color: #4A90E2;margin-left: 20px;background: #fff !important; padding: 2px 10px;  border: 1px solid #4A90E2;cursor: pointer;"  id="btnchecknoticeypt">点击查看</button></div>';
                            }
                        } else {
                            strcontent += '<div style="height: fit-content;top:40%;transform:translateY(-50%);position: relative;">';
                            if (ptnoticenum > 0) {
                                strcontent += '<div style="">您有' + ptnoticenum + '条区县通知公告<button style="color: #4A90E2;margin-left: 20px;background: #fff !important;padding: 4px 10px;  border: 1px solid #4A90E2; cursor: pointer;"  id="btncheckptnotice">点击查看</button></div>';
                                // $("#msgtotalnum").attr("ptnoticenum", ptre[0].ptnoticenum);
                            }
                            if (ptnoticenumypt > 0) {
                                strcontent += '<div style="margin-top: 10px;">您有' + ptnoticenumypt + '条市级普通通知<button style="color: #4A90E2;margin-left: 20px;background: #fff !important;padding: 4px 10px;  border: 1px solid #4A90E2;cursor: pointer;" id="btncheckptnoticeypt">点击查看</button></div>';
                                // $("#msgtotalnum").attr("pxnoticenum", ptre[0].pxnoticenum);
                            }
                            if (pxnoticenumypt > 0) {
                                strcontent += '<div style="margin-top: 10px;">您有' + pxnoticenumypt + '条培训通知<button style="color: #4A90E2;margin-left: 20px;background: #fff !important;padding: 4px 10px;  border: 1px solid #4A90E2;cursor: pointer;" id="btncheckpxnotice">点击查看</button></div>';
                                // $("#msgtotalnum").attr("pxnoticenum", ptre[0].pxnoticenum);
                            }
                            strcontent += '</div>';
                        }
                        strcontent += '</div></div>';
                        objdata.layers.noticeinfo = layer.open({
                            type: 1,
                            title: ["消息通知", 'background-color:' + objtheme[objdata.curtheme] + ';color:#ffffff'],
                            area: ['480px', '300px'],
                            // offset: 'rb',
                            shadeClose: false, // 点击遮罩关闭
                            // btn: ["关闭"],
                            content: strcontent,
                            success: function (layero, index) {
                                // objdata.layers.noticeinfoiframe = layero.find('iframe')[0].contentWindow;
                            },
                            yes: function (index, layero) { //或者使用btn1
                                layer.close(index);
                            }
                        });
                        var objparam = {};
                        objparam.noticeid = noticeid;
                        objparam.fromtype = "remind";
                        $("#btncheckptnotice").off("click").on("click", function () {//区县普通通知
                            desktop.openwinbymid(168, objparam);
                            layer.close(objdata.layers.noticeinfo);
                        });
                        $("#btncheckptnoticeypt").off("click").on("click", function () {//市妇幼普通通知
                            objparam.noticeid = noticeidypt;
                            desktop.openwinbymid(275, objparam);
                            layer.close(objdata.layers.noticeinfo);
                        });
                        $("#btncheckpxnotice").off("click").on("click", function () {//妇幼培训通知
                            objparam.noticeid = noticeidypt;
                            objparam.type = 2;
                            desktop.openwinbymid(189, objparam);
                            layer.close(objdata.layers.noticeinfo);
                        });
                        $("#btnchecknotice").off("click").on("click", function () {//园所普通通知
                            objparam.type = 1;
                            desktop.openwinbymid(170, objparam);
                            layer.close(objdata.layers.noticeinfo);
                        });
                        $("#btnchecknoticeypt").off("click").on("click", function () {//园所培训通知
                            objparam.noticeid = noticeidypt;
                            objparam.type = 2;
                            desktop.openwinbymid(189, objparam);
                            layer.close(objdata.layers.noticeinfo);
                        });
                    }
                } else {
                    $("#msgtotalnum").hide().html("");
                }
                cb && cb();
            }
        }, arrsm);
    } else {
        $("#msgtotalnum").hide();
        $(".click-img").hide();
        cb && cb();
    }
}

/**
 * 功能：获取园所幼儿排序设置
 */
function getsysset(yeyid){
    $.sm(function (re, err) {
        if(err){
            // jQuery.getparent().layer.msg(err);
        }else if(re && re[0]) {
            objdata.stusortway = re[0].sortway;
            if(objdata.stusortway == 4){
                objdata.strorder = "classno,stusort";
            }else if(objdata.stusortway == 3){
                objdata.strorder = "birthday";
            }else{
                objdata.strorder = "sex desc, birthday";
            }
            objdata.strorder += ",stuno";
        }
    }, ["desktop.getstusortway", yeyid]);
}

/**
 * 功能：返回幼儿排序设置
 */
function getstusortway(stusortway){
    var strorder = "sex desc, birthday"
    if(stusortway == 4){
        strorder = "classno,stusort";
    }else if(stusortway == 3){
        strorder = "birthday";
    }else{
        strorder = "sex desc, birthday";
    }
    strorder += ",stuno";
    return strorder;
}

function getOrderWhere() {
    var ssort = objdata.stusortway;
    var objwhere = {};
    if (ssort == "3") {
        objwhere.order_birthday = [""];
    } else if (ssort == "4") {//自定义排序
        objwhere.order_stusort = [""];
    } else {
        objwhere.order_sexbirth = [""];
    }
    return (objwhere);
}


// 遍历菜单
window.getMenuHtml = function (menulists, ismergegroup) {
    let menuArr = menulists.filter(item => item.menu_type === 'M' || item.menu_type === 'G' || item.menu_type === 'C'); // 筛选条件;
    objdata.objMainPid = {};
    var objMenu = {}, objMenuName = {};//名称id对应关系
    for (var i = 0; i < menuArr.length; i++) {
        var item = menuArr[i];
        if (!objdata.objMainPid[item.pid]) {
            objdata.objMainPid[item.pid] = [];
        }
        objdata.objMainPid[item.pid].push(item);
        objMenu[item.id] = item;
        objMenuName[item.menu_name] = item.id;
    }
    objdata.objMenu = objMenu;
    objdata.objMenuName = objMenuName;
    let rootMenuItems = getMenuAll("0");
    let menuHtml = generateMainHtml(rootMenuItems, ismergegroup);
    return menuHtml;
};

//递归获取数据
window.getMenuAll = function (pid) {
    var arrnew = [];
    var arr = objdata.objMainPid[pid] || [];
    // 对子节点按照 tsort 的顺序进行排序
    arr.sort(function (a, b) {
        return a.tsort - b.tsort;
    });
    for (var i = 0; i < arr.length; i++) {
        var item = JSON.parse(JSON.stringify(arr[i]));
        var childrenList = getMenuAll(item.id);
        if (childrenList && childrenList.length) {
            item.isParent = true;
        } else {
            item.isParent = false;
        }
        item.children = childrenList;
        arrnew.push(item);
    }
    return arrnew;
};

//一级菜单
window.generateMainHtml = function (menuData, ismergegroup) {
    var menuHtml = '';
    if (ismergegroup) {
        menuHtml += '<ul class="menulist" data-type="erbao" style="overflow-y: auto;background: #ffffff;display: none">';
    }
    for (var i = 0; i < menuData.length; i++) {
        var menuItem = menuData[i];
        if (!ismergegroup) {
            menuHtml += '<ul class="menulist" data-type="' + menuItem.perms + '" style="overflow-y: auto;background: #ffffff;display: none">';
        }

        if (menuItem.isParent && menuItem.children && menuItem.children.length > 0) {
            menuHtml += generateMenurenHtml(menuItem.children);
        }

        if (!ismergegroup) {
            menuHtml += '</ul>';
        }
    }
    if (ismergegroup) {
        menuHtml += '</ul>';
    }
    return menuHtml;
};

//二级菜单
window.generateMenurenHtml = function (menuData) {
    var menuHtml = '';
    for (var i = 0; i < menuData.length; i++) {
        var menuItem = menuData[i];

        menuHtml += ('<li class="funList ' + (objdata.menustyle ? "listing" : "") + '" data-menuid="' + menuItem.id + '">');
        menuHtml += ('<h5><img src="' + (menuItem.icon ? menuItem.icon.replace('../', '') : '') + ' " style="width: 26px;height: 26px;">' + menuItem.menu_name + '<i class="iconfont icon_trian-right"></i></h5>');
        menuHtml += ('<ul style="display: none" class="ulsonmenu">');

        if (menuItem.isParent && menuItem.children && menuItem.children.length > 0) {
            menuHtml += generateChildrenHtml(menuItem.children);
        }

        menuHtml += ('</ul>');
        menuHtml += ('</li>');
    }
    return menuHtml;
};

//三级菜单
window.generateChildrenHtml = function (menuData) {
    var menuHtml = '';
    var hidemenu = "";
    for (var i = 0; i < menuData.length; i++) {
        var menuItem = menuData[i];

        menuHtml += ('<li mid="' + menuItem.id + '" src="' + menuItem.path + '" class="onmenuapp" style="' + hidemenu + '">');
        // menuHtml += ('<img src="images/uncoll_icon.png" class="coll_mark uncoll"><img  style="display: none" src="images/coll_icon.png" class="coll_mark coll">');
        menuHtml += ('<img src="' + (menuItem.icon ? menuItem.icon.replace('../', '') : '') + '">');
        menuHtml += ('<p mid="' + menuItem.id + '" class="menulan" zh="' + menuItem.menu_name + '" title="' + menuItem.menu_name + '">' + menuItem.menu_name + '</p>');
        menuHtml += ('</li>');

        if (menuItem.isParent && menuItem.children && menuItem.children.length > 0) {
            menuHtml += generateChildrenHtml(menuItem.children);
        }
    }
    menuHtml += '';
    return menuHtml;
};

function changeObjPost() {
    var arrpost = objdata.arrpost;
    var objpost = {};
    for (var i = 0; i < arrpost.length; i++) {
        objpost[arrpost[i][1]] = arrpost[i][0];
    }
    objdata.objpost = objpost;
}

function systemTitle() {
    $.sm(function (re, err) {
        if (re && re[0] && re[0][0]) {
            var title = re[0][0];
            // $("#log").find('img').hide();
            $("#yeyname1").html(title || "妇幼健康管理中心");
        } else {
            console.log("获取系统名称出错");
        }
    }, ["systemsetting.sysname", "sysname"]);
}

//打开窗口
//param参数可不传
function openwinbyid(id, param) {
    desktop.openwinbymid(id, param);
}

//上级通知右下角弹框
function noticeread(noticeid, fromdepart, title, areacode) {
    //弹框
    layer.open({
        type: 2,
        title: "上级通知",
        shadeClose: false,
        shade: 0,
        offset: 'rb',
        area: ['300px', '200px'],
        btn: ["查看详情"],
        content: 'tongall/noticeNoread.html?mid=' + Arg("mid") + '&v=' + (version || 1) + '&fromdepart=' + fromdepart + '&title=' + title,
        success: function (index1, layero1) {
            //layero1.find(".layui-layer-title").prop({textAlign:"center"})
            layer.find(".layui-layer-title").prop("textAlign", "center");
        },
        yes: function (index, layero) { //或者使用btn1
            openNotice(noticeid, '1', areacode);
            layer.close(index);
        }
    });
}

function getrv(cb) {
    $.sm(function (re, err) {
        if (re) {
            if (re[0][0] == '-1') {
                objdata.meetingrv = 31;
                objdata.noticerv = 7;
                myrv = -1;
            } else {
                eval("var oright={" + decodeURIComponent(re[0][0]) + "}");
                objdata.meetingrv = oright['43'];
                objdata.noticerv = oright['46'];
                myrv = oright;
            }
            cb && cb();
        }
    }, ['index.getmr', objdata.my.id]);
}

function getmyrv(mid) {
    if (myrv == '-1')
        return -1;
    if (myrv && myrv[mid]) {
        return myrv[mid];
    }
    return 0;
}

//保存常用栏目
function savefmids(cb) {
    var arrmid = [];
    $('.usualFun a').each(function () {
        var _a = $(this);
        var mid = _a.attr('mid');
        arrmid.push(mid);
    });
    $.sm(function (re) {
        if (re) {
            objdata.fmids = arrmid;//常用栏目更新
            cb();
        }
    }, ["index.fmids", arrmid.join(','), objdata.arrmy[0] || "0"]);
}

/**
 * 添加监控功能加载时间
 * @param mid
 * @param callback
 */
function addFuncTime(mid, callback) {
    // if (mid == "12") {//智能配餐
    //     $.sm(function (re, err) {
    //         callback && callback(re || "");
    //     }, ["w_functiontime.add", Logid || 0, mid]);
    // } else {
        callback && callback();
    // }
}

function changepagelan(lan) {
    var divs = objdata._divframes.children();
    divs.each(function () {
        var iframe = $(this).find('iframe');
        if (iframe.length > 0) {
            //var fun = iframe[0].contentWindow.changelan;
            //if (fun)
            //fun(lan, objdata.objentext, 0);
            iframe[0].contentWindow.location.reload();
        }
    });
}

Array.prototype.remove = function (val) {
    for (var i = 0; i < this.length; i++) {
        if (this[i] == val) {
            this.splice(i, 1);
            return i;
        }
    }
    return -1;
}

function changeURLPar(url, ref, value) {
    var str = "";
    if (url.indexOf('?') != -1)
        str = url.substr(url.indexOf('?') + 1);
    else
        return url + "?" + ref + "=" + value;
    var returnurl = "";
    var setparam = "";
    var arr;
    var modify = "0";
    if (str.indexOf('&') != -1) {
        arr = str.split('&');
        for (i in arr) {
            if (i == 'remove') {
                break;
            }
            if (arr[i].split('=')[0] == ref) {
                if (value == '+1') {
                    var oldv = arr[i].split('=')[1];
                    var intnewv = 0;
                    try {
                        setparam = oldv + '1';
                    } catch (e) {

                    }
                } else
                    setparam = value;
                modify = "1";
            } else {
                setparam = arr[i].split('=')[1];
            }
            returnurl = returnurl + arr[i].split('=')[0] + "="
                + setparam + "&";
        }
        returnurl = returnurl.substr(0, returnurl.length - 1);
        if (modify == "0")
            if (returnurl == str)
                returnurl = returnurl + "&" + ref + "=" + value;
    } else {
        if (str.indexOf('=') != -1) {
            arr = str.split('=');
            if (arr[0] == ref) {
                if (value == '+1') {
                    var oldv = arr[i].split('=')[1];
                    var intnewv = 0;
                    try {
                        setparam = parseInt(oldv) + 1;
                    } catch (e) {

                    }
                } else
                    setparam = value;
                modify = "1";
            } else {
                setparam = arr[1];
            }
            returnurl = arr[0] + "=" + setparam;
            if (modify == "0")
                if (returnurl == str)
                    returnurl = returnurl + "&" + ref + "=" + value;
        } else
            returnurl = ref + "=" + value;
    }
    return url.substr(0, url.indexOf('?')) + "?" + returnurl;
}

//在页面中改变常用栏目
function changefmid(id, obj) {
    var _this = obj;
    var issuccess = false;
    if ($(_this).hasClass('coll')) {//取消 _this.hasClass('icon13')
        var _a = $('.usualFun a[mid="' + id + '"]');
        if (_a.length > 0) {
            _a.remove();
            issuccess = true;
        }
    } else {
        if ($('.usualFun a[mid="' + id + '"]').length == 0) {
            var ref = objdata.objMenu[id];
            if (ref) {
                $('.usualFun').append('<a mid="' + ref.id + '" src="' + ref.path + '"><img style="display: none" src="../images/uncoll_icon.png" class="coll_mark uncoll"><img  src="../images/coll_icon.png" class="coll_mark coll"><img src="' + (ref.icon ? ref.icon.replace('../', '') : '') + '" alt=""/><u class="icon5"></u><p mid="' + ref.id + '" class="menulan" zh="' + ref.menu_name + '" title="' + ref.menu_name + '">' + ref.menu_name + '</p></a>');
                issuccess = true;
            }
        }
    }
    if (issuccess)
        savefmids(function () {
            // if (_this.hasClass('icon13')) {
            //     _this.removeClass('icon13').addClass("icon13-no");
            //     _this.prop('title', '设为常用栏目');
            // } else {
            //     _this.removeClass('icon13-no').addClass("icon13");
            //     _this.prop('title', '取消常用栏目');
            // }
        });
}

// 关闭窗口
function closewin(id, _this) {
    closeSave(id, function () {
        $(".btnscore").hide();
        var obj = objdata.wins[id];
        $("#task_" + id).remove();
        $("#inner_" + id).remove();
        //缩小面板移除
        $("#ulwinleft").find("[wid=" + id + "]").remove();
        if (_this)
            $(_this).parents('.win').remove();
        else {
            $("#win_" + id).remove();
        }
        // 窗口remove
        objdata.arrwin.remove(id);
        // 找到最顶部
        if (objdata.arrwin.length > 0) {
            if (objdata.arrwin[objdata.arrwin.length - 1] == objdata.pageids.peican) {
                $(".btnscore").show();
            }
            freshbottom(objdata.arrwin[objdata.arrwin.length - 1]);
            //$("#task_" + objdata.arrwin[objdata.arrwin.length - 1]).addClass('current');
        } else {
            $("#task_main").addClass('current');
        }
        delete objdata.wins[id];
    });
}

//刷新底部
function freshbottom(curid) {
    var html = [];
    for (var i = objdata.arrwin.length - 3; i < objdata.arrwin.length; i++) {
        var id = objdata.arrwin[i];
        var cobj = objdata.wins[id];
        if (id) {
            html.push('<a id="task_' + id + '" tid="' + id + '" ' + (id == curid ? 'class="current"' : '') + ' zh="' + cobj.zhtitle + '">' + cobj.title + '</a>');
        }
    }
    objdata._curlist.html(html.join(''));
}

// 重置窗口大小
function resize() {
    var wh = objdata.w.height();
    objdata._mainwrap.height(wh);
    objdata.size.frame = wh - 20 - objdata.size.title;
    objdata._divframes.children('div').children('iframe').height(objdata.size.frame);
    // 定位聊天窗口高度
    if (wh > objdata._imlayer.height()) {
        objdata._imlayer.css('top', wh / 2 - objdata._imlayer.height() / 2);
    } else {
        objdata._imlayer.css('top', 0);
    }
    $("#innerHistory").css('max-height', wh - 45);//关闭全部 滚动条
}

//栏目英文
function menutoen(cb) {
    $.sm(function (re, err) {//栏目英文
        if (re) {
            for (var i = 0; i < re.length; i++) {
                if (re[i][0]) {
                    var _this = $(".menulan[mid=" + re[i][0] + "]");
                    if (_this.length > 0)
                        _this.attr('zh', _this.html()).html(re[i][1]).prop('title', re[i][1]);
                    objdata.menuenobjs[re[i][0]] = re[i];
                }
            }
            objdata.menuenobjs['theme'] = ['', 'theme and background'];
            cb && cb();
            $("#curlist").children().each(function () {
                var _this = $(this);
                var id = _this.attr('tid');
                if (objdata.menuenobjs[id]) {
                    _this.attr('zh', _this.html()).html(objdata.menuenobjs[id][1]).prop('title', objdata.menuenobjs[id][1]);
                }
            });
            $(".wintitlelan").each(function () {
                var _this = $(this);
                var id = _this.attr('mid');
                if (objdata.menuenobjs[id]) {
                    _this.attr('zh', _this.html()).html(objdata.menuenobjs[id][1]).prop('title', objdata.menuenobjs[id][1]);
                }
            });
        }
    }, ["index.menulan", "en", objdata.menuids.join(",")]);
}

function menutozh() {
    var _list = $("#curlist");
    $(".menulan").each(function () {
        var _this = $(this);
        if (_this.attr('zh'))
            _this.html(_this.attr('zh'));
    });
    _list.children().each(function () {
        var _this = $(this);
        if (_this.attr('zh'))
            _this.html(_this.attr('zh'));
    });
    $(".wintitlelan").each(function () {
        var _this = $(this);
        if (_this.attr('zh'))
            _this.html(_this.attr('zh'));
    });
}

/**
 * @param 回调
 * @param codes 字典编号 string 或者 数组
 * @param type 类型 arr obj
 * @param lan 语言
 */
function getdic(cb, codes, lan, type) {
    lan = lan || 'zh';
    type = type || 'all';
    if (typeof codes == "string") {
        codes = [codes];
    }
    var uncodes = [];
    for (var i = 0; i < codes.length; i++) {
        if (!objdata.dics[codes[i]]) {
            uncodes.push(codes[i]);
        }
    }
    getdicdata(function (re) {//code,dname,typecode,lan
        if (re && re.length > 0) {
            for (var j = 0; j < re.length; j++) {
                if (objdata.dics[re[j][2]]) {//已经有
                    if (objdata.dics[re[j][2]][re[j][3]]) {
                        objdata.dics[re[j][2]][re[j][3]].push([re[j][0], re[j][1]]);
                    } else {
                        objdata.dics[re[j][2]][re[j][3]] = [[re[j][0], re[j][1]]];
                    }
                } else {
                    objdata.dics[re[j][2]] = {};
                    objdata.dics[re[j][2]][re[j][3]] = [[re[j][0], re[j][1]]];
                }
            }
        }
        var obj = {};
        for (var k = 0; k < codes.length; k++) {
            if (type == "arr") {
                obj[codes[k]] = objdata.dics[codes[k]][lan];
            } else if (type == 'obj') {
                var one = {};
                if (objdata.dics[codes[k]] && objdata.dics[codes[k]][lan]) {
                    var data = objdata.dics[codes[k]][lan];
                    for (var l = 0; l < data.length; l++) {
                        one[data[l][0]] = data[l][1];
                    }
                }
                obj[codes[k]] = one;
            } else {
                var one = {};
                if (objdata.dics[codes[k]] && objdata.dics[codes[k]][lan]) {
                    var data = objdata.dics[codes[k]][lan];
                    for (var l = 0; l < data.length; l++) {
                        one[data[l][0]] = data[l][1];
                    }
                }
                obj[codes[k]] = {
                    'arr': objdata.dics[codes[k]][lan],
                    'obj': one
                }
            }
        }
        cb(obj);
    }, uncodes);
}

function getdicdata(cb, arrcodes) {
    if (arrcodes) {
        $.sm(function (re, err) {
            if (re) {
                cb(re);
            } else {
                cb();
            }
        }, ["index.getdic", $.msgwhere({typecode: $.msgpJoin(arrcodes)})]);
    } else {
        cb();
    }
}

var jishuqi = 0;

function interfun() {
    //时间
    objdata.curmoment.add(1, 's');
    // objdata.headtime.html(objdata.curmoment.add(1, 's').format('YYYY-MM-DD HH:mm:ss'));

    var ms = objdata.curmoment._d.getTime() - objdata.loginmoment._d.getTime();
    var hours = parseInt(ms / 1000 / 60 / 60);
    var minute = parseInt((ms - (1000 * 60 * 60 * hours)) / (1000 * 60));
    if (hours > 0) {
        objdata._labellogintime.text(hours + "小时" + minute + "分钟")
    } else {
        if (minute > 0) {
            var secondes = parseInt((ms - (1000 * 60 * minute)) / (1000));
            objdata._labellogintime.text(minute + "分钟" + secondes + "秒")
        } else {
            var secondes = parseInt(ms / 1000);
            objdata._labellogintime.text(secondes + "秒")
        }
    }
    //会议时间
    if (objdata.markzonetime) {
        marktime();
    }
    jishuqi += 1;
    if (jishuqi == 30 * 60) {
        jishuqi = 0;
        $.sm(function (re, err) {
            if (re) {
                objdata.curmoment = objdata.moment(new Date(re[0], re[1] * 1 - 1, re[2], re[3], re[4], re[5]));
            }
        }, ['common.getdate']);
    }
}

function ftwo(num) {
    return ("" + num).length < 2 ? '0' + num : num;
}

//多时区时间
function marktime() {
    var html = [];
    for (c in objCountry) {
        var obj = objCountry[c];
        if (obj[2]) {
            var str = objdata.curmoment.tz(obj[2]).format('YYYY-MM-DD HH:mm:ss');
        } else {
            var str = objdata.curmoment.utcOffset(obj[3]).format('YYYY-MM-DD HH:mm:ss');
        }
        if (objdata.lan == 'en') {
            var strname = c;
        } else {
            var strname = obj[0];
        }
        html.push('<li><span class="timestr">' + str + '</span><span>' + strname + '</span></li>');
    }
    if (html.length) {
        objdata.markzonetime.html(html.join(''));
    }
}

function parentWindow() {
    return $('#win_' + (objdata.curfrmid)).children('iframe')[0].contentWindow;
}

function playsong(src) {
    var borswer = window.navigator.userAgent.toLowerCase();
    if (borswer.indexOf("ie") >= 0) {
        //IE内核浏览器
        var strEmbed = '<embed name="embedPlay" src="' + src + '" autostart="true" hidden="true" loop="false"></embed>';
        if ($("body").find("embed").length <= 0)
            $("body").append(strEmbed);
        else
            $("body").find("embed").attr('src', src);
        var embed = document.embedPlay;

        //浏览器不支持 audion，则使用 embed 播放
        embed.volume = 100;
        //embed.play();这个不需要
    } else {
        //非IE内核浏览器
        var strAudio = "<audio id='audioPlay' src='" + src + "' hidden='true'>";
        if ($("body").find("audio").length <= 0)
            $("body").append(strAudio);
        else
            $("body").find("audio").attr('src', src);
        var audio = document.getElementById("audioPlay");

        //浏览器支持 audion
        audio.play();
    }
}

function dynamicLoadJs(url, callback) {
    var head = document.getElementsByTagName('head')[0];
    var script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = url;
    if (typeof (callback) == 'function') {
        script.onload = script.onreadystatechange = function () {
            if (!this.readyState || this.readyState === "loaded" || this.readyState === "complete") {
                callback();
                script.onload = script.onreadystatechange = null;
            }
        };
    }
    head.appendChild(script);
}

function dynamicLoadCss(url) {
    var head = document.getElementsByTagName('head')[0];
    var link = document.createElement('link');
    link.type = 'text/css';
    link.rel = 'stylesheet';
    link.href = url;
    head.appendChild(link);
}

function openalterpwd() {
    desktop.openmyinfo(null, {alterpwd: 1});
}

/**
 * 获取地区名称
 * @param areacode
 * @return {string}
 */
function getAreaNamebyCode(areacode) {
    var name = "";
    if (areacode.length == 6) {
        name = objZhArea.getNameByCode(areacode);
    } else {
        for (var i = 0; i < arrarea.length; i++) {
            if (arrarea[i].areacode == areacode && arrarea[i].areaname) {
                name = arrarea[i].areaname;
                break;
            }
        }
    }
    return name;
}

function getarrarea(cb) {
    $.sm(function (res, err) {
        if (res) {
            arrarea = res;
            cb && cb();
        } else {
            layer.msg("读取区域信息出错");
        }
    }, ["index.getarrarea", objdata.my.areacode])
}

/**
 * 获取系统名称
 */
function getSysName(cb) {
    $.sm(function (re, err, obj) {
        if (err) {
        } else {
            var sysname = obj.fuyouname || parent.getAreaNamebyCode(objdata.my.areacode);
            $("#current_name").attr('title', sysname).text(sysname);
            objdata.my.fuyouid = obj.id;
            objdata.my.fuyouname = sysname;
        }
        cb && cb();
    }, ['index.getsysname']);
}

/**
 * 异步加载依赖的javascript文件
 * src：script的路径
 * callback：当外部的javascript文件被load的时候，执行的回调
 */
function load_js(src, callback) {
    var srcArray = src.split("?")[0].split("/");
    var scr_src = srcArray[srcArray.length - 1];

    // 判断要 添加的脚本是否存在如果存在则不继续添加了
    var scripts = document.getElementsByTagName("script");
    if (!!scripts && 0 != scripts.length) {
        for (var i = 0; i < scripts.length; i++) {
            if (-1 != scripts[i].src.indexOf(scr_src)) {
                callback();
                return true;
            }
        }
    }

    // 不存在需要的则添加
    var head = document.getElementsByTagName("head")[0];
    var script = document.createElement("script");
    script.setAttribute("type", "text/javascript");
    script.setAttribute("src", src);
    script.setAttribute("async", true);
    script.setAttribute("defer", true);
    head.appendChild(script);

    //fuck ie! duck type
    if (document.all) {
        script.onreadystatechange = function () {
            var state = this.readyState;
            if (state === "loaded" || state === "complete") {
                callback();
            }
        };
    } else {
        //firefox, chrome
        script.onload = function () {
            callback();
        };
    }
}

//保存uid impwd
function handleUId() {
    $.sm(function (re, err) {
        if (re && !err) {
            $.cookie("uid", objdata.my.id);
            $.cookie("impwd", re[0]);
        }
    }, ["login.getImPwd"])
}

//year 数组[2016,2017]
function getholidays(year, callback) {
    for (var i = 0; i < year.length; i++) {
        objdata.holidays[year[i]] = {};
        objdata.tbptdays[year[i]] = {};
    }
    // $.sm(function (res, err) {
    //     if (!err) {//hname,htype,startdate,enddate,remark
    //         var re = res && res[0] || [];
    //         for (var i = 0; i < re.length; i++) {
    //             var start = re[i][2];
    //             var end = re[i][3];
    //             while (DateCompare(end, start)) {
    //                 var obj = {
    //                     date: start,
    //                     type: re[i][1],
    //                     name: re[i][0]
    //                 };
    //                 objdata.holidays[re[i][5]][start] = obj;
    //                 objdata.tbptdays[re[i][5]][start] = obj;
    //                 start = getnextday(start, 1);
    //             }
    //         }
    //         //获取子表请假设置
    //         //			getholiday(year, callback);
    //     }
    //     callback && callback();
    // }, ["index.getholidays", 'getholidays', year.join('-')]);
    callback && callback();
}

//读取公告和邀请加入消息数目
//读取公告和邀请消息时可调用此方法
function messagenum(isTrigger) {
    var spnum = 0;
    var tznum = 0;
    $.sm(function (re, err) {
        if (re) {
            if (re[0][0]) {
                tznum = re[0][0];
                if (Number(tznum) > 99) {
                    $("#tznum").html("99+");
                } else {
                    $("#tznum").html(tznum);
                }
                $("#tznum").show();
            } else {
                $("#tznum").html("0").hide();
            }
            if (re[0][1]) {
                spnum = re[0][1];
                if (Number(spnum) > 99) {
                    $("#spnum").html("99+");
                } else {
                    $("#spnum").html(spnum);
                }

                $("#spnum").show();
            } else {
                $("#spnum").html("0").hide();
            }
            if (isTrigger) {
                if (tznum) {
                    $("#messFunNotice").trigger('click');
                } else if (spnum) {
                    $("#messFunJoin").trigger('click');
                }
            }
            totalRemindNum();
        }
    }, ["index.message.count", objdata.my.mobile]);
}

/**
 * 右上角总体的数目
 */
function totalRemindNum() {
    var tnum = 0;
    $("#sideNavContent").children().eq(0).find(".layui-badge").each(function () {
        tnum += $(this).text() ? $(this).text() * 1 : 0;
    });
    if (tnum) {
        if (tnum > 99)
            $("#totalRemindNum").text("99+").show();
        else
            $("#totalRemindNum").text(tnum).show();
    } else {
        $("#totalRemindNum").text(0).hide();
    }
}

/**
 * 退出事件
 */
function logOutEvent() {
    $.smaction(function(re, err){
        if(!err){
            $(window).unbind('beforeunload');
            localStorage.setItem("token", ""); //存储token
            location.href = objdata.loginPage;
        }
    }, {}, {route: "fuyou-auth", action: "login/exit", datastring: true})
}

/*
 功能：根据数据库表对应字段得到最新序列号
 strbef 前缀
 strmidtype中缀类型 1时间 2其他固定
 strmid中缀 yyyyMMdd,空
 strgd 后面格式
 strtable 表名
 strfield 字段名
 callback 回调函数
 */
function GetNo(strbef, strmidtype, strmid, strgd, strtable, strfield, callback, objwhere) {
    objwhere = objwhere || {};
    $.sm(function (re, err) {
        if (err) {
            jQuery.getparent().layer.msg(err, {icon: 5});
        } else {
            var num = strgd;
            if (re.length > 0) {
                var str = re[0][0];
                num = str.substring(strbef.length, str.length);
            }
            num = parseInt(num) + 1;
            var str = strbef + "" + strmid + strgd.substring(0, strgd.length - (num + "").length) + num;
            if (callback)
                callback(str);
        }
    }, ["tong.getno", strfield, strtable, strbef, $.msgwhere(objwhere)]);
};

function setdate(id, mindate, maxdate, callback) {
    $('#' + id).focus(function () {
        WdatePicker({
            dateFmt: 'yyyy-MM-dd',
            el: this,
            maxDate: maxdate ? maxdate : "",
            minDate: mindate ? mindate : "",
            onpicked: function () {
                if (callback) {
                    callback($('#' + id).val());
                }
            }
        });
    }).blur();
    $('#' + id).val(maxdate).blur();
}

/**
 * 判断儿保管理机构
 * @returns {boolean}
 */
function iserbao() {
    return (objdata.my.roletype === "e" || objdata.my.roletype === "ehos" || objdata.my.roletype === "ehosin" || objdata.my.roletype === "ehosother");
}

/**
 * 判断是否妇幼
 * @returns {boolean}
 */
function isfuyou() {
    return (objdata.my.roletype === "e");
}

/**
 * 判断是否医院角色
 * @returns {boolean}
 */
function ishospital() {
    return (objdata.my.roletype === "ehos" || objdata.my.roletype === "ehosin" || objdata.my.roletype === "ehosother");
}

/**
 * 判断是否是托幼
 * @returns {boolean}
 */
function istuoyou() {
    return (objdata.my.roletype === "eyey" || objdata.my.roletype === "echild" || objdata.my.roletype === "echyey");
}

/**
 * 幼儿园类型下拉
 * @returns {[]}
 */
function getYeyTypeHtml(havenull) {
    var arrhtml = [];
    if (havenull) {
        arrhtml.push('<option value="">请选择</option>');
    }
    for (var i = 0; i < objdata.arryeytype.length; i++) {
        arrhtml.push('<option value="' + objdata.arryeytype[i][1] + '">' + objdata.arryeytype[i][0] + '</option>');
    }
    return arrhtml.join("");
}

/**
 * 修改密码
 * @param cb
 */
function alterpwd(cb, title) {
    var strhtml =
        '<div style="padding: 40px;">\
            <div class="layui-form-item layui-form" style="margin:0px 0  10px 30px ">\
                <label class="layui-form-label" style="width:137px;">旧密码：</label>\
                <div class="layui-input-block" style="margin:0 20px 0 144px;">\
                    <input name="title" placeholder="请输入旧密码" class="layui-input" type="password" style="display: inline-block;width:280px;" id="txtoldpwd">\
                </div>\
            </div>\
            <div class="layui-form-item layui-form" style="margin:5px 0  10px 30px ">\
                <label class="layui-form-label" style="width:137px;">新密码：</label>\
                <div class="layui-input-block" style="margin-left:144px;">\
                    <input name="title" placeholder="请输入新密码" class="layui-input" type="password" style="width:280px; display: inline-block" id="txtnewpwd">\
                </div>\
            </div>\
            <div class="layui-form-item layui-form" style="margin:5px 0  10px 30px ">\
                <label class="layui-form-label" style="width:137px;">确认密码：</label>\
                <div class="layui-input-block" style="margin-left:144px;">\
                    <input name="title" placeholder="请再次输入新密码" class="layui-input" type="password" style="width:280px; display: inline-block" id="txtrenewpwd">\
                </div>\
            </div>\
        </div>';
    layer.open({
        type: 1,
        title: [title || "修改密码", 'text-align:center'],
        area: ['620px', '340px'], //宽高
        btnAlign: 'c',
        btn: ["确定", "取消"],
        content: strhtml,
        yes: function (index, layero) {
            var oldpwd = layero.find("#txtoldpwd").val();
            var newpwd = layero.find("#txtnewpwd").val();
            var renewpwd = layero.find("#txtrenewpwd").val();
            if($.cookie('islockpwd') && $.cookie('islockpwd')==jQuery.getparent().objdata.my.id){
                layer.msg("连续错误多次，请稍后1小时后再次尝试！");
                return false;
            }
            if (!oldpwd) {
                layer.msg("请输入旧密码！");
                return false;
            }
            if (!newpwd) {
                layer.msg("请输入新密码！");
                return false;
            }
            if (!ckpwd.reg.test(newpwd)) {
                return layer.msg("新" + ckpwd.tip);
            }
            var strongval = ckpwd.checkStrong(newpwd);
            if (strongval < 2) {
                return layer.msg("新" + ckpwd.tip2);
            }
            if (!renewpwd) {
                layer.msg("请再次确认新密码！");
                return false;
            }
            if (newpwd != renewpwd) {
                layer.msg("两次密码输入不一致！");
                return false;
            }
            $.sm(function (re, err) {
                if (re) {
                    if (re.wrong) {
                        layer.msg(re.wrong);
                    } else {
                        $.sm(function () {
                        }, ['user.updatepwdlevel', strongval])
                        layer.msg("修改成功！");
                        if($.cookie('islockpwd')){
                            $.cookie('islockpwd', "");
                            objdata.errCount = 0;
                        }
                        cb && cb(strongval);
                        layer.close(index);
                    }
                } else {
                    layer.msg(err);
                    if(err=="旧密码不正确"){
                       // 输入错误6次，锁定1小时
                        objdata.errCount++;
                        if(objdata.errCount==6){
                            $.cookie('islockpwd', jQuery.getparent().objdata.my.id, { expires: 1/24 }); // 1小时的过期时间
                            objdata.errCount = 0;
                        }
                    }
                }
            }, ["alterpwd.alterpwd", oldpwd, newpwd]);
        }
    });
}

/***
 * 获取实际名称
 */
function getcityname(){
    var citycode = objdata.fyinfo[2] || "";
    citycode = citycode.substring(0,4) + "00";
    return objdata.cityMap[citycode] || "";
}

/***
 * 获取身高别体重、年龄别体重、年龄别身高 评判汉字条件 2
 * @param hw
 * @param str
 * @returns {string}
 * @constructor
 */
function GetHWJudge2(hw, str) {
    var value = '';
    switch (hw){
        case '下':
            value = str + '&lt;M-2SD';//下
            break;
        case '中下':
            value ='M-2SD≤' + str + "&lt;M-1SD";//中下
            break;
        case '中-':
            value = 'M-1SD≤' + str + "&lt;M";//中-
            break;
        case '中':
            value = 'M-1SD≤' + str + '&lt;M+1SD';//中
            break;
        case '中+':
            value = 'M≤' + str + '&lt;M+1SD';//中+
            break;
        case '中上':
            value = 'M+1SD≤' + str + '&lt;M+2SD';//中上
            break;
        case '上':
            value = str + '≥M+2SD';//上
            break;
    }
    return value;
}