<!DOCTYPE html>
<html>
	    <head>
	    <title>添加体检项目</title>
	    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
	    <script type="text/javascript" src="../layui-btkj/layui.js"></script>
	    <script type="text/javascript" src="../sys/jquery.js"></script>
	    <link rel="stylesheet" href="../css/style.css"/>
	    <link rel="stylesheet" href="../css/icon.css"/>
	    <script type="text/javascript">
	        document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
	    </script>
	    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
			<style>
			body {
				background: #fff;
			}
			.incr-chat {
				width: 850px;
				margin: 0px auto;
				padding: 0 40px
			}
			.physical-tab td{ padding:0 5px}
			</style>
	    </head>
<body>
      <section class="incr-chat" style="display: ;">
              <h3 class="noticeTitle bg1" style="margin-top:10px;"><span class="lanflag">添加体检项目</span></h3>
	         <div class="physical-tab" style="margin: 10px 0;">
             	<div style="width: 90%; margin: 0 auto; border-bottom:1px dashed #dadada; padding-bottom:20px">
				<form class="layui-form" action="">
					<div class="layui-form-item" style="display: inline-block;margin: 15px 0 0 5px;">
					    <label class="layui-form-label" style="width: 100px;">体检项目类型：</label>
					    <div class="layui-input-block" style="width: 190px;margin-left: 100px;">
					      <select name="city" lay-verify="required">
					        <option value="">科室检查</option>
                            <option value="">实验室检查</option>
					      </select>
					    </div>
					</div>
					<div class="layui-form-item" style="display: inline-block;margin: 15px 0 0 5px;">
					    <label class="layui-form-label"  style="width: 120px;">体检项目名称：</label>
					    <div class="layui-input-block" style="width: 190px;margin-left: 120px;">
					      <input type="text" name="title" required  lay-verify="required" placeholder="" autocomplete="off" class="layui-input">
					    </div>
					</div>
					<a class="layui-btn" style="margin: -2px 0 0 20px;">查询</a>
				</form>
			</div>
				<table border="1" cellpadding="0" cellspacing="0" style="width: 90%;border-collapse: collapse; margin: 30px auto">
					<tbody>
						<tr style="background: #F1F1F1;">
							<td style="width:20%;">体检项目类型</td>
							<td style="width: 20%;">体检项目名称</td>
							<td style="width: 50%;">所含指标</td>
							<td style="width: 10%;">选择</td>
						</tr>
						<tr>
							<td rowspan="4"><b>科室检查</b></td>
							<td style="text-align: left"><b>一般检查A</b></td>
							<td style="text-align: left">身高、体重、体重指数、收缩压、舒张压</td>
							<td><input name="" type="checkbox" value=""></td>
						</tr>
						<tr>
							<td style="text-align: left"><b>外科（女）</b></td>
							<td style="text-align: left">皮肤、四肢关节、乳房</td>
							<td><input name="" type="checkbox" value=""></td>
						</tr>
						<tr>
							<td style="text-align: left"><b>耳鼻喉检查</b></td>
							<td style="text-align: left">耳朵、鼻子、喉咙</td>
							<td><input name="" type="checkbox" value=""></td>
						</tr>
						<tr>
							<td style="text-align: left"><b>听力</b></td>
							<td style="text-align: left">听力</td>
							<td><input name="" type="checkbox" value=""></td>
						</tr>
						<tr>
							<td rowspan="2"><b>实验室检查</b></td>
							<td style="text-align: left"><b>血常规</b></td>
							<td style="text-align: left">白细胞计数、红细胞计数、血红蛋白、红细胞压积、平均红细胞体积、血小板压积、中间细胞绝对值</td>
							<td><input name="" type="checkbox" value=""></td>
						</tr>
						<tr>
							<td style="text-align: left"><b>外科（女）</b></td>
							<td style="text-align: left">空腹血葡萄糖</td>
							<td><input name="" type="checkbox" value=""></td>
						</tr>
						<tr>
							<td rowspan="3"><b>医技检查</b></td>
							<td style="text-align: left"><b>腹部彩超</b></td>
							<td style="text-align: left">超肝、胆、胰、脾</td>
							<td><input name="" type="checkbox" value=""></td>
						</tr>
						<tr>
							<td style="text-align: left"><b>子宫、附件彩</b></td>
							<td style="text-align: left">子宫、附件</td>
							<td><input name="" type="checkbox" value=""></td>
						</tr>
                        <tr>
							<td style="text-align: left"><b>心电图</b></td>
							<td style="text-align: left">心电图</td>
							<td><input name="" type="checkbox" value=""></td>
						</tr>
					</tbody>
				</table>
			</div>
             <div style="text-align: center;margin-top: 30px;">
				    <button type="" class="layui-btn" style="padding:0 25px; color:#fff; border-radius:5px; background:#4bb1ff; margin-right:20px">确定</button>
				    <button type="" class="layui-btn layui-btn-primary" style="border:1px solid #333333; padding:0 25px; color:#333333; border-radius:5px;">取消</button>
		  </div>
        </section>
        
		<script>
		//Demo
		layui.use('form', function(){
		  var form = layui.form;
		  
		  //监听提交
		  form.on('submit(formDemo)', function(data){
		    layer.msg(JSON.stringify(data.field));
		    return false;
		  });
		});
		</script>
</body>
</html>
