/*
***文件创建: 2025-07-01 17:58:08
***创建作者: wang<PERSON><PERSON><PERSON>
***最后修改: 2025-07-01 17:58:08
***修改人员: wangzenghui
***内容摘要: ai入园体检分析-统计详细页面
*/
require.config({
    paths: {
        jquery: '../../sys/jquery',
        system: '../../sys/system',
        echarts: '../../plugin/echart-5.1.2/dist/echarts.min',
        echartsgl: '../../plugin/echarts-gl/dist/echarts-gl.min',
        commom3dbt: 'common',
        mock: '../../plugin/mock/dist/mock-min',
        layui: '../../layui-btkj/layui'
    },
    shim: {
        "system": {
            deps: ["jquery"]
        },
        "echarts": {
            deps: ["jquery"]
        },
        "echartsgl": {
            deps: ["echarts"]
        },
        "commom3dbt": {
            deps: ["jquery", "echarts"],
            exports: "commom3dbt"
        },
        "mock": {
            deps: ["jquery"],
            exports: "Mock"
        },
        "layui": {
            deps: ["jquery"]
        }
    },
    waitSeconds: 0
});

require(["jquery", "system", "mock", "commom3dbt", "layui"], function ($, system, Mock, commom3dbt, a) {
    // 获取URL参数中的totalcount
    var totalCountFromUrl = Arg("totalcount") || 50; // 默认50条

    //Mock响应数据
    Mock.mock(/\/fuyou\/LayEnter/, 'get', function (options) {
        var params = {};

        // 解析URL参数
        if (options.url && options.url.indexOf('?') !== -1) {
            var queryString = options.url.split('?')[1];
            var pairs = queryString.split('&');
            for (var i = 0; i < pairs.length; i++) {
                var pair = pairs[i].split('=');
                if (pair.length >= 2) {
                    params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1] || '');
                }
            }
        }

        // 只处理目标请求
        if (params.arr && params.arr.indexOf('w_AISchoolHealthAnalysis_statisticsDetail.getStatisticsDetail') !== -1) {
            // 解析参数 - 优先使用URL中的totalcount参数
            var totalCount = parseInt(totalCountFromUrl) || 50;
            if (params.swhere) {
                try {
                    var swhere = JSON.parse(decodeURIComponent(params.swhere));
                    if (swhere.msg_where && swhere.msg_where.totalCount) {
                        totalCount = parseInt(swhere.msg_where.totalCount[0]) || 50;
                    }
                } catch (e) {
                    console.log('解析swhere参数失败:', e);
                }
            }
            var childNames = ['李玲', '李二', '王三', '赵四', '钱五', '孙六', '周七', '吴八'];
            var kindergartens = ['阳光幼儿园', '蓝天幼儿园', '彩虹幼儿园', '星星幼儿园', '月亮幼儿园'];
            var grades = ['小班', '中班', '大班'];
            var classes = ['一班', '二班', '三班', '四班'];
            var sexes = ['男', '女'];
            var appointmentTypes = ['家长', '儿童'];
            var phonePrefixes = ['131', '132', '135', '136', '137', '138', '139', '150', '151', '152'];
            var mockData = [];
            var currentDate = new Date();
            for (var i = 0; i < totalCount; i++) {
                // 基本信息和时间
                var birthYear = 2018 + Math.floor(Math.random() * 4); // 2018-2021
                var birthMonth = String(Math.floor(Math.random() * 12) + 1).padStart(2, '0');
                var birthDay = String(Math.floor(Math.random() * 28) + 1).padStart(2, '0');
                var birthDate = birthYear + '-' + birthMonth + '-' + birthDay;
                var checkYear = currentDate.getFullYear();
                var checkMonth = String(Math.floor(Math.random() * 12) + 1).padStart(2, '0');
                var checkDay = String(Math.floor(Math.random() * 28) + 1).padStart(2, '0');
                var checkDate = checkYear + '-' + checkMonth + '-' + checkDay;
                var age = checkYear - birthYear;
                var ageMonths = Math.floor(Math.random() * 12);
                var ageString = age + '岁' + (ageMonths > 0 ? ageMonths + '月' : '');
                // 预约时间 (格式: 2024-06-30上午/下午)
                var appointmentDate = new Date(currentDate);
                appointmentDate.setDate(currentDate.getDate() - Math.floor(Math.random() * 30));
                var appointmentDay = appointmentDate.toISOString().split('T')[0];
                var appointmentPeriod = Math.random() > 0.5 ? '上午' : '下午';
                var appointmentTime = appointmentDay + appointmentPeriod;
                // 创建时间 (格式: 2024-06-30 12:30:24)
                var createDate = new Date(appointmentDate);
                createDate.setHours(Math.floor(Math.random() * 24));
                createDate.setMinutes(Math.floor(Math.random() * 60));
                createDate.setSeconds(Math.floor(Math.random() * 60));
                var createTime = createDate.getFullYear() + '-' +
                    String(createDate.getMonth() + 1).padStart(2, '0') + '-' +
                    String(createDate.getDate()).padStart(2, '0') + ' ' +
                    String(createDate.getHours()).padStart(2, '0') + ':' +
                    String(createDate.getMinutes()).padStart(2, '0') + ':' +
                    String(createDate.getSeconds()).padStart(2, '0');
                // 其他信息
                var phoneNumber = phonePrefixes[Math.floor(Math.random() * phonePrefixes.length)] + '****' +
                    String(Math.floor(Math.random() * 10000)).padStart(4, '0');
                var record = {
                    id: i + 1,
                    childname: childNames[i % childNames.length],
                    childsex: sexes[Math.floor(Math.random() * sexes.length)],
                    checkage: ageString,
                    childbirthday: birthDate,
                    checkdate: checkDate,
                    datetime: appointmentTime,
                    // datetype: appointmentTypes[Math.floor(Math.random() * appointmentTypes.length)],
                    createtime: createTime,
                    parentmobile: phoneNumber,
                    kindergarten: kindergartens[Math.floor(Math.random() * kindergartens.length)],
                    classname: grades[Math.floor(Math.random() * grades.length)] +
                        classes[Math.floor(Math.random() * classes.length)]
                };

                mockData.push(record);
            }

            // 返回标准格式
            return {
                msg: "",
                code: 0,
                data: mockData,
                count: mockData.length
            };
        }

        // 非目标请求返回空数据
        return {
            msg: "",
            code: 0,
            data: [],
            count: 0
        };
    });

    //初始化Mock数据
    function initMockData() {
        console.log('Mock数据初始化完成');
    }

    //下拉框的mock
    Mock.mock(new RegExp(location.origin.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '/api/AISchoolHealthAnalysis/kindergartens'), 'post', function () {
        var data = [
            {value: "0", text: "阳光幼儿园"},
            {value: "1", text: "蓝天幼儿园"},
            {value: "2", text: "彩虹幼儿园"},
            {value: "3", text: "星星幼儿园"},
            {value: "4", text: "月亮幼儿园"}
        ];
        return {
            "code": 200,
            "msg": "success",
            "data": data
        };
    });

    // 园所获取
    function getKindergartenSelectData(callback) {
        $.smaction(function (response) {
            if (response) {
                commom3dbt.updateSelectOptions('kindergarten', response);
                layui.use(['form'], function () {
                    var form = layui.form;
                    form.render('select');
                    // 获取URL参数中的kindergarten并设置
                    var kindergarten = Arg("kindergarten");
                    console.log("园所参数：" + kindergarten);
                    if (kindergarten !== undefined && kindergarten !== null) {
                        $("#kindergarten").val(kindergarten);
                    }
                    form.render('select'); // 重新渲染使选中生效
                    // 绑定select变更事件
                    form.on('select(kindergarten)', function (data) {
                        btnsearch(); // 自动触发搜索
                    });

                    // 执行回调（用于初始化表格）
                    if (callback) callback();
                });
            }
        }, {}, {route: "api", action: "AISchoolHealthAnalysis/kindergartens", datastring: true});
    }

    // 使用 layui 模块
    layui.use(['form', 'table', 'laydate'], function () {
        window.layuiForm = layui.form; // 全局保存 form 模块
        window.laydate = layui.laydate // 这里获取 laydate 引用
        window.commom3dbt = commom3dbt;
        layer = layui.layer;
        form = layui.form;
        table = layui.table;
        laydate = layui.laydate;

        // 初始化日期选择器
        laydate.render({
            elem: '#datestart',
            format: 'yyyy-MM-dd'
        });

        laydate.render({
            elem: '#dateend',
            format: 'yyyy-MM-dd'
        });

        // 手动渲染表单元素（重要：让下拉框显示）
        form.render();
        console.log('form.render()已执行');

        // 初始化Mock数据
        initMockData();

        getKindergartenSelectData();
        // 初始化事件
        initEvent();
        // 修改初始化流程：先获取下拉数据再初始化表格
        getKindergartenSelectData(function () {
            // 确保下拉框数据加载完成后再初始化表格
            initTable();
        });
    });

    // 初始化所有下拉框并绑定事件
    function initEvent() {
        // 设置日期范围
        var startDate = Arg("startDate");
        var endDate = Arg("endDate");
        if (startDate) $("#datestart").val(startDate);
        if (endDate) $("#dateend").val(endDate);

        // 设置园所
        var kindergarten = Arg("kindergarten");
        if (kindergarten) {
            $("#kindergarten").val(kindergarten);
            form.render('select');
        }

        // 设置完成状态
        var completedstatus = Arg("completedstatus");
        if (completedstatus) {
            $("#completedstatus").val(completedstatus);
            form.render('select');
        }

        // 设置检查结果
        var inspectionresult = Arg("inspectionresult");
        if (inspectionresult) {
            $("#inspectionresult").val(inspectionresult);
            form.render('select');
        }

        // 设置报告发布状态
        var reportreleasestatus = Arg("reportreleasestatus");
        if (reportreleasestatus) {
            $("#reportreleasestatus").val(reportreleasestatus);
            form.render('select');
        }

        // 设置家长阅读状态
        var parentsreadingstatus = Arg("parentsreadingstatus");
        if (parentsreadingstatus) {
            $("#parentsreadingstatus").val(parentsreadingstatus);
            form.render('select');
        }

        // 绑定搜索按钮事件
        $("#btnsearch").click(function () {
            btnsearch();
        });
        // 绑定所有下拉框的变更事件，变更时自动触发搜索
        form.on('select(kindergarten)', btnsearch);
        form.on('select(completedstatus)', btnsearch);
        form.on('select(inspectionresult)', btnsearch);
        form.on('select(reportreleasestatus)', btnsearch);
        form.on('select(parentsreadingstatus)', btnsearch);
    }

    /*
     * 功能：查询
     * 参数说明：无
     * 返回值说明：无
     */
    function btnsearch() {
        var objwhere = getswhere();
        console.log("%c打印参数::::::::" + objwhere.kindergarten + "::::::" + objwhere.completedstatus, "color:red");
        layui.table.reload('laytable', {
            url: encodeURI($.getLayUrl() + "?" + $.getSmStr(["w_AISchoolHealthAnalysis_statisticsDetail.getStatisticsDetail"])),
            page: {curr: 1},
            where: {
                swhere: $.msgwhere(objwhere),
                fields: 'id',
                types: 'asc'
            }
        });
    }

    /*
     * 功能：获取查询条件
     * 参数说明：无
     * 返回值说明：查询条件对象
     */
    function getswhere() {
        const objwhere = {};
        ['startDate', 'endDate'].forEach(param => {
            const value = Arg(param);
            if (value) objwhere[param] = value;
        });
        const formFields = [
            'kindergarten',
            'completedstatus',
            'inspectionresult',
            'reportreleasestatus',
            'parentsreadingstatus'
        ];
        formFields.forEach(field => {
            const value = $(`#${field}`).val();
            if (value) objwhere[field] = [value];
        });
        console.log("当前查询条件:", objwhere);
        return objwhere;
    }

    /*
     * 功能：初始化统计详情列表
     * 参数说明：无
     * 返回值说明：无
     */
    function initTable() {
        var objwhere = getswhere();
        var arrcol = [
            {fields: 'id', type: 'numbers', title: '序号', width: '8%', align: 'center'},
            {field: 'childname', title: '幼儿姓名', width: '8%', align: 'center'},
            {field: 'childsex', title: '性别', width: '8%', align: 'center'},
            {field: 'checkage', title: '体检年龄', width: '12%', align: 'center'},
            {field: 'childbirthday', title: '出生日期', width: '8%', align: 'center'},
            {field: 'checkdate', title: '体检日期', width: '8%', align: 'center'},
            {field: 'datetime', title: '预约时间', width: '12%', align: 'center'},
            // {field: 'datetype', title: '预约类型', width: '8%', align: 'center'},
            {field: 'createtime', title: '创建时间', width: '12%', align: 'center'},
            {field: 'parentmobile', title: '家长手机号', width: '15%', align: 'center'},
            {
                field: 'kindergarten', title: '幼儿园名称', width: '15%', align: 'center',
                templet: function (d) {
                    // 获取当前选中的幼儿园值
                    var selectedVal = $("#kindergarten").val();
                    console.log("%c幼儿园的kindergarten：" + selectedVal, "color:red");

                    // 如果有选中具体的幼儿园（非空值），显示选中的幼儿园名称
                    if (selectedVal && selectedVal !== "") {
                        return $("#kindergarten option:selected").text();
                    }

                    // 否则显示数据中实际的幼儿园名称
                    return d.kindergarten;
                }
            }
        ];

        layui.table.render({
            elem: '#laytable',
            url: encodeURI($.getLayUrl() + "?" + $.getSmStr(["w_AISchoolHealthAnalysis_statisticsDetail.getStatisticsDetail"])),
            height: 'full-' + ($('#laytable').offset().top + 10),
            where: {
                swhere: $.msgwhere(objwhere),
                fields: 'id',
                types: 'asc'
            },
            cols: [arrcol],
            done: function (res, curr, count) {
                console.log('统计详细数据加载完成，共' + count + '条记录');
            },
            countNumberBool: true,
            even: true,
            page: {
                layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                align: 'right'
            },
            limits: [30, 50, 100, 200],
            limit: 30
        });
    }
});