<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="Author" content="" />
    <meta name="Robots" content="All" />
    <title>查看请假情况</title>
    <link rel="stylesheet" href="../../css/reset.css" />
    <link rel="stylesheet" href="../../css/style.css" />
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <!--<script type="text/javascript" src="../../layui-btkj/layui.js"></script>-->
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <script src="../../sys/jquery.js"></script>
    <script src="../../sys/system.js"></script>
    <style>
        .wrap,.circle,.percent{
            position: absolute;
            width:160px;
            height:160px;
            border-radius: 50%;
        }
        .wrap{
            background-color: #eee;
        }
        .circle{
            box-sizing: border-box;
            border:20px solid #eee;
            clip:rect(0,160px,160px,80px);
        }
        .clip-auto{
            clip:rect(auto, auto, auto, auto);
        }
        .percent{
            box-sizing: border-box;
            top:-20px;
            left:-20px;
        }
        .left{
            transition:transform ease;
            border:20px solid #fc784f;
            clip: rect(0,80px,160px,0);
        }
        .right{
            border:20px solid #fc784f;
            clip: rect(0,160px,160px,80px);
        }
        .wth0{
            width:0;
        }
        .num{
            position: absolute;
            box-sizing: border-box;
            width: 140px;
            height: 140px;
            line-height:140px;
            text-align: center;
            font-size: 32px;
            left: 10px;
            top: 10px;
            border-radius: 50%;
            background-color: #fff;
            z-index: 1;
        }
        .num span {
            font-size: 16px;
        }
        .business{
            left: 18%;
        }
        .sick {
            left: 40%;
        }
        .other {
            left: 62%;
        }
        .num .reason {
            position: absolute;
            bottom: -25px;
            font-size: 14px;
            width: 100%;
            text-align: center;
            color: #333333;

        }
        .business .num {
            color: #26aff7;
        }
        .sick .num{
            color: #ff9a16;
        }
        .other .num{
            color: #28c883;
        }
        .sick .left, .sick .right {
            border-color: #1bc57b;
        }
        .other .left, .other .right {
            border-color: #cccccc;
        }
        .stat-chart {
            position: relative;
            width: 100%;
            height: 150px;
            margin-top: 20px;
        }
        .layui-table[lay-size="lg"] td, .layui-table[lay-size="lg"] th{padding: 10px 15px;}
    </style>
</head>
<body>
<section id="sectioncontent">
</section>
<script data-main="../../js/yey/cq_leavedetail" src='../../sys/require.min.js'></script>
</body>
</html>
