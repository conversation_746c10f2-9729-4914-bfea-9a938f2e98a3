<!DOCTYPE html>
<html>
<head>
<title>表1-6托育机构营养性疾病及常见病儿童登记表</title>
<meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
<link rel="stylesheet" href="../css/reset.css"/>
<link rel="stylesheet" href="../css/style.css"/>
<link rel="stylesheet" href="../css/icon.css">
<script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
<style>
body {
	background: #fff;
}
</style>
</head>
<body>
<div style="width: 90%;margin: 0 auto;">
<div class="rep-table tab_parent_sec" style="width: 90%;margin: 0 auto 0 auto;padding: 30px 0 0px 0;">
	<div class="divhead" title="表1-1托育机构儿童晨午检及全日健康观察表" style="text-align: center; font-size: 16pt; font-weight: bold; font-family: 宋体, 微软雅黑, 黑体; margin: 0px auto; width: 100%;">       <div class="divprintreport" style="font-size:12pt;font-weight:bold;text-align:center; display:block; margin-bottom:10px;">
			表1-6托育机构营养性疾病及常见病儿童登记表
		</div></div>
    	
	</div>

	

  
  <div style="margin-top:10px;">
   
  
       <table class="reporttable" style="clear: both; margin: 0px auto; font-size: 11pt; font-family: 宋体; border: 2px solid black; width: 100%;" cellspacing="0" cellpadding="1" border="0">
		<thead>
			<tr>
				
              <th>班级</th>
			  <th style="border-left:1px solid Black">姓名</th>
			
				<th  style=" border-left:1px solid Black;">性别</th>
				<th  style=" border-left:1px solid Black;">年龄</th>
                <th  style=" border-left:1px solid Black;">疾病名称</th>
                <th  style=" border-left:1px solid Black;">主要症状</th>
                <th  style=" border-left:1px solid Black;">干预与治疗</th>
                <th  style=" border-left:1px solid Black;">转归</th>
                
                 <th  style=" border-left:1px solid Black;">结案日期</th>
                <th  style=" border-left:1px solid Black;">收托类型</th>
        
				
			</tr>
		
		
		</thead>
		<tbody>
			
            
            	<tr style="line-height:20px;height:20px;">
				<td align="center" style="border-top:1px solid Black;"></td>
                <td align="center" style=" border-left:1px solid Black;border-top:1px solid Black;"></td>
                <td  align="center" style=" border-left:1px solid Black;border-top:1px solid Black;"></td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"></td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"></td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"></td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"></td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"></td>
                	
			
            	
			</tr>
         </tbody>
         </table>
        <div class="divprintreport" style="font-size:10pt;font-weight:bold;text-align: left;display:inline;">备注：1.登记范围包括营养不良、营养性缺铁性贫血、超重、肥胖、心理行为发育异常、听力异常、视力异常、龋齿等；2.收托类型包括全日托、半日托、计时托、临时托。3.转归包括痊愈、好转、未愈、离园。4.此表由保健人员填写。
         </div>
      
        
  </div>
</div>
<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../system/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../system/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../plugin/nicescroll/jquery.nicescroll.min.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../plugin/js/moment.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../plugin/viewer/viewer.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../js/order/orderdetail.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>
