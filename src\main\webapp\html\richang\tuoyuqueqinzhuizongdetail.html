﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <link rel="shortcut icon" href="/images/favicon.ico" type="image/x-icon" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="Copyright" content="Copyright 2016 by tongbang" />
    <meta name="Author" content="" />
    <meta name="Robots" content="All" />
    <title>表1-4托育机构因病缺勤追踪表</title>
    <link href="../../styles/admin.css" rel="stylesheet" type="text/css" />
    <link href="../../styles/tbstyles.css" type="text/css" rel="stylesheet" />
    <link href="../../plugin/ehaiform/css/tbbaseform.css" rel="stylesheet" type="text/css" />
    <link href="../../plugin/editselect/css/editselect.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../plugin/jquery_tabs/css/tbjquery.tabs.css" type="text/css"
          media="print, projection, screen" />
	<style>

		.rytjdjedit_table td{border:#eee solid 1px}
        /*滚动条样式*/
        html{
            color: #000;
            font-size: .7rem;
            font-family: "\5FAE\8F6F\96C5\9ED1",Helvetica,"黑体",Arial,Tahoma;
            height: 100%;
        }
        /*滚动条样式*/
        html::-webkit-scrollbar {
            display:none;
            width: 6px;
            height: 6px;
        }
        html:hover::-webkit-scrollbar{
            display:block;
        }
        html::-webkit-scrollbar-thumb {
            border-radius: 3px;
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
            border: 1px solid rgb(255, 255, 255);
            background: rgb(66, 66, 66);
        }
        html::-webkit-scrollbar-track {
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
            border-radius: 0;
            background: rgba(0,0,0,0.1);
        }
        .full_disease {
            display: inline-block;
            background: #eee;
            padding:0 10px;
            width:60px;
            text-align: center;
        }
		.layui-form-label{width: 90px;}
	</style>
</head>
<body>
    <div class="bodywidth" style="min-width: 920px;">
        <div class="content">
            <div class="layui-form layui-comselect" style="position: relative;padding: 10px 10px 5px 10px;">
                <div style="margin:10px 0;">
                    <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                        <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">日期：</label>
                        <div class="layui-input-inline" style="min-height: 30px;width:180px;">
                            <input id="recordtime" type="text" placeholder="日期" readonly class="layui-input"/>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                        <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">班级：</label>
                        <div class="layui-input-inline" style="min-height: 30px;width:180px;">
                            <select lay-filter="sel_classno" id="classno">
                                <option value="">请选择</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                        <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">幼儿：</label>
                        <div class="layui-input-inline" style="min-height: 30px;width:180px;">
                            <select lay-filter="sel_stuno" id="stuno">
                                <option value="">请选择</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                        <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;width: auto;">性别：</label>
                        <label id="sex" class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;width: auto;"></label>
                    </div>
                    <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                        <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">年龄：</label>
                        <label id="age" class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;width: auto;"></label>
                    </div>
                </div>
                <div style="margin:10px 0;">
<!--                    <div class="layui-form-item" style="display: inline-block;vertical-align: top;">-->
<!--                        <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">排查原因：</label>-->
<!--                        <div class="layui-input-inline" style="min-height: 30px;width:180px;">-->
<!--                            <input id="disease_time" type="text" placeholder="排查原因" readonly class="layui-input"/>-->
<!--                        </div>-->
<!--                    </div>-->
                    <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                        <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">发病时间：</label>
                        <div class="layui-input-inline" style="min-height: 30px;width:180px;">
                            <input id="disease_time" type="text" placeholder="发病时间" readonly class="layui-input"/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-label">主要症状：</div>
                <div class="layui-input-inline">
                    <div class="layui-form-item" style="display: block;vertical-align: top;">
<!--                        <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">主要症状：</label>-->
                        <div class="layui-input-inline" style="min-height: 30px;width:60px;">
                            <input type="checkbox" name="disease_text" value="发热" title="发热" lay-filter="disease_text" lay-skin="primary">
                        </div>
                        <div class="layui-input-inline" style="min-height: 30px;width:180px;display: none;">
                            <input type="text" class="txtweight" id="fr_des" placeholder="请注明温度" maxlength="20" style="width: 95%;"/>
                            <span class="FancyInput__bar___1P3wW" ></span>
                        </div>
                        <div class="layui-input-inline" style="min-height: 30px;width:60px;">
                            <input type="checkbox" name="disease_text" value="咳嗽" title="咳嗽" lay-filter="disease_text" lay-skin="primary">
                        </div>
                        <div class="layui-input-inline" style="min-height: 30px;width:60px;">
                            <input type="checkbox" name="disease_text" value="咽痛" title="咽痛" lay-filter="disease_text" lay-skin="primary">
                        </div>
                    </div>
                    <div class="layui-form-item" style="display:block;vertical-align: top;">
<!--                        <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;"></label>-->
                        <div class="layui-input-inline" style="min-height: 30px;width:60px;">
                            <input type="checkbox" name="disease_text" value="呕吐" title="呕吐" lay-filter="disease_text" lay-skin="primary">
                        </div>
                        <div class="layui-input-inline" style="min-height: 30px;width:180px;display: none;">
                            <input type="text" class="txtweight" id="ot_des" placeholder="注明次数" maxlength="20" style="width: 95%;"/>
                            <span class="FancyInput__bar___1P3wW" ></span>
                        </div>
                        <div class="layui-input-inline" style="min-height: 30px;width:60px;">
                            <input type="checkbox" name="disease_text" value="腹泻" title="腹泻" lay-filter="disease_text" lay-skin="primary">
                        </div>
                        <div class="layui-input-inline" style="min-height: 30px;width:180px;display: none;">
                            <input type="text" class="txtweight" id="fx_des" placeholder="注明次数" maxlength="20" style="width: 95%;"/>
                            <span class="FancyInput__bar___1P3wW" ></span>
                        </div>
                        <div class="layui-input-inline" style="min-height: 30px;width:60px;">
                            <input type="checkbox" name="disease_text" value="皮疹" title="皮疹" lay-filter="disease_text" lay-skin="primary">
                        </div>
                        <div class="layui-input-inline" style="min-height: 30px;width:60px;">
                            <input type="checkbox" name="disease_text" value="结膜充血" title="结膜充血" lay-filter="disease_text" lay-skin="primary">
                        </div>
                    </div>
                    <div class="layui-form-item" style="display:block;vertical-align: top;">
<!--                        <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;"></label>-->
                        <div class="layui-input-inline" style="min-height: 30px;width:60px;">
                            <input type="checkbox" name="disease_text" value="其他" title="其他" lay-filter="disease_text" lay-skin="primary">
                        </div>
                        <div class="layui-input-inline" style="min-height: 30px;width:180px;display: none;">
                            <input type="text" class="txtweight" id="qt_des" placeholder="请详细描述" maxlength="20" style="width: 95%;"/>
                            <span class="FancyInput__bar___1P3wW" ></span>
                        </div>
                    </div>
                </div>
                <div style="margin:10px 0;">
                    <div class="layui-form-item" style="display: block;vertical-align: top;">
                        <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">处理：</label>
                        <div class="layui-input-inline" style="min-height: 30px;width:180px;">
                            <div class="rytj_tz">
                                <input type="text" class="txtweight" id="handle_method" maxlength="200" style="width: 95%;"/>
                                <span class="FancyInput__bar___1P3wW" ></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div style="margin:10px 0;">
                    <div class="layui-form-item" style="display: block;vertical-align: top;">
                        <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">就诊医院：</label>
                        <div class="layui-input-inline" style="min-height: 30px;width:180px;">
                            <div class="rytj_tz">
                                <input type="text" class="txtweight" id="hospital" maxlength="60" style="width: 95%;"/>
                                <span class="FancyInput__bar___1P3wW" ></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div style="margin:10px 0;">
                    <div class="layui-form-item" style="display: block;vertical-align: top;">
                        <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">追踪结果：</label>
                        <div class="layui-input-inline" style="min-height: 30px;width:180px;">
                            <div class="rytj_tz">
                                <input type="text" class="txtweight" id="track_result" maxlength="200" style="width: 95%;"/>
                                <span class="FancyInput__bar___1P3wW" ></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div style="margin:10px 0;">
                    <div class="layui-form-item" style="display: block;vertical-align: top;">
                        <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">诊断：</label>
                        <div class="layui-input-inline" style="min-height: 30px;width:180px;">
                            <div class="rytj_tz">
                                <input type="text" class="txtweight" id="diagnosis" maxlength="200" style="width: 95%;"/>
                                <span class="FancyInput__bar___1P3wW" ></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div style="margin:10px 0;">
                    <div class="layui-form-item" style="display: block;vertical-align: top;">
                        <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">追踪联系人：</label>
                        <div class="layui-input-inline" style="min-height: 30px;width:180px;">
                            <div class="rytj_tz">
                                <input type="text" class="txtweight" id="track_person" maxlength="60" style="width: 95%;"/>
                                <span class="FancyInput__bar___1P3wW" ></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item" style="display: block;vertical-align: top;">
                    <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">备注：</label>
                    <div class="layui-input-inline" style="min-height: 30px;width:180px;">
                        <div class="rytj_tz">
                            <input type="text" class="txtweight" id="mark" maxlength="200" style="width: 95%;"/>
                            <span class="FancyInput__bar___1P3wW" ></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript">
        var v = top.version;
        document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../sys/function.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../js/richang/tuoyucommon.js?v='" + v + "><" + "/script>");
        document.write("<script type='text/javascript' src='../../js/richang/tuoyuqueqinzhuizongdetail.js?v='" + v + "><" + "/script>");
    </script>
</body>
</html>
