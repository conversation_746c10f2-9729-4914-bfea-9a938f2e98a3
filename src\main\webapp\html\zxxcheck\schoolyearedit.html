<!DOCTYPE html>
<html>
<head>
    <title>年度编辑</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="../../styles/xbcomstyle.css">
    <link rel="stylesheet" href="../../styles/tbstyles.css"/>
    <link rel="stylesheet" href="../../css/reset.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">

    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + top.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <style>
        .project-form {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
            gap: 10px;
        }

        .form-group {
            line-height: 35px;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
        }

        .label-text {
            margin-right: 10px;
            font-size: 15px;
            width: 80px;
            text-align: right;
            flex-shrink: 0;
        }

        .form-control {
            width: 200px;
            height: 35px;
            border: none;
            padding-left: 10px;
        }

        .layui-btn{
            margin: 30px 30px 0px 30px;
            background-color: #4A90E2;
        }

        .input-style{
            border: #cbd6e1 solid 1px;
        }
        .layui-form-label{
            width: 100px;
        }
    </style>
</head>
<body>
<div class="">
    <form class="project-form layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label" style="color:#778CA2;"><em>*</em>学年:</label>
            <div class="layui-input-inline" style="width: 310px;">
                <input type="text" id="yearname" name="yearname" placeholder="请输入" class="layui-input" maxlength="60">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="color:#778CA2;"><em>*</em>起止日期:</label>
            <div class="layui-input-inline" style="width: 150px;margin-right: 0px;">
                <input id="starttime" type="text" style="min-width: 130px;" readonly placeholder="开始时间" class="layui-input"/>
                <img id="iconyystartdate" src="../../images/newicon/ico_calendar.png" style="cursor: pointer;position: relative;top: -29px;width: 20px;left: 103px;">
            </div>
            <div class="layui-input-inline" style="width: 12px;margin-right: 0px;">
                <span class="linebg">-</span>
            </div>
            <div class="layui-input-inline" style="width: 150px;">
                <input id="endtime" type="text" style="min-width: 130px; " readonly placeholder="结束时间" class="layui-input"/>
                <img id="iconyyenddate" src="../../images/newicon/ico_calendar.png" style="cursor: pointer;position: relative;top: -29px;width: 20px;left: 103px;">
            </div>
        </div>
    </form>
</div>
<script data-main="../../js/zxxcheck/schoolyearedit" src='../../sys/require.min.js'></script>
</body>
</html>
