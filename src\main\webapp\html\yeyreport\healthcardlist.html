<!DOCTYPE html>
<html>
<head>
    <title>健康证列表</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <link rel="stylesheet" href="../../css/reset.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <style>
        html, body { height: 100%; background: #EAEFF3; overflow-y: hidden; }
        .layui-form-label { line-height: 28px; }
    </style>
</head>
<body>
    <section>
        <div class="marmain">
            <div class="content-medical" style="display:none;">
                <div class="layui-form layui-comselect">
                    <div class="layui-form" style="display: inline-block;">
                        <label class="layui-form-label" style="width: 60px;">关键字：</label>
                        <div class="def-search" style="display: inline-block;vertical-align: top;width: 200px;margin: 0px 10px 0 0; height: 28px;">
                            <input id="txtkey" type="text" placeholder="" class="layui-input">
                        </div>
                        <div class="layui-form-item" style="display: inline-block;">
                            <button id="btnsearch" class="layui-btn form-search" style="vertical-align: top;">查询</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="marmain-cen">
                <div class="content-medical">
                    <div class="tbmargin">
                        <table id="laytable" lay-filter="laytable" class="layui-table"></table>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <script data-main="../../js/yeyreport/healthcardlist" src="../../sys/require.min.js"></script>
</body>
</html>