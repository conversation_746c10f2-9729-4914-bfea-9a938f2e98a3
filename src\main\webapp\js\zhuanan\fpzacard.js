﻿/*
日期： 
作者：
內容摘要:肥胖儿管理
*/
var fpsetting = {
    arrstuno: [], //学生的学号  用于上一个下一个
    curstuname: "",
    strsex: '',
    strbirthday: '',
    curstuno: ''//当前学生的学号
    , objtjinfo: {}
    , objfpfd: {}//肥胖度
    , oldwlw: {}//卧立位
}

require.config({
    paths: {
        sys: "../../sys/system",
        jquery: "../../sys/jquery",
        editselect: "../../plugin/editselect/js/editselect",
        validator: "../../plugin/validator/js/validator",
        wdatepicker: "../../plugin/wdatepicker/wdatepicker"
    }, shim: {
//		contextmenu:["jquery"]
    },
    waitSeconds: 0
});
var parentobj = null;
require(["jquery", "sys"], function () {
    require(["editselect", "validator", "wdatepicker"], function () {
        parentobj = jQuery.getparent().$("#win_" + Arg('mid')).children('iframe')[0].contentWindow;
        jQuery.getparent().layer.load();
        initclassdata(Arg("id"));
        initselect1();
        //$(".lbstandard").text(parentobj.tongsetting.strstandard);
        $('#chkvest').click(check);
        $("#txtvestheight").change(function () {
            strhewe('vest', 1);
        });
        $("#txtvestweight").change(function () {
            strhewe('vest', 2);
        });
        $("#txtoldheight").change(function () {
            strhewe('old', 1);
        });
        $("#txtoldweight").change(function () {
            strhewe('old', 2);
        });
        $("#txtfheight").change(function () {
            strhewe('f', 1);
        });
        $("#txtfweight").change(function () {
            strhewe('f', 2);
        });
        $("#txtmheight").change(function () {
            strhewe('m', 1);
        });
        $("#txtmweight").change(function () {
            strhewe('m', 2);
        });
        $('#txtvestheight').prop('disabled', true);
        $('#txtvestweight').prop('disabled', true);
        $('#txtenddate').prop('disabled', true);
        $("#txtolddate").click(function () {
            WdatePicker({skin: 'whyGreen'});
        });
        $("#txtenddate").click(function () {
            WdatePicker({skin: 'whyGreen'});
        });
        $("#btnsave").click(function (event, callback) {
            btnsave(fpsetting.curstuno, callback);
        });//保存
        $(document).scroll(function () {//滚动固定表头
            //console.log($("#t_bodyhead").offset().top + ":" + $(document).scrollTop());
            if ($("#t_bodyhead").offset().top - $(document).scrollTop() <= 0) {
                $("#tbl_clone").show().css('margin-top', $(document).scrollTop() - $("#t_bodyhead").offset().top).html('<tbody>' + $("#t_bodyhead").html() + '</tbody>');
            } else {
                $("#tbl_clone").hide().html('');
            }
        });
    });
});

/*
功能：初始化下拉框
*/
function initselect1() {
    $('#selmt').editableSelect({
        bg_iframe: true,
        isreadonly: 1,
        width: '60px',
        case_sensitive: false, // If set to true, the user has to type in an exact
        items_then_scroll: 10// If there are more than 10 items, display a scrollbar
    });
}

function check() {
    if ($("#chkvest")[0].checked) {
        $('#txtvestheight').prop('disabled', false);
        $('#txtvestweight').prop('disabled', false);
        $('#txtenddate').prop('disabled', false);
        $('#txtenddate').attr('require', true);
        $('#selvest').replaceWith('<label id="selvest"></label>');
        var arrdata = [['痊愈', "0"], ['好转', "1"], ['转医院', "2"], ['未愈', "3"], ['离园', "4"]];
        $('#selvest').editableSelect({
            bg_iframe: true,
            isreadonly: 1,
            arrdata: arrdata,
            deftext: '痊愈',
            defvalue: "0", //默认值
            width: '50px',
            case_sensitive: false, // If set to true, the user has to type in an exact
            items_then_scroll: 10// If there are more than 10 items, display a scrollbar
        });
    } else {
        $('#txtenddate').attr('require', false);
        $('#txtvestheight').prop('disabled', true);
        $('#txtvestweight').prop('disabled', true);
        $('#txtenddate').prop('disabled', true);
        $('#txtvestheight').val("");
        $('#txtvestweight').val("");
        $('#txtenddate').val("");
        $('#selvest').replaceWith('<label id="selvest"></label>');
        $('#txtvestfat').val("");
        $('#divvestfat').html("");
    }
}

/*
功能：加载数据
*/
function initclassdata(stuno) {
    $("#divtbody").html("");
    fpsetting.curstuno = stuno;
    var strdate = parentobj.tongsetting.cursysdate;
    $.sm(function (re, err) {
        if (err) {
            jQuery.getparent().layer.msg(err, {icon: 5});
            jQuery.getparent().layer.closeAll('loading');
        } else if (re && re[0]) {
            fpsetting.strbirthday = re[0][4];
            fpsetting.strsex = re[0][2];
            fpsetting.hest = re[0][15];
            fpsetting.curstuname = re[0][1];
            $("#txtname").html(re[0][1]);
            $("#txtsex").html(re[0][2]);
            $("#txtclass").html(re[0][3]);
            $("#txtdirthday").html(re[0][4]);
            $("#txtfheight").val(re[0][5]);
            $("#txtfweight").val(re[0][6]);
            $("#txtmheight").val(re[0][7]);
            $("#txtmweight").val(re[0][8]);
            $("#lbfbmi").text(rebmi(re[0][5], re[0][6]));
            $("#lbmbmi").text(rebmi(re[0][7], re[0][8]));
            $("#txtotherfat").val(re[0][9]);
            $("#txtcondition").val(re[0][10]);
            $("#txtolddate").val(re[0][11]);
            $("#txtoldheight").val(re[0][12]);
            $("#txtoldweight").val(re[0][13]);
            $("#txtselwlwway").val(re[0][15]);
            fpsetting.oldwlw["first"] = re[0][15];
            $('#txtselwlwway').editableSelect({
                bg_iframe: true,
                isreadonly: 1,
                width: '46px',
                onSelect: initselclick, //选择事件
                case_sensitive: false, // If set to true, the user has to type in an exact
                items_then_scroll: 10// If there are more than 10 items, display a scrollbar
            });
            $("#selmt").val(re[0][16]);
            $("#chkvest").val(re[0][18]);
            if (re[0][18] == '1') {
                $('#chkvest').prop('checked', true);
                $('#txtenddate').attr('require', true);
                $('#txtvestheight').prop('disabled', false);
                $('#txtvestweight').prop('disabled', false);
                $('#txtenddate').prop('disabled', false);
                $('#txtvestheight').val(re[0][20]);
                $('#txtvestweight').val(re[0][21]);
                $('#txtenddate').val(re[0][18]);

                $('#selvest').replaceWith('<label id="selvest"></label>');
                var arrdata = [['痊愈', "0"], ['好转', "1"], ['转医院', "2"], ['未愈', "3"], ['离园', "4"]];
                $('#selvest').editableSelect({
                    bg_iframe: true,
                    isreadonly: 1,
                    arrdata: arrdata,
                    deftext: '痊愈',
                    defvalue: "0", //默认值
                    width: '50px',
                    case_sensitive: false, // If set to true, the user has to type in an exact
                    items_then_scroll: 10// If there are more than 10 items, display a scrollbar
                });
                $('#selvest').val(re[0][22]);
            } else {
                $('#chkvest').prop('checked', false);
                $('#txtenddate').attr('require', false);
                $('#txtvestheight').prop('disabled', true);
                $('#txtvestweight').prop('disabled', true);
                $('#txtenddate').prop('disabled', true);
                $('#txtvestheight').val("");
                $('#txtvestweight').val("");
                $('#txtenddate').val("");
                $('#selvest').replaceWith('<label id="selvest"></label>');
                $('#txtvestmalnu').html("");
            }
            $('#divvestfat').html(re[0][23]);
            $('#divvestwufat').html(re[0][24]);
            $('#txtenddate').val(re[0][19]);
            var arrhwle = parentobj.tongsetting.hwle["a_" + fpsetting.strsex + "_" + re[0][12] + "_" + re[0][15]];
            var strhw = "";
            if (arrhwle) {
                strhw = parentobj.GetHW(re[0][13], [arrhwle[0], arrhwle[1], arrhwle[2], arrhwle[4], arrhwle[5], arrhwle[6], arrhwle[3]]);
                var strfat = parentobj.GetFat(arrhwle[3], re[0][13]);
                var strvalue = parentobj.getFloatValue(re[0][13] * 100 / arrhwle[3] - 100);
                $("#divoldfat").html((parentobj.tongsetting.fatshow == 1 ? strfat : parentobj.tongsetting.objfatshow[strfat]) + "(" + strvalue + "%)");
                $("#txtoldfat").val(strfat);
            }
            if (!re[0][17]) {
                var age = parentobj.GetAge(fpsetting.strbirthday, re[0][11]);
                var bmi = parentobj.getbmi(re[0][12], re[0][13]);
                var strwfat = parentobj.getWFat(strhw, bmi, parentobj.tongsetting.strnormal, age, fpsetting.strsex);
                $("#divoldwufat").html(strwfat);
            } else {
                $("#divoldwufat").html(re[0][17]);
            }
            init(stuno);
        } else {
            jQuery.getparent().layer.msg("未查到有关数据！");
            jQuery.getparent().layer.closeAll('loading');
        }
    }, ["fpzacard.initcladata", stuno, strdate]);
}

function initselclick() {
    var strid = $(this.text).attr("id").substring(0, 6);
    if (strid == "selwlw") {
        var num = $(this.text).attr("id").substring(6);
        var hest = $("#selwlw" + num).val();
        var height = $("#txtheight" + num).val();
        var weight = $("#txtweight" + num).val();
        var txtdate = $("#txtdate" + num).val();
        if (height != "" && weight != "") {
            var arrhwle = parentobj.tongsetting.hwle["a_" + fpsetting.strsex + "_" + height + "_" + hest];
            var strhw = "";
            var age = "";
            if (arrhwle) {
                strhw = parentobj.GetHW(weight, [arrhwle[0], arrhwle[1], arrhwle[2], arrhwle[4], arrhwle[5], arrhwle[6], arrhwle[3]]);
                var strfat = parentobj.GetFat(arrhwle[3], weight);
                var strvalue = parentobj.getFloatValue(weight * 100 / arrhwle[3] - 100);
                age = parentobj.GetAge(fpsetting.strbirthday, txtdate);
                $("#divfat" + num).html((parentobj.tongsetting.fatshow == 1 ? strfat : parentobj.tongsetting.objfatshow[strfat]) + "<br />(" + strvalue + "%)");
                $("#txtfat" + num).val(strfat);
                fpsetting.oldwlw[num] = hest;
            } else {
                jQuery.getparent().layer.msg("此条体检数据不存在" + $(this.text)[0].value + "的标准");
                $(this.text).val(fpsetting.oldwlw[num]);
            }
            var bmi = parentobj.getbmi(height, weight);
            var strwfat = parentobj.getWFat(strhw, bmi, parentobj.tongsetting.strnormal, age, fpsetting.strsex);
            $("#divwfat" + num).html(strwfat);
        }
    } else if ($(this.text).attr("id") == 'txtselwlwway') {//首次检查结果的卧立位
        var hest = $(this.text).val();
        var height = $("#txtoldheight").val();
        var weight = $("#txtoldweight").val();
        var startdate = $("#txtolddate").val();//开始管理日期
        if (height != "" && weight != "") {
            var arrhwle = parentobj.tongsetting.hwle["a_" + fpsetting.strsex + "_" + height + "_" + hest];
            var age = "";
            var strhw = "";
            age = parentobj.GetAge(fpsetting.strbirthday, startdate);
            if (arrhwle) {
                strhw = parentobj.GetHW(weight, [arrhwle[0], arrhwle[1], arrhwle[2], arrhwle[4], arrhwle[5], arrhwle[6], arrhwle[3]]);
                var strfat = parentobj.GetFat(arrhwle[3], weight);
                var strvalue = parentobj.getFloatValue(weight * 100 / arrhwle[3] - 100);
                $("#divoldfat").html((parentobj.tongsetting.fatshow == 1 ? strfat : parentobj.tongsetting.objfatshow[strfat]) + "(" + strvalue + "%)");
                $("#txtoldfat").val(strfat);
                fpsetting.oldwlw["first"] = hest;
            } else {
                jQuery.getparent().layer.msg("此条体检数据不存在" + $(this.text)[0].value + "的标准");
                $(this.text).val(fpsetting.oldwlw["first"]);
            }
            var bmi = parentobj.getbmi(height, weight);
            var strwfat = parentobj.getWFat(strhw, bmi, parentobj.tongsetting.strnormal, age, fpsetting.strsex);
            $("#divoldwufat").html(strwfat);
        }
    }
}

/*
功能：读取管理卡
*/
function init(stuno) {
    var objwhere = {}
    if (Arg("sdate"))
        objwhere.managetimesdate = [Arg("sdate")];
    if (Arg("edate"))
        objwhere.managetimeedate = [Arg("edate") + " 23:59:59"];
    $.sm(function (re, err) {
        if (err) {
            jQuery.getparent().layer.msg(err, {icon: 5});
            jQuery.getparent().layer.closeAll('loading');
        } else {
            var arrdata = [];
            for (var i = 0; i < re.length; i++) {
                arrdata = re[i];
                var arrhwle = parentobj.tongsetting.hwle["a_" + fpsetting.strsex + "_" + arrdata[4] + "_" + arrdata[3]];
                var strhw = "";
                if (arrhwle) {
                    strhw = parentobj.GetHW(arrdata[5], [arrhwle[0], arrhwle[1], arrhwle[2], arrhwle[4], arrhwle[5], arrhwle[6], arrhwle[3]]);
                    var strfat = parentobj.GetFat(arrhwle[3], arrdata[5]);
                    var strvalue = parentobj.getFloatValue(arrdata[5] * 100 / arrhwle[3] - 100);
                    arrdata.push((parentobj.tongsetting.fatshow == 1 ? strfat : parentobj.tongsetting.objfatshow[strfat]) + "<br />(" + strvalue + "%)");
                    fpsetting.objtjinfo[arrdata[0]] = arrdata;
                    fpsetting.objfpfd[arrdata[0]] = strvalue;
                    setHtml(arrdata);
                }
                if (!arrdata[21]) {
                    var age = parentobj.GetAge(fpsetting.strbirthday, arrdata[1]);
                    var bmi = parentobj.getbmi(arrdata[4], arrdata[5]);
                    arrdata[21] = parentobj.getWFat(strhw, bmi, parentobj.tongsetting.strnormal, age, fpsetting.strsex);
                }
            }
            setHtml();
            jQuery.getparent().layer.closeAll('loading');
        }
    }, ["fpzacard.init", stuno, $.msgwhere(objwhere)]);
}

/*
设置肥胖明细记录行
*/
function setHtml(arr) {
    var len = $("#divtbody")[0].rows.length;
    var fno = len ? $($("#divtbody")[0].rows[len - 1]).attr('fno') : 0;
    fno = fno ? Math.floor(fno) + 1 : 1;
    var strstyle = "", isdisabled = 0;
    if (arr && arr[22]) {//从体检进来的
        strstyle = "disabled='disabled'";
        isdisabled = 1;
    }
    var arrstr = ['<tr fno=' + fno + ' ftime="' + (arr && arr[1] ? arr[1] : '') + '"><td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;padding:0 5px;line-height:80px;height:80px;">'];
    arrstr.push([arr && arr[0] ? '<input type="button" onclick="delrowevent(this,' + fno + ',' + arr[0] + ');" id="btdelete' + fno + '" value="删除"  class="btn"  style=" margin-bottom:2px"/>' : '&nbsp;'].join(""));

    arrstr.push('</td><td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;padding:0 5px;"><input ' + strstyle + ' type="text" id="txtdate' + fno + '" style="width: 95px;" class="Wdate" value="' + (arr && arr[1] ? arr[1] : '') + '" onclick="initdate(' + fno + ');" onblur="changeDate(' + fno + ');" />' + (arr && arr[22] ? "<label style='color:#ff8591;'>体</label>" : "") + '</td>');
    arrstr.push('<td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;padding:0 5px;"><label id="txtage' + fno + '" age="' + (arr && arr[1] ? parentobj.GetAge(fpsetting.strbirthday, arr[1]) : "") + '">' + (arr && arr[1] ? parentobj.GetAge(fpsetting.strbirthday, arr[1], 'zh') : "") + '</label>&nbsp;</td>'); //年龄(arr && arr[2] ? arr[2] : '')
    arrstr.push('<td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;padding:0 5px;"><select id="selwlw' + fno + '" class="editable-select">');
    arrstr.push('<option value="2" ' + (arr && arr[3] == 2 ? 'selected="selected"' : '') + 'selected="selected">立位</option><option ' + (arr && arr[3] == 1 ? 'selected="selected"' : '') + ' value="1">卧位</option>');
    fpsetting.oldwlw[fno] = arr && arr[3] || "";
    arrstr.push('</select></td><td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;padding:0 5px;">');
    //体重
    arrstr.push('<input ' + strstyle + ' type="text" id="txtweight' + fno + '" onchange="strhewe(' + fno + ',2);" value="' + (arr && arr[5] ? arr[5] : '') + '" style="width:95%;" class="tjzagl_input" />  <span class="FancyInput__bar___1P3wW" ></span>');
    arrstr.push('</td><td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;padding:0 5px;">');
    //身高
    arrstr.push('<input ' + strstyle + ' type="text" id="txtheight' + fno + '" onchange="strhewe(' + fno + ',1);" value="' + (arr && arr[4] ? arr[4] : '') + '" style="width: 95%"   class="tjzagl_input"/>');
    arrstr.push('</td>');
    arrstr.push('<td style="border-right: solid 1px #d8d8d8; border-bottom: solid 1px #d8d8d8;"><label style="display:none;" id="txtagew' + fno + '">' + (arr && arr[26] ? arr[26] : '') + '</label><label id="agewjudge'+fno+'">' + jQuery.getparent().GetHWJudge2((arr && arr[26] ? arr[26] : ''), "W/A") + '</label></td>');
    arrstr.push('<td style="border-right: solid 1px #d8d8d8; border-bottom: solid 1px #d8d8d8;"><label style="display:none;" id="txtageh' + fno + '">' + (arr && arr[27] ? arr[27] : '') + '</label><label id="agehjudge'+fno+'">' + jQuery.getparent().GetHWJudge2((arr && arr[27] ? arr[27] : ''), "H/A") + '</label></td>');
    arrstr.push('<td style="border-right: solid 1px #d8d8d8; border-bottom: solid 1px #d8d8d8;"><label style="display:none;" id="txthewe' + fno + '">' + (arr && arr[28] ? arr[28] : '') + '</label><label id="hewejudge'+fno+'">' + jQuery.getparent().GetHWJudge2((arr && arr[28] ? arr[28] : ''), "W/H") + '</label></td>');
    var bmi = "&nbsp;";
    if (arr && arr[4] && arr[5]) {
        bmi = arr[4] == 0 ? 0 : parseInt((arr[5] / ((arr[4] / 100) * (arr[4] / 100))) * 1000) / 1000;
    }
    arrstr.push('<td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;"><label id="divbmi' + fno + '">' + bmi + '</label></td>');
    arrstr.push('<td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;display:none;"><label id="divfat' + fno + '">' + (arr && arr[6] ? arr[6] : '') + '</label>&nbsp;<input id="txtfat' + fno + '" type="hidden" value="' + (arr && arr[6] ? arr[6] : '') + '" /></td>');
    arrstr.push('</td><td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;"><label id="divwfat' + fno + '">' + (arr && arr[21] ? arr[21] : '') + '</label></td>');
    arrstr.push('<td style="border-right: solid 1px #d8d8d8; border-bottom: solid 1px #d8d8d8; padding:0 10px""><textarea id="txtproblem' + fno + '" maxlength="500" style="width:95%;" class="tjzagl_textarea">' + (arr && arr[23] ? arr[23] : '') + '</textarea>  <span class="FancyInput__bar___1P3wW" ></span></td>');
    arrstr.push('<td style="border-right: solid 1px #d8d8d8; border-bottom: solid 1px #d8d8d8; padding:0 10px""><textarea id="txtidea' + fno + '" maxlength="500"  style="width:95%;" class="tjzagl_textarea">' + (arr && arr[24] ? arr[24] : '') + '</textarea>  <span class="FancyInput__bar___1P3wW" ></span></td>');
    arrstr.push('<td style="border-bottom: solid 1px #d8d8d8;padding-right:4px;padding-left:4px;"><input id="txtdoctor' + fno + '" type="text"  style="width:95%;" value="' + (arr && arr[25] ? arr[25] : '') + '" maxlength="20" class="tjzagl_input" />  <span class="FancyInput__bar___1P3wW" ></span></td>');
    arrstr.push('</tr>');
    $("#divtbody").append(arrstr.join(""));
}

/*
功能：删除功能
*/
function delrowevent(obj, num, id) {
    jQuery.getparent().layer.confirm('请确认是否真的进行删除操作？', function (r) {
        $.sm(function (re, err) {
            if (err) {
                jQuery.getparent().layer.msg(err, {icon: 5});
            } else {
                $(obj).closest('tr').remove();
            }
            jQuery.getparent().layer.close(r);
        }, ["fpzacard.delmanage", id]);
    });
}

/*
功能：初始化日期
*/
function initdate(num) {
    WdatePicker({
        skin: 'whyGreen', minDate: Arg("sdate"), maxDate: Arg("edate"), onpicking: function (dp) {
            var age = parentobj.GetAge(fpsetting.strbirthday, dp.cal.getNewDateStr());
            if (age < 3) {
                $("#selwlw" + num).val(1);
            }
            $("#txtage" + num).html(parentobj.getZhAgeByAge(age)).attr("age", age);
        }
    });
}

function changeDate(num) {
    var strdate = $("#txtdate" + num).val();
    var age = parentobj.GetAge(fpsetting.strbirthday, strdate);
    if (age < 3) {
        $("#selwlw" + num).val(1);
    }
    $("#txtage" + num).html(parentobj.getZhAgeByAge(age)).attr("age", age);
}

/*
功能：
*/
function strhewe(num, type) {
    var heightid = isNaN(num) ? 'txt' + num + 'height' : 'txtheight' + num;
    var weightid = isNaN(num) ? 'txt' + num + 'weight' : 'txtweight' + num;
    var strfatid = isNaN(num) ? 'div' + num + 'fat' : 'divfat' + num;
    var strtxtid = isNaN(num) ? 'txt' + num + 'fat' : 'txtfat' + num;
    var strwfatid = isNaN(num) ? 'div' + num + 'wufat' : 'divwfat' + num;
    var strageh = isNaN(num) ? 'txt' + num + 'ageh' : "txtageh" + num;
    var stragew = isNaN(num) ? 'txt' + num + 'agew' : "txtagew" + num;
    var strhewe = isNaN(num) ? 'txt' + num + 'hewe' : "txthewe" + num;
    var strdateid = isNaN(num) ? 'txt' + num + 'date' : "txtdate" + num;
    // var hest = isNaN(num) ? fpsetting.hest : $("#selwlw" + num).val();
    var hest = isNaN(num) ? $("#txtselwlwway").val() : $("#selwlw" + num).val();
    var len = $("#divtbody")[0].rows.length - 1;
    var fno = $($("#divtbody")[0].rows[len]).attr("fno");
    var strdate = $("#" + strdateid).val();
    if (type == 1) {
        var height = $.trim($("#" + heightid).val());
        if (!height || isNaN(height)) {
            jQuery.getparent().layer.msg('请输入数字！', function (r) {
                jQuery.getparent().layer.close(r);
            });
            $("#" + heightid).val("");
            $("#" + heightid).focus();
            if (num == "f" || num == "m") {//父母添加bmi
                var bmi = rebmi(height, weight);
                $("#lb" + num + "bmi").text(bmi);
            }
        } else if (parseFloat(height) >= 300 || height == 0) {
            jQuery.getparent().layer.msg('您输入的身高值不符合,请重新输入!', function (r) {
                jQuery.getparent().layer.close(r);
            });
            $("#" + heightid).val("");
            $("#" + heightid).focus();
        } else {
            height = parentobj.getOneFloat(height);
            var weight = $("#" + weightid).val();
            $("#" + heightid).val(height);
            if (!isNaN(num) || num == 'vest' || num == 'old') {
                if ($("#txtage" + num).attr("age")) {
                    var arrawhl = parentobj.tongsetting.awhlevel["a_" + $("#txtage" + num).attr("age") + "_" + fpsetting.strsex]; //年龄别身高
                    var agewh = "";
                    if(arrawhl){
                        agewh = parentobj.GetHW(height, [arrawhl[0], arrawhl[1], arrawhl[2], arrawhl[4], arrawhl[5], arrawhl[6], arrawhl[3]]);
                    }
                    $("#" + strageh).html(agewh);
                    if(!isNaN(num)){//判断文字显示
                        $("#agehjudge" + num).html(jQuery.getparent().GetHWJudge2(agewh, "H/A"));
                    }
                }
                if (weight != "") {
                    var arrhwle = parentobj.tongsetting.hwle["a_" + fpsetting.strsex + "_" + height + "_" + hest];
                    var strhw = "";
                    if (arrhwle) {
                        strhw = parentobj.GetHW(weight, [arrhwle[0], arrhwle[1], arrhwle[2], arrhwle[4], arrhwle[5], arrhwle[6], arrhwle[3]]);
                        var strfat = parentobj.GetFat(arrhwle[3], weight);

                        var strvalue = parentobj.getFloatValue(weight * 100 / arrhwle[3] - 100);
                        $("#" + strhewe).html(strhw);
                        if(!isNaN(num)){//判断文字显示
                            $("#hewejudge" + num).html(jQuery.getparent().GetHWJudge2(strhw, "W/H"));
                        }
                        $("#" + strfatid).html((parentobj.tongsetting.fatshow == 1 ? strfat : parentobj.tongsetting.objfatshow[strfat]) + (isNaN(num) ? "" : "<br />") + "(" + strvalue + "%)");
                        $("#" + strtxtid).val(strfat);
                        if (num == 'vest' && strfat != "正常") {
                            $("#selvest").val(3);
                        }
                        if (!isNaN(num)) {
                            var bmi = rebmi(height, weight);
                            $("#divbmi" + num).html(bmi);
                            // $("#txthewe" + num).val(strhw);
                        }
                        if (fno == num)
                            setHtml();
                    }
                    var age = $("#txtage" + num).attr("age") ||parentobj.GetAge(fpsetting.strbirthday, strdate);
                    var bmi = parentobj.getbmi(height, weight);
                    var strwfat = parentobj.getWFat(strhw, bmi, parentobj.tongsetting.strnormal, age, fpsetting.strsex);
                    $("#" + strwfatid).html(strwfat);
                }
            }
            if (num == "f" || num == "m") {//父母添加bmi
                var bmi = rebmi(height, weight);
                $("#lb" + num + "bmi").text(bmi);
            }
        }
    } else {
        var weight = $.trim($("#" + weightid).val());
        if (isNaN(weight)) {
            jQuery.getparent().layer.msg('请输入数字！', function (r) {
                jQuery.getparent().layer.close(r);
            });
            $("#" + weightid).val("");
            $("#" + weightid).focus();
        } else if (parseFloat(weight) > ((num == "f" || num == "m") ? 300 : 70) || parseFloat(weight) <= 0) {
            jQuery.getparent().layer.msg('请输入0至' + ((num == "f" || num == "m") ? 300 : 70) + '之间的数字！', function (r) {
                jQuery.getparent().layer.close(r);
            });
            $("#" + weightid).val("");
            $("#" + weightid).focus();
        } else {
            weight = parentobj.getFloatV(weight);
            // var lastnum = weight.charAt(weight.length - 1);//获取末尾数
            // if (lastnum != "0" && lastnum != 5) {
            //     jQuery.getparent().layer.msg('输入的体重可能有误，小数点最后一位应为0或5！');
            // }
            var height = $("#" + heightid).val();
            $("#" + weightid).val(weight);
            if (!isNaN(num) || num == 'vest' || num == 'old') {
                if ($("#txtage" + num).attr("age")) {
                    var arrawhl = parentobj.tongsetting.awhlevel["a_" + $("#txtage" + num).attr("age") + "_" + fpsetting.strsex]; //年龄别身高
                    var agewh = "";
                    if(arrawhl){
                        agewh = parentobj.GetHW(weight, [arrawhl[7], arrawhl[8], arrawhl[9], arrawhl[11], arrawhl[12], arrawhl[13], arrawhl[10]]);
                    }
                    $("#" + stragew).html(agewh);
                    if(!isNaN(num)){
                        $("#agewjudge" + num).html(jQuery.getparent().GetHWJudge2(agewh, "W/A"));
                    }
                }
                if (height != "") {
                    var arrhwle = parentobj.tongsetting.hwle["a_" + fpsetting.strsex + "_" + height + "_" + hest];
                    var strhw = "";
                    if (arrhwle) {
                        strhw = parentobj.GetHW(weight, [arrhwle[0], arrhwle[1], arrhwle[2], arrhwle[4], arrhwle[5], arrhwle[6], arrhwle[3]]);
                        var strfat = parentobj.GetFat(arrhwle[3], weight);

                        var strvalue = parentobj.getFloatValue(weight * 100 / arrhwle[3] - 100);
                        $("#" + strhewe).html(strhw);
                        if(!isNaN(num)){//判断文字显示
                            $("#hewejudge" + num).html(jQuery.getparent().GetHWJudge2(strhw, "W/H"));
                        }
                        $("#" + strfatid).html((parentobj.tongsetting.fatshow == 1 ? strfat : parentobj.tongsetting.objfatshow[strfat]) + (isNaN(num) ? "" : "<br />") + "(" + strvalue + "%)");
                        $("#" + strtxtid).val(strfat);
                        if (num == 'vest' && strfat != "正常") {
                            $("#selvest").val(3);
                        }
                        if (!isNaN(num)) {
                            var bmi = rebmi(height, weight);
                            $("#divbmi" + num).html(bmi);
                            // $("#txthewe" + num).val(strhw);
                        }
                        if (fno == num)
                            setHtml();
                    }
                    var age = $("#txtage" + num).attr("age") || parentobj.GetAge(fpsetting.strbirthday, strdate);
                    var bmi = parentobj.getbmi(height, weight);
                    var strwfat = parentobj.getWFat(strhw, bmi, parentobj.tongsetting.strnormal, age, fpsetting.strsex);
                    $("#" + strwfatid).html(strwfat);
                }
            }
            if (num == "f" || num == "m") {//父母添加bmi
                var bmi = rebmi(height, weight);
                $("#lb" + num + "bmi").text(bmi);
            }
        }
    }
}

/*
功能：保存  
*/
function btnsave(stno, cb) {
    if (!Validator.Validate($('#tabcard0').val(), 2)) {
        jQuery.getparent().layer.closeAll('loading');
        return;
    }
    var arrpm = [], startmanagedate = $("#txtolddate").val();
    if (startmanagedate && !parentobj.isDate(startmanagedate)) {
        jQuery.getparent().layer.closeAll('loading');
        jQuery.getparent().layer.msg("开始管理日期格式不正确，请重新输入！");
        return;
    }
    var len = $("#divtbody")[0].rows.length;
    var fno = $($("#divtbody")[0].rows[len - 2]).attr("fno"),
        chkvest = $("#chkvest")[0].checked ? 1 : 0;
    var enddate = $("#txtenddate").val();
    if (chkvest == 1 && !enddate) {//结案日期不能为空
        jQuery.getparent().layer.msg('请先选择结案日期，结案日期不能为空！');
        jQuery.getparent().layer.closeAll('loading');
        return;
    }
    if (enddate && !parentobj.isDate(enddate)) {
        jQuery.getparent().layer.closeAll('loading');
        jQuery.getparent().layer.msg("结案日期格式不正确，请重新输入！");
        return;
    }
    var strtip = '', arrdate = [];
    var curfat = '', curwufat = '';
    for (var i = 0; i < len; i++) {
        var num = $($("#divtbody")[0].rows[i]).attr("fno");
        var fno1 = $($("#divtbody")[0].rows[i]).attr("ftime");
        var date = $("#txtdate" + num).val(),
            tage = $("#txtage" + num).attr("age");
        if (date && (!parseFloat($.trim($("#txtweight" + num).val())) || !parseFloat($.trim($("#txtheight" + num).val())))) {//有日期，没有身高，体重
            strtip += '第' + (i + 1) + '行信息不完整，数据将不会被处理</br>';
        } else if (parseFloat($.trim($("#txtweight" + num).val())) && (!date || !parseFloat($.trim($("#txtheight" + num).val())))) {//有体重，没有日期或身高
            strtip += '第' + (i + 1) + '行信息不完整，数据将不会被处理</br>';
        } else if (parseFloat($.trim($("#txtheight" + num).val())) && (!date || !parseFloat($.trim($("#txtweight" + num).val())))) {//有身高，没有日期或体重
            strtip += '第' + (i + 1) + '行信息不完整，数据将不会被处理</br>';
        }

        if (date && $.inArray(date, arrdate) >= 0) {
            jQuery.getparent().layer.msg("第" + (i + 1) + "行日期" + date + "与其它行日期重复，请检查！", {area: '400px'});
            jQuery.getparent().layer.closeAll('loading');
            return;
        } else if (date) {
            if (!parentobj.isDate(date)) {
                jQuery.getparent().layer.closeAll('loading');
                jQuery.getparent().layer.msg('第' + (i + 1) + '行日期格式不正确，请重新输入!');
                return;
            }
            arrdate.push(date);
        }
        if (!parseFloat($.trim($("#txtweight" + num).val())) || !parseFloat($.trim($("#txtheight" + num).val()))) {// || tage >= 7
            continue;
        }
        var strpm = '';
        if (date != "") {
            var arr = ["fpzacard.addmanage", fpsetting.curstuno, date, $("#txtage" + num).attr("age") || 0, $("#selwlw" + num).val(), ($("#txtheight" + num).val() == "" ? 'null' : $("#txtheight" + num).val()), ($("#txtweight" + num).val() == "" ? 'null' : $("#txtweight" + num).val()), $("#txtfat" + num).val()];
            // for (var m = 1; m < 15; m++) {
            //     arr.push($("#selsport_" + m + "_" + num).val());
            // }
            arr.push($("#txtproblem" + num).val());
            arr.push($("#txtidea" + num).val());
            arr.push($("#txtdoctor" + num).val());
            arr.push($("#divwfat" + num).html());
            arr.push($("#txtagew" + num).html());
            arr.push($("#txtageh" + num).html());
            arr.push($("#txthewe" + num).html());
            arr.push((fno1 ? fno1 : date))
            arrpm.push(arr);
            curfat = $("#txtfat" + num).val();
            curwufat = $("#divwfat" + num).html();
        }
    }
    if (chkvest == 1 && enddate) {//结案
        curfat = $("#txtvestfat").val();
        curwufat = $("#divvestwufat").html();
    }
    arrpm.push(["fpzacard.addfat", ($("#txtclass").html()), ($("#txtfheight").val() == "" ? 'null' : $("#txtfheight").val()), ($("#txtfweight").val() == "" ? 'null' : $("#txtfweight").val()), ($("#txtmheight").val() == "" ? 'null' : $("#txtmheight").val()), ($("#txtmweight").val() == "" ? 'null' : $("#txtmweight").val()), $("#txtotherfat").val(), $("#txtcondition").val(), ($("#txtoldheight").val() == "" ? 'null' : $("#txtoldheight").val()), ($("#txtoldweight").val() == "" ? 'null' : $("#txtoldweight").val()), $("#txtoldfat").val(), $.msgwhere({startmanagedate: [startmanagedate || ""]}), $("#txtfat" + fno).val(), $("#selmt").val(), $("#divwfat" + fno).html(), $("#divoldwufat").html(), fpsetting.strbirthday, fpsetting.strsex, curfat, curwufat, fpsetting.curstuno])

    arrpm.push(["fpzacard.addfat1", chkvest, $.msgwhere({enddate: [enddate || ""]}), ($("#txtvestheight").val() == "" ? 'null' : $("#txtvestheight").val()), ($("#txtvestweight").val() == "" ? 'null' : $("#txtvestweight").val()), $("#txtvestfat").val(), $("#selvest").val() || 'null', $("#divvestwufat").html(), fpsetting.curstuno]);
    if (chkvest == 1 && enddate) {//结案。记录结案记录stuno,stuname,stusex,claname,endtime,endheight,endweight,rank,vest,wrank,mt,casetype
        arrpm.push(["fpzacard.addclosecaserecord", fpsetting.curstuno, fpsetting.curstuname, fpsetting.strsex, $("#txtclass").html(), enddate, ($("#txtvestheight").val() == "" ? 'null' : $("#txtvestheight").val()), ($("#txtvestweight").val() == "" ? 'null' : $("#txtvestweight").val()), $("#txtvestfat").val(), $("#selvest").val() || 'null', $("#divvestwufat").html(), $("#selmt").val()]);
    }

    if (strtip) {
        jQuery.getparent().layer.confirm(strtip + '请确定是否真的进行保存操作?', function (r) {
            btsavesm(arrpm, stno, r, cb);
        }, function (r) {
            jQuery.getparent().layer.closeAll('loading');
        });
    } else {
        btsavesm(arrpm, stno, null, cb);
    }
}

function btsavesm(arrpm, stno, r, cb) {
    $.sm(function (re, err) {
        if (err) {
            jQuery.getparent().layer.msg(err, {icon: 5});
            jQuery.getparent().layer.closeAll('loading');
        } else {
            if (stno) {
                initclassdata(stno);
                $('#txtvestheight').prop('disabled', false);
                $('#txtvestweight').prop('disabled', false);
                $('#txtenddate').prop('disabled', false);
                $('#selvest').replaceWith('<label id="selvest"></label>');
                var arrdata = [['痊愈', "0"], ['好转', "1"], ['转医院', "2"], ['未愈', "3"], ['离园', "4"]];
                $('#selvest').editableSelect({
                    bg_iframe: true,
                    isreadonly: 1,
                    arrdata: arrdata,
                    deftext: '痊愈',
                    defvalue: "0", //默认值
                    width: '50px',
                    case_sensitive: false, // If set to true, the user has to type in an exact
                    items_then_scroll: 10// If there are more than 10 items, display a scrollbar
                });
            }
            jQuery.getparent().layer.closeAll('loading');
            jQuery.getparent().layer.msg('保存成功！');
            cb && cb();
            if (r)
                jQuery.getparent().layer.close(r);
        }
    }, arrpm, null, null, null, null, 1);
}

function rebmi(height, weight) {
    return height == 0 || !weight ? "" : parseInt((weight / ((height / 100) * (height / 100))) * 1000) / 1000;
}