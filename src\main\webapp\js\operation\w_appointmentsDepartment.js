/*新增日期: 2025.06.27
作 者: 朱辽原
修改时间：
修改人：
內容摘要: 预约科室数据详情
*/
require.config({
    paths: {
        system: '../../sys/system',
        jquery: '../../sys/jquery',
        layui: '../../layui-btkj/layui',
        dataSource: './enterData',
        common: './common',
        echarts: '../../plugin/echart-5.1.2/dist/echarts.min',
        commonUtils: 'commonUtils'
    },
    shim: {
        system: {
            deps: ['jquery']
        },
        layui: {
            deps: ['jquery', 'system']
        },
        common: {
            deps: ['echarts']
        }
    },
    waitSeconds: 0
})

const openMock = false

const baseModule = ['jquery', 'commonUtils', 'layui', 'system', 'common']

if (openMock) {
    baseModule.push('dataSource')
}

require(baseModule, ($, utils) => {
    const openLog = true
    layui.config().extend({
        system: '../../sys/system',
    });
    layui.use(['system', 'form', 'table', 'laydate'], function () {
        layer = layui.layer;
        form = layui.form;
        laydate = layui.laydate;
        const params = Arg.all()
        initDatePicker()
        initSelectData();
        initEvent();
        initTable();

        /*
         * 初始化日期选择器
         */
        function initDatePicker() {
            const isoDate = new Date().toISOString();
            const nowDate = isoDate.split('T')[0];
            // 开始日期
            laydate.render({
                elem: '#startDate',
                type: 'date',
                value: params.startDate || nowDate
            });
            // 结束日期
            laydate.render({
                elem: '#endDate',
                type: 'date',
                value: params.endDate || nowDate
            });
        }

        function initSelectData() {
            // 预约科室数据
            if (openMock) {
                var deptData = [
                    { id: '', name: '请选择' },
                    { id: '儿科', name: '儿科' },
                    { id: '耳鼻喉科', name: '耳鼻喉科' },
                    { id: '眼科', name: '眼科' },
                    { id: '口腔科', name: '口腔科' },
                    { id: '保健科', name: '保健科' }
                ];
                var deptSelect = $('#departmentName');
                deptSelect.empty();
                deptData.forEach(function (item) {
                    deptSelect.append('<option value="' + item.id + '">' + item.name + '</option>');
                });
                deptSelect.val(params.deptId || '');
            } else {
                $.sm(function (re1, err1) {
                    if (re1) {
                        utils.logWithCondition(openLog, new Error().stack, re1);
                        const deptList = re1
                        var deptSelect = $('#deptName');
                        deptSelect.empty();
                        deptSelect.append(`<option value=''>请选择</option>`);
                        deptList.forEach(function (item) {
                            deptSelect.append(`<option value='${item.id}'>${item.nname}</option>`);
                        });
                        utils.logWithCondition(openLog, new Error().stack, params);
                        deptSelect.val(params.deptId || '');
                    } else {
                        // jQuery.getparent().layer.msg(err1, { icon: 5 });
                    }
                }, ["depart.list"], null, null, { async: false });
            }
        }
        // 预约科室数据
        if (openMock) {
            var deptData = [
                { id: '', name: '请选择' },
                { id: '儿科', name: '儿科' },
                { id: '耳鼻喉科', name: '耳鼻喉科' },
                { id: '眼科', name: '眼科' },
                { id: '口腔科', name: '口腔科' },
                { id: '保健科', name: '保健科' }
            ];
            var deptSelect = $('#departmentName');
            deptSelect.empty();
            deptData.forEach(function (item) {
                deptSelect.append('<option value="' + item.id + '">' + item.name + '</option>');
            });
            deptSelect.val(params.deptId || '');
        } else {
            $.sm(function (re1, err1) {
                if (re1) {
                    utils.logWithCondition(openLog, new Error().stack, re1);
                    const deptList = re1
                    var deptSelect = $('#departmentName');
                    deptSelect.empty();
                    deptSelect.append(`<option value=''>请选择</option>`);
                    deptList.forEach(function (item) {
                        deptSelect.append(`<option value='${item.id}'>${item.nname}</option>`);
                    });
                    utils.logWithCondition(openLog, new Error().stack, params);
                    deptSelect.val(params.deptId || '');
                } else {
                    // jQuery.getparent().layer.msg(err1, { icon: 5 });
                }
            }, ["depart.list"], null, null, { async: false });
            form.render('select');
        }

        function initEvent() {
            $("#btnsearch").click(function () {
                btnsearch();
            });
            $("#closeBtn").click(function () {
                parent.layer.closeAll();
            });
        }

        function initTable() {
            var objwhere = getswhere();
            var arrcol = [ // 表格列定义匹配图片内容
                { field: 'id', type: "numbers", title: '序号', align: 'left' },
                { field: 'departmentname', title: '预约科室', align: 'center' },
                {
                    field: 'successcount', title: '预约成功数', align: 'center',
                    templet: function (d) {
                        if (d.successcount > 0) {
                            let param = JSON.stringify({
                                deptId: openMock ? d.departmentname : d.dept_id,
                                count: +d.successcount,
                                startDate: params.startDate,
                                endDate: params.endDate,
                                // isCancel: 0
                            })
                            param = encodeURIComponent(param)
                            return `<span onclick="pageJumpToTable('parentReservationInfo.html', '${param}', '家长预约详情')" style="color:#02baf6; cursor: pointer"">
                                        ${d.successcount}
                                    </span>`;
                        }
                        return 0
                    }
                },
                {
                    field: 'cancelcount', title: '取消预约数', align: 'center',
                    templet: function (d) {
                        if (d.cancelcount > 0) {
                            let param = JSON.stringify({
                                deptId: openMock ? d.departmentname : d.dept_id,
                                count: +d.cancelcount,
                                startDate: params.startDate,
                                endDate: params.endDate,
                                isCancel: '1'
                            })
                            param = encodeURIComponent(param)
                            return `<span onclick="pageJumpToTable('parentReservationInfo.html', '${param}', '家长预约详情')" style="color:#02baf6; cursor: pointer"">
                                        ${d.cancelcount}
                                    </span>`;
                        }
                        return 0
                    }
                }
            ];
            layui.table.render({
                elem: '#laytable',
                url: encodeURI($.getLayUrl() + "?" + $.getSmStr(["departmentReservation.list"])),
                height: 'full-50',
                where: {
                    swhere: $.msgwhere(objwhere),
                    fields: 'rnoi.dept_id',
                    types: 'asc'
                },
                cols: [arrcol],
                done: function (res, curr, count) {
                    // 表格加载完成回调
                },
                countNumberBool: true,
                even: true,
                page: true,
                limits: [30, 50, 100, 200],
                limit: 30,
                skin: 'row'
            });
            layui.table.on('tool(laytable)', function (obj) {
                // 工具条事件处理
            });
        }

        function btnsearch() {
            var objwhere = getswhere();
            layui.table.reload('laytable', {
                page: { curr: 1 },
                where: {
                    swhere: $.msgwhere(objwhere),
                    fields: 'rnoi.dept_id',
                    types: 'asc'
                }
            });
        }

        function getswhere() {
            var objwhere = {};
            var startDate = $("#startDate").val() || params.startDate;
            var endDate = $("#endDate").val() || params.endDate;
            var departmentName = $("#departmentName").val();
            var keyword = $("#keyword").val();
            utils.logWithCondition(openLog, new Error().stack, startDate, endDate)

            // 处理日期范围
            if (startDate) {
                objwhere.startDate = [startDate];
            }
            if (endDate) {
                objwhere.endDate = [endDate];
            }
            if (departmentName) {
                objwhere.departmentName = [departmentName];
            }
            if (keyword) {
                objwhere.keyword = [keyword];
            }
            if (params.regional) {
                objwhere.regional = [params.regional]
            }
            utils.logWithCondition(openLog, new Error().stack, { ...params, ...objwhere })
            return { ...params, ...objwhere };
        }

        /*
        功能：页面跳转
        参数说明：param - URL 中的参数
        返回值说明：无
        */
        window.pageJumpToTable = utils.pageJumpToTable
    });

})
