<!DOCTYPE html>
<html>
<head>
    <title>新增上报</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <link rel="stylesheet" href="../css/reset.css" />
    <link rel="stylesheet" href="../css/style.css" />
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../styles/xbcomstyle.css">
    <!--[if lt IE 9]>
    <script src='sys/html5shiv.min.js'></script>
    <![endif]-->
    <style>
        body { background: #F4F5F7; }

        .div_btn { position: fixed; bottom: 25px; left: 25%; }

        /*月份季度*/
        .rate_second, .rate_month, .rate_jd, .sp_forver { display: none; }

        .incr-chat .layui-form-label { width: 165px; }

        .WdateDiv .MTitle { position: inherit; }
        .incr-chat .div_field .layui-form-label { background: #fff; }
        .n-msg { color: red; }
    </style>
</head>
<body>
    <!--按次-->
    <section class="incr-chat" style="">
        <div class="site-text site-block div_field">
            <div id="form" class="layui-form" lay-filter="formOk">
                <!--选择频次-->
                <div class="layui-form-item" style="display: none;">
                    <label class="layui-form-label"><i class="layui-mark-red">*</i>选择上报频次：</label>
                    <div class="layui-input-block">
                        <input lay-filter="radrate" type="radio" name="radrate" value="second" title="年/半年" checked="checked" />
                        <input lay-filter="radrate" type="radio" name="radrate" value="month" title="按月" />
                        <input lay-filter="radrate" type="radio" id="radratequarter" name="radrate" value="quarter" title="按季" />
                    </div>
                    <div class="layui-form-item" style="display: none;">
                        <label class="layui-form-label" style="height: 50px"></label>
                        <div class="layui-input-block" style="margin-top: 10px; margin-left: 194px;">
                            <div class="layui-input-inline" style="width: 130px;">
                                <select name="endmonth" lay-filter="endmonth">
                                    <option value="1">月前</option>
                                    <option value="2">月末</option>
                                </select>
                            </div>
                            <div class="layui-input-inline" style="width: 130px;">
                                <input type="text" id="txtendmonth" required="" placeholder="天数" autocomplete="off" class="layui-input"
                                    style="border: none; border-bottom: 1px solid #E6E6E6;">
                            </div>
                            <span class="layui-form-mid layui-word-aux">（每到<i class="ititle">月前</i><i class="red">0</i>天为上报截止时间）</span>
                        </div>
                    </div>
                    <div class="layui-form-item" style="display: none;">
                        <label class="layui-form-label" style="height: 50px"></label>
                        <div class="layui-input-block" style="margin-top: 10px; margin-left: 194px;">
                            <div class="layui-input-inline" style="width: 130px;">
                                <select name="endjd" lay-filter="endjd">
                                    <option value="1">季前</option>
                                    <option value="2">季末</option>
                                </select>
                            </div>
                            <div class="layui-input-inline" style="width: 130px;">
                                <input type="text" id="txtendjd" required="" placeholder="天数" autocomplete="off" class="layui-input"
                                    style="border: none; border-bottom: 1px solid #E6E6E6;">
                            </div>
                            <span class="layui-form-mid layui-word-aux">（每到<i class="ititle">季前</i><i class="red">0</i>天为上报截止时间）</span>
                        </div>
                    </div>
                </div>
                <!--上报园所范围-->
                <div id="div_yey" class="layui-form-item">
                    <label class="layui-form-label"><i class="layui-mark-red">*</i>选择上报的园所类型：</label>
                    <div class="layui-input-inline" style="width: 154px;">
                        <select lay-filter="yeytype" name="yeytype" lay-verify="required" disabled="disabled">
                            <option value="">请选择</option>
                            <option value="1">幼儿园</option>
                            <option value="3">托育机构</option>
                        </select>
                    </div>
                    <div class="layui-input-inline" style="width: 154px; margin-left: 0px; display: none;">
                        <select lay-filter="classification" name="classification" lay-verify="">
                        </select>
                    </div>
                    <span class="layui-form-mid layui-word-aux">（已关联此类别的园所<i class="red">0</i>园）</span>
                </div>
                <!--选择上报类型-->
                <div class="layui-form-item">
                    <label class="layui-form-label"><i class="layui-mark-red">*</i>选择上报类型：</label>
                    <div class="layui-input-inline" style="width: 318px; margin-top: 5px;">
                        <div id="selreportType" class="xm-select-demo" style="display: inline-block; width: 100%;"></div>
                    </div>
                </div>
                <!--上报名称-->
                <div class="layui-form-item">
                    <label class="layui-form-label"><i class="layui-mark-red">*</i>上报名称：</label>
                    <div id="divreport">
                    </div>
                </div>
                <!--是否有上报截止时间，根据选择频次改变-->
                <div class="layui-form-item" style="display: none;">
                    <label class="layui-form-label"><i class="layui-mark-red">*</i>该上报是否有截止时间：</label>
                    <div class="layui-input-inline" style="width: 250px;">
                        <input lay-filter="radisend" type="radio" name="radisend" value="1" title="是" checked="checked">
                        <input lay-filter="radisend" type="radio" name="radisend" value="0" title="否">
                        <span class="layui-form-mid layui-word-aux sp_forver" style="position: absolute; top: 2px; left: 110px;">（长期有效）</span>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><i class="layui-mark-red">*</i><label id="labjdmonthname"></label>：</label>
                    <div class="layui-input-inline" style="width: 80px;">
                        <input id="txtyear" type="text" class="layui-input" autocomplete="off" style="display: inline-block; width: 80px;" readonly="readonly" />
                    </div>
                    <div class="layui-input-inline">
                        <select id="seljdmonth">
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><i class="layui-mark-red">*</i>选择上报截止日期：</label>
                    <div class="layui-input-inline">
                        <input type="text" id="txtendtate" placeholder="上报截止日期" autocomplete="off" class="layui-input" style="width: 200px;" readonly lay-verify="required" />
                        <img id="btnimgrecond" src="../images/cal_icon.png" style="float: right; margin: -29px -5px;">
                    </div>
                    <span id="divcount1" class="layui-form-mid layui-word-aux" style="display: none;">（共需上报<i class="red" id="labcount">0</i>次）</span>
                </div>
                <div class="div_btn" style="display: none;">
                    <button id="btnok" class="layui-btn bluebtn">保存并发布</button>
                </div>
            </div>
        </div>
    </section>
    <script data-main="../js/reportadd" src="../sys/require.min.js"></script>
</body>
</html>
