<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>生命周期设置</title>

    <link rel="stylesheet" href="../../plugin/zTree/css/zTreeStyle/zTreeStyle.css" type="text/css">
    <link rel="stylesheet" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../css/style.css"/>
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../styles/tbstyles.css"/>
    <link rel="stylesheet" type="text/css" href="../../css/commonstyle-layui-btkj.css"/>
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + top.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <script src="../../sys/html5shiv.min.js"></script>

    <style>
        html, body {
            background: #EAEFF3;
        }

        table.appo-table th, table td {
            padding: 0px;
        }

        .popRight {
            margin-left: 260px;
            background: #fff;
            min-height: calc(100vh - 28px);
        }

        .title-span {
            margin-left: 20px;
            color: #8A8A8A;
            font-size: 15px;
            font-weight: bold;
        }

        /*按钮被禁用时的样式*/
        .layui-btn.disabled-btn {
            background-color: #ccc;
            color: #888;
            opacity: 0.6;
            cursor: not-allowed;
        }

        .popRight {
            margin-left: 260px;
        }

    </style>
</head>
<body>
<div class="marmain">
    <section class="treeWrap" style="position:fixed;background: #fff;color: #34495E;left:8px;">
        <div class="content_wrap">
            <div class="zTreeDemoBackground left">
                <ul id="treeDemo" class="ztree"></ul>
            </div>
        </div>
    </section>
    <section class="popRight" style="padding:10px 0 ;display:none;">
        <div class="layui-form" style="padding: 10px;" lay-filter="formOk">
            <div class="layui-form-item">
                <label class="layui-form-label" style="float: left;"><em>*</em>类型：</label>
                <div class="layui-input-block" style="min-height: 30px;float: left;width: 200px; margin-left:0px;">
                    <select type="text" id="stu_select" autocomplete="off" lay-filter="stu_select"
                             lay-verify="required" class="layui-select" >
                        <option value="">请选择</option>
                        <option value="1">婚检</option>
                        <option value="2">建册</option>
                        <option value="3">分娩</option>
                        <option value="4">入园体检</option>
                        <option value="5">在园体检</option>
                        <option value="6">门诊</option>
                    </select>
                </div>
            </div>
        </div>
        <div id="childnode-box">
            <p class="title-span">预约门诊：</p>
            <div style="padding: 10px">
                <div id="Opinionkey" class="xm-select-demo"></div>
            </div>
        </div>
        <div>
            <button id="btnadd" class="layui-btn bluebtn btnoperate" lay-submit="" lay-filter="formDemo"
                    style="background:#4A90E2 !important; color: #fff !important;width: 140px; height: 48px; line-height: 48px; margin-left: 10px;">
                保存
            </button>
        </div>

    </section>
</div>
<script type="text/javascript" src="../../layui-btkj/layui.js"></script>
<script type="text/javascript" src="../../sys/jquery.js"></script>
<script type="text/javascript" src="../../sys/arg.js"></script>
<script type="text/javascript" src="../../plugin/zTree/js/jquery.ztree.core-3.5.js"></script>
<script type="text/javascript" src="../../plugin/zTree/js/jquery.ztree.excheck-3.5.js"></script>
<script type="text/javascript" src="../../plugin/zTree/js/jquery.ztree.exedit-3.5.js"></script>
<script type="text/javascript" data-main="../../js/prem/lifecycle" src="../../sys/require.min.js"></script>

</body>
</html>
