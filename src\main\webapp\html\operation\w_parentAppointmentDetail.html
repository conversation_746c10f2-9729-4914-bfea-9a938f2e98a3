<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>家长预约详情</title>
    <link rel="stylesheet" type="text/css" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" type="text/css" href="../../css/icon.css"/>
    <link rel="stylesheet" type="text/css" href="../../styles/tbstyles.css"/>
    <link href="../../plugin/flexigrid/css/tbflexigrid.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css"/>
    <script type="text/javascript">
        // 检查 top.objdata 是否存在，如果不存在则设置默认主题
        var curtheme = (top && top.objdata && top.objdata.curtheme) ? top.objdata.curtheme : 'backstage';
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + curtheme + '.css" type="text/css" media="screen"/>');
    </script>

    <style type="text/css">
        html, body {
            background: #EAEFF3;
            overflow: hidden;
        }
        .layui-form-item {
            display: inline-block;
            vertical-align: top;
            margin: 5px 5px 0px 0;
        }
        .layui-form-label {
            line-height: 28px;
            padding: 0 10px;
            width: auto;
        }
        .layui-input-inline {
            margin-right: 5px !important;
        }

        /* 添加分页栏按钮样式 */
        .layui-table-page {
            display: flex !important;
            align-items: center;
            padding: 0 10px;
            position: relative;
        }

        #btnclose {
            background-color: #FF5722 !important;
            color: #fff !important;
            position: absolute !important;
            left: 50% !important;
            transform: translateX(-50%) !important;
            top: 50% !important;
            margin-top: -14px !important;
            z-index: 1 !important;
            height: 28px !important;
            line-height: 28px !important;
            padding: 0 15px !important;
            font-size: 12px !important;
            border-radius: 2px !important;
        }
    </style>
</head>
<body>
<div class="marmain">
    <div class="content-medical">
        <div class="layui-form layui-comselect" style="padding:10px;">
            <!-- 第一行 -->
            <div class="layui-form-item" style="margin-bottom: 10px;">
                <div class="layui-input-inline" style="width:120px;">
                    <input id="datestart" type="text" autocomplete="off" placeholder="开始日期" class="layui-input">
                </div>
                <div class="layui-form-mid">至</div>
                <div class="layui-input-inline" style="width:120px;">
                    <input id="dateend" type="text" autocomplete="off" placeholder="结束日期" class="layui-input">
                </div>
                <label class="layui-form-label">预约科室：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="department" lay-filter="department">
                        <option value="">请选择</option>
                    </select>
                </div>
                <label class="layui-form-label">预约套餐：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="appointmentType" lay-filter="appointmentType">
                        <option value="">请选择</option>
                    </select>
                </div>
                <label class="layui-form-label">是否取消预约：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="isCancelled" lay-filter="isCancelled">
                        <option value="">请选择</option>
                    </select>
                </div>
            </div>
            
            <!-- 第二行 -->
            <div class="layui-form-item" style="margin-bottom: 10px;">
                <label class="layui-form-label">幼儿园：</label>
                <div class="layui-input-inline" style="width:120px;">
                    <select id="kindergarten" lay-filter="kindergarten">
                        <option value="">请选择</option>
                    </select>
                </div>
                <label class="layui-form-label">年级：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="grade" lay-filter="grade">
                        <option value="">请选择</option>
                    </select>
                </div>
                <label class="layui-form-label">班级：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="class" lay-filter="class">
                        <option value="">请选择</option>
                    </select>
                </div>
                <label class="layui-form-label">幼儿性别：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="childGender" lay-filter="childGender">
                        <option value="">请选择</option>
                    </select>
                </div>
                <label class="layui-form-label">体检年龄：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="checkupAge" lay-filter="checkupAge">
                        <option value="">请选择</option>
                    </select>
                </div>
            </div>

            <!-- 第三行 -->
            <div class="layui-form-item" style="margin-bottom: 10px;">
                <label class="layui-form-label">家长性别：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="parentGender" lay-filter="parentGender">
                        <option value="">请选择</option>
                    </select>
                </div>
                <label class="layui-form-label">家长年龄：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="parentAge" lay-filter="parentAge">
                        <option value="">请选择</option>
                    </select>
                </div>
                <label class="layui-form-label">访问设备：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="device" lay-filter="device">
                        <option value="">请选择</option>
                    </select>
                </div>
                <label class="layui-form-label">区域分布：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="region" lay-filter="region">
                        <option value="">请选择</option>
                    </select>
                </div>
                <label class="layui-form-label" style="width: auto; min-width: 120px;">孩子与家长关系：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="relationship" lay-filter="relationship">
                        <option value="">请选择</option>
                    </select>
                </div>
                <div class="layui-input-inline" style="width:170px;">
                    <input type="text" id="keyword" placeholder="请输入幼儿姓名/家长姓名" class="layui-input">
                </div>
                <button class="layui-btn form-search" id="btnsearch">查询</button>
            </div>
        </div>
    </div>
    <div id="content" style="width: 100%;">
        <div class="marmain-cen">
            <div class="content-medical" id="divtable">
                <table id="laytable" lay-filter="laytable"></table>
            </div>
        </div>
    </div>
</div>

<script data-main="../../js/operation/w_parentAppointmentDetail" src='../../sys/require.min.js'></script>
</body>
</html> 