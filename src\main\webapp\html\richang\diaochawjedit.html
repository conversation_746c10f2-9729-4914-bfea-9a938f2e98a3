<!DOCTYPE html>
<html>
<head>
    <title>调查问卷管理-编辑</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <link rel="stylesheet" href="../../css/reset.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/style.css"/>
    <link rel="stylesheet" href="../../css/xixian.css">
    <script type="text/javascript" src="../../layui-btkj/layui.js"></script>
    <style>
        html, body {
            height: 100%;
            background: #F7FAFC;
            overflow-y: hidden;
        }

        .non_border {
            border: none;
        }
        .hide {
            display: none;
        }
        .questionnaire-title.choosed {
            border: 1px dashed #1665d7;
        }
        .questionnaire-title {position: relative;}
        .questionnaire-title .switchhide{display: none;}
        .questionnaire-title.choosed .switchhide{
            display: inline-block;
        }
        .editdel.switchhide{
            position: absolute;
            top: 18px;
            right: 23px;
        }
        .option_oper span,.option_oper label {cursor: pointer;}
		.labcantiankong{margin-right: 10px;}
        .wentititle{width:35%;margin-left:15px;}
        .wentititle::-webkit-scrollbar {
            width: 0; /* 隐藏滚动条 */
        }
        /*.wentititle:hover::-webkit-scrollbar {*/
        /*    width: 10px; !* 鼠标 hover 时显示滚动条 *!*/
        /*}*/
        .xuhao{display: inline-block;position: absolute;top: 19px;}
    </style>
</head>
<body>
<section>
    <div class="allcontent-col3" style="position: absolute;top: 0;">
        <div class="allcontent-col3left" style="width: 204px;">
            <div style="margin: 10px 15px;">
                <ul class="default-tabsel">
                    <li>题型样式</li>
                </ul>
                <p id="btnimport" class="mobantxt" style="cursor:pointer;">导入模版</p>
                <ul class="choose-txt">
                    <li id="addSelect"><img src="../../images/wenjuan/rodioimg.png">单选题</li>
                    <li id="addMulti"><img src="../../images/wenjuan/selectimg.png">多选题</li>
                    <li id="addQuestion"><img src="../../images/wenjuan/answer-txt.png">问答题</li>
                    <li id="addStar"><img src="../../images/wenjuan/xing.png">评星题</li>
                </ul>
            </div>
        </div>
        <div class="allcontent-col3cen" style="overflow-y: auto;">
            <div class="questionnaire-title">
                <form class="layui-form">
<!--                    <div class="layui-form-item">-->
<!--                        <label class="layui-form-label">群发对象：</label>-->
<!--                        <div class="layui-input-block">-->
<!--                            <select id="sendobj" name="sendobj" lay-filter="sendobj" class="layui-select">-->
<!--                                <option value="select">单选</option>-->
<!--                                <option value="multi">多选</option>-->
<!--                                <option value="question">问答</option>-->
<!--                                <option value="star">评星</option>-->
<!--                            </select>-->
<!--                        </div>-->
<!--                    </div>-->
                    <div class="layui-form-item">
                        <label class="layui-form-label"><i class="layui-mark-red"></i>问卷标题：</label>
                        <div class="layui-input-block">
                            <input id="wtitle" name="wtitle" lay-verify="required" maxlength="100" autocomplete="off" placeholder="请输入问卷标题" class="layui-input" type="text" style="float: left">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><i class="layui-mark-red"></i>问卷说明：</label>
                        <div class="layui-input-block">
                            <textarea id="introduce" name="introduce" lay-verify="required" autocomplete="off" placeholder="请填写问卷说明内容（最多500字，可不填）" class="layui-input" type="text" style="height:80px;"></textarea>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">是否匿名答卷：</label>
                        <div class="layui-input-block" style="padding-top: 3px;">
                            <input lay-filter="isniming" name="isniming" value="1" title="是" type="radio">
                            <input lay-filter="isniming" name="isniming" value="0" title="否" type="radio" checked="checked">
                        </div>
                    </div>
                </form>
            </div>
            <div id="arrquestion">

            </div>
                <!--点击题型-->
            <div id="tip" class="questionstxt" style="display: none;">
                <p>
                    <span><img src="../../images/wenjuan/click.png" style="margin-right: 6px;">点击题型添加问题</span>
                </p>
            </div>
        </div>
        <div class="allcontent-col3right" style="">
            <div style="margin: 10px 15px;">
                <h3 class="topic-txt">题目设置</h3>
                <form class="layui-form default-form checkbox-blue radio-blue" action="">
                    <div class="layui-form-item">
                        <label class="layui-form-label">切换题型：</label>
                        <div class="layui-input-block">
                            <select id="wentitype" name="wentitype" lay-filter="wentitype" class="layui-select">
                                <option value="select">单选</option>
                                <option value="multi">多选</option>
                                <option value="question">问答</option>
                                <option value="star">评星</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">关联ID：</label>
                        <div class="layui-input-block" style="">
                            <select id="zhibiaoid" name="zhibiaoid" lay-filter="zhibiaoid" class="layui-select">
                                <option value="">请选择</option>
                                <option value="1">ID001 服务价格满意度-公示</option>
                                <option value="2">ID002 服务价格满意度-有无乱收费</option>
                                <option value="3">ID003 托育服务内容满意度-家长参与率</option>
                                <option value="4">ID004 托育服务内容满意度-活动内容满意度</option>
                                <option value="5">ID005 托育服务内容满意度-一日活动安排合理度</option>
                                <option value="6">ID006 照护压力满意度</option>
                            </select>
                        </div>
                    </div>
<!--                    <div class="layui-form-item">-->
<!--                        <label class="layui-form-label">显示输入框：</label>-->
<!--                        <div class="layui-input-block">-->
<!--                            <input name="close" lay-skin="switch" lay-text="" type="checkbox">-->
<!--                        </div>-->
<!--                    </div>-->
                    <div class="layui-form-item">
                        <label class="layui-form-label">此题必答：</label>
                        <div class="layui-input-block">
                            <input id="ismust" name="ismust" lay-filter="ismust" lay-skin="switch" lay-text="" type="checkbox">
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/function.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../js/richang/diaochawjedit.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>