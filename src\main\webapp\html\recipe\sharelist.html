<!DOCTYPE html>
<html>
<head>
    <title>用户管理</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <link rel="stylesheet" href="../../css/reset.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <style>
        html, body { height: 100%; background: #fff; overflow-y: hidden; }
    </style>
</head>
<body>
    <section>
        <!--<div id="h_head" class="layui-form layui-comselect cuptop divsearch" >
            <div class="layui-tab-brief">
                <div class="layui-form layui-comselect" style="display: inline-block;">
                    <div class="def-search" style="display: inline-block;vertical-align: top;width: 200px;margin: 0px 10px 0 0; height: 28px;">
                        <input type="text" id="txtkey" placeholder="请输入关键字" class="layui-input">
                    </div>
                    <div class="layui-inline" style="display: inline-block;vertical-align: top;margin-left: 8px;">
                        <span id="btnList">
                            <button class="layui-btn" id="btnSearch" data-type="search"><i class="layui-icon layui-icon-search">查询</i></button>
                        </span>
                    </div>
                </div>
            </div>
        </div>-->
        <div class="cuptop">
            <table id="laytable" lay-filter="laytable" class="layui-table"></table>
        </div>
    </section>
    <script type="text/javascript">
        var v = top.version;
        document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
        document.write("<script type='text/javascript' src='../../js/recipe/sharelist.js?v=" + v + "'><" + "/script>");
    </script>
</body>
</html>