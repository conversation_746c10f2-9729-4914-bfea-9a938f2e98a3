﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8"/>
    <title>便民服务牌数据详细</title>
    <link rel="stylesheet" type="text/css" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" type="text/css" href="../../css/icon.css"/>
    <link rel="stylesheet" type="text/css" href="../../styles/tbstyles.css"/>
    <link href="../../plugin/flexigrid/css/tbflexigrid.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css"/>
    <!-- <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + top.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script> -->
    <style type="text/css">
        html,
        body {
            background: #EAEFF3;
            overflow: hidden;
        }

        .layui-form-item {
            display: inline-block;
            vertical-align: top;
        }

        .layui-form-label {
            line-height: 28px;
            width: 120px;
        }

        .layui-input-inline{
            width:180px;
        }
    </style>
</head>

<body>
<div class="marmain">
    <div class="content-medical">
        <div class="search-container layui-form layui-comselect">
            <label class="layui-form-label">预约挂号时间：</label>
            <div class="layui-form-item">
                <div class="layui-input-inline">
                    <input id="startDate" type="text" autocomplete="off" placeholder="选择开始日期" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-form-mid">至</div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-inline">
                    <input id="endDate" type="text" autocomplete="off" placeholder="选择结束日期" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">园所名称：</label>
                <div class="layui-input-inline">
                    <select id="gardenName" lay-filter="gardenName">
                        <option value="">请选择</option>
                        <!-- 选项将通过JS动态加载 -->
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">是否有便民服务牌：</label>
                <div class="layui-input-inline">
                    <select id="hasServiceCard" lay-filter="hasServiceCard">
                        <option value="">请选择</option>
                        <option value="1">是</option>
                        <option value="0">否</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <button class="layui-btn layui-btn-normal" id="btnsearch">查询</button>
            </div>
        </div>
        <div id="content" style="width: 100%;">
            <div class="marmain-cen">
                <div class="content-medical" id="divtable">
                    <table id="laytable" lay-filter="laytable"></table>
                </div>
            </div>
        </div>
    </div>
    <!-- <script type="text/javascript">
var v = top.version;
    document.write('<script type="text/javascript" src="../../sys/jquery.js?v=' + v + '"><' + '/script>');
    document.write('<script type="text/javascript" src="../../sys/arg.js?v=' + v + '"><' + '/script>');
    document.write('<script type="text/javascript" src="../../plugin/js/selarea.js?v=' + v + '"><' + '/script>');
    document.write('<script type="text/javascript" src="../../layui-btkj/layui.js?v=' + v + '"><' + '/script>');
    document.write('<script type="text/javascript" src="../../js/operation/w_bianminServiceInfo.js?v=' + v + '"><' + '/script>');
</script> -->
</div>
<script type="text/javascript" data-main="../../js/operation/w_bianminServiceInfo.js"
        src="../../sys/require.min.js"></script>
</body>

</html>