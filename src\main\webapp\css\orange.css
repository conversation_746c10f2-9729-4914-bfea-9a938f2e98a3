/*橘色皮肤*/
/*color*/
.bg1{background: #fd762d;}
.bg3{background: #533e33;}
.bg4{background: #f3f3f3;}/*浅灰*/
.bg5{background: #e9e9e9;}/*深灰*/
.cl1{color: #47494a;}
.cl2{color: #ff751a;}/*橘色*/

/*icon*/
u[class^=skinIcon],.layim_chateme .layim_chatsay .layim_zero{background-image:url(../images/icon-orange.png);}

/*btn*/
.btn-big,.personalImg .webuploader-pick,.popTitle,.fc-day-header,.echarts-dataview button,.btn-black, .monthHead,.jOrgChart .node,.formList .webuploader-pick{background: #fd762d;}
.btn-border{border:1px solid #ff751a;}
.btn-black:hover{background: #d44a00;}
a.btn-lang{border-color:#aaa;color:#aaa;}
.layui-layer-btn a.layui-layer-btn0,.btnTop a:hover,.btn-control:hover,.operateBtn a:hover{background:#fd762d;border-color:#d44a00;}

/*tab*/
.tab li:hover, .tab li.current{color: #fff; background:#fd762d!important;border: 1px solid #fd762d;}
.tab li.pulldown.current{color: #fff; background:#fd762d url(../images/icon_pulldown_white.png) no-repeat 75px center!important;background-size: 12px !important;border: 1px solid #fd762d;}
.tab li.pulldown:hover{color: #fff; background:#fd762d url(../images/icon_pulldown_white.png) no-repeat 75px center!important; background-size: 12px !important;border: 1px solid #fd762d;}

/*page*/
.dataTables_wrapper .dataTables_paginate .paginate_button.current, .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover,.dataTables_wrapper .dataTables_paginate .paginate_button:hover{ background: -webkit-linear-gradient(#fd762d 0%, #d44a00 100%);background: linear-gradient(#fd762d 0%, #d44a00 100%);color: #fff !important;border-color:#d44a00;filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fd762d', endColorstr='#d44a00', GradientType=0);}

/*聊天窗口*/
.layim_chatmore{border-right:1px solid #e38000; background-color:#fd762d;}
.layim_chatlist li:hover,.layim_chatlist .layim_chatnow,.layim_chatlist .layim_chatnow:hover{background-color:#fed6c0;}
.layim_sendbtn{ background-color:#fd762d;}
.layim_enter{border-left-color:#fed6c0;}
.layim_sendbtn:hover,.layim_enter:hover{background-color:#d44a00;}
.layim_sendtype span:hover,.layim_sendtype span:hover i{background-color:#fd762d;color:#fff;}
.layim_chateme .layim_chatsay{background: #f6ebd8;}

/*左侧面板*/
.funList li{color: #fd722e;}
.usualFun a:hover,.inner a:hover,.letterList li:hover,.letterList li.current,.btn-head:hover,.articleGray a:hover,.loadMore:hover,.innerHistory li:hover,.today_date,.alm_content p,.list .current,.ztree li a:hover,.foldList li:hover,.dataTables_wrapper a,.otx-table a,.foldList li p,.fc-sat .fc-day-num, .fc-sun .fc-day-num{color: #fd762d;}
.curList .current,.curList a:hover{color: #533e33;}
footer a.current,footer a:hover{color: #fd762d;}
.usualFun a:hover img,.inner a:hover img,.foldList li:hover img{/*background: url(../images/hover-orange.png) no-repeat center center;*/}
.list li{border-bottom: 1px solid #fd762d;}
.leftTitle{border-bottom: 1px solid #fd762d; background: #fd762d;}
.btn-lang:hover,.current.btn-lang{border-color:#fd762d;color:#fd762d;}
.letterIndex{background: #ccc;}
.list img.face{border-color:#fff;}
.historyList{background: #fd762d;}

/*智能中控*/
.btn-far:hover,.btn-near:hover{background:url(../images/distance-orange.png);}
.fast:hover,.normal:hover,.slow:hover,.arrow-up:hover,.arrow-down:hover{background: url(../images/hover-orange.png);}
.numberList span:hover{background: url(../images/orange-big.png);}

/*通知公告*/
.noticeTitle a:hover{color:#533e33;}

/*日历*/
.fc-state-down,.fc-state-active,.fc-button:hover{border-color:#fd9128;}
.fc-planmeeting-button{border:0;border-radius: 0;}
.fc-year-button:hover,.fc-month-button:hover,.fc-agendaWeek-button:hover,.fc-agendaDay-button:hover,.fc-state-active{background: #fd762d;color:#fff;}

/*栏目管理*/
.meauList span{border-top: 2px solid #fd762d; border-bottom: 2px solid #fd762d;}
.meauList .inner a{color: #fd762d;}
.inner a.selected{background: #ffecd4;}

/*tree*/
.ztree li a.curSelectedNode{background-color: #ffecd4;border: 1px solid #fd762d;color: #fd762d;}

/*会议控制*/
.meetMain{border-top: 4px solid #fd762d;}
.submeetList{border-top: 4px solid #fec1a0;}
.rankList span.current{background: #ff751a;}

/*下拉多选*/
.ui-widget-header{background: #ff761b; border-color:#ff761b;}
.ui-state-hover, .ui-widget-content .ui-state-hover, .ui-widget-header .ui-state-hover, .ui-state-focus, .ui-widget-content .ui-state-focus, .ui-widget-header .ui-state-focus{color: #ff761b;}
.ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active{background: none; border-color:#ff761b;color: #ff761b;}
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default{ border-color:#ff761b;color: #ff761b;}
/*幼儿园名称下拉背景色*/
.selectColor{background-color:#fd762d }

/*智慧数字幼儿园*/
/*桌面头部菜单*/
.ultopnums li.current,.ultopnums li:hover,.topultool li:hover,.topultool li.current {
    color: #ffffff;
    background: -webkit-linear-gradient(#ff761b, #d66012); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(#ff761b, #d66012); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(#ff761b, #d66012); /* Firefox 3.6 - 15 */
    background: linear-gradient(#ff761b, #d66012); /* 标准的语法 */
}
/*菜单切换*/
/*菜单切换*/
.menutab ul{color: #34495E!important;}
.menutab ul li{color: #F3F4F4;border-bottom:3px solid #D5D6D6; 
	background: #F3F4F4;		
	border-image: -webkit-linear-gradient(#F2F3F4,#D5D6D6) 40 20;
    border-image: -moz-linear-gradient(#F2F3F4,#D5D6D6) 40 20;		
	border-image: -o-linear-gradient(#F2F3F4,#D5D6D6) 40 20;
	border-image: linear-gradient(#F2F3F4,#D5D6D6) 40 20;}
.menutab ul li.current{color: #34495E!important; background: #fff;}
#divdesktop .scrollArea{background: none;}
#divmenulist .funList li:hover{background: #F2F2F2;color: #666!important;}
#divdeskleft:after{content: "";width: 20px;position: absolute;top: 0px;bottom: 0;right: -20px;box-shadow: 5px 0px 5px -4px inset #DCE0E4;
  
}
.funtop .desktop-logo .tabout-div{box-shadow: 0px 0px 5px 2px #db6d23;}
.funtop .desktop-logo .tab-cell{box-shadow: 0px -8px 4px -5px inset #daa785;}

/*保教*/
.term-tab.current{background: #fba268;color: #ffffff;}
.term-tab.current .calendar-div{border: 1px solid #ff7215;}
.term-list.redbg{background: #fba268;}
.calen-top h5{background: #fba268;}
.calen-txt p{color: #fba268;}
.div-right .layui-table tbody.current{border: 2px solid #fba268;}
.div-right .layui-table tbody.current tr:nth-child(1) td:nth-child(1){background-color: #fba268;}
.div-right .layui-table td.pinkcurrent{background-color: #fba268;}
.workrest-left h5{background: #f99c5f!important;}
.workrest-right h5{background: #fba268!important;}
.time-work li.time-worksel{ background:#fba268;}
.week-tab.current{background: #fba268;}
.icon-list .layui-btn{background: #f28036!important;}
.protect-educate .plan-txt.current{background: #fba268;color: #ffffff;border: 1px solid #fba268;}
.play-sub{border: 2px solid #fba268!important;}
.def-icon .icon_calen:before{color: #fba268!important;}
.opreate-icon .icon-txt .iconfont{color: #fba268!important;}
.protect-educate .plan-left-icon>div .iconfont:before{color: #fba268;}
.protect-educate .plan-left-icon>div .icon_ratio:before{border: 2px solid #fba268;}
.def-icon .icon_tableft:before,.def-icon .icon_tabright:before{color: #fba268;}
#a_unselect{color: #fba268!important;}
.upload-btn .opr_select{background: #fba268!important;}
.opr-btn.pay-save{background: #fba268!important;}
#pgriddivshipuarea.def-layui-table tr.current td:not(.tr-firsttd){background: #fdeadf;}
#pgridplantable.def-layui-table tr.current td:not(.tr-firsttd){background: #fdeadf;}
/*作息弹框按钮颜色*/
.opr-btn{background: #fba268;}
.cho-main .layui-btn{background: #fba268;}
.protect-educate .plan-left-icon>div.current .iconfont:before{color: #fba268;}
.protect-educate .plan-left-icon>div.current .iconfont:before{color: #fba268;}
.plan-con{border: 4px solid #fba268;}
.plan-leftspan.currentpink, .plan-rightspan.currentpink{background: #fba268;}
.plan-leftspan.currentred, .plan-rightspan.currentred{background: #ef7c32;}
.left-contain .common-tab3 li.current a{color: #fba268;font-weight: bold;border-bottom: 2px solid #fba268;}
.left-contain .icon_search2:before{color: #fba268;}
.type-plan .iconfont.redicon2:before{color: #fba268;}
#divdt .layui-table tbody.current{border: 2px solid #fba268;}
#divdt .layui-table tbody.current tr:nth-child(1) td:nth-child(1){background-color: #fba268;}
#divdt .layui-table td.pinkcurrent{background-color: #fba268;}
#divdt .tdexist{background-color: #f2853f;}
.timetable-list .circle-label{background: #fba268;}
.teaching-list .teachadd-btn .teachlayui-btn{background: #fba268;}
.upload-list .layui-btn-normal, .teaching-list-bom .upload-right .layui-btn, .cover-list .layui-btn{background: #fba268;}
#add_pic .webuploader-pick{background: #fba268;}
.teaching-list .layui-btn{color: #fba268;}
.upload-btn .opr_save{background: #fba268!important;}
.upload-btn .opr_saveadd{background: #fba268!important;}
.course-opr .layui-tab-brief>.layui-tab-title .layui-this{color: #fba268;}
.course-opr .layui-tab-brief>.layui-tab-more li.layui-this:after,.course-opr .layui-tab-brief>.layui-tab-title .layui-this:after{border-bottom: 4px solid #fba268;}
#add_pic .webuploader-pick{background:#fba268;}
#add_pic .webuploader-pick, #add_video .webuploader-pick{background:#fba268;}
#add_audio .webuploader-pick{background:#fba268;}
#add_files .webuploader-pick{background:#fba268;}

