﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <link rel="shortcut icon" href="/images/favicon.ico" type="image/x-icon"/>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="Copyright" content="Copyright 2016 by tongbang"/>
    <meta name="Author" content=""/>
    <meta name="Robots" content="All"/>
    <title>表4-2托育机构膳食营养分析表</title>
    <link href="../../styles/admin.css" rel="stylesheet" type="text/css"/>
    <link href="../../styles/tbstyles.css" type="text/css" rel="stylesheet"/>
    <link href="../../plugin/ehaiform/css/tbbaseform.css" rel="stylesheet" type="text/css"/>
    <link href="../../plugin/editselect/css/editselect.css" rel="stylesheet" type="text/css"/>
    <link href="../../layui-btkj/css/layui.css" rel="stylesheet">
    <link href="../../css/commonstyle-layui-btkj.css" rel="stylesheet">
    <link href="../../css/style.css" rel="stylesheet" >
    <link rel="stylesheet" href="../../plugin/jquery_tabs/css/tbjquery.tabs.css" type="text/css"
          media="print, projection, screen"/>
    <style>

        .rytjdjedit_table td {
            border: #eee solid 1px
        }

        /*滚动条样式*/
        html {
            color: #000;
            font-size: .7rem;
            font-family: "\5FAE\8F6F\96C5\9ED1", Helvetica, "黑体", Arial, Tahoma;
            height: 100%;
        }

        /*滚动条样式*/
        html::-webkit-scrollbar {
            display: none;
            width: 6px;
            height: 6px;
        }

        html:hover::-webkit-scrollbar {
            display: block;
        }

        html::-webkit-scrollbar-thumb {
            border-radius: 3px;
            -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
            border: 1px solid rgb(255, 255, 255);
            background: rgb(66, 66, 66);
        }

        html::-webkit-scrollbar-track {
            -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
            border-radius: 0;
            background: rgba(0, 0, 0, 0.1);
        }

        .full_disease {
            display: inline-block;
            background: #eee;
            padding: 0 10px;
            width: 60px;
            text-align: center;
        }

        table.tabledata td.tdhead {
            border-top: 1px solid black;
            border-right: 1px solid black;
            font-weight: bold;
            text-align: center;
            vertical-align: middle;
        }

        table.tabledata td {
            border-top: 1px solid black;
            border-right: 1px solid black;
            text-align: center;
            vertical-align: middle;
        }

        input {
            margin: 5px auto
        }
    </style>
</head>
<body>
<div class="bodywidth" style="min-width: 1500px;">
    <div id="tablename" class="divhead" style="text-align: center; font-size: 16pt; font-weight: bold; font-family: 宋体, 微软雅黑, 黑体; margin: 15px auto 20px; width: 100%;"></div>
    <div class="content-medical" style="width: 90%;margin: 0 auto;">
        <div class="layui-form layui-comselect" style="position: relative;padding: 10px 10px 5px 10px;">
            <div style="display: block;">
                <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                    <label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 10px;line-height: 28px;">年份：</label>
                    <div class="layui-input-inline" style="min-height: 30px;width:180px;">
                        <input id="year" type="text" placeholder="年份" readonly class="layui-input"/>
                    </div>
                </div>
                <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                    <label class="layui-form-label" style="padding: 5px 0px 5px 10px;line-height: 28px;">月份：</label>
                    <div class="def-search" style="display: inline-block;vertical-align: top;width: 160px;margin: 0px 0px 0 0; height: 28px;">
                        <select lay-filter="sel_month" id="month">
                            <option value="">请选择</option>
                            <option value="1">1月</option>
                            <option value="2">2月</option>
                            <option value="3">3月</option>
                            <option value="4">4月</option>
                            <option value="5">5月</option>
                            <option value="6">6月</option>
                            <option value="7">7月</option>
                            <option value="8">8月</option>
                            <option value="9">9月</option>
                            <option value="10">10月</option>
                            <option value="11">11月</option>
                            <option value="12">12月</option>
                        </select>
                    </div>
                </div>
                <button id="btnsearch" class="layui-btn form-search" style="vertical-align: top; margin-left: 10px;">查询</button>
            </div>
        </div>
    </div>


    <div id="divcontent" style="min-width: 1500px;">
        <div class="rep-table tab_parent_sec" style="width: 90%;margin: 0 auto;">
            <div style="height:25px;">
                <div class="divprintreport" style="font-size:10pt;text-align: left;display:inline;">
                    <span style="width:33%; text-align:left; display:inline-block">
                        <span id="organ">托育机构名称：</span><span id="txtyeyname"></span>
                    </span>
                    <span style="width:33%; text-align:left; display:inline-block">
                    儿童年龄段：<input id="txtnianlingduan" type="text" style=" text-align:left ; border: solid 1px #CFCFCF; width: 90%;width: 100px;" value="">            </span>
                    <span style="width:33%; text-align:left; display:inline-block">
                        提供餐次：<input id="txtcanci" type="text" style=" text-align:left ; border: solid 1px #CFCFCF; width: 90%;width: 100px;" value="">                      </span>
                </div>
            </div>
            <table class="reporttable" style="clear: both; margin: 0px auto; font-size: 11pt; font-family: 宋体; border: 2px solid black; width: 100%;" cellspacing="0" cellpadding="1" border="0">
                <thead>
                <tr>
                    <th style=" border-left:1px solid Black;border-top:1px solid Black;">食物类别</th>
                    <th style=" border-left:1px solid Black;border-top:1px solid Black;">细粮</th>
                    <th style=" border-left:1px solid Black;border-top:1px solid Black;">杂粮</th>
                    <th style=" border-left:1px solid Black;border-top:1px solid Black;">糕点</th>
                    <th style=" border-left:1px solid Black;border-top:1px solid Black;">干豆类</th>
                    <th style=" border-left:1px solid Black;border-top:1px solid Black;">豆制品</th>
                    <th style=" border-left:1px solid Black;border-top:1px solid Black;">蔬菜总量</th>
                    <th style=" border-left:1px solid Black;border-top:1px solid Black;">绿橙蔬菜</th>
                    <th style=" border-left:1px solid Black;border-top:1px solid Black;">水果</th>
                    <th style=" border-left:1px solid Black;border-top:1px solid Black;">乳类</th>
                    <th style=" border-left:1px solid Black;border-top:1px solid Black;">蛋类</th>
                    <th style=" border-left:1px solid Black;border-top:1px solid Black;">肉类</th>
                    <th style=" border-left:1px solid Black;border-top:1px solid Black;">肝</th>
                    <th style=" border-left:1px solid Black;border-top:1px solid Black;">鱼</th>
                    <th style=" border-left:1px solid Black;border-top:1px solid Black;">糖</th>
                    <th style=" border-left:1px solid Black;border-top:1px solid Black;">食油</th>
                    <th style=" border-left:1px solid Black;border-top:1px solid Black;"><input id="txtleibiekongname" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 85%;" value=""></th>
                </tr>
                </thead>
                <tbody>
                <tr style="line-height:20px;height:20px;">
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;">数量（g）</td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;"><input id="txtxiliang" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value="12"></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;"><input id="txtzaliang" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;"><input id="txtgaodian" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;"><input id="txtgandoulei" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;"><input id="txtdouzhipin" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;"><input id="txtshucai" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;"><input id="txtlvcheng" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;"><input id="txtshuiguo" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;"><input id="txtrulei" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;"><input id="txtdanlei" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;"><input id="txtroulei" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;"><input id="txtgan" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;"><input id="txtyu" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;"><input id="txttang" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;"><input id="txtshiyou" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;"><input id="txtleibiekong" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                </tr>
                </tbody>
            </table>
            <div style="height:25px; margin-top:10px;">
                <div class="divprintreport" style="font-size:10pt;font-weight:bold;text-align: left;display:inline;"><span class="lbselectdate0">二、营养素摄入量</span></div>
            </div>
            <table class="reporttable" style="clear: both; margin: 0px auto; font-size: 11pt; font-family: 宋体; border: 2px solid black; width: 100%; " cellspacing="0" cellpadding="1" border="0">
                <thead>
                <tr>
                    <th rowspan="2" style=" border-left:1px solid Black;border-top:1px solid Black;"></th>
                    <th colspan="2" style=" border-left:1px solid Black;border-top:1px solid Black;"><p>热量</p></th>
                    <th rowspan="2" style=" border-left:1px solid Black;border-top:1px solid Black;">蛋白质(克)</th>
                    <th rowspan="2" style=" border-left:1px solid Black;border-top:1px solid Black;">脂肪(克)</th>
                    <th rowspan="2" style=" border-left:1px solid Black;border-top:1px solid Black;">视黄醇当量(微克)</th>
                    <th rowspan="2" style=" border-left:1px solid Black;border-top:1px solid Black;">维生素A(微克)</th>
                    <th rowspan="2" style=" border-left:1px solid Black;border-top:1px solid Black;">胡萝卜素(微克)</th>
                    <th rowspan="2" style=" border-left:1px solid Black;border-top:1px solid Black;">维生素B1(毫克)</th>
                    <th rowspan="2" style=" border-left:1px solid Black;border-top:1px solid Black;">维生素B2(毫克)</th>
                    <th rowspan="2" style=" border-left:1px solid Black;border-top:1px solid Black;">维生素C(毫克)</th>
                    <th rowspan="2" style=" border-left:1px solid Black;border-top:1px solid Black;">钙(毫克)</th>
                    <th rowspan="2" style=" border-left:1px solid Black;border-top:1px solid Black;">锌(毫克)</th>
                    <th rowspan="2" style=" border-left:1px solid Black;border-top:1px solid Black;">铁(毫克)</th>
                    <th rowspan="2" style=" border-left:1px solid Black;border-top:1px solid Black;"><input id="txtyingyangkongname" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 85%;" value=""></th>
                </tr>
                <tr>
                    <th style=" border-left:1px solid Black;border-top:1px solid Black;"><p>(千卡)</p></th>
                    <th style=" border-left:1px solid Black;border-top:1px solid Black;"><p>(千焦)</p></th>
                </tr>
                </thead>
                <tbody>
                <tr style="line-height:20px;height:20px;">
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;">平均每人每日</td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="left"><input id="txtreliangka" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="left"><input id="txtreliangjiao" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtpro" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtfat" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtshihuangchun" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtva" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtcar" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtvb1" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtvb2" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtvc" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtca" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtzn" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtfe" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtyingyangkong" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                </tr>
                <tr style="line-height:20px;height:20px;">
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;">DRIs</td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="left"><input id="txtreliangka2" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="left"><input id="txtreliangjiao2" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtpro2" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtfat2" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtshihuangchun2" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtva2" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtcar2" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtvb12" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtvb22" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtvc2" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtca2" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtzn2" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtfe2" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtyingyangkong2" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                </tr>
                <tr style="line-height:20px;height:20px;">
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;">比较%</td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="left"><input id="txtreliangka3" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="left"><input id="txtreliangjiao3" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtpro3" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtfat3" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtshihuangchun3" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtva3" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtcar3" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value="&nbsp;"></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtvb13" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtvb23" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtvc3" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtca3" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtzn3" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtfe3" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                    <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtyingyangkong3" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                </tr>
                </tbody>
            </table>
            <div style="margin-top:10px; display: flex;">
                <div style="float:left;width: 25%;">
                    <div class="divprintreport" style="font-size:10pt;font-weight:bold;text-align: left;display:inline;"><span class="lbselectdate0">三、热量来源分布</span></div>
                    <table class="reporttable" style="clear: both; margin: 0px auto; font-size: 11pt; font-family: 宋体; border: 2px solid black; width: 100%;" cellspacing="0" cellpadding="1" border="0">
                        <thead>
                        <tr>
                            <th width="30%" colspan="2" rowspan="2" style=" border-left:1px solid Black;border-top:1px solid Black;">&nbsp;</th>
                            <th colspan="2" style=" border-left:1px solid Black;border-top:1px solid Black;"><p>脂肪</p></th>
                            <th colspan="2" style=" border-left:1px solid Black;border-top:1px solid Black;"><p>蛋白质</p></th>
                        </tr>
                        <tr>
                            <th style=" border-left:1px solid Black;border-top:1px solid Black;"><p>要求</p></th>
                            <th style=" border-left:1px solid Black;border-top:1px solid Black;"><p>现状</p></th>
                            <th style=" border-left:1px solid Black;border-top:1px solid Black;"><p>要求</p></th>
                            <th style=" border-left:1px solid Black;border-top:1px solid Black;">现状</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr style="line-height:20px;height:20px;">
                            <td rowspan="2" align="center" style=" border-left:1px solid Black;border-top:1px solid Black;">摄入量</td>
                            <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">(千卡)</td>
                            <td align="center" style=" border-left:1px solid Black;border-top:1px solid Black;"><input id="txtfatneed" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                            <td align="center" style=" border-left:1px solid Black;border-top:1px solid Black;"><input id="txtfatnow" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                            <td align="center" style=" border-left:1px solid Black;border-top:1px solid Black;"><input id="txtproneed" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                            <td align="center" style=" border-left:1px solid Black;border-top:1px solid Black;"><input id="txtpronow" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                        </tr>
                        <tr style="line-height:20px;height:20px;">
                            <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">(千焦)</td>
                            <td align="center" style=" border-left:1px solid Black;border-top:1px solid Black;"><input id="txtfatneed2" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                            <td align="center" style=" border-left:1px solid Black;border-top:1px solid Black;"><input id="txtfatnow2" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                            <td align="center" style=" border-left:1px solid Black;border-top:1px solid Black;"><input id="txtproneed2" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                            <td align="center" style=" border-left:1px solid Black;border-top:1px solid Black;"><input id="txtpronow2" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                        </tr>
                        <tr style="line-height:20px;height:20px;">
                            <td colspan="2" align="center" style=" border-left:1px solid Black;border-top:1px solid Black;"><p>占总热量%</p></td>
                            <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><p>30～35%</p></td>
                            <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtfatnow3" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                            <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><p>12～15%</p></td>
                            <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txtpronow3" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div style="float:left; margin-left:30px; width:45%;">
                    <div class="divprintreport" style="font-size:10pt;font-weight:bold;text-align: left;display:inline;"><span class="lbselectdate0">     四、蛋白质来源    </span></div>
                    <table class="reporttable" style="clear: both; margin: 0px auto; font-size: 11pt; font-family: 宋体; border: 2px solid black; width: 100%;" cellspacing="0" cellpadding="1" border="0">
                        <thead>
                        <tr>
                            <th colspan="2" rowspan="2" style=" border-left:1px solid Black;border-top:1px solid Black;">&nbsp;</th>
                            <th colspan="3" style=" border-left:1px solid Black;border-top:1px solid Black;">优质蛋白质</th>
                        </tr>
                        <tr>
                            <th width="23%" style=" border-left:1px solid Black;border-top:1px solid Black;">要求</th>
                            <th width="42%" style=" border-left:1px solid Black;border-top:1px solid Black;">动物性食物</th>
                            <th width="25%" style=" border-left:1px solid Black;border-top:1px solid Black;">豆类</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr style="line-height:20px;height:20px;">
                            <td colspan="2" align="center" style=" border-left:1px solid Black;border-top:1px solid Black;">摄入量(克)</td>
                            <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txt4proneed" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                            <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txt4dongwu" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                            <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txt4doulei" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                        </tr>
                        <tr style="line-height:20px;height:20px;">
                            <td colspan="2" align="center" style=" border-left:1px solid Black;border-top:1px solid Black;">占蛋白质总量%</td>
                            <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">≥50%</td>
                            <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txt4dongwu2" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                            <td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center"><input id="txt4doulei2" type="text" style=" text-align:center ; border: solid 1px #CFCFCF; width: 90%;width: 98%;" value=""></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div style="float:left; margin-left:30px;width: 25%;">
                    <div class="divprintreport" style="font-size:10pt;font-weight:bold;text-align: left;display:inline;"><span class="lbselectdate0">五、膳食费使用：当月膳食费：/人</span></div>
                    <div style="clear: both; margin: 0px auto; font-size: 11pt; font-family: 宋体; border: 2px solid black;  padding:0px 20px"><p><label style="display: inline-block;width: 90px;">本月总收入：</label><input id="txtmonthin" type="text" style=" text-align:left ; border: solid 1px #CFCFCF; width: 90%;width: 100px;" value="">元</p>
                        <p><label style="display: inline-block;width: 90px;">本月支出：</label><input id="txtmonthout" type="text" style=" text-align:left ; border: solid 1px #CFCFCF; width: 90%;width:100px;" value="">元</p>
                        <p><label style="display: inline-block;width: 90px;">盈亏：</label><input id="txtyingkui" type="text" style=" text-align:left ; border: solid 1px #CFCFCF; width: 90%;width: 100px;" value="">元</p>
                        <p><label style="display: inline-block;width: 90px;">占总收入：</label><input id="txtallin" type="text" style=" text-align:left ; border: solid 1px #CFCFCF; width: 90%;width:100px;" value="">%</p></div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/function.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../js/richang/tuoyushanshiyingyangdetail.js?v='" + v + "><" + "/script>");
</script>
</body>
</html>
