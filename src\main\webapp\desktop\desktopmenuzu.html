<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>选择菜单</title>
    <link rel="stylesheet" href="../css/icon.css">
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../desktop.css">
    <style>
        html {
            background-color: #333;
        }
    </style>
</head>
<body>
	<div class="nav-menu">
		<ul id="uldefault">
			<li type="baoyu"><img src="../images/desktop/top/icon_baoyu.png"  alt=""/>保育<label class="set-checkbox-style"><input type="checkbox"></label></li>
			<li type="baojiao"><img src="../images/desktop/top/icon_baojiao.png"  alt=""/>保教<label class="set-checkbox-style"><input type="checkbox"></label></li>
			<li type="richang"><img src="../images/desktop/top/icon_richang.png"  alt=""/>日常<label class="set-checkbox-style"><input type="checkbox"></label></li>
			<li type="bangong"><img src="../images/desktop/top/icon_bangong.png"  alt=""/>办公<label class="set-checkbox-style"><input type="checkbox"></label></li>
		</ul>
		 <ul id="ulmenu">
			 <!--<li><img src="../images/desktop/backgroud/icon_zhaoshengguanli.png"  alt=""/>招生管理<label class="set-checkbox-style"><input type="checkbox"></label></li>
			 <li class="current"><img src="../images/desktop/backgroud/icon_zhinengfenban.png"  alt=""/>智能分班<label class="set-checkbox-style"><input type="checkbox" checked></label></li>
			 <li><img src="../images/desktop/backgroud/icon_zaojiaoban.png"  alt=""/>早教班管理<label class="set-checkbox-style"><input type="checkbox"></label></li>
			 <li><img src="../images/desktop/backgroud/icon_zhaoshengguanli.png"  alt=""/>招生管理<label class="set-checkbox-style"><input type="checkbox"></label></li>
			 <li class="current"><img src="../images/desktop/backgroud/icon_zhinengfenban.png"  alt=""/>智能分班<label class="set-checkbox-style"><input type="checkbox" checked></label></li>
			 <li><img src="../images/desktop/backgroud/icon_zaojiaoban.png"  alt=""/>早教班管理<label class="set-checkbox-style"><input type="checkbox"></label></li> -->
		 </ul>
	</div>
	<script data-main="desktopmenuzu" src="../sys/require.min.js"></script>
</body>
</html>
