﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8"/>
    <title>便民服务牌数据详细</title>
    <link rel="stylesheet" type="text/css" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" type="text/css" href="../../css/icon.css"/>
    <link rel="stylesheet" type="text/css" href="../../styles/tbstyles.css"/>
    <link href="../../plugin/flexigrid/css/tbflexigrid.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css"/>
    <style type="text/css">
        html,
        body {
            background: #EAEFF3;
            overflow: hidden;
        }

        .layui-form-item {
            display: inline-block;
            vertical-align: top;
        }

        .layui-form-label {
            line-height: 28px;
            width: 110px;
        }

        .layui-input-inline {
            width: 180px;
        }
    </style>
</head>

<body>
<div class="marmain">
    <div class="content-medical">
        <div class="search-container layui-form layui-comselect">
            <div class="layui-form-item">
                <label class="layui-form-label">预约挂号时间：</label>
                <div class="layui-input-inline">
                    <input id="startDate" type="text" autocomplete="off" placeholder="选择开始日期" class="layui-input">
                </div>
                <div class="layui-form-mid">至</div>
                <div class="layui-input-inline">
                    <input id="endDate" type="text" autocomplete="off" placeholder="选择结束日期" class="layui-input">
                </div>
                <div class="layui-input-inline" style="display: none;">
                    <input id="startTime" type="text" autocomplete="off" placeholder="开始时间" class="layui-input">
                </div>
                <div class="layui-form-mid" style="display: none;">至</div>
                <div class="layui-input-inline" style="display: none;">
                    <input id="endTime" type="text" autocomplete="off" placeholder="结束时间" class="layui-input">
                </div>
            </div>
            <br>
            <div class="layui-form-item">
                <label class="layui-form-label">园所名称：</label>
                <div class="layui-input-inline">
                    <select id="gardenName" lay-filter="gardenName">
                        <option value="">请选择</option>
                        <!-- 选项将通过JS动态加载 -->
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">年级：</label>
                <div class="layui-input-inline">
                    <select id="grade" lay-filter="grade">
                        <option value="">请选择</option>
                        <option value="小班">小班</option>
                        <option value="中班">中班</option>
                        <option value="大班">大班</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">预约科室：</label>
                <div class="layui-input-inline">
                    <select id="dept" lay-filter="dept">
                        <option value="">请选择</option>
                        <option value="儿科">儿科</option>
                        <option value="耳鼻喉科">耳鼻喉科</option>
                        <option value="眼科">眼科</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">是否取消预约：</label>
                <div class="layui-input-inline">
                    <select id="isCancel" lay-filter="isCancel">
                        <option value="">请选择</option>
                        <option value="1">是</option>
                        <option value="0">否</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <button class="layui-btn layui-btn-normal" style="margin-left: 5px;" id="btnsearch">查询</button>
            </div>
        </div>
    </div>
    <div id="content" style="width: 100%;">
        <div class="marmain-cen">
            <div class="content-medical" id="divtable">
                <table id="laytable" lay-filter="laytable"></table>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" data-main="../../js/operation/bianminServiceUnit_parent_appointment.js"
        src="../../sys/require.min.js"></script>
</body>

</html>