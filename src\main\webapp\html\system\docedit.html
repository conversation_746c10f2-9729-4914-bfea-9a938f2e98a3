﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>餐费统计</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <link href="../../styles/admin.css" rel="stylesheet" type="text/css"/>
    <link href="../../css/reset.css" rel="stylesheet" type="text/css"/>
    <link href="../../css/icon.css" rel="stylesheet">
    <link href="../../styles/tbstyles.css" rel="stylesheet" type="text/css"/>
    <link href="../../css/style.css" rel="stylesheet" type="text/css"/>
    <link href="../../layui-btkj/css/layui.css" rel="stylesheet" type="text/css"/>
    <style type="text/css">

        /*.laydate-time-show .layui-laydate-content .layui-laydate-list > li { width: 50% !important; }*/
        .layui-laydate-range {
            width: 568px!important;
            /*left: 110px;*/
            /*top: 116px!important;*/
        }
        .layui-form-label {
            width: 91px;
        }
        .layui-input-block{
            margin-left: 125px;
        }
        .layui-progress{ width: 219px;display: inline-block; margin: 0 10px; }
        .layui-btn{height: 36px;line-height: 36px;}
        .upload-list .upload-right, .teaching-list .upload-right{height: auto;}
        #add_files .webuploader-pick{
            position: inherit;
            width: 100%;
            padding: inherit;
            height: 100%;
            margin-left: -18px;
            margin-top: -2px;
        }
        .upload-list .upload-left{width:80px;}
    </style>
</head>
<body>
<!--编辑餐费统计信息-->
<div class="addbill layui-form radio-blue" style="margin: 0;padding: 0;height: 100%;">
    <div style="margin-top:20px;">
<!--        <div>-->
<!--            <input id="testfile" type="file">-->
<!--            <label for="testfile">上传</label>-->
<!--        </div>-->
        <div class="upload-list" style="border-bottom: none;padding: 10px 0px;">
            <div>
				<label class="layui-form-label" style="float: left;"><b style="display: inline-block; color: red;">*</b>上传文件：</label>
          
                <div class="upload-right" style="padding-left: 85px;float: left;">
                    <div id="add_files" class="layui-btn layui-btn-normal" style="margin-top:0;width:93px;margin-left: 40px;margin-bottom: 10px;">上传文件</div>
                    <label>文件大小不超过15M</label>
                    <input type="hidden" id="txt_size"/>
                    <input type="hidden" id="txt_unit"/>
                    <input type="hidden" id="txt_duration"/>
                    <input type="hidden" id="txt_murl"/>
                    <input type="hidden" id="txt_oldmurl"/>
                </div>
            </div>
            <div class="upload-video"></div>
            <div class="upload-cen" style="margin: 10px 0;"><span id="sp_progress">上传进度</span>
                <div class="layui-progress" lay-filter="demo-process"><div class="layui-progress-bar layui-bg-blue" lay-percent="0%"></div> </div>
                <!--<label>剩余时间：正在计算</label><label> 速度：264.0KB</label>-->
                <label>已上传：
                    <label id="lb_progress">
                        <!--123KB/34MB-->
                    </label>
                </label>
            </div>
            <!--上传成功-->
            <!--<div class="audio-fre">-->
            <!--<span><i class="iconfont icon_audiofre"></i></span>-->
            <!--<h3>无标题.mp3</h3>-->
            <!--<label>00:36</label>-->
            <!--</div>-->
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><b style="display: inline-block; color: red;">*</b>文件名称：</label>
            <div class="layui-input-block" style="width:300px;">
                <input type="text" id="filename" name="filename" placeholder="请输入文件名称" class="layui-input" maxlength="50"/>
            </div>
        </div>
        <div class="layui-form-item layui-form">
            <label class="layui-form-label">描述：</label>
            <div class="layui-input-block" style="width: 300px;">
                <textarea id="description" class="layui-input" style="width: 300px;height:150px;padding: 10px;" maxlength="180"></textarea>
            </div>
        </div>
        <input type="hidden" id="dataset_id"/>
        <input type="hidden" id="document_id"/>
    </div>
    <div style="display:none;">
        <a id="btnok" class="layui-btn">保存</a>
    </div>
</div>
<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/system.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/function.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../plugin/ueditor/third-party/webuploader/webuploader.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../plugin/nicescroll/jquery.nicescroll.min.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../js/system/docedit.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>
