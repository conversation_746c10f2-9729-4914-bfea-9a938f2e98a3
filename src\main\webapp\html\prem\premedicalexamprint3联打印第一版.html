<!DOCTYPE html>
<html>
<head>
    <title>打印预览婚前检查</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <style type="text/css">
        /* 打印样式 3联打印第一版 高265长558mm 需要在打印首选项里设置打印尺寸 */

        @media print {
            .jc_content{
                transform: scale(1);
            }
        }
        body{
            /* --dpi 是动态计算并保存全局变量，在界面下方的script中 */
            /* px转mm的比例 */
            --pxmm: 25.4;
            /* px转毫米公式  px / (dpi/25.4) ; 1mm ≈ 3.78px; 0.5cm ≈ 19px; 1cm ≈ 38px*/
            --dpirate: calc(var(--dpi) / var(--pxmm));
            /* 第一联的宽度 */
            --firstwidth:calc(4200px / var(--dpirate));
            /* 第二联的宽度 */
            --secondwidth:calc(8740px / var(--dpirate));
        }
        body, html {
            width: calc(12960px / var(--dpirate));
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden; /* 防止滚动条出现，如果图片比视口小 */
        }
        .jc_content {
            /* 打印区域大小 12960 5640*/
            width: calc(12960px / var(--dpirate));
            height: calc(5640px / var(--dpirate));
            margin: 0;
            color: rgba(1, 1, 1, 1);
            border-top: none;
            background-repeat: no-repeat;
            background-size: cover;
            font-weight: bolder;
        }
        .report1{
            display: inline-block;
            width: calc(703px / var(--dpirate));
            height: calc(1002px / var(--dpirate));
        }
        .report1 .province{/* 省 */
            position:absolute;
            top: calc(410px / var(--dpirate));
            left: calc(270px / var(--dpirate));
            width: calc(756px / var(--dpirate));
        }
        .report1 .city{/* 市 */
            position:absolute;
            top: calc(410px / var(--dpirate));
            left: calc(670px / var(--dpirate));
            width: calc(756px / var(--dpirate));
        }
        .report1 .area{/* 区 */
            position:absolute;
            top: calc(410px / var(--dpirate));
            left: calc(990px / var(--dpirate));
            width: calc(756px / var(--dpirate));
        }
        .report1 .premcheck_id{/* 婚检编号 */
             position:absolute;
             top: calc(410px / var(--dpirate));
             left: calc(2870px / var(--dpirate));
             width: calc(756px / var(--dpirate));
         }
        .report1 .hospital_fulladdress{
            position:absolute;
            top: calc(410px / var(--dpirate));
            left: calc(1400px / var(--dpirate));
            width: calc(1134px / var(--dpirate));
        }
        .report1 .name{
            position:absolute;
            top: calc(670px / var(--dpirate));
            left: calc(1000px / var(--dpirate));
            width: calc(756px / var(--dpirate));
        }
        .report1 .sex{
            position:absolute;
            top: calc(700px / var(--dpirate));
            left: calc(2920px / var(--dpirate));
            width: calc(378px / var(--dpirate));
        }
        .report1 .birthdayyear{
            position:absolute;
            top: calc(920px / var(--dpirate));
            left: calc(1270px / var(--dpirate));
            width: calc(756px / var(--dpirate));
        }
        .report1 .birthdaymonth{
            position:absolute;
            top: calc(920px / var(--dpirate));
            left: calc(1620px / var(--dpirate));
            width: calc(756px / var(--dpirate));
        }
        .report1 .birthdayday{
            position:absolute;
            top: calc(920px / var(--dpirate));
            left: calc(1870px / var(--dpirate));
            width: calc(756px / var(--dpirate));
        }
        .report1 .ethnic{/* 民族 */
            position:absolute;
            top: calc(970px / var(--dpirate));
            left: calc(2940px / var(--dpirate));
            width: calc(756px / var(--dpirate));
        }
        .report1 .idcard{/* 身份证号 */
            position:absolute;
            top: calc(1210px / var(--dpirate));
            left: calc(880px / var(--dpirate));
            width: calc(2000px / var(--dpirate));
        }
        .report1 .occupation{/* 职业 */
            position:absolute;
            top: calc(1500px / var(--dpirate));
            left: calc(1000px / var(--dpirate));
            width: calc(2000px / var(--dpirate));
        }
        .report1 .current_address{/* 现住址 */
            position:absolute;
            top: calc(1800px / var(--dpirate));
            left: calc(1000px / var(--dpirate));
            width: calc(2000px / var(--dpirate));
        }
        .report1 .other_name{/* 他人姓名 */
            position:absolute;
            top: calc(2050px / var(--dpirate));
            left: calc(1000px / var(--dpirate));
            width: calc(1000px / var(--dpirate));
        }
        .report1 .blrelationsno{/* 无血缘关系 */
            position:absolute;
            top: calc(2480px / var(--dpirate));
            left: calc(2300px / var(--dpirate));
            width: calc(500px / var(--dpirate));
        }
        .report1 .blrelationsyes{/* 有血缘关系 */
            position:absolute;
            top: calc(2480px / var(--dpirate));
            left: calc(2800px / var(--dpirate));
            width: calc(756px / var(--dpirate));
        }
        .report1 .premcheck_result{/* 婚前检查结果 第1，2联显示前面的半句，3联显示后面的半句 */
            position:absolute;
            top: calc(2950px / var(--dpirate));
            left: calc(1000px / var(--dpirate));
            width: calc(2000px / var(--dpirate));
        }
        .report1 .check_opinionone{
            position:absolute;
            top: calc(3700px / var(--dpirate));
            left: calc(720px / var(--dpirate));
            width: calc(756px / var(--dpirate));
        }
        .report1 .check_opiniontwo{
            position:absolute;
            top: calc(3700px / var(--dpirate));
            left: calc(2020px / var(--dpirate));
            width: calc(756px / var(--dpirate));
        }
        .report1 .check_opinionthree{
            position:absolute;
            top: calc(3900px / var(--dpirate));
            left: calc(720px / var(--dpirate));
            width: calc(756px / var(--dpirate));
        }
        .report1 .check_opinionfour{
            position:absolute;
            top: calc(3900px / var(--dpirate));
            left: calc(2020px / var(--dpirate));
            width: calc(756px / var(--dpirate));
        }
        .report1 .check_opinionfive{
            position:absolute;
            top: calc(4100px / var(--dpirate));
            left: calc(720px / var(--dpirate));
            width: calc(756px / var(--dpirate));
        }
        .report1 .doctor_name{
            position:absolute;
            top: calc(4400px / var(--dpirate));
            left: calc(1000px / var(--dpirate));
            width: calc(756px / var(--dpirate));
        }
        .report1 .hospital_seal{
            position:absolute;
            top: calc(4400px / var(--dpirate));
            left: calc(2300px / var(--dpirate));
            width: calc(756px / var(--dpirate));
        }
        .report1 .timeyear{
            position:absolute;
            top: calc(4550px / var(--dpirate));
            left: calc(2970px / var(--dpirate));
            width: calc(378px / var(--dpirate));
        }
        .report1 .timemonth{
            position:absolute;
            top: calc(4550px / var(--dpirate));
            left: calc(3350px / var(--dpirate));
            width: calc(378px / var(--dpirate));
        }
        .report1 .timeday{
            position:absolute;
            top: calc(4550px / var(--dpirate));
            left: calc(3580px / var(--dpirate));
            width: calc(378px / var(--dpirate));
        }
        .report2{
            display: inline-block;
            width: calc(703px / var(--dpirate));
            height: calc(1002px / var(--dpirate));
        }
        .report2 .province{/* 省 */
            position:absolute;
            top: calc(440px / var(--dpirate));
            left: calc(var(--firstwidth) + (600px / var(--dpirate)));
            width: 756px;
        }
        .report2 .city{/* 市 */
            position:absolute;
            top: calc(440px / var(--dpirate));
            left: calc(var(--firstwidth) + (1000px / var(--dpirate)));
            width: 756px;
        }
        .report2 .area{/* 区 */
            position:absolute;
            top: calc(440px / var(--dpirate));
            left: calc(var(--firstwidth) + (1400px / var(--dpirate)));
            width: 756px;
        }
        .report2 .premcheck_id{/* 婚检编号 */
            position:absolute;
            top: calc(440px / var(--dpirate));
            left: calc(var(--firstwidth) + (3270px / var(--dpirate)));
            width: 756px;
        }
        .report2 .hospital_fulladdress{
            position:absolute;
            top: calc(440px / var(--dpirate));
            left: calc(var(--firstwidth) + (1800px / var(--dpirate)));
            width: 1134px;
        }
        .report2 .name{
            position:absolute;
            top: calc(700px / var(--dpirate));
            left: calc(var(--firstwidth) + (1400px / var(--dpirate)));
            width: 756px;
        }
        .report2 .sex{
            position:absolute;
            top: calc(700px / var(--dpirate));
            left: calc(var(--firstwidth) + (3270px / var(--dpirate)));
            width: 378px;
        }
        .report2 .birthdayyear{
            position:absolute;
            top: calc(990px / var(--dpirate));
            left: calc(var(--firstwidth) + (1580px / var(--dpirate)));
            width: 756px;
        }
        .report2 .birthdaymonth{
            position:absolute;
            top: calc(990px / var(--dpirate));
            left: calc(var(--firstwidth) + (1930px / var(--dpirate)));
            width: 756px;
        }
        .report2 .birthdayday{
            position:absolute;
            top: calc(990px / var(--dpirate));
            left: calc(var(--firstwidth) + (2180px / var(--dpirate)));
            width: 756px;
        }
        .report2 .ethnic{/* 民族 */
            position:absolute;
            top: calc(1000px / var(--dpirate));
            left: calc(var(--firstwidth) + (3270px / var(--dpirate)));
            width: 756px;
        }
        .report2 .idcard{/* 身份证号 */
            position:absolute;
            top: calc(1250px / var(--dpirate));
            left: calc(var(--firstwidth) + (1200px / var(--dpirate)));
            width: 2000px;
        }
        .report2 .occupation{/* 职业 */
            position:absolute;
            top: calc(1550px / var(--dpirate));
            left: calc(var(--firstwidth) + (1200px / var(--dpirate)));
            width: 2000px;
        }
        .report2 .current_address{/* 现住址 */
            position:absolute;
            top: calc(1850px / var(--dpirate));
            left: calc(var(--firstwidth) + (1200px / var(--dpirate)));
            width: 2000px;
        }
        .report2 .other_name{/* 他人姓名 */
            position:absolute;
            top: calc(2100px / var(--dpirate));
            left: calc(var(--firstwidth) + (1200px / var(--dpirate)));
            width: 1000px;
        }
        .report2 .blrelationsno{/* 无血缘关系 */
            position:absolute;
            top: calc(2500px / var(--dpirate));
            left: calc(var(--firstwidth) + (2600px / var(--dpirate)));
            width: 500px;
        }
        .report2 .blrelationsyes{/* 有血缘关系 */
            position:absolute;
            top: calc(2500px / var(--dpirate));
            left: calc(var(--firstwidth) + (3270px / var(--dpirate)));
            width: 756px;
        }
        .report2 .premcheck_result{/* 婚前检查结果 第1，2联显示前面的半句，3联显示后面的半句 */
            position:absolute;
            top: calc(2950px / var(--dpirate));
            left: calc(var(--firstwidth) + (1200px / var(--dpirate)));
            width: 2000px;
        }
        .report2 .check_opinionone{
            position:absolute;
            top: calc(3700px / var(--dpirate));
            left: calc(var(--firstwidth) + (1000px / var(--dpirate)));
            width: 756px;
        }
        .report2 .check_opiniontwo{
            position:absolute;
            top: calc(3700px / var(--dpirate));
            left: calc(var(--firstwidth) + (2300px / var(--dpirate)));
            width: 756px;
        }
        .report2 .check_opinionthree{
            position:absolute;
            top: calc(3900px / var(--dpirate));
            left: calc(var(--firstwidth) + (1000px / var(--dpirate)));
            width: 756px;
        }
        .report2 .check_opinionfour{
            position:absolute;
            top: calc(3900px / var(--dpirate));
            left: calc(var(--firstwidth) + (2300px / var(--dpirate)));
            width: 756px;
        }
        .report2 .check_opinionfive{
            position:absolute;
            top: calc(4100px / var(--dpirate));
            left: calc(var(--firstwidth) + (1000px / var(--dpirate)));
            width: 756px;
        }
        .report2 .doctor_name{
            position:absolute;
            top: calc(4400px / var(--dpirate));
            left: calc(var(--firstwidth) + (1400px / var(--dpirate)));
            width: 756px;
        }
        .report2 .hospital_seal{
            position:absolute;
            top: calc(4400px / var(--dpirate));
            left: calc(var(--firstwidth) + (2600px / var(--dpirate)));
            width: 756px;
        }
        .report2 .timeyear{
            position:absolute;
            top: calc(4620px / var(--dpirate));
            left: calc(var(--firstwidth) + (3300px / var(--dpirate)));
            width: 378px;
        }
        .report2 .timemonth{
            position:absolute;
            top: calc(4620px / var(--dpirate));
            left: calc(var(--firstwidth) + (3680px / var(--dpirate)));
            width: 378px;
        }
        .report2 .timeday{
            position:absolute;
            top: calc(4620px / var(--dpirate));
            left: calc(var(--firstwidth) + (3910px / var(--dpirate)));
            width: 378px;
        }
        .report3{
            display: inline-block;
            width: calc(703px / var(--dpirate));
            height: calc(1002px / var(--dpirate));
        }
        .report3 .province{/* 省 */
            position:absolute;
            top: calc(440px / var(--dpirate));
            left: calc(var(--secondwidth) + (600px / var(--dpirate)));
            width: 756px;
        }
        .report3 .city{/* 市 */
            position:absolute;
            top: calc(440px / var(--dpirate));
            left: calc(var(--secondwidth) + (1000px / var(--dpirate)));
            width: 756px;
        }
        .report3 .area{/* 区 */
            position:absolute;
            top: calc(440px / var(--dpirate));
            left: calc(var(--secondwidth) + (1400px / var(--dpirate)));
            width: 756px;
        }
        .report3 .premcheck_id{/* 婚检编号 */
            position:absolute;
            top: calc(440px / var(--dpirate));
            left: calc(var(--secondwidth) + (3270px / var(--dpirate)));
            width: 756px;
        }
        .report3 .hospital_fulladdress{
            position:absolute;
            top: calc(440px / var(--dpirate));
            left: calc(var(--secondwidth) + (1800px / var(--dpirate)));
            width: 1134px;
        }
        .report3 .name{
            position:absolute;
            top: calc(700px / var(--dpirate));
            left: calc(var(--secondwidth) + (1400px / var(--dpirate)));
            width: 756px;
        }
        .report3 .sex{
            position:absolute;
            top: calc(700px / var(--dpirate));
            left: calc(var(--secondwidth) + (3270px / var(--dpirate)));
            width: 378px;
        }
        .report3 .birthdayyear{
            position:absolute;
            top: calc(1000px / var(--dpirate));
            left: calc(var(--secondwidth) + (1600px / var(--dpirate)));
            width: 756px;
        }
        .report3 .birthdaymonth{
            position:absolute;
            top: calc(1000px / var(--dpirate));
            left: calc(var(--secondwidth) + (1980px / var(--dpirate)));
            width: 756px;
        }
        .report3 .birthdayday{
            position:absolute;
            top: calc(1000px / var(--dpirate));
            left: calc(var(--secondwidth) + (2230px / var(--dpirate)));
            width: 756px;
        }
        .report3 .ethnic{/* 民族 */
            position:absolute;
            top: calc(1000px / var(--dpirate));
            left: calc(var(--secondwidth) + (3270px / var(--dpirate)));
            width: 756px;
        }
        .report3 .idcard{/* 身份证号 */
            position:absolute;
            top: calc(1250px / var(--dpirate));
            left: calc(var(--secondwidth) + (1250px / var(--dpirate)));
            width: 2000px;
        }
        .report3 .occupation{/* 职业 */
            position:absolute;
            top: calc(1500px / var(--dpirate));
            left: calc(var(--secondwidth) + (1200px / var(--dpirate)));
            width: 2000px;
        }
        .report3 .current_address{/* 现住址 */
            position:absolute;
            top: calc(1800px / var(--dpirate));
            left: calc(var(--secondwidth) + (1200px / var(--dpirate)));
            width: 2000px;
        }
        .report3 .other_name{/* 他人姓名 */
            position:absolute;
            top: calc(2100px / var(--dpirate));
            left: calc(var(--secondwidth) + (1200px / var(--dpirate)));
            width: 1000px;
        }
        .report3 .blrelationsno{/* 无血缘关系 */
            position:absolute;
            top: calc(2500px / var(--dpirate));
            left: calc(var(--secondwidth) + (2750px / var(--dpirate)));
            width: 500px;
        }
        .report3 .blrelationsyes{/* 有血缘关系 */
            position:absolute;
            top: calc(2500px / var(--dpirate));
            left: calc(var(--secondwidth) + (3220px / var(--dpirate)));
            width: 756px;
        }
        .report3 .premcheck_result{/* 婚前检查结果 第1，2联显示前面的半句，3联显示后面的半句 */
            position:absolute;
            top: calc(2950px / var(--dpirate));
            left: calc(var(--secondwidth) + (1200px / var(--dpirate)));
            width: 2000px;
        }
        .report3 .check_opinionone{
            position:absolute;
            top: calc(3700px / var(--dpirate));
            left: calc(var(--secondwidth) + (1000px / var(--dpirate)));
            width: 756px;
        }
        .report3 .check_opiniontwo{
            position:absolute;
            top: calc(3700px / var(--dpirate));
            left: calc(var(--secondwidth) + (2320px / var(--dpirate)));
            width: 756px;
        }
        .report3 .check_opinionthree{
            position:absolute;
            top: calc(3900px / var(--dpirate));
            left: calc(var(--secondwidth) + (1000px / var(--dpirate)));
            width: 756px;
        }
        .report3 .check_opinionfour{
            position:absolute;
            top: calc(3900px / var(--dpirate));
            left: calc(var(--secondwidth) + (2320px / var(--dpirate)));
            width: 756px;
        }
        .report3 .check_opinionfive{
            position:absolute;
            top: calc(4100px / var(--dpirate));
            left: calc(var(--secondwidth) + (1000px / var(--dpirate)));
            width: 756px;
        }
        .report3 .doctor_name{
            position:absolute;
            top: calc(4400px / var(--dpirate));
            left: calc(var(--secondwidth) + (1400px / var(--dpirate)));
            width: 756px;
        }
        .report3 .hospital_seal{
            position:absolute;
            top: calc(4400px / var(--dpirate));
            left: calc(var(--secondwidth) + (2750px / var(--dpirate)));
            width: 756px;
        }
        .report3 .timeyear{
            position:absolute;
            top: calc(4650px / var(--dpirate));
            left: calc(var(--secondwidth) + (3350px / var(--dpirate)));
            width: 378px;
        }
        .report3 .timemonth{
            position:absolute;
            top: calc(4650px / var(--dpirate));
            left: calc(var(--secondwidth) + (3750px / var(--dpirate)));
            width: 378px;
        }
        .report3 .timeday{
            position:absolute;
            top: calc(4650px / var(--dpirate));
            left: calc(var(--secondwidth) + (3960px / var(--dpirate)));
            width: 378px;
        }
    </style>
</head>
<body style="overflow:auto;">
    <!-- 背景图 style="border:1px solid red;background: url('../../images/prem/printbk.png') no-repeat center 0px background-size: cover;" -->
    <div class="jc_content" id="jc_content" style="width: 575mm;">
        <div id="report1" class="report1" style="background-repeat: no-repeat;height: 274mm;
    width: 190mm; background-image: url(../../images/prem/printbk.png);background-size: 173mm 260mm;">
            <span id="birthday1" class="birthday"></span>
            <span id="occupation1" class="occupation"></span>
            <span id="doctor_name1" class="doctor_name"></span>
            <span id="hospital_seal1" class="hospital_seal"></span>
            <span id="premcheck_id1" class="premcheck_id"></span>
            <span id="hospital_fulladdress1" class="hospital_fulladdress"></span>
            <span id="hospital_address1" class="hospital_address"></span>
            <span id="ethnic1" class="ethnic"></span>
            <span id="premcheck_result1" class="premcheck_result"></span>
            <span id="idcard1" class="idcard"></span>
            <span id="other_name1" class="other_name"></span>
            <span id="name1" class="name"></span>
            <span id="sex1" class="sex"></span>
            <span id="time1" class="time"></span>
            <span id="current_address1" class="current_address"></span>
            <span id="blrelationsno1" class="blrelationsno"></span>
            <span id="blrelationsyes1" class="blrelationsyes"></span>
            <span id="province1" class="province"></span>
            <span id="city1" class="city"></span>
            <span id="area1" class="area"></span>
            <span id="check_opinionon1" class="check_opinionone"></span>
            <span id="check_opiniontwo1" class="check_opiniontwo"></span>
            <span id="check_opinionthree1" class="check_opinionthree"></span>
            <span id="check_opinionfour1" class="check_opinionfour"></span>
            <span id="check_opinionfive1" class="check_opinionfive"></span>
            <span id="timeyear1" class="timeyear"></span>
            <span id="timemonth1" class="timemonth"></span>
            <span id="timeday1" class="timeday"></span>
            <span id="birthdayyear1" class="birthdayyear"></span>
            <span id="birthdaymonth1" class="birthdaymonth"></span>
            <span id="birthdayday1" class="birthdayday"></span>
        </div>
        <div id="report2" class="report2" style="background-repeat: no-repeat;height: 274mm;
    width: 166mm; background-image: url(../../images/prem/printbk.png);background-size:166mm 264mm;">
            <span id="birthday2" class="birthday"></span>
            <span id="occupation2" class="occupation"></span>
            <span id="doctor_name2" class="doctor_name"></span>
            <span id="hospital_seal2" class="hospital_seal"></span>
            <span id="premcheck_id2" class="premcheck_id"></span>
            <span id="hospital_fulladdress2" class="hospital_fulladdress"></span>
            <span id="hospital_address2" class="hospital_address"></span>
            <span id="ethnic2" class="ethnic"></span>
            <span id="premcheck_result2" class="premcheck_result"></span>
            <span id="idcard2" class="idcard"></span>
            <span id="other_name2" class="other_name"></span>
            <span id="name2" class="name"></span>
            <span id="sex2" class="sex"></span>
            <span id="time2" class="time"></span>
            <span id="current_address2" class="current_address"></span>
            <span id="blrelationsno2" class="blrelationsno"></span>
            <span id="blrelationsyes2" class="blrelationsyes"></span>
            <span id="province2" class="province"></span>
            <span id="city2" class="city"></span>
            <span id="area2" class="area"></span>
            <span id="check_opinionon2" class="check_opinionone"></span>
            <span id="check_opiniontwo2" class="check_opiniontwo"></span>
            <span id="check_opinionthree2" class="check_opinionthree"></span>
            <span id="check_opinionfour2" class="check_opinionfour"></span>
            <span id="check_opinionfive2" class="check_opinionfive"></span>
            <span id="timeyear2" class="timeyear"></span>
            <span id="timemonth2" class="timemonth"></span>
            <span id="timeday2" class="timeday"></span>
            <span id="birthdayyear2" class="birthdayyear"></span>
            <span id="birthdaymonth2" class="birthdaymonth"></span>
            <span id="birthdayday2" class="birthdayday"></span>
        </div>
        <div id="report3" class="report3" style="background-repeat: no-repeat;height: 274mm;
    width: 190mm; background-image: url(../../images/prem/printbk.png);background-size:166mm 264mm;float: right;">
            <span id="birthday3" class="birthday"></span>
            <span id="occupation3" class="occupation"></span>
            <span id="doctor_name3" class="doctor_name"></span>
            <span id="hospital_seal3" class="hospital_seal"></span>
            <span id="premcheck_id3" class="premcheck_id"></span>
            <span id="hospital_fulladdress3" class="hospital_fulladdress"></span>
            <span id="hospital_address3" class="hospital_address"></span>
            <span id="ethnic3" class="ethnic"></span>
            <span id="premcheck_result3" class="premcheck_result"></span>
            <span id="idcard3" class="idcard"></span>
            <span id="other_name3" class="other_name"></span>
            <span id="name3" class="name"></span>
            <span id="sex3" class="sex"></span>
            <span id="time3" class="time"></span>
            <span id="current_address3" class="current_address"></span>
            <span id="blrelationsno3" class="blrelationsno"></span>
            <span id="blrelationsyes3" class="blrelationsyes"></span>
            <span id="province3" class="province"></span>
            <span id="city3" class="city"></span>
            <span id="area3" class="area"></span>
            <span id="check_opinionon3" class="check_opinionone"></span>
            <span id="check_opiniontwo3" class="check_opiniontwo"></span>
            <span id="check_opinionthree3" class="check_opinionthree"></span>
            <span id="check_opinionfour3" class="check_opinionfour"></span>
            <span id="check_opinionfive3" class="check_opinionfive"></span>
            <span id="timeyear3" class="timeyear"></span>
            <span id="timemonth3" class="timemonth"></span>
            <span id="timeday3" class="timeday"></span>
            <span id="birthdayyear3" class="birthdayyear"></span>
            <span id="birthdaymonth3" class="birthdaymonth"></span>
            <span id="birthdayday3" class="birthdayday"></span>
        </div>
        <div class="layui-input-block" style="display: none">
            <a id="printBtn" class="layui-btn">打印</a>
        </div>
    </div>
</body>
<script>
    //获取屏幕DPI
    function getDPI() {
        let dpi = window.devicePixelRatio || 96;
        if (window.screen.deviceXDPI) {
            dpi = window.screen.deviceXDPI;
        } else {
            // 计算近似的 DPI
            const diagonalPixels = Math.sqrt(window.screen.width ** 2 + window.screen.height ** 2);
            const diagonalInches = Math.sqrt(window.screen.width / 96 * 2.54 ** 2 + window.screen.height / 96 * 2.54 ** 2);
            dpi = diagonalPixels / diagonalInches;
        }
        console.log("DPI -> ",dpi);
        return dpi;
    }
    document.documentElement.style.setProperty('--dpi', getDPI());
</script>
<script type="text/javascript" src="../../plugin/htmltopdf/js/html2canvas.js"></script>
<script type="text/javascript" src="../../plugin/htmltopdf/js/jspdf.debug.js"></script>
<script type="text/javascript" data-main="../../js/prem/premedicalexamprint" src="../../sys/require.min.js"></script>
</html>