﻿<!DOCTYPE html>
<html style="overflow: hidden;">
<head>
    <meta charset="utf-8" />
    <title>当前上报</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <link rel="stylesheet" href="../../css/reset.css" />
    <link rel="stylesheet" href="../../css/style.css" />
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../styles/xbcomstyle.css">
    <!--[if lt IE 9]>
    <script src='../../sys/html5shiv.min.js'></script>
    <![endif]-->
    <style>
        body { background: #EAEFF3; }
        .sp-badge, .sp-count { display: none; }
        .report-list { display: none; overflow: auto; }
            .report-list h5 { height: 34px; line-height: 34px; background: transparent; margin: 10px 0px 0px 0px; padding-left: 15px; position: relative; color: #34495E; }
        .person-tab tr th, .roleTitle th { border: 1px solid #eee; background-color: #f9f9f9; font-weight: normal; }
        .layui-tab-title { height: 47px; }
            .layui-tab-title li { line-height: 47px; }
            .layui-tab-title .layui-this::after { height: 47px; }
    </style>
</head>
<body style="background-color: #f4f5f7;">
    <section style="">
        <div class="content-medical marleft" style="border-radius: 0px;">
            <div class="layui-tab layui-tab-brief tab-tit" style="margin-bottom: 0;">
                <ul class="layui-tab-title">
                    <li class="layui-this">当前上报<span class="sp-count"></span><span class="layui-badge sp-badge" title="不通过">0</span></li>
                    <li>已截止<span class="sp-count"></span><span class="layui-badge sp-badge">0</span></li>
                    <!--                <li>基础信息上报</li>-->
                    <!--                <li>文件上报</li>-->
                </ul>
                <button id="btnrefresh1" class="layui-btn addbtn" style="float: right; margin-right: 20px; z-index: 999; margin-top: 8px; display: none;">刷新</button>
                <button id="btnaddfile1" class="layui-btn addbtn" style="float: right; margin-right: 20px; z-index: 999; margin-top: 8px; display: none;">添加</button>
            </div>
        </div>
        <div class="marmain topmar">
            <div class="content-medical">
                <div class="div-report">
                    <form class="layui-form" action="">
                        <div class="layui-form-item" style="display: inline-block; margin: 15px 0 0 5px; display: none;">
                            <div class="layui-inline">
                                <label class="layui-form-label" style="width: 100px;">上报截止年份：</label>
                                <div class="layui-input-block" style="">
                                    <input id="txtyear" type="text" class="layui-input" autocomplete="off" style="display: inline-block; width: 80px;" /><span class="linebg">-</span><input id="txtyear2" type="text" class="layui-input" autocomplete="off" style="display: inline-block; width: 80px;" />
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item" style="margin: 15px 0 0 10px; display: inline-flex;">
                            <label class="layui-form-label">
                                <label id="labjdmonthname"></label>
                                ：</label>
                            <div class="layui-input-inline" style="width: 80px;">
                                <input id="txtyear3" type="text" class="layui-input" autocomplete="off" style="display: inline-block; width: 80px;" readonly="readonly" />
                            </div>
                            <div class="layui-input-inline" style="width: 100px;">
                                <select id="seljdmonth">
                                </select>
                            </div>
                        </div>
                        <div class="layui-form-item" style="display: inline-block; margin: 15px 0 0 5px;">
                            <label class="layui-form-label">上报类型：</label>
                            <div class="layui-input-block" style="width: 300px; margin-left: 84px;">
                                <select id="selreporttype"></select>
                            </div>
                        </div>
                        <div class="layui-form-item" style="display: inline-block; margin: 15px 0 0 5px;">
                            <label class="layui-form-label">上报名称：</label>
                            <div class="layui-input-block" style="width: 210px; margin-left: 84px;">
                                <input type="text" name="name" required lay-verify="required" placeholder="" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <a id="btnsearch" class="layui-btn btn-search" style="margin: -2px 0 0 10px;">查询</a>
                    </form>
                </div>
                <!--当前上报-->
                <section class="report-list person-tab">
                    <!--年/半年上报列表-->
                    <section class="sec_second">
                        <h5><i class="left-tit"></i>年/半年上报列表（<span class="sp_num">0</span>）</h5>
                        <div style="margin: 0px 0px;">
                            <table border="1" cellpadding="0" cellspacing="0" style="width: 100%; border-collapse: collapse;">
                                <tr style="background: #F2F2F2;">
                                    <td style="width: 22%;">上报名称</td>
                                    <td style="width: 8%;">上报类型</td>
                                    <td style="width: 8%;">表的个数</td>
                                    <td style="width: 10%;">上报活动状态</td>
                                    <td style="width: 10%;">上报截止日期</td>
                                    <td style="width: 8%;">报表是否完善</td>
                                    <td style="width: 8%;">上报状态</td>
                                    <td style="width: 8%;">审核状态</td>
                                    <td style="width: 17%;">操作</td>
                                </tr>
                                <tbody class="tb_body">
                                </tbody>
                            </table>
                        </div>
                    </section>
                    <!--按月度上报列表-->
                    <section class="sec_month">
                        <h5><i class="left-tit"></i>按月度上报列表（<span class="sp_num">0</span>）</h5>
                        <div style="margin: 0px 0px;">
                            <table border="1" cellpadding="0" cellspacing="0" style="width: 100%; border-collapse: collapse;">
                                <tr style="background: #F2F2F2;">
                                    <td style="width: 22%;">上报名称</td>
                                    <td style="width: 6%;">上报类型</td>
                                    <td style="width: 5%;">表的个数</td>
                                    <td style="width: 5%;">上报月份</td>
                                    <td style="width: 10%;">上报活动状态</td>
                                    <td style="width: 10%;">上报截止日期</td>
                                    <td style="width: 8%;">报表是否完善</td>
                                    <td style="width: 8%;">上报状态</td>
                                    <td style="width: 8%;">审核状态</td>
                                    <td style="width: 17%;">操作</td>
                                </tr>
                                <tbody class="tb_body">
                                </tbody>
                            </table>
                        </div>
                    </section>
                    <!--按季度上报列表-->
                    <section class="sec_quarter">
                        <h5><i class="left-tit"></i>按季度上报列表（<span class="sp_num">0</span>）</h5>
                        <div style="margin: 0px 0px;">
                            <table border="1" cellpadding="0" cellspacing="0" style="width: 100%; border-collapse: collapse;">
                                <tr style="background: #F2F2F2;">
                                    <td style="width: 22%;">上报名称</td>
                                    <td style="width: 6%;">上报类型</td>
                                    <td style="width: 5%;">表的个数</td>
                                    <td style="width: 5%;">上报季度</td>
                                    <td style="width: 10%;">上报活动状态</td>
                                    <td style="width: 10%;">上报截止日期</td>
                                    <td style="width: 8%;">报表是否完善</td>
                                    <td style="width: 8%;">上报状态</td>
                                    <td style="width: 8%;">审核状态</td>
                                    <td style="width: 17%;">操作</td>
                                </tr>
                                <tbody class="tb_body">
                                </tbody>
                            </table>
                        </div>
                    </section>
                </section>
                <!--历史上报-->
                <section class="report-list person-tab">
                    <!--年/半年上报列表-->
                    <section class="sec_second">
                        <h5><i class="left-tit"></i>年/半年上报列表（<span class="sp_num">0</span>）</h5>
                        <div style="margin: 0px 0px;">
                            <table border="1" cellpadding="0" cellspacing="0" style="width: 100%; border-collapse: collapse;">
                                <tr style="background: #F2F2F2;">
                                    <td style="width: 22%;">上报名称</td>
                                    <td style="width: 8%;">上报类型</td>
                                    <td style="width: 8%;">表的个数</td>
                                    <td style="width: 10%;">上报活动状态</td>
                                    <td style="width: 10%;">上报截止日期</td>
                                    <td style="width: 8%;">报表是否完善</td>
                                    <td style="width: 8%;">上报状态</td>
                                    <td style="width: 8%;">审核状态</td>
                                    <td style="width: 17%;">操作</td>
                                </tr>
                                <tbody class="tb_body">
                                </tbody>
                            </table>
                        </div>
                    </section>
                    <!--按月度上报列表-->
                    <section class="sec_month">
                        <h5><i class="left-tit"></i>按月度上报列表（<span class="sp_num">0</span>）</h5>
                        <div style="margin: 0px 0px;">
                            <table border="1" cellpadding="0" cellspacing="0" style="width: 100%; border-collapse: collapse;">
                                <tr style="background: #F2F2F2;">
                                    <td style="width: 22%;">上报名称</td>
                                    <td style="width: 6%;">上报类型</td>
                                    <td style="width: 5%;">表的个数</td>
                                    <td style="width: 5%;">上报月份</td>
                                    <td style="width: 10%;">上报活动状态</td>
                                    <td style="width: 10%;">上报截止日期</td>
                                    <td style="width: 8%;">报表是否完善</td>
                                    <td style="width: 8%;">上报状态</td>
                                    <td style="width: 8%;">审核状态</td>
                                    <td style="width: 17%;">操作</td>
                                </tr>
                                <tbody class="tb_body">
                                </tbody>
                            </table>
                        </div>
                    </section>
                    <!--按季度上报列表-->
                    <section class="sec_quarter">
                        <h5><i class="left-tit"></i>按季度上报列表（<span class="sp_num">0</span>）</h5>
                        <div style="margin: 0px 0px;">
                            <table border="1" cellpadding="0" cellspacing="0" style="width: 100%; border-collapse: collapse;">
                                <tr style="background: #F2F2F2;">
                                    <td style="width: 22%;">上报名称</td>
                                    <td style="width: 6%;">上报类型</td>
                                    <td style="width: 5%;">表的个数</td>
                                    <td style="width: 5%;">上报季度</td>
                                    <td style="width: 10%;">上报活动状态</td>
                                    <td style="width: 10%;">上报截止日期</td>
                                    <td style="width: 8%;">报表是否完善</td>
                                    <td style="width: 8%;">上报状态</td>
                                    <td style="width: 8%;">审核状态</td>
                                    <td style="width: 17%;">操作</td>
                                </tr>
                                <tbody class="tb_body">
                                </tbody>
                            </table>
                        </div>
                    </section>
                </section>
                <!--基础信息上报-->
                <section class="report-list">
                    <ul class="define-form">
                        <li>
                            <span>类型：</span>
                            <select id="seltype" style="width: 100px;">
                                <option value="0">区网列表</option>
                                <option value="1">省网列表</option>
                            </select>
                        </li>
                        <li>
                            <span>选择学年：</span>
                            <label>
                                <select id="selyear"></select></label>
                        </li>
                        <li>
                            <span>幼儿姓名：</span>
                            <label>
                                <input type="text" class="txtname" style="width: 100px;"></label>
                        </li>
                        <li>
                            <span>出生日期：</span>
                            <label><span class="cal-span">
                                <input class="txtmindate Wdate" type="text" style="width: 100px;" placeholder="请选择"></span><span style="margin: 0 5px;">至</span><span class="cal-span"><input class="txtmaxdate Wdate" style="width: 100px;" type="text" placeholder="请选择"></span></label>
                        </li>
                        <li>
                            <span>性别：</span>
                            <select class="selsex" style="width: 70px;">
                                <option value="">全部</option>
                                <option value="男">男</option>
                                <option value="女">女</option>
                            </select>
                        </li>
                        <li>
                            <a class="opr-btn btn-search" style="height: 25px; line-height: 25px; width: 60px; color: #fff; text-decoration: inherit;">查询</a>
                            <a class="opr-btn btn-out" style="height: 25px; line-height: 25px; width: 60px; color: #fff; text-decoration: inherit; margin-left: 10px;">导出</a>
                        </li>
                    </ul>
                    <div class="person-tab">
                        <div class="alle-tab">
                            <table border="1" cellpadding="0" cellspacing="0" class="grdlist">
                            </table>
                        </div>
                    </div>
                </section>
                <!--文件上报-->
                <section class="report-list secfilereport">
                    <div class="layui-form-item" style="display: inline-block; margin: 10px 0 0 5px;">
                        <label class="layui-form-label" style="width: 100px;">主题名称：</label>
                        <div class="layui-input-block" style="width: 210px; margin-left: 130px;">
                            <input type="text" class="layui-input txtthemename" style="width: 150px;">
                        </div>
                    </div>
                    <div class="layui-form-item" style="display: inline-block; margin: 10px 0 0 5px;">
                        <label class="layui-form-label">上报时间：</label>
                        <div class="layui-input-block" style="width: 310px;">
                            <input id="txtfileupdate1" type="text" name="name" required lay-verify="required" placeholder="" autocomplete="off" class="layui-input" style="width: 120px; display: inline-block;" /><i style="vertical-align: top; line-height: 36px; margin: 0 7px 0 6px;">—</i><input id="txtfileupdate2" type="text" name="name" required lay-verify="required" placeholder="" autocomplete="off" class="layui-input" style="width: 120px; display: inline-block;" />
                        </div>
                        <!--                  <label><span class="cal-span"><input class="txtmindate Wdate" type="text" style="width:100px;" placeholder="请选择"></span><span style="margin: 0 5px;">至</span><span class="cal-span"><input class="txtmaxdate Wdate" style="width:100px;" type="text" placeholder="请选择"></span></label> -->

                    </div>
                    <span><a class="layui-btn btn-search" style="margin: -2px 0 0 -20px; color: #fff;">查询</a></span>
                    <div class="person-tab" style="margin-top: 10px;">
                        <div class="alle-tab">
                            <table border="1" cellpadding="0" cellspacing="0" class="grdlistfile">
                            </table>
                        </div>
                    </div>
                </section>
                <!-- 无数据显示 -->
                <section class="report-none">
                    <img src="../../images/nodata.png" class="nodata-img">
                </section>
                <!--未关联提示-->
                <section class="sec-noarea" style="height: 100%; display: none;">
                    <div class="no-gover">
                        <p>你所在的园所还没有关联上级妇幼，无法进行上报操作，请打开“加入上级部门”后进行关联妇幼。</p>
                        <p><a class="cl2">前去加入上级部门</a></p>
                    </div>

                </section>
            </div>
        </div>
    </section>
    <script data-main="../../js/citysb/reportsblist" src="../../sys/require.min.js"></script>
</body>
</html>
