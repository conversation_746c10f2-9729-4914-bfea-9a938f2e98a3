﻿/*
日期： 
作者： 
功能：营养不良儿童管理卡
*/
var thsetting = {
    arrstuno: [], //学生的学号  用于上一个下一个
    strsex: '',
    strbirthday: '',
    curstuname: '',
    hest: 2,
    objstrorder: {},//"sex desc,ek_student.birthday,ek_student.stuno",
    curstuno: ''//当前学生的学号
    , objstuinfo: {}
};
require.config({
    paths: {
        sys: "../../sys/system",
        jquery: "../../sys/jquery",
        editselect: "../../plugin/editselect/js/editselect",
        validator: "../../plugin/validator/js/validator",
        wdatepicker: "../../plugin/wdatepicker/wdatepicker"
    }, shim: {
        contextmenu: ["jquery"]
    },
    waitSeconds: 0
});
var parentobj = null;
require(["jquery", "sys"], function () {
    require(["editselect", "validator", "wdatepicker"], function () {
        parentobj = jQuery.getparent().$("#win_" + Arg('mid')).children('iframe')[0].contentWindow;
        thsetting.objstrorder = parentobj.getOrderWhere();
        initclassdata(Arg("id"));

        $("#txtvestheight").change(function () {
            heightweight(1);
        });
        $("#txtvestweight").change(function () {
            heightweight(2);
        });
        $("#txtthinheight").change(function () {
            heightweight(7);
        });
        $("#txtthinweight").change(function () {
            heightweight(8);
        });
        $('#chkvest').click(function () {
            check(0);
        });
        $("#btnprev").click(function () {
            upanddown(0);
        });
        $("#btnnext").click(function () {
            upanddown(1);
        });
        $("#txtenddate").click(function () {
            initdate('vest', this);
        });
        $("#txtdate").click(function () {
            initdate('thin', this);
        });
        $("#btnsave").click(function (event, callback) {
            btnsave(thsetting.curstuno, callback);
        });//保存
        $(document).scroll(function () {//滚动固定表头
            // console.log($("#t_bodyhead").offset().top + ":" + $(document).scrollTop());
            if ($("#t_bodyhead").offset().top - $(document).scrollTop() <= 0) {
                $("#tbl_clone").show().css('margin-top', $(document).scrollTop() - $("#t_bodyhead").offset().top).html('<tbody>' + $("#t_bodyhead").html() + '</tbody>');
            } else {
                $("#tbl_clone").hide().html('');
            }
        });
        initselect1();
    });
});

/*
功能：初始化下拉框
*/
function initselect1() {
    $('#selmt').editableSelect({
        bg_iframe: true,
        isreadonly: 1,
        width: '80px',
        case_sensitive: false, // If set to true, the user has to type in an exact
        items_then_scroll: 10// If there are more than 10 items, display a scrollbar
    });
};

function check() {
    if ($("#chkvest")[0].checked) {
        $('#txtvestheight').prop('disabled', false);
        $('#txtvestweight').prop('disabled', false);
        $('#txtenddate').prop('disabled', false);
        $('#txtenddate').attr('require', true);
        $('#selvest').replaceWith('<label id="selvest"></label>');
        var arrdata = [['痊愈', "0"], ['好转', "1"], ['转医院', "2"], ['未愈', "3"], ['离园', "4"]];
        $('#selvest').editableSelect({
            bg_iframe: true,
            isreadonly: 1,
            arrdata: arrdata,
            deftext: '痊愈',
            defvalue: "0",
            width: '50px',
            case_sensitive: false, // If set to true, the user has to type in an exact
            items_then_scroll: 10// If there are more than 10 items, display a scrollbar
        });
    } else {
        $('#txtenddate').attr('require', false);
        $('#txtvestheight').prop('disabled', true);
        $('#txtvestweight').prop('disabled', true);
        $('#txtenddate').prop('disabled', true);
        $('#txtvestheight').val("");
        $('#txtvestweight').val("");
        $('#txtenddate').val("");
        $('#selvest').replaceWith('<label id="selvest"></label>');
        $('#txtvestmalnu').html("");
        $('#txtvestgrade').html("");
    }
};

/*
功能：加载数据
*/
function initclassdata(stuno) {
    jQuery.getparent().layer.load();
    $("#divtbody").html("");
    thsetting.curstuno = stuno;
    var strdate = parentobj.tongsetting.cursysdate;
    $.sm(function (re, err) {
        if (err) {
            jQuery.getparent().layer.msg(err, {icon: 5});
            jQuery.getparent().layer.closeAll('loading');
        } else if (re && re[0]) {
            thsetting.strbirthday = re[0][4];
            thsetting.strsex = re[0][2];
            thsetting.hest = re[0][15];
            thsetting.curstuname = re[0][1];
            var strhtml = '<tr><td style="width: 20%; border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">姓名</td><td id="txtname" style="width: 30%; border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">' + re[0][1] + '</td>' +
                '<td style="width: 20%; border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">性别</td><td id="txtsex" style="width: 30%; border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">' + re[0][2] + '</td></tr>' +
                '<tr><td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">班级</td><td id="txtclass" style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">' + re[0][3] + '</td>' +
                '<td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">出生年月</td><td id="txtdirthday" style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">' + re[0][4] + '</td></tr>' +
                '<tr><td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">父亲身高</td><td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;"><input id="txtfheight" maxlength="20" type="text" style="width: 95%;" value="' + re[0][5] + '" onchange="heightweight(3)"class="tjzagl_input" />  <span class="FancyInput__bar___1P3wW" ></span></td>' +
                '<td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;" >父亲体重</td><td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;"><input id="txtfweight" maxlength="20" type="text" style="width: 95%;" value="' + re[0][6] + '" onchange="heightweight(4)" class="tjzagl_input" />  <span class="FancyInput__bar___1P3wW" ></span></td></tr>' +
                '<tr><td style="border-right:solid 1px #d8d8d8; border-bottom:solid 1px #d8d8d8;" >母亲身高</td><td style="border-right:solid 1px #d8d8d8; border-bottom:solid 1px #d8d8d8;"><input id="txtmheight" type="text" style="width: 95%;"  value="' + re[0][7] + '" onchange="heightweight(5)" class="tjzagl_input" />  <span class="FancyInput__bar___1P3wW" ></span></td>' +
                '<td style="border-right:solid 1px #d8d8d8;border-bottom:solid 1px #d8d8d8;" >母亲体重</td><td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;"><input id="txtmweight" type="text" style="width: 95%;" value="' + re[0][8] + '" onchange="heightweight(6)"class="tjzagl_input" />  <span class="FancyInput__bar___1P3wW" ></span></td></tr>' +
                '<tr><td style="border-right: #d8d8d8 1px solid; border-bottom:#d8d8d8 1px solid;">其他营养<br />不良成员</td><td colspan="3" style="border-right: #d8d8d8 1px solid; border-bottom:#d8d8d8 1px solid;"><input type="text" id="txtotherfat" style="width:95%; height:30px;" maxlength="50" value="' + re[0][9] + '" class="tjzagl_input" />  <span class="FancyInput__bar___1P3wW" ></span></td></tr>';
            $("#txtthinmalnu").html(re[0][14]);
            $("#txtdate").val(re[0][11]);
            $("#txtthinweight").val(re[0][13]);
            $("#txtthinheight").val(re[0][12]);
            $("#selmt").val(re[0][16]);
            $("#txtcondition").val(re[0][10]);
            if (re[0][17] == '1') {
                $('#chkvest').prop('checked', true);
                $('#txtenddate').attr('require', true);
                $('#txtvestheight').prop('disabled', false);
                $('#txtvestweight').prop('disabled', false);
                $('#txtenddate').prop('disabled', false);
                $('#txtvestheight').val(re[0][20]);
                $('#txtvestweight').val(re[0][21]);
                $('#txtenddate').val(re[0][18]);

                $('#selvest').replaceWith('<label id="selvest"></label>');
                var arrdata = [['痊愈', "0"], ['好转', "1"], ['转医院', "2"], ['未愈', "3"], ['离园', "4"]];
                $('#selvest').editableSelect({
                    bg_iframe: true,
                    isreadonly: 1,
                    arrdata: arrdata,
                    deftext: '痊愈',
                    defvalue: "0",
                    width: '50px',
                    case_sensitive: false, // If set to true, the user has to type in an exact
                    items_then_scroll: 10// If there are more than 10 items, display a scrollbar
                });
                $('#selvest').val(re[0][22]);
            } else {
                $('#chkvest').prop('checked', false);
                $('#txtenddate').attr('require', false);
                $('#txtvestheight').prop('disabled', true);
                $('#txtvestweight').prop('disabled', true);
                $('#txtenddate').prop('disabled', true);
                $('#txtvestheight').val("");
                $('#txtvestweight').val("");
                $('#txtenddate').val("");
                $('#selvest').replaceWith('<label id="selvest"></label>');
                $('#txtvestmalnu').html("");
                $('#txtvestgrade').html("");
            }
            $('#txtvestmalnu').html(re[0][19]);
            $('#txtvestgrade').html(re[0][23]);
            $("#divfattable").html(strhtml);
            init(stuno);
        } else {
            jQuery.getparent().layer.msg("未查到有关数据！");
            jQuery.getparent().layer.closeAll('loading');
        }
    }, ["yyblcard.initclassdata", stuno, strdate]);
};

/*
功能：初始化下拉框
*/
function initselect(num, isdisabled) {
    $('#selwlw' + num).editableSelect({
        bg_iframe: true,
        isreadonly: 1,
        width: '50px',
        isdisabled: isdisabled,
        onSelect: initselclick, //选择事件
        case_sensitive: false, // If set to true, the user has to type in an exact
        items_then_scroll: 10// If there are more than 10 items, display a scrollbar
    });
};

function initselclick() {
    var strid = $(this.text).attr("id").substring(0, 6);
    if (strid == "selwlw") {
        var num = $(this.text).attr("id").substring(6);
        var hest = $("#selwlw" + num).val();
        var height = $("#txtheight" + num).val();
        var weight = $("#txtweight" + num).val();
        if (height != "" && weight != "") {
            var arrhwle = parentobj.tongsetting.hwle["a_" + thsetting.strsex + "_" + height + "_" + hest];
            if (arrhwle) {
                var strhw = parentobj.GetHW(weight, [arrhwle[0], arrhwle[1], arrhwle[2], arrhwle[4], arrhwle[5], arrhwle[6], arrhwle[3]]);
                var strfat = parentobj.GetFat(arrhwle[3], weight);
                $("#txtfat" + num).val(strfat);
                $("#txthewe" + num).html(parentobj.GetHW(weight, [arrhwle[0], arrhwle[1], arrhwle[2], arrhwle[4], arrhwle[5], arrhwle[6], arrhwle[3]]));
            }
        }
        var ageh = $("#txtageh" + num).html();
        var agew = $("#txtagew" + num).html();
        var hewe = $("#txthewe" + num).html();
        thsetting.objstuinfo[num].sex = thsetting.strsex;
        thsetting.objstuinfo[num].hight = height;
        thsetting.objstuinfo[num].weight = weight;
        thsetting.objstuinfo[num].hest = hest || 2;
        var age = thsetting.objstuinfo[num].age || $("#txtage" + num).attr("age");
        var bmi = parentobj.getbmi(height, weight);
        var arrmenu = parentobj.GetThinArr(ageh, agew, hewe, thsetting.objstuinfo[num]);
        var yyblpj = parentobj.getThin(ageh, agew, hewe, bmi, parentobj.tongsetting.strnormal, age, thsetting.strsex, weight, height);//parentobj.GetThin(ageh, agew, hewe);
        $("#txtmalnu" + num).attr("yyblpj", yyblpj).html(arrmenu.join("，"));
    }
};

/*
功能：读取管理卡
*/
function init(stuno) {
    var objwhere = {};
    if (Arg("sdate"))
        objwhere.managetime1 = [Arg("sdate")];
    if (Arg("edate"))
        objwhere.managetime2 = [Arg("edate") + " 23:59:59"];
    $.sm(function (re, err) {
        if (err) {
            jQuery.getparent().layer.msg(err, {icon: 5});
            jQuery.getparent().layer.closeAll('loading');
        } else {
            var arrhtml = [];
            for (var i = 0; i < re.length; i++) {
                if (i == 0 && re[i]) {
                    if (re[i][6]) {
                        $("#txtthinweight").val(re[i][6]);
                    }
                    if (re[i][6]) {
                        $("#txtthinheight").val(re[i][5]);
                    }
                    // agewe,agehe,hewe
                    // var strpj = parentobj.GetThin(re[i][8], re[i][7], re[i][9], parentobj.tongsetting.strnormal, age, thsetting.strsex, weight, height);// parentobj.GetThin(re[i][8], re[i][7], re[i][9]);
                    // var arrpj = parentobj.GetThinArr(re[i][8], re[i][7], re[i][9]);
                    // yyblpj,diagnosis
                    var yyblpj = re[i][16];
                    var diagnosis = re[i][17];
                    $("#txtthinmalnu").attr("yyblpj", diagnosis).text(yyblpj);
                }
                setHtml(re[i]);
            }
            setHtml();
        }
    }, ["yyblcard.init", stuno, $.msgwhere(objwhere)]);
};

/*
设置营养不良明细记录行
*/
function setHtml(arr) {
    var len = $("#divtbody")[0].rows.length;
    var fno = len ? $($("#divtbody")[0].rows[len - 1]).attr("fno") : 0;
    fno = fno ? Math.floor(fno) + 1 : 1;
    var strstyle = "", isdisabled = 0;
    if (arr && arr[15]) {//从体检进来的
        strstyle = "disabled='disabled'";
        isdisabled = 1;
    }
    var age = arr && arr[1] ? parentobj.GetAge(thsetting.strbirthday, arr[1]) : "", objstuinfo = {};
    objstuinfo[fno] = {};
    var arrawhl = parentobj.tongsetting.awhlevel["a_" + age + "_" + thsetting.strsex];
    var arrhwle = arr ? parentobj.tongsetting.hwle["a_" + thsetting.strsex + "_" + arr[5] + "_" + arr[4]] : "";
    objstuinfo[fno].age = age;
    objstuinfo[fno].sex = thsetting.strsex;
    objstuinfo[fno].hight = arr && arr[5];
    objstuinfo[fno].weight = arr && arr[6];
    objstuinfo[fno].hest = arr && arr[4];
    thsetting.objstuinfo[fno] = objstuinfo[fno];
    var arrstr = ['<tr fno=' + fno + ' id="4" ftime="' + (arr && arr[1] ? arr[1] : '') + '"><td style="border-right: #d8d8d8 1px solid;border-bottom: #d8d8d8 1px solid;line-height:80px;height:80px;">'];
    arrstr.push(arr && arr[0] ? '<input type="button" onclick="delrowevent(this,' + fno + ',' + arr[0] + ');" id="btdelete' + fno + '" value="删除"  class="btn" />' : '&nbsp;');
    arrstr.push('</td><td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;"><input ' + strstyle + ' type="text" id="txtdate' + fno + '" fid="' + (arr && arr[0] ? arr[0] : '') + '" style="width: 95px;" class="Wdate" value="' + (arr && arr[1] ? arr[1] : '') + '" onclick="initdate(' + fno + ',this);" onblur="changeDate(' + fno + ',this);"/>' + (arr && arr[15] ? "<label style='color:#ff8591;'>体</label>" : "") + '</td>');
    arrstr.push('<td style="border-right: solid 1px #d8d8d8; border-bottom: solid 1px #d8d8d8;"><label id="txtage' + fno + '" age="' + (arr && arr[1] ? parentobj.GetAge(thsetting.strbirthday, arr[1]) : "") + '">' + (arr && arr[1] ? parentobj.GetAge(thsetting.strbirthday, arr[1], "zh") : "") + '</label>&nbsp;</td>');//;(arr && arr[2] ? arr[2] : '')
    arrstr.push('<td style="border-right: solid 1px #d8d8d8; border-bottom: solid 1px #d8d8d8;padding:0 10px;display:none;"><input id="txtcondition' + fno + '" maxlength="30" type="text" style="width:95%;" value="' + (arr && arr[3] ? arr[3] : '') + '" class="tjzagl_input" />  <span class="FancyInput__bar___1P3wW" ></span></td>');
    arrstr.push('<td style="border-right: solid 1px #d8d8d8; border-bottom: solid 1px #d8d8d8;"><select id="selwlw' + fno + '" class="editable-select"><option value="2" ' + (arr && arr[4] == 2 ? 'selected="selected"' : '') + 'selected="selected">立位</option><option ' + (arr && arr[4] == 1 ? 'selected="selected"' : '') + ' value="1">卧位</option></select></td>');
    arrstr.push('<td style="border-right: solid 1px #d8d8d8; border-bottom: solid 1px #d8d8d8;padding:0 10px""><input ' + strstyle + ' id="txtweight' + fno + '" type="text"  style="width:55%;" value="' + (arr && arr[6] ? arr[6] : '') + '"  onchange="strhewe(' + fno + ',2);" class="tjzagl_input" /><label style="display:none;" id="lbczl' + fno + '">' + (arrhwle ? '(' + parentobj.formatFloatValue(parseFloat(arr[6]) * 100 / arrhwle[3] - 100, 0, parentobj.tongsetting.tjkeepnum) + '%)' : "") + '</label>  <span class="FancyInput__bar___1P3wW" ></span></td>');
    arrstr.push('<td style="border-right: solid 1px #d8d8d8; border-bottom: solid 1px #d8d8d8;padding:0 10px""><input ' + strstyle + ' id="txtheight' + fno + '" type="text"  style="width:95%;" value="' + (arr && arr[5] ? arr[5] : '') + '"  onchange="strhewe(' + fno + ',1);" class="tjzagl_input" />  <span class="FancyInput__bar___1P3wW" ></span></td>');
    arrstr.push('<td style="border-right: solid 1px #d8d8d8; border-bottom: solid 1px #d8d8d8;"><label style="display:none;" id="txtagew' + fno + '">' + (arr && arr[7] ? arr[7] : '') + '</label><label id="agewjudge'+fno+'">' + jQuery.getparent().GetHWJudge2((arr && arr[7] ? arr[7] : ''), "W/A") + '</label></td>');
    arrstr.push('<td style="border-right: solid 1px #d8d8d8; border-bottom: solid 1px #d8d8d8;"><label style="display:none;" id="txtageh' + fno + '">' + (arr && arr[8] ? arr[8] : '') + '</label><label id="agehjudge'+fno+'">' + jQuery.getparent().GetHWJudge2((arr && arr[8] ? arr[8] : ''), "H/A") + '</label></td>');
    arrstr.push('<td style="border-right: solid 1px #d8d8d8; border-bottom: solid 1px #d8d8d8;"><label style="display:none;" id="txthewe' + fno + '">' + (arr && arr[9] ? arr[9] : '') + '</label><label id="hewejudge'+fno+'">' + jQuery.getparent().GetHWJudge2((arr && arr[9] ? arr[9] : ''), "W/H") + '</label></td>');
    var arrpj = arr ? parentobj.GetThinArr(arr[8], arr[7], arr[9], objstuinfo[fno]) : []//用于显示
        , strpj = (arr && arr[10] ? (arr[10] + (arr[14] ? '(' + arr[14] + ')' : '')) : '');//兼容之前做的，此值用來保存
    arrstr.push('<td style="border-right: solid 1px #d8d8d8; border-bottom: solid 1px #d8d8d8;"><label id="txtmalnu' + fno + '" yyblpj="' + strpj + '">' + (arrpj.join('，') || (arr && arr[10] ? (arr[10] + (arr[14] ? '(' + arr[14] + ')' : '')) : '')) + '</label>&nbsp;<input type="hidden" id="txtfat' + fno + '" /></td>');
    arrstr.push('<td style="border-right: solid 1px #d8d8d8; border-bottom: solid 1px #d8d8d8; padding:0 1px""><textarea id="txtproblem' + fno + '" maxlength="500"  style="width:95%;" class="tjzagl_textarea">' + (arr && arr[11] ? arr[11] : '') + '</textarea>  <span class="FancyInput__bar___1P3wW" ></span></td>');
    arrstr.push('<td style="border-right: solid 1px #d8d8d8; border-bottom: solid 1px #d8d8d8; padding:0 10px""><textarea id="txtidea' + fno + '" type="text" maxlength="500" style="width:95%;" class="tjzagl_textarea">' + (arr && arr[12] ? arr[12] : '') + '</textarea>  <span class="FancyInput__bar___1P3wW" ></span></td>');
    arrstr.push('<td style="border-bottom: solid 1px #d8d8d8;padding-right:4px;padding-left:4px;"><input id="txtdoctor' + fno + '" type="text"  style="width:95%;" value="' + (arr && arr[13] ? arr[13] : '') + '" maxlength="20" class="tjzagl_input" />  <span class="FancyInput__bar___1P3wW" ></span></td></tr>');
    $("#divtbody").append(arrstr.join(""));
    initselect(fno, isdisabled);
    if (!arr)
        jQuery.getparent().layer.closeAll('loading');
};

/*
功能：删除功能
*/
function delrowevent(obj, num, id) {
    jQuery.getparent().layer.confirm('请确认是否真的进行删除操作？', function (r) {
        $.sm(function (re, err) {
            if (err) {
                jQuery.getparent().layer.msg(err, {icon: 5});
            } else {
                $(obj).closest('tr').remove();
            }
            jQuery.getparent().layer.close(r);
        }, ["yyblcard.delmanage", id]);
    });
};

function changeDate(num, ths) {
    var v = $(ths).val();
    parentobj.isDate(v);
    if (parentobj.isDate(v)) {
        var age = parentobj.GetAge(thsetting.strbirthday, v);
        if (age < 3)
            $("#selwlw" + num).val(1);
        $("#txtage" + num).html(parentobj.getZhAgeByAge(age)).attr("age", age);
        strhewe(num, 2);//体重
        strhewe(num, 1);//身高
    } else {
        jQuery.getparent().layer.msg('检查日期输入有误,请重新输入!');
    }
}

/*
功能：初始化日期
*/
function initdate(num, ths) {
    WdatePicker({
        skin: 'whyGreen', minDate: Arg("sdate"), maxDate: Arg("edate"), onpicked: function (dp) {
            var age = parentobj.GetAge(thsetting.strbirthday, dp.cal.getNewDateStr());
            if (age < 3)
                $("#selwlw" + num).val(1);
            $("#txtage" + num).html(parentobj.getZhAgeByAge(age)).attr("age", age);
            changeDate(num, ths);
            strhewe(num, 2);//体重
            strhewe(num, 1);//身高
        }
    });
};

/*
功能：
*/
function strhewe(num, type) {
    var heightid = isNaN(num) ? 'txt' + num + 'height' : 'txtheight' + num;
    var weightid = isNaN(num) ? 'txt' + num + 'weight' : 'txtweight' + num;
    var strfatid = 'txtfat' + num;
    var strageh = isNaN(num) ? 'txt' + num + 'ageh' : "txtageh" + num;
    var stragew = isNaN(num) ? 'txt' + num + 'agew' : "txtagew" + num;
    var strhewe = isNaN(num) ? 'txt' + num + 'hewe' : "txthewe" + num;
    var strmalid = isNaN(num) ? 'txt' + num + 'malnu' : 'txtmalnu' + num;
    var hest = isNaN(num) ? thsetting.hest : $("#selwlw" + num).val();
    var len = $("#divtbody")[0].rows.length - 1;
    var fno = $($("#divtbody")[0].rows[len]).attr("fno");
    if (type == 1) {
        var height = $("#" + heightid).val();
        if (isNaN(height) || height == "") {
            jQuery.getparent().layer.msg('身高请输入数字！');
            $("#" + heightid).val("");
            $("#" + heightid).focus();
        } else if (height.indexOf('.') > 3 || height > 200) {
            jQuery.getparent().layer.msg('您输入的身高值不符合常规,请重新输入!');
            $("#" + heightid).val("");
            $("#" + heightid).focus();
        } else {
            height = parentobj.getOneFloat(height);
            var weight = $("#" + weightid).val();
            $("#" + heightid).val(height);
            num = num == 'thin' ? 1 : num;
            var age = $("#txtage" + num).attr("age") != "" ? $("#txtage" + num).attr("age") : parentobj.GetAge(thsetting.strbirthday, $("#txtdate" + num).val());
            if (num == 'vest') {//转归
                if (!$("#txtenddate").val()) {
                    jQuery.getparent().layer.msg('请先选择结案日期!');
                    return;
                }
                age = parentobj.GetAge(thsetting.strbirthday, $("#txtenddate").val());
            }
            var arrawhl = parentobj.tongsetting.awhlevel["a_" + age + "_" + thsetting.strsex];
            var agewh = "";
            if(arrawhl){
                agewh = parentobj.GetHW(height, [arrawhl[0], arrawhl[1], arrawhl[2], arrawhl[4], arrawhl[5], arrawhl[6], arrawhl[3]]);
            }
            $("#" + strageh).html(agewh);
            if(!isNaN(num)){//判断文字显示
                $("#agehjudge" + num).html(jQuery.getparent().GetHWJudge2(agewh, "H/A"));
            }
            if (weight != "") {
                var arrhwle = parentobj.tongsetting.hwle["a_" + thsetting.strsex + "_" + height + "_" + hest];
                if (arrhwle) {
                    var strhw = parentobj.GetHW(weight, [arrhwle[0], arrhwle[1], arrhwle[2], arrhwle[4], arrhwle[5], arrhwle[6], arrhwle[3]]);
                    $("#" + strhewe).html(strhw);
                    if (!isNaN(num)) {
                        var strfat = parentobj.GetFat(arrhwle[3], weight);
                        $("#" + strfatid).val(strfat);
                    }
                    if(!isNaN(num)){//判断文字显示
                        $("#hewejudge" + num).html(jQuery.getparent().GetHWJudge2(strhw, "W/H"));
                    }
                    $("#lbczl" + num).text('(' + parentobj.formatFloatValue(parseFloat(weight) * 100 / arrhwle[3] - 100, 0, parentobj.tongsetting.tjkeepnum) + '%)');
                }
            }
            thsetting.objstuinfo[num] = {};
            thsetting.objstuinfo[num].age = age;
            thsetting.objstuinfo[num].sex = thsetting.strsex;
            thsetting.objstuinfo[num].hight = height;
            thsetting.objstuinfo[num].weight = weight;
            thsetting.objstuinfo[num].hest = hest || 2;
            var bmi = parentobj.getbmi(height, weight);
            var ageh = $("#" + strageh).html();
            var agew = $("#" + stragew).html();
            var hewe = $("#" + strhewe).html();
            var stryyblpj = parentobj.getThin(ageh, agew, hewe, bmi, parentobj.tongsetting.strnormal, age, thsetting.strsex, weight, height);//parentobj.GetThin(ageh, agew, hewe);
            var strmalnu = parentobj.GetThinArr(ageh, agew, hewe, thsetting.objstuinfo[num]) || [];
            var strmaltype = "";
            if (stryyblpj == "低体重" && arrawhl) {
                strmaltype = parentobj.GetThinType(weight, arrawhl[7], age);
            } else if (stryyblpj == "生长迟缓" && arrawhl) {
                strmaltype = parentobj.GetThinType(height, arrawhl[0], age);
            } else if (stryyblpj == "消瘦" && arrhwle) {
                strmaltype = parentobj.GetThinType(weight, arrhwle[0], age);
            }
            if (stryyblpj != "") {
                if (strmalid.indexOf('vest') > 0 || strmalid.indexOf('thin') > 0) {
                    $("#" + strmalid).attr("yyblpj", stryyblpj).html(strmalnu.join("，"));
                    if(strmalid.indexOf('vest') > 0){
                        $("#txtvestgrade").html(strmaltype || "");
                    }
                } else {
                    $("#" + strmalid).attr("yyblpj", stryyblpj + (strmaltype ? "(" + strmaltype + ")" : "")).html(strmalnu.join("，"));
                    if(strmalid.indexOf('vest') > 0){
                        $("#txtvestgrade").html(strmaltype || "");
                    }
                }
            }
            if (fno == num)
                setHtml();
        }
    } else {
        var weight = $("#" + weightid).val();
        if (isNaN(weight) || weight == "") {
            jQuery.getparent().layer.msg('体重请输入数字！');
            $("#" + weightid).val("");
            $("#" + weightid).focus();
        } else if (parseFloat(weight) > ((num == "f" || num == "m") ? 300 : 70) || parseFloat(weight) < 0) {
            jQuery.getparent().layer.msg('请输入0至' + ((num == "f" || num == "m") ? 300 : 70) + '之间的数字！');
            $("#" + weightid).val("");
            $("#" + weightid).focus();
        } else {
            weight = parentobj.getFloatV(weight);
            // var lastnum = weight.charAt(weight.length - 1);//获取末尾数
            // if (lastnum != "0" && lastnum != 5) {
            //     jQuery.getparent().layer.msg('输入的体重可能有误，小数点最后一位应为0或5！');
            // }
            var height = $("#" + heightid).val();
            $("#" + weightid).val(weight);
            var age = $("#txtage" + (num == 'thin' ? 1 : num)).attr("age");
            if (num == 'vest') {//转归
                if (!$("#txtenddate").val()) {
                    jQuery.getparent().layer.msg('请先选择结案日期!');
                    return;
                }
                age = parentobj.GetAge(thsetting.strbirthday, $("#txtenddate").val());
            }
            var arrawhl = parentobj.tongsetting.awhlevel["a_" + age + "_" + thsetting.strsex];
            var agew = "";
            if (arrawhl) {
                agew = parentobj.GetHW(weight, [arrawhl[7], arrawhl[8], arrawhl[9], arrawhl[11], arrawhl[12], arrawhl[13], arrawhl[10]]);
                $("#" + stragew).html(agew);
            } else {
                $("#" + stragew).html("");
            }
            if(!isNaN(num)){
                $("#agewjudge" + num).html(jQuery.getparent().GetHWJudge2(agew, "W/A"));
            }
            if (height != "") {
                var arrhwle = parentobj.tongsetting.hwle["a_" + thsetting.strsex + "_" + height + "_" + hest];
                if (arrhwle) {
                    var strhw = parentobj.GetHW(weight, [arrhwle[0], arrhwle[1], arrhwle[2], arrhwle[4], arrhwle[5], arrhwle[6], arrhwle[3]]);
                    $("#" + strhewe).html(strhw);
                    if (!isNaN(num)) {
                        var strfat = parentobj.GetFat(arrhwle[1], weight);
                        $("#" + strfatid).val(strfat);
                    }
                    if(!isNaN(num)){//判断文字显示
                        $("#hewejudge" + num).html(jQuery.getparent().GetHWJudge2(strhw, "W/H"));
                    }
                    $("#lbczl" + num).text('(' + parentobj.formatFloatValue(parseFloat(weight) * 100 / arrhwle[3] - 100, 0, parentobj.tongsetting.tjkeepnum) + '%)');
                }
            }
            thsetting.objstuinfo[num] = {};
            thsetting.objstuinfo[num].age = age;
            thsetting.objstuinfo[num].sex = thsetting.strsex;
            thsetting.objstuinfo[num].hight = height;
            thsetting.objstuinfo[num].weight = weight;
            thsetting.objstuinfo[num].hest = hest || 2;
            var bmi = parentobj.getbmi(height, weight);
            var ageh = $("#" + strageh).html();
            var agew = $("#" + stragew).html();
            var hewe = $("#" + strhewe).html();
            var stryyblpj = parentobj.getThin(ageh, agew, hewe, bmi, parentobj.tongsetting.strnormal, age, thsetting.strsex, weight, height);// parentobj.GetThin(ageh, agew, hewe);
            var strmalnu = parentobj.GetThinArr(ageh, agew, hewe, thsetting.objstuinfo[num]) || [];
            var strmaltype = "";
            if (stryyblpj == "低体重" && arrawhl) {
                strmaltype = parentobj.GetThinType(weight, arrawhl[7], age);
            } else if (stryyblpj == "生长迟缓" && arrawhl) {
                strmaltype = parentobj.GetThinType(height, arrawhl[0], age);
            } else if (stryyblpj == "消瘦" && arrhwle) {
                strmaltype = parentobj.GetThinType(weight, arrhwle[0], age);
            }
            if (stryyblpj != "") {
                if (strmalid.indexOf('vest') > 0 || strmalid.indexOf('thin') > 0) {
                    $("#" + strmalid).attr("yyblpj", stryyblpj).html(strmalnu.join("，"));
                } else {
                    $("#" + strmalid).attr("yyblpj", stryyblpj + (strmaltype ? "(" + strmaltype + ")" : "")).html(strmalnu.join("，"));
                }
            }
            if (fno == num)
                setHtml();
        }
    }
};

/*
功能：保存
*/
function btnsave(stno, cb) {
    if (!Validator.Validate($('#tabcard0').val(), 2)) {
        return;
    }
    var arrpm = [];
    var len = $("#divtbody")[0].rows.length;
    var fno = $($("#divtbody")[0].rows[len - 2]).attr("fno"),
        chkvest = $("#chkvest")[0].checked ? 1 : 0;
    var enddate = $("#txtenddate").val();
    if (chkvest == 1 && !enddate) {//结案日期不能为空
        jQuery.getparent().layer.msg('请先选择结案日期，结案日期不能为空！');
        return;
    }
    var curvaluation = "";//当前评价
    var curgrade = "";//当前评价程度
    var strtip = '', arrdate = [];
    for (var i = 0; i < len; i++) {
        // var num = (i + 1);
        var num = $($("#divtbody")[0].rows[i]).attr("fno");
        if (!$("#txtweight" + num)[0]) {//当前不行存在被删除了
            continue;
        }
        var txtweight = parseFloat($.trim($("#txtweight" + num).val()));
        var txtheight = parseFloat($.trim($("#txtheight" + num).val()));
        var date = $("#txtdate" + num).val(),
            fid = $("#txtdate" + num).attr('fid'),
            tage = $("#txtage" + num).attr("age");//年龄
        if (!date && !txtweight && !txtheight) {//日期身高体重都没有不处理
            continue;
        }
        if (date && (!txtweight || !txtheight)) {//有日期，没有身高，体重
            strtip += '第' + (i + 1) + '行信息不完整，数据将不会被处理</br>';
        } else if (txtweight && (!date || !txtheight)) {//有体重，没有日期或身高
            strtip += '第' + (i + 1) + '行信息不完整，数据将不会被处理</br>';
        } else if (txtheight && (!date || !txtweight)) {//有身高，没有日期或体重
            strtip += '第' + (i + 1) + '行信息不完整，数据将不会被处理</br>';
        // } else if (tage >= 7) {
        //     strtip += '第' + (i + 1) + '行年龄已满7岁，数据将不会被处理</br>';
        }
        if (!date || !txtweight || !txtheight) {
            continue;
        }
        if ($.inArray(date, arrdate) >= 0) {
            jQuery.getparent().layer.msg("第" + (i + 1) + "行日期" + date + "与其它行日期重复，请检查！", {area: '400px'});
            jQuery.getparent().layer.closeAll('loading');
            return;
        } else if (date) {
            arrdate.push(date);
        }
        var fno1 = $("#txtweight" + num).closest('tr').attr("ftime");
        if (date != "" && $("#txtmalnu" + num).attr("yyblpj") != "") {
            var smalnu = $("#txtmalnu" + num).attr("yyblpj");
            var stmalnu = ""
                , objwhere = {};
            var stmalnutype = "";
            if (smalnu.indexOf('(') >= 0) {
                stmalnu = smalnu.split('(')[0];
                stmalnutype = smalnu.split('(')[1].substr(0, 2);
            } else {
                stmalnu = smalnu;
            }
            if (fid) {
                objwhere.id = [fid];
            }
            var stryyblpj = $("#txtmalnu" + num).html();
            var objpj = getyyblpj(stryyblpj);
            var yyblpj = "";
            if(objpj.lowweight[0]){
                yyblpj += objpj.lowweight[0] + (objpj.lowweight[1] || "");
            }
            if(objpj.thin[0]){
                yyblpj += yyblpj ? "," + objpj.thin[0] + (objpj.thin[1] || "") : objpj.thin[0] + (objpj.thin[1] || "");
            }
            if(objpj.growthslow[0]){
                yyblpj += yyblpj ? "," + objpj.growthslow[0] + (objpj.growthslow[1] || "") : objpj.growthslow[0] + (objpj.growthslow[1] || "");
            }
            arrpm.push(["yyblcard.addmanage", thsetting.curstuno, date, $("#txtage" + num).attr("age"), $("#txtcondition" + num).val(), $("#selwlw" + num).val(), ($("#txtheight" + num).val() == "" ? 'null' : $("#txtheight" + num).val()), ($("#txtweight" + num).val() == "" ? 'null' : $("#txtweight" + num).val()), $("#txtageh" + num).html(), $("#txtagew" + num).html(), $("#txthewe" + num).html(), stmalnu, $("#txtproblem" + num).val(), $("#txtidea" + num).val(), $("#txtdoctor" + num).val(), stmalnutype,objpj.lowweight[0], objpj.lowweight[1], objpj.thin[0], objpj.thin[1], objpj.growthslow[0], objpj.growthslow[1], yyblpj,fno1 ? fno1 : date, $.msgwhere(objwhere)]);
            curvaluation = stmalnu;
            curgrade = stmalnutype;
        }
    }
    if (chkvest == 1) {//结案
        curvaluation = $("#txtvestmalnu").attr("yyblpj");
        curgrade = $("#txtvestgrade").html();
    }
    //获取营养不良评价
    var stryyblpj = $("#txtmalnu" + fno).html();
    var objpj = getyyblpj(stryyblpj);
    var yyblpj = "";
    if(objpj.lowweight[0]){
        yyblpj += objpj.lowweight[0] + (objpj.lowweight[1] || "");
    }
    if(objpj.thin[0]){
        yyblpj += yyblpj ? "," + objpj.thin[0] + (objpj.thin[1] || "") : objpj.thin[0] + (objpj.thin[1] || "");
    }
    if(objpj.growthslow[0]){
        yyblpj += yyblpj ? "," + objpj.growthslow[0] + (objpj.growthslow[1] || "") : objpj.growthslow[0] + (objpj.growthslow[1] || "");
    }
    arrpm.push(["yyblcard.addthin", $("#txtclass").html(), ($("#txtfheight").val() == "" ? 'null' : $("#txtfheight").val()), ($("#txtfweight").val() == "" ? 'null' : $("#txtfweight").val()), ($("#txtmheight").val() == "" ? 'null' : $("#txtmheight").val()), ($("#txtmweight").val() == "" ? 'null' : $("#txtmweight").val()), $("#txtotherfat").val(), $("#txtcondition").val(), $.msgwhere({managetime: [$("#txtdate").val()]}), ($("#txtthinheight").val() == "" ? 'null' : $("#txtthinheight").val()), ($("#txtthinweight").val() == "" ? 'null' : $("#txtthinweight").val()), $("#txtthinmalnu").attr("yyblpj"), $("#txtmalnu" + fno).attr("yyblpj").split('(')[0], $("#selmt").val(), $("#txtdirthday").html(), thsetting.strsex, curvaluation, curgrade, objpj.lowweight[0], objpj.lowweight[1], objpj.thin[0], objpj.thin[1], objpj.growthslow[0], objpj.growthslow[1], yyblpj, thsetting.curstuno]);
    //处理结案信息
    stryyblpj = $("#txtvestmalnu").html();
    var objpj = getyyblpj(stryyblpj);
    arrpm.push(["yyblcard.addthin1", chkvest, $.msgwhere({endtime: [enddate]}), $("#txtvestmalnu").attr("yyblpj"), ($("#txtvestheight").val() == "" ? 'null' : $("#txtvestheight").val()), ($("#txtvestweight").val() == "" ? 'null' : $("#txtvestweight").val()), $("#selvest").val() || 'null', $("#txtvestgrade").html(), objpj.lowweight[0], objpj.lowweight[1], objpj.thin[0], objpj.thin[1], objpj.growthslow[0], objpj.growthslow[1], thsetting.curstuno]);
    if (chkvest == 1) {//结案。记录结案记录stuno,stuname,stusex,claname,endtime,endheight,endweight,rank,vest,mt,casetype
        arrpm.push(["yyblcard.addclosecaserecord", thsetting.curstuno, thsetting.curstuname, thsetting.strsex, $("#txtclass").html(), enddate, ($("#txtvestheight").val() == "" ? 'null' : $("#txtvestheight").val()), ($("#txtvestweight").val() == "" ? 'null' : $("#txtvestweight").val()), $("#txtvestmalnu").attr("yyblpj"), $("#selvest").val() || 'null', $("#selmt").val()]);
    }
    if (strtip) {
        jQuery.getparent().layer.confirm(strtip + '请确定是否真的进行保存操作?', function (r) {
            btsavesm(arrpm, stno, r, cb);
        }, function (r) {
            jQuery.getparent().layer.closeAll('loading');
        });
    } else {
        btsavesm(arrpm, stno, null, cb);
    }
};

/**
 * 获取营养不良评价
 * @param stryyblpj
 * @returns {{}}
 */
function getyyblpj(stryyblpj){
    var objpj = {};
    var arrpj = stryyblpj.split("，");
    objpj.lowweight = ["", ""], objpj.thin = ["", ""], objpj.growthslow = ["", ""];
    for (var i = 0; i < arrpj.length; i++) {
        var item = arrpj[i];
        var arr = item.split("(");
        if(item.indexOf("低体重") >= 0){
            objpj.lowweight = [arr[0], arr[1] && arr[1].substr(0, 2) || ""];
        } else if(item.indexOf("生长迟缓") >= 0){
            objpj.growthslow = [arr[0], arr[1] && arr[1].substr(0, 2) || ""];
        } else if(item.indexOf("消瘦") >= 0){
            objpj.thin = [arr[0], arr[1] && arr[1].substr(0, 2) || ""];
        }
    }
    return objpj;
}

function btsavesm(arrpm, stno, r, cb) {
    $.sm(function (re1, err1) {
        if (err1) {
            jQuery.getparent().layer.msg(err1, {icon: 5});
            jQuery.getparent().layer.closeAll('loading');
        } else {
            if (stno) {
                initclassdata(stno);
                $('#txtvestheight').prop('disabled', false);
                $('#txtvestweight').prop('disabled', false);
                $('#txtenddate').prop('disabled', false);
                $('#selvest').replaceWith('<label id="selvest"></label>');
                var arrdata = [['痊愈', "0"], ['好转', "1"], ['转医院', "2"], ['未愈', "3"], ['离园', "4"]];
                $('#selvest').editableSelect({
                    bg_iframe: true,
                    isreadonly: 1,
                    arrdata: arrdata,
                    deftext: '痊愈',
                    defvalue: "0",
                    width: '50px',
                    case_sensitive: false, // If set to true, the user has to type in an exact
                    items_then_scroll: 10// If there are more than 10 items, display a scrollbar
                });
            }
            jQuery.getparent().layer.closeAll('loading');
            jQuery.getparent().layer.msg('保存成功！');
            cb && cb();
            if (r) {
                jQuery.getparent().layer.close(r);
            }
        }
    }, arrpm, null, null, null, null, 1);
}

/*
功能：身高数值,体重数值
*/
function heightweight(type) {
    var height = $("#txtvestheight").val();
    var tweight = $("#txtthinweight").val();
    var theight = $("#txtthinheight").val();
    var weight = $("#txtvestweight").val();
    var fheight = $("#txtfheight").val();
    var fweight = $("#txtfweight").val();
    var mheight = $("#txtmheight").val();
    var mweight = $("#txtmweight").val();
    switch (type) {
        case 1:
            if (isNaN(height)) {
                jQuery.getparent().layer.msg('请输入数字！', function (r) {
                    jQuery.getparent().layer.close(r);
                });
                $("#txtvestheight").val("");
                $("#txtvestheight").focus();
            } else if (Math.floor(height) >= 300) {
                jQuery.getparent().layer.msg('您输入的身高值不符合常理,请重新输入!', function (r) {
                    jQuery.getparent().layer.close(r);
                });
                $("#txtvestheight").val("");
                $("#txtvestheight").focus();
            }
        case 2:
            if (isNaN(weight)) {
                jQuery.getparent().layer.msg('请输入数字！', function (r) {
                    jQuery.getparent().layer.close(r);
                });
                $("#txtvestweight").val("");
                $("#txtvestweight").focus();
            } else if (Math.floor(weight) >= 300 || Math.floor(weight) < 0) {
                jQuery.getparent().layer.msg('您输入的体重值不符合常理,请重新输入!', function (r) {
                    jQuery.getparent().layer.close(r);
                });
                $("#txtvestweight").val("");
                $("#txtvestweight").focus();
            }
        case 3:
            if (isNaN(fheight)) {
                jQuery.getparent().layer.msg('请输入数字！', function (r) {
                    jQuery.getparent().layer.close(r);
                });
                $("#txtfheight").val("");
                $("#txtfheight").focus();
            } else if (Math.floor(fheight) >= 300) {
                jQuery.getparent().layer.msg('您输入的身高值不符合常理,请重新输入!', function (r) {
                    jQuery.getparent().layer.close(r);
                });
                $("#txtfheight").val("");
                $("#txtfheight").focus();
            }
        case 4:
            if (isNaN(fweight)) {
                jQuery.getparent().layer.msg('请输入数字！', function (r) {
                    jQuery.getparent().layer.close(r);
                });
                $("#txtfweight").val("");
                $("#txtfweight").focus();
            } else if (Math.floor(fweight) >= 300) {
                jQuery.getparent().layer.msg('您输入的体重值不符合常理,请重新输入!', function (r) {
                    jQuery.getparent().layer.close(r);
                });
                $("#txtfweight").val("");
                $("#txtfweight").focus();
            }
        case 5:
            if (isNaN(mheight)) {
                jQuery.getparent().layer.msg('请输入数字！');
                $("#txtmheight").val("");
                $("#txtmheight").focus();
            } else if (Math.floor(mheight) >= 300) {
                jQuery.getparent().layer.msg('您输入的身高值不符合常理,请重新输入!');
                $("#txtmheight").val("");
                $("#txtmheight").focus();
            }
        case 6:
            if (isNaN(mweight)) {
                jQuery.getparent().layer.msg('请输入数字！');
                $("#txtmweight").val("");
                $("#txtmweight").focus();
            } else if (Math.floor(mweight) >= 300) {
                jQuery.getparent().layer.msg('您输入的体重值不符合常理,请重新输入!');
                $("#txtmweight").val("");
                $("#txtmweight").focus();
            }
        case 7:
            if (isNaN(theight)) {
                jQuery.getparent().layer.msg('请输入数字！');
                $("#txtthinheight").val("");
                $("#txtthinheight").focus();
            } else if (Math.floor(theight) >= 300) {
                jQuery.getparent().layer.msg('您输入的身高值不符合常理,请重新输入!');
                $("#txtthinheight").val("");
                $("#txtthinheight").focus();
            }
        case 8:
            if (isNaN(tweight)) {
                jQuery.getparent().layer.msg('请输入数字！');
                $("#txtthinweight").val("");
                $("#txtthinweight").focus();
            } else if (Math.floor(tweight) >= 300) {
                jQuery.getparent().layer.msg('您输入的体重值不符合常理,请重新输入!');
                $("#txtthinweight").val("");
                $("#txtthinweight").focus();
            }
    }
}