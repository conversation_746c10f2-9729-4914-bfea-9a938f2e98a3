﻿<!--﻿-->
<!DOCTYPE html>
<html>

<head>
	<meta charset="utf-8" />
	<title>便民服务牌大数据</title>
	<link rel="stylesheet" href="../../css/reset.css" />
	<link rel="stylesheet" href="../../css/icon.css" />
	<link rel="stylesheet" href="../../css/style.css" />
	<link rel="stylesheet" href="../../layui-btkj/css/layui.css" />
	<link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css" />
	<link rel="stylesheet" href="../../css/physical.css" />
	<style type="text/css">
		html,
		body {
			background: #eaeff3;
		}

		.layui-form-label {
			/*padding: 8px 0px 5px 10px;*/
			width: auto;
		}

		#parentRelation {
			width: 100%;
			height: 90%;
		}

		.num-label {
			cursor: pointer;
		}

		#avgScanCount {
			cursor: default;
		}
	</style>
</head>

<body>
	<div class="marmain" style="min-width: 950px">
		<div class="content-medical">
			<div class="layui-form layui-comselect" style="position: relative">
				<div class="btn-nextfl">
					<span class="layui-form-label" style="text-align: left; padding-left: 0">年份：</span>
					<div class="layui-input-inline" style="float: left; width: 120px">
						<input id="startDate" type="text" autocomplete="off" placeholder="选择开始日期" class="layui-input">
					</div>
					<div class="layui-form-mid">-</div>
					<div class="layui-input-inline layui-form isshowenrollstatus" style="float: left; width: 120px"
						id="diverollstatus">
						<input id="endDate" type="text" autocomplete="off" placeholder="选择结束日期" class="layui-input">
					</div>
					<div class="layui-input-inline layui-form isshowenrollstatus" style="float: left; width: 120px">
						<button class="layui-btn form-search" style="margin-left: 5px; vertical-align: top;"
							id="btnSearch">查询</button>
					</div>
				</div>
			</div>
		</div>
		<div id="content" style="width: 100%">
			<div class="marmain-cen">
				<div class="cenmartop">
					<div class="conference-sta">
						<div class="total-div" style="margin-left: 0">
							<img src="../../images/physical/nkindergarten01.png" class="kindergartenimg" />
							<div class="total-panel">
								<div class="total-left">
									<img src="../../images/physical/kindergarten01img.png" class="kinder-ltimg" />
									<div class="total-numbg">
										<div class="num-label" id="kindergartenCount">0</div>
										<div class="num-peo">幼儿园数</div>
									</div>
								</div>
							</div>
						</div>
						<div class="total-div">
							<img src="../../images/physical/nkindergarten02.png" class="kindergartenimg" />
							<div class="total-panel">
								<div class="total-left">
									<img src="../../images/physical/zkindergarten05img.png" class="kinder-ltimg" />
									<div class="total-numbg">
										<div class="num-label" id="bianminCardCount">0</div>
										<div class="num-peo">便民牌数</div>
									</div>
								</div>
							</div>
						</div>
						<div class="total-div">
							<img src="../../images/physical/nkindergarten03.png" class="kindergartenimg" />
							<div class="total-panel">
								<div class="total-left">
									<img src="../../images/physical/ygkindergarten02img.png" class="kinder-ltimg" />
									<div class="total-numbg">
										<div class="num-label" id="parentScanCount">0</div>
										<div class="num-peo">家长扫码预约数</div>
									</div>
								</div>
							</div>
						</div>
						<div class="total-div">
							<img src="../../images/physical/nkindergarten04.png" class="kindergartenimg" />
							<div class="total-panel">
								<div class="total-left">
									<img src="../../images/physical/ygkindergarten03img.png" class="kinder-ltimg" />
									<div class="total-numbg">
										<div class="num-label" id="parentCancelCount">0</div>
										<div class="num-peo">家长取消预约数</div>
									</div>
								</div>
							</div>
						</div>
						<div class="total-div">
							<img src="../../images/physical/nkindergarten05.png" class="kindergartenimg" />
							<div class="total-panel">
								<div class="total-left">
									<img src="../../images/physical/ygkindergarten04img.png" class="kinder-ltimg" />
									<div class="total-numbg">
										<div class="num-label" id="avgScanCount">0</div>
										<div class="num-peo">家长人均扫码数</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="visiting-statis">
						<div class="visiting-div">
							<div class="safestat-cen">
								<div class="safestat-tit"><i class="left-tit"></i><span
										id="dailyScanTrendTitle">每日扫码预约趋势</span></div>
								<div class="colunwit" id="openTrend" style="overflow: auto;"></div>
							</div>
							<div class="safestat-cen">
								<div class="safestat-tit"><i class="left-tit"></i><span>分小时预约/取消趋势</span></div>
								<div class="colunwit" id="appoint"></div>
							</div>
						</div>
						<div class="safestat-cen height770">
							<div class="safestat-tit"><i class="left-tit"></i><span>便民服务牌分布</span></div>
							<div class="colunwit" id="bianminServiceCard" style="width: 100%"></div>
						</div>
						<div class="visiting-div">
							<div class="safestat-cen">
								<div class="safestat-tit"><i class="left-tit"></i><span>分时段预约量对比</span></div>
								<div class="colunwit" id="appointBar"></div>
							</div>
							<div class="safestat-cen">
								<div class="safestat-tit"><i class="left-tit"></i><span>幼儿园扫码量（top5）</span></div>
								<div class="colunwit" id="scan"></div>
							</div>
						</div>
					</div>
					<div class="visiting-statis">
						<div class="safestat-cen heighrow">
							<div class="safestat-tit"><i class="left-tit"></i><span>家长画像</span></div>
							<div class="colunwit">
								<div class="weui-flex hit50">
									<div class="columnlist" id="parentGender">
										<h3></h3>
										<div></div>
									</div>
									<div class="columnlist" id="parentDevice">
										<h3></h3>
										<div></div>
									</div>
								</div>
								<div class="weui-flex hit50">
									<div class="columnlist" id="parentAge">
										<h3>年龄分布</h3>
										<div>11</div>
									</div>
									<div class="columnlist" id="parentRegion">
										<h3>区域分布</h3>
										<div>11</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="visiting-statis">
						<div class="safestat-cen">
							<div class="safestat-tit"><i class="left-tit"></i><span>孩子与家长关系</span></div>
							<div id="parentRelation"></div>
						</div>
					</div>
					<div class="visiting-statis">
						<div class="safestat-cen">
							<div class="safestat-tit"><i class="left-tit"></i><span>幼儿画像</span></div>
							<div class="colunwit weui-flex">
								<div class="columnlist" id="childrenGgender">
									<h3></h3>
									<div></div>
								</div>
								<div class="columnlist" id="childrenAge">
									<h3></h3>
									<div></div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<script type="text/javascript" data-main="../../js/operation/bianminService.js"
		src="../../sys/require.min.js"></script>
</body>

</html>