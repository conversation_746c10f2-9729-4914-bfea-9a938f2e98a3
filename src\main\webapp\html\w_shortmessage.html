﻿<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8" />
	<title>发布通知公告</title>
	<link rel="stylesheet" href="../css/reset.css" />
	<link href='../css/style.css' rel='stylesheet' />
	<link href='../css/gray.css' rel='stylesheet' />
	<script type="text/javascript">
		document.write('<link rel="stylesheet" id="linktheme" href="../css/'+parent.objdata.curtheme+'.css" type="text/css" media="screen"/>');
	</script>
	<!--[if lt IE 9]>
		<script src='../sys/html5shiv.min.js'></script>
	<![endif]-->
</head>
<body>
	<!-- 发布通知公告 -->
	<ul class="cl1 noticeAdd pd10-20 font14">
		<li>
			<label><em>*</em>发布范围：</label><textarea style="width: 455px;height:80px;" readonly="readonly" id="txtrange"></textarea>
			<a id="aselect" class="btn-border">选择</a><a id="aclear" class="btn-border">清空</a>
		</li>
		<li>
			<label><em>*</em>手机号：</label>
			<input type="text" style="width: 600px;" id="txtmobiles"/>
			<span style="color:#ddd;">多个手机号以，分割</span>
		</li>
		<li class="clearfix">
			<label>内容：</label>
			<textarea id="txtcontent" style="width:600px;height:250px;"></textarea>
		</li>
		<li>
			<a id="btnpulish" class="btn-black" style="margin-left:100px;">发送</a>
		</li>
	</ul>
	<script src="../sys/jquery.js"></script>
	<script type="text/javascript" src="../layui-btkj/layui.js"></script>
	<script type="text/javascript" src="../sys/jquery.js"></script>
	<script type="text/javascript" src="../sys/arg.js"></script>
	<script type="text/javascript" src="../js/w_shortmessage.js"></script>
</body>
</html>
