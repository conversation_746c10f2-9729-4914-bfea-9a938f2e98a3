﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>通知阅读详情</title>
    <link rel="stylesheet" href="../css/reset.css">
    <link rel="stylesheet" href="../css/icon.css">
    <link rel="stylesheet" href="../css/style.css"/>
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../css/medical.css">
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <!--[if lt IE 9]>
    <script src="../sys/html5shiv.min.js"></script>
    <![endif]-->
    <style type="text/css">
        html, body {
            background: #EAEFF3;
            overflow: hidden;
        }

        .layui-form-label {
            line-height: 28px;
        }
    </style>
</head>
<body>
<div class="marmain" style="min-width: 950px;">
    <div class="content-medical">
        <div class="layui-form layui-comselect" style="position: relative; ">
            <div class="btn-nextfl">
                <span id="sporganoryeyname" class="layui-form-label" style="text-align: left;"></span>
                <div class="layui-input-inline layui-form " style="float:left;width: 105px;">
                    <input id="organoryeyname" name="organoryeyname" type="text" autocomplete="off" placeholder="机构名称" class="layui-input">
                </div>
                <span class="layui-form-label" style="width: 100px;">联系人电话：</span>
                <div class="layui-input-inline organhide" style="float: left;">
                    <input id="txtreceivemobile" name="txtreceivemobile" type="text" autocomplete="off" placeholder="联系人电话" class="layui-input">
                </div>
                <span class="layui-form-label isshowenrollstatus" style="width: 115px;;">报名状态：</span>
                <div class="layui-input-inline layui-form isshowenrollstatus" style="float:left;width: 85px;" id="diverollstatus">
                    <select id="isneroll" lay-filter="isneroll">
                        <option value="" selected="">全部</option>
                        <option value="0">未报名</option>
                        <option value="1">已报名</option>
                        <option value="2">不报名</option>
                    </select>
                </div>
                <span class="layui-form-label isnhide" style="width: 115px;display: none;">是否已读：</span>
                <div class="layui-input-inline layui-form isnhide" style="float:left;width: 85px;display:none;" id="didread">
                    <select id="isnread" lay-filter="isnread">
                        <option value="" selected="">全部</option>
                        <option value="1">已读</option>
                        <option value="0">未读</option>
                    </select>
                </div>
            </div>
            <div class="btn-nextlt">
                <button class="layui-btn blockcol  mgl-20" id="search" style="margin-left: 10px;">查询</button>
                <!--                <button class="layui-btn blockcol  mgl-20 organhide" id="refresh" style="display:none;">刷新</button>-->
                <!--                <button class="layui-btn blockcol  mgl-20 organhide" id="btnaddyey" style="display:none;margin-left: 10px;">添加托幼机构</button>-->
                <!--&lt;!&ndash;                <button class="layui-btn blockcol  mgl-20 organhide" id="btnadd" style="display:none;margin-left: 10px;">申请园所</button>&ndash;&gt;-->
                <!--                <button class="layui-btn blockcol  mgl-20 organhide" id="btnmodeldown" style="display:none;"><a style="color: #fff;" href="../templates/modefile/yeyinfo.xls">模板下载</a></button>-->
                <!--                <button class="layui-btn blockcol  mgl-20 organhide" id="btnimport" style="display:none;">导入托幼机构</button>-->
                <!--                <button class="layui-btn blockcol  mgl-20 organhide" id="btnapplymore" style="display:none;">批量申请开通园所</button>-->
            </div>
        </div>
    </div>
    <div id="content" style="width: 100%;">
        <div class="marmain-cen">
            <div class="content-medical">
                <div class="tbmargin">
                    <table id="tbmain" lay-filter="tbmain">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../js/noticereadlist.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>
