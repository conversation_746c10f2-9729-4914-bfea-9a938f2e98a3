/*
內容摘要:角色编辑与添加
测定分析
修改日期：2024.8.6
*/
var objdata = {
    objRole: []
};

require.config({
    paths: {
        jquery: '../../sys/jquery',
        system: '../../sys/system',
        layuicommon: "../../sys/layuicommon",
        layui: "../../layui-btkj/layui"

    },
    shim: {
        "system": {
            deps: ["jquery"]
        },
        "layui": {
            deps: ["jquery", "system"]
        },
        "layuicommon": {
            deps: ["jquery", "layui"]
        }
    },
    waitSeconds: 0
});
require(["jquery", "system", 'layui', "layuicommon"], function () {
    layui.use(['form'], function () {
        var form = layui.form;
        form.render();
        //表单验证
        form.verify({
            rolesort: function (value){
                if(value.trim() === ''){
                    return "排序不能为空";
                } else if(isNaN(value) || value.indexOf(".") > -1){
                    return "请输入数字";
                }
            },
            //角色名称
            rname: function (value) {
                var isRoleName = false;
                $.sm(function (re, err) {
                    if (err) {
                        layer.msg(err);
                    } else {
                        if (re.id) {
                            if (Arg("type") == "add") {
                                isRoleName = true;
                            }
                            if (Arg("type") == "edit") {
                                if (re.id && re.id != Arg("id")) {
                                    isRoleName = true;
                                }
                            }
                        }
                    }
                }, ["role.detail", $.msgwhere({rname: [value], id: [Arg("id")]})], null, null, {async: false});
                if (isRoleName) {
                    return '角色名称已存在，请重新输入';
                } else if (value.trim() === '') {
                    return '角色名称不能为空';
                }
            },

            //权限字符串不能重复
            rolekey: function (value) {
                var isRoleKey = false;
                if (value.trim() !== '') {
                    $.sm(function (re, err) {
                        if (err) {
                            layer.msg(err);
                        } else {
                            if (re.id) {
                                if (Arg("type") === "add") {
                                    isRoleKey = true;
                                }
                                if (Arg("type") === "edit") {
                                    if (re.id && re.id !== Arg("id")) {
                                        isRoleKey = true;
                                    }
                                }
                            }
                        }
                    }, ["role.detail", $.msgwhere({rolekey: [value]})], null, null, {async: false});
                }
                if (isRoleKey) {
                    return '权限字符串已存在，请重新输入';
                    // } else if (value.trim() === '') {
                    //     return '权限字符串不能为空';
                }
            }
        });

        if (Arg("id") !== null && Arg("id") !== undefined) {
            $.sm(function (re, err) {
                if (err) {
                    layer.msg(err);
                } else {
                    form.val('formOk', {
                        "roletype": re.roletype,
                        "rname": re.rname,
                        "rolekey": re.rolekey,
                        "rolesort": re.rolesort,
                        "datascope": re.datascope,
                        "status": re.status,
                        "remark": re.remark
                    });
                }
            }, ["role.detail", $.msgwhere({id: [Arg("id")]})]);
        } else {
            $("#roletype").val(Arg("roletype"));
        }
        form.render();
        $('#form').on('blur', '[lay-verify]', function () {
            layformblur($(this), form);
        });
        $("#saveOK").click(function (event, callback) {
            form.submit('formOk', function (data) {
                var field = data.field; // 获取表单字段值
                $.sm(function (re, err) {
                    if (err) {
                        layer.msg(err);
                    } else {
                        layer.msg('保存成功');
                        callback && callback();
                    }
                }, ["role.save", JSON.stringify(field), $.msgwhere({id: [Arg("id") || 0]})]);
                return false;
            });
            return false;
        });
    });
});
