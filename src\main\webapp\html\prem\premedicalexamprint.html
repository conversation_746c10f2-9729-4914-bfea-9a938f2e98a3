<!DOCTYPE html>
<html>
<head>
    <title>打印预览婚前检查</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <style type="text/css">
        /* 打印样式 3联打印第一版 高265长558mm 需要在打印首选项里设置打印尺寸 */
        @media print {
            .jc_content{
                transform: scale(1);
            }
        }

        body, html {
            width: calc(13060px / var(--dpirate));
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden!important; /* 防止滚动条出现，如果图片比视口小 */
        }
        .jc_content {
            /* 打印区域大小（打印机设置首选项） 570 259 mm*/
            width: 570mm;
            height: 259mm;
            margin: 0;
            color: rgba(1, 1, 1, 1);
            border-top: none;
            background-repeat: no-repeat;
            background-size: cover;
            font-weight: bolder;
        }
        .report1{
            width:190mm;
            position: relative;
        }
        .report1 .province{/* 省 */
            position:absolute;
            top: 104px;
            left: 90px;
            width: 200px;
        }
        .report1 .city{/* 市 */
            position:absolute;
            top: 104px;
            left: 148px;
            width: 200px;
        }
        .report1 .area{/* 区 */
            position:absolute;
            top: 104px;
            left: 204px;
            width: 200px;
        }
        .report1 .premcheck_id{/* 婚检编号 */
            position:absolute;
            top: 104px;
            left: 524px;
            width: 200px;
        }
        .report1 .hospital_fulladdress{
            position:absolute;
            top: 104px;
            left: 300px;
            width: 200px;
        }
        .report1 .name{
            position:absolute;
            top: 147px;
            left: 176px;
            width: 200px;
        }
        .report1 .sex{
            position:absolute;
            top: 147px;
            left: 485px;
            width: 200px;
        }
        .report1 .birthdayyear{
            position:absolute;
            top: 192px;
            left: 218px;
            width: 200px;
        }
        .report1 .birthdaymonth{
            position:absolute;
            top: 192px;
            left: 279px;
            width: 200px;
        }
        .report1 .birthdayday{
            position:absolute;
            top: 192px;
            left: 318px;
            width: 200px;
        }
        .report1 .ethnic{/* 民族 */
            position:absolute;
            top: 192px;
            left: 485px;
            width: 200px;
        }
        .report1 .idcard{/* 身份证号 */
            position:absolute;
            top: 243px;
            left: 178px;
            width: 200px;
            font-size: inherit;
        }
        .report1 .occupation{/* 职业 */
            position:absolute;
            top: 288px;
            left: 183px;
            width: 200px;
        }
        .report1 .current_address{/* 现住址 */
            position:absolute;
            top: 332px;
            left:183px;
            width: 500px;
        }
        .report1 .other_name{/* 他人姓名 */
            position:absolute;
            top: 377px;
            left: 183px;
            width: 200px;
        }
        .report1 .blrelationsno{/* 无血缘关系 */
            position:absolute;
            top: 422px;
            left: 400px;
            width: 200px;
        }
        .report1 .blrelationsyes{/* 有血缘关系 */
            position:absolute;
            top: 422px;
            left: 467px;
            width: 200px;
        }
        .report1 .premcheck_result{/*婚前检查结果 第1，2联显示前面的半句，3联显示后面的半句 ; 如果是自定义输入的异常，则三联内容显示一样 */
            position:absolute;
            top: 500px;
            left:  150px;
            width: 500px;
        }
        .report1 .check_opinionone{
            position:absolute;
            top: 631px;
            left: 158px;
            width: 200px;
        }
        .report1 .check_opiniontwo{
            position:absolute;
            top: 631px;
            left: 360px;
            width: 200px;
        }
        .report1 .check_opinionthree{
            position:absolute;
            top: 663px;
            left: 158px;
            width: 200px;
        }
        .report1 .check_opinionfour{
            position:absolute;
            top: 663px;
            left: 360px;
            width: 200px;
        }
        .report1 .check_opinionfive{
            position:absolute;
            top: 693px;
            left: 158px;
            width: 200px;
        }
        .report1 .doctor_name{
            position:absolute;
            top:759px;
            left: 174px;
            width: 200px;
        }
        .report1 .hospital_seal{
            position:absolute;
            top:759px;
            left: 421px;
            width: 200px;
        }
        .report1 .timeyear{
            position:absolute;
            top:778px;
            left: 495px;
            width: 200px;
        }
        .report1 .timemonth{
            position:absolute;
            top:778px;
            left: 563px;
            width: 200px;
        }
        .report1 .timeday{
            position:absolute;
            top:778px;
            left: 611px;
            width: 200px;
        }
    </style>
</head>
<body>
<div class="jc_content" id="jc_content" style="width: 570mm; height:259mm;background-image: url(../../images/prem/scanprint570_259_3page.jpg); background-repeat: no-repeat;    background-size: cover; background-size: 100% 100%;">
    <div class="report1" style="height: 100%;float: left; ">
        <span class="birthday"></span>
        <span class="occupation"></span>
        <span class="doctor_name"></span>
        <span class="hospital_seal"></span>
        <span class="premcheck_id"></span>
        <span  class="hospital_fulladdress"></span>
        <span   class="hospital_address"></span>
        <span   class="ethnic"></span>
        <span  class="premcheck_result"></span>
        <span  class="idcard"></span>
        <span   class="other_name"></span>
        <span   class="name"></span>
        <span  class="sex"></span>
        <span   class="time"></span>
        <span  class="current_address"></span>
        <span  class="blrelationsno"></span>
        <span   class="blrelationsyes"></span>
        <span  class="province"></span>
        <span   class="city"></span>
        <span   class="area"></span>
        <span  class="check_opinionone"></span>
        <span  class="check_opiniontwo"></span>
        <span    class="check_opinionthree"></span>
        <span   class="check_opinionfour"></span>
        <span   class="check_opinionfive"></span>
        <span   class="timeyear"></span>
        <span  class="timemonth"></span>
        <span  class="timeday"></span>
        <span   class="birthdayyear"></span>
        <span   class="birthdaymonth"></span>
        <span  class="birthdayday"></span>
    </div>
    <div class="report1" style="height: 100%; float: left;  ">
        <span class="birthday"></span>
        <span class="occupation"></span>
        <span class="doctor_name"></span>
        <span class="hospital_seal"></span>
        <span class="premcheck_id"></span>
        <span  class="hospital_fulladdress"></span>
        <span   class="hospital_address"></span>
        <span   class="ethnic"></span>
        <span  class="premcheck_result"></span>
        <span  class="idcard"></span>
        <span   class="other_name"></span>
        <span   class="name"></span>
        <span  class="sex"></span>
        <span   class="time"></span>
        <span  class="current_address"></span>
        <span  class="blrelationsno"></span>
        <span   class="blrelationsyes"></span>
        <span  class="province"></span>
        <span   class="city"></span>
        <span   class="area"></span>
        <span  class="check_opinionone"></span>
        <span  class="check_opiniontwo"></span>
        <span    class="check_opinionthree"></span>
        <span   class="check_opinionfour"></span>
        <span   class="check_opinionfive"></span>
        <span   class="timeyear"></span>
        <span  class="timemonth"></span>
        <span  class="timeday"></span>
        <span   class="birthdayyear"></span>
        <span   class="birthdaymonth"></span>
        <span  class="birthdayday"></span>
    </div>
    <div class="report1" style="height: 100%; float: left;  ">
        <span class="birthday"></span>
        <span class="occupation"></span>
        <span class="doctor_name"></span>
        <span class="hospital_seal"></span>
        <span class="premcheck_id"></span>
        <span  class="hospital_fulladdress"></span>
        <span   class="hospital_address"></span>
        <span   class="ethnic"></span>
        <span  class="premcheck_result"></span>
        <span  class="idcard"></span>
        <span   class="other_name"></span>
        <span   class="name"></span>
        <span  class="sex"></span>
        <span   class="time"></span>
        <span  class="current_address"></span>
        <span  class="blrelationsno"></span>
        <span   class="blrelationsyes"></span>
        <span  class="province"></span>
        <span   class="city"></span>
        <span   class="area"></span>
        <span  class="check_opinionone"></span>
        <span  class="check_opiniontwo"></span>
        <span    class="check_opinionthree"></span>
        <span   class="check_opinionfour"></span>
        <span   class="check_opinionfive"></span>
        <span   class="timeyear"></span>
        <span  class="timemonth"></span>
        <span  class="timeday"></span>
        <span   class="birthdayyear"></span>
        <span   class="birthdaymonth"></span>
        <span  class="birthdayday"></span>
    </div>

    <div class="layui-input-block" style="display: none">
        <a id="printBtn" class="layui-btn">打印</a>
    </div>
</div>
</body>
<script>
    //获取屏幕DPI
    function getDPI() {
        let dpi = window.devicePixelRatio || 96;
        if (window.screen.deviceXDPI) {
            dpi = window.screen.deviceXDPI;
        } else {
            // 计算近似的 DPI
            const diagonalPixels = Math.sqrt(window.screen.width ** 2 + window.screen.height ** 2);
            const diagonalInches = Math.sqrt(window.screen.width / 96 * 2.54 ** 2 + window.screen.height / 96 * 2.54 ** 2);
            dpi = diagonalPixels / diagonalInches;
        }
        console.log("DPI -> ",dpi);
        return dpi;
    }
    document.documentElement.style.setProperty('--dpi', getDPI());
</script>
<script type="text/javascript" src="../../plugin/htmltopdf/js/html2canvas.js"></script>
<script type="text/javascript" src="../../plugin/htmltopdf/js/jspdf.debug.js"></script>
<script type="text/javascript" data-main="../../js/prem/premedicalexamprint" src="../../sys/require.min.js"></script>
</html>