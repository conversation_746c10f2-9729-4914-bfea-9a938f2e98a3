<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/html">
<head>
    <meta charset="UTF-8">
    <title>婚前医学检查</title>
    <link rel="stylesheet" type="text/css" href="../../layui-btkj/css/layui.css"/>
    <link rel="stylesheet" type="text/css" href="../../css/icon.css"/>
    <link rel="stylesheet" type="text/css" href="../../css/commonstyle-layui-btkj.css"/>
    <!--    <link rel="stylesheet" href="../../css/style.css"/>-->

    <link rel="stylesheet" href="../../css/medical.css">
    <link rel="stylesheet" href="../../styles/tbstyles.css"/>
    <link rel="stylesheet" type="text/css" href="../../css/reset.css"/>
    <style type="text/css">

        html, body {
            background: #EAEFF3;
        }

        .layui-btn {
            height: 37px;
            line-height: 37px;
            background: white;
        }

        .layui-form-item {
            display: inline-block;
            vertical-align: top;
            margin: 10px 5px 0 0;
        }

        #btnsetid {
            margin-right: 5px;
            vertical-align: top;
            background-color: white !important;
            font-size: 15px;
            color: #4A90E2;
            border: none;
            width: 140px;
            height: 40px;
        }

        #btnadd {
            margin-right: 150px;
            vertical-align: top;
            background-color: #4A90E2;
            font-size: 15px;
            color: white;
            border: none;
            width: 140px;
            height: 40px;
        }

        #btnSearch {
            vertical-align: top;
            background-color: #4A90E2;
        }

        /*下拉框的选项背景颜色和字体*/
        .layui-form-select dl dd.layui-this {
            background-color: #70B1FC;
            color: white;
        }

        xm-select {
            width: 100%;
        }

        xm-select > .xm-tips {
            font-size: 15px;
        }

    </style>
</head>
<body>
<section>
    <div class="marmain topmar">
        <div class="content-medical">
            <div class="comtitletopbor comtitletoptop" style="padding-right: 0;">
                <div class="layui-form layui-comselect">
                    <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                        <div class="layui-input-inline" style="width:200px;">
                            <input type="text" id="IDkey" name="IDkey" lay-verify="required" lay-vertype="tips"
                                   autocomplete="off"
                                   placeholder="请输入姓名/编号/身份证号"
                                   class="layui-input" style="float:left;width: 200px;">
                        </div>
                    </div>
                    <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                        <label class="layui-form-label" style="padding: 8px 0px 5px 10px">性别：</label>
                        <div class="layui-input-inline" style="float: left;width:200px;">
                            <select id="Sexkey">
                                <option value="">请选择</option>
                                <option value="0">男</option>
                                <option value="1">女</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                        <label class="layui-form-label" style="padding: 8px 0px 5px 10px">民族：</label>
                        <div class="layui-input-inline" style="float: left;width:200px;">
                            <select id="Ethnickey" lay-search="">
                            </select>
                        </div>
                    </div>

                    <div>
                        <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                            <label class="layui-form-label" style="padding: 8px 0px 5px 10px">婚前医学检查结果：</label>
                            <div class="layui-input-inline" style="float: left;width:200px;">
                                <select id="Premcheckstu_key">
                                    <option value="">请选择</option>
                                    <option value="0">未见异常</option>
                                    <option value="1">异常</option>
                                </select>
                            </div>
                        </div>

                        <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                            <label class="layui-form-label"
                                   style="padding: 8px 0px 5px 10px;width: auto;">医学意见：</label>
                            <div class="layui-input-inline" style="float: left;">
                                <div id="Opinionkey" class="xm-select-demo" style="width: 200px"></div>
                            </div>
                        </div>

                        <div class="layui-form-item" style="height:39px;">
                            <label class="layui-form-label">检查日期：</label>
                            <div class="layui-input-inline" style="float: none;width: auto;margin-right: 0px;">
                                <input id="checkdate" type="text" style="width: 220px;" readonly placeholder="请选择" class="layui-input"/>
                                <img id="iconcheckdate" src="../../images/newicon/ico_calendar.png" style="cursor: pointer;position: relative;top: -29px;width: 20px;left: 190px;">
                            </div>
                        </div>

                        <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                            <button class="layui-btn form-search" id="btnSearch">查询</button>
                        </div>

                        <div class="layui-form-item" style="display: inline-block;vertical-align: top; float: right">
                            <button type="button" class="layui-btn form-search" id="btnsetid"
                                    lay-on="test-tips-prompt-0" style="width: auto;">
                                编号设置
                            </button>
                            <button class="layui-btn form-search" id="btnadd" style="margin-right: 10px;">添加检查结果
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div id="content" class="marmain-cen">
                <div class="content-medical">
                    <div class="tbmargin" id="divtable">
                        <table id="laytable" lay-filter="laytable"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<div style="display: none;">
    <iframe id="frmdetail" style="border:none;width:100%;"></iframe>
</div>
<script type="text/javascript" data-main="../../js/prem/premedicalexam" src="../../sys/require.min.js"></script>

</body>
</html>
