/*新增日期: 2025.06.20
作 者: 朱辽原
修改时间：
修改人：
內容摘要: 专家讲座数据详情
*/
require.config({
    paths: {
        system: '../../sys/system',
        jquery: '../../sys/jquery',
        layui: '../../layui-btkj/layui',
        dataSource: './enterData',
        common: './common',
        echarts: '../../plugin/echart-5.1.2/dist/echarts.min',
        commonUtils: './commonUtils'
    },
    shim: {
        system: {
            deps: ['jquery']
        },
        layui: {
            deps: ['jquery', 'system']
        },
        common: {
            deps: ['echarts']
        }
    },
    waitSeconds: 0
})

const openMock = false

const baseModule = ['jquery', 'commonUtils', 'layui', 'system', 'common']

if (openMock) {
    baseModule.push('dataSource')
}

require(baseModule, ($, utils) => {
    const openLog = true
    layui.config().extend({
        system: '../../sys/system',
    });
    layui.use(['system', 'form', 'table', 'laydate'], function () {
        layer = layui.layer;
        form = layui.form;
        laydate = layui.laydate;
        const params = Arg.all();
        initDatePicker();
        initSelectData();
        initEvent();
        initTable();

        function initDatePicker() {
            // 保留日期选择功能，但默认值通过参数或当前日期设置
            const isoDate = new Date().toISOString();
            const nowDate = isoDate.split('T')[0];
            laydate.render({
                elem: '#startDate',
                type: 'date',
                value: params.startDate || nowDate
            });
            laydate.render({
                elem: '#endDate',
                type: 'date',
                value: params.endDate || nowDate
            });
        }

        function initSelectData() {
            form.render('select');
        }

        function initEvent() {
            $("#btnsearch").click(function () {
                btnsearch();
            });
        }

        function initTable() {
            var objwhere = getswhere();
            var arrcol = [ // 表格列定义匹配图片内容
                { field: 'id', type: "numbers", title: '序号', align: 'center' },
                { field: 'lecturename', title: '专家讲座通知', align: 'center' },
                { field: 'publishtime', title: '发布时间', align: 'center' },
                {
                    field: 'successcount', title: '已读', align: 'center',
                    templet: (d) => {
                        utils.logWithCondition(openLog, new Error().stack,d)
                        let param = JSON.stringify({
                            count: d.successcount,
                            lectureId: d.noticeid,
                            title: d.lecturename,
                            startDate: params.startDate,
                            endDate: params.endDate,
                            isSuccess: 1
                        })
                        param = encodeURIComponent(param)
                        if (+d.successcount > 0) {
                            return `<span onclick="pageJumpToTable('expertLecturesInfo_person.html', '${param}', '专家讲座数据详细')" style="color:#02baf6;cursor: pointer">
                                    ${d.successcount}
                                </span>`
                        }
                        return 0
                    }
                },
                {
                    field: 'failcount', title: '未读', align: 'center',
                    templet: function (d) {
                        let param = JSON.stringify({
                            count: d.failcount,
                            lectureId: d.noticeid,
                            title: d.lecturename,
                            startDate: params.startDate,
                            endDate: params.endDate,
                            isSuccess: 0
                        })
                        param = encodeURIComponent(param)

                        if (+d.failcount > 0) {
                            return `<span onclick="pageJumpToTable('expertLecturesInfo_person.html', '${param}', '专家讲座数据详细')" style="color:#02baf6;cursor: pointer">
                                    ${d.failcount}
                                </span>`
                        }
                        return 0
                    }
                },
                {
                    field: 'reachrate', title: '触达率', align: 'center', templet: function (d) {
                        return d.reachrate * 100 + '%'
                    }
                },
                {
                    field: 'signupcount', title: '报名人数', align: 'center',
                    templet: function (d) {
                        let param = JSON.stringify({
                            count: d.signupcount,
                            lectureId: d.noticeid,
                            title: d.lecturename,
                            startDate: params.startDate,
                            endDate: params.endDate,
                            signupcount: 1
                        })
                        param = encodeURIComponent(param)
                        if (d.signupcount > 0) {
                            return `<span onclick="pageJumpToTable('expertLecturesInfo_person.html', '${param}', '专家讲座数据详细')" style="color:#02baf6;cursor: pointer">
                                    ${d.signupcount}
                                </span>`
                        }
                        return 0
                    }
                },
                {
                    field: 'checkincount', title: '签到人数', align: 'center',
                    templet: function (d) {
                        let param = JSON.stringify({
                            count: d.checkincount,
                            lectureId: d.noticeid,
                            title: d.lecturename,
                            startDate: params.startDate,
                            endDate: params.endDate,
                            isSign: 1
                        })
                        param = encodeURIComponent(param)
                        if (d.checkincount > 0) {
                            return `<span onclick="pageJumpToTable('expertLecturesInfo_person.html', '${param}', '专家讲座数据详细')" style="color:#02baf6;cursor: pointer">
                                    ${d.checkincount}
                                </span>`
                        }
                        return 0
                    }
                }
            ];
            layui.table.render({
                elem: '#laytable',
                url: encodeURI($.getLayUrl() + "?" + $.getSmStr(["expertLecture.list"])),
                height: 'full-50',
                where: {
                    swhere: $.msgwhere(objwhere),
                    fields: 'n.id',
                    types: 'asc'
                },
                cols: [arrcol],
                done: function (res, curr, count) {
                    // 表格加载完成回调
                },
                countNumberBool: true,
                even: true,
                page: true,
                limits: [30, 50, 100, 200],
                limit: 30,
                skin: 'row'
            });
            layui.table.on('tool(laytable)', function (obj) {
                // 工具条事件处理
            });
        }

        function btnsearch() {
            var objwhere = getswhere();
            layui.table.reload('laytable', {
                page: { curr: 1 },
                where: {
                    swhere: $.msgwhere(objwhere),
                    fields: 'n.id',
                    types: 'asc'
                },
            });
        }

        function getswhere() {
            var objwhere = {};
            var startDate = $("#startDate").val() || params.startDate;
            var endDate = $("#endDate").val() || params.endDate;
            var title = $("#lectureTitle").val() || ''

            if (startDate) {
                objwhere.startDate = [startDate];
            }
            if (endDate) {
                objwhere.endDate = [endDate];
            }
            if (title) {
                objwhere.title = [title]
            }

            utils.logWithCondition(openLog, new Error().stack, objwhere)
            return { ...params, ...objwhere };
        }

        /*
        功能：页面跳转
        参数说明：param - URL 中的参数
        返回值说明：无
        */
        window.pageJumpToTable = utils.pageJumpToTable
    });
})

