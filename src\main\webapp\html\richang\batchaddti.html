<!DOCTYPE html>
<html>
<head>
    <title>批量添加选项</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <link rel="stylesheet" href="../../css/reset.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/xixian.css">
    <script type="text/javascript" src="../../layui-btkj/layui.js"></script>
</head>
<body>
<section>
    <div style="margin: 20px;">
        <div class="batch-add">
            <div class="batch-add-left">
                <textarea id="textcontent" name="name" lay-verify="required" autocomplete="off" placeholder="每行代表一个选项，可以添加多个选项" class="layui-input" type="text" style="height:200px;"></textarea>
            </div>
            <div class="batch-add-right">
                <h3>预定义选项</h3>
                <ul id="divchange">
                    <li>满意度</li>
                    <li>认可度</li>
                    <li>评价</li>
                    <li>频率</li>
                    <li>数字</li>
                    <li>学历</li>
                    <li>星期</li>
                    <li>月份</li>
                </ul>
            </div>
        </div>
    </div>
</section>
<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/function.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../js/richang/batchaddti.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>