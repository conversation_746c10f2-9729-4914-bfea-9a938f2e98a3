<!DOCTYPE html>
<html>
<head>
    <title>食谱管理</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <link rel="stylesheet" href="../../css/reset.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <style>
        html, body {
            height: 100%;
            background: #EAEFF3;
            overflow-y: hidden;
        }

        .layui-form-label {
            line-height: 28px;
        }

        thead tr th {
            border-right: none
        }
    </style>
</head>
<body>
<section>
    <div class="marmain">
        <div class="content-medical">
            <div class="layui-form layui-comselect">

                <div class="layui-form" style="display: inline-block;">
                    <label class="layui-form-label" style="width: 60px;">关键字：</label>
                    <div class="def-search" style="display: inline-block;vertical-align: top;width: 200px;margin: 0px 10px 0 0; height: 28px;">
                        <input id="txtkey" type="text" placeholder="请输入幼儿园名称" class="layui-input">
                    </div>
                    <div class="layui-form-item" style="display: inline-block;">
                        <label class="layui-form-label" style="padding: 8px 0px 0px 0px;">创建时间：</label>
                        <div class="layui-input-inline" style="min-height: 30px;margin-right:5px; position: relative;float: left; width: 150px;">
                            <input autocomplete="off" class="layui-input form-ordernum" type="text" id="txtstartdate" placeholder="开始时间" style="width:150px; display: inline-block;">
                            <i class="layui-alendar"><img id="iconstartdate" src="../../images/recipes/cal_icon.png" style="width: 14px;"></i>
                        </div>
                        <span style="display: inline-block;vertical-align: top;margin-top:8px;color: #e6e6e6;float: left;margin-right:5px;">—</span>
                        <div class="layui-input-inline" style="min-height: 30px;margin-left: 0px; position: relative;width: 150px; float: left;">
                            <input autocomplete="off" class="layui-input form-ordernum" type="text" id="txtenddate" placeholder="结束时间" style="width:150px; display: inline-block;">
                            <i class="layui-alendar"><img id="iconenddate" src="../../images/recipes/cal_icon.png" style="width: 14px;"></i>
                        </div>
                        <button id="btnsearch" class="layui-btn form-search" style="vertical-align: top;">查询</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="marmain-cen">
            <div class="content-medical">
                <div class="tbmargin">
                    <table id="laytable" lay-filter="laytable" class="layui-table"></table>
                </div>
            </div>
        </div>
    </div>
</section>
<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../js/system/operationlog.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../js/recipe/sharerecipelist.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>