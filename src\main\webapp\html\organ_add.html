﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>用户管理</title>
    <link rel="stylesheet" href="../css/reset.css"/>
    <link rel="stylesheet" href="../plugin/jQueryValidationEngine/css/validationEngine.jquery.css" type="text/css"/>
    <link rel="stylesheet" href="../css/style.css"/>
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <!--[if lt IE 9]>
    <script src="../sys/html5shiv.min.js"></script>
    <![endif]-->
    <style>
        .u-edit {
            display: none;
        }
        .divrole {
            display: none;
        }
        .layui-form-label {
            width: 160px;
			/*text-align: left;*/
        }
        .location{
            cursor: pointer;
            padding: 4px 4px 4px 20px;
            margin-left: 4px;
            background: url(../images/locate.png) 0 2px no-repeat;
            background-size: 20px;
        }
        #bmapicon{
            display: inline-block;
            background: url(../images/bmapicon.png) center no-repeat;
            background-size: 20px;
            width: 30px;
            height: 20px;
            float: right;
        }
        .theme-fgc{
            color: #3aa6ff !important;
        }
        .layui-input-inline{
            width: 260px;
        }
		.layui-form-label{padding: 9px 5px;}
    </style>
</head>
<body>
<section class="personalInfo font14 cl1 ">
    <form id="form" class="layui-form" style="height: 100%;width: 100%;position: absolute">
        <section id="contant" style="overflow-y: auto;overflow-x: hidden" class="clearfix pd20">
<!--            <section id="divicon" class="fl personalImg" style="margin-top:100px">-->
<!--                <img src="../images/default.png" onerror="this.src='../images/default.png'" alt="" class="otx-head"/>-->
<!--                <p>尺寸：96*96</p>-->
<!--                <p>支持jpg、png、gif、bmp</p>-->
<!--                <a class="cl2 lanflag" id="btndelimg">删除</a>-->
<!--                <span id="btnup">-->
<!--                        <a class="lanflag" id="lanlocalup">本地上传</a>-->
<!--                    </span>-->
<!--            </section>-->

            <section class=" fl">
                <div class="layui-form-item layui-form">
                    <label class="layui-form-label"><em>*</em>所属地区：</label>
                    <div class="layui-input-inline">
                        <label class="layui-form-label" style="text-align: left;" id="lbarea"></label>
                    </div>
<!--                    <div class="layui-input-inline">-->
<!--                        <select id="selprovince" name="selprovince" lay-filter="selprovince">-->
<!--                        </select>-->
<!--                    </div>-->
<!--                    <div class="layui-input-inline">-->
<!--                        <select id="selcity" name="selcity" lay-filter="selcity">-->
<!--                        </select>-->
<!--                    </div>-->
<!--                    <div class="layui-input-inline">-->
<!--                        <select id="selarea" name="selarea" lay-filter="selarea">-->
<!--                        </select>-->
<!--                    </div>-->
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em id="emjgcode">*</em>机构编码：</label>
                    <div class="layui-input-inline" style="width: 350px;">
                        <input id="txtjgcode" type="text" name="jgcode" disabled="disabled" readonly="readonly" required lay-verify="required|digitOrLetter" placeholder="请输入机构编码" maxlength="22" autocomplete="off" class="layui-input">
                    </div>
                    <a id="btnselfuyouorgan" style="margin-left:5px;vertical-align: top;display: none;" class="layui-btn">选择</a>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>医院名称：</label>
                    <div class="layui-input-inline" style="width: 350px;">
                        <input id="txtjgname" type="text" name="jgname" required lay-verify="required" placeholder="请输入医院名称" maxlength="30" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>详细地址：</label>
                    <div class="layui-input-inline" style="width: 350px;">
                        <input id="txtjgaddress" type="text" name="jgaddress" autocomplete="off" required lay-verify="required" placeholder="请点击右侧定位详细地址" class="layui-input" maxlength="100">
                    </div>
                   <div style="margin-top: 8px; display: inline-block;"><span class="location" id="mapmsg"><a class="theme-fgc">点击图标定位</a></span><a id="bmapicon" title="点击查看百度地址"></a></div> 
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>负责人姓名：</label>
                    <div class="layui-input-inline" style="width: 350px;">
<!--                    	<div style="display:none;">-->
<!--                    		<input type="text" name="truename"/>-->
<!--                    	</div>-->
                        <input id='txtfzrname' type="text" name="fzrname" required lay-verify="required" placeholder="请输入负责人姓名" autocomplete="off" class="layui-input" maxlength="30">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>负责人手机：</label>
                    <div class="layui-input-inline" style="width: 350px;">
                        <input id="txtmobile" type="text" name="mobile" maxlength="13" required lay-verify="required|phone" placeholder="请输入负责人手机" autocomplete="off" class="layui-input" maxlength="20">
                    </div>
                </div>
                <div class="layui-form-item pr layui-u-role" style="display: none;">
                    <label class="layui-form-label"><em>*</em>角色：</label>
                    <div class="layui-input-inline">
                        <select id="roleSel" lay-filter="roles" lay-verify="required">
                        </select>
                    </div>
<!--                    <input type="hidden" id="rolesid"/>-->
<!--                    <div id="divrole" class="divrole pa bg4 font14">-->
<!--                        <ul id="ulselect" class="ulselect"></ul>-->
<!--                    </div>-->
                </div>
                <div class="layui-form-item" id="divisnmdical">
                    <label class="layui-form-label"><em>*</em>支持预约年度体检：</label>
                    <input type="radio" lay-filter="radisnmdical" name="radisnmdical" value="1" title="是">
                    <input type="radio" lay-filter="radisnmdical" name="radisnmdical" value="0" title="否" checked>
                    <lable id="lbtip" style="color: red;display: none; vertical-align: sub;">当前有关联的体检医院，请先解除与托幼机构的关联在修改</lable>
                </div>
                <div class="layui-form-item" id="divisninmdical">
                    <label class="layui-form-label"><em>*</em>支持预约入园体检：</label>
                    <input type="radio" lay-filter="radisninmdical" name="radisninmdical" value="1" title="是">
                    <input type="radio" lay-filter="radisninmdical" name="radisninmdical" value="0" title="否" checked>
                </div>

                <div id="divqualificationdate" class="layui-form-item" style="display: none;">
                    <label class="layui-form-label"><em>*</em>获得入托体检资质时间：</label>
                    <div class="layui-input-inline" style="width: 350px;">
                        <input id="txtqualificationdate" type="text" style="width: 150px;" lay-verify="required|date" placeholder="" class="layui-input"/>
                        <img id="iconqualificationdate" src="../images/newicon/ico_calendar.png" style="cursor: pointer;position: relative;top: -29px;width: 20px;left: 128px;">
                    </div>
                </div>
<!--                <input type="hidden" id="txtareacode"/>-->
<!--                <div class="layui-form-item" id="duanxin">-->
<!--                    <input type="checkbox" name="sendmessage" value="1" lay-skin="primary" title="发送短信通知成功创建账户">-->
<!--                </div>-->
            </section>
        </section>
        <div class="btns-foot" style="display:none;">
<!--            <input type="hidden" id="txtroletype"/>-->
            <a id="btnsave" class="layui-btn" lay-submit="" lay-filter="btnsave">保存</a>
<!--            <a style="display:none;" id="btnresetpwd" class="layui-btn">密码重置</a>-->
<!--            <a style="display:none;" id="btndelete" class="layui-btn">删除</a>-->
            <!--                 <a style="display:none;" id="btncheck" class=" layui-btn">检测</a> -->
            <a id="btncancel" class="layui-btn">取消</a>
        </div>
    </form>
</section>

<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../plugin/ueditor/third-party/webuploader/webuploader.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../plugin/validator/js/pwdvalidator.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../plugin/js/selarea.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../js/organ_add.js?v=" + v + "'><" + "/script>");
</script>

</body>
</html>
