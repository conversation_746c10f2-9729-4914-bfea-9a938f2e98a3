<!DOCTYPE html>
<html>
<head>
<title>幼儿入园体检</title>
<meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
<link rel="stylesheet" href="../css/reset.css">
<link rel="stylesheet" href="../css/icon.css">
<link rel="stylesheet" href="../layui-btkj/css/layui.css">
<link rel="stylesheet" href="../css/commonstyle-layui-btkj.css">
<link rel="stylesheet" href="../css/medical.css">
<script type="text/javascript" src="../layui-btkj/layui.js"></script>
<style>
html, body {
	background: #EAEFF3;
}
</style>
</head>
<body>
<section>
	<div>
		<div class="content-medical"  style="margin: 0px;border-radius:0px;">
			<div class="layui-form layui-comselect" style="padding: 10px 20px; position: relative; ">
				<div class="layui-form-item" style="display: inline-block;vertical-align: top;">
					<label class="layui-form-label" style="padding: 8px 0px 5px 0px; text-align: left;">幼儿姓名：</label>
					<div class="def-search" style="display: inline-block;vertical-align: top;width: 100px;margin: 0px 10px 0 0; height: 28px;">
						<label>
							<input type="text" placeholder="请输入" class="layui-input" >
						</label>
					</div>
				</div>
				<div class="layui-form-item" style="display: inline-block;vertical-align: top; margin-left: 10px;">
					<label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 0px;line-height: 28px;">性别：</label>
					<div class="layui-input-block" style="min-height: 30px;margin-left:44px;width:100px;">
						<select name="city" lay-verify="required">
							<option value="全部">全部</option>
						</select>
					</div>
				</div>
				<div class="layui-form-item" style="display: inline-block;vertical-align: top; margin-left:20px;height: 40px;">
					<label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 0px;line-height: 28px;">体检码字段查询：</label>
					<div class="layui-input-inline" style="float: none;width: auto;margin-right: 0px;">
						<input id="txtstart" type="text" autocomplete="off" placeholder="开始时间" class="layui-input" style="width: 130px;">
						<img id="iconstartdate" src="../images/medical/pulldown_ico.png" style="cursor: pointer;position: relative;top: -29px;width: 14px;left: 110px;"> </div>
					<span class="linebg">-</span>
					<div class="layui-input-inline"  style="float: none;width: auto;">
						<input id="txtstart" type="text" autocomplete="off" placeholder="结束日期" class="layui-input" style="width: 130px;">
						<img id="iconstartdate" src="../images/medical/pulldown_ico.png" style="cursor: pointer;position: relative;top: -29px;width: 14px;;left: 110px;"> </div>
				</div>
				<button id="btnsearch" class="layui-btn form-search" style="vertical-align: top;">查询</button>
				<button id="btnsearch" class="layui-btn form-search" style="vertical-align: top;background:#00AC9F;">导出Excel</button>
				<button id="btnsearch" class="layui-btn form-search" style="vertical-align: top;background:#F29700;">体检幼儿信息校对</button>
				<button id="btnsearch" class="layui-btn form-search" style="vertical-align: top;background:#00AC9F;">各项统计报表</button>
				<button id="btnsearch" class="layui-btn form-search" style="vertical-align: top;">发布报告</button>
			</div>
		</div>
	</div>
    <div style="position: absolute;top:60px;bottom: 0;width: 100%;" class="layui-row" >
	  <div class="layui-col-xs5 layui-col-sm5 layui-col-md5" style="height: 100%;">
                <div class="child-medicallt">
					<div style="padding: 0px 0px;" class="layui-tab-brief">
					<ul class="layui-tab-title">
						<li class="layui-this">已体检</li>
						<li>未体检</li>
					</ul>
				</div>
	
				<table class="layui-table checkbox-blue" lay-skin="row" lay-even="" cellspacing="0" cellpadding="0" border="0">
					<thead>
						<tr>
							<th><div>
									<input type="checkbox" checked>
								</div></th>
							<th style="text-align: center;"><div>序号</div></th>
							<th><div>幼儿姓名</div></th>
							<th  style="text-align: center;"><div>性别</div></th>
							<th style="text-align: center;" ><div>年龄</div></th>
							<th style="text-align: center;"><div>出生日期</div></th>
								<th style="text-align: center;"><div>体检日期</div></th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td><div>
									<input type="checkbox">
								</div></td>
							<td style="text-align: center;"><div>1</div></td>
									<td><div>
									2021体检
								</div></td>
							<td><div>小静</div></td>
							<td style="text-align: center;"><div>男</div></td>
							<td style="text-align: center;"><div>40</div></td>
							<td style="text-align: center;"><div>2020-09-09</div></td>
						  </tr>
							<tr>
							<td><div>
									<input type="checkbox">
								</div></td>
							<td style="text-align: center;"><div>1</div></td>
									<td><div>
									2021体检
								</div></td>
							<td><div>小静</div></td>
							<td style="text-align: center;"><div>男</div></td>
							<td style="text-align: center;"><div>40</div></td>
							<td style="text-align: center;"><div>2020-09-09</div></td>
						  </tr>
					     <tr>
							<td><div>
									<input type="checkbox">
								</div></td>
							<td style="text-align: center;"><div>1</div></td>
									<td><div>
									2021体检
								</div></td>
							<td><div>小静</div></td>
							<td style="text-align: center;"><div>男</div></td>
							<td style="text-align: center;"><div>40</div></td>
							<td style="text-align: center;"><div>2020-09-09</div></td>
						  </tr>
					</tbody>
				</table>
		
					
				</div>
		  </div>
	   <div class="layui-col-xs7 layui-col-sm7 layui-col-md7" style="height: 100%;">
                <div class="child-medicalrt">
		         <h3  class="comtitletop"><b>入园体检记录</b>
				<div style="float: right">
					<button id="" class="layui-btn addbtn" style="background: #FFFFFF; border: 1px solid #DEDEDE;color: #34495E;">取消</button>
					<button id="" class="layui-btn addbtn">打印预览</button>
					<button id="" class="layui-btn addbtn" style="background:#00AC9F;">导出Excel</button>
					<button id="" class="layui-btn addbtn">发布报告</button>
				</div>
			</h3>
	  </div>
	</div>
	
	</div>
</section>
<script>
		//Demo
		layui.use('form', function(){
		  var form = layui.form;
		  
		  //监听提交
		  form.on('submit(formDemo)', function(data){
		    layer.msg(JSON.stringify(data.field));
		    return false;
		  });
		});
		</script>
</body>
</html>