/**
 * 选择专家
 */
layui.config({
    base: './js/'
}).extend({ //设定模块别名
    system: '../../sys/system'
});
var parentobj = null;
var form;
var objdata = {
    expertinfo:{}//专家信息
    , flow: null
    , experttype:{
        "1": "讲座型中医儿科专家",
        "2": "治疗型中医儿科专家"
    }
    , expertinfotemp: '<div class="layui-col-xs6 layui-col-sm6 layui-col-md6">\
                    <div class="health-list hedoc divselexpert" expertid="{{expertid}}">\
                        <div class="health-listdiv pad10">\
                            <div style="position: relative;display: inline-block;vertical-align: top;"><img src="{{img}}" onerror="this.src=\'../images/healthimg/healthimg.png\'" class="doc-defimg"></div>\
                            <div style="display: inline-block;">\
                                <h3><b>{{name}}</b> <span>{{professional}}&nbsp;&nbsp;&nbsp; {{expertypename}}</span></h3>\
                                <h5><span>{{grade}}</span>{{workunit}}</h5>\
                                <div class="detail-doc">\
                                    <p>{{goodat}}</p>\
                                </div>\
                            </div>\
                        </div>\
                        <i class="selico"></i> </div>\
                </div>'
    , page: {
        curpage: 1,
        limit: 10
    }
},table, laypage;

layui.use(['system', 'form', 'table', 'laypage', 'flow'], function () {
    objdata.flow = layui.flow;
    laypage = layui.laypage;
    form = layui.form;
    table = layui.table;
    // initlist(0, false, true);
    $(window).resize(function () {
        $(".expertlist").height($("body").height() - 168);
    }).trigger('resize');
    initlist();
    initEvent();
    if(Arg("type") == 3){//专家讲座通知
        $("#ultop-tab li").eq(0).show();
    } else if(Arg("type") == 4){//专家坐诊通知
        $("#ultop-tab li").eq(1).show().trigger("click");
    }
    form.render();
    //处理职务
});

function initEvent() {
    $("#btnsearch").click(function () {//查询
        initlist();
    });
    $("#ultop-tab li").click(function (){//切换专家类型
        var num = $(this).index();
        $(this).addClass("layui-this").siblings().removeClass();
        $(".expertlist").hide();
        $("#expertlist" + num).show();
        initlist();
    });
    $(".content-medical").off("click", '.divselexpert').on("click", '.divselexpert', function (){//选择专家
         var seltabindex = $("#ultop-tab li.layui-this").index();
        $("#expertlist" + seltabindex + " .divselexpert").removeClass("current");
        $(this).addClass("current");
    });
    form.on("select(sel_expertfrom)", function (){
        initlist();
    });
}

function initlist() {
    var expertfrom = $("#sel_expertfrom").val();//库
    if(!expertfrom){
        return parent.layer.msg("请先选择专家库");
    }
    jQuery.getparent().layer.load();
    if(expertfrom == 'p'){
        $.sm(function (re, err){
            getlistcount (re, err);
        }, ["xjexpert.getlistcount", $.msgwhere(getwhere())], "w", "", { rpc: 'tbpt' });
    }else{
        $.sm(function (re, err){
            getlistcount (re, err);
        }, ["xjexpert.getlistcount", $.msgwhere(getwhere())]);
    }
}

function getlistcount (re, err) {
    if (err) {
        jQuery.getparent().layer.msg(err);
    } else {
        var count = re[0].total || 0;
        layui.laypage.render({
            elem: 'laypage'
            , count: count // 数据总数
            , limit: objdata.page.limit
            , layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
            , jump: function (obj) {
                objdata.page.curpage = obj.curr;
                objdata.page.limit = obj.limit;
                initlistdata();
            }
        });
    }
}

/**
 * type 1.讲座型中医儿科专家, 2.治疗型中医儿科专家
 */
function initlistdata() {
    var expertfrom = $("#sel_expertfrom").val();//库
    jQuery.getparent().layer.load();
    if(expertfrom == 'p'){
        $.sm(getjsonlist, ["xjexpert.getjsonlist", $.msgwhere(getwhere()), objdata.page.limit, (objdata.page.curpage - 1) * objdata.page.limit], "w", "", { rpc: 'tbpt' });
    } else {
        $.sm(getjsonlist, ["xjexpert.getjsonlist", $.msgwhere(getwhere()), objdata.page.limit, (objdata.page.curpage - 1) * objdata.page.limit]);
    }
}

function getjsonlist(re, err) {
    if (err) {
        jQuery.getparent().layer.msg(err);
    } else {
        var expertfrom = $("#sel_expertfrom").val();//库
        var type = $("#ultop-tab li.layui-this").index();
        var arrhtml = [];
        // id, name, img, type, professional, workunit,grade,goodat
        for (var i = 0; i < re.length; i++) {
            objdata.expertinfo[re[i].id] = re[i];
            if(expertfrom == 'p'){
                re[i].img = tbptossPrefix + re[i].img;
            }else{
                re[i].img = (islocal ? 'https://testshenyangfuyou.cpmch.com:553/' + re[i].img : ossPrefix + re[i].img)
            }
            re[i].expertypename = objdata.experttype[re[i].type];//专家类型名称

            arrhtml.push(renderTemp(objdata.expertinfotemp, re[i]));
        }
        if (arrhtml.length > 0) {
            $("#expertlist" + type).html(arrhtml.join(''));
            $("#divnodata").hide();
            $("#laypage").show();
            $(".layui-col-space10>*").css({"padding": "5px"});
        } else {
            $("#expertlist" + type).html("");
            $("#divnodata").show();
            $("#laypage").hide();
        }
        jQuery.getparent().layer.closeAll('loading');
    }
}

/**
 * 查询条件
 * @returns {{}}
 */
function getwhere() {
    var sou = {};
    var expertname = $.trim($("#txtexpertname").val());
    if (expertname) {
        sou.name = [expertname];
    }
    var type = $("#ultop-tab li.layui-this").index();
    if(type == 1){//治疗型中医儿科专家
        sou.type = [2];
    }else{//讲座型中医儿科专家
        sou.type = [1];
    }
    return sou;
}

/**
 * 选择医生
 * @param cb
 * @returns {boolean}
 */
function saveEvent(cb){
    var type = $("#ultop-tab li.layui-this").index();
    var curselexpert = $("#expertlist" + type + " .divselexpert.current");
    if(!curselexpert.length){//没有选择专家
        return layer.msg('请先选择专家！');
    }
    var expertid = curselexpert.attr("expertid");
    var expertinfo = objdata.expertinfo[expertid];
    var expertfrom = $("#sel_expertfrom").val();//专家来源 f自建库，p童帮库
    expertinfo.expertfrom = expertfrom;
    expertinfo.expertypename = objdata.experttype[objdata.expertinfo[expertid].type];
    cb && cb(expertinfo);
}

function renderTemp (template, model) {
    var reg = /\{\{\w+\}\}/g;
    template = template.replace(reg, function (regStr) {
        var reg2 = /\{\{(\w+)\}\}/g,
            key = reg2.exec(regStr)[1];
        return model[key];
    });
    return template;
};