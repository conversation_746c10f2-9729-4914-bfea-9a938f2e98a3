﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>幼儿园管理</title>
    <link rel="stylesheet" href="../css/reset.css">
    <link rel="stylesheet" href="../css/icon.css">
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../css/medical.css">
    <!--[if lt IE 9]>
    <script src="../sys/html5shiv.min.js"></script>
    <![endif]-->
    <style type="text/css">

        html, body {
            background: #EAEFF3;
            overflow: hidden;
        }

        .layui-form-label {
            line-height: 28px;
        }
    </style>
</head>
<body>
<div class="marmain" style="min-width: 950px;">
    <div class="content-medical">
        <div class="layui-form layui-comselect" style="padding:10px;">
            <div class="layui-form-item" style="display:inline-block;">
                <span class="layui-form-label" style="width: 75px;">托幼机构：</span>
                <div class="layui-input-inline" style="">
                    <input name="yeyname" type="text" autocomplete="off" placeholder="托幼机构名称" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item" style="display:inline-block;">
                <span class="layui-form-label" style="width: 75px;">机构类型：</span>
                <div class="layui-input-inline layui-form " style="width: 120px;">
                    <select id="yeytype" lay-filter="yeytype"></select>
                </div>
            </div>
            <div class="layui-form-item" style="display:inline-block;">
                <span class="layui-form-label organhide" style="width: 60px;display:none;">医院：</span>
                <div class="layui-input-inline organhide" style="display:none;">
                    <input name="fuyouorganname" type="text" autocomplete="off" placeholder="医院名称" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item" style="display:inline-block;">
                <span class="layui-form-label" style="width: 160px;">所属基层卫生服务机构：</span>
                <div class="layui-input-inline layui-form " style="width: 200px;">
                    <select id="selorgan" lay-filter="selorgan"></select>
                </div>
            </div>
            <div class="layui-form-item" style="display:inline-block;">
                <span class="layui-form-label" style="width: 75px;">体检医院：</span>
                <div class="layui-input-inline layui-form " style="width: 200px;">
                    <select id="selmdical" lay-filter="selmdical"></select>
                </div>
            </div>
            <div class="layui-form-item" style="display:inline-block;">
                <span class="layui-form-label" style="width: 75px;">园所等级：</span>
                <div class="layui-input-inline layui-form " style="width: 200px;">
                    <select id="selclassify" lay-filter="selclassify">
                        <option value="">请选择</option>
                        <option value="0">无等级</option>
                        <option value="1">一级</option>
                        <option value="4">二级</option>
                        <option value="7">三级</option>
                    </select>
                </div>
            </div>
            <!--                <span class="layui-form-label organhide" style="width: 115px;display:none;">是否关联医院：</span>-->
            <!--                <div class="layui-input-inline layui-form organhide" style="float:left;width: 85px;display:none;" id="un_select">-->
            <!--                    <select id="isrelevan" lay-filter="isrelevan">-->
            <!--                        <option value="" selected="">全部</option>-->
            <!--                        <option value="1">是</option>-->
            <!--                        <option value="0">否</option>-->
            <!--                    </select>-->
            <!--                </div>-->
            <!--                <span class="layui-form-label organhide" style="width: 60px;display:none;">来源：</span>-->
            <!--                <div class="layui-input-inline layui-form organhide" style="float:left;width: 105px;display:none;">-->
            <!--                    <select id="yeysrc" lay-filter="yeysrc">-->
            <!--                        <option value="" selected="">请选择</option>-->
            <!--                        <option value="1">申请加入</option>-->
            <!--                        <option value="0">妇幼添加</option>-->
            <!--                    </select>-->
            <!--                </div>-->
    

                <button class="layui-btn blockcol  mgl-20" id="search" style="margin-left: 10px;vertical-align: top;">查询</button>
                <button class="layui-btn blockcol  mgl-20 organhide" id="refresh" style="display:none;vertical-align: top;">刷新</button>
                <button class="layui-btn blockcol  mgl-20 organhide" id="btnaddyey" style="display:none;margin-left: 10px;vertical-align: top;">添加托幼机构</button>
                <!--<button class="layui-btn blockcol  mgl-20 organhide" id="btnadd" style="display:none;margin-left: 10px;">申请园所</button>-->
                <button class="layui-btn blockcol  mgl-20 organhide" id="btnmodeldown" style="display:none;vertical-align: top;">模板下载</button>
                <button class="layui-btn blockcol  mgl-20 organhide" id="btnimport" style="display:none;vertical-align: top;">导入托幼机构</button>
            <button class="layui-btn blockcol  mgl-20 organhide" id="btnexport" style="display:none;vertical-align: top;">导出</button>
            <button class="layui-btn blockcol  mgl-20" id="btnorgan" style="vertical-align: top;">批量关联基层卫生服务机构</button>
            <button class="layui-btn blockcol  mgl-20" id="btnmdical" style="display:none;vertical-align: top;">批量关联体检医院1</button>
                <!--<button class="layui-btn blockcol  mgl-20 organhide" id="btnapplymore" style="display:none;">批量申请开通园所</button>-->
            
            
        </div>
    </div>
    <div id="content" style="width: 100%;">
        <div class="marmain-cen">
            <div class="content-medical">
                <div class="tbmargin">
                    <table id="tbmain" lay-filter="tbmain">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/html" id="statusTpl">
    {{#  if(d.relationstatus === 'no'){ }}
    <span style="color: #F581B1;">未关联</span>
    {{#  } else if(d.relationstatus === 'yes'){ }}
    <span style="">已关联</span>
    {{#  }}}
</script>
<script type="text/html" id="jointimeTpl">
    {{#  if(d.relationtime){ }}
    {{ d.relationtime }}
    {{#  } else { }}
    无
    {{#  } }}
</script>

<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../js/yeylist.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>
