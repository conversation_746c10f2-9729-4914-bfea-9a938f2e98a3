/*红色皮肤*/
/*color*/
.bg1{background: #de575d;}
/*.bg2{background: #de575d;}*/
.bg3{background: #de575d;}
.bg4{background: #f3f3f3;}/*浅灰*/
.bg5{background: #e9e9e9;}/*深灰*/
.cl1{color: #47494a;}
.cl2{color: #de575d;}/*红色*/

/*icon*/
u[class^=skinIcon],.layim_chateme .layim_chatsay .layim_zero,skinIcondenglu u,skinIconbq u{background-image:url(../images/icon-red.png);}

/*btn*/
.btn-big,.personalImg .webuploader-pick,.btn-black,.monthHead,.echarts-dataview button{background: #de575d;}
.popTitle,.fc-day-header{background: #de575d;}
.letterIndex{background: #ccc;}
.btn-border{border:1px solid #de575d;}
.btn-black:hover,.btn-big:hover{background: #ba3523;}
.layui-layer-btn a.layui-layer-btn0,.btnTop a:hover,.btn-control:hover,.operateBtn a:hover{background:#de575d;border-color:#ba3523;}

/*tab*/
.tab li:hover, .tab li.current{color: #fff; background:#de575d!important;border: 1px solid #de575d;}
.tab li.pulldown.current{color: #fff; background:#de575d url(../images/icon_pulldown_white.png) no-repeat 75px center!important;background-size: 12px !important;border: 1px solid #de575d;}
.tab li.pulldown:hover{color: #fff; background:#de575d url(../images/icon_pulldown_white.png) no-repeat 75px center!important; background-size: 12px !important;border: 1px solid #de575d;}

/*page*/
.dataTables_wrapper .dataTables_paginate .paginate_button.current, .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover,.dataTables_wrapper .dataTables_paginate .paginate_button:hover{ background: -webkit-linear-gradient(#de575d 0%, #c23a23 100%);background: linear-gradient(#de575d 0%, #c23a23 100%);color: #fff !important;border-color:#de575d;filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#de575d', endColorstr='#c23a23', GradientType=0);}

/*聊天窗口*/
.layim_chatmore{border-right:1px solid #c23a23; background-color:#de575d;}
.layim_chatlist li:hover,.layim_chatlist .layim_chatnow,.layim_chatlist .layim_chatnow:hover{background-color:#f5bcb5;}
.layim_sendbtn{ background-color:#de575d;}
.layim_enter{border-left-color:#f5bcb5;}
.layim_sendbtn:hover,.layim_enter:hover{background-color:#c23a23;}
.layim_sendtype span:hover,.layim_sendtype span:hover i{background-color:#de575d;color:#fff;}
.layim_chateme .layim_chatsay{background: #f5bcb5;}

/*左侧面板*/
/*.contaceHead,.head,.scrollArea,.foldList{background: #403433;}*/
.funList li{color: #de575d;}
/*.usualFun a:hover,.inner a:hover,*/.letterList li:hover,.letterList li.current,.btn-head:hover,.articleGray a:hover,.loadMore:hover,.innerHistory li:hover,.today_date,.alm_content p,.list .current,.ztree li a:hover,.foldList li p,.foldList li:hover,.dataTables_wrapper a,.otx-table a,.foldList li,.fc-sat .fc-day-num, .fc-sun .fc-day-num{color: #de575d;}
footer a.current,footer a:hover{color: #de575d;}
.curList .current,.curList a:hover{color: #403433;}
/*.usualFun a:hover img,.inner a:hover img,.foldList li:hover img{background: url(../images/hover-red.png) no-repeat center center;}*/
.list li{border-bottom: 1px solid #de575d;}
.leftTitle{border-bottom: 1px solid #de575d; background: #de575d;}
.btn-lang:hover,.current.btn-lang{border-color:#de575d;color:#de575d;}
.list .face{border-color:#b8b8b8;}

/*智能中控*/
.btn-far:hover,.btn-near:hover{background:url(../images/distance-red.png);}
.fast:hover,.normal:hover,.slow:hover,.arrow-up:hover,.arrow-down:hover{background: url(../images/hover-red.png);}
.numberList span:hover{background: url(../images/red-big.png);}

/*通知公告*/
/*.focus,.contaceHead .name,.letter,.numberList span:hover,.logo,.leftTitle,.head,.usualFun a, .inner a,footer a,.curList a,.workTotal{color: #fff;}*/

.noticeTitle a:hover{color:#403433;}

/*日历*/
.fc-state-down,.fc-state-active,.fc-button:hover{border-color:#de575d;}
.fc-planmeeting-button{border:0;border-radius: 0;}
.fc-year-button:hover,.fc-month-button:hover,.fc-agendaWeek-button:hover,.fc-agendaDay-button:hover,.fc-state-active{background: #de575d;color:#fff;}

/*会控*/
.meetingIndex,.fc-planmeeting-button{color:#fff; background: #de575d;}
.rankList span.current{background: #ea583f;}

/*栏目管理*/
.meauList span{border-top: 2px solid #de575d; border-bottom: 2px solid #de575d;}
.meauList .inner a{color: #de575d;}
.inner a.selected{background: #f5bcb5;}

/*单位管理*/
.jOrgChart .node,.formList .webuploader-pick{background:#de575d;}

/*tree*/
.ztree li a.curSelectedNode{background-color: #f8cdc7;border: 1px solid #de575d; color: #de575d;}

/*会议控制*/
.meetMain{border-top: 4px solid #de575d;}
.submeetList{border-top: 4px solid #f5bcb5;}

/*下拉多选*/
.ui-widget-header{background: #ea583f; border-color:#ea583f;}
.ui-state-hover, .ui-widget-content .ui-state-hover, .ui-widget-header .ui-state-hover, .ui-state-focus, .ui-widget-content .ui-state-focus, .ui-widget-header .ui-state-focus{color: #ea583f;}
.ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active{background: none; border-color:#ea583f;color: #ea583f;}
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default{ border-color:#ea583f;color: #ea583f;}





/*新增加样式*/

.usualFun{ background:#FFFFFF; padding-left:3px; padding-bottom:3px}
.usualFun a{float: left; width: 32%; +width: 32%; margin-top:3px;position: relative;text-align: center;font-size: 12px;line-height: 28px;color:#ffffff; background:#de575d; margin-right:3px; font-size:14px;}
.usualFun img{width: 35px;height: 35px; display: block; margin:0 auto;/*border-radius: 50%; *//*background: url(../images/gray.png) no-repeat center center;*/ padding-top:10px}
.usualFun u{position: absolute;display: none;}

/*.inner a:hover{width: 32%; +width: 32%; margin-top:3px;position: relative;text-align: center;font-size: 12px;line-height: 28px;color:#ffffff; background:#53C2BC; margin-right:3px; font-size:14px; display:block}
*/


.inner a:hover{ color:#FFF; background:#de575d; display:inline-block}

.usualFun a:hover{color:#FFF;}

/*幼儿园名称下拉背景色*/
.selectColor{background-color:#de575d }

/*智慧数字幼儿园*/
/*桌面头部菜单*/
.ultopnums li.current,.ultopnums li:hover,.topultool li:hover,.topultool li.current {
    color: #ffffff;
    background: -webkit-linear-gradient(#de575d, #b54247); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(#de575d, #b54247); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(#de575d, #b54247); /* Firefox 3.6 - 15 */
    background: linear-gradient(#de575d, #b54247); /* 标准的语法 */
}
/*菜单切换*/
.menutab ul{color: #34495E!important;}
.menutab ul li{color: #F3F4F4;border-bottom:3px solid #D5D6D6; 
	background: #F3F4F4;		
	border-image: -webkit-linear-gradient(#F2F3F4,#D5D6D6) 40 20;
    border-image: -moz-linear-gradient(#F2F3F4,#D5D6D6) 40 20;		
	border-image: -o-linear-gradient(#F2F3F4,#D5D6D6) 40 20;
	border-image: linear-gradient(#F2F3F4,#D5D6D6) 40 20;}
.menutab ul li.current{color: #34495E!important; background: #fff;}
#divdesktop .scrollArea{background: none;}
#divmenulist .funList li:hover{background: #F2F2F2;color: #666!important;}
#divdeskleft:after{content: "";width: 20px;position: absolute;top: 0px;bottom: 0;right: -20px;box-shadow: 5px 0px 5px -4px inset #DCE0E4;
  
}
.funtop .desktop-logo .tabout-div{box-shadow: 0px 0px 5px 2px #cc464c;}
.funtop .desktop-logo .tab-cell{box-shadow: 0px -8px 4px -5px inset #e1a6a9;}

/*保教*/
.term-tab.current{background: #f49090;color: #ffffff;}
.term-tab.current .calendar-div{border: 1px solid #e06c6c;}
.term-list.redbg{background: #fd9d9d;}
.calen-top h5{background: #f49090;}
.calen-txt p{color: #e46160;}
.div-right .layui-table tbody.current{border: 2px solid #fd9d9d;}
.div-right .layui-table tbody.current tr:nth-child(1) td:nth-child(1){background-color: #fd9d9d;}
.div-right .layui-table td.pinkcurrent{background-color: #fd9d9d;}
.workrest-left h5{background: #f49090!important;}
.workrest-right h5{background: #fd9d9d!important;}
.time-work li.time-worksel{ background:#fd9d9d;}
.week-tab.current{background: #f49090;}
.icon-list .layui-btn{background: #ec8383!important;}
.protect-educate .plan-txt.current{background: #fd9d9d;color: #ffffff;border: 1px solid #fd9d9d;}
.play-sub{border: 2px solid #fd9d9d!important;}
.def-icon .icon_calen:before{color: #fd9d9d!important;}
.opreate-icon .icon-txt .iconfont{color: #fd9d9d!important;}
.protect-educate .plan-left-icon>div .iconfont:before{color: #fd9d9d;}
.protect-educate .plan-left-icon>div .icon_ratio:before{border: 2px solid #fd9d9d;}
.def-icon .icon_tableft:before,.def-icon .icon_tabright:before{color: #fd9d9d;}
#a_unselect{color: #fd9d9d!important;}
.upload-btn .opr_select{background: #fd9d9d!important;}
.opr-btn.pay-save{background: #fd9d9d!important;}
#pgriddivshipuarea.def-layui-table tr.current td:not(.tr-firsttd){background: #FCEBEB;}
#pgridplantable.def-layui-table tr.current td:not(.tr-firsttd){background: #FCEBEB;}
/*作息弹框按钮颜色*/
.opr-btn{background:#fd9d9d;}
.cho-main .layui-btn{background: #fd9d9d;}
.plan-con{border: 4px solid #fd9d9d;}
.plan-leftspan.currentpink, .plan-rightspan.currentpink{background: #fd9d9d;}
.plan-leftspan.currentred, .plan-rightspan.currentred{background: #f45f58;}
.left-contain .common-tab3 li.current a{color: #fd9d9d;font-weight: bold;border-bottom: 2px solid #fd9d9d;}
.left-contain .icon_search2:before{color: #fd9d9d;}
.type-plan .iconfont.redicon2:before{color: #fd9d9d;}
#divdt .layui-table tbody.current{border: 2px solid #fd9d9d;}
#divdt .layui-table tbody.current tr:nth-child(1) td:nth-child(1){background-color: #fd9d9d;}
#divdt .layui-table td.pinkcurrent{background-color: #fd9d9d;}
#divdt .tdexist{background-color: #fc5757;}
.timetable-list .circle-label{background: #fd9d9d;}
.teaching-list .teachadd-btn .teachlayui-btn{background: #fd9d9d;}
.upload-list .layui-btn-normal, .teaching-list-bom .upload-right .layui-btn, .cover-list .layui-btn{background: #fd9d9d;}
#add_pic .webuploader-pick{background: #fd9d9d;}
.teaching-list .layui-btn{color: #fd9d9d;}
.upload-btn .opr_save{background: #fd9d9d!important;}
.upload-btn .opr_saveadd{background: #fd9d9d!important;}
.course-opr .layui-tab-brief>.layui-tab-title .layui-this{color: #fd9d9d;}
.course-opr .layui-tab-brief>.layui-tab-more li.layui-this:after,.course-opr .layui-tab-brief>.layui-tab-title .layui-this:after{border-bottom: 4px solid #fd9d9d;}
#add_pic .webuploader-pick{background:#fd9d9d;}
#add_pic .webuploader-pick, #add_video .webuploader-pick{background:#fd9d9d;}
#add_audio .webuploader-pick{background:#fd9d9d;}
#add_files .webuploader-pick{background:#fd9d9d;}



