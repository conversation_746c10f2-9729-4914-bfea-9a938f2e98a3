﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>儿童基础档案</title>
    <link rel="stylesheet" type="text/css" href="../../css/reset.css"/>
    <link rel="stylesheet" type="text/css" href="../../layui-btkj/css/layui.css"/>
    <link rel="stylesheet" type="text/css" href="../../css/icon.css"/>
    <link rel="stylesheet" type="text/css" href="../../css/commonstyle-layui-btkj.css"/>
    <link rel="stylesheet" type="text/css" href="../../css/style.css"/>
    <link rel="stylesheet" href="../../css/medical.css"/>
    <style>
        html, body {
            background: #EAEFF3;
            overflow: hidden;
        }

        .layui-form-item {
            /*display: inline-block;*/
            vertical-align: top;
            margin: 5px 5px 0px 0;
        }

        .layui-form-label {
            line-height: 28px;
        }

        /*.layui-table-cell{height:auto;}*/
        .progressbar_1 {
            z-index: 999;
        }

        .layui-form-onswitch em {
            margin-left: 0;
        }

        .scenarios .layui-tab-title {
            margin-left: 15px;
            border: 1px solid #d8d8d8;
            margin: 20px 0;
            border-radius: 3px;
            height: 30px;
        }

        .scenarios .layui-tab-title li {
            line-height: 30px;
            font-size: 14px;
            color: #34495E;
        }

        .scenarios .layui-tab-title .layui-this::after {
            border-bottom: none;
            border-top: none;
        }

        .scenarios .layui-tab-title .layui-this {
            border: 1px solid #4A90E2;
            background: #E8F3FF;
            color: #4A90E2;
        }

        .scenarios .layui-tab-title .layui-this::after {
            height: 30px;
        }

        .scenarios .layui-tab-title li:first-child {
            border-right: none;
        }

        .messagesimg {
            float: left;
            padding-top: 8px;
        }

        .layui-form-onswitch {
            border-color: #4A90E2;
            background-color: #4A90E2;
        }

        .layui-form-switch {
            min-width: 30px;
        }

        .settingslist {
            border: 1px solid #e4e4e4;
            padding: 10px 20px;
            position: relative;
        }

        .offimg {
            position: absolute;
            right: 20px;
            top: 15px;
        }
    </style>
</head>
<body>
<div class="">
    <div class="layui-form layui-comselect" style="padding: 10px 20px; position: relative; background-color: white; margin-bottom: 8px;">
        <div class="layui-form-item margin20" style="display: inline-block;vertical-align: top; float: left;margin-right:10px;">
            <label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 0px;line-height: 28px;">短信网关设置：</label>
            <div class="layui-input-block" style="min-height: 30px;margin-left:0px;width:347px; float: left;">
                <select id="smsgatewaytype" lay-filter="smsgatewaytype">
                    <option value="">请选择</option>
                    <option value="1">系统内置网关</option>
                    <option value="2">外部网关</option>
                </select>
            </div>
        </div>
        <input id="smsgateway" name="smsgateway" type="text" placeholder="外部网关请输入参数" class="layui-input" style="width:300px; border: none; border-bottom: 1px solid #CBD6E1;display: inline-block;">
        <div class="layui-input-inline">
            <button class="layui-btn form-search" style="margin-left: 5px;vertical-align: top;" id="btnsave">保存</button>
        </div>
    </div>

    <div class="content-medical text-verification">
        <div class="layui-form layui-comselect" style="padding:10px;">
            <div class="layui-form-item settingslist">
                <img src="../../images/medical/messagesimg.png" style="width: 16px; margin-right: 5px;" class="messagesimg">
                <div>
                    <h3>预约入园体检成功通知</h3>
                    <div class="tong-list">
                        【云妇幼康】王娟您好，您已预约我院&lt;2024年03月18日-上午-入园体检&gt;，预约识别码:【44271998】，请携带预约时使用的有效证件取号。
                    </div>
                    <div class="layui-form offimg">
                        <input id="ryyy" type="checkbox" name="status" lay-skin="switch" lay-filter="ryyy" checked lay-text="启用|停用" value="1">
                    </div>
                </div>
            </div>
            <div class="layui-form-item settingslist">
                <img src="../../images/medical/messagesimg.png" style="width: 16px; margin-right: 5px;" class="messagesimg">
                <div>
                    <h3>预约在园体检成功通知</h3>
                    <div class="tong-list">
                        【云妇幼康】王娟您好，您已预约我院&lt;2024年03月18日-上午-在园体检&gt;，预约识别码:【44271998】，请携带预约时使用的有效证件取号。
                    </div>
                    <div class="layui-form offimg">
                        <input id="dqyy" type="checkbox" name="status" lay-skin="switch" lay-filter="dqyy" checked lay-text="启用|停用" value="1">
                    </div>
                </div>
            </div>
            <div class="layui-form-item settingslist">
                <img src="../../images/medical/messagesimg.png" style="width: 16px; margin-right: 5px;" class="messagesimg">
                <div>
                    <h3>入园体检结果通知</h3>
                    <div class="tong-list">
                        【云妇幼康】王娟您好，张小宝的入园体检报告已发布，请前往云妇幼康小程序查看。
                    </div>
                    <div class="layui-form offimg">
                        <input id="ryjg" type="checkbox" name="status" lay-skin="switch" lay-filter="ryjg" checked lay-text="启用|停用" value="1">
                    </div>
                </div>
            </div>
            <div class="layui-form-item settingslist">
                <img src="../../images/medical/messagesimg.png" style="width: 16px; margin-right: 5px;" class="messagesimg">
                <div>
                    <h3>在园体检结果通知</h3>
                    <div class="tong-list">
                        【云妇幼康】王娟您好，张小宝的在园体检报告已发布，请前往云妇幼康小程序查看。
                    </div>
                    <div class="layui-form offimg">
                        <input id="dqjg" type="checkbox" name="status" lay-skin="switch" lay-filter="dqjg" checked lay-text="启用|停用" value="1">
                    </div>
                </div>
            </div>
            <div class="layui-form-item settingslist">
                <img src="../../images/medical/messagesimg.png" style="width: 16px; margin-right: 5px;" class="messagesimg">
                <div>
                    <h3>健康证到期通知</h3>
                    <div class="tong-list">
                        【云妇幼康】王娟您好，您的健康证将于2024年03月18日到期，请及时预约下次体检。
                    </div>
                    <div class="layui-form offimg">
                        <input id="card_expire" type="checkbox" name="status" lay-skin="switch" lay-filter="card_expire" checked lay-text="启用|停用" value="1">
                    </div>
                </div>
            </div>
            <div class="layui-form-item settingslist">
                <img src="../../images/medical/messagesimg.png" style="width: 16px; margin-right: 5px;" class="messagesimg">
                <div>
                    <h3>健康证结果通知</h3>
                    <div class="tong-list">
                        【云妇幼康】王娟您好，您的健康证体检报告已发布，请前往云妇幼康小程序查看。
                    </div>
                    <div class="layui-form offimg">
                        <input id="card_result" type="checkbox" name="status" lay-skin="switch" lay-filter="card_result" checked lay-text="启用|停用" value="1">
                    </div>
                </div>
            </div>
            <div class="layui-form-item settingslist">
                <img src="../../images/medical/messagesimg.png" style="width: 16px; margin-right: 5px;" class="messagesimg">
                <div>
                    <h3>预约科室成功通知</h3>
                    <div class="tong-list">
                        【云妇幼康】王娟您好，您已预约我院&lt;2024年03月18日-上午-消化内科门诊，建议候诊时间：08:00-08:30&gt;，就诊序号2号，就诊位置三层西南侧，如不能按时就诊，请在就诊前1日15:00前在云妇幼康小程序办理预约取消，以免爽约影响今后预约。
                    </div>
                    <div class="layui-form offimg">
                        <input id="yyks" type="checkbox" name="status" lay-skin="switch" lay-filter="yyks" checked lay-text="启用|停用" value="1">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/function.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../js/sms/sms_set.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>
