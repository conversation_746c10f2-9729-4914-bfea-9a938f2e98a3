<?xml version="1.0" encoding="utf-8"?>
<Root>
    <msgs>
        <!--健康宣教库-->
        <msg id="xjhealthstore.getlistcount" type="selectjson"  v="select count(1) as total from xj_healthstore where isdel=0 #0#" d="f" did="{guid}">
            <where idx="0">
                <p key="title"> and title like '%{0}%'</p>
                <p key="types"> and types={0}</p>
            </where>
        </msg>
        <msg id="xjhealthstore.getjsonlist" type="selectjson" v="select id,title,types,content,labels,img,author,address,to_char(creatime, 'yyyy-MM-dd HH24:MI:SS') as creatime from xj_healthstore where isdel=0  #0# order by creatime desc limit #1# offset #2#" d="f" did="{guid}">
            <where idx="0">
                <p key="title"> and title like '%{0}%'</p>
                <p key="types"> and types={0}</p>
            </where>
        </msg>
        <!-- 健康宣教库-童帮库-点击title查询详情 -->
        <msg id="xjhealthstore.detail" type="selectonejson" v="select id,title,types,content,labels,img,author,address,creatime from xj_healthstore where isdel=0 and #0# " d="f" did="{guid}">
            <where idx="0">
                <p key="id"> id={0}</p>
            </where>
        </msg>
        <msg id="xjhealthstore.add" type="insertjson" v="xj_healthstore%16#0#">
            <where idx="columns">
                <p key="title" format="string"></p>
                <p key="types" format="int"></p>
                <p key="content" format="string" decode="1"></p>
                <p key="labels" format="string"></p>
                <p key="img" format="string"></p>
                <p key="author" format="string"></p>
                <p key="address" format="string"></p>
            </where>
        </msg>
        <msg id="xjhealthstore.update" type="updatejson" v="xj_healthstore%16#0#%16#1#">
            <where idx="columns">
                <p key="title" format="string"></p>
                <p key="types" format="int"></p>
                <p key="content" format="string" decode="1"></p>
                <p key="labels" format="string"></p>
                <p key="img" format="string"></p>
                <p key="author" format="string"></p>
                <p key="address" format="string"></p>
            </where>
            <where idx="1">
                <p key="id">id={0}</p>
            </where>
        </msg>
        <msg id="xjhealthstore.del" type="update"  v="update xj_healthstore set isdel=1 where isdel=0 and #0#"  d="f" did="{guid}">
            <where idx="0">
                <p key="id"> id={0}</p>
            </where>
        </msg>
        <msg id="xjhealthstore.getcountbytypes" type="selectonejson" v="select count(1) as count from xj_healthstore where isdel=0 and #0# " d="f" did="{guid}">
            <where idx="0">
                <p key="types"> types={0}</p>
            </where>
        </msg>


        <!--健康宣教-->
        <msg id="xjhealth.getlistcount" type="selectjson"  v="select count(1) as total from xj_health where isdel=0 #0#" d="f" did="{guid}">
            <where idx="0">
                <p key="title"> and title like '%{0}%'</p>
                <p key="types"> and types={0}</p>
                <p key="status"> and status={0}</p>
                <p key="releaseuserid"> and releaseuserid={uid}</p>
            </where>
        </msg>
        <msg id="xjhealth.getjsonlist" type="selectjson" v="select id,title,types,content,labels,img,author,address,to_char(creatime, 'yyyy-MM-dd HH24:MI:SS') as creatime,readcount,releaseusername from xj_health where isdel=0  #0# order by creatime desc limit #1# offset #2#" d="f" did="{guid}">
            <where idx="0">
                <p key="title"> and title like '%{0}%'</p>
                <p key="types"> and types={0}</p>
                <p key="status"> and status={0}</p>
                <p key="releaseuserid"> and releaseuserid={uid}</p>
            </where>
        </msg>
        <msg id="xjhealth.detail" type="selectonejson" v="select id,title,types,content,labels,img,author,address,status from xj_health where isdel=0 and #0# " d="f" did="{guid}">
            <where idx="0">
                <p key="id"> id={0}</p>
            </where>
        </msg>
        <msg id="xjhealth.add" type="insertjson" v="xj_health%16#0#">
            <where idx="columns">
                <p key="title" format="string"></p>
                <p key="types" format="int"></p>
                <p key="content" format="string" decode="1"></p>
                <p key="labels" format="string"></p>
                <p key="img" format="string"></p>
                <p key="author" format="string"></p>
                <p key="address" format="string"></p>
                <p key="status" format="int"></p>
                <p key="releaseuserid" format="int"></p>
                <p key="releaseusername" format="string"></p>
            </where>
        </msg>
        <msg id="xjhealth.update" type="updatejson" v="xj_health%16#0#%16#1#">
            <where idx="columns">
                <p key="title" format="string"></p>
                <p key="types" format="int"></p>
                <p key="content" format="string" decode="1"></p>
                <p key="labels" format="string"></p>
                <p key="img" format="string"></p>
                <p key="author" format="string"></p>
                <p key="address" format="string"></p>
                <p key="status" format="int"></p>
                <p key="releaseuserid" format="int"></p>
                <p key="releaseusername" format="string"></p>
            </where>
            <where idx="1">
                <p key="id">id={0}</p>
            </where>
        </msg>
        <msg id="xjhealth.del" type="update"  v="update xj_health set isdel=1 where isdel=0 and #0#"  d="f" did="{guid}">
            <where idx="0">
                <p key="id"> id={0}</p>
            </where>
        </msg>
        <msg id="xjhealth.savefile" type="myrule" v="enrollrules.savefile%16#0#%16#1#%16#2#%16#3#"/>
        <msg id="xjhealth.getcountbytypes" type="selectonejson" v="select count(1) as count from xj_health where isdel=0 and #0# " d="f" did="{guid}">
            <where idx="0">
                <p key="types"> types={0}</p>
            </where>
        </msg>

        <!--健康宣教分类-->
        <msg id="xjhealthtypes.getlist" type="selectjson" v="select id,typename from xj_healthtypes where isdel=0 order by id desc" d="f" did="{guid}"></msg>
        <msg id="xjhealthtypes.add" type="insertjson" v="xj_healthtypes%16#0#">
            <where idx="columns">
                <p key="typename" format="string"></p>
            </where>
        </msg>
        <msg id="xjhealthtypes.update" type="updatejson" v="xj_healthtypes%16#0#%16#1#">
            <where idx="columns">
                <p key="typename" format="string"></p>
            </where>
            <where idx="1">
                <p key="id">id={0}</p>
            </where>
        </msg>
        <msg id="xjhealthtypes.del" type="update"  v="update xj_healthtypes set isdel=1 where isdel=0 and #0#"  d="f" did="{guid}">
            <where idx="0">
                <p key="id"> id={0}</p>
            </where>
        </msg>

        <!-- 健康宣教 标签管理-->
        <msg id="labellist.getdata" type="layuitable"
             v="id,labelname,labelremark,category,to_char(creatime,'YYYY-MM-DD HH24:MI:SS') as creatime,to_char(altime,'YYYY-MM-DD HH24:MI:SS') as altime,algorithm_type,algorithm_content,is_active %16 xj_label %16 isdel = 0">
            <where idx="laywhere">
                <p key="labelname">and (labelname like '%{0}%')</p>
            </where>
        </msg>
        <msg id="labellist.del" type="update" v="update xj_label set isdel=1 where id=#0#"/>
        <msg id="labellist.add" type="insertjson" v="xj_label%16#0#%16#1#">
            <where idx="columns">
                <p key="labelname" format="string"></p>
                <p key="labelremark" format="string"></p>
                <p key="category" format="string"></p>
                <p key="algorithm_type" format="string"></p>
                <p key="algorithm_content" format="string"></p>
                <p key="is_active" format="int"></p>
            </where>
            <where idx="1">
                <p key="id">id={0}</p>
            </where>
        </msg>
        <msg id="labellist.getbyid" type="selectonejson" v="select labelname,labelremark,category,algorithm_type,algorithm_content,is_active from xj_label where isdel=0 and id=#0#"/>
        <msg id="labellist.getlist" type="selectjson" v="select labelname,labelremark,category,algorithm_type,algorithm_content,is_active from xj_label where isdel=0 #0#">
            <where idx="0">
                <p key="labelname"> and labelname like '%{0}%'</p>
            </where>
        </msg>

        <!--专家名医-->
        <msg id="xjexpert.getlistcount" type="selectjson"  v="select count(1) as total from xj_expert where isdel=0 #0#" d="f" did="{guid}">
            <where idx="0">
                <p key="name"> and name like '%{0}%'</p>
                <p key="type"> and type={0}</p>
            </where>
        </msg>

        <msg id="xjexpert.getjsonlist" type="selectjson" v="select id,name,type,img,professional,workunit,grade,goodat,id as expertid from xj_expert where isdel=0  #0# order by creatime desc, id limit #1# offset #2#" d="f" did="{guid}">
            <where idx="0">
                <p key="name"> and name like '%{0}%'</p>
                <p key="type"> and type={0}</p>
            </where>
        </msg>
        <msg id="xjexpert.detail" type="selectonejson" v="select id,name,type,img,professional,workunit,grade,goodat,id as expertid from xj_expert where isdel=0 and #0# " d="f" did="{guid}">
            <where idx="0">
                <p key="id"> id={0}</p>
            </where>
        </msg>
        <msg id="xjexpert.add" type="insertjson" v="xj_expert%16#0#">
            <where idx="columns">
                <p key="name" format="string"></p>
                <p key="img" format="string"></p>
                <p key="professional" format="string"></p>
                <p key="workunit" format="string"></p>
                <p key="grade" format="string"></p>
                <p key="goodat" format="string"></p>
                <p key="type" format="int"></p>
            </where>
        </msg>
        <msg id="xjexpert.update" type="updatejson" v="xj_expert%16#0#%16#1#">
            <where idx="columns">
                <p key="name" format="string"></p>
                <p key="img" format="string"></p>
                <p key="professional" format="string"></p>
                <p key="workunit" format="string"></p>
                <p key="grade" format="string"></p>
                <p key="goodat" format="string"></p>
                <p key="type" format="int"></p>
            </where>
            <where idx="1">
                <p key="id">id={0}</p>
            </where>
        </msg>
        <msg id="xjexpert.del" type="update"  v="update xj_expert set isdel=1 where isdel=0 and #0#"  d="f" did="{guid}">
            <where idx="0">
                <p key="id"> id={0}</p>
            </where>
        </msg>



        <!--  宝宝体检信息-->
        <msg id="xjdetail.ek_inmedical" type="selectonejson" v="select * from ek_inmedical where isdel=0 #1# order by id desc limit 1" d="f" did="#0#">
            <where idx="1">
                <p key="credentialsnum"> and credentialsnum='{0}'</p>
            </where>
        </msg>
        <msg id="xjdetail.check_child_result" type="selectonejson" v="select * from check_child_result where isdel=0 #1# order by id desc limit 1" d="f" did="#0#">
            <where idx="1">
                <p key="credentialsnum"> and credentialsnum='{0}'</p>
            </where>
        </msg>

    </msgs>
</Root>