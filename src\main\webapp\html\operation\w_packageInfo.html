<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>家长预约体检套餐情况</title>
    <link rel="stylesheet" type="text/css" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" type="text/css" href="../../css/icon.css"/>
    <link rel="stylesheet" type="text/css" href="../../styles/tbstyles.css"/>
    <link href="../../plugin/flexigrid/css/tbflexigrid.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css"/>
    <style type="text/css">
        html,
        body {
            background: #EAEFF3;
            overflow: hidden;
        }

        .layui-form-item {
            display: inline-block;
            vertical-align: top;
            margin: 5px 15px 0px 0;
        }

        .layui-form-label {
            line-height: 28px;
        }

        .search-container {
            padding: 15px;
            background-color: #fff;
            border-radius: 5px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
<div class="marmain">
    <div class="content-medical">
        <div class="search-container layui-form layui-comselect">
            <!-- 日期范围 -->
            <div class="layui-form-item">
                <label class="layui-form-label">预约挂号时间：</label>
                <div class="layui-input-inline">
                    <input id="startDate" type="text" autocomplete="off" placeholder="开始日期" class="layui-input">
                </div>
                <div class="layui-form-mid">至</div>
                <div class="layui-input-inline">
                    <input id="endDate" type="text" autocomplete="off" placeholder="结束日期" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">体检套餐：</label>
                <div class="layui-input-inline" style="width:200px;">
                    <select id="packageName" lay-filter="packageName">
                        <option value="">请选择</option>
                        <!-- 选项将通过JS动态加载 -->
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">关键字：</label>
                <div class="layui-input-inline" style="width:150px;">
                    <input id="keyword" type="text" autocomplete="off" placeholder="请输入体检套餐名称"
                           class="layui-input">
                </div>
            </div>
            <div class="btn-nextfl">
                <div class="layui-form-item">
                    <button class="layui-btn layui-btn-normal" style="margin-left: 5px;" id="btnsearch">查询</button>
                </div>
            </div>
        </div>
        <div id="content" style="width: 100%;">
            <div class="marmain-cen">
                <div class="content-medical" id="divtable">
                    <table id="laytable" lay-filter="laytable"></table>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript" data-main="../../js/operation/w_packageInfo.js"
            src="../../sys/require.min.js"></script>
</body>
</html>