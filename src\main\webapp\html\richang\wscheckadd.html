﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>添加卫生检查</title>
    <link type="text/css" rel="stylesheet" href="../../css/reset.css" />
    <link type="text/css" rel="stylesheet" href="../../layui-btkj/css/layui.css" />
    <link rel="stylesheet" type="text/css" href="../../css/icon.css" />
    <link rel="stylesheet" type="text/css" href="../../css/commonstyle-layui-btkj.css" />
    <link rel="stylesheet" type="text/css" href="../../css/style.css" />
    <style type="text/css">
        /*滚动条样式*/
        html{
            color: #000;
            font-size: .7rem;
            font-family: "\5FAE\8F6F\96C5\9ED1",Helvetica,"黑体",Arial,Tahoma;
            height: 100%;
        }
        /*滚动条样式*/
        html::-webkit-scrollbar {
            display:none;
            width: 6px;
            height: 6px;
        }
        html:hover::-webkit-scrollbar{
            display:block;
        }
        html::-webkit-scrollbar-thumb {
            border-radius: 3px;
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
            border: 1px solid rgb(255, 255, 255);
            background: rgb(66, 66, 66);
        }
        html::-webkit-scrollbar-track {
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
            border-radius: 0;
            background: rgba(0,0,0,0.1);
        }

        .default-form .layui-form-label { width: 100px; color: #778CA2; }
        .mwidth { width: 98px; display: inline-block; text-align: right; }
    </style>
</head>
<body>
    <div class="layui-page" style="padding: 0 15px;min-width: 666px;">
        <form id="form" action="" class="layui-form form-display" lay-filter="formOk">
            <div class="default-form" style="margin:20px 10px;">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>日期：</label>
                        <div class="layui-input-block" style="min-height: 30px;margin-left: 0px; position: relative;float: left; width: 150px;">
                            <input autocomplete="off" class="layui-input alendar-padding" type="text" id="txtstartdate" placeholder="日期" style="width:150px; display: inline-block;" lay-verify="required|chkclass" notip="1" />
                            <i class="layui-alendar"><img id="iconstartdate" src="../../images/cal_icon.png" style="width: 14px;" /></i>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><em>*</em>班级：</label>
                        <div class="layui-input-block">
                            <select id="selclass" lay-filter="selclass" lay-verify="required|chkclass"></select>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">检查项目：</label>
                        <div class="layui-input-block">
                            <div style="display: inline-block;">
                                <label class="mwidth">开窗通风：</label>
                                <input name="radwindows" type="radio" value="1" title="已完成" />
                                <input name="radwindows" type="radio" value="2" title="未完成" />
                            </div>
                            <div style="display: inline-block;margin-left:10px;">
                                <label class="mwidth">餐桌：</label>
                                <input name="radboard" type="radio" value="1" title="已完成" />
                                <input name="radboard" type="radio" value="2" title="未完成" />
                            </div>
                        </div>
                        <div class="layui-input-block">
                            <div style="display: inline-block;">
                                <label class="mwidth">餐饮具：</label>
                                <input name="radtableware" type="radio" value="1" title="已完成" />
                                <input name="radtableware" type="radio" value="2" title="未完成" />
                            </div>
                            <div style="display: inline-block;margin-left:10px;">
                                <label class="mwidth">毛巾等织物类：</label>
                                <input name="radtowel" type="radio" value="1" title="已完成" />
                                <input name="radtowel" type="radio" value="2" title="未完成" />
                            </div>
                        </div>
                        <div class="layui-input-block">
                            <div style="display: inline-block;">
                                <label class="mwidth">地面：</label>
                                <input name="radground" type="radio" value="1" title="已完成" />
                                <input name="radground" type="radio" value="2" title="未完成" />
                            </div>
                            <div style="display: inline-block;margin-left:10px;">
                                <label class="mwidth">床围栏：</label>
                                <input name="radbed" type="radio" value="1" title="已完成" />
                                <input name="radbed" type="radio" value="2" title="未完成" />
                            </div>
                        </div>
                        <div class="layui-input-block">
                            <div style="display: inline-block;">
                                <label class="mwidth">门把手：</label>
                                <input name="raddoor" type="radio" value="1" title="已完成" />
                                <input name="raddoor" type="radio" value="2" title="未完成" />
                            </div>
                            <div style="display: inline-block;margin-left:10px;">
                                <label class="mwidth">水龙头：</label>
                                <input name="radstopcock" type="radio" value="1" title="已完成" />
                                <input name="radstopcock" type="radio" value="2" title="未完成" />
                            </div>
                        </div>
                        <div class="layui-input-block">
                            <div style="display: inline-block;">
                                <label class="mwidth">图书晾晒：</label>
                                <input name="radbook" type="radio" value="1" title="已完成" />
                                <input name="radbook" type="radio" value="2" title="未完成" />
                            </div>
                            <div style="display: inline-block;margin-left:10px;">
                                <label class="mwidth">玩具：</label>
                                <input name="radtoy" type="radio" value="1" title="已完成" />
                                <input name="radtoy" type="radio" value="2" title="未完成" />
                            </div>
                        </div>
                        <div class="layui-input-block">
                            <div style="display: inline-block;">
                                <label class="mwidth">被褥晾晒：</label>
                                <input name="radquilt" type="radio" value="1" title="已完成" />
                                <input name="radquilt" type="radio" value="2" title="未完成" />
                            </div>
                            <div style="display: inline-block;margin-left:10px;">
                                <label class="mwidth">厕所：</label>
                                <input name="radtoilet" type="radio" value="1" title="已完成" />
                                <input name="radtoilet" type="radio" value="2" title="未完成" />
                            </div>
                        </div>
                        <div class="layui-input-block">
                            <div style="display: inline-block;">
                                <label class="mwidth">其他：</label>
                                <input class="layui-input" type="text" id="txtothername" placeholder="请自行注明" style="width:150px;display:inline-block" maxlength="10" />
                                <input name="radother" type="radio" value="1" title="已完成" />
                                <input name="radother" type="radio" value="2" title="未完成" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">检查人：</label>
                        <div class="layui-input-block">
                            <input class="layui-input" type="text" id="txtchecker" placeholder="请输入检查人姓名" style="width:252px;" maxlength="20" />
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">备注：</label>
                        <div class="layui-input-block">
                            <input class="layui-input" type="text" id="txtremarks" placeholder="请输入填写检查时发现的问题" style="width:486px;" maxlength="200" />
                        </div>
                    </div>
                </div>
                <div style="display:none;">
                    <div class="layui-input-block">
                        <a id="btnok" class="layui-btn" lay-submit=lay-submit>立即提交</a>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <script data-main="../../js/richang/wscheckadd" src="../../sys/require.min.js"></script>
</body>
</html>