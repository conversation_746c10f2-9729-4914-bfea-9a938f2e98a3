/*
日期：2019-
作者：
功能：
*/
var objdata = {
    filepath: "userfiles/xj_expert",
};

require.config({
    paths: {
        jquery: '../../sys/jquery',
        system: '../../sys/system',
        function: '../../sys/function',
        layuicommon: "../../sys/layuicommon",
        layui: "../../layui-btkj/layui"
    },
    shim: {
        "system": {
            deps: ["jquery", "layui"]
        },
        "layuicommon": {
            deps: ["jquery", "layui"]
        }
    },
    waitSeconds: 0
});

require(["jquery", "system", 'layui', "layuicommon"], function () {
    layui.use(['form', "upload"], function () {
        var form = layui.form;
        form.verify({
            cheksign_img: function (value) {
                if (!value) {
                    return '请上传专家头像';
                }
            }
        });
        $('#form').on('blur', '[lay-verify]', function () {
            layformblur($(this), form);
        });
        $("#btnok").click(function (event, callback) {
            form.submit('formOk', function () {
                saveEvent(callback);
                return false;
            });
            return false;
        });
        $("#btnselimg").click(function () {
            jQuery.getparent().layer.open({
                type: 2,
                title: '选择图片',
                shadeClose: false,
                area: ['1100px', '90%'],
                content: 'plugin/cropper/uploadoss.html?v=' + (Arg("v") || 1) + "&Ratio=" + (142 / 142),
                success: function (layero, index) {
                },
                btn: ["确定", "取消"],
                yes: function (index, layero) { //或者使用btn1
                    var w = layero.find('iframe')[0].contentWindow;
                    var option = {
                        region: "oss-cn-beijing",
                        bucket: bucketName,
                        path: objdata.filepath
                    };
                    w.uploadFile(option, function (file) {
                        jQuery.getparent().layer.close(index);
                        $('#img_picurl').attr('src', ossPrefix + file.key + '?x-oss-process=image/resize,m_lfit,w_142,h_142');
                        $("#txtimgurl").val(file.key);
                    });
                }
            });
        });
        if (Arg("type") == "edit") {
            getEditInfo();
        }
        form.render();
    });
});

function getEditInfo() {
    $.sm(function (re, err, obj) {
        if (obj) {
            $("#txtname").val(obj.name);
            $("#txtprofessional").val(obj.professional);
            $("#txtworkunit").val(obj.workunit);
            $("#txtgrade").val(obj.grade);
            $("#txtgoodat").val(obj.goodat);
            $("input[name=radtype][value=" + obj.type + "]").attr("checked", "checked");
            if (obj.img) {
                $('#img_picurl').attr('src', ossPrefix + obj.img);
                $("#txtimgurl").val(obj.img);
            }
            layui.form.render();
        } else {
            jQuery.getparent().layer.msg("读取信息出错");
        }
    }, ["xjexpert.detail", $.msgwhere({ id: [Arg("id")] })]);
}

/**
 * 添加 
 */
function saveEvent(callback, release) {
    jQuery.getparent().layer.load();
    var objpostdata = {
        name: $("#txtname").val(),
        type: $("input[name=radtype]:checked").val(),
        img: $("#txtimgurl").val(),
        professional: $("#txtprofessional").val(),
        workunit: $("#txtworkunit").val(),
        grade: $("#txtgrade").val(),
        goodat: $("#txtgoodat").val()
    };
    var pm = ["xjexpert.add", JSON.stringify(objpostdata)];
    if (Arg("id")) {
        var pm = ["xjexpert.update", JSON.stringify(objpostdata), $.msgwhere({ id: [Arg("id")] })];
    }
    $.sm(function (re, err) {
        jQuery.getparent().layer.closeAll('loading');
        if (re && !err) {
            jQuery.getparent().layer.msg("保存成功！", {icon: 1});
            callback && callback();
        } else {
            jQuery.getparent().layer.msg("操作失败！");
        }
    }, pm);
}