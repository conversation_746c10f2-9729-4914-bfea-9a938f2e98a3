package com.btkj.fuyou.wrules.check;

import com.whoami.db.DBUtil;
import com.whoami.util.StringUtil;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * 标签关联管理器
 * 负责管理体检记录与标签的关联关系
 * 完全兼容现有项目的数据库操作模式
 */
public class TagRelationManager {
    private static Logger log = org.apache.logging.log4j.LogManager.getLogger(TagRelationManager.class);
    
    /**
     * 保存标签关联关系
     * @param cp 数据库连接参数
     * @param checkRecordId 体检记录ID
     * @param matchResults 匹配结果列表
     * @return 操作结果
     */
    public static JSONObject saveTagRelations(String[] cp, String checkRecordId, List<JSONObject> matchResults) {
        JSONObject objre = new JSONObject();
        
        if (StringUtil.isNull(checkRecordId) || matchResults == null || matchResults.isEmpty()) {
            objre.put("error", "参数无效");
            return objre;
        }
        
        try {
            // 先删除旧的关联关系
            String whereClause = "result_id = " + checkRecordId;
            JSONObject deleteResult = DBUtil.update(cp[0], cp[1], "check_child_result_label", "isdel=1",whereClause);
            
            // 插入新的关联关系
            int insertCount = 0;
            for (JSONObject matchResult : matchResults) {
                if (matchResult.optBoolean("is_matched", false)) {
                    String columns = "result_id,label_id,label_name,match_reason,isdel";
                    String values = "" + checkRecordId + "," +matchResult.optString("id") + ",'" +matchResult.optString("labelname") + "','" +matchResult.optString("match_reason")+ "'"+",0";

                    JSONObject insertResult = DBUtil.insert(cp[0], cp[1], "check_child_result_label", columns, values, whereClause + " and label_id=" + matchResult.optString("id"));
                    if (insertResult.has("re")) {
                        insertCount++;
                    }
                }
            }
            
            objre.put("re", insertCount);
            log.info("保存标签关联关系成功，记录ID: " + checkRecordId + "，关联标签数: " + insertCount);
            
        } catch (Exception e) {
            log.error("保存标签关联关系异常", e);
            objre.put("error", "保存标签关联关系异常: " + e.getMessage());
        }
        
        return objre;
    }
    
    /**
     * 删除指定记录的标签关联关系
     * @param cp 数据库连接参数
     * @param checkRecordId 体检记录ID
     * @return 操作结果
     */
    public static JSONObject removeTagRelations(String[] cp, String checkRecordId) {
        JSONObject objre = new JSONObject();
        
        if (StringUtil.isNull(checkRecordId)) {
            objre.put("error", "体检记录ID不能为空");
            return objre;
        }
        
        String sql = "DELETE FROM check_child_result_label WHERE result_id = '" + checkRecordId + "'";
        JSONObject result = DBUtil.delete(cp[0], cp[1], "check_child_result_label", "result_id = '" + checkRecordId + "'");
        
        if (result.has("re")) {
            objre.put("re", result.optInt("re"));
            log.info("删除标签关联关系成功，记录ID: " + checkRecordId);
        } else {
            objre.put("error", result.optString("msg", "删除标签关联关系失败"));
        }
        
        return objre;
    }
    
    /**
     * 查询指定记录的标签关联关系
     * @param cp 数据库连接参数
     * @param checkRecordId 体检记录ID
     * @return 标签关联列表
     */
    public static List<JSONObject> getTagRelations(String[] cp, String checkRecordId) {
        List<JSONObject> relations = new ArrayList<>();
        
        if (StringUtil.isNull(checkRecordId)) {
            return relations;
        }
        
        try {
            String sql = "SELECT id, result_id, label_id, label_name, " +
                       "match_reason FROM check_child_result_label WHERE result_id = '" +
                       checkRecordId + "' ORDER BY id DESC";
            
            JSONObject result = DBUtil.select(cp[0], cp[1], sql);
            if (result.has("re")) {
                List<JSONObject> rows = (List<JSONObject>) result.get("re");
                for (JSONObject row : rows) {
                    JSONObject relation = new JSONObject();
                    relation.put("id", row.optLong("id"));
                    relation.put("result_id", row.optString("result_id"));
                    relation.put("label_id", row.optString("label_id"));
                    relation.put("label_name", row.optString("label_name"));
                    relation.put("match_reason", row.optString("match_reason"));
                    relations.add(relation);
                }
            }
            
        } catch (Exception e) {
            log.error("查询标签关联关系异常", e);
        }
        
        return relations;
    }
    
    /**
     * 根据标签编码查询关联的体检记录
     * @param cp 数据库连接参数
     * @param tagCode 标签编码
     * @return 体检记录ID列表
     */
    public static List<String> getCheckRecordsByTag(String[] cp, String tagCode) {
        List<String> recordIds = new ArrayList<>();
        
        if (StringUtil.isNull(tagCode)) {
            return recordIds;
        }
        
        try {
            String sql = "SELECT DISTINCT result_id FROM check_child_result_label WHERE label_id = '" + tagCode + "'";
            
            JSONObject result = DBUtil.select(cp[0], cp[1], sql);
            if (result.has("re")) {
                List<JSONObject> rows = (List<JSONObject>) result.get("re");
                for (JSONObject row : rows) {
                    recordIds.add(row.optString("result_id"));
                }
            }
            
        } catch (Exception e) {
            log.error("根据标签查询体检记录异常", e);
        }
        
        return recordIds;
    }
}