﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>固定排班列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" type="text/css" href="../../css/reset.css"/>
    <link rel="stylesheet" type="text/css" href="../../layui-btkj/css/layui.css"/>
    <link rel="stylesheet" type="text/css" href="../../css/icon.css"/>
    <link rel="stylesheet" type="text/css" href="../../styles/tbstyles.css"/>
    <link rel="stylesheet" type="text/css" href="../../css/commonstyle-layui-btkj.css"/>
    <link rel="stylesheet" href="../../css/medical.css"/>
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.7.1/jquery.min.js"></script>

    <script src="../../layui-btkj/layui.js"></script>
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + top.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <style type="text/css">
        html, body {
            background: #EAEFF3;
            overflow: hidden;
        }

        .layui-form-item {
            display: inline-block;
            vertical-align: top;
            margin: 5px 5px 0px 0;
        }

        .layui-form-label {
            line-height: 28px;
        }
    </style>
</head>
<body>
<div class="">
    <div class="content-medical">
        <div class="layui-form layui-comselect" style="padding:10px;">
            <div class="layui-form-item" style="">
                <label class="layui-form-label">科室：</label>
                <div class="layui-input-inline">
                    <select id="dept" lay-search lay-filter="selectDept"></select>
                </div>
            </div>
            <div class="layui-form-item" style="">
                <label class="layui-form-label">门诊：</label>
                <div class="layui-input-inline">
                    <select id="mzId" lay-search lay-filter="selectMzId"></select>
                </div>
            </div>
            <div class="layui-form-item layarea">
                <label class="layui-form-label">医生：</label>
                <div class="layui-input-inline" style="float: left;width:150px;">
                    <select id="doctor" lay-search lay-filter="selectDoc"></select>
                </div>
            </div>
            <div class="layui-form-item">
                <button class="layui-btn form-search" style="margin-left: 5px;vertical-align: top;" id="btnsearch">查询</button>
            </div>
            <div class="layui-form-item">
                <button class="layui-btn form-search" style="margin-left: 5px;vertical-align: top;" id="batchsave">批量保存</button>
            </div>
        </div>
    </div>
    <div id="content" style="width: 100%;">
        <div class="marmain-cen">
            <div class="content-medical" id="divtable">
                <table id="laytable" lay-filter="laytable"></table>
            </div>
        </div>
    </div>

    <script type="text/html" id="mondayamCheckBox">
        <input type="checkbox" name="mondayam" value="{{d.mondayam}}" lay-filter="lockDemo" {{ d.mondayam == 1 ? 'checked' : '' }}>
    </script>
    <script type="text/html" id="mondaypmCheckBox">
        <input type="checkbox" name="mondaypm" value="{{d.mondaypm}}" lay-filter="lockDemo" {{ d.mondaypm == 1 ? 'checked' : '' }}>
    </script>
    <script type="text/html" id="tuesdayamCheckBox">
        <input type="checkbox" name="tuesdayam" value="{{d.tuesdayam}}" lay-filter="lockDemo" {{ d.tuesdayam == 1 ? 'checked' : '' }}>
    </script>
    <script type="text/html" id="tuesdaypmCheckBox">
        <input type="checkbox" name="tuesdaypm" value="{{d.tuesdaypm}}" lay-filter="lockDemo" {{ d.tuesdaypm == 1 ? 'checked' : '' }}>
    </script>
    <script type="text/html" id="wednesdayamCheckBox">
        <input type="checkbox" name="wednesdayam" value="{{d.wednesdayam}}" lay-filter="lockDemo" {{ d.wednesdayam == 1 ? 'checked' : '' }}>
    </script>
    <script type="text/html" id="wednesdaypmCheckBox">
        <input type="checkbox" name="wednesdaypm" value="{{d.wednesdaypm}}" lay-filter="lockDemo" {{ d.wednesdaypm == 1 ? 'checked' : '' }}>
    </script>
    <script type="text/html" id="thursdayamCheckBox">
        <input type="checkbox" name="thursdayam" value="{{d.thursdayam}}" lay-filter="lockDemo" {{ d.thursdayam == 1 ? 'checked' : '' }}>
    </script>
    <script type="text/html" id="thursdaypmCheckBox">
        <input type="checkbox" name="thursdaypm" value="{{d.thursdaypm}}" lay-filter="lockDemo" {{ d.thursdaypm == 1 ? 'checked' : '' }}>
    </script>
    <script type="text/html" id="fridayamCheckBox">
        <input type="checkbox" name="fridayam" value="{{d.fridayam}}" lay-filter="lockDemo" {{ d.fridayam == 1 ? 'checked' : '' }}>
    </script>
    <script type="text/html" id="fridaypmCheckBox">
        <input type="checkbox" name="fridaypm" value="{{d.fridaypm}}" lay-filter="lockDemo" {{ d.fridaypm == 1 ? 'checked' : '' }}>
    </script>
    <script type="text/html" id="saturdayamCheckBox">
        <input type="checkbox" name="saturdayam" value="{{d.saturdayam}}" lay-filter="lockDemo" {{ d.saturdayam == 1 ? 'checked' : '' }}>
    </script>
    <script type="text/html" id="saturdaypmCheckBox">
        <input type="checkbox" name="saturdaypm" value="{{d.saturdaypm}}" lay-filter="lockDemo" {{ d.saturdaypm == 1 ? 'checked' : '' }}>
    </script>
    <script type="text/html" id="sundayamCheckBox">
        <input type="checkbox" name="sundayam" value="{{d.sundayam}}" lay-filter="lockDemo" {{ d.sundayam == 1 ? 'checked' : '' }}>
    </script>
    <script type="text/html" id="sundaypmCheckBox">
        <input type="checkbox" name="sundaypm" value="{{d.sundaypm}}" lay-filter="lockDemo" {{ d.sundaypm == 1 ? 'checked' : '' }}>
    </script>
    <script type="text/html" id="barDemo">
        <a class="blue-txt layui-btn-xs" lay-event="save">保存</a>
    </script>
</div>

<script data-main="../../js/workingschedule/list" src='../../sys/require.min.js'></script>
</body>
</html>