<!DOCTYPE html>
<html>
<head>
<title>表1-3 托育机构出勤登记表</title>
<meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
<link rel="stylesheet" href="../css/reset.css"/>
<link rel="stylesheet" href="../css/style.css"/>
<link rel="stylesheet" href="../css/icon.css">
<script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
<style>
body {
	background: #fff;
}
</style>
</head>
<body>
<div style="width: 90%;margin: 0 auto;">
<div class="rep-table tab_parent_sec" style="width: 90%;margin: 0 auto;">
	<div class="divhead" title="表1-3 托育机构出勤登记表" style="text-align: center; font-size: 16pt; font-weight: bold; font-family: 宋体, 微软雅黑, 黑体; margin: 15px auto 20px; width: 100%;">表1-3 托育机构出勤登记表</div>

	<div style="height:25px;line-height: 25px;">
		<div class="divprintreport" style="font-size:10pt;font-weight:bold;text-align: left;display:inline;"> 
			<span class="lbselectdate0">班级： </span> <span style="float:right;">     年     
			<label style="display:inline-block; margin-left: 50px"> 月</label>
			</span> 
		</div>
	</div>
	<table class="reporttable" style="clear: both; margin: 0px auto; font-size: 11pt; font-family: 宋体; border: 2px solid black; width: 100%;" cellspacing="0" cellpadding="1" border="0">
	
		<tbody>
        		<tr>
				<td rowspan="2" align="center">姓名</td>
				<td colspan="31" style=" border-left:1px solid Black; text-align:center;">日期</td>
				<td rowspan="2" style=" border-left:1px solid Black;" align="center">备注</td>
			
			
		
			</tr>
			<tr style="line-height:20px;height:20px;">
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">1</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">2</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">3</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">4</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">5</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">6</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">7</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">8</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">9</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">10</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">11</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">12</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">13</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">14</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">15</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">16</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">17</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">18</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">19</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">20</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">21</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">22</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">23</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">24</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">25</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">26</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">27</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">28</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">29</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">30</td>
				<td style=" border-left:1px solid Black;border-top:1px solid Black;" align="center">31</td>
			</tr>
            
            
		</tbody>
	</table>

<div style="font-size:10pt;font-weight:bold;text-align: left;display:inline;">备注：<br>
“√”代表出勤，“〇”代表缺勤； 2.缺勤儿童查明原因后在“〇”内补全相应的符号：“×”代表病假，“— ”代表事假；3.因病缺勤，需在备注栏注明疾病名称；4.此表由保育人员填写。</div>

	</div>
	</div>
	<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../system/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../system/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../plugin/nicescroll/jquery.nicescroll.min.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../plugin/js/moment.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../plugin/viewer/viewer.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../js/order/orderdetail.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>
