<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>营养素设置</title>
    <link rel="stylesheet" href="../css/reset.css"/>
    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../css/style.css">
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <!--[if lt IE 9]>
    <script src="../sys/html5shiv.min.js"></script>
    <![endif]-->
    <style>
    </style>
</head>
<body>
<div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
    <ul id="title" class="layui-tab-title">
        <li id="one" class="layui-this">统计项设置</li>
        <li id="two">适中范围值设置</li>
    </ul>
    <div class="layui-tab-content"></div>
</div>
<div id="tongji" style="border-bottom: 1px solid #E3E3E3;padding-bottom: 20px;margin: 10px 20px 0 20px;">
    <p style="font-size: 13px;color: red;margin-left: 105px;margin-bottom: 25px;">*最多设置五项*</p>
    <ul class="data-num-set tongjidata">
        <li>
            <span class="num-set-left"><img src="../images/data_icon.png">分数：</span>
            <span class="num-set-right"><div id="is_score" class="layui-unselect layui-form-switch layui-form-onswitch"
                                             lay-skin="_switch"><i></i></div><label>统计</label></span>
        </li>
        <li>
            <span class="num-set-left"><img src="../images/energy.png">能量：</span>
            <span class="num-set-right"><div id="is_cal" class="layui-unselect layui-form-switch "
                                             lay-skin="_switch"><i></i></div><label>统计</label></span>
        </li>
        <li>
            <span class="num-set-left"><img src="../images/danbaizhi.png">蛋白质：</span>
            <span class="num-set-right"><div id="is_pro" class="layui-unselect layui-form-switch "
                                             lay-skin="_switch"><i></i></div><label>统计</label></span>
        </li>
        <li>
            <span class="num-set-left"><img src="../images/VA.png">维生素A：</span>
            <span class="num-set-right"><div id="is_va" class="layui-unselect layui-form-switch "
                                             lay-skin="_switch"><i></i></div><label>统计</label></span>
        </li>
        <li>
            <span class="num-set-left"><img src="../images/VB1.png">维生素B1：</span>
            <span class="num-set-right"><div id="is_vb1" class="layui-unselect layui-form-switch "
                                             lay-skin="_switch"><i></i></div><label>统计</label></span>
        </li>
        <li>
            <span class="num-set-left"><img src="../images/V1.png">维生素B2：</span>
            <span class="num-set-right"><div id="is_vb2" class="layui-unselect layui-form-switch "
                                             lay-skin="_switch"><i></i></div><label>统计</label></span>
        </li>
        <li>
            <span class="num-set-left"><img src="../images/VC.png">维生素C：</span>
            <span class="num-set-right"><div id="is_vc" class="layui-unselect layui-form-switch "
                                             lay-skin="_switch"><i></i></div><label>统计</label></span>
        </li>
        <li>
            <span class="num-set-left"><img src="../images/Ca.png">钙：</span>
            <span class="num-set-right"><div id="is_ca" class="layui-unselect layui-form-switch "
                                             lay-skin="_switch"><i></i></div><label>统计</label></span>
        </li>
        <li>
            <span class="num-set-left"><img src="../images/Zn.png">锌：</span>
            <span class="num-set-right"><div id="is_zn" class="layui-unselect layui-form-switch "
                                             lay-skin="_switch"><i></i></div><label>统计</label></span>
        </li>
        <li>
            <span class="num-set-left"><img src="../images/Fe.png">铁：</span>
            <span class="num-set-right"><div id="is_fe" class="layui-unselect layui-form-switch "
                                             lay-skin="_switch"><i></i></div><label>统计</label></span>
        </li>
        <li>
            <span class="num-set-left"><img src="../images/Na.png">钠：</span>
            <span class="num-set-right"><div id="is_na2" class="layui-unselect layui-form-switch "
                                             lay-skin="_switch"><i></i></div><label>统计</label></span>
        </li>
        <!--<li>-->
        <!--<span class="num-set-left"><img src="../images/V1.png">VC：</span>-->
        <!--<span class="num-set-right"><div class="layui-unselect layui-form-switch " lay-skin="_switch"><i></i></div><label>统计</label></span>-->
        <!--</li>-->
    </ul>
</div>
<div id="fanwei" style="border-bottom: 1px solid #E3E3E3;padding-bottom: 20px;margin: 10px 20px 0 20px;display: none">
    <ul class="data-num-set data-num-set2">
        <li>
            <span class="num-set-left"><img src="../images/energy.png">能量：</span>
            <span class="num-set-right">
						<label><input id="per_cal" type="text" placeholder="80"></label> - <label><input id="per_cal_2" type="text"
                                                                                                  placeholder="120"></label>% <i
                    class="layui-color-green">(建议范围值：80-120)</i>
            </span>
        </li>
        <li>
            <span class="num-set-left"><img src="../images/danbaizhi.png">蛋白质：</span>
            <span class="num-set-right">
						<label><input id="per_pro" type="text" placeholder="80"></label> - <label><input id="per_pro_2" type="text"
                                                                                            placeholder="120"></label>% <i
                    class="layui-color-green">(建议范围值：80-120)</i>
					</span></li>
        <li>
            <span class="num-set-left"><img src="../images/VA.png">维生素A：</span>
            <span class="num-set-right">
						<label><input id="per_va" type="text" placeholder="80"></label> - <label><input id="per_va_2" type="text"
                                                                                            placeholder="120"></label>% <i
                    class="layui-color-green">(建议范围值：80-120)</i>
					</span></li>
        <li>
            <span class="num-set-left"><img src="../images/VB1.png">维生素B1：</span>
            <span class="num-set-right">
						<label><input id="per_vb1" type="text" placeholder="80"></label> - <label><input id="per_vb1_2" type="text"
                                                                                            placeholder="120"></label>% <i
                    class="layui-color-green">(建议范围值：80-120)</i>
					</span></li>
        <li>
            <span class="num-set-left"><img src="../images/V1.png">维生素B2：</span>
            <span class="num-set-right">
						<label><input id="per_vb2" type="text" placeholder="80"></label> - <label><input id="per_vb2_2" type="text"
                                                                                            placeholder="120"></label>% <i
                    class="layui-color-green">(建议范围值：80-120)</i>
					</span></li>
        <li>
            <span class="num-set-left"><img src="../images/VC.png">维生素C：</span>
            <span class="num-set-right">
						<label><input id="per_vc" type="text" placeholder="80"></label> - <label><input id="per_vc_2" type="text"
                                                                                            placeholder="120"></label>% <i
                    class="layui-color-green">(建议范围值：80-120)</i>
					</span></li>
        <li>
            <span class="num-set-left"><img src="../images/Ca.png">钙：</span>
            <span class="num-set-right">
						<label><input id="per_ca" type="text" placeholder="80"></label> - <label><input id="per_ca_2" type="text"
                                                                                            placeholder="120"></label>% <i
                    class="layui-color-green">(建议范围值：80-120)</i>
					</span></li>
        <li>
            <span class="num-set-left"><img src="../images/Zn.png">锌：</span>
            <span class="num-set-right">
						<label><input id="per_zn" type="text" placeholder="80"></label> - <label><input id="per_zn_2" type="text"
                                                                                            placeholder="120"></label>% <i
                    class="layui-color-green">(建议范围值：80-120)</i>
					</span></li>
        <li>
            <span class="num-set-left"><img src="../images/Fe.png">铁：</span>
            <span class="num-set-right">
						<label><input id="per_fe" type="text" placeholder="80"></label> - <label><input id="per_fe_2" type="text"
                                                                                            placeholder="120"></label>% <i
                    class="layui-color-green">(建议范围值：80-120)</i>
					</span></li>
        <li>
            <span class="num-set-left"><img src="../images/Na.png">钠：</span>
            <span class="num-set-right">
						<label><input id="per_na2" type="text" placeholder="80"></label> - <label><input id="per_na2_2" type="text"
                                                                                            placeholder="120"></label>% <i
                    class="layui-color-green">(建议范围值：80-120)</i>
					</span></li>

    </ul>
</div>
<!--<div class="pay-btn">-->
<!--<a class="opr-btn">保存</a>-->
<!--<a class="opr-btn" style="background: none;border: 1px solid #E4E4E4;color: #3B3B3B;">关闭</a>-->
<!--</div>-->
<script type="text/javascript" src="../layui-btkj/layui.js"></script>
<script type="text/javascript" src="../sys/jquery.js"></script>
<script type="text/javascript" src="../js/show_set.js"></script>
</body>
</html>
