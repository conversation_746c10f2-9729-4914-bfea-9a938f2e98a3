﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>心理专案管理卡</title>
    <link rel="shortcut icon" href="/images/favicon.ico" type="image/x-icon"/>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="Copyright" content="Copyright 2016 by tongbang"/>
    <meta name="Author" content=""/>
    <meta name="Robots" content="All"/>
    <link href="../../css/reset.css" type="text/css" rel="stylesheet"/>
    <link href="../../styles/tbstyles.css" type="text/css" rel="stylesheet"/>
    <link href="../../plugin/ehaiform/css/tbbaseform.css" rel="stylesheet" type="text/css"/>
    <link href="../../plugin/editselect/css/editselect.css" rel="stylesheet" type="text/css"/>
    <style>
        input[type="radio"] {
            vertical-align: top;
            margin-top: 3px;
        }
        .tjzagl_textarea{
            text-align: left;
            height: 70px;
            border: 1px solid #eee;
        }
    </style>
</head>
<body>
<div class="marmain" style="">
    <table id="tabcard0" border="0" cellpadding="0" cellspacing="0" style="border:  #d8d8d8 1px solid;width:100%;">
        <tr>
            <td style="width: 30%;">
                <table border="0" cellpadding="10" cellspacing="0" style="text-align: left; width: 100%;">
                    <tr>
                        <td style="width: 10%; border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            <label>姓名：<label id="txtname"></label></label>
                            <label style="padding-left:100px;">性别：<label id="txtsex"></label></label>
                            <label style="padding-left:100px;">出生日期：<label id="txtbirthday"></label></label>
                            <label style="padding-left:100px;">开始管理日期：<input id="txtmanagetime" maxlength="10" type="text" style="width: 120px;" class="Wdate"/></label>
                        </td>
                    </tr>
                    <tr>
                        <td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            <label>诊断名称：<input id="txtzdname" maxlength="30" type="text" style="width: 200px;" class="tjzagl_input"/></label>
                            <label style="padding-left:100px;">诊断日期：<input id="txtzdtime" maxlength="10" type="text" style="width: 120px;" class="Wdate"/></label>
                            <label style="padding-left:100px;">家族史：<input id="txtfamhistory" maxlength="50" type="text" style="width: 250px;" class="tjzagl_input"/></label>
                        </td>
                    </tr>
                    <tr>
                        <td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            <div>
                                备注：<br />
                                <textarea id="txtremark" cols=" " rows=" " style="width: 98%; height:100px;border:1px solid #ccc;" require="false"
                                          datatype="LimitEn" msg="输入长度不能大于400！" max="400"></textarea>
                            </div>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td colspan="2">
                <table border="0" cellpadding="10" cellspacing="0" style="text-align: center; width: 100%;
                        margin-top: 0px; border-top:  #d8d8d8 1px solid;">
                    <thead style="background: #1da89e;color:white;">
                    <tr>
                        <td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            删除
                        </td>
                        <td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            检查日期
                        </td>
                        <td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            年龄<br/>(岁)
                        </td>
                        <td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            筛查方法
                        </td>
                        <td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            筛查结果
                        </td>
                        <td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            医疗机构诊疗意见
                        </td>
                        <td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            管理措施
                        </td>
                        <td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            管理成效评估
                        </td>
                        <td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            签名
                        </td>
                    </tr>
                    </thead>
                    <tbody id="divtbody">
                    </tbody>
                </table>
            </td>
        </tr>
        <tr>
            <td colspan="2" style="padding: 8px 0 10px 0;">
                <p style="line-height: 20px;">
                    <input type="checkbox" id="chkvest"/>&nbsp;&nbsp;<label for="chkvest">转归：</label>
                    <label id="lbvest" style="display:none;">
                        <input id="txtvest0" name="radvest" type="radio" value="0"/><label for="txtvest0">痊愈</label>
                        <input id="txtvest1" name="radvest" type="radio" value="1"/><label for="txtvest1">好转</label>
                        <input id="txtvest2" name="radvest" type="radio" value="2"/><label for="txtvest2">未愈</label>
                        <input id="txtvest3" name="radvest" type="radio" value="3"/><label for="txtvest3">失访</label>
                        <input id="txtvest4" name="radvest" type="radio" value="4"/><label for="txtvest4">离园</label>
                    </label>
                    <label style="margin-left: 20px">结案日期：<input id="txtendtime" class="Wdate" type="text" style="width: 100px;"/></label>
                </p>
            </td>
        </tr>
    </table>
    <div style="color:red;padding:5px;">
        填表说明：
        <p>1.随访要求：每季度至少 1 次，可根据监测手段和实际情况决定发育监测密度。</p>
        <p>2.结案要求：根据治疗方案和治疗情况。</p>
    </div>
    <div style="margin: 10px 0 10px 0;display:none;">
        <input type="button" id="btnsave" class="btn" value="保存"/>
        <input id="txtoldfat" type="hidden"/>
        <input id="txtvestfat" type="hidden"/>
    </div>
</div>
<script data-main="../../js/zhuanan/psychologycard" src='../../sys/require.min.js'></script>
</body>
</html>
