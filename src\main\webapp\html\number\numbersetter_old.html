<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <link rel="shortcut icon" href="/images/favicon.ico" type="image/x-icon"/>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="Copyright" content="Copyright 2016 by tongbang"/>
    <meta name="Author" content=""/>
    <meta name="Robots" content="All"/>
    <title>号源设置</title>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css"/>
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css"/>
    <link rel="stylesheet" href="../../css/icon.css"/>
    <link rel="stylesheet" href="../../css/reset.css"/>

    <style>
        body {
            background: #F4F5F7;
        }

        .tablestyle_field input.btntop_3 {
            width: auto;
            margin: auto;
            text-align: right;
            margin-right: 20px;
        }

        .tablestyle_field .btntop_3.nograycol {
            background: #4FA99F;
            border: 1px solid #039589;
            border-radius: 0;
        }

        .tablestyle_field .btntop_3 {
            background: #ccc;
            border: 1px solid #ccc;
            border-radius: 0;
        }

        .fieldset_fillet.messdiv td {
            padding: 0
        }

        .tablestyle_field td {
            border-bottom: 1px solid #EAEAEA;
            height: 60px;
        }

        img {
            vertical-align: middle;
        }
    </style>
</head>
<body>
<div class="bodywidth">
    <div class="content" style="margin:0 8px;">
        <fieldset class="fieldset_fillet">
            <table id="setterTable" cellpadding="0" cellspacing="0" border="0" class="tablestyle_field btn_style2"
                   style="width: 100%; line-height: 35px; margin-top: 5px;">
                <tr>
                    <td style="width: 15%; text-align: right;">
                        <em>*</em>支付方式：
                    </td>
                    <td style="width: 40%;">
                        <select name="pay_type" id="pay_type">
                            <option selected>请选择</option>
                            <option value="2">线上支付</option>
                            <option value="1">线下支付</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td style="width: 15%; text-align: right;">
                        <em>*</em>放号设置：
                    </td>
                    <td>
                        <label class="layui-form-label">每日：</label>
                        <div style="min-height: 30px; position: relative;">
                            <input type="time" id="start_time"/>~<input type="time" id="end_time"/>
                            ,可预订当天至第<input style="width: 30px;" id="days" type="number" min="0"/>天(包含)内的号源
                        </div>
                    </td>
                </tr>
                <tr>
                    <td style="width: 15%;text-align: right;">
                        <em>*</em>取号时间：
                    </td>
                    <td>
                        <label class="layui-form-label">上午号截止：</label>
                        <div class="layui-input-block" style="min-height: 30px; position: relative;">
                            <input type="time" id="morning_stop_time"/>
                        </div>
                        <label class="layui-form-label">下午号截止：</label>
                        <div class="layui-input-block" style="min-height: 30px; position: relative;">
                            <input type="time" id="afternoon_stop_time"/>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td style="width: 15%; text-align: right;">
                        <em>*</em>取号地址：
                    </td>
                    <td style="width: 80%;">
                        <input type="text" id="address" style="width: 50%;"/>
                    </td>
                </tr>
                <tr>
                    <td style="width: 15%;text-align: right;">
                        <em>*</em>退号时间：
                    </td>
                    <td>
                        <input id="refund_type1" type="radio" name="refund" value="1" class="radio"/><label
                            for="refund_type1">随时退</label>
                        <input id="refund_type2" type="radio" name="refund" value="2" class="radio"/><label
                            for="refund_type2">就诊前</label>
                        <input style="width: 30px;" id="refund_days" type="number" min="0"/>个工作日
                        <input type="time" id="refund_times"/>前取消
                    </td>
                </tr>
                <!-- 界面弃用 -->
                <tr>
                    <td style="width: 15%;text-align:right;">
                        <em>*</em>挂号限制：
                    </td>
                    <td style="width: 40%;">
                        就诊人每天限挂号<input style="width: 30px;" id="limit_number" type="number" min="0"/>次,每个科室同一就诊单元(上午,下午)限挂号(此界面弃用)<input
                            style="width: 25px;" id="same_dept_limit" value="1" disabled type="number"/>次
                    </td>
                </tr>
                <tr>
                    <td style="width: 15%;text-align:right;">
                        <em>*</em>锁号限制：
                    </td>
                    <td style="width: 40%;">
                        锁号<input style="width: 30px;" id="valid_time" type="number" min="0"/>分钟后未支付,释放号源
                    </td>
                </tr>

            </table>
            <div style="text-align: center">
                <input onclick="save()" id="save" type="button" class="btntop_3" value="保存"/>
            </div>
        </fieldset>
        </fieldset>
    </div>
</div>
<script data-main="../../js/number/numbersetter" src="../../sys/require.min.js"></script>
</body>
</html>
