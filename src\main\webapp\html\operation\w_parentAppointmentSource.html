<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>家长预约来源</title>
    <link rel="stylesheet" type="text/css" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" type="text/css" href="../../css/icon.css"/>
    <link rel="stylesheet" type="text/css" href="../../styles/tbstyles.css"/>
    <link href="../../plugin/flexigrid/css/tbflexigrid.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css"/>
    <script type="text/javascript">
        // 检查 top.objdata 是否存在，如果不存在则设置默认主题
        var curtheme = (top && top.objdata && top.objdata.curtheme) ? top.objdata.curtheme : 'backstage';
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + curtheme + '.css" type="text/css" media="screen"/>');
    </script>

    <style type="text/css">
        html, body {
            background: #EAEFF3;
            overflow: hidden;
        }
        .layui-form-item {
            display: inline-block;
            vertical-align: top;
            margin: 5px 5px 0px 0;
        }

        .layui-form-label {
            line-height: 28px;
        }
        
        /* 添加可点击单元格的样式 */
        .layui-table-link {
            color: #1890ff;
            cursor: pointer;
            text-decoration: none;
        }
        
        .layui-table-link:hover {
            color: #40a9ff;
            text-decoration: underline;
        }

        /* 添加分页栏按钮样式 */
        .layui-table-page {
            display: flex !important;
            align-items: center;
            padding: 0 10px;
            position: relative;
        }

        #btnclose {
            background-color: #FF5722 !important;
            color: #fff !important;
            position: absolute !important;
            left: 50% !important;
            transform: translateX(-50%) !important;
            top: 50% !important;
            margin-top: -14px !important;
            z-index: 1 !important;
            height: 28px !important;
            line-height: 28px !important;
            padding: 0 15px !important;
            font-size: 12px !important;
            border-radius: 2px !important;
        }
    </style>
</head>
<body>
<div class="marmain">
    <div class="content-medical">
        <div class="layui-form layui-comselect" style="padding:10px;">
            <!-- 第一行 -->
            <div class="layui-form-item" style="margin-bottom: 10px;">
                <div class="layui-input-inline" style="width:120px; margin-right: 5px;">
                    <input id="datestart" type="text" autocomplete="off" placeholder="开始日期" class="layui-input">
                </div>
                <div class="layui-form-mid">至</div>
                <div class="layui-input-inline" style="width:120px; margin-right: 20px;">
                    <input id="dateend" type="text" autocomplete="off" placeholder="结束日期" class="layui-input">
                </div>
                <label class="layui-form-label" style="width: 80px;">预约来源：</label>
                <div class="layui-input-inline" style="width:120px; margin-right: 15px;">
                    <select id="appointsource" lay-filter="appointsource">
                        <option value="">请选择</option>
                        <option value="幼儿入园体检报告">幼儿入园体检报告</option>
                        <option value="幼儿定期体检报告">幼儿定期体检报告</option>
                        <option value="健康宣教">健康宣教</option>
                        <option value="AI健康问">AI健康问</option>
                        <option value="AI外呼">AI外呼</option>
                        <option value="主动预约">主动预约</option>
                        <option value="便民服务牌">便民服务牌</option>
                        <option value="专家讲座">专家讲座</option>
                        <option value="专家坐诊">专家坐诊</option>
                        <option value="专家智能提醒">专家智能提醒</option>
                        <option value="医院推送通知">医院推送通知</option>
                        <option value="家长分享">家长分享</option>
                    </select>
                </div>
                <label class="layui-form-label" style="width: 90px;">就诊人性别：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="patientsex" lay-filter="patientsex">
                        <option value="">请选择</option>
                        <option value="男">男</option>
                        <option value="女">女</option>
                    </select>
                </div>
                <label class="layui-form-label" style="width: 90px;">就诊人年龄：</label>
                <div class="layui-input-inline" style="width:100px; margin-right: 15px;">
                    <select id="patientage" lay-filter="patientage">
                        <option value="">请选择</option>
                        <option value="0-2岁">0-2岁</option>
                        <option value="3-6岁">3-6岁</option>
                        <option value="7-18岁">7-18岁</option>
                        <option value="19-30岁">19-30岁</option>
                        <option value="31-40岁">31-40岁</option>
                        <option value="41-50岁">41-50岁</option>
                        <option value="51-60岁">51-60岁</option>
                        <option value="60+岁">60+岁</option>
                    </select>
                </div>
                <label class="layui-form-label" style="width: 90px;">就诊人距离：</label>
                <div class="layui-input-inline" style="width:100px;">
                    <select id="patientdistance" lay-filter="patientdistance">
                        <option value="">请选择</option>
                        <option value="1km">1km</option>
                        <option value="1-3km">1-3km</option>
                        <option value="3-5km">3-5km</option>
                        <option value="5-10km">5-10km</option>
                        <option value="10km+">10km+</option>
                    </select>
                </div>
            </div>
            
            <!-- 第二行 -->
            <div class="layui-form-item" style="margin-bottom: 15px;">
                <label class="layui-form-label" style="width: 60px;">关键字：</label>
                <div class="layui-input-inline" style="width:200px; margin-right: 20px;">
                    <input id="txtkeyword" type="text" autocomplete="off" placeholder="请输入幼儿姓名或家长姓名" class="layui-input">
                </div>
                <button class="layui-btn form-search" style="margin-left: 10px;" id="btnsearch">查询</button>
            </div>
        </div>
    </div>
    <div id="content" style="width: 100%;">
        <div class="marmain-cen">
            <div class="content-medical" id="divtable">
                <table id="laytable" lay-filter="laytable"></table>
            </div>
        </div>
    </div>
</div>

<script data-main="../../js/operation/w_parentAppointmentSource" src='../../sys/require.min.js'></script>
</body>
</html> 