<!DOCTYPE html>
<html>
	    <head>
	    <title>查看团购预订</title>
	    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
	    <script type="text/javascript" src="../layui-btkj/layui.js"></script>
	    <script type="text/javascript" src="../sys/jquery.js"></script>
	    <link rel="stylesheet" href="../css/style.css"/>
	    <link rel="stylesheet" href="../css/icon.css"/>
	    <script type="text/javascript">
	        document.write('<link rel="stylesheet" id="linktheme" href="../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
	    </script>
	    <link rel="stylesheet" href="../layui-btkj/css/layui.css">
			<style>
			body {
				background: #fff;
			}
			.incr-chat {
				width: 700px;
				margin: 0px auto;
				padding: 0 40px
			}
			.layui-input-block .layui-input {
				width: 483px;
			}
			</style>
	    </head>
<body>
      <section class="incr-chat" style="display: ;">
              <h3 class="noticeTitle bg1" style="margin-top:10px;"><span class="lanflag">查看团购预订</span></h3>
				  <div class="order-cen" > 
					   <p>企业名称: <font color="#999999">北京童话邦教育科技有限公司</font></p>
                       <p>企业负责人／手机号: <font color="#999999"> 文海星／15300020001</font></p>
					   <p>团购人／团购的时间:  <font color="#999999"> 王海（妇幼）／2018-04-20</font></p>
					   <p>团购单号:<font color="#999999">828348-18-89030980093809</font></p>
                   </div>
                  <div class="order-bot"> 
					  <p>选择套餐：<span class="order-tc">健康基础A套餐</span></p>
					  <p style="margin-top:10px">套餐单价：<span class="order-price">男 原价¥120，<font color="#ff9f23">折后¥96</font>;  女 原价¥150，<font color="#ff9f23">折后¥120</font></span></p>
					  <div class="order-div"><span class="order-peo"><img src="../images/physical/man_ico.png" width="22" height="33" alt=""/>男 1人</span>96*1=96元</div>
					  <div class="order-div"><span class="order-peo"><img src="../images/physical/woman_.png" width="22" height="33" alt=""/>女 5人</span>120*5=600</div>
					  <p class="order-alt">共计<label class="red-txt font30">¥696</label></p>  
		       </div>
		       <div style="text-align: center;margin-top: 50px;"> 
				   <button type="" class="layui-btn layui-btn-primary" style="border:1px solid #333333; padding:0 40px; color:#333333; border-radius:5px;">关闭</button>
		       </div>
        </section>
</body>
</html>
