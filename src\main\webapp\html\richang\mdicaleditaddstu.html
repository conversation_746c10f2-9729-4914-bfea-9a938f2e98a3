﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>选择幼儿</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <link href="../../styles/admin.css" rel="stylesheet" type="text/css" />
    <link href="../../styles/tbstyles.css" type="text/css" rel="stylesheet" />
    <link rel="stylesheet" href="../../css/reset.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../css/style.css"/>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <style type="text/css">
        html{overflow-x: hidden;}
        ul.ulstus
        {
            display: inline-block;
            margin-left: 10px;
            margin-top: 5px;
        }
        ul.ulstus li
        {
            /*width: 80px;*/
            /*height: 30px;*/
            display: inline-block;
            float:left;
            margin-left: 10px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
<div class="content-medical" id="container" style="height: 100%;">
    <div class="tbmargin" style="margin: 0 20px;">
        <div id="divnoticeinfos" style="margin: 30px 30px;width: 560px;" class="layui-input-inline layui-form layui-form-item">
            <img class="clickall" src="../../plugin/simpletree/images/default/folderopen.gif" isnopen="1" alt="" style="width: 24px;margin-right: 10px;"><input type="checkbox" name="txtallsel" lay-filter="chkall" style="margin-top: -3px;margin-right: 5px;vertical-align: middle;" id="txtallsel" title="全选"/>
            <div id="ulselneedinfo"></div>
        </div>
    </div>
</div>
<!--    <div class="content-medical marmain-cen layui-form">-->
<!--        <div class="layui-unselect layui-form layui-select-title layui-form-item">-->
<!--            <div class="layui-input-inline">-->
<!--                <div id="divstulist"></div>-->
<!--&lt;!&ndash;                <p><input type="checkbox" lay-filter="fypgstate" name="CheckboxGroup1" id="zd1" value="1"><label>合理膳食</label></p>&ndash;&gt;-->
<!--&lt;!&ndash;                <p><input type="checkbox" lay-filter="fypgstate" name="CheckboxGroup1" id="zd2" value="2"><label>生长发育</label></p>&ndash;&gt;-->
<!--&lt;!&ndash;                <p><input type="checkbox" lay-filter="fypgstate" name="CheckboxGroup1" id="zd3" value="3"><label>疾病预防</label></p>&ndash;&gt;-->
<!--&lt;!&ndash;                <p><input type="checkbox" lay-filter="fypgstate" name="CheckboxGroup1" id="zd4" value="4"><label>预防伤害</label></p>&ndash;&gt;-->
<!--&lt;!&ndash;                <p><input type="checkbox" lay-filter="fypgstate" name="CheckboxGroup1" id="zd5" value="5"><label>口腔保健</label></p>&ndash;&gt;-->
<!--&lt;!&ndash;                <p><input type="checkbox" lay-filter="fypgstate" name="CheckboxGroup1" id="zd6" value="6"><label>其他</label></p>&ndash;&gt;-->
<!--            </div>-->
<!--        </div>-->
<!--    </div>-->
<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/system.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../js/richang/mdicaleditaddstu.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>
