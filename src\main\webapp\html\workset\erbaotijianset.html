﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <link rel="shortcut icon" href="/images/favicon.ico" type="image/x-icon" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="Copyright" content="Copyright 2016 by tongbang" />
    <meta name="Author" content="" />
    <meta name="Robots" content="All" />
    <title>儿保体检设置</title>
    <link href="../../styles/admin.css" rel="stylesheet" type="text/css" />
    <link href="../../styles/tbstyles.css" type="text/css" rel="stylesheet" />
    <link href="../../plugin/ehaiform/css/tbbaseform.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../../styles/xbcomstyle.css" />
    <style>
        body { background: #F4F5F7; }

        .tablestyle_field input.btntop_3 { width: auto; margin: auto; text-align: right; margin-right: 20px; }
        .tablestyle_field .btntop_3.nograycol { background: #4FA99F; border: 1px solid #039589; border-radius: 0; }
        .tablestyle_field .btntop_3 { background: #ccc; border: 1px solid #ccc; border-radius: 0; }
        .fieldset_fillet.messdiv td { padding: 0 }
        .tablestyle_field td { border-bottom: 1px solid #EAEAEA; height: 60px; }
        .layui-comthis.tjpad::after { width: 60px; height: 41px; }
        .legend_bjcyadd { padding-left: 0; }
        .layui-comthis.messagemt::after { width: 120px; height: 38px; }
    </style>
</head>
<body>
    <div class="bodywidth">
        <div class="content" style="margin:8px;">
            <fieldset class="fieldset_fillet">
                <table cellpadding="0" cellspacing="0" border="0" class="tablestyle_field btn_style2" style="width: 100%; line-height: 35px; margin-top: 5px;">                  
                    <tr>
                        <td style="width: 15%;text-align:right;">
                            体检合格印章：
                        </td>
                        <td style="width: 75%;">
                            <img id="imgtijianOKseal" src="" style="display:none;" />
                            <input type="hidden" id="txttijianOKseal" />
                            <a id="btnsettijianOKseal" class="layui-btn" data-fxindex="2" style="cursor:pointer;margin-left: 10px;">设置</a>
                            <a id="btndelOKseal" class="layui-btn" data-fxindex="2" style="cursor:pointer;margin-left: 10px;color: red;">删除印章</a>
                        </td>
                        <td style="width: 10%; padding-bottom: 5px;text-align: right; ">
                            <input id="btntijianOKseal" type="button" class="btntop_3" value="应用" />
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 15%;text-align:right;">
                            印章设置：
                        </td>
                        <td style="width: 75%;">
                            <img id="imgseal" src="" style="width:90px;display:none;" />
                            <input type="hidden" id="txtseal" name="seal" />
                            <a id="btnsetseal" class="layui-btn" data-fxindex="2" style="cursor:pointer;margin-left: 10px;">设置</a>
                            <a id="btndelseal" class="layui-btn" data-fxindex="2" style="cursor:pointer;margin-left: 10px;color: red;">删除印章</a>
                        </td>
                        <td style="width: 10%; padding-bottom: 5px;text-align: right; ">
                            <input id="btnOKseal" type="button" class="btntop_3" value="应用" />
                        </td>
                    </tr>
                </table>
            </fieldset>
        </div>
    </div>
    <script data-main="../../js/workset/erbaotijianset" src='../../sys/require.min.js'></script>
</body>
</html>
