/*新增日期: 2025.06.26
作 者: 朱辽原
修改时间：
修改人：
內容摘要: 家长扫码预约情况-家长
*/
require.config({
    paths: {
        system: '../../sys/system',
        jquery: '../../sys/jquery',
        layui: '../../layui-btkj/layui',
        dataSource: './enterData',
        commonUtils: './commonUtils'
    },
    shim: {
        system: {
            deps: ['jquery']
        },
        layui: {
            deps: ['jquery', 'system', 'dataSource']
        }
    },
    waitSeconds: 0
})
require(['jquery', 'commonUtils', 'layui', 'system', 'dataSource'], ($, utils) => {
    layui.config().extend({ //设定模块别名
        system: '../../sys/system',
    });
    layui.use(['system', 'form', 'table', 'laydate'], function () {
        layer = layui.layer;
        form = layui.form;
        laydate = layui.laydate;
        const params = Arg.all();
        initDatePicker();
        initSelectData();
        initEvent();
        initTable();

        function initDatePicker() {
            const isoDate = new Date().toISOString();
            const nowDate = isoDate.split('T')[0];
            console.log(params.startDate, params.endDate, nowDate)
            laydate.render({
                elem: '#startDate',
                type: 'date',
                value: params.startDate || nowDate
            });
            laydate.render({
                elem: '#endDate',
                type: 'date',
                value: params.endDate || nowDate
            });
            // 初始化开始时间选择器
            // laydate.render({
            //     elem: '#startTime',
            //     type: 'time',
            //     format: 'HH:mm',
            //     value: params.startTime || '00:00'
            // });
            // 初始化结束时间选择器
            // laydate.render({
            //     elem: '#endTime',
            //     type: 'time',
            //     format: 'HH:mm',
            //     value: params.endTime || '23:59'
            // });
        }

        function initSelectData() {
            // 获取园所名称数据
            $.sm(function (re1, err1) {
                if (re1) {
                    var gardenData = [{id: '', name: '请选择'}];
                    for (let i = 0; i < re1.length; i++) {
                        gardenData.push({id: re1[i].id, name: re1[i].name});
                    }
                    var gardenSelect = $('#gardenName');
                    gardenSelect.empty();
                    gardenData.forEach(item => {
                        gardenSelect.append(`<option value="${item.id}">${item.name}</option>`);
                    });
                    gardenSelect.val(params.kindergartenId || '');
                } else {
                    jQuery.getparent().layer.msg(err1, {icon: 5});
                }
            }, ["garden.list", JSON.stringify({regional: params.regional})], null, null, {async: false});

            // 获取家长孩子关系数据
            $.sm(function (re1, err1) {
                if (re1) {
                    var parentRelationData = [{id: '', name: '请选择'}];
                    for (let i = 0; i < re1.length; i++) {
                        parentRelationData.push({id: re1[i].id, name: re1[i].name});
                    }
                    var parentRelationSelect = $('#parentRelation');
                    parentRelationSelect.empty();
                    parentRelationData.forEach(item => {
                        parentRelationSelect.append(`<option value="${item.id}">${item.name}</option>`);
                    });
                    parentRelationSelect.val(params.id || '');
                } else {
                    jQuery.getparent().layer.msg(err1, {icon: 5});
                }
            }, ["garden.relationship"], null, null, {async: false});

            // 分类数据
            const classifyData = [{id: '', name: '请选择'}, {id: '1', name: '口腔'}, {
                id: '2',
                name: '眼睛'
            }, {id: '3', name: '心理'}];
            const classifySelect = $('#classify');
            classifySelect.empty();
            classifyData.forEach(item => {
                classifySelect.append(`<option value="${item.id}">${item.name}</option>`);
            });
            classifySelect.val(params.classify || '');

            // 班级数据
            const classData = [{id: '', name: '请选择'}, {id: '小一班', name: '小一班'}, {
                id: '小二班',
                name: '小二班'
            }, {id: '中一班', name: '中一班'}];
            const classSelect = $('#className');
            classSelect.empty();
            classData.forEach(item => {
                classSelect.append(`<option value="${item.id}">${item.name}</option>`);
            });
            classSelect.val(params.className || '');

            // 年级数据
            const gradeData = [{id: '', name: '请选择'}, {id: '小班', name: '小班'}, {
                id: '中班',
                name: '中班'
            }, {id: '大班', name: '大班'}];
            const gradeSelect = $('#grade');
            gradeSelect.empty();
            gradeData.forEach(item => {
                gradeSelect.append(`<option value="${item.id}">${item.name}</option>`);
            });
            gradeSelect.val(params.grade || '');

            // 预约挂号状态
            const appointmentSuccessSelect = $('#appointmentSuccess');
            appointmentSuccessSelect.val(params.appointmentSuccess || '');

            // 体检年龄数据
            const physicalAgeData = [{id: '', name: '请选择'}, {id: '1', name: '1岁以下'}, {
                id: '2',
                name: '1-3岁'
            }, {id: '3', name: '3-6岁'}];
            const physicalAgeSelect = $('#physicalAge');
            physicalAgeSelect.empty();
            physicalAgeData.forEach(item => {
                physicalAgeSelect.append(`<option value="${item.id}">${item.name}</option>`);
            });
            physicalAgeSelect.val(params.physicalAge || '');

            // 访问设备数据
            const deviceTypeData = [
                {id: '', name: '请选择'},
                {id: '安卓', name: '安卓'},
                {id: '苹果', name: '苹果'},
                {id: '其他', name: '其他'}];
            const deviceTypeSelect = $('#deviceType');
            deviceTypeSelect.empty();
            deviceTypeData.forEach(item => {
                deviceTypeSelect.append(`<option value="${item.id}">${item.name}</option>`);
            });
            deviceTypeSelect.val(params.deviceType || '');

            // 区域分布数据
            const areaDistributionData = [{id: '', name: '请选择'}, {id: '1', name: '1km以内'}, {
                id: '2',
                name: '1km~3km'
            }, {id: '3', name: '3km~5km'}, {id: '4', name: '≥5km'}];
            const areaDistributionSelect = $('#areaDistribution');
            areaDistributionSelect.empty();
            areaDistributionData.forEach(item => {
                areaDistributionSelect.append(`<option value="${item.id}">${item.name}</option>`);
            });
            areaDistributionSelect.val(params.areaDistribution || '');

            // 家长性别
            // console.log(params.parentGender)
            const parentGenderSelect = $('#parentGender');
            parentGenderSelect.val(params.parentGender || '');
            // 年龄分布
            const parentAgeSelect = $('#parentAge');
            parentAgeSelect.val(params.parentAge || '');
            // 孩子与家长关系
            const parentRelationSelect = $('#parentRelation')
            parentRelationSelect.val(params.parentRelation || '')

            form.render('select');
        }

        function initEvent() {
            $("#btnsearch").click(btnsearch);
            $("#printPreview").click(function () {
                layer.msg('打印预览功能', {icon: 1});
            });
            $("#closeBtn").click(function () {
                parent.layer.closeAll();
            });

            // 添加家长关系选择事件，自动设置对应性别
            $("#parentRelation").change(function () {
                const selectedRelation = $(this).val();
                if (selectedRelation) {
                    // 获取关系对应的性别信息
                    $.sm(function (re1, err1) {
                        if (re1) {
                            const relationData = re1.find(item => item.id === selectedRelation);
                            if (relationData && relationData.gender && relationData.gender !== '0') {
                                // 设置家长性别
                                $("#parentGender").val(relationData.gender);
                                form.render('select'); // 重新渲染表单
                            }
                        }
                    }, ["garden.relationship"], null, null, {async: false});
                }
            });
        }

        function initTable() {
            var objwhere = getswhere();
            var arrcol = [
                {field: 'id', type: "numbers", title: '序号', align: 'center', width: '100'},
                {field: 'gardenName', title: '幼儿园', align: 'center', width: '300'},
                {field: 'parentIdentity', title: '家长身份', align: "center", width: '140'},
                {field: 'parentName', title: '家长姓名', align: 'center', width: '140'},
                {
                    field: 'parentGender', title: '性别', align: 'center', width: '140',
                    templet: d => d.parentGender == '2' ? '女' : '男'
                },
                {field: 'parentAge', title: '年龄', align: 'center', width: '140'},
                {field: 'address', title: '家庭住址', align: 'center', width: '300'},
                {field: 'phone', title: '家长手机号', align: 'center', width: '200'},
                {field: 'deviceType', title: '设备类型', align: 'center', width: '150'},
                {
                    field: 'recheckCount', title: '预约挂号数', align: 'center', width: '150',
                    templet: function (d) {
                        let param = JSON.stringify({count: d.recheckCount})
                        param = encodeURIComponent(param)
                        return `<sapn onclick="pageJumpToTable('bianminServiceUnit_parent_appointment.html', '${param}', '便民服务牌数据详细')" style="color:#02baf6; cursor: pointer"">
                                    ${d.recheckCount || ''}
                                </sapn>`;
                    }
                },
                {
                    field: 'cancelCount', title: '取消预约挂号数', align: 'center', width: '150',
                    templet: function (d) {
                        if (d.cancelCount > 0) {
                            let param = JSON.stringify({count: d.cancelCount})
                            param = encodeURIComponent(param)
                            return `<span onclick="pageJumpToTable('bianminServiceUnit_parent_appointment.html', '${param}', '便民服务牌数据详细')" style="color:#02baf6; cursor: pointer"">
                                        ${d.cancelCount}
                                    </span>`;
                        }
                        return d.cancelCount
                    }
                }
            ];

            layui.table.render({
                elem: '#laytable',
                url: encodeURI($.getLayUrl() + "?" + $.getSmStr(["parentAppointment.list"])),
                height: 'full-180',
                where: {
                    swhere: $.msgwhere(objwhere),
                    fields: 'appointmentTime',
                    types: 'desc'
                },
                cols: [arrcol],
                done: function (res, curr, count) {
                    // 表格加载完成后的回调
                },
                countNumberBool: true,
                even: true,
                page: true,
                limits: [30, 50, 100, 200],
                limit: 30,
                skin: 'row'
            });

            layui.table.on('tool(laytable)', function (obj) {
                // 表格工具条事件处理
            });

            layui.table.on('row(laytable)', function (obj) {
                // 单元格点击事件处理
            });
        }

        function btnsearch() {
            var objwhere = getswhere();
            layui.table.reload('laytable', {
                url: encodeURI($.getLayUrl() + "?" + $.getSmStr(["parentAppointment.list"])),
                page: {curr: 1},
                where: {
                    swhere: $.msgwhere({...objwhere, isSearch: '1'}),
                    fields: 'id',
                    types: 'asc'
                }
            });
        }

        function getswhere() {
            var objwhere = {};
            var startDate = $("#startDate").val();
            var endDate = $("#endDate").val();
            var classify = $("#classify").val();
            var appointmentSuccess = $("#appointmentSuccess").val();
            var gardenId = $("#gardenName").val();
            var grade = $("#grade").val();
            var className = $("#className").val();
            var childGender = $("#childGender").val();
            var physicalAge = $("#physicalAge").val();
            var keyword = $("#keyword").val();
            var parentGender = $("#parentGender").val();
            var parentAge = $("#parentAge").val();
            var deviceType = $("#deviceType").val();
            var areaDistribution = $("#areaDistribution").val();
            var parentRelation = $("#parentRelation").val();

            // 处理日期范围
            if (startDate) {
                objwhere.startDate = [startDate];
            }
            if (endDate) {
                objwhere.endDate = [endDate];
            }

            // 处理其他查询条件
            if (classify) objwhere.classify = [classify];
            if (appointmentSuccess !== '') objwhere.appointmentSuccess = [appointmentSuccess];
            if (gardenId) objwhere.gardenId = [gardenId];
            if (grade) objwhere.grade = [grade];
            if (className) objwhere.className = [className];
            if (childGender) objwhere.childGender = [childGender];
            if (physicalAge) objwhere.physicalAge = [physicalAge];
            if (keyword) objwhere.keyword = [keyword];
            if (parentGender) objwhere.parentGender = [parentGender];
            if (parentAge) objwhere.parentAge = [parentAge];
            if (deviceType) objwhere.deviceType = [deviceType];
            if (areaDistribution) objwhere.areaDistribution = [areaDistribution];
            if (parentRelation) objwhere.parentRelation = [parentRelation];

            return {
                ...params,
                ...objwhere
            };
        }

        /*
        功能：页面跳转
        参数说明：param - URL 中的参数
        返回值说明：无
        */
        window.pageJumpToTable = utils.pageJumpToTable
    });
})
