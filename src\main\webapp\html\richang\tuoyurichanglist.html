﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>用户管理</title>
    <link rel="stylesheet" href="../../css/reset.css" />
    <link rel="stylesheet" href="../../plugin/zTree/css/zTreeStyle/zTreeStyle.css" type="text/css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" type="text/css" href="../../css/icon.css" />
    <link rel="stylesheet" type="text/css" href="../../css/style.css" />
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css" />
    <script type="text/javascript">
        document.write("<link rel=\"stylesheet\" id=\"linktheme\" href=\"../../css/" + parent.objdata.curtheme + ".css\" type=\"text/css\" media=\"screen\"/>");
    </script>
    <!--[if lt IE 9]>
    <script src="../../sys/html5shiv.min.js"></script>
    <![endif]-->
    <style>
        body, html {
            background: #eaeff3;
        }
        .popRight{
            margin-left:253px;
        }
        #richangtuoyu li {
            cursor: pointer;
            font-size: 14px;
            padding: 0px 20px 0 10px;
            line-height: 30px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        #richangtuoyu li:hover {
            color: #59b3f0;
        }

        #richangtuoyu li.current {
            color: #23b7e5;
            border: 1px solid #23b7e5;
            background-color: #f0fbfe;
        }
        .agent-left { background: #eee; width: 250px; display: inline-block; font-size: 16px; position: absolute; height: 100%; overflow-y: auto; overflow-x: hidden; }
        .agent-right { vertical-align: top; font-size: 16px; margin-left: 250px; }
        .ztree li a { padding: 0px 20px 0 0; color: #333; width: 210px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
        .ico_docu { background-size: 20px 20px !important; }
        .ztree li a.curSelectedNode {height: 30px;}
        .ztree li {line-height: 30px;}
        .ztree li span { line-height: 30px;}
        .ztree {width: 215px;}
        .ztree li span.button.noline_docu{display: none;}
        .ztree li a.curSelectedNode{padding:0px 20px 0 10px;}
        .ztree li a{padding: 0px 20px 0 10px;}
        .ztree li span.button {margin-top: -3px;}
    </style>
</head>
<body>
    <div class="agent-left">
        <div id="divtree" style="overflow:auto;margin-top:10px;">
            <div id="treeDemo" class="ztree"></div>
        </div>
    </div>
    <div class="agent-right">
        <i style="position: relative; left: 10px; top: 0px; line-height: 25px; display: block;float: left;" id="icon_showhide" isnshow="1"><img src="../../images/image1/tbdown.png" style=" height: 15px;width: 13px; position: absolute; left: -2px;top: 2px; cursor: pointer;"></i>
        <div style=" background: #EAEFF3;">
            <p id="labtablename" style="margin:0 8px 0;background-color:#fff;padding: 10px 20px 0;font-size:18px;"></p>
        </div>
        <iframe id="iframe" src=""></iframe>
    </div>

<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../plugin/nicescroll/jquery.nicescroll.min.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../plugin/zTree/js/jquery.ztree.core-3.5.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../plugin/zTree/js/jquery.ztree.excheck-3.5.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../plugin/zTree/js/jquery.ztree.exedit-3.5.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../js/richang/tuoyurichanglist.js?v=" + v + "'><" + "/script>");
</script>

</body>
</html>
