<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>报告发布情况</title>
    <link rel="stylesheet" type="text/css" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" type="text/css" href="../../css/icon.css"/>
    <link rel="stylesheet" type="text/css" href="../../styles/tbstyles.css"/>
    <link href="../../plugin/flexigrid/css/tbflexigrid.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css"/>
    <script type="text/javascript">
        // 检查 top.objdata 是否存在，如果不存在则设置默认主题
        var curtheme = (top && top.objdata && top.objdata.curtheme) ? top.objdata.curtheme : 'backstage';
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + curtheme + '.css" type="text/css" media="screen"/>');
    </script>

    <style type="text/css">
        html, body {
            background: #EAEFF3;
            overflow: hidden;
        }
        .layui-form-item {
            display: inline-block;
            vertical-align: top;
            margin: 5px 5px 0px 0;
        }

        .layui-form-label {
            line-height: 28px;
        }
    </style>
</head>
<body>
<div class="marmain">
    <div class="content-medical">
        <div class="layui-form layui-comselect" style="padding:10px;">
            <!-- 第一行 -->
            <div class="layui-form-item" style="margin-bottom: 10px;">
                <div class="layui-form-item" style="margin-bottom: 10px;">
                    <label class="layui-form-label" style="width: 80px;">起止日期&nbsp;&nbsp;</label>
                    <div class="layui-input-inline" style="width:120px; margin-right: 5px;">
                        <input id="datestart" type="text" autocomplete="off" placeholder="开始日期" class="layui-input">
                    </div>
                    <div class="layui-form-mid">至</div>
                    <div class="layui-input-inline" style="width:120px; margin-right: 20px;">
                        <input id="dateend" type="text" autocomplete="off" placeholder="结束日期" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item" style="margin-bottom: 10px;">
                    <label class="layui-form-label" style="width: 60px;">关键字：</label>
                    <div class="layui-input-inline" style="width:200px; margin-right: 20px;">
                        <input id="txtkeyword" type="text" autocomplete="off" placeholder="请输入关键字" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item" style="margin-bottom: 10px;">
                    <label class="layui-form-label" style="width: 100px;">是否颁发证书：</label>
                    <div class="layui-input-inline" style="width:120px; margin-right: 15px;">
                        <select id="Issuecertificate" lay-filter="Issuecertificate">
                            <option value="">请选择</option>
                            <option value="1">是</option>
                            <option value="0">否</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item" style="margin-bottom: 10px;">
                <label class="layui-form-label" style="width: 100px;">是否付费课程：</label>
                    <div class="layui-input-inline" style="width:120px; margin-right: 15px;">
                        <select id="paidcourses" lay-filter="paidcourses">
                            <option value="">请选择</option>
                            <option value="1">是</option>
                            <option value="0">否</option>
                        </select>
                    </div>
                </div>
                <button class="layui-btn form-search" style="margin-left: 10px;" id="btnsearch">查询</button>
            </div>
        </div>
    </div>
    <div id="content" style="width: 100%;">
        <div class="marmain-cen">
            <div class="content-medical" id="divtable">
                <table id="laytable" lay-filter="laytable"></table>
            </div>
        </div>
    </div>
</div>

<script data-main="../../js/operation/w_AILearnPA_coursedetail" src='../../sys/require.min.js'></script>
</body>
</html>