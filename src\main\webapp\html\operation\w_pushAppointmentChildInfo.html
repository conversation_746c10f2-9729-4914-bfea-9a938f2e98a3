<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title>家长预约幼儿情况</title>
    <link rel="stylesheet" type="text/css" href="../../css/reset.css" />
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" type="text/css" href="../../css/icon.css" />
    <link rel="stylesheet" type="text/css" href="../../styles/tbstyles.css" />
    <link href="../../plugin/flexigrid/css/tbflexigrid.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css" />
    <style type="text/css">
        html,
        body {
            background: #EAEFF3;
            overflow: hidden;
        }

        .layui-form-item {
            display: inline-block;
            vertical-align: top;
        }

        .layui-form-label {
            line-height: 28px;
            width: 110px;
        }

        .layui-input-inline {
            width: 180px;
        }
    </style>
</head>

<body>
    <div class="marmain">
        <div class="content-medical">
            <div class="search-container layui-form layui-comselect">
                <!-- 第一行搜索条件 -->
                <!-- 日期范围 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">开始日期：</label>
                    <div class="layui-input-inline">
                        <input id="startDate" type="text" autocomplete="off" placeholder="开始日期" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-form-label">结束日期：</div>
                    <div class="layui-input-inline">
                        <input id="endDate" type="text" autocomplete="off" placeholder="结束日期" class="layui-input">
                    </div>
                </div>
                <!-- 第二行搜索条件 -->
                <!-- 幼儿园 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">幼儿园：</label>
                    <div class="layui-input-inline">
                        <select id="gardenName" lay-filter="gardenName">
                            <option value="">请选择</option>
                            <option value="1">蓝天幼儿园</option>
                            <option value="2">红星幼儿园</option>
                            <option value="3">阳光幼儿园</option>
                            <option value="4">彩虹幼儿园</option>
                            <option value="5">希望幼儿园</option>
                        </select>
                    </div>
                </div>
                <!-- 年级 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">年级：</label>
                    <div class="layui-input-inline">
                        <select id="grade" lay-filter="grade">
                            <option value="">请选择</option>
                            <option value="小班">小班</option>
                            <option value="中班">中班</option>
                            <option value="大班">大班</option>
                        </select>
                    </div>
                </div>
                <!-- 班级 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">班级：</label>
                    <div class="layui-input-inline">
                        <select id="className" lay-filter="className">
                            <option value="">请选择</option>
                            <option value="小一班">小一班</option>
                            <option value="2">二班</option>
                            <option value="3">三班</option>
                            <option value="4">四班</option>
                        </select>
                    </div>
                </div>
                <!-- 幼儿性别 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">幼儿性别：</label>
                    <div class="layui-input-inline">
                        <select id="childGender" lay-filter="childGender">
                            <option value="">请选择</option>
                            <option value="男">男</option>
                            <option value="女">女</option>
                        </select>
                    </div>
                </div>
                <!-- 体检年龄 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">体检年龄：</label>
                    <div class="layui-input-inline">
                        <select id="physicalAge" lay-filter="physicalAge">
                            <option value="">请选择</option>
                            <option value="3">3岁~</option>
                            <option value="4">4岁~</option>
                            <option value="5">5岁~</option>
                            <option value="6">6岁~</option>
                            <option value="7">7岁~</option>
                        </select>
                    </div>
                </div>
                <!-- 关键字 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">关键字：</label>
                    <div class="layui-input-inline">
                        <input id="keyword" type="text" autocomplete="off" placeholder="幼儿姓名/家长姓名" class="layui-input">
                    </div>
                </div>
                <!-- 第三行搜索条件 -->
                <!-- 区域分布 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">区域分布：</label>
                    <div class="layui-input-inline">
                        <select id="address" lay-filter="address">
                            <option value="">请选择</option>
                            <option value="1">1km以内</option>
                            <option value="2">1km~3km</option>
                            <option value="3">3km~5km</option>
                            <option value="4">≥5km</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <button class="layui-btn layui-btn-normal" id="btnsearch">查询</button>
                </div>
            </div>
            <div id="content" style="width: 100%;">
                <div class="marmain-cen">
                    <div class="content-medical" id="divtable">
                        <table id="laytable" lay-filter="laytable"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script data-main="../../js/operation/w_pushAppointmentChildInfo" src="../../sys/require.min.js"></script>
</body>

</html>