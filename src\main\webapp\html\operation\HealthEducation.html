<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>健康宣传大数据</title>
    <link rel="stylesheet" href="../../css/reset.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../css/style.css"/>
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/physical.css">
    <script type="text/javascript" src="../../layui-btkj/layui.js"></script>
    <script type="text/javascript">
        document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');
    </script>
    <!--[if lt IE 9]>
    <script src="../sys/html5shiv.min.js"></script>
    <![endif]-->
    <style type="text/css">
        html, body {
            background: #EAEFF3;
        }

        .layui-form-label {
            padding: 8px 0px 5px 10px;
            width: auto;
        }

        /* 热门文章排名样式 */
        .rank-number {
            display: inline-block;
            width: 20px;
            height: 20px;
            line-height: 20px;
            text-align: center;
            border-radius: 3px;
            color: white;
            font-size: 12px;
            font-weight: bold;
            margin-right: 8px;
            vertical-align: middle;
        }

        .rank-1 {
            background-color: #FF4444;
        }

        .rank-2 {
            background-color: #FF8800;
        }

        .rank-3 {
            background-color: #FFBB33;
        }

        .rank-4, .rank-5 {
            background-color: #999999;
        }

        /* 热门文章表格对齐优化 */
        #topArticlesTable td:first-child {
            text-align: left !important;
            padding-left: 15px !important;
        }

        #topArticlesTable td:first-child span {
            vertical-align: middle;
        }
    </style>
</head>
<body>
<div class="marmain" style="min-width: 950px;">
    <div class="content-medical">
        <div class="layui-form layui-comselect" style="position: relative; ">
            <div class="btn-nextfl"><span class="layui-form-label" style="text-align: left;padding-left: 0;">时间范围：</span>
                <div class="layui-input-inline" style="float: left; width: 150px;">
                    <input type="text" class="layui-input" id="startDate" placeholder="开始日期" readonly>
                </div>
                <div class="layui-form-mid">-</div>
                <div class="layui-input-inline layui-form" style="float:left;width:150px;">
                    <input type="text" class="layui-input" id="endDate" placeholder="结束日期" readonly>
                </div>
            </div>
        </div>
    </div>
    <div id="content" style="width: 100%;">
        <div class="marmain-cen">
            <div class="cenmartop">
                <div class="conference-sta">
                    <div class="total-div" style="margin-left: 0;"><img src="../../images/physical/yekindergarten01.png" class="kindergartenimg">
                        <div class="total-panel">
                            <div class="total-left"><img src="../../images/physical/tskindergarten01img.png" class="kinder-ltimg">
                                <div class="total-numbg">
                                    <div class="num-label" id="totalViews">0</div>
                                    <div class="num-peo">总阅读数</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="total-div"><img src="../../images/physical/yekindergarten02.png" class="kindergartenimg">
                        <div class="total-panel">
                            <div class="total-left"><img src="../../images/physical/xkindergarten02img.png" class="kinder-ltimg">
                                <div class="total-numbg">
                                    <div class="num-label" id="avgReadTime">0</div>
                                    <div class="num-peo">人均阅读时长</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="total-div"><img src="../../images/physical/yekindergarten03.png" class="kindergartenimg">
                        <div class="total-panel">
                            <div class="total-left"><img src="../../images/physical/zkindergarten03img.png" class="kinder-ltimg">
                                <div class="total-numbg">
                                    <div class="num-label" id="totalLikes">0</div>
                                    <div class="num-peo">点赞量</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="total-div"><img src="../../images/physical/yekindergarten04.png" class="kindergartenimg">
                        <div class="total-panel">
                            <div class="total-left"><img src="../../images/physical/xjindergarten04img.png"
                                                         class="kinder-ltimg">
                                <div class="total-numbg">
                                    <div class="num-label" id="totalShares">0</div>
                                    <div class="num-peo">转发量</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="total-div"><img src="../../images/physical/yekindergarten05.png" class="kindergartenimg">
                        <div class="total-panel">
                            <div class="total-left"><img src="../../images/physical/xjindergarten05img.png"
                                                         class="kinder-ltimg">
                                <div class="total-numbg">
                                    <div class="num-label" id="totalComments">0</div>
                                    <div class="num-peo">评论量</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="total-div"><img src="../../images/physical/yekindergarten06.png" class="kindergartenimg">
                        <div class="total-panel">
                            <div class="total-left"><img src="../../images/physical/xjindergarten06img.png"
                                                         class="kinder-ltimg">
                                <div class="total-numbg">
                                    <div class="num-label" id="totalArticles">234</div>
                                    <div class="num-peo">预约量</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="visiting-statis">
                    <div class="safestat-cen">
                        <div class="safestat-tit"><i class="left-tit"></i><span>阅读行为分析</span></div>
                        <div class="colunwit">
                            <div id="readingBehaviorChart" style="width: 100%; height: 300px;"></div>
                        </div>
                    </div>
                    <div class="safestat-cen">
                        <div class="safestat-tit"><i class="left-tit"></i><span>预约分析</span></div>
                        <div class="colunwit">
                            <div id="appointmentChart" style="width: 100%; height: 300px;"></div>
                        </div>
                    </div>
                </div>
                <div class="visiting-statis">
                    <div class="safestat-cen">
                        <div class="safestat-tit"><i class="left-tit"></i><span>内容类型分析</span></div>
                        <div class="colunwit">
                            <div id="contentTypeChart" style="width: 100%; height: 350px;"></div>
                        </div>
                    </div>
                    <div class="safestat-cen">
                        <div class="safestat-tit"><i class="left-tit"></i><span>热门文章top10</span></div>
                        <div class="colunwit" style="overflow-y: auto;">
                            <table class="layui-table" lay-size="lg"
                                   style="text-align: center;color: #999999; width: 97%; margin: 10px auto">
                                <thead>
                                <tr style="color: #666666; ">
                                    <th style="text-align: center;">文章</th>
                                    <th style="text-align: center;">阅读量</th>
                                    <th style="text-align: center;">点赞</th>
                                    <th style="text-align: center;">收藏</th>
                                    <th style="text-align: center;">分享</th>
                                </tr>
                                </thead>
                                <tbody id="topArticlesTable">
                                <!-- 动态填充内容 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="visiting-statis">
                    <div class="safestat-cen heighrow">
                        <div class="safestat-tit"><i class="left-tit"></i><span>家长画像</span></div>
                        <div class="colunwit ">
                            <div class=" weui-flex  hit50">
                                <div class="columnlist">
                                    <h3>性别</h3>
                                    <div id="parentGenderPie" style="width: 100%; height: 300px;"></div>
                                </div>
                                <div class="columnlist">
                                    <h3>访问设备</h3>
                                    <div id="devicePie" style="width: 100%; height: 300px;"></div>
                                </div>
                            </div>
                            <div class="weui-flex hit50">
                                <div class="columnlist">
                                    <h3>年龄分布</h3>
                                    <div id="parentAgePie" style="width: 100%; height: 300px;"></div>
                                </div>
                                <div class="columnlist">
                                    <h3>区域分布</h3>
                                    <div id="regionRadar" style="width: 100%; height: 300px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="visiting-statis">
                    <div class="safestat-cen">
                        <div class="safestat-tit"><i class="left-tit"></i><span>孩子与家长关系</span></div>
                        <div class="colunwit">
                            <div id="relationBar" style="width: 100%; height: 400px;"></div>
                        </div>
                    </div>
                </div>
                <div class="visiting-statis">
                    <div class="safestat-cen">
                        <div class="safestat-tit"><i class="left-tit"></i><span>幼儿画像</span></div>
                        <div class="colunwit weui-flex ">
                            <div class="columnlist">
                                <h3>性别</h3>
                                <div id="childGenderPie" style="width: 100%; height: 250px;"></div>
                            </div>
                            <div class="columnlist">
                                <h3>年龄分布</h3>
                                <div id="childAgePie" style="width: 100%; height: 250px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script data-main="../../js/operation/HealthEducation.js" src="../../sys/require.min.js"></script>
</body>
</html>
