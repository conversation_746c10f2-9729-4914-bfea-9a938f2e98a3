﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <link rel="shortcut icon" href="/images/favicon.ico" type="image/x-icon"/>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="Copyright" content="Copyright 2016 by tongbang"/>
    <meta name="Author" content=""/>
    <meta name="Robots" content="All"/>
    <title>表1-6托育机构营养性疾病及常见病儿童登记表</title>
    <link rel="stylesheet" href="../../css/reset.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <style>
        html, body {
            background: #EAEFF3;
        }

        /*		.bDiv table td{ float:left}*/
        .staff-sear label {
            display: initial;
            margin-left: 6px;
            margin-top: 0px;
        }

        .flexigrid div.hDiv th div { /*border-bottom: 1px solid #1da89e;*/
            line-height: 18px;
            height: 18px;
        }

        .flexigrid div.shlockDiv th div {
            height: 40px;
            line-height: 40px; /*border-bottom: 1px solid #1da89e;*/
        }

        .warning {
            border-color: red !important;
        }

        .flexigrid div.hDiv th, div.colCopy, .flexigrid div.hlockDiv th {
            height: 20px;
        }

        .flexigrid div.pDiv input {
            top: 1px;
        }

        .flexigrid div.bDiv input, .flexigrid div.bDiv select {
            border: none;
            border-radius: 0;
        }

        .flexigrid div.hDiv, .flexigrid div.hlockDiv {
            background: #F5F7F9;
        }

        .flexigrid div.blockDiv, .flexigrid div.bDiv {
            background: #fff;
        }

        .flexigrid div.blockDiv {
            top: 40px !important;
        }

        .flexigrid div.bDiv td, .flexigrid div.blockDiv td {
            border-bottom: 1px solid #C3CBDD;
            border-right: 1px solid #C3CBDD;
        }

        .flexigrid div.hDiv th div, .flexigrid div.bDiv td div, .flexigrid div.blockDiv td div, div.colCopy div, .flexigrid div.hlockDiv th div {
            padding: 0 2px;
        }
        .layui-table-cell .layui-form-checkbox[lay-skin="primary"] {
            top: 7px;
        }
	   .layui-form .layui-form-item{margin-bottom: 5px;display: inline-block;}
	   .layui-form .layui-form-item,.layui-btn{margin-bottom: 5px;}
    </style>
</head>
<body>
<div class="">
    <div class="content-medical">
        <div class="layui-form layui-comselect" style="position: relative;padding: 10px 10px 5px 10px;">
            <div style="display: block;">
                <div style="display: inline-block;padding-left: 10px;margin-bottom:15px;font-size: 18px;">表1-6托育机构营养性疾病及常见病儿童登记表</div>
<!--                <div style="display: inline-block;margin-left: 60px;">此表由保健人员填写</div>-->
            </div>
            <div style="display: block;">
                <div class="layui-form-item" style="display: none;vertical-align: top;">
                    <label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 10px;line-height: 28px;">园所名称：</label>
                    <div class="layui-input-inline" style="min-height: 30px;width:180px;">
                        <div id="yeyid" class="xm-select-demo" style="display: inline-block; width: 100%;"></div>
                    </div>
                </div>
                <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                    <label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 10px;line-height: 28px;">年份：</label>
                    <div class="layui-input-inline" style="min-height: 30px;width:180px;">
                        <input id="year" type="text" placeholder="年份" readonly class="layui-input"/>
                    </div>
                </div>
                <button id="btnsearch" class="layui-btn form-search" style="vertical-align: top;">查询</button>
                <button id="btnadd" class="layui-btn form-search" style="vertical-align: top;">新增登记</button>
                <button id="btnemptytable" class="layui-btn form-search" style="vertical-align: top;">空白表</button>
            </div>
        </div>
    </div>
    <div class="content-medical marmain-cen">
        <table id="grdlist" lay-filter="grdlist"></table>
    </div>
</div>
<!--<script data-main="../js/tongph/tjdjrecheck_yey" src='../sys/require.min.js'></script>-->
<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/function.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../plugin/xm-select/xm-select.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../js/richang/tuoyucommon.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../js/richang/tuoyuchangjianbing.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>
