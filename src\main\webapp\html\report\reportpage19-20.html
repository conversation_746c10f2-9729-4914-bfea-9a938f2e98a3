<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>page19-20</title>
    <link rel="stylesheet" href="../../css/reset.css"/>
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/style.css">
    <link rel="stylesheet" href="../../css/report.css">
    <style>
        html,body{height: 100%;}
        body{background: #f6f6f6;}
    </style>
</head>
<body>
<section style="height: 100%;">
    <div class="weui-flex_center" style="height: 100%;">
        <div class="page-content weui-flex">
            <div class="page-cell weui-flex__item" style="background: url(../../images/report/page1_bg.png)no-repeat;">
                <div class="title-page">
                    <h1>四</h1>
                    <h5>体检体测</h5>
                </div>
                <div class="page-num" style="left: 18px;">19</div>
            </div>
            <div class="page-cell weui-flex__item" style="background: url(../../images/report/page2_bg.png)no-repeat;">
                <h6 class="page-name">四、体检体测</h6>
                <div style="margin: 70px 50px 0 50px;">
                    <div class="page-top">体检</div>
                    <div class="page1-righttxt" style="margin: 0;">
                        <p>0-6岁是儿童生长发育最快的时期，一些在生活中易被忽视的儿童疾病（如弱视、脊柱侧弯等）通过体检都可以发现，而这些问题的治疗必须在低领期进行。因此体检是儿童成长过程中重要的一部分，通过对儿童体检的各项数据，运用科学的统计分析方法可分析出儿童所患疾病和致病危险因素，为幼儿园、家长、和社会卫生保健部门做到早发现，早治疗，早期干预提供科学依据。本篇对2020.01.01至2020.12.31时间段内XX区域内幼儿园的在园适龄儿童体检结果进行分析，希望从中找出对儿童健康造成危害的因素，并在今后的养育工作中避免及改正。</p>
                        <p>全区共XX所幼儿园共有XX儿童，其中XX儿童进行了体检测试，测试率超过了XX%</p>
                    </div>
                    <div style="height: 200px;margin: 20px 0;">

                    </div>
                    <div class="page1-righttxt" style="margin: 0;">
                        <p>根据年报表统计数据分析，全区儿童生长发育水平有所提高，与去年相比体格增长人数增加了xx%（去年增长人数xxx，今年增长人数xxxx），增长合格人数增加了xx%（去年增长合格人数xxxx，今年增长合格人数xxxx）。</p>
                    </div>
                    <div style="text-align: center;padding: 20px 0 5px 0;">
                        <span class="title-btn">体格增长对比</span>
                    </div>
                    <div style="height: 180px;">

                    </div>
                </div>
                <div class="page-num" style="right: 18px;">20</div>
            </div>
        </div>
    </div>
</section>
</body>
</html>