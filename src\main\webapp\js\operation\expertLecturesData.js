require.config({
    paths: {
        mock: '../../plugin/mock/dist/mock',
        commonUtils: './commonUtils'
    },
    waitSeconds: 0
})
require(['commonUtils', 'mock'], (util) => {
    const Mock = require('mock')
    // 启用请求拦截
    Mock.setup({
        timeout: '200-600' // 模拟网络延迟
    })
    // Mock.js 模拟数据

    /**
     * 统计卡片
     */
    Mock.mock(/\/expertLectures\/stats/, 'post', function (option) {
        // console.log('拦截到 POST 请求:', option) // 可选：打印请求信息
        const lectures = Mock.Random.natural(15, 30); // 专家讲座数
        const touchRate = Mock.Random.float(80, 95, 1, 1); // 触达率80-95%
        const viewRate = Mock.Random.float(30, 50, 1, 1); // 查看率30-50%
        const registerRate = Mock.Random.float(15, 25, 1, 1); // 报名率15-25%
        const signRate = Mock.Random.float(60, 80, 1, 1); // 签到率60-80%

        const base = lectures * 1000; // 基准人数
        return {
            code: 0,
            msg: 'success',
            data: {
                lecturesNumber: lectures,
                touchRate: touchRate + '%',
                viewNumber: Math.round(base * viewRate / 100),
                registrationNumber: Math.round(base * registerRate / 100),
                signNumber: Math.round(base * registerRate / 100 * signRate / 100)
            }

        }
    })

    /**
     * 通知触达情况
     */
    Mock.mock(/\/expertLectures\/reachData/, 'post', function (option) {
        const data = Mock.mock({
            code: 0,
            msg: 'success',
            data: [
                {
                    name: '成功触达',
                    value: '@integer(500, 1000)'
                },
                {
                    name: '未触达',
                    value: '@integer(50, 100)'
                }
            ]
        })
        return data
    })

    /**
     * 用户打开趋势
     */
    Mock.mock(/\/expertLectures\/openTrendData/, function (option) {
        const body = JSON.parse(option.body);

        // 解析请求中的日期范围
        const startDate = new Date(body.startDate); // 2025-06-24
        const endDate = new Date(body.endDate);     // 2025-06-24

        const data = {
            xAxis: [],
            yAxis: []
        }
        // 计算日期范围内的所有日期
        // const dates = [];
        // const values = [];
        let currentDate = startDate

        // 计算时间跨度（天）
        const timeSpan = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
        // 决定是否需要显示年份（超过30天显示年份）
        // const showYear = timeSpan > 30;

        // 生成日期数组和对应数值
        while (currentDate <= endDate) {
            // 格式化日期
            const year = currentDate.getFullYear();
            const month = currentDate.getMonth() + 1;
            const day = currentDate.getDate();

            // 根据是否需要显示年份决定格式
            data.xAxis.push(util.formatDate(`${year}-${month}-${day}`));

            // 判断日期特征
            const dayOfWeek = currentDate.getDay();
            const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
            const isHoliday = checkIfHoliday(currentDate); // 假设节假日

            // 基础值设置
            let baseValue = 200; // 工作日默认值
            if (isWeekend) baseValue = 100; // 周末较低
            if (isHoliday) baseValue = 200;  // 节假日最低

            // 学期周期影响 (3-6月,9-12月较高)
            const isSemesterPeak = (month >= 3 && month <= 6) || (month >= 9 && month <= 12);
            if (isSemesterPeak) baseValue *= 1.2;

            // 计算趋势值 (随时间缓慢上升)
            const daysFromStart = Math.floor((currentDate - startDate) / (1000 * 60 * 60 * 24));
            const trendValue = daysFromStart * 3; // 更平缓的增长

            // 添加随机波动 (周末波动更大)
            const randomRange = isWeekend ? 30 : 15;
            const randomVariation = Mock.Random.natural(-randomRange, randomRange);

            if (currentDate > new Date()) {
                data.yAxis.push(0) // 未来日期数据为0
            } else {
                // 确保最小值合理
                data.yAxis.push(Math.max(50, baseValue + trendValue + randomVariation));
            }

            // 辅助函数 - 简单节假日判断
            function checkIfHoliday(date) {
                const monthDay = (date.getMonth() + 1) + '-' + date.getDate();
                const holidays = ['1-1', '5-1', '10-1', '10-2', '10-3']; // 元旦、劳动节、国庆节
                return holidays.includes(monthDay);
            }

            // 移动到下一天
            currentDate.setDate(currentDate.getDate() + 1);
        }
        return Mock.mock({
            code: 0,
            message: 'success',
            data
        });
    })

    /**
     * 报名时段分布
     */
    Mock.mock(/\/expertLectures\/registerTimeData/, 'post', function (option) {
        const xAxis = []
        const yAxis = []
        for (let i = 0; i < 24; i++) {
            let time = i + ':00'
            if (i < 10) {
                time = '0' + time
            }
            xAxis.push(time)
            if (i >= 0 && i <= 3) {
                yAxis.push(Mock.mock('@integer(5, 10)'))
            }
            if (i > 3 && i <= 6 || i > 15 && i <= 18) {
                yAxis.push(Mock.mock('@integer(18, 22)'))
            }
            if (i > 6 && i <= 9) {
                yAxis.push(Mock.mock('@integer(35, 40)'))
            }
            if (i > 9 && i <= 15) {
                yAxis.push(Mock.mock('@integer(8, 12)'))
            }
            if (i > 18 && i <= 21) {
                yAxis.push(Mock.mock('@integer(48, 52)'))
            }
            if (i > 21 && i <= 24) {
                yAxis.push(Mock.mock('@integer(38, 42)'))
            }
        }
        return {
            code: 0,
            msg: 'success',
            data: { xAxis, yAxis }
        }
    })

    /**
     * 预约时段分布
     */
    Mock.mock(/\/expertLectures\/oppointTimeData/, function (option) {
        return Mock.mock({
            code: 0,
            msg: 'success',
            data: {
                timeData: [
                    '@integer(5, 10)', // 0-3 时段数据
                    '@integer(18, 22)', // 3-6 时段数据
                    '@integer(35, 40)', // 6-9 时段数据
                    '@integer(8, 12)', // 9-12 时段数据
                    '@integer(8, 12)', // 12-15 时段数据
                    '@integer(18, 22)', // 15-18 时段数据
                    '@integer(48, 52)', // 18-21 时段数据
                    '@integer(38, 42)' // 21-24 时段数据
                ]
            }
        })
    })

    /**
     * 现场听讲转化漏斗
     */
    Mock.mock(/\/expertLectures\/funnelData/, 'post', function (option) {
        return Mock.mock({
            code: 0,
            message: 'success',
            data: () => {
                const base = Mock.Random.natural(800, 1200);
                return [
                    { name: '接收通知', value: base },
                    { name: '打开通知', value: Math.round(base * 0.8) }, // 80%打开率
                    { name: '报名讲座', value: Math.round(base * 0.8 * 0.3) }, // 30%报名率
                    { name: '现场签到', value: Math.round(base * 0.8 * 0.3 * 0.7) } // 70%签到率
                ];
            }
        })
    })

    // 拦截性别数据请求
    Mock.mock(/\/expertLectures\/gender/, 'post', () => {
        const total = Mock.Random.natural(200, 1000)
        const male = Math.floor(total / 2)
        const female = Math.floor(total - male)
        return Mock.mock({
            code: 0,
            message: 'success',
            data: [
                {
                    name: '男性',
                    value: male
                },
                {
                    name: '女性',
                    value: female
                }
            ]
        })
    })

    // 拦截设备数据请求
    Mock.mock(/\/expertLectures\/device/, 'post', () => {
        const base = Mock.Random.natural(800, 1200);
        return Mock.mock({
            code: 0,
            message: 'success',
            data: [
                {
                    name: '安卓',
                    value: Math.round(base * 0.6)  // 60%
                },
                {
                    name: 'iOS',
                    value: Math.round(base * 0.35) // 35%
                },
                {
                    name: '未知',
                    value: Math.round(base * 0.05) // 5%
                }
            ]
        })
    })

    // 拦截年龄数据请求
    Mock.mock(/\/expertLectures\/age/, 'post', () => {
        const base = Mock.Random.natural(800, 1200);
        return Mock.mock({
            code: 0,
            message: 'success',
            data: [
                {
                    name: '26到35岁',
                    value: Math.round(base * 0.35)  // 35%
                },
                {
                    name: '36到45岁',
                    value: Math.round(base * 0.4)   // 40%
                },
                {
                    name: '46到60岁',
                    value: Math.round(base * 0.15)  // 15%
                },
                {
                    name: '18岁到25岁',
                    value: Math.round(base * 0.05)  // 5%
                },
                {
                    name: '60岁以上',
                    value: Math.round(base * 0.03)  // 3%
                },
                {
                    name: '未知',
                    value: Math.round(base * 0.02)   // 2%
                }

            ]
        })
    })

    // 拦截区域数据请求
    Mock.mock(/\/expertLectures\/region/, 'post', () => {
        return Mock.mock({
            code: 0,
            message: 'success',
            data: {
                // 模拟不同距离范围的家长数、幼儿数
                areas: [
                    {
                        distance: '1km',
                        parentCount: '@integer(10, 50)',
                        childCount: '@integer(10, 50)'
                    },
                    {
                        distance: '3km',
                        parentCount: '@integer(30, 80)',
                        childCount: '@integer(30, 80)'
                    },
                    {
                        distance: '5km',
                        parentCount: '@integer(50, 100)',
                        childCount: '@integer(50, 100)'
                    },
                    {
                        distance: '未知',
                        parentCount: '@integer(50, 100)',
                        childCount: '@integer(50, 100)'
                    }
                ]
            }
        })
    })

    // 获取关系数据
    Mock.mock(/\/expertLectures\/relationship/, 'post', () => {
        const categories = [
            '爸爸',
            '妈妈',
            '爷爷',
            '奶奶',
            '外公',
            '外婆',
            '叔叔',
            '阿姨',
            '婶婶',
            '姑妈',
            '姑父',
            '伯父',
            '伯母',
            '舅舅',
            '舅妈',
            '哥哥',
            '姐姐',
            '其他'
        ];

        const values = [
            Mock.Random.natural(180, 220),  // 爸爸
            Mock.Random.natural(200, 250),  // 妈妈
            Mock.Random.natural(30, 60),    // 爷爷
            Mock.Random.natural(40, 70),    // 奶奶
            Mock.Random.natural(25, 50),    // 外公
            Mock.Random.natural(35, 60),    // 外婆
            Mock.Random.natural(5, 15),     // 叔叔
            Mock.Random.natural(5, 15),     // 阿姨
            Mock.Random.natural(3, 10),     // 婶婶
            Mock.Random.natural(3, 10),     // 姑妈
            Mock.Random.natural(2, 8),      // 姑父
            Mock.Random.natural(2, 8),      // 伯父
            Mock.Random.natural(2, 8),      // 伯母
            Mock.Random.natural(2, 8),      // 舅舅
            Mock.Random.natural(2, 8),      // 舅妈
            Mock.Random.natural(1, 5),      // 哥哥
            Mock.Random.natural(1, 5),      // 姐姐
            Mock.Random.natural(5, 15)      // 其他
        ];
        return Mock.mock({
            code: 0,
            message: 'success',

            data: {
                xAxis: categories,
                data: values
            }
        })
    })

    // 拦截年龄数据请求
    Mock.mock(/\/expertLectures\/childAge/, 'post', () => {
        return Mock.mock({
            code: 0,
            message: 'success',
            data: [
                {
                    name: '3岁',
                    value: 425
                },
                {
                    name: '4岁',
                    value: Mock.Random.natural(100, 400)
                },
                {
                    name: '5岁',
                    value: Mock.Random.natural(50, 200)
                },
                {
                    name: '6岁',
                    value: Mock.Random.natural(20, 100)
                },
                {
                    name: '7岁',
                    value: Mock.Random.natural(10, 50)
                }
            ]
        })
    })
})