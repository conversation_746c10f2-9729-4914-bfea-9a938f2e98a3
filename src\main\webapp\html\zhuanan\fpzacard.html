﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>卫生保健-体检-肥胖儿专案管理卡</title>
    <link rel="shortcut icon" href="/images/favicon.ico" type="image/x-icon"/>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="Copyright" content="Copyright 2016 by tongbang"/>
    <meta name="Author" content=""/>
    <meta name="Robots" content="All"/>
    <link href="../../css/reset.css" type="text/css" rel="stylesheet"/>
    <link href="../../styles/tbstyles.css" type="text/css" rel="stylesheet"/>
    <link href="../../plugin/ehaiform/css/tbbaseform.css" rel="stylesheet" type="text/css"/>
    <link href="../../plugin/editselect/css/editselect.css" rel="stylesheet" type="text/css"/>
    <style type="text/css">
        .tjzagl_textarea{
            text-align: left;
            height: 70px;
            border: 1px solid #eee;
        }
    </style>
</head>
<body>
<div class="marmain" style="">
    <span style="margin:10px 0 10px 0;display:none;">监测库类型：<select id="selmt" class="editable-select">
            <option value="">请选择</option>
            <option value="正常">正常</option>
            <option value="超重">超重</option>
            <option value="肥胖">肥胖</option>
        </select>
        </span>
    <table id="tabcard0" border="0" cellpadding="0" cellspacing="0" style="border:  #d8d8d8 1px solid;width:100%;">
        <tr>
            <td style="width: 40%;">
                <table border="0" cellpadding="10" cellspacing="0" style="text-align: center; width: 100%;">
                    <tr>
                        <td style="width: 10%; border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            姓名
                        </td>
                        <td id="txtname" style="width: 20%; border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;"></td>
                        <td style="width: 10%; border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            性别
                        </td>
                        <td id="txtsex" style="width: 20%; border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            男
                        </td>
                    </tr>
                    <tr>
                        <td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            班级
                        </td>
                        <td id="txtclass" style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                        </td>
                        <td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            出生年月
                        </td>
                        <td id="txtdirthday" style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                        </td>
                    </tr>
                    <tr>
                        <td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            父亲身高
                        </td>
                        <td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            <input id="txtfheight" maxlength="20" type="text" style="width: 80%;" class="tjzagl_input"/>cm <span class="FancyInput__bar___1P3wW"></span>
                        </td>
                        <td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            父亲体重
                        </td>
                        <td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            <input id="txtfweight" maxlength="20" type="text" style="width: 85%;" class="tjzagl_input"/>kg <span class="FancyInput__bar___1P3wW"></span>
                        </td>
                    </tr>
                    <tr>
                        <td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            母亲身高
                        </td>
                        <td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            <input id="txtmheight" maxlength="20" type="text" style="width: 80%;" class="tjzagl_input"/>cm <span class="FancyInput__bar___1P3wW"></span>
                        </td>
                        <td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            母亲体重
                        </td>
                        <td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            <input id="txtmweight" maxlength="20" type="text" style="width: 85%;" class="tjzagl_input"/>kg <span class="FancyInput__bar___1P3wW"></span>
                        </td>
                    </tr>
                    <tr>
                        <td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            父亲BMI
                        </td>
                        <td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            <label id="lbfbmi"></label>
                        </td>
                        <td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            母亲BMI
                        </td>
                        <td style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;">
                            <label id="lbmbmi"></label>
                        </td>
                    </tr>
                    <tr>
                        <td style="border-right: #d8d8d8 1px solid; border-bottom:  #d8d8d8 1px solid;">
                            家庭其他<br/>
                            肥胖成员
                        </td>
                        <td colspan="3" style="border-right: #d8d8d8 1px solid; border-bottom:  #d8d8d8 1px solid;">
                            <input id="txtotherfat" maxlength="20" type="text" style="width: 98%; height: 30px;" class="tjzagl_input"/> <span class="FancyInput__bar___1P3wW"></span>
                        </td>
                    </tr>
                </table>
            </td>
            <td style="width: 60%; border-bottom:  #d8d8d8 1px solid;" valign="top">
                <!--                干预方案：<br/>-->
                <!--                <textarea id="txteplan" cols=" " rows=" " style="width: 95%; height: 210px;" require="false" datatype="LimitEn" msg="不能超过最大长度2000" max="2000"></textarea>-->
                <div style="padding:5px 10px;">
                    <label>既往病史：出生情况、喂养（饮食）与患病情况：</label><br/>
                    <textarea id="txtcondition" cols=" " rows=" " style="width: 98%; height: 210px;border:1px solid #ccc;" require="false" datatype="LimitEn" msg="不能超过最大长度400" max="400"></textarea>
                </div>
            </td>
        </tr>
        <tr>
            <td colspan="2" style="padding: 10px 10px 10px 5px;">
                <label>首次检查结果：</label>
                <label style="">测量方式：
                    <select id="txtselwlwway" class="editable-select">
                        <option value="2">立位</option>
                        <option value="1">卧位</option>
                    </select>
                </label><!--<input id="txtselwlwway" type="text" style="width: 50px;" />-->
                <label style="margin-left: 20px">体重（kg）：<input type="text" id="txtoldweight" style="width: 50px;"/></label>
                <label style="margin-left: 20px">身高（cm）：<input id="txtoldheight" type="text" style="width: 50px;"/></label>
                <label style="margin-left: 20px;display:none;">肥胖度：<label id="divoldfat"></label></label>
                <label style="margin-left: 20px"><label class="lbstandard"></label>当前评价：<label id="divoldwufat"></label></label>
                <label style="margin-left: 20px">开始管理日期：<input id="txtolddate" class="Wdate" type="text" style="width: 100px;"/></label>
            </td>
        </tr>
        <tr>
            <td colspan="2">
                <table id="tbl_clone" border="0" cellpadding="10" cellspacing="0" style="text-align: center; width: 100%;
                        margin-top: 0px; border-top:  #d8d8d8 1px solid;display: none;float: left;position: absolute;background: #1da89e;color:white;">
                </table>
                <table border="0" cellpadding="10" cellspacing="0" style="text-align: center; width: 100%;
                        margin-top: 0px; border-top:  #d8d8d8 1px solid;">
                    <tbody id="t_bodyhead" style="background: #1da89e;color:white;">
                    <tr>
                        <td rowspan="3" style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;min-width: 40px;width: 3.7%; ">
                            删除
                        </td>
                        <td rowspan="3" style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;min-width: 110px;"><b style="color:red;">*</b>
                            日期
                        </td>
                        <td rowspan="3" style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;min-width: 23px;">
                            年龄
                        </td>
                        <td rowspan="3" style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;min-width: 40px;">
                            测量<br/>
                            方式
                        </td>
                        <td colspan="5" style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid; min-width:35px;"><b style="color:red;">*</b>
                            体格检查
                        </td>
                        <td rowspan="3" style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;min-width: 45px;">
                            BMI
                        </td>
                        <td rowspan="3" style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;min-width: 60px;display:none;">
                            肥胖度
                        </td>
                        <td rowspan="3" style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;min-width: 26px;">
                            诊断
                        </td>
                        <td rowspan="3" style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;min-width: 26px;">
                            目前存在<br/>主要问题
                        </td>
                        <td rowspan="3" style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;min-width: 26px;">
                            干预措施
                        </td>
                        <td rowspan="3" style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;min-width: 26px;">
                            签字
                        </td>
                    </tr>
                    <tr>
                        <td rowspan="2" style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid; min-width:35px;"><b style="color:red;">*</b>
                            体重<br/>
                            (kg)
                        </td>
                        <td rowspan="2" style="border-right: #d8d8d8 1px solid; border-bottom: #d8d8d8 1px solid;min-width:35px"><b style="color:red;">*</b>
                            身高<br/>
                            (cm)
                        </td>
                        <td colspan="3" style="border-right: solid 1px #d8d8d8; border-bottom: solid 1px #d8d8d8;  width:6.2%;min-width: 88px;">
                            评价
                        </td>
                    </tr>
                    <tr>
                        <td style="border-right: solid 1px #d8d8d8; border-bottom: solid 1px #d8d8d8;min-width:120px;">
                            W/A
                        </td>
                        <td style="border-right: solid 1px #d8d8d8; border-bottom: solid 1px #d8d8d8;min-width:120px;">
                            H/A
                        </td>
                        <td style="border-right: solid 1px #d8d8d8; border-bottom: solid 1px #d8d8d8;min-width:120px;">
                            W/H
                        </td>
                    </tr>
                    </tbody>
                    <tbody id="divtbody">
                    </tbody>
                </table>
            </td>
        </tr>
        <tr>
            <td colspan="2" style="padding: 10px 10px 10px 5px;">
                <p style="line-height: 20px;">
                    <input type="checkbox" id="chkvest"/>&nbsp;&nbsp;<label for="chkvest">转归：</label>
                    <label id="selvest" style="display:none;"></label>
                    <label style="margin-left: 10px;">体重：<input type="text" id="txtvestweight" style="width: 50px;"/></label><label style="margin-left: 20px;">身高：<input id="txtvestheight" type="text" style="width: 50px;"/></label>
                    <label style="margin-left: 30px;display:none;"> 肥胖度：</label>
                    <label id="divvestfat" style="display:none;"></label>
                    <label style="margin-left: 30px;"> <label class="lbstandard"></label>当前评价：</label>
                    <label id="divvestwufat"> </label>
                    <label style="margin-left: 30px;"> 结案日期：</label>
                    <input id="txtenddate" type="text" class="Wdate" style="width: 100px;" datatype="Date" require="true" format="ymd" msg="结案日期不能为空！" maxlength="10"/>
                </p>
            </td>
        </tr>
    </table>
    <div style="color:red;padding:5px;">
        填表说明：
        <p>1.随访要求：幼儿期每月测量一次体重，每 3 个月测量一次身高；学龄前期每季度进行一次体格发育评价。</p>
        <p>2.结案要求：儿童的身高别体重值正常后继续监测 6 个月，不反弹者方可结案。
        </p>
    </div>
    <div style="margin: 10px 0 10px 0;display:none;">
        <input id="txtoldfat" type="hidden"/>
        <input id="txtvestfat" type="hidden"/>
        <input type="button" id="btnglk" class="btn" style="float:right;margin:5px;" value="管理卡"/>
        <input type="button" id="btnsave" class="btn" style="float:right;margin:5px;" value="保存"/>
    </div>
</div>
<script data-main="../../js/zhuanan/fpzacard" src='../../sys/require.min.js'></script>
</body>
</html>
