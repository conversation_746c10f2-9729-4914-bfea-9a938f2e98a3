﻿<!DOCTYPE html>
<html>
<head>
    <title>园所日常体检登记</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <link rel="stylesheet" href="../../css/reset.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <!--<script type="text/javascript">-->
    <!--document.write('<link rel="stylesheet" id="linktheme" href="../../css/' + parent.objdata.curtheme + '.css" type="text/css" media="screen"/>');-->
    <!--</script>-->
    <style type="text/css">
        html, body {
            background: #EAEFF3;
        }

        .ischange {
            border-color: #6587c7;
        }

        .layui-form-label {
            width: 70px;
        }

        .layui-form .layui-form-item {
            margin-bottom: 5px;
        }

        .layui-table-cell {
            overflow: visible;
        }

        .layui-form-select dl dd {
            text-align: left;
        }

    </style>
</head>
<body>
<div class="out-container">
    <div class="marmain">
        <div class="content-medical">
            <div class="layui-form" style="padding: 10px 10px 5px 10px;">
                <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                    <label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 10px;line-height: 28px;"><em>*</em>体检：</label>
                    <div class="layui-input-inline" style="min-height: 30px;width:190px;">
                        <label id="lbmdicalrecord" class="layui-form-label" style="width:auto;padding: 5px 0px 5px 10px;line-height: 28px;"></label>
                    </div>
                </div>
                <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                    <label class="layui-form-label" style="width:auto;padding: 5px 0px 5px 10px;line-height: 28px;">班级名称：</label>
                    <div class="layui-input-inline" style="min-height: 30px;width:170px;">
                        <select lay-filter="sel_class" id="sel_class">
                        </select>
                    </div>
                </div>
                <div class="layui-form-item" style="display: inline-block;vertical-align: top;">
                    <label class="layui-form-label" style="padding: 8px 0px 5px 10px; text-align: left;">幼儿姓名：</label>
                    <div class="layui-input-inline" style="min-height: 30px;width:200px;">
                        <input type="text" placeholder="幼儿姓名 " class="layui-input" id="truename">
                    </div>
                </div>
                <button id="select" class="layui-btn form-search" style="vertical-align: top;">查询</button>
                <button id="btnsetup" class="layui-btn form-search" style="vertical-align: top;display: none;">批量设置</button>
                <button id="btnSaveAll" class="layui-btn form-search" style="vertical-align: top;display: none;">保存</button>
                <button id="btnaddstu" class="layui-btn form-search" style="vertical-align: top;display: none;">添加</button>
                <button id="btnexcel" class="layui-btn form-search" style="vertical-align: top;display: none;">导出</button>
            </div>
        </div>
<!--        <div class="content-medical marmain-cen" style="padding:5px 10px;">-->
<!--&lt;!&ndash;            <span style="margin-top: 16px;margin-left: 30px;position: absolute;color: #c3551e" id="sortips"></span>&ndash;&gt;-->
<!--        </div>-->
        <div class="layui-row marmain-cen">
            <div class="" style="height: 100%;background: #ffffff;">
                <table id="tabelList" lay-filter="test"></table>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/system.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../js/tong/tongpublic.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/function.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../plugin/download/download.min.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../js/richang/mdicaledit.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>
