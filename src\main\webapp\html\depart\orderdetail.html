<!DOCTYPE html>
<html lang="en">
<head>
    <title>Title</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <link rel="stylesheet" href="../../styles/xbcomstyle.css">
    <link rel="stylesheet" href="../../styles/tbstyles.css"/>
    <link rel="stylesheet" href="../../css/reset.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <style>
        .layui-form-label {
            width:80px;
            text-align: left;
            padding: 5px 0px 5px 10px;
            line-height: 28px;
        }
        .layui-input-block {
            width:450px;
            text-align: right;
            padding: 5px 0px 5px 10px;
            line-height: 28px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            word-break: break-all;
        }
        .label-title{
            font-size: 18px;
        }
        .qrcode-text{
            margin: 0 auto;
            display: none;
        }

    </style>
</head>
<body>
<div id="layer-view-box">
    <div style="padding:10px 55px 10px 55px; display: flex; flex-direction: column;">
        <div style="width: auto; color: black;">
            <label class="layui-form-label blue-txt label-title"><br>预约成功</label>
            <div class="layui-input-block">挂号单号<br><span class="blue-txt label-title" id="order_no"></span></div>
        </div>
        <div id="qrcodeimg" style="margin: 10px auto"></div>
        <div class="qrcode-text">到医院挂号窗口出示给工作人员</div>
        <div class="qrcode-text cred">二维码含个人信息,请注意保密</div>

        <div style="width: auto; background-color: #F2F2F2; color: black;">
            <label class="layui-form-label">挂号信息</label>
        </div>
        <div>
            <label class="layui-form-label">就诊人</label>
            <div class="layui-input-block" id="visitor_name"></div>
        </div>
        <div>
            <label class="layui-form-label">就诊日期</label>
            <div class="layui-input-block" id="visitor_time"></div>
        </div>
        <div id="visitor_num_box" style="display: none">
            <label class="layui-form-label">就诊序号</label>
            <div class="layui-input-block" id="visitor_num"></div>
        </div>
        <hr>
        <div>
            <label class="layui-form-label">就诊医院</label>
            <div class="layui-input-block" id="visitor_hospital_name"></div>
        </div>
        <div>
            <label class="layui-form-label">就诊科室</label>
            <div class="layui-input-block" id="dept_name"></div>
        </div>
        <div>
            <label class="layui-form-label">号源类型</label>
            <div class="layui-input-block" id="numbertype"></div>
        </div>
        <div>
            <label class="layui-form-label">取号时间</label>
            <div class="layui-input-block" id="quhao_time"></div>
        </div>
        <div>
            <label class="layui-form-label">取号地点</label>
            <div class="layui-input-block" id="number_address"></div>
        </div>
        <div>
            <label class="layui-form-label">医事服务费</label>
            <div class="layui-input-block" id="service_charge"></div>
        </div>
        <div>
            <label class="layui-form-label">支付方式</label>
            <div class="layui-input-block" id="pay_type"></div>
        </div>
        <div>
            <label class="layui-form-label">退号时间</label>
            <div class="layui-input-block" id="refund_type"></div>
        </div>
        <div>
            <label class="layui-form-label">症状描述</label>
            <div class="layui-input-block" id="description"></div>
        </div>
        <div>
            <label class="layui-form-label">体检机构</label>
            <div class="layui-input-block" id="hospitalname"></div>
        </div>
        <div>
            <label class="layui-form-label">地址</label>
            <div class="layui-input-block" id="hospitaladdress"></div>
        </div>
    </div>
</div>
<script data-main="../../js/depart/orderdetail" src='../../sys/require.min.js'></script>
</body>
</html>