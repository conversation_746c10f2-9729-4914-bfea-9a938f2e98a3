/*新增日期: 2025.06.16
作 者: 朱辽原
修改时间：
修改人：
內容摘要: 家长预约大数据功能
*/
require.config({
    paths: {
        system: '../../sys/system',
        jquery: '../../sys/jquery',
        echarts: '../../plugin/echart-5.1.2/dist/echarts.min',
        echartsgl: '../../plugin/echarts-gl/dist/echarts-gl',
        layui: '../../layui-btkj/layui',
        bianminServiceData: './bianminServiceData',
        common: './common',
        commonUtils: './commonUtils'
    },
    shim: {
        system: {
            deps: ['jquery']
        },
        layui: {
            deps: ['jquery', 'system']
        },
        echarts: {
            deps: ['jquery', 'system']
        },
        echartsgl: {
            deps: ['echarts']
        }
    },
    waitSeconds: 0
})

localStorage.setItem('regional', '海淀区')

const openMock = true

const baseModule = ['jquery', 'echarts', 'common', 'commonUtils', 'system', 'layui', 'echartsgl']

if (openMock) {
    baseModule.push('bianminServiceData')
}

require(baseModule, ($, echarts, common, utils) => {
    const openLog = false

    /**
     * 当前页面展示数据的时间段
     */
    let pageDate = () => {
        const endDate = utils.getFormattedDate()
        const startDate = utils.formatDate(new Date((new Date(endDate)).getTime() - 7 * 24 * 60 * 60 * 1000));
        return { startDate, endDate }
    }
    pageDate = pageDate()

    const drawSpaceMap = {
        openTrend: $('#openTrend')[0], // 每日扫码预约
        appoint: $('#appoint')[0], // 每日扫码预约
        appointBar: $('#appointBar')[0], // 分时段预约量对比
        parentGender: $('#parentGender')[0], // 性别分布
        parentDevice: $('#parentDevice')[0], // 设备分布
        parentAge: $('#parentAge')[0], // 年龄分布
        parentRegion: $('#parentRegion')[0], // 区域分布
        parentRelation: $('#parentRelation')[0], // 关系分布
        childrenGgender: $('#childrenGgender')[0], // 性别分布
        childrenAge: $('#childrenAge')[0], // 年龄分布
        scan: $('#scan')[0], // 扫码量
        bianminServiceCard: $('#bianminServiceCard')[0] // 便民服务牌
    }

    // 定义统计项与 DOM 中 num-peo 文本的映射关系
    const numPeoMap = {
        kindergartenCount: $('#kindergartenCount'), // 幼儿园数
        bianminCardCount: $('#bianminCardCount'), // 便民牌数
        parentScanCount: $('#parentScanCount'), // 家长扫码预约数
        parentCancelCount: $('#parentCancelCount'), // 家长取消预约数
        avgScanCount: $('#avgScanCount'), // 家长人均扫码数
    }

    numPeoMap.kindergartenCount.click(() => {
        let statsData = getStatsData()
        utils.pageJump('w_bianminServiceInfo.html', {
            startDate: pageDate.startDate,
            endDate: pageDate.endDate,
            ...statsData
        }, '便民服务牌数据详细')
    })
    numPeoMap.bianminCardCount.click(() => {
        let statsData = getStatsData()
        utils.pageJump('w_bianminServiceInfo.html', {
            startDate: pageDate.startDate,
            endDate: pageDate.endDate,
            ...statsData
        }, '便民服务牌数据详细')
    })
    numPeoMap.parentScanCount.click(() => {
        let statsData = getStatsData()
        utils.pageJump('bianminServiceUnit_parent_appointment.html', {
            startDate: pageDate.startDate,
            endDate: pageDate.endDate,
            isCancel: 0,
            count: numPeoMap.parentScanCount.text(),
            ...statsData
        }, '便民服务牌数据详细')
    })
    numPeoMap.parentCancelCount.click(() => {
        let statsData = getStatsData()
        utils.pageJump('bianminServiceUnit_parent_appointment.html', {
            startDate: pageDate.startDate,
            endDate: pageDate.endDate,
            isCancel: 1,
            count: numPeoMap.parentCancelCount.text(),
            ...statsData
        }, '便民服务牌数据详细')
    })

    /*
    功能：获取统计卡片数据
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function getStatsData() {
        let statsData = {}
        // 渲染统计项
        for (const key in numPeoMap) {
            statsData[key] = numPeoMap[key].text()
        }
        return statsData
    }

    /*
    功能：渲染统计卡片
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderStats(data) {
        utils.logWithCondition(openLog, data)
        // 渲染统计项
        for (const key in numPeoMap) {
            numPeoMap[key].text(data[key])
        }
    }

    /*
    功能：渲染每日扫码预约趋势折线图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderOpenTrend(data) {
        let myChart = echarts.init(drawSpaceMap['openTrend']);

        // 处理数据 - 仅对当前年份超过当前日期的数据设置为0
        const processedYAxis = data.yAxis.map((value, index) => value);

        const option = {
            tooltip: {
                trigger: 'axis'
            },
            xAxis: {
                type: 'category',
                data: data.xAxis,
                boundaryGap: false, // 使折线从坐标轴起点开始
                name: '日期',
                axisLabel: {
                    interval: 0, // 强制显示所有标签
                    rotate: 20 // 0度
                }
            },
            yAxis: {
                type: 'value',
                name: '人数'
            },
            dataZoom: [
                {
                    type: 'inside',
                    start: 0,
                    end: data.xAxis.length > 7 ? 100 * 7 / data.xAxis.length : 100, // 默认显示7天
                    zoomLock: false
                },
                {
                    type: 'slider',
                    start: 0,
                    end: data.xAxis.length > 7 ? 100 * 7 / data.xAxis.length : 100,
                    handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
                    handleSize: '80%',
                    handleStyle: {
                        color: '#fff',
                        shadowBlur: 3,
                        shadowColor: 'rgba(0, 0, 0, 0.6)',
                        shadowOffsetX: 2,
                        shadowOffsetY: 2
                    }
                }
            ],
            series: [
                {
                    name: '扫码预约数量',
                    type: 'line',
                    data: processedYAxis,
                    smooth: true, // 开启平滑曲线
                    label: {
                        show: true, // 显示数据点数值
                        position: 'top'
                    },
                    lineStyle: {
                        color: '#2ea875' // 折线颜色
                    },
                    itemStyle: {
                        color: '#2ea875' // 数据点颜色
                    },
                    areaStyle: {
                        // 区域阴影配置
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [
                                {
                                    offset: 0,
                                    color: '#abffdc' // 起始透明度
                                },
                                {
                                    offset: 1,
                                    color: '#fff' // 结束透明度
                                }
                            ]
                        }
                    },
                    emphasis: {
                        lineStyle: {
                            width: 4
                        },
                        symbolSize: 8
                    }
                }
            ]
        }
        myChart.setOption(option)
        myChart.off('click');
        myChart.on('click', e => {
            const date = utils.formatDate(e.name) // 6/25 或 2024/6/25
            utils.logWithCondition(openLog, e.data)
            utils.pageJump('bianminServiceUnit_parent_appointment.html', {
                startDate: date,
                endDate: date,
                count: e.data
            }, '便民服务牌数据详细')
        })
    }

    /*
    功能：渲染分小时预约/取消趋势折线图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderAppointChart(data) {
        let myChart = echarts.init(drawSpaceMap['appoint'], null, { renderer: 'div' });

        const option = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    crossStyle: {
                        color: '#999'
                    }
                }
            },
            dataZoom: [
                {
                    type: 'inside',
                    start: 0,
                    end: data.xAxis.length > 7 ? 100 * 7 / data.xAxis.length : 100, // 默认显示7天
                    zoomLock: false
                },
                {
                    type: 'slider',
                    start: 0,
                    end: data.xAxis.length > 7 ? 100 * 7 / data.xAxis.length : 100,
                    handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
                    handleSize: '80%',
                    handleStyle: {
                        color: '#fff',
                        shadowBlur: 3,
                        shadowColor: 'rgba(0, 0, 0, 0.6)',
                        shadowOffsetX: 2,
                        shadowOffsetY: 2
                    }
                }
            ],
            legend: {
                data: ['预约量', '取消量'],
                top: '5%'
            },
            xAxis: {
                type: 'category',
                data: data.xAxis,
                boundaryGap: false, // 使折线从坐标轴起点开始
                name: '时间'
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    formatter: '{value}'
                },
                min: 0,
                axisLine: {
                    show: true
                },
                splitLine: {
                    show: true
                }
            },
            series: [
                {
                    name: '预约量',
                    type: 'line',
                    smooth: true, // 开启平滑曲线
                    data: data.appointData,
                    color: '#2ea875',
                    // symbol: 'circle',
                    symbolSize: 8,
                    lineStyle: {
                        width: 2
                    },
                    itemStyle: {
                        borderWidth: 2
                    },
                    areaStyle: {
                        opacity: 0.2
                    },
                    areaStyle: {
                        // 区域阴影配置
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [
                                {
                                    offset: 0,
                                    color: '#abffdc' // 起始透明度
                                },
                                {
                                    offset: 1,
                                    color: '#fff' // 结束透明度
                                }
                            ]
                        }
                    },
                    emphasis: {
                        lineStyle: {
                            width: 4
                        },
                        symbolSize: 8
                    }
                },
                {
                    name: '取消量',
                    type: 'line',
                    smooth: true, // 开启平滑曲线
                    data: data.cancelData,
                    color: '#f99e35',
                    // symbol: 'circle',
                    symbolSize: 8,
                    lineStyle: {
                        width: 2
                    },
                    itemStyle: {
                        borderWidth: 2
                    },
                    areaStyle: {
                        opacity: 0.2
                    },
                    areaStyle: {
                        // 区域阴影配置
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [
                                {
                                    offset: 0,
                                    color: '#ffc787' // 起始透明度
                                },
                                {
                                    offset: 1,
                                    color: '#fff' // 结束透明度
                                }
                            ]
                        }
                    },
                    emphasis: {
                        lineStyle: {
                            width: 4
                        },
                        symbolSize: 8
                    }
                }
            ]
        }

        myChart.setOption(option)
        myChart.on('click', e => {
            utils.logWithCondition(openLog, e)
            let isCancel = ''
            if (e.seriesName === '预约量') {
                isCancel = '0'
            } else {
                isCancel = '1'
            }
            utils.pageJump('bianminServiceUnit_parent_appointment.html', {
                startDate: pageDate.startDate,
                endDate: pageDate.endDate,
                startTime: e.name.split('-')[0],
                endTime: e.name.split('-')[1],
                count: e.value,
                isCancel
            }, '便民服务牌数据详细')
        })
    }

    /*
    功能：渲染便民服务牌分布地图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function bianminServiceCardChart(gardenList) {
        let myChart = echarts.init(drawSpaceMap['bianminServiceCard'], null, { renderer: 'div' });

        var nameMap = localStorage.getItem('regional')
        var citys = {
            '怀柔区': '../../js/mapdata/110116.json',
            '延庆县': '../../js/mapdata/110229.json',
            '密云县': '../../js/mapdata/110228.json',
            '昌平区': '../../js/mapdata/110114.json',
            '顺义区': '../../js/mapdata/110113.json',
            '平谷区': '../../js/mapdata/110117.json',
            '门头沟区': '../../js/mapdata/110109.json',
            '海淀区': '../../js/mapdata/110108.json',
            '朝阳区': '../../js/mapdata/110105.json',
            '石景山区': '../../js/mapdata/110107.json',
            '西城区': '../../js/mapdata/110102.json',
            '东城区': '../../js/mapdata/110101.json',
            '丰台区': '../../js/mapdata/110106.json',
            '通州区': '../../js/mapdata/110112.json',
            '房山区': '../../js/mapdata/110111.json',
            '大兴区': '../../js/mapdata/110115.json'
        };
        var uploadedDataURL = citys[nameMap]

        // 加载地图
        loadMap(uploadedDataURL, nameMap)

        /**
         获取对应的json地图数据，然后向echarts注册该区域的地图，最后加载地图信息
         @params {String} mapCode:json数据的地址
         @params {String} name: 地图名称
         */
        function loadMap(mapCode, name) {
            const gardenData = gardenList.filter(item => nameMap == item.regional)
            gardenData.map(item => item.value = item.geo)

            $.get(mapCode, function (data) {
                if (data) {
                    data.features[0].properties.name = data.features[0].properties.name.replace("北京市", "");
                    echarts.registerMap(name, data)
                    var option = {
                        backgroundColor: '#15184d',
                        tooltip: {
                            trigger: 'item',
                            show: true,
                            enterable: true,
                            textStyle: {
                                fontSize: 13,
                                color: '#fff'
                            },
                            backgroundColor: 'rgba(0, 2, 102, 0.8)',
                            formatter: params => {
                                utils.logWithCondition(openLog, params)
                                if (params.data) {
                                    return `
                                    <span style="width:195px;height:35px;line-height:28px;float:left;">&nbsp;&nbsp;
                                    ${params.data.name}
                                    </span></br>
                                    <span style="float:left;magin-top:50px;width:195px;height:35px;line-height:25px;">&nbsp;&nbsp;
                                    家长扫码：${params.data['parentScanCount']}
                                    </span></br>
                                    <span style="float:left;magin-top:50px;width:195px;height:35px;line-height:25px;">&nbsp;&nbsp;
                                    预约成功：${params.data['appointmentSuccessful']}
                                    </span></br>
                                    <span style="float:left;magin-top:50px;width:195px;height:35px;line-height:25px;">&nbsp;&nbsp;
                                    取消预约：${params.data['appointmentCancel']}
                                    </span></br>
                                `;
                                }
                                return ''
                            },
                            extraCssText:
                                "background:url('https://www.isqqw.com/asset/get/s/data-1630478118371-aR5gezvxy.png') 100% 100% repeat;width:195px;height:142px;"
                        },
                        geo: [
                            {
                                map: name,
                                aspectScale: 1,
                                zoom: 0.56,
                                layoutCenter: ['50%', '50%'],
                                layoutSize: '180%',
                                show: true,
                                roam: false,
                                label: {
                                    emphasis: {
                                        show: false
                                    }
                                },
                                itemStyle: {
                                    normal: {
                                        borderColor: '#c0f3fb',
                                        borderWidth: 1,
                                        shadowColor: '#8cd3ef',
                                        shadowOffsetY: 10,
                                        shadowBlur: 120,
                                        areaColor: 'transparent'
                                    }
                                }
                            },
                            // 重影
                            {
                                type: 'map',
                                map: name,
                                zlevel: -1,
                                aspectScale: 1,
                                zoom: 0.56,
                                layoutCenter: ['50%', '51%'],
                                layoutSize: '180%',
                                roam: false,
                                silent: true,
                                itemStyle: {
                                    normal: {
                                        borderWidth: 1,
                                        // borderColor:"rgba(17, 149, 216,0.6)",
                                        borderColor: 'rgba(58,149,253,0.8)',
                                        shadowColor: 'rgba(172, 122, 255,0.5)',
                                        shadowOffsetY: 5,
                                        shadowBlur: 15,
                                        areaColor: 'rgba(5,21,35,0.1)'
                                    }
                                }
                            },
                            {
                                type: 'map',
                                map: name,
                                zlevel: -2,
                                aspectScale: 1,
                                zoom: 0.56,
                                layoutCenter: ['50%', '52%'],
                                layoutSize: '180%',
                                roam: false,
                                silent: true,
                                itemStyle: {
                                    normal: {
                                        borderWidth: 1,
                                        // borderColor: "rgba(57, 132, 188,0.4)",
                                        borderColor: 'rgba(58,149,253,0.6)',
                                        shadowColor: 'rgba(65, 214, 255,1)',
                                        shadowOffsetY: 5,
                                        shadowBlur: 15,
                                        areaColor: 'transpercent'
                                    }
                                }
                            },
                            {
                                type: 'map',
                                map: name,
                                zlevel: -3,
                                aspectScale: 1,
                                zoom: 0.56,
                                layoutCenter: ['50%', '53%'],
                                layoutSize: '180%',
                                roam: false,
                                silent: true,
                                itemStyle: {
                                    normal: {
                                        borderWidth: 1,
                                        // borderColor: "rgba(11, 43, 97,0.8)",
                                        borderColor: 'rgba(58,149,253,0.4)',
                                        shadowColor: 'rgba(58,149,253,1)',
                                        shadowOffsetY: 15,
                                        shadowBlur: 10,
                                        areaColor: 'transpercent'
                                    }
                                }
                            },
                            {
                                type: 'map',
                                map: name,
                                zlevel: -4,
                                aspectScale: 1,
                                zoom: 0.56,
                                layoutCenter: ['50%', '54%'],
                                layoutSize: '180%',
                                roam: false,
                                silent: true,
                                itemStyle: {
                                    normal: {
                                        borderWidth: 5,
                                        // borderColor: "rgba(11, 43, 97,0.8)",
                                        borderColor: 'rgba(5,9,57,0.8)',
                                        shadowColor: 'rgba(29, 111, 165,0.8)',
                                        shadowOffsetY: 15,
                                        shadowBlur: 10,
                                        areaColor: 'rgba(5,21,35,0.1)'
                                    }
                                }
                            }
                        ],
                        series: [
                            {
                                name: 'Map',
                                type: 'map',
                                mapType: name,
                                aspectScale: 1,
                                zoom: 0.56, // 缩放
                                layoutCenter: ['50%', '50%'],
                                layoutSize: '180%',
                                showLegendSymbol: true,
                                label: {
                                    normal: {
                                        show: true,
                                        textStyle: {
                                            color: '#fff',
                                            fontSize: '100%'
                                        }
                                    },
                                    emphasis: {
                                        show: true,
                                    }
                                },
                                itemStyle: {
                                    normal: {
                                        areaColor: {
                                            type: 'linear',
                                            x: 1200,
                                            y: 0,
                                            x2: 0,
                                            y2: 0,
                                            colorStops: [
                                                {
                                                    offset: 0,
                                                    color: 'rgba(3,27,78,0.75)' // 0% 处的颜色
                                                },
                                                {
                                                    offset: 1,
                                                    color: 'rgba(58,149,253,0.75)' // 50% 处的颜色
                                                }
                                            ],
                                            global: true // 缺市为 false
                                        },
                                        borderColor: '#fff',
                                        borderWidth: 0.2
                                    },
                                    emphasis: {
                                        show: false,
                                        color: '#fff',
                                        areaColor: 'rgba(0,254,233,0.6)'
                                    }
                                },
                                markPoint: {
                                    symbol: 'none'
                                }
                            },
                            {
                                type: 'scatter', // 使用散点图系列来显示标点
                                coordinateSystem: 'geo', // 依赖于geo坐标系
                                symbol: 'pin', //气泡
                                data: gardenData,
                                label: {
                                    show: true,  // 直接配置 show，移除 normal 嵌套
                                    position: 'top',  // 标签位置
                                    formatter: function (params) {
                                        // return params.data.gardenName;
                                        return '';
                                    },
                                    color: '#fff',  // 标签文本颜色
                                    fontSize: 14    // 标签文本大小
                                },
                                symbolSize: 30, // 标点大小
                                itemStyle: {
                                    color: '#fff' // 标点颜色
                                }
                            }
                        ]
                    }
                    myChart.clear()
                    myChart.setOption(option, true)
                } else {
                    alert('无法加载该地图')
                }
            })
        }

        myChart.off('click');
        myChart.on('click', function (params) {
            if (params.data && params.data.value) {
                utils.logWithCondition(openLog, params.data)
                utils.pageJump('w_bianminServiceInfo.html', {
                    ...params.data,
                    parentCancelCount: params.data.appointmentCancel,
                    kindergartenId: params.data.id
                }, '便民服务牌数据详细')
            }
        });
    }

    /*
    功能：渲染分时段预约量对比柱状图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderAppointBarData(data) {
        let myChart = echarts.init(drawSpaceMap['appointBar'], null, { renderer: 'div' });

        var colorConfig = [
            ['#1ae9a3'], // 顶面：蓝色
            ['#1acf92', '#aae4d0'], // 左侧面：深蓝渐变
            ['#17de9b', '#a9f4db'] // 右侧面：浅蓝渐变
        ]

        var options = {
            labelRotate: 45,
            labelInterval: 0
        }

        var option = common.getBar3D(data.xAxis, data.yAxis, 'AppointBar', colorConfig, options)
        option.dataZoom = [
            {
                type: 'inside',
                start: 0,
                end: data.xAxis.length > 7 ? 100 * 7 / data.xAxis.length : 100, // 默认显示7天
                zoomLock: false
            },
            {
                type: 'slider',
                start: 0,
                end: data.xAxis.length > 7 ? 100 * 7 / data.xAxis.length : 100,
                handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
                handleSize: '80%',
                handleStyle: {
                    color: '#fff',
                    shadowBlur: 3,
                    shadowColor: 'rgba(0, 0, 0, 0.6)',
                    shadowOffsetX: 2,
                    shadowOffsetY: 2
                }
            }
        ]
        myChart.setOption(option)
        myChart.off('click');
        myChart.on('click', e => {
            utils.logWithCondition(openLog, e.name, e.value)
            let startTime = `${e.name.split('-')[0]}:00`
            let endTime = `${e.name.split('-')[1]}:00`
            utils.pageJump('bianminServiceUnit_parent_appointment.html', {
                startDate: pageDate.startDate,
                endDate: pageDate.endDate,
                startTime: startTime,
                endTime: endTime,
                count: e.value
            }, '便民服务牌数据详细')
        })
    }

    /*
    功能：渲染幼儿园扫码量水平柱状图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    /**
     * 渲染3D扫描图表
     * @param {Object} data - 图表数据对象，包含values和labels属性
     * @param {Array} data.values - 图表数值数组
     * @param {Array} data.labels - 图表标签数组
     * @description 使用echarts初始化3D柱状图，并设置点击事件跳转页面
     */
    function renderScanChart(data) {
        utils.logWithCondition(openLog, data)
        setTimeout(() => {
            let myChart = echarts.init(drawSpaceMap['scan'], null, { renderer: 'div' });

            // 生成图表配置
            var option = common.getLevelBar3D(
                data.values,
                data.labels.map(item => item.name),
                'my3D',
                [
                    ['#1ae9a3'], // 顶面：蓝色
                    ['#a9f4db', '#17de9b'], // 右侧面：浅蓝渐变
                    ['#aae4d0', '#1acf92'] // 左侧面：深蓝渐变
                ],
                {
                    labelFormatter: function (value) {
                        return value + '人'
                    },
                    xAxisFormatter: function (value) {
                        return value + '人'
                    },
                    offsetX: 8,
                    offsetY: 20
                }
            )

            myChart.setOption(option)
            myChart.off('click');
            myChart.on('click', e => {
                let statsData = getStatsData()
                utils.pageJump('w_bianminServiceInfo.html', {
                    startDate: pageDate.startDate,
                    endDate: pageDate.endDate,
                    kinderCount: e.value,
                    kindergartenId: data.labels.filter(item => item.name == e.name)[0].id,
                    ...statsData
                }, '便民服务牌数据详细')
            })
        }, 0)
    }

    /*
    功能：渲染渲染性别分布情况饼图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderGenderChart(data) {

        const itemStyleMap = {
            男性: {
                color: '#fea131'
            },
            女性: {
                color: '#f54d48'
            }
        }
        // 使用common模块的get3dPie函数生成3D饼图配置
        const options = {
            title: '性别'
        }
        data.map(item => (item.itemStyle = itemStyleMap[item.name]))

        let myChart = common.create3DPie(drawSpaceMap['parentGender'].id, data, options)
        myChart.off('click');
        myChart.on('click', e => {
            utils.logWithCondition(openLog, e)
            let gender = '1'
            if (e.seriesName == '女性') {
                gender = '2'
            }
            utils.pageJump('w_parentAppointment_parent_1.html', {
                startDate: pageDate.startDate,
                endDate: pageDate.endDate,
                parentGender: gender,
                count: data[e.componentIndex].value
            }, '便民服务牌数据详细')
        })
    }

    /*
    功能：渲染访问设备分布情况饼图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderDeviceChart(data) {
        const itemStyleMap = {
            安卓: {
                color: '#fea033'
            },
            iOS: {
                color: '#8485fe'
            },
            未知: {
                color: '#f54d48'
            }
        }
        // 使用common模块的get3dPie函数生成3D饼图配置
        const options = {
            title: '访问设备'
        }
        data.map(item => (item.itemStyle = itemStyleMap[item.name]))
        let deviceChart = common.create3DPie(drawSpaceMap['parentDevice'].id, data, options)
        deviceChart.off('click');
        deviceChart.on('click', e => {
            utils.vlogWithCondition(openLog, e)
            let deviceType = '安卓'
            if (e.seriesName === 'iOS') {
                deviceType = '苹果'
            } else if (e.seriesName === '未知') {
                deviceType = '其他'
            }
            utils.pageJump('w_parentAppointment_parent_1.html', {
                startDate: pageDate.startDate,
                endDate: pageDate.endDate,
                deviceType,
                count: data[e.componentIndex].value
            }, '便民服务牌数据详细')

        })
    }

    /*
    功能：渲染访问年龄分布情况饼图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderAgeChart(data) {
        const itemStyleMap = {
            '18岁到25岁': {
                color: '#ffd15d'
            },
            '26岁到35岁': {
                color: '#08d0fd'
            },
            '36岁到45岁': {
                color: '#20e8a4'
            },
            '46岁到60岁': {
                color: '#0883fe'
            },
            '60岁以上': {
                color: '#f54e46'
            },
            未知: {
                color: '#8583fe'
            }
        }
        // 使用common模块的get3dPie函数生成3D饼图配置
        const options = {
            title: '年龄分布'
        }
        data.map(item => (item.itemStyle = itemStyleMap[item.name]))
        let ageChart = common.create3DPie(drawSpaceMap['parentAge'].id, data, options);
        ageChart.off('click')
        ageChart.on('click', e => {
            utils.logWithCondition(openLog, e)
            utils.pageJump('w_parentAppointment_parent_1.html', {
                startDate: pageDate.startDate,
                endDate: pageDate.endDate,
                parentAge: e.name[0],
                count: data[e.componentIndex].value
            }, '便民服务牌数据详细')
        })
    }

    /*
    功能：渲染访问区域分布情况雷达图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderRegionChart(data) {
        let regionChart = echarts.init(drawSpaceMap['parentRegion'], null, { renderer: 'div' });

        try {
            const indicator = []
            for (let i = 0; i < 18; i++) {
                indicator.push({
                    name: '',
                    max: 100
                })
            }

            const option = {
                // backgroundColor: "rgba(114, 184, 255, 0.5)",
                title: {
                    text: '区域分布',
                    left: '5px',
                    top: '5px',
                    textStyle: {
                        color: '#333',
                        fontSize: 14
                    }
                },
                grid: {
                    top: '10%'
                },
                tooltip: {
                    show: true,
                    trigger: 'item',
                    // 自定义tooltip内容
                    formatter: function (params) {
                        utils.logWithCondition(openLog, data.areas[params.componentIndex])
                        utils.logWithCondition(openLog, params.componentIndex)

                        return `家长数：${data.areas[params.componentIndex].parentCount}<br>
								幼儿数：${data.areas[params.componentIndex].childCount}`
                    }
                },
                radar: {
                    // 雷达图起始角度（关键配置）
                    startAngle: 180, // 从0度开始（默认是90度，即从正上方开始）
                    splitNumber: 3,
                    center: ['50%', '50%'],
                    radius: '70%',
                    shape: 'circle',
                    splitArea: {
                        show: true,
                        areaStyle: {
                            // 渐变背景色，从中心向外扩散
                            color: [
                                'rgba(114, 184, 255, 0.8)', // 最内部区域
                                'rgba(114, 184, 255, 0.6)', // 第二层
                                'rgba(114, 184, 255, 0.4)' // 第三层
                            ]
                        }
                    },
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(114, 184, 255)'
                        }
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: [
                                'rgba(114, 184, 255, 0.5)', // 最内部区域
                                'rgba(114, 184, 255, 0.5)', // 第二层
                                'rgba(114, 184, 255, 0.4)' // 第三层
                            ]
                        }
                    },
                    indicator
                },
                series: [
                    {
                        type: 'radar',
                        symbol: 'circle',
                        symbolSize: 6,
                        itemStyle: {
                            normal: {}
                        },
                        areaStyle: {
                            normal: {
                                color: 'rgba(114, 184, 255, 0.5)',
                                opacity: 0.2
                            }
                        },
                        lineStyle: {
                            normal: {}
                        },
                        label: {
                            normal: {
                                show: true,
                                color: '#a4a4a4',
                                position: 'top',
                                fontSize: 12,
                                fontWeight: 200,
                                formatter: params => {
                                    return '1KM'
                                }
                            }
                        },
                        data: [33]
                    },
                    {
                        type: 'radar',
                        symbol: 'circle',
                        symbolSize: 6,
                        itemStyle: {
                            normal: {}
                        },
                        areaStyle: {
                            normal: {
                                color: 'rgba(114, 184, 255, 0.5)'
                            }
                        },
                        lineStyle: {},
                        label: {
                            normal: {
                                show: true,
                                color: '#a4a4a4',
                                position: 'top',
                                fontSize: 13,
                                fontWeight: 200,
                                formatter: params => {
                                    return '3KM'
                                }
                            }
                        },
                        data: [66]
                    },
                    {
                        type: 'radar',
                        symbol: 'circle',
                        symbolSize: 6,
                        itemStyle: {
                            normal: {}
                        },
                        areaStyle: {
                            normal: {
                                color: 'rgba(114, 184, 255, 0.5)'
                            }
                        },
                        lineStyle: {
                            normal: {}
                        },
                        label: {
                            normal: {
                                show: true,
                                color: '#a4a4a4',
                                position: 'top',
                                fontSize: 15,
                                fontWeight: 200,
                                formatter: params => {
                                    return '5KM'
                                }
                            }
                        },
                        data: [99]
                    }
                ]
            }
            regionChart.setOption(option)
        } catch (e) {
            console.error('初始化图表失败:', e)
        }
        regionChart.off('click');
        regionChart.on('click', e => {
            utils.logWithCondition(openLog, e.componentIndex)
            utils.pageJump('w_parentAppointment_parent_1.html', {
                startDate: pageDate.startDate,
                endDate: pageDate.endDate,
                areaDistribution: e.componentIndex + 1,
                count: data.areas[e.componentIndex].parentCount + data.areas[e.componentIndex].childCount
            }, '便民服务牌数据详细')
        })
    }

    /*
    功能：渲染孩子与家长关系柱状图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderChildParentRelationChart(data) {
        // 初始化 ECharts 实例
        let myChart = echarts.init(drawSpaceMap['parentRelation'], null, { renderer: 'div' });


        var colorConfig = [
            ['#42A5F5'], // 顶面：蓝色
            ['#1976D2', 'rgba(25,118,210,0.3)'], // 左侧面：深蓝渐变
            ['#64B5F6', 'rgba(100,181,246,0.3)'] // 右侧面：浅蓝渐变
        ]
        var options = {
            labelRotate: 45,
            labelInterval: 0
        }
        var option = common.getBar3D(data.xAxis, data.data, 'Relation', colorConfig, options)
        myChart.setOption(option)
        myChart.off('click');
        myChart.on('click', e => {
            utils.logWithCondition(openLog, e)
            utils.pageJump('w_parentAppointment_parent_1.html', {
                startDate: pageDate.startDate,
                endDate: pageDate.endDate,
                parentRelation: e.dataIndex + 1,
                count: e.data
            }, '便民服务牌数据详细')
        })
    }

    /*
    功能：渲染孩子性别分布饼图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderChildGenderChart(data) {
        const itemStyleMap = {
            男性: {
                color: '#fea131'
            },
            女性: {
                color: '#f54d48'
            }
        }
        // 使用common模块的get3dPie函数生成3D饼图配置
        const options = {
            title: '性别',
            boxHeight: 0.03,
            distance: 230
        }
        data.map(item => (item.itemStyle = itemStyleMap[item.name]))
        let myChart = common.create3DPie(drawSpaceMap['childrenGgender'].id, data, options)
        myChart.off('click');
        myChart.on('click', e => {
            utils.logWithCondition(openLog, e)
            let gender = '1'
            if (e.seriesName == '女性') {
                gender = '2'
            }
            utils.pageJump('w_parentAppointment_child.html', {
                startDate: pageDate.startDate,
                endDate: pageDate.endDate,
                childGender: gender,
                count: data[e.componentIndex].value
            }, '便民服务牌数据详细')
        })
    }

    /*
    功能：渲染孩子年龄分布饼图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderChildAgeChart(data) {
        const itemStyleMap = {
            '3岁': {
                color: '#ffd15d'
            },
            '4岁': {
                color: '#08d0fd'
            },
            '5岁': {
                color: '#20e8a4'
            },
            '6岁': {
                color: '#0883fe'
            },
            '7岁': {
                color: '#f54e46'
            }
        }
        // 使用common模块的get3dPie函数生成3D饼图配置
        const options = {
            title: '年龄分布'
        }
        data.map(item => (item.itemStyle = itemStyleMap[item.name]))
        let ageChart = common.create3DPie(drawSpaceMap['childrenAge'].id, data, options)
        ageChart.off('click');
        ageChart.on('click', e => {
            utils.logWithCondition(openLog, e)
            utils.pageJump('w_parentAppointment_child.html', {
                startDate: pageDate.startDate,
                endDate: pageDate.endDate,
                physicalAge: e.name[0],
                count: data[e.componentIndex].value
            }, '便民服务牌数据详细')
        })
    }

    layui.config().extend({ //设定模块别名
        system: '../../sys/system',
    });
    // 页面初始化
    layui.use(['system', 'form', 'table', 'laydate'], function () {
        laydate = layui.laydate;
        const param = {
            startDate: pageDate.startDate,
            endDate: pageDate.endDate,
            regional: localStorage.getItem('regional')
        }

        const tasks = [
            () => utils.smactionMockData('/bianminService/stats', param, renderStats),
            () => utils.smactionMockData('/bianminService/openTrendData', param, renderOpenTrend),
            () => utils.smactionMockData('/bianminService/bianminServiceCard', param, bianminServiceCardChart),
            () => utils.smactionMockData('/bianminService/getAppointTrend', param, renderAppointChart),
            () => utils.smactionMockData('/bianminService/getAppointBarData', param, renderAppointBarData),
            () => utils.smactionMockData('/bianminService/scan', param, renderScanChart),
            () => utils.smactionMockData('/bianminService/gender', param, renderGenderChart),
            () => utils.smactionMockData('/bianminService/age', param, renderAgeChart),
            () => utils.smactionMockData('/bianminService/device', param, renderDeviceChart),
            () => utils.smactionMockData('/bianminService/region', param, renderRegionChart),
            () => utils.smactionMockData('/bianminService/relationship', param, renderChildParentRelationChart),
            () => utils.smactionMockData('/bianminService/gender', param, renderChildGenderChart),
            () => utils.smactionMockData('/bianminService/childAge', param, renderChildAgeChart)
        ]

        // 加载初始数据
        function loadData() {
            const sheduler = runChunk => {
                setTimeout(() => {
                    let count = 0
                    runChunk(() => count++ < 1)
                }, 100)
            }
            utils.performTask(tasks, sheduler)
        }

        // 初始加载
        loadData()

        // 初始化日期选择器
        initDatePicker();

        /*
         * 初始化日期选择器
         */
        function initDatePicker() {
            // 开始日期
            laydate.render({
                elem: '#startDate',
                type: 'date',
                value: param.startDate,
                done: function (data) {
                    param.startDate = data;
                    pageDate.startDate = data
                }
            });

            // 结束日期
            laydate.render({
                elem: '#endDate',
                type: 'date',
                value: param.endDate,
                done: function (data) {
                    param.endDate = data;
                    pageDate.endDate = data
                }
            });
        }

        /**
        * 绑定事件
        */
        function initEvent() {
            $("#btnSearch").click(function () {
                loadData()
            });
        }
        initEvent()
    })

    /*
    功能：图表窗口自适应
    参数说明：无
    返回值说明：无
    */
    function resizeCharts() {
        for (let key in drawSpaceMap) {
            const dom = drawSpaceMap[key]
            const echartsDom = echarts.getInstanceByDom(dom)
            if (echartsDom) {
                setTimeout(() => {
                    echartsDom.resize()
                }, 0)
            }
        }
    }

    // 监听窗口大小变化，自适应图表
    window.addEventListener('resize', resizeCharts)
})