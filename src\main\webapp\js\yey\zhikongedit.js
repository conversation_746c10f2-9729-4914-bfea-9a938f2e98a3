﻿/*
日期：2019-
作者：
功能：
*/
var objdata = {
    uploaddir: "peixun/lecturer"
};

require.config({
    paths: {
        jquery: '../../sys/jquery',
        system: '../../sys/system',
        layuicommon: "../../sys/layuicommon",
        layui: "../../layui-btkj/layui",
        "xm-select": "../../plugin/xm-select/xm-select"
    },
    shim: {
        "system": {
            deps: ["jquery"]
        },
        "layuicommon": {
            deps: ["jquery", "layui"]
        }
    },
    waitSeconds: 0
});

require(["jquery", "system", "xm-select", 'layui', "layuicommon"], function () {
    layui.use(['form', 'laydate'], function () {
        var form = layui.form;
        form.render();
        form.verify({
            // requiredyeyid: function (value) {
            //     if (!value || value == 0) {
            //         return '请选择幼儿园';
            //     }
            // }
        });
        $('#form').on('blur', '[lay-verify]', function () {
            layformblur($(this), form);
        });
        $("#btnok").click(function (event, callback) {
            form.submit('formOk', function () {
                saveEvent(callback);
                return false;
            });
            return false;
        });
        if (Arg("type") == "view") {
            $(".isview").show();
        } else {
            $(".isedit").show();
        }
        getyeydata(function () {
            if (Arg("id") && (Arg("type") == "edit" || Arg("type") == "view")) { // 编辑
                getEditInfo();
            }
        });
        initEvent();
    });
});

function initEvent() {
    $(".txtscore").on("blur", function () {
        var score = 0;
        $(".txtscore").each(function () {
            score = parseFloat($(this).val() || 0).add(score);
        });
        $("#txtscore").val(score);
    });
    $('#iconverifieddate').click(function (e) {
        var show = true;
        if (document.activeElement.id == "txtverifieddate") {
            show = false;
        }
        var objdate = {
            elem: '#txtverifieddate',
            show: show, //直接显示
            needrender: true,
            isInitValue: true,
            showBottom: true,
            done: function () {
                $('#txtverifieddate').blur();
            }
        }
        layui.laydate.render(objdate);
        return false;
    });
    $('#txtverifieddate').focus(function (e) {
        setTimeout(function () {
            $('#iconverifieddate').trigger("click");
        }, 100);
    });
    $('#txtverifieddate').val(jQuery.getparent().objdata.strtoday);
}

/**
 * 获取园所信息
 * @param cb
 */
function getyeydata(cb) {
    var guid = "";
    if ($("#selarea").val()) {
        guid = $("#selarea").val();
    } else {
        guid = jQuery.getparent().objdata.fyinfo[1];
    }
    var objwhere = {};
    if (guid == jQuery.getparent().objdata.fyinfo[1]) {
        objwhere.yey_id = [jQuery.getparent().objdata.my.yeyid || 0];
    }
    objwhere._yeytype = [3];
    if (guid) {
        $.sm(function (re, err) {
            if (err) {
                jQuery.getparent().layer.msg("读取信息出错", {icon: 5});
            } else {
                objdata.objyey = {};
                var arrdata = [];
                for (var i = 0; i < re.length; i++) {
                    objdata.objyey[re[i].id] = re[i];
                    arrdata.push({name: re[i].yeyname, value: re[i].id});
                }
                objdata.selyey = xmSelect.render({
                    el: '#selyeyid',
                    theme: {
                        color: jQuery.getparent().objtheme[jQuery.getparent().objdata.curtheme],
                    },
                    filterable: true,
                    data: arrdata,
                    radio: true,
                    on: function (data) {
                        //arr:  当前多选已选中的数据
                        var arr = data.arr;
                        //change, 此次选择变化的数据,数组
                        var change = data.change;
                        //isAdd, 此次操作是新增还是删除
                        var isAdd = data.isAdd;
                        // $("#txtyeyid").val(arr.length);
                        // alert('已有: '+arr.length+' 变化: '+change.length+', 状态: ' + isAdd)
                    },
                });
                // if (jQuery.getparent().objdata.my.yeyid && jQuery.getparent().objdata.my.yeyid != 0) {
                //     objdata.selyey.setValue([jQuery.getparent().objdata.my.yeyid]);
                // }
                cb && cb();
            }
        }, ["yey.getyey", guid, $.msgwhere(objwhere)]);
    }
}

/**
 * 编辑信息
 */
function getEditInfo() {
    $.sm(function (re, err, obj) {
        if (obj) {
            if (Arg("type") == "edit") { // 编辑
                objdata.selyey.setValue([obj.yeyid]);
                $("#txtyingjian").val(obj.yingjian || "");
                $("#txtshenghuo").val(obj.shenghuo || "");
                $("#txtyinshi").val(obj.yinshi || "");
                $("#txtjibing").val(obj.jibing || "");
                $("#txtxinli").val(obj.xinli || "");
                $("#txtzhibiao").val(obj.zhibiao || "");
                $("#txtfujia").val(obj.fujia || "");
                $("#txtscore").val(obj.score || "");
                $("#txtoverall").val(obj.overall || "");
                $("#txtquestion").val(obj.question || "");
                $("#txtverifieddate").val(obj.verifieddate || "");
                $("#txtpersons").val(obj.persons || "");
            } else if (Arg("type") == "view") {
                var objyey = objdata.objyey[obj.yeyid];
                $("#lbyeyid").text(objyey.yeyname || "");
                $("#lbyingjian").text(obj.yingjian || "");
                $("#lbshenghuo").text(obj.shenghuo || "");
                $("#lbyinshi").text(obj.yinshi || "");
                $("#lbjibing").text(obj.jibing || "");
                $("#lbxinli").text(obj.xinli || "");
                $("#lbzhibiao").text(obj.zhibiao || "");
                $("#lbfujia").text(obj.fujia || "");
                $("#lbscore").text(obj.score || "");
                $("#lboverall").html(obj.overall || "");
                $("#lbquestion").html(obj.question || "");
                $("#lbverifieddate").text(obj.verifieddate || "");
                $("#lbpersons").text(obj.persons || "");
            }
            layui.form.render();
        } else {
            jQuery.getparent().layer.msg("读取信息出错");
        }
    }, ["zhikongedit.detail", Arg("id")]);
}

/**
 * 添加
 */
function saveEvent(callback) {
    var yeyid = objdata.selyey.getValue('valueStr');
    if (!yeyid) {
        return jQuery.getparent().layer.msg("请选择幼儿园", {icon: 5});
    }
    var yingjian = $("#txtyingjian").val();
    var shenghuo = $("#txtshenghuo").val();
    var yinshi = $("#txtyinshi").val();
    var jibing = $("#txtjibing").val();
    var xinli = $("#txtxinli").val();
    var zhibiao = $("#txtzhibiao").val();
    var fujia = $("#txtfujia").val();
    var score = $("#txtscore").val();
    var overall = $("#txtoverall").val();
    var question = $("#txtquestion").val();
    var persons = $("#txtpersons").val();
    var verifieddate = $("#txtverifieddate").val();
    var my = parent.objdata.my || {};
    jQuery.getparent().layer.load();
    $.sm(function (re, err) {
        jQuery.getparent().layer.closeAll('loading');
        if (re && !err) {
            jQuery.getparent().layer.msg("保存成功！", {icon: 1});
            callback && callback();
        } else {
            jQuery.getparent().layer.msg("添加出错！");
        }
    }, ["zhikongedit.add", (Arg("id") || 0), yeyid, my.areacode || '', my.organname || '', my.username || '', verifieddate, yingjian, shenghuo, yinshi, jibing, xinli, zhibiao, fujia, score, overall, question, persons]);
}