<!DOCTYPE html>
<html>
<head>
    <title>食谱管理</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <link rel="stylesheet" href="../../css/reset.css">
    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="../../layui-btkj/css/layui.css">
    <link rel="stylesheet" href="../../css/commonstyle-layui-btkj.css">
    <link rel="stylesheet" href="../../css/medical.css">
    <style>
        html, body {
            background: #EAEFF3;
            overflow-y: hidden;
        }

        .layui-form-label {
            line-height:28px;
        }
    </style>
</head>
<body>

<div class="marmain">
    <div class="content-medical">
        <div class="layui-form layui-comselect" style="position: relative; ">
            <span class="layui-form-label" style="width: 60px;">关键字：</span>
            <div class="layui-input-inline" style="float: left;">
                <input id="txtkey" type="text" placeholder="幼儿园名称/食谱名称" class="layui-input">
            </div>
            <span class="layui-form-label" style="width: 60px;">状态：</span>
            <div class="layui-input-inline" style="min-height: 30px;width: 120px;float: left;">
                <select id="selispublic">
                    <option value="">请选择状态</option>
                    <option value="1">已公示</option>
                    <option value="0">未公示</option>
                </select>
            </div>
            <span class="layui-form-label" style="width: 100px;">创建时间：</span>
            <div class="layui-input-inline" style="float: left; width: 135px;">
                <input autocomplete="off" class="layui-input form-ordernum" type="text" id="txtstartdate" placeholder="开始时间" style="width:130px; display: inline-block;padding-left:10px;text-align: left;">
                <i class="layui-alendar"><img id="iconstartdate" src="../../images/recipes/cal_icon.png" style="cursor: pointer;position: relative;top: 0px;width: 20px;left: 93px;"></i>
            </div>
            <span style="display: inline-block;vertical-align: top;margin-top:8px;color: #e6e6e6;margin-right:5px;float: left;">—</span>
            <div class="layui-input-inline" style="float: left;width: 135px;">
                <input autocomplete="off" class="layui-input form-ordernum" type="text" id="txtenddate" placeholder="结束时间" style="width:130px; display: inline-block;padding-left:10px;text-align: left;">
                <i class="layui-alendar"><img id="iconenddate" src="../../images/recipes/cal_icon.png" style="cursor: pointer;position: relative;top:0px;width: 20px;left: 93px;"></i>
            </div>
            <button id="btnsearch" class="layui-btn blockcol  mgl-20" style="margin-left: 10px;">查询</button>
        </div>
    </div>
    <div id="content" style="width: 100%;">
        <div class="marmain-cen">
            <div class="content-medical">
                <div class="tbmargin">
                    <table id="laytable" lay-filter="laytable" class="layui-table">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var v = top.version;
    document.write("<script type='text/javascript' src='../../sys/jquery.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../sys/arg.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../layui-btkj/layui.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../js/system/operationlog.js?v=" + v + "'><" + "/script>");
    document.write("<script type='text/javascript' src='../../js/recipe/chdcrecipelist.js?v=" + v + "'><" + "/script>");
</script>
</body>
</html>