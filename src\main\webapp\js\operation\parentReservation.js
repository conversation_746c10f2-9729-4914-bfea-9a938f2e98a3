/*新增日期: 2025.06.16
作 者: 朱辽原
修改时间：
修改人：
內容摘要: 家长预约大数据功能
*/
require.config({
    paths: {
        system: '../../sys/system',
        jquery: '../../sys/jquery',
        echarts: '../../plugin/echart-5.1.2/dist/echarts.min',
        echartsgl: '../../plugin/echarts-gl/dist/echarts-gl',
        layui: '../../layui-btkj/layui',
        parentReservationData: './parentReservationData',
        common: './common',
        commonUtils: './commonUtils'
    },
    shim: {
        system: {
            deps: ['jquery']
        },
        layui: {
            deps: ['jquery', 'system']
        },
        echarts: {
            deps: ['jquery', 'system']
        }
    },
    waitSeconds: 0
})

localStorage.setItem('regional', '海淀区')

const openMock = false

const baseModule = ['jquery', 'echarts', 'common', 'commonUtils', 'layui', 'system', 'echartsgl']

if (openMock) {
    baseModule.push('parentReservationData')
}

require(baseModule, ($, echarts, common, utils) => {
    const openLog = true

    // 配置常量
    const CHART_CONFIG = {
        deptTitle: '科室预约量对比',
        packageTitle: '体检套餐使用率',
        timeTitle: '预约时段分布',
        cancelTitle: '预约取消情况',
        colorScheme: ['#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE', '#3BA272', '#FC8452', '#9A60B4', '#EA7CCC']
    }

    const drawSpaceMap = {
        departmentappointments: $('#departmentappointments')[0], // 科室预约量
        timeappointment: $('#timeappointment')[0], // 家长预约时段分布
        cancelappointment: $('#cancelappointment')[0], // 预约取消情况
        parentGender: $('#parentGender')[0], // 家长性别
        parentDevice: $('#parentDevice')[0], // 家长设备
        parentAge: $('#parentAge')[0], // 家长年龄
        parentRegion: $('#parentRegion')[0], // 区域分布
        parentChildRelation: $('#parentChildRelation')[0], // 家长与孩子关系
        childrenGender: $('#childrenGender')[0], // 儿童性别分布
        childrenAge: $('#childrenAge')[0] // 儿童年龄分布
    }

    /**
     * 当前页面展示数据的时间段
     */
    let pageDate = () => {
        const endDate = utils.getFormattedDate()
        const startDate = utils.formatDate(new Date((new Date(endDate)).getTime() - 7 * 24 * 60 * 60 * 1000));
        return { startDate, endDate }
    }
    pageDate = pageDate()

    const numPeoMap = {
        appointmentSuccess: $('#appointmentSuccess'), // 预约成功
        appointmentCancellation: $('#appointmentCancellation'), // 预约取消
        parentCount: $('#parentCount'), // 家长数
        childCount: $('#childCount'), // 幼儿数
        avgAppointmentsPerParent: $('#avgAppointmentsPerParent'), // 家长人均预约数
        appointmentsDepartment: $('#appointmentsDepartment') // 预约科室数
    }
    numPeoMap.appointmentSuccess.click((e) => {
        utils.pageJump('parentReservationInfo.html', {
            startDate: pageDate.startDate,
            endDate: pageDate.endDate,
            count: numPeoMap.appointmentSuccess.text(),
            // isCancel: '0'
        }, '家长预约详情')
    })
    numPeoMap.appointmentCancellation.click(() => {
        utils.pageJump('parentReservationInfo.html', {
            startDate: pageDate.startDate,
            endDate: pageDate.endDate,
            count: numPeoMap.appointmentCancellation.text(),
            isCancel: '1'
        }, '家长预约详情')
    })
    numPeoMap.appointmentsDepartment.click(() => {
        utils.pageJump('w_appointmentsDepartment.html', {
            startDate: pageDate.startDate,
            endDate: pageDate.endDate
        }, '家长预约详情')
    })
    numPeoMap.parentCount.click(() => {
        utils.pageJump('w_parentAppointment_parent.html', {
            startDate: pageDate.startDate,
            endDate: pageDate.endDate,
            count: numPeoMap.parentCount.text()
        }, '家长预约详情')
    })
    numPeoMap.childCount.click(() => {
        utils.pageJump('w_parentAppointment_child.html', {
            startDate: pageDate.startDate,
            endDate: pageDate.endDate,
            count: numPeoMap.childCount.text()
        }, '家长预约幼儿情况')
    })

    /*
    功能：渲染预约取消情况折线图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderCancelChart(data) {
        let myChart = echarts.init(drawSpaceMap['cancelappointment'], null, { renderer: 'div' });
        // 生成图表配置
        var option = common.getLine(
            data.values,
            data.labels,
            'myLine',
            {
                labelFormatter: function (value) {
                    return value + '人'
                },
                xAxisFormatter: function (value) {
                    return value + '人'
                },
                offsetY: 20
            }
        )
        myChart.setOption(option)
    }

    /*
    功能：渲染统计卡片
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderStats(data) {
        utils.logWithCondition(openLog, new Error().stack, data);
        // 渲染统计项
        for (const key in numPeoMap) {
            numPeoMap[key].text(data[key])
        }
    }

    /*
    功能：渲染科室预约量水平柱状图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderDeptChart(data) {
        let myChart = echarts.init(drawSpaceMap['departmentappointments']);
        utils.logWithCondition(openLog, new Error().stack, data)
        const idList = data.labels.map(item => item.split('_')[1])
        data.labels = data.labels.map(item => item.split('_')[0])
        // 生成图表配置
        var option = common.getLevelBar3D(
            data.values,
            data.labels,
            'my3D',
            [
                ['#1ae9a3'], // 顶面：蓝色
                ['#a9f4db', '#17de9b'], // 右侧面：浅蓝渐变
                ['#aae4d0', '#1acf92'] // 左侧面：深蓝渐变
            ],
            {
                labelFormatter: function (value) {
                    return value + '人'
                },
                xAxisFormatter: function (value) {
                    return value + '人'
                },
                offsetX: 8,
                offsetY: 20
            }
        )
        myChart.setOption(option)
        myChart.off('click');
        myChart.on('click', e => {
            // utils.logWithCondition(openLog,new Error().stack, e.name, e.value)
            utils.logWithCondition(openLog, new Error().stack, e)
            utils.logWithCondition(openLog, new Error().stack, e.name, e.data)
            utils.pageJump('parentReservationInfo.html', {
                startDate: pageDate.startDate,
                endDate: pageDate.endDate,
                deptId: openMock ? e.name : idList[e.dataIndex],
                count: e.data
            }, '家长预约详情')
        })
    }

    /*
     功能：渲染预约取消情况饼图
     参数说明：data - 网络请求后的数据
     返回值说明：无
     */
    function renderCancelChart(data) {
        let myChart = echarts.init(drawSpaceMap['cancelappointment'], null, { renderer: 'div' });

        const itemStyleMap = {
            已预约: {
                color: '#1fe6a3'
            },
            已取消: {
                color: '#ffd05f'
            }
        }
        // 使用common模块的getPie3D函数生成3D饼图配置
        const options = {
            showLegend: false,
            distance: 200,
            boxHeight: 0.02,
        }
        data.map(item => (item.itemStyle = itemStyleMap[item.name]))
        data.map(item => (item.value = +item.value))
        common.create3DPie(drawSpaceMap['cancelappointment'].id, data, options)
        myChart.off('click');
        myChart.on('click', e => {
            utils.logWithCondition(openLog, new Error().stack, e);
            let isCancel = '1'
            if (e.seriesName === '已预约') {
                isCancel = '0'
            }
            utils.pageJump('w_appointmentsDepartment.html', {
                startDate: pageDate.startDate,
                endDate: pageDate.endDate,
                packageData: {
                    packageName: 'all',
                    successCount: data[e.componentIndex].value,
                    isCancel
                }
            }, '家长预约详情')
        })
    }

    /*
    功能：渲染家长预约时段分布折线图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderTimeChart(data) {
        utils.logWithCondition(openLog, new Error().stack, data);

        let myChart = echarts.init(drawSpaceMap['timeappointment'], null, { renderer: 'div' });

        const option = {
            color: [CHART_CONFIG.colorScheme[0]],
            tooltip: {
                trigger: 'axis',
                formatter: function (params) {
                    return `${params[0].name}<br/>预约量: ${params[0].value}`
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: data.map(item => item['time'])
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '预约量',
                    type: 'line',
                    smooth: true,
                    areaStyle: {},
                    data: data.map(item => item['count']),
                    smooth: true, // 开启平滑曲线
                    label: {
                        show: true, // 显示数据点数值
                        position: 'top'
                    },
                    lineStyle: {
                        color: '#2ea875' // 折线颜色
                    },
                    itemStyle: {
                        color: '#2ea875' // 数据点颜色
                    },
                    areaStyle: {
                        // 区域阴影配置
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [
                                {
                                    offset: 0,
                                    color: '#abffdc' // 起始透明度
                                },
                                {
                                    offset: 1,
                                    color: '#fff' // 结束透明度
                                }
                            ]
                        }
                    },
                    emphasis: {
                        lineStyle: {
                            width: 4
                        },
                        symbolSize: 8
                    }
                }
            ]
        }
        myChart.off('click');
        myChart.setOption(option)
        myChart.on('click', e => {
            // utils.logWithCondition(openLog,new Error().stack, e.name, e.value)
            const startTime = e.name
            const endTime = `${+e.name.split(':')[0] + 1}:00`
            utils.pageJump('parentReservationInfo.html', {
                startDate: pageDate.startDate,
                endDate: pageDate.endDate,
                count: e.data,
                startTime,
                endTime
            }, '家长预约详情')
        })
    }

    /*
    功能：渲染性别分布情况饼图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderGenderChart(data) {
        const itemStyleMap = {
            男性: {
                color: '#fea131'
            },
            女性: {
                color: '#f54d48'
            }
        }
        // 使用common模块的getPie3D函数生成3D饼图配置
        const options = {
            title: '性别',
        }
        data.map(item => (item.itemStyle = itemStyleMap[item.name]))
        let myChart = common.create3DPie(drawSpaceMap['parentGender'].id, data, options)
        myChart.off('click');
        myChart.on('click', e => {
            let gender = '1'
            if (e.seriesName === '女性') {
                gender = '2'
            }
            utils.pageJump('w_parentAppointment_parent.html', {
                startDate: pageDate.startDate,
                endDate: pageDate.endDate,
                parentGender: gender,
                count: data[e.componentIndex].value
            }, '家长预约详情')
        })
    }

    /*
    功能：渲染访问设备分布情况饼图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderDeviceChart(data) {
        const itemStyleMap = {
            安卓: {
                color: '#fea033'
            },
            iOS: {
                color: '#8485fe'
            },
            未知: {
                color: '#f54d48'
            }
        }
        // 使用common模块的getPie3D函数生成3D饼图配置
        const options = {
            title: '访问设备',
            showLegend: false,
            distance: 200,
            boxHeight: 0.05,
        }
        data.map(item => (item.itemStyle = itemStyleMap[item.name]))
        let deviceChart = common.create3DPie(drawSpaceMap['parentDevice'].id, data, options)
        deviceChart.off('click');
        deviceChart.on('click', e => {
            // utils.logWithCondition(openLog,new Error().stack, e.name, e.value)
            utils.pageJump('w_parentAppointment_parent.html', {
                startDate: pageDate.startDate,
                endDate: pageDate.endDate,
                deviceType: e.seriesName === 'iOS' ? '苹果' : e.seriesName,
                count: data[e.componentIndex].value
            }, '家长预约详情')
        })
    }

    /*
    功能：渲染访问年龄分布情况饼图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderAgeChart(data) {
        const itemStyleMap = {
            '18岁到25岁': {
                color: '#ffd15d'
            },
            '26岁到35岁': {
                color: '#08d0fd'
            },
            '36岁到45岁': {
                color: '#20e8a4'
            },
            '46岁到60岁': {
                color: '#0883fe'
            },
            '60岁以上': {
                color: '#f54e46'
            },
            未知: {
                color: '#8583fe'
            }
        }
        // 使用common模块的getPie3D函数生成3D饼图配置
        const options = {
            title: '年龄分布',
        }
        data.map(item => (item.itemStyle = itemStyleMap[item.name]))
        let ageChart = common.create3DPie(drawSpaceMap['parentAge'].id, data, options)
        ageChart.off('click');
        ageChart.on('click', e => {
            utils.pageJump('w_parentAppointment_parent.html', {
                startDate: pageDate.startDate,
                endDate: pageDate.endDate,
                parentAge: e.seriesName[0],
                count: data[e.componentIndex].value
            }, '家长预约详情')
        })
    }

    /*
    功能：渲染访问区域分布情况雷达图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderRegionChart(data) {
        let regionChart = echarts.getInstanceByDom(drawSpaceMap['parentRegion']);
        if (!regionChart) {
            regionChart = echarts.init(drawSpaceMap['parentRegion'], null, { renderer: 'div' });
        } else {
            regionChart.off('click');
        }
        try {
            const indicator = []
            for (let i = 0; i < 18; i++) {
                indicator.push({
                    name: '',
                    max: 100
                })
            }

            const option = {
                // backgroundColor: "rgba(114, 184, 255, 0.5)",
                title: {
                    text: '区域分布',
                    left: '5px',
                    top: '5px',
                    textStyle: {
                        color: '#333',
                        fontSize: 14
                    }
                },
                grid: {
                    top: '10%'
                },
                tooltip: {
                    show: true,
                    trigger: 'item',
                    // 自定义tooltip内容
                    formatter: function (params) {
                        // utils.logWithCondition(openLog,new Error().stack, data.areas[params.componentIndex])
                        // utils.logWithCondition(openLog,new Error().stack, params.componentIndex)

                        return `家长数：${data.areas[params.componentIndex].parentCount}<br>
								幼儿数：${data.areas[params.componentIndex].childCount}`
                    }
                },
                radar: {
                    // 雷达图起始角度（关键配置）
                    startAngle: 180, // 从0度开始（默认是90度，即从正上方开始）
                    splitNumber: 3,
                    center: ['50%', '50%'],
                    radius: '70%',
                    shape: 'circle',
                    splitArea: {
                        show: true,
                        areaStyle: {
                            // 渐变背景色，从中心向外扩散
                            color: [
                                'rgba(114, 184, 255, 0.8)', // 最内部区域
                                'rgba(114, 184, 255, 0.6)', // 第二层
                                'rgba(114, 184, 255, 0.4)' // 第三层
                            ]
                        }
                    },
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(114, 184, 255)'
                        }
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: [
                                'rgba(114, 184, 255, 0.5)', // 最内部区域
                                'rgba(114, 184, 255, 0.5)', // 第二层
                                'rgba(114, 184, 255, 0.4)' // 第三层
                            ]
                        }
                    },
                    indicator
                },
                series: [
                    {
                        type: 'radar',
                        symbol: 'circle',
                        symbolSize: 6,
                        itemStyle: {
                            normal: {}
                        },
                        areaStyle: {
                            normal: {
                                color: 'rgba(114, 184, 255, 0.5)',
                                opacity: 0.2
                            }
                        },
                        lineStyle: {
                            normal: {}
                        },
                        label: {
                            normal: {
                                show: true,
                                color: '#a4a4a4',
                                position: 'top',
                                fontSize: 12,
                                fontWeight: 200,
                                formatter: params => {
                                    return '1KM'
                                }
                            }
                        },
                        data: [33]
                    },
                    {
                        type: 'radar',
                        symbol: 'circle',
                        symbolSize: 6,
                        itemStyle: {
                            normal: {}
                        },
                        areaStyle: {
                            normal: {
                                color: 'rgba(114, 184, 255, 0.5)'
                            }
                        },
                        lineStyle: {},
                        label: {
                            normal: {
                                show: true,
                                color: '#a4a4a4',
                                position: 'top',
                                fontSize: 13,
                                fontWeight: 200,
                                formatter: params => {
                                    return '3KM'
                                }
                            }
                        },
                        data: [66]
                    },
                    {
                        type: 'radar',
                        symbol: 'circle',
                        symbolSize: 6,
                        itemStyle: {
                            normal: {}
                        },
                        areaStyle: {
                            normal: {
                                color: 'rgba(114, 184, 255, 0.5)'
                            }
                        },
                        lineStyle: {
                            normal: {}
                        },
                        label: {
                            normal: {
                                show: true,
                                color: '#a4a4a4',
                                position: 'top',
                                fontSize: 15,
                                fontWeight: 200,
                                formatter: params => {
                                    return '5KM'
                                }
                            }
                        },
                        data: [99]
                    }
                ]
            }

            // 设置配置项并渲染图表
            regionChart.setOption(option)
        } catch (e) {
            console.error('初始化图表失败:', e)
        }
        regionChart.on('click', e => {
            // utils.logWithCondition(openLog,new Error().stack, e.componentIndex)
            utils.logWithCondition(openLog, new Error().stack, e)
            utils.pageJump('w_parentAppointment_parent.html', {
                startDate: pageDate.startDate,
                endDate: pageDate.endDate,
                areaDistribution: e.componentIndex + 1,
                count: data.areas[e.componentIndex].parentCount + data.areas[e.componentIndex].childCount
            }, '家长预约详情')
        })
    }

    /*
    功能：渲染孩子与家长关系柱状图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderChildParentRelationChart(data) {
        // 初始化 ECharts 实例
        let myChart = echarts.getInstanceByDom(drawSpaceMap['parentChildRelation']);
        if (!myChart) {
            myChart = echarts.init(drawSpaceMap['parentChildRelation'], null, { renderer: 'div' });
        } else {
            myChart.off('click');
        }

        var colorConfig = [
            ['#42A5F5'], // 顶面：蓝色
            ['#1976D2', 'rgba(25,118,210,0.3)'], // 左侧面：深蓝渐变
            ['#64B5F6', 'rgba(100,181,246,0.3)'] // 右侧面：浅蓝渐变
        ]
        var options = {
            labelRotate: 45,
            labelInterval: 0
        }
        var option = common.getBar3D(data.categories, data.values, 'Relation', colorConfig, options)
        myChart.setOption(option)
        myChart.on('click', e => {
            utils.logWithCondition(openLog, new Error().stack, e)
            utils.pageJump('w_parentAppointment_parent.html', {
                startDate: pageDate.startDate,
                endDate: pageDate.endDate,
                parentRelation: e.name,
                count: e.data
            }, '家长预约详情')
        })
    }

    /*
    功能：渲渲染孩子性别分布饼图
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderChildGenderChart(data) {
        const itemStyleMap = {
            男性: {
                color: '#fea131'
            },
            女性: {
                color: '#f54d48'
            }
        }
        // 使用common模块的getPie3D函数生成3D饼图配置
        const options = {
            title: '性别'
        }
        data.map(item => (item.itemStyle = itemStyleMap[item.name]))
        let myChart = common.create3DPie(drawSpaceMap['childrenGender'].id, data, options)
        myChart.off('click');
        myChart.on('click', e => {
            let childGender = '2'
            if (e.seriesName == '女性') {
                childGender = '1'
            }
            utils.pageJump('w_parentAppointment_child.html', {
                startDate: pageDate.startDate,
                endDate: pageDate.endDate,
                childGender,
                count: data[e.componentIndex].value
            }, '家长预约详情')
        })
    }

    /*
    功能：渲染孩子年龄分布
    参数说明：data - 网络请求后的数据
    返回值说明：无
    */
    function renderChildAgeChart(data) {
        const itemStyleMap = {
            '3岁': {
                color: '#ffd15d'
            },
            '4岁': {
                color: '#08d0fd'
            },
            '5岁': {
                color: '#20e8a4'
            },
            '6岁': {
                color: '#0883fe'
            },
            '7岁': {
                color: '#f54e46'
            }
        }
        // 使用common模块的getPie3D函数生成3D饼图配置
        const options = {
            title: '年龄分布',
        }
        data.map(item => (item.itemStyle = itemStyleMap[item.name]))
        let ageChart = common.create3DPie(drawSpaceMap['childrenAge'].id, data, options)
        ageChart.off('click');
        ageChart.on('click', e => {
            utils.pageJump('w_parentAppointment_child.html', {
                startDate: pageDate.startDate,
                endDate: pageDate.endDate,
                physicalAge: e.seriesName[0],
                count: data[e.componentIndex].value
            }, '家长预约详情')
        })
    }

    // 页面初始化
    layui.use(['form', 'laydate'], function () {
        laydate = layui.laydate
        const param = {
            startDate: pageDate.startDate,
            endDate: pageDate.endDate
        }

        const tasks = [
            () => utils.smactionMockData('parentReservation/stats', param, renderStats), // 统计卡
            () => utils.smactionMockData('parentReservation/dept', param, renderDeptChart), // 科室预约量
            () => utils.smactionMockData('parentReservation/cancel', param, renderCancelChart), // 家长预约与取消情况
            () => utils.smactionMockData('parentReservation/time', param, renderTimeChart), // 家长预约时段分布（24小时）
            // () => utils.smactionMockData('parentReservation/gender', param, renderGenderChart), // 性别分布
            // () => utils.smactionMockData('parentReservation/age', param, renderAgeChart), // 年龄分布
            // () => utils.smactionMockData('parentReservation/device', param, renderDeviceChart), // 设备分布
            // () => utils.smactionMockData('parentReservation/region', param, renderRegionChart), // 区域分布
            // () => utils.smactionMockData('parentReservation/relationship', param, renderChildParentRelationChart), // 孩子与家长关系
            () => utils.smactionMockData('parentReservation/childGender', param, renderChildGenderChart), // 孩子性别分布
            () => utils.smactionMockData('parentReservation/childAge', param, renderChildAgeChart) // 孩子年龄分布
        ]

        // 加载初始数据
        function loadData() {
            const sheduler = runChunk => {
                setTimeout(() => {
                    let count = 0
                    runChunk(() => count++ < 1)
                }, 100)
            }
            utils.performTask(tasks, sheduler)
        }

        // 初始加载
        loadData()

        // 初始化日期选择器
        initDatePicker();

        /*
         * 初始化日期选择器
         */
        function initDatePicker() {
            // 开始日期
            laydate.render({
                elem: '#startDate',
                type: 'date',
                value: param.startDate,
                done: function (data) {
                    param.startDate = data;
                    pageDate.startDate = data
                }
            });

            // 结束日期
            laydate.render({
                elem: '#endDate',
                type: 'date',
                value: param.endDate,
                done: function (data) {
                    param.endDate = data;
                    pageDate.endDate = data
                }
            });
        }

        /**
         * 绑定事件
         */
        function initEvent() {
            $("#btnSearch").click(function () {
                loadData()
            });
        }
        initEvent()
    })

    /*
    功能：图表窗口自适应
    参数说明：无
    返回值说明：无
    */
    function resizeCharts() {
        for (let key in drawSpaceMap) {
            const dom = drawSpaceMap[key]
            const echartsDom = echarts.getInstanceByDom(dom)
            if (echartsDom) {
                echartsDom.resize()
            }
        }
    }

    // 监听窗口大小变化，自适应图表
    window.addEventListener('resize', resizeCharts)

})