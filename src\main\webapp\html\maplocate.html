<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="../css/reset.css">
    <link rel="stylesheet" href="../css/main.css">
    <title>maplocate</title>
</head>
<style>
    #mapVessel {width: 75%;height: 500px;float: left;}
    #searchControl {margin-left: 75%;height: 500px;padding-left: 5px;font-size: 14px;}
    #searchTitle {padding-top: 5px;}
    #searchInput {height: 30px;padding-top: 5px;}
    #searchInput>input {width: 70%;height: 24px;border: 1px solid #f44c28;border-right: 0;float: left;padding-left: 4px;font-size: 14px;}
    #searchInput>button {width: 25%;height: 28px;line-height: 26px;background-color: #f44c28;color: #fff;float: left;border: none;}
    .info-title {color: #f44c28;font-size: 14px;}
    .info-content {font-size: 14px;padding: 4px 0;word-break: break-all;}
    .confirm-btn {background-color: #f44c28;font-size: 13px;width: 60px;padding: 4px;color: #fff;position: absolute;right:0;bottom:0;border: none;}
    #mapshow {padding-top: 10px;overflow: auto;}
    .pointer {position: relative;}
    .title-list{padding: 3px 0 3px 28px;color: #f44c28;cursor: pointer;}
    .text-list {color: #808080;padding-left: 28px;}
    .icon-list {width: 23px;height: 25px;background: url("//map.baidu.com/img/markers.png") no-repeat;position: absolute;top: 5px;left: 3px;}
    .pagination {padding-top: 20px;text-align: center;}
    .pagination a {margin: 0 5px;color: black;}
    .pagination span {padding-top: 7px;display: inline-block;}
    .pagination .active {color: #f44c28;text-decoration: underline;}
</style>
<body>
<div>
    <div id="mapVessel"></div>
    <div id="searchControl" >
        <div id="searchTitle">
            <p> 您可以使用以下功能：</p>
            <p>1.右键定位</p>
            <p>2.搜索并选择位置</p>
        </div>
        <div id ="searchInput">
            <label for="inputContent"></label><input type="text" name="address" id="inputContent" />
            <button id="searchBtn">搜索</button>
        </div>
        <div id="searchResultPanel" style="display:none;"></div>
        <div id="mapshow"></div>
    </div>
</div>
<script type="text/javascript" src="../sys/jquery.js"></script>
<script type="text/javascript" type="text/javascript" src="//api.map.baidu.com/api?v=2.0&ak=MfX8ZrC7i89wF1UcPFnorhsWFebQLtBv"></script>
<script type="text/javascript" src="../sys/arg.js"></script>
<script type="text/javascript" src="../js/citycode.js"></script>
<script type="text/javascript" src="../js/maplocate.js"></script>
</body>
</html>