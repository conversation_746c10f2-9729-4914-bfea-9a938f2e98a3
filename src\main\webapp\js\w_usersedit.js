// 用户管理
// 2015.09.17
// 李红利

var objdata = {
    arrcurright: []
    , objrole: {}//下拉角色
    , unameIsUsed: 0//用户名是否使用
    , isSendmsg: false,
    upfile_endpoint: '',//上传地址
    upfile_nametype: 'random_name',//local_name random_name //文件名类型
    upfile_defaltdir: 'fuyou/userimg'//上传路径 多层  格式  upload/floder1/floder2
    , lng: null
    , lat: null
    , index: ''
    , bmapaddr: null
    , isFromBmap: false
    , mainid: 0
};
var parentobj = null;
layui.config({
    base: './js/'
}).extend({ //设定模块别名
    system: '../../sys/system',
    waitSeconds: 0
});
var ossEqParam_150 = '?x-oss-process=image/resize,m_fill,w_150,h_150';
var rv;
layui.use(['system', 'form'], function () {//(!parent.objdata.lan ? 'vlanzh' : parent.objdata.lan == 'zh' ? 'vlanzh' : 'vlanen'),
    var usermidv = Arg('mid');
    if (Arg("frompage") && (Arg("frompage") == "slice" || Arg("frompage") == "yeylist")) {
        usermidv = Arg('usermidv') || Arg('mid');
    }
    rv = parent.getmyrv(usermidv);//功能权限
    objdata.upfile_endpoint = ossPrefix;
    var heigth = $(window).height() - 100;
    $("#contant").css('height', heigth);
    $("#txtareacode").val(Arg("curareacode") || parent.objdata.my.areacode);
    window.form = layui.form;
    objdata.index = parent.layer.getFrameIndex(window.name);
    parentobj = jQuery.getparent().$("#win_" + Arg('mid')).children('iframe')[0].contentWindow;
    initData();
    initEvent();
    if (parent.layui.browser.name == 'safari') {
        $("#btnup").click(function () {
            parent.layer.msg("不支持safari浏览器上传图片");
        })
    } else {
        inituploadLocal();
    }
    $("#txtuname").removeAttr("readonly");
    $("#txtmobile").removeAttr("readonly");
    $("#txtpwd").removeAttr("readonly");
    $("#txtpwdagin").removeAttr("readonly");
});

/**
 * 事件
 */
function initEvent() {
    $("#txtpwd").blur(function () {
        var _this = $(this),
            newv = _this.val();
        ckpwd.pwStrength(newv);
        if (!ckpwd.reg.test(newv)) {
            return parent.layer.msg(ckpwd.tip);
        }
    });
    //修改角色
    form.on('select(roles)', function (data) {
        rolechange(data.value);
    });
    form.on('submit(btnsave)', function (data) {
        if (!objdata.unameIsUsed) {
            saveEvent();
        } else {
            parent.layer.msg("此用户名已使用,请更换");
            $("#txtuname").focus();
        }
    });
    $("#btnisforbid").click(function () {//禁用
        disableuser(1, "禁用", Arg("yeyid"));
    });
    $("#btnnotforbid").click(function () {//取消禁用
        disableuser(0, "取消禁用", Arg("yeyid"));
    });
    $("#mapmsg").on("click", function () { // 打开地图定位
        var yeyname = $("#txtdepartname").val();
        var areacode = Arg("curareacode");
        parent.layer.open({
            type: 2,
            area: ["100%", "100%"],
            closeBtn: 1,
            title: "正在定位：",
            shadeClose: false, //点击遮罩关闭
            content: ["html/maplocate.html?name=" + (yeyname || "") + "&lng=" + (objdata.lng || "") + "&lat=" + (objdata.lat || "") + "&layerIndex=" + (objdata.index || "") + "&areaCode=" + (areacode || "") + "&mid=" + Arg("mid") + "&from=usersedit", "no"]
        })
    });
    $("#bmapicon").on("click", function () { // 查看百度地图地址
        var bmapaddr = objdata.bmapaddr ? "：" + objdata.bmapaddr : "空";
        if (objdata.bmapaddr) {
            if (objdata.isFromBmap || $.trim($("#txtdepartaddress").val()) == objdata.bmapaddr) {
                parent.layer.msg("百度地图地址与现存地址一致");
                objdata.isFromBmap = true;
            } else {
                parent.layer.confirm("百度地图定位的地址为" + bmapaddr + "。确定要取代您的输入吗？", {
                    shadeClose: false,
                    title: ["查看", "font-size: 18px; font-family: SimHei;"],
                    btn: ["确定", "取消"],
                    yes: function (index) {
                        objdata.isFromBmap = true;
                        var body = parent.layer.getChildFrame("body", objdata.index);
                        body.find("#mapaddress").val(objdata.bmapaddr ? objdata.bmapaddr : "");
                        parent.layer.close(index);
                    }
                })
            }
        } else {
            parent.layer.msg("百度地图地址为空");
        }
    });

    $("#btndelete").click(function () {
        parent.layer.confirm('请确定要删除吗？', {
            btn: ['确定', '取消'], //按钮
            shade: false //不显示遮罩
        }, function (index) {
            var arrsm = [];
            $.sm(function (re, err) {
                if (re) {
                    $.sm(function (rep, errp) {//删除平台妇幼用户
                        if (rep) {
                            parent.$("#win_" + Arg("mid")).find('iframe')[0].contentWindow.$("#uluser li[rid='" + Arg('id') + "']").remove();
                            //parent.$("#uluser li[rid='" + Arg('id') + "']").remove();
                            parent.layer.msg('删除成功！', {icon: 1});
                            //删除申请记录
                            var mobile = $("#txtmobile").attr("oldmobile")
                            $.sm(function(){

                            },["w_users.delapply", mobile])
                            parent.objdata.layers.wusersindex && parent.$("#layui-layer-iframe" + parent.objdata.layers.wusersindex)[0] ? parent.$("#layui-layer-iframe" + parent.objdata.layers.wusersindex)[0].contentWindow.location.reload() : "";
                            parent.layer.close(index);
                            parent.layer.close(parent.objdata.layers.edituser);
                        } else {
                            $.sm(function (ref, errf) {//若删除平台用户失败后，恢复妇幼用户
                                parent.layer.msg('删除失败！', {icon: 1});
                            }, ['w_users.return6', Arg('id')]);
                        }
                    }, ['w_user.delptfuyouuser', objdata.mainid], "fyyypt", "", {rpc: 'fyyypt'});
                } else {
                    parent.layer.msg(err, {shift: 6});
                }
            }, ['w_users.6', Arg('id')]);
            parent.layer.close(index);
        });
    });
    $("#txttruename").blur(function () {
        var _this = $(this);
        getspell(_this);
    });
    $("#txtdepartname").blur(function () {
        var _this = $(this);
        var $val = $.trim(_this.val());
        if ($val.length > 20) {
            parent.layer.msg("单位名称不能超过20个字", {shift: 6});
        }
    });
    // 验证用户名
    $("#txtuname").blur(function () {
        var _this = $(this);
        var $val = $.trim(_this.val());
        if (/[\u4E00-\u9FA5]/g.test($val)) {
            return parent.layer.msg("用户名中不能有汉字", {shift: 6});
        }
        if ($val) {
            $.sm(function (re) {
                if (re) {
                    if (re.length > 0) {
                        objdata.unameIsUsed = true;
                        parent.layer.msg("此用户名已使用,请更换");
                        $("#txtuname").focus();
                    } else {
                        objdata.unameIsUsed = false;
                    }
                } else {
                    parent.layer.msg("验证用户名出错，请联系管理员！");
                    objdata.unameIsUsed = true;
                }
            }, ["useredit.checkunamebypt", $val, (objdata.mainid || "0")], "fyyypt", "", {route: 'fyyypt'});//(Arg("id") || "0")
        }
    });
    $("#txtpwdagin").blur(function () {
        if ($('#txtpwdagin').val() != $('#txtpwd').val()) {
            parent.layer.msg("确认密码不相同，请重新输入！");
            return;
        }
    });

    // 验证电话
    $('#txtmobile').on('blur', function () {
        var $this = $(this);
        var $val = $.trim($this.val());
        if ($val) {
            if (!mobileCheck($val)) {
                parent.layer.msg("请输入有效的手机号码！", {shift: 6});
                return false;
            }
            $.sm(function (re, err) {
                if (re) {
                    if (re.length) {
                        objdata.phoneIsUsed = true;
                        parent.layer.msg("此电话号码已使用，请更换", {shift: 6});
                        $this.focus();
                    } else {
                        if (!$("#txtuname").val()) {
                            $("#txtuname").val($val);
                            objdata.phoneIsUsed = false;
                        }
                    }
                } else {
                    parent.layer.msg("验证电话号码出错，请联系管理员！", {shift: 6});
                    objdata.phoneIsUsed = true;
                }
            }, ["useredit.checkunamebypt", $val, (objdata.mainid || "0")], "fyyypt", "", {route: 'fyyypt'})
        }
    });
    // 验证邮箱
    $('#txtemail').on('blur', function () {
        var $this = $(this)
            , $val = $.trim($this.val());
        if ($val) {
            $.sm(function (re) {
                if (re) {
                    if (re.length) {
                        objdata.emailIsUsed = true;
                        parent.layer.msg("此邮箱已使用，请更换");
                        $this.focus();
                    } else {
                        objdata.emailIsUsed = false;
                    }
                } else {
                    parent.layer.msg("验证邮箱出错，请联系管理员！");
                    objdata.emailIsUsed = true;
                }
            }, ["useredit.checkunamebypt", $val, (objdata.mainid || "0")], "fyyypt", "", {route: 'fyyypt'})
        } else { // 邮箱可以为空
            objdata.emailIsUsed = false;
        }
    });
    $('#btsetpwd1').click(function () {
        randompwd(0);
    });
    $('#btsetpwd2').click(function () {
        randompwd(1);
    });
    $("#btncancel").click(function () {
        closeWin();
    });
    $("#btnresetpwd").click(function () {
        var id = objdata.uid;
        var html = [];
        html.push('<div style="width: 300px;text-align: center; margin-left: 30px;">');
        html.push('<table style="height:100px;font-size:14px;">');
        html.push('<tr>');
        html.push('<td>密码：</td>');
        html.push('<td><input type="text" id="txtpwd" style="width: 120px;"/></td>');
        html.push('<td style="display:none;"><a id="btsetpwd1" class="" style="color:#23b7e5;">固定密码</a></td>');//btn-black
        html.push('<td style="display:none;"><a id="btsetpwd2" class="" style="color:#23b7e5;padding-left:5px;">随机密码</a></td>');
        html.push('</tr>');
        html.push('</table>');
        html.push('</div>');
        parent.layer.open({
            type: 1,
            title: '密码重置',
            skin: 'layui-layer-rim', //加上边框
            content: html.join(''),
            btn: ['确定', '取消']
            , yes: function (index, layero) { //或者使用btn1
                var pwd = layero.find('#txtpwd').val();
                if (!CheckPassWord(pwd)) {
                    parent.layer.msg("密码必须是以字母开头，并且只能够包含字母、数字和下划线，长度在9-20之间！", {shift: 6});
                    return false;
                }
                // var arrsm = [['w_users.8', JSON.stringify({pwd: pwd}), $.msgwhere({id: [id]})], ['w_users.altpwdpt', pwd, pwd, id]];
                $.sm(function (re, err) {
                    if (re) {
                        parent.layer.msg('重置成功！', {icon: 1});
                        parent.layer.close(index);
                    } else
                        parent.layer.msg(err, {shift: 6});
                }, [["w_users.8", JSON.stringify({pwd: pwd}), $.msgwhere({id: [id]})], ['w_users.altpwdpt', JSON.stringify({pwd: pwd}), $.msgwhere({id: [id]})]], null, null, {trans: true});
            }, cancel: function (index) { //或者使用btn2
                parent.layer.close(index);
            },
            success: function (layero, index) {
                layero.find("#btsetpwd1").click(function () {
                    layero.find('#txtpwd').val('111111');
                });
                layero.find("#btsetpwd2").click(function () {
                    var rand = Math.round(Math.random() * 899999 + 100000);
                    layero.find('#txtpwd').val(rand);
                });
            }
        });
    });
    $("#btnunbindwx").click(function () {
        jQuery.getparent().layer.confirm("确定解除微信绑定吗？", function(i){
            $.sm(function(re, err, obj){
                jQuery.getparent().layer.close(i);
                if(re){
                    $("#btnunbindwx").hide();
                }
            }, ["personalCenter.unbindwx", objdata.mainid])
        })
    });
}

/**
 * 初始化数据
 */
function initData() {
    if (Arg("id")) {//编辑
        // $("#duanxin").hide();
        // if (rv & 2){//有添加权限
            $(".editrv").removeClass("editrv");
        // }else{
        //     $(".editrv").remove();
        // }
        objdata.uid = Arg("id");
        $(".u-edit").hide();
        // if(rv & 8){//重置密码权限
            $("#btnresetpwd").show();
        // }else{
        //     $("#btnresetpwd").remove();
        // }
        if (parent.objdata.my.id != objdata.uid && rv & 4) {
            $("#btndelete").show();
        }
        $.sm(function (re, err) {//truename,uname,sp,spe,mobile,email,headimg,headimg_c,headimg_z,depart_id,isforbid,roles,mids,mr,seer
            if (re) {
                initTree(function (res) {
                    if (Arg("frompage") && Arg("frompage") == "yeylist") {
                        if (res[10] == "1") {
                            $("#btnnotforbid").show();
                            $("#btnisforbid").hide();
                        } else {
                            $("#btnnotforbid").hide();
                            $("#btnisforbid").show();
                        }
                    }
                    var txttruename = $("#txttruename");
                    txttruename.val(res[0]);
                    $("#txtuname").val(res[1]);
                    txttruename.attr('sp', res[2]);
                    txttruename.attr('spe', res[3]);
                    $("#txtmobile").val(res[4]).attr("oldmobile", res[4]);
                    $("#txtemail").val(res[5]);
                    $("#txtdepartaddress").val(res[19]);
                    objdata.lng = res[20];
                    objdata.lat = res[21];
                    objdata.bmapaddr = res[19];
                    res[19] ? objdata.isFromBmap = true : objdata.isFromBmap = false;
                    // objdata.bmapaddr=
                    //头像
                    if (re[0][6])
                        $(".otx-head").prop('src', ossPrefix + re[0][6]).attr('o', re[0][6]);
                    $("#txtareacode").val(res[9]);

                    //	                $("#departSel").val(res[15]);
                    $("#txtroletype").val(res[15]);
                    //角色
                    $("#roleSel").val(res[11]);
                    if(parent.objdata.my.id != objdata.uid){
                        $("#divrolesel").show();
                    }
                    //根据角色判断是否允许分片
                    // 0truename,1uname,sp,spe,mobile,email,6headimg,headimg_c,headimg_z,areacode,isforbid,11roles,mids,mr,seer,15roletype,isadmin,sex,departname,departaddress,departlng,departlat,22mainid,'',organid,25depart_id
                    objdata.mainid = res[22];//对应平台用户的id
                    objdata.organid = res[24];//医院机构id
                    objdata.depart_id = res[25];//机构、企业等id
                    objdata.yeyid = res[26];//园所id
                    rolechange(res[15]);
                    $("#txtdepartname").val(res[18]);
                    // $("[name='radisadmin'][value='" + res[16] + "']").prop('checked', true);
                    $("[name='sex'][value='" + res[17] + "']").prop('checked', true);
                    if (res[13] < 0)//mr
                        var oright = -1;
                    else if (res[13] != "" && res[13] != "0")
                        eval('var oright={' + decodeURIComponent(res[13]) + '}');
                    else
                        var oright = {};
                    if (res[14] < 0)//seer
                        var seer = -1;
                    else if (res[14] != "" && res[14] != "0")
                        eval('var seer={' + decodeURIComponent(res[14]) + '}');
                    else
                        var seer = {};
                    objdata.arrcurright = [decodeURIComponent(res[12]), oright, seer]; //mids,mr,seer
                    form.render();
                    $.sm(function(re2, err2, obj2){
                        if(obj2 && obj2.wx_userid){
                            $("#btnunbindwx").show();
                        }
                    }, ["personalCenter.selectwxbind", objdata.mainid])

                }, re[0]);
            } else
                parent.layer.msg(err, {shift: 6});
        }, ["w_users.2", objdata.uid]);
    } else {
        objdata.yeyid = Arg("yeyid") || "";//园所id
        $(".u-edit").show();
        $("#divrolesel").show();
        // if (rv & 1){//有添加权限
            $("#btnsave").removeClass("editrv");
        // }else{
        //     $("#btnsave").remove();
        // }
        if (Arg("frompage") == "yeylist") {//从医院界面或园所管理列表页面进入时
            rolechange("");
        } else {
            rolechange(parent.objdata.my.roletype);
        }
        initTree();
    }
}

//修改角色
function rolechange(value) {
    // if (value == 'e') {//妇幼下有关用户有分片操作权限
    //     $("#divCanSlice").show();
    // } else {
    //     $("#divCanSlice").hide();
    // }
    onchange();
}

function onchange() {
    $("#divselpart").html("");
    var arrright = ["", {}, {}];
    var roletype = "";
    var id = $("#roleSel").val();
    if (id && objdata.objrole[id]) {
        var role = objdata.objrole[id];
        if (role.mr < 0)//mr
            var oright = -1;
        else if (role.mr != "" && role.mr != "0")
            eval('var oright={' + role.mr + '}');
        else
            var oright = {};
        if (role.seer < 0)//seer
            var seer = -1;
        else if (role.seer != "" && role.seer != "0")
            eval('var seer={' + role.seer + '}');
        else
            var seer = {};
        arrright = [role.mids, oright, seer]; //mids,mr,seer
        roletype = role.roletype;
    }
    $("#txtroletype").val(roletype);
    objdata.arrcurright = arrright; //mids,mr,seer
    // getpart(id);
    // if(id == 2){//儿保管理员
    // }else if(id == 3){//体检机构管理员，id角色id,
    // }else if(id == 4){//医生（体检中心的）
    // }else if(id == 5){//护士
    // }else if(id == 6){//企业管理员
    // }else if(id == 7){//技师
    // }else if(id == 6){//企业管理员
    // }else if(id == 7){//技师
    // }else if(id == 8){//录入管理员
    // }else if(id == 9){//医院(儿保)
    // }else if(id == 10){//园所（幼儿园）
    // }else if(id == 11){//体检中心管理员管理员
    // }
    // if(parent.objdata.my.role == "1"){//超级管理员
    //     if(){
    //
    //     }
    // }
}

function saveEvent() {
    var img = $('#divicon').children('img');
    var headimg = img.attr('o') || "";
    var truename = $.trim($("#txttruename").val());
    var sp = $("#txttruename").attr('sp') || "";
    var spe = $("#txttruename").attr('spe') || "";
    var uname = $.trim($("#txtuname").val());
    if (/[\u4E00-\u9FA5]/g.test(uname)) {
        parent.layer.msg("用户名中不能有汉字", {shift: 6});
        return false;
    }
    var mobile = $.trim($("#txtmobile").val());
    if (!mobileCheck($.trim(mobile))) {
        parent.layer.msg("请输入有效的手机号码！", {shift: 6});
        return false;
    }
    var email = $.trim($("#txtemail").val());
    var areacode = $("#txtareacode").val();
    var departname = $.trim($("#txtdepartname").val());
    if (departname.length > 20) {
        parent.layer.msg("单位名称不能超过20个字", {shift: 6});
        return;
    }
    var sex = $("sex").val();
    var roles = $("#roleSel").val();
    var isadmin = 0;//$("radisadmin").val();//roles == 11体检中心管理员（）
    var rname = $("#roleSel").find("option:selected").text();
    var r = JSON.stringify(objdata.arrcurright[1]);//, 'object'
    r = r.replace(/{|}|\"/g, '').replace(/(\d+:0)(,)?/g, '');
    var dex = r.lastIndexOf(',');
    if (dex == r.length - 1) {
        r = r.substring(0, dex);
    }
    var s = JSON.stringify(objdata.arrcurright[2]);//, 'object'
    s = s.replace(/{|}|\"/g, '').replace(/(\d+:0)(,)?/g, '');
    var dex = s.lastIndexOf(',');
    if (dex == s.length - 1) {
        s = s.substring(0, dex);
    }
    var roletype = $("#txtroletype").val();
    var objarea = parent.getAreaInfo(areacode);
    var cguid = parent.objdata.fyinfo[1];
    var txtdepartaddress = $("#txtdepartaddress").val();
    var departid = "null", organid = "null", yeyid = "null";
    if (Arg("id")) {//编辑
        organid = objdata.organid || "null";
        yeyid = objdata.yeyid || "null";
        departid = objdata.depart_id || "null";
    } else {
        organid = Arg("organid") || parent.objdata.my.organid || 'null';
        yeyid = Arg("yeyid") || parent.objdata.my.yeyid || 'null';
        yeyid = (yeyid == "0" ? "null" : yeyid);
        departid = Arg("departid") || parent.objdata.my.departId || 'null';
    }
    if (!parent.objdata.my.fuyouid) {
        parent.layer.closeAll("loading");
        parent.layer.msg("初始化妇幼失败！");
        return;
    }

    var pwd = $.trim($("#txtpwd").val());
    if (!Arg("id") && !CheckPassWord(pwd)) {
        parent.layer.msg("密码必须是以字母开头，并且只能够包含字母、数字和下划线，长度在9-20之间！", {shift: 6});
        parent.layer.closeAll("loading");
        return false;
    }
    parent.layer.load();
    var objuser = {
        uname: uname,
        pwd: pwd,
        mobile: mobile,
        email: email,
        roles: roles,
        truename: truename,
        sp: sp,
        spe: spe,
        headimg: headimg,
        areacode: areacode,
        sex: sex,
        isadmin: isadmin,
        fromtype: 1,
        departname: departname,
        utype: '',
        departaddress: (txtdepartaddress || objdata.bmapaddr),
        departlng: (objdata.lng || ''),
        departlat: (objdata.lat || ''),
        id: Arg("id"),
        mainid: objdata.mainid || 0,
        organid: organid,
        departid: departid,
        yeyid: yeyid,
        fuyouid: parent.objdata.my.fuyouid
    };
    $.sm(function (re, err, obj) {
        if (err) {
            parent.layer.msg('用户添加失败！');
            parent.layer.closeAll("loading");
        } else {
            objdata.mainid = obj.mainid;
            objuser.id = obj.id;
            objuser.mainid = obj.mainid;
            objuser.rname = rname;
            if (Arg("frompage") && (Arg("frompage") == "slice" || Arg("frompage") == "yeylist")) {
                parent.$("#layui-layer-iframe" + parent.objdata.layers.wusersindex)[0].contentWindow.resetUser(objuser);
            } else {
                parent.$("#win_" + Arg("mid")).find('iframe')[0].contentWindow.resetUser(objuser);
            }
            parent.layer.alert('用户保存成功！');
            parent.layer.closeAll("loading");
            closeWin();
        }
    }, ["w_usersedit.add", JSON.stringify(objuser)]);
}

function updateAddress(transobj) {
    objdata.lng = transobj.lng;
    objdata.lat = transobj.lat;
    objdata.bmapaddr = transobj.address;
    var $ma = $("#txtdepartaddress")
        , address = $.trim($ma.val());
    if (address == "" || objdata.isFromBmap) {
        $ma.val(transobj.address);
        objdata.isFromBmap = true;
    }
}

/*
 * 必须包含数字加字母 不能包含特殊符号等
 */
function CheckPassWord(password) {//必须为字母加数字且长度不小于8位
    var str = password;
    if (str == null || str.length < 9) {
        return false;
    }
    // var reg1 = new RegExp(/^[0-9A-Za-z]+$/);
    // if (!reg1.test(str)) {
    //     return false;
    // }
    var reg = new RegExp(/^[a-zA-Z]\w{8,19}$/);
    if (reg.test(str)) {
        return true;
    } else {
        return false;
    }
}

function inituploadLocal(){
    var config = {
        id: 'btnup',
        successCb: function(file, ret){
            var url = ossPrefix + ret.key;
            $(".otx-head").attr('o', ret.key).prop('src',url);
            // parent.$("#headimg").prop('src',url);
        }
    }
    inituploadUtil(config, uploadPrefix + '/userimg');
    $("#btndelimg").click(function () {
        $(".otx-head").prop('src', '../images/default.png').attr('c', '').attr('z', '').attr('o', '');
    });
}

function closeWin() {
    var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
    parent.layer.close(index);
    if (Arg("frompage") && Arg("frompage") == "slice") {//从分片进入
        parentobj.initOrgan(Arg("curareacode"));
    }
    // parent.$("#win_" + Arg("mid")).find('iframe')[0].contentWindow.makeHtml();
    // parent.location.reload();
}

/*
 功能：生成密码
 Math.round(Math.random()*(20-10)+10) 指 10`20
 */
function randompwd(israndom) {
    var pv = (israndom) ? Math.round(Math.random() * 899999 + 100000) : '111111';
    $('#txtpwd').val(pv);
    $('#txtpwdagin').val(pv);
}

// 得到拼写
function getspell(_this, cb) {
    $.sm(function (re) {
        _this.attr('sp', re[0][0]);
        _this.attr('spe', re[0][1]);
        if (cb)
            cb(re[0][0], re[0][1]);
    }, ["common.spell", _this.val(), "0"]);
}

/*
 ***********************初始化角色***************************************
 */
function initTree(callback, data) {
    var arrsm = [];
    var roletype = Arg("roletype") || parent.objdata.my.roletype || "";
    arrsm = ["w_role.listbytype", roletype || '', ''];
    $.sm(function (re, err) {
        if (re) {
            var zNodes = [];//id,rname,mids,mr,seer,roletype
            var _default = "";
            for (var i = 0; i < re.length; i++) {
                if (i == 0) {
                    _default = re[i].id;
                }
                objdata.objrole[re[i].id] = re[i];
                zNodes.push('<option value="' + re[i].id + '">' + re[i].rname + '</option>');
            }
            $("#roleSel").html(zNodes.join(''));
            form.render('select');
            if (!objdata.uid) {
                $("#roleSel").val(_default);
                onchange();
            }
            if (callback)
                callback(data || "");
        } else
            parent.layer.msg(err, {shift: 6});
    }, arrsm);
}

function mobileCheck(mobile) {
    var first = mobile.charAt(0);
    if (first == 1 && mobile.length == 11) {
        return true;
    } else {
        return false;
    }
}

//修改depart
function resetdepart(depart, id) {
    $("#departSel").val(depart);
    $("#txtdepart_id").val(id);
    $("#departSel").validationEngine('hide');
}

/**
 * 禁用用户
 */
function disableuser(isndisable, title, yeyid) {
    parent.layer.load();
    $.sm(function (re, err) {//妇幼分库
        if (re) {
            $.sm(function (re, err) {//妇幼平台
                if (re) {
                    parent.layer.msg(title + '成功');
                    if (isndisable == "1") {
                        $("#btnnotforbid").show();
                        $("#btnisforbid").hide();
                    } else {
                        $("#btnnotforbid").hide();
                        $("#btnisforbid").show();
                    }
                } else {
                    parent.layer.msg('操作失败');
                }
                parent.layer.closeAll("loading");
            }, ["w_useredit.isdisableuserToPt", isndisable, objdata.mainid], "fyyypt", "", {rpc: 'fyyypt'});
        } else {
            parent.layer.msg(title + '失败');
            parent.layer.closeAll("loading");
        }
    }, ["w_useredit.isdisableuser", Arg("id"), isndisable]);
}