﻿/*
日期： 
作者： 
功能：添加班级
*/
var objdata = {
    classform: null
    , clano: ''
	, arraddclatype: {}
	, curyear: 0//当前添加或编辑班级时对应的年份
	, objeditdata: {}
};
layui.config({
	base: './js/' //假设这是test.js所在的目录
}).extend({ //设定模块别名
	system: '../../sys/system',
	waitSeconds: 0
});
var parentobj = null, form;
layui.use(['system', 'form'], function () {
	jQuery.getparent().layer.load();
	form = layui.form;
	parentobj = jQuery.getparent().$("#win_" + Arg('mid')).children('iframe')[0].contentWindow;
	if (Arg("cno")) {
		initclasstype();
		initclass();
	} else {
		jQuery.getparent().GetNo("Ub", 2, "", "000000", "ek_class", "classno", function (re2) {//生成班级编码规则
			var arrmsg = [["classadd.classmaxtsort.data"], ['classadd.sbdate.data'], ['classadd.classno.data', re2]];//获取排序，最后一次升班日期，班级编号验重
			$.sm(function (re1, err) {
				if (err) {
					jQuery.getparent().layer.msg(err);
					jQuery.getparent().layer.closeAll('loading');
				} else {
					if (re1[2] && re1[2].length > 0) {
						jQuery.getparent().layer.msg("班级编号已存在，请重新添加！");
					} else {
						objdata.clano = re2;
						$("#txtclassno").val(re2);
						var curyear = parentobj.getDateByStr(parentobj.tongsetting.cursysdate).getFullYear();
						var curmonth = parentobj.getDateByStr(parentobj.tongsetting.cursysdate).getMonth() + 1;
						var curdate = "";
						if(curmonth >= 9)
							curdate = curyear + '-09-01';
						else
							curdate = (curyear - 1) + '-09-01';
						parentobj.tongsetting.gouptime = re1[1] && re1[1][0] && re1[1][0][0] ? re1[1][0][0] : curdate;
						var yyyy = parseInt(parentobj.$("#sel_year").val());
						jQuery.getparent().$("#parentlbgouptime").text("当前新建班级生效日期为：" + parentobj.tongsetting.objgouptime[yyyy]);
						initclasstype();
						initclass();
						jQuery.getparent().layer.closeAll('loading');
					}
				}
			}, arrmsg);
		}, {yeyid: [jQuery.getparent().objdata.my.yeyid]});
	}
	$("#txtclassname").on('keypress blur', function(){
		$("#txtteacher" + objdata.curyear).val($(this).val());
	});
});
/*
 *初始化班级类型 
 */
function initclasstype(){
    var gradearr = parentobj.tongsetting.gradesarr;
    var arrstr = [];
    for (var t = 0; t < gradearr.length; t++) {
    	arrstr.push('<option value="' + gradearr[t][1] + '">' + gradearr[t][0] + '</option>');
	}
    $("#lbclasstype").html(arrstr.join(''));
	form.on('select(lbclasstype)', function (data) {
		bqhisclass();
	});
    form.render();
}
/*
 * 补全历史班级
 */
function bqhisclass(){
	var _this = $("#lbclasstype");
	var val = parseInt(_this.val());
//	if(val > 1){
		objdata.arraddclatype = {};
		var arrhtml = [], yyyy = parseInt(parentobj.$("#sel_year").val());
		var goupyear = parentobj.getDateByStr(parentobj.tongsetting.gouptime).getFullYear();
		var curyear = parentobj.getDateByStr(parentobj.tongsetting.cursysdate).getFullYear();
		arrhtml.push('<tr class="tbg"><td>年度</td><td>班级类型</td><td>班级名称</td></tr>');
		if(val != parentobj.tongsetting.gradesarr[parentobj.tongsetting.gradesarr.length - 1][1]){
			var start = 1;
			if(parseInt(parentobj.tongsetting.arrYear[0][1]) > yyyy - val){
				start = val - yyyy + parseInt(parentobj.tongsetting.arrYear[0][1]);
			}
			for (var i = start; i < val + 1; i++) {
				arrhtml.push('<tr><td>' + (yyyy - val + i) + '</td><td>' + parentobj.tongsetting.gradesarr[i-1][0] + '</td>');
				arrhtml.push('<td><div class="tjbanji_xhxdiv"><input id="txtteacher' + (yyyy - val + i) + '" class="ssjh_xhx" style=" width:100%" type="text" value="' + (i == val ? $("#txtclassname").val() : "") + '"><span class="FancyInput__bar___1P3wW"></span></div></td></tr>');
				if(!objdata.curyear){
	    			objdata.curyear = i == val ? (yyyy - val + i) : 0;
				}
				objdata.arraddclatype[yyyy - val + i] = [parentobj.tongsetting.gradesarr[i-1][1], (yyyy - val + i)];
			}
			if(yyyy < goupyear){//最后升班日期的年份大于当前正在添加班级的年份
				var d = val + goupyear - yyyy >= 6 ? 6 : val + goupyear - yyyy + 1;
				for (var j = val + 1; j < d; j++) {
	    			arrhtml.push('<tr><td>' + (yyyy - val + j) + '</td><td>' + parentobj.tongsetting.gradesarr[j-1][0] + '</td>');
	    			arrhtml.push('<td><div class="tjbanji_xhxdiv"><input id="txtteacher' + (yyyy - val + j) + '" class="ssjh_xhx" style=" width:100%" type="text"><span class="FancyInput__bar___1P3wW"></span></div></td></tr>');
	    			objdata.arraddclatype[yyyy - val + j] = [parentobj.tongsetting.gradesarr[j-1][1], yyyy - val + j];	
				}
			}
		}else if(val == parentobj.tongsetting.gradesarr[parentobj.tongsetting.gradesarr.length - 1][1]){//混龄班
			if(!objdata.curyear){
    			objdata.curyear = yyyy;//h == val ? (yyyy - val + h) : 0;
			}
			if(Arg('cno')){
				for (var h in objdata.objeditdata) {
					arrhtml.push('<tr><td>' + h + '</td><td>' + parentobj.tongsetting.gradesarr[val - 1][0] + '</td>');
					arrhtml.push('<td><div class="tjbanji_xhxdiv"><input id="txtteacher' + h + '" class="ssjh_xhx" style=" width:100%" type="text" value="' + (parentobj.tongsetting.gradesarr[val - 1][1] == val ? $("#txtclassname").val() : "") + '"> <span class="FancyInput__bar___1P3wW"></span></div></td></tr>');
					objdata.arraddclatype[h] = [parentobj.tongsetting.gradesarr[val - 1][1], h];
				}
			}else{ 
				arrhtml.push('<tr><td>' + yyyy + '</td><td>' + parentobj.tongsetting.gradesarr[val - 1][0] + '</td>');
				arrhtml.push('<td><div class="tjbanji_xhxdiv"><input id="txtteacher' + yyyy + '" class="ssjh_xhx" style=" width:100%" type="text" value="' + (parentobj.tongsetting.gradesarr[val - 1][1] == val ? $("#txtclassname").val() : "") + '"> <span class="FancyInput__bar___1P3wW"></span></div></td></tr>');
				objdata.arraddclatype[yyyy] = [parentobj.tongsetting.gradesarr[val - 1][1], yyyy];	
			}
		}
		$("#divclasshis").show();
		$("#tbclasshis").html(arrhtml.join(''));
	    $("#txtteacher" + objdata.curyear).on('keypress blur', function() {  
	        $('#txtclassname').val($(this).val());  
	    });
}

/*
 *功能：保存班级
 */
function initclass() {
	var objwhere = [];
	if(Arg("cno")) {
		objwhere.classno = [Arg('cno')];
		objwhere.gouptime = [Arg('gouptime')];
	}
    objdata.classform = new ehaiform({
        status: Arg("cno") ? 1 : 0,
		strWhere: Arg("cno") ? 1 : 0,
        arrSelectPm: ["banjiadd.form.select", $.msgwhere(objwhere), jQuery.getparent().objdata.my.yeyid],
        strParamId: 'txtclassno:1,txtclassname:1,lbclasstype:19,txtisexclude:0,txtgouptime:2,txtclasssort:0,txtid:0,txtteachername:1,txtphone:1',
        keyids: 'id', //关键字id 多个逗号分开
        keyvalues: Arg("id") ? Arg("id") : 0, //关键字值 多个逗号分开
        type: 'DBInsertReId',
        saveId: 'btnsave', //保存id 在内部绑定保存事件 【可选】，如果没有该参数 则外部写保存方法
        formid: 'form',
        savevalidate: function () {
            if (!$.trim($("#txtclassname").val())) {
                jQuery.getparent().layer.msg("请输入班级名称！");
                return false;
            }
            if (!$("#lbclasstype").val()) {
                jQuery.getparent().layer.msg("请选择班级类型！");
                return false;
            }
            saveclassinfo();
            return false;
        },
        aftercallback: aftercallback
    });
}

function aftercallback(re, err) {
	$.sm(function(re1, err1){
		if(err1){
			jQuery.getparent().layer.msg(err1);
            jQuery.getparent().layer.closeAll('loading');
		}else{
			objdata.arraddclatype = {};
			objdata.curyear = parentobj.getDateByStr(Arg('gouptime')).getFullYear();
			var arrhtml = [];
    		arrhtml.push('<tr class="tbg"><td>年度</td><td>班级类型</td><td>班级名称</td></tr>');
    		var cltype = re1[0][1];
    		if(cltype != parentobj.tongsetting.gradesarr[parentobj.tongsetting.gradesarr.length - 1][1]){//混龄班
        		for (var cl = 0; cl < cltype - 1; cl++) {
        			var yyyy1 = (re1[0][0] - cltype + (cl+1));
        			arrhtml.push('<tr><td>' + yyyy1 + '</td><td>' + parentobj.tongsetting.gradesarr[cl][0] + '</td>');
        			arrhtml.push('<td><div class="tjbanji_xhxdiv"><input id="txtteacher' + yyyy1 + '" class="ssjh_xhx" style=" width:100%" type="text" maxlength="20"/><span class="FancyInput__bar___1P3wW"></span></div></td></tr>');
        			objdata.arraddclatype[yyyy1] = [parentobj.tongsetting.gradesarr[cl][1], yyyy1];
        		}
    		}
			for (var c = 0; c < re1.length; c++) {
    			arrhtml.push('<tr><td>' + re1[c][0] + '</td><td>' + parentobj.tongsetting.gradesarr[re1[c][1]-1][0] + '</td>');
    			arrhtml.push('<td><div class="tjbanji_xhxdiv"><input id="txtteacher' + re1[c][0] + '" class="ssjh_xhx" style=" width:100%" type="text" value="' + re1[c][2] + '" oldval="' + re1[c][2] + '" maxlength="20"><span class="FancyInput__bar___1P3wW"></span></div></td></tr>');
    			objdata.arraddclatype[re1[c][0]] = [parentobj.tongsetting.gradesarr[re1[c][1]-1][1], re1[c][0]];
    			objdata.objeditdata[re1[c][0]] = re1[c];
			}
			$("#divclasshis").show();
    		$("#tbclasshis").html(arrhtml.join(''));
    	    $("#txtteacher" + objdata.curyear).on('keypress blur',function() {  
    	        $('#txtclassname').val($(this).val());  
    	    });
		}
		objdata.curyear = parentobj.getDateByStr(Arg('gouptime')).getFullYear();
	    if (re && re[0] && re[0].length) {
	        objdata.clano = re[0][0];
	        $("#txtclassno").val(re[0][0]);
	        //        $("#txtclassname").val(re[0][1]);
	        $("#txtisexclude").val(re[0][3]);
	        $("#txtgouptime").val(re[0][4]);
	        $("#txtclasssort").val(re[0][5]);
			$("#lbclasstype").val(re[0][2]);//年级
			form.render();
	    }
        jQuery.getparent().layer.closeAll('loading');
	},['banjiadd.classinfo.data', Arg('cno'), jQuery.getparent().objdata.my.yeyid]);
};
/*
//功能：保存回调函数外部处理逻辑  添加教师信息
*/
function saveclassinfo() {
	jQuery.getparent().layer.load();
    var classname = $.trim($("#txtclassname").val());//班级名称
    if($.regCharacter(classname)){
        jQuery.getparent().layer.msg("班级名称不能包含特殊字符，请检查并重新输入！");
        jQuery.getparent().layer.closeAll('loading');
        return;
    }
    var classtype = $("#lbclasstype").val();//班级类型
    var sms = [["classadd.classmaxtsort.data"]]
    	, arrpm = [];
    for (var y in objdata.arraddclatype) {//验证班级名称是否重复
    	var cval = $.trim($("#txtteacher" + y).val());
        if($.regCharacter(cval)){
            jQuery.getparent().layer.msg(y + "年的班级名称不能包含特殊字符，请检查并重新输入！");
            jQuery.getparent().layer.closeAll('loading');
            return;
        }
        sms.push(["classadd.yzclass.data", objdata.clano, parentobj.tongsetting.objgouptime[y], cval]);
	}
    if(!Arg("cno")){//添加时验证班级是否已经添加
        sms.push(["classadd.yzclass.data", objdata.clano, parentobj.tongsetting.objgouptime[parentobj.$("#sel_year").val()], classname]);
    	sms.push(['classadd.classno.data', objdata.clano]);
    }
    $.sm(function(re2, err2){//排序，验证班级名称
    	if (err2) {
            jQuery.getparent().layer.msg(err2);
            jQuery.getparent().layer.closeAll('loading');
        } else {
        	var objadded = {};
        	for (var y = 1; y < re2.length; y++) {
        		for (var cla = 0; cla < re2[y].length; cla++) {
        			objadded[re2[y][cla][0]] = re2[y][cla];
				}
			}
        	var tip = '';
        	for (var t in objadded) {
        		tip += (t + "的班级中已存在【" + objadded[t][1] + "】,<br/>");
			}
        	if(tip){
        		jQuery.getparent().layer.msg(tip + '请重新修改对应的班级名称！');
            	jQuery.getparent().layer.closeAll('loading');
        		return;
        	}
        	if(re2[re2.length-1] && re2[re2.length-1][0] && re2[re2.length-1][0][0] && !Arg("cno")){
        		jQuery.getparent().layer.msg("班级编号已被占用，请重新添加！");
            	jQuery.getparent().layer.closeAll('loading');
        		return;
        	}
        	var tsort = parseInt(re2[0] && re2[0][0] && re2[0][0][0] ? re2[0][0][0] : 0);
        	if(!parentobj.isEmpty(objdata.arraddclatype)){
        		for (var y in objdata.objeditdata) {
        			if(Arg("cno") && !objdata.arraddclatype[y] && objdata.objeditdata[y]){//编辑时删除班级（当班级切换到低年级时）
        				arrpm.push(['classadd.delclass', objdata.clano, objdata.objeditdata[y][3], jQuery.getparent().objdata.my.yeyid]);
        			}
				}
        		for (var n in objdata.arraddclatype) {
        			if(!Arg("cno"))
        				tsort++;
        			var tch = $.trim($("#txtteacher" + objdata.arraddclatype[n][1]).val());//班级名称
        			if(tch){
        				arrpm.push(["classadd.addclass.add", objdata.clano, (tch), objdata.arraddclatype[n][0], 0, parentobj.tongsetting.objgouptime[objdata.arraddclatype[n][1]], !Arg("cno") ? tsort :　objdata.objeditdata && objdata.objeditdata[objdata.arraddclatype[n][1]] ? objdata.objeditdata[objdata.arraddclatype[n][1]][4] : tsort, jQuery.getparent().objdata.my.yeyid]);
                    }
				}
        	} else {
        		tsort++;
                arrpm.push(["classadd.addclass.add", objdata.clano, (classname), classtype, 0, parentobj.tongsetting.objgouptime[parentobj.$("#sel_year").val()], tsort, jQuery.getparent().objdata.my.yeyid]);
        	}
			addclass(arrpm, classname, classtype);
        }
    }, sms);
};

function addclass(arrpm, classname, classtype, r){
	$.sm(function (re, err) {
        if (err) {
            jQuery.getparent().layer.msg(err);
            jQuery.getparent().layer.closeAll('loading');
        } else {
			if(Arg("cno")){
				var curclassname = objdata.objeditdata[parentobj.$("#sel_year").val()][2];
                if (curclassname != classname) {
                	parentobj.tongsetting.clatree.resetTreeCode(Arg("cno"), Arg("cno"), curclassname, classname);
                }
			}else{
                $.getparent().adminSetting.objtree["ek_class"].obj[objdata.clano] = [objdata.clano, classname, 0, "1"];
                $.getparent().adminSetting.objtree["ek_class"].arr.push([objdata.clano, classname, 0, "1"]);
                parentobj.tongsetting.clatree.addtreepointer(0, "ultreebrowserclass", true);
			}
			var objcurclainfo = parentobj.tongsetting.objcurclainfo[objdata.clano];
			var bjstunum = objcurclainfo && objcurclainfo[6] ? objcurclainfo[6] : 0;
            parentobj.tongsetting.objcurclainfo[objdata.clano] = [classname, $("#txtisexclude").val(), $("#txtclasssort").val(), classtype, parentobj.tongsetting.objgouptime[parentobj.$("#sel_year").val()],(re && re[0] && re[0].re ? re[0].re : 0), bjstunum];
            Arg("cno") ? setclaname(classname) : "";
            var cnum = 0;
        	for ( var ocla in parentobj.tongsetting.objcurclainfo) {//更新班级数量
        		cnum++;
        		if(!parentobj.$("#jcxxtreespanulclassno" + ocla)[0]){
        			parentobj.$("#ultreebrowserclassatree" + ocla).append('(<span id="jcxxtreespanulclassno' + ocla + '">' +  parentobj.tongsetting.objcurclainfo[ocla][6] + '</span>)');
        		}
        	}
        	parentobj.$("#ultreebrowserclassatree0").find("#spclassnum").html("("+cnum+")");
        	if(r)
        		jQuery.getparent().layer.close(r);
			jQuery.getparent().layer.msg("保存成功！");
            jQuery.getparent().layer.closeAll('loading');
            jQuery.getparent().layer.close(jQuery.getparent().objdata.classadd);
        }
    }, arrpm);
}

/*
 * 功能：同步修改树
 */
function setclaname(classname) {
    var objtree = $.getparent().adminSetting.objtree["ek_class"];
    for (var h in parentobj.tongsetting.objcurclainfo) {
        if (!objtree.obj[h] || objdata.clano == h) {
            objtree.obj[h][1] = classname;
        }
    }
    for (var h = 0; h < objtree.arr.length; h++) {
        if (objdata.clano == objtree.arr[h][0]) {
            objtree.arr[h][1] = classname;
        }
    }
};

